class_name Action
extends Object

# This class implements an action in the game.
# It is used to represent a move made by a player in the game.
enum ActionType {
    PASS,
    PLAY_CARD_FROM_HAND,
    PLAY_CARD_FROM_GRAVEYARD,
    PLAY_CARD_FROM_DECK,
    }

var actionType: int # The type of action being performed.
var handIndex: int # The index of the card in the player's hand.
var laneIndex: int # The index of the lane where the card is being played.
var slotIndex: int # The index of the slot where the card is being played.
var graveyardIndex: int # The index of the card in the player's graveyard.
var deckIndex: int # The index of the card in the player's deck.

#create a new action
func _init(_actionType: int = ActionType.PASS, _handIndex:int = -1, _laneIndex:int = -1, _slotIndex:int = -1, _graveyardIndex:int = -1, _deckIndex:int = -1):
    self.actionType = _actionType
    self.handIndex = _handIndex
    self.laneIndex = _laneIndex
    self.slotIndex = _slotIndex
    self.graveyardIndex = _graveyardIndex
    self.deckIndex = _deckIndex

