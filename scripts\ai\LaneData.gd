class_name LaneData
extends Object

var laneIndex: int = -1
var cardSlots: Array[CardCollectionData] = []

var isLocationRevealed: bool = false
var location: PlayCardData
var mainSlots: Array[int] # Store index of cardSlot belongs to Main player
var neutralSlots: Array[int] # Store index of neutral cardSlots
var oppoSlots: Array[int] # Store index of cardSlot belongs to Oppo player

func _init(_laneIndex: int, _cardSlots: Array[CardCollectionData]):
    self.laneIndex = _laneIndex
    self.cardSlots = _cardSlots















