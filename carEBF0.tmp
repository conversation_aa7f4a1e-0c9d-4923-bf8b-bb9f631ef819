[gd_scene load_steps=2 format=3 uid="uid://dj3ay2qyknjuf"]

[ext_resource type="Script" path="res://scripts/ui/CardUI.gd" id="1_d7x6h"]

[node name="CardUI" type="Control"]
custom_minimum_size = Vector2(70, 90)
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = -1082.0
offset_bottom = -558.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_d7x6h")

[node name="CardColor" type="ColorRect" parent="."]
layout_mode = 0
offset_right = 70.0
offset_bottom = 90.0
color = Color(0.0962047, 0.0962048, 0.0962047, 1)

[node name="CostLabel" type="Label" parent="."]
layout_mode = 0
offset_left = 5.0
offset_top = -1.0
offset_right = 22.0
offset_bottom = 22.0
theme_override_font_sizes/font_size = 10
vertical_alignment = 1

[node name="AbilityLabel" type="Label" parent="."]
layout_mode = 0
offset_left = 6.0
offset_top = 27.0
offset_right = 65.0
offset_bottom = 86.0
theme_override_font_sizes/font_size = 8
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 3

[node name="PowerLabel" type="Label" parent="."]
offset_left = 42.0
offset_right = 64.0
offset_bottom = 23.0
theme_override_font_sizes/font_size = 16
horizontal_alignment = 2
vertical_alignment = 1
