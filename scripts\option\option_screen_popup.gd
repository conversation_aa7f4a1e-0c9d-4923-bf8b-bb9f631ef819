extends Control

@onready var option_screen: Control = $"."
@onready var return_button: Button = $VBoxContainer/ReturnButton
@onready var confirmation_screen: Control = $ConfirmationScreen


# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	#if Const.CURRENT_SCREEN == "continue_screen":
		#return_button.visible = false
	pass # Replace with function body.

# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass

func _on_quit_button_pressed() -> void:
	confirmation_screen.visible = true
	confirmation_screen.popup_label.text = "Return to Desktop?"

func _on_return_button_pressed() -> void:
	confirmation_screen.visible = true
	confirmation_screen.popup_label.text = "Return to Title Screen?"

func _on_close_button_pressed() -> void:
	option_screen.visible = false
