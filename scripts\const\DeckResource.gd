extends Object
class_name DeckResource
enum {
	G<PERSON>EN,
	RED,
	BLUE,
	GREEN_RED,
	GREEN_BLUE,
	RED_BLUE,
}

static var MainDeck = DeckResource.RED
static var OppoDeck = DeckResource.RED


static func getDeck(deckID:int):
	var cardNames = DeckResource.DeckList[deckID]["cards"]
	var fullDeck = []

	# Transform array of card names into array of card details
	for cardName in cardNames:
		var cardDetails = CardData.getCardByName(cardName)
		if not cardDetails.is_empty():
			fullDeck.append(cardDetails)
		else:
			push_error("Card not found in CardData: " + cardName)
	return fullDeck

static var DeckList = {
	DeckResource.GREEN: {
		"cards": [
			CardData.Obrelith,
			CardData.Obrelith,
			CardData.Myrthalia
		]
	},
	DeckResource.RED: {
		"cards": [
			CardData.Butterfire,
			CardData.NightButterfire,
			CardData.HellButterfire,
			CardData.Flarefire,
			CardData.Bomberkid,
			CardData.Bomber,
			CardData.BigBomber,
			CardData.Boomgobb,
			CardData.Impling,
			CardData.Imp,
			CardData.PitImp,
			CardData.Kravveth,
			CardData.FireKnight,
			CardData.BlazeKnight,
			CardData.DragonKnight,
			CardData.Titus,
			CardData.Flinger,
			CardData.EliteFlinger,
			CardData.BossFlinger,
			CardData.ZarnaktheFling,
			CardData.YoungSuccubus,
			CardData.Succubus,
			CardData.HighSuccubus,
			CardData.LadyLamia,
			CardData.Larvaling,
			CardData.Larva,
			CardData.DeepLarva,
			CardData.AncientLarva,
			CardData.FireElemental,
			CardData.MagmaElemental,
			CardData.LordMalfyris,
			CardData.Redwings,
			CardData.GrandRedwings,
			CardData.Heliora,
			CardData.Flamecoat,
			CardData.DarkFlamecoat,
			CardData.Apocalypse,
			CardData.RedMage,
			CardData.EmberistheRed,
			CardData.Explodus,
			CardData.ExplodusII,
			CardData.Fireshadow,
			CardData.Evilshadow,
			CardData.Nymora,
			CardData.Grashnak,
			CardData.KingKrotos,
			CardData.Ignix,
		]
	},
	DeckResource.BLUE: {
		"cards": [
			CardData.Siren,
			CardData.EliteSiren,
			CardData.QueenSiren,
			CardData.Calyra,
			CardData.Shelling,
			CardData.Shell,
			CardData.MagicShell,
			CardData.Pearind,
			CardData.Mimicling,
			CardData.Mimic,
			CardData.FangedMimic,
			CardData.TheUnhollow,
			CardData.Mermage,
			CardData.DeepMermage,
			CardData.HighMermage,
			CardData.Tydros,
			CardData.Angler,
			CardData.PearlAngler,
			CardData.GrandAngler,
			CardData.Lurkgloom,
			CardData.IceGolem,
			CardData.CrystalGolem,
			CardData.DiamondGolem,
			CardData.Frostbane,
			CardData.Metaling,
			CardData.Metamore,
			CardData.Metamonster,
			CardData.Murkendral,
			CardData.Silencer,
			CardData.EliteSilencer,
			CardData.Caelith,
			CardData.WaterElemental,
			CardData.TideElemental,
			CardData.Ocevaris,
			CardData.SnowFairy,
			CardData.BlizzardFairy,
			CardData.Eirwyn,
			CardData.Levisia,
			CardData.SkyLevisia,
			CardData.Trine,
			CardData.MasterTrine,
			CardData.BlueMage,
			CardData.ElyndriatheBlue,
			CardData.Phantarion,
			CardData.Leviathan,
			CardData.Tidecaller,
			CardData.SeaSovereign,
		]
	},
	DeckResource.GREEN_RED: {
		"cards": [
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp,
			CardData.Imp
		]
	},
	DeckResource.GREEN_BLUE: {
		"cards": [
			CardData.Slime1C,
			CardData.Slime1C,
			CardData.Slime1C,
			CardData.Slime1C,
			CardData.Slime1C,
			CardData.Slime1C,
			CardData.Slime1C,
			CardData.Slime1C,
			CardData.Slime1C,
			CardData.Slime1C,
		]
	},
	DeckResource.RED_BLUE: {
		"cards": [
			CardData.Slime1C,
			CardData.Slime1R,
			CardData.Slime1E,
			CardData.Slime1L,
			CardData.Slime2C,
			CardData.Slime2R,
			CardData.Slime2E,
			CardData.Slime2L,
			CardData.Slime3C,
			CardData.Slime3R,
			CardData.Slime3E,
			CardData.Slime4C,
			CardData.Slime4R,
			CardData.Slime4E,
			CardData.Slime5C,
			CardData.Slime5R,
			CardData.Slime5L,
			CardData.Slime6R,
			CardData.Slime6E,
			CardData.Slime6L
		]
	},
}

static func getDeckName(deckID:int):
	match deckID:
		DeckResource.GREEN:
			return "General Prixa"
		DeckResource.RED:
			return "King Krotos"
		DeckResource.BLUE:
			return "Phantarion"
		DeckResource.GREEN_BLUE:
			return "Leaf Slime"
		DeckResource.GREEN_RED:
			return "Pyragon"
		DeckResource.RED_BLUE:
			return "Fire Slime"
		_:
			return "Foxy"
