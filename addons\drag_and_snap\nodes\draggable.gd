@tool
extends Node
class_name Draggable

# Snapping.
@onready var battleNode = get_tree().get_root().get_node("Battle") 
var _original_parent
var _original_rotation
var _snapped_to = null

func _enter_tree():
	update_configuration_warnings()

	assert(get_parent() != get_tree().get_root(), "ERROR: Draggable is a component and must be a child of another node.")
	
	assert(get_parent() is CollisionObject3D, "ERROR: Component must be applied to a CollisionObject3D derived node.")
	if not get_parent().is_connected("input_event", Callable(self, "_on_input_event")):
		get_parent().connect("input_event", Callable(self, "_on_input_event"))

func _ready():
	# At run time, a draggable object can not be the scene root.
	assert(get_parent().get_parent() != get_tree().get_root(), "ERROR: At runtime, a draggable object must be a child of another node.")

	# For snapping.
	_original_parent = get_parent().get_parent()
	_original_rotation = get_parent().get_rotation()

func _get_configuration_warnings():
	var warnings = PackedStringArray()
	
	if self == get_tree().get_edited_scene_root():
		warnings.push_back("Draggable is a component and must be a child of another node.")

	# Parent is not the correct type.
	if not get_parent() is CollisionObject3D:
		warnings.push_back("Component must be applied to a CollisionObject3D derived node.")
	
	# Name has been changed.
	if get_name() != "Draggable":
		warnings.push_back("Component must be named \"Draggable\"")
	
	return warnings

func _on_input_event(camera, event, _position, _normal, _shape_idx):
	# Start a new drag.
	if event is InputEventMouseButton and event.is_action_pressed("clicked"):# and (get_parent().get_parent() is Hand or get_parent().get_parent().name == "Lane") and get_parent().draggable:
		if !get_parent().isOnHand:
			return
		await get_tree().create_timer(Const.INSPECT_TIME).timeout
		if Input.is_action_pressed("clicked"):
			if get_parent().get_parent().get_parent().name == "Enemy":
				get_parent().currentState = get_parent().states.IDLE
				return
			#get_parent().top_level = true
			if get_parent().get_parent():
				get_parent().get_parent().draggedIndex.emit(get_parent().brightness_on, true)
			battleNode.find_child("Arena").dragging.emit(true)
			get_parent().transform.origin += Vector3.UP
			get_parent().currentState = get_parent().states.DRAG
			#get_parent().scale = Vector3.ONE * 0.75
			#get_parent().rotation.z = lerp(get_parent().rotation.z, 0.0, 1)
			#get_parent().target_transform.basis = get_parent().transform.basis
			DragSnap.start_drag(camera, get_parent())

func is_snapped():
	return _snapped_to != null

func set_snapped(snapped_to):
	_snapped_to = snapped_to

func get_original_parent():
	return _original_parent

func get_original_rotation():
	return _original_rotation
