[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://dun4w5y08xrn3"
path.s3tc="res://.godot/imported/dummy_normal.png-79090569a283ac850c876f6b6b26faef.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}

[deps]

source_file="res://Assets/dummy/dummy_normal.png"
dest_files=["res://.godot/imported/dummy_normal.png-79090569a283ac850c876f6b6b26faef.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=1
roughness/src_normal="res://assets/dummy/dummy_normal.png"
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
