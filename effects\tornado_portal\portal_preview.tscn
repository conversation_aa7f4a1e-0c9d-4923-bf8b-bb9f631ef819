[gd_scene load_steps=32 format=3 uid="uid://clqfhoslbs2f"]

[ext_resource type="Script" path="res://effects/tornado_portal/portal_preview.gd" id="1_uniox"]
[ext_resource type="Texture2D" uid="uid://3ro0yvv3roj3" path="res://Assets/checkerboard.svg" id="2_yf5rq"]
[ext_resource type="Material" uid="uid://5dlygf08frjc" path="res://effects/tornado_portal/materials/portal_effect_mat.tres" id="4_8vccs"]
[ext_resource type="Material" uid="uid://dcvf6avwqqxhu" path="res://effects/tornado_portal/materials/portal_light_mat.tres" id="5_oiel3"]
[ext_resource type="Material" uid="uid://dq7edc1qpr6ct" path="res://effects/tornado_portal/materials/pull_deform_mat.tres" id="6_og3s2"]
[ext_resource type="Material" uid="uid://b73q6qfh1ldan" path="res://effects/tornado_portal/materials/halo_light_mat.tres" id="7_jal61"]
[ext_resource type="Material" uid="uid://cmsx7fp6tjcbi" path="res://effects/tornado_portal/materials/trail_particles_mat.tres" id="8_lptxy"]
[ext_resource type="Shader" path="res://effects/tornado_portal/materials/small_particles.gdshader" id="9_qn7hw"]
[ext_resource type="PackedScene" path="res://turner/turner.tscn" id="9_wj2xm"]
[ext_resource type="Material" uid="uid://q8vkh5skmqsn" path="res://effects/tornado_portal/materials/small_particles_mat.tres" id="10_i86j3"]

[sub_resource type="Environment" id="Environment_2m44q"]
background_mode = 1
background_color = Color(0.14902, 0.14902, 0.14902, 1)
ambient_light_source = 2
ambient_light_color = Color(0.368627, 0.368627, 0.368627, 1)
tonemap_mode = 2
glow_enabled = true
glow_normalized = true
glow_intensity = 1.2
glow_strength = 1.2

[sub_resource type="CameraAttributesPhysical" id="CameraAttributesPhysical_ubj2o"]
frustum_focus_distance = 5.5
frustum_focal_length = 60.0

[sub_resource type="Shader" id="Shader_7gwc1"]
code = "shader_type spatial;
render_mode diffuse_lambert_wrap, specular_disabled, depth_draw_always;

uniform sampler2D checkerboard_sampler : filter_linear_mipmap_anisotropic, repeat_enable;
uniform vec3 albedo_color : source_color;
uniform float uv_scale = 20.0;

void fragment() {
	ALBEDO = albedo_color * (texture(checkerboard_sampler, UV * uv_scale).x * 0.2 + 0.8);
	ALPHA = smoothstep(0.5, 0.25, length(UV - vec2(0.5)));
}
"

[sub_resource type="ShaderMaterial" id="ShaderMaterial_t058v"]
render_priority = -128
shader = SubResource("Shader_7gwc1")
shader_parameter/albedo_color = Color(0.2, 0.2, 0.2, 1)
shader_parameter/uv_scale = 20.0
shader_parameter/checkerboard_sampler = ExtResource("2_yf5rq")

[sub_resource type="PlaneMesh" id="PlaneMesh_aq72j"]
size = Vector2(40, 40)

[sub_resource type="QuadMesh" id="QuadMesh_end5x"]
size = Vector2(4, 4)

[sub_resource type="CylinderMesh" id="CylinderMesh_ku4rn"]
flip_faces = true
top_radius = 2.0
bottom_radius = 0.0
height = 4.0
cap_top = false
cap_bottom = false

[sub_resource type="BoxMesh" id="BoxMesh_t7whr"]

[sub_resource type="CylinderMesh" id="CylinderMesh_gmeoj"]
top_radius = 1.8
bottom_radius = 1.4
height = 1.4
rings = 2
cap_top = false
cap_bottom = false

[sub_resource type="Gradient" id="Gradient_p2hsa"]
offsets = PackedFloat32Array(0, 0.1, 1)
colors = PackedColorArray(1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_4ssin"]
gradient = SubResource("Gradient_p2hsa")

[sub_resource type="Curve" id="Curve_tbwf0"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.2, 1), 0.0, 0.0, 0, 0, Vector2(0.8, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 4

[sub_resource type="CurveTexture" id="CurveTexture_wyrbq"]
curve = SubResource("Curve_tbwf0")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_0egtw"]
lifetime_randomness = 0.2
emission_shape = 1
emission_sphere_radius = 1.0
direction = Vector3(0, 1, 0)
initial_velocity_min = 1.0
initial_velocity_max = 4.0
gravity = Vector3(0, 8, 0)
scale_min = 0.4
scale_curve = SubResource("CurveTexture_wyrbq")
color_ramp = SubResource("GradientTexture1D_4ssin")
turbulence_enabled = true
turbulence_noise_scale = 3.0
turbulence_noise_speed_random = 0.0
turbulence_influence_max = 0.2

[sub_resource type="Curve" id="Curve_yd6ma"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.5, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="RibbonTrailMesh" id="RibbonTrailMesh_ppf3r"]
shape = 0
size = 0.04
curve = SubResource("Curve_yd6ma")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_sdv0b"]
render_priority = 0
shader = ExtResource("9_qn7hw")
shader_parameter/color = Color(0.619608, 0.4, 1, 1)
shader_parameter/intensity = 8.0

[sub_resource type="QuadMesh" id="QuadMesh_g34lk"]
size = Vector2(0.1, 0.1)

[sub_resource type="Animation" id="Animation_m6s3y"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Target:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(0, 0.5, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Hole:material_override:shader_parameter/opening")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("TrailsParticles:emitting")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("HaloLight:scale")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(1, 1, 1)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("HaloLight:material_override:shader_parameter/alpha")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Target:rotation")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(0, 0, 0)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("SmallParticles:emitting")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Hole:scale")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(1, 1, 1)]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("GPUParticlesAttractorSphere3D:strength")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Hole:material_override:shader_parameter/time_offset")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Target:material_override:shader_parameter/pull_intensity")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}

[sub_resource type="Animation" id="Animation_2tqdi"]
resource_name = "default"
length = 4.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Target:position:y")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 2.8, 3.2),
"transitions": PackedFloat32Array(-2, -2, -2, 1),
"update": 0,
"values": [0.5, 0.5, 1.0, -4.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Hole:material_override:shader_parameter/opening")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.5, 3, 3.4),
"transitions": PackedFloat32Array(1, 0.5, -2, 2),
"update": 0,
"values": [0.0, 1.0, 1.0, 0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("TrailsParticles:emitting")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 2.5),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("HaloLight:scale")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.5, 3, 3.4),
"transitions": PackedFloat32Array(-2, -2, -2, -2),
"update": 0,
"values": [Vector3(0, 1, 0), Vector3(1, 1, 1), Vector3(1, 1, 1), Vector3(0, 1, 0)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("HaloLight:material_override:shader_parameter/alpha")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0.2, 0.5, 3, 3.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, 1.0, 1.0, 0.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Target:rotation_degrees:y")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 3.3),
"transitions": PackedFloat32Array(4, 1),
"update": 0,
"values": [0.0, 488.0]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("SmallParticles:emitting")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 2.5),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Hole:scale")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.5, 0.7, 3, 3.4),
"transitions": PackedFloat32Array(2, -2, -2, -2, -2),
"update": 0,
"values": [Vector3(1, 1, 1), Vector3(1.2, 1.2, 1), Vector3(1, 1, 1), Vector3(1, 1, 1), Vector3(0, 0, 1)]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("GPUParticlesAttractorSphere3D:strength")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(2.3, 2.7),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 40.0]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Hole:material_override:shader_parameter/time_offset")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(-1.5, 4),
"transitions": PackedFloat32Array(4, 0.5),
"update": 0,
"values": [0.0, 8.0]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Target:rotation_degrees:x")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 3.3),
"transitions": PackedFloat32Array(2, 1),
"update": 0,
"values": [0.0, -180.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Target:material_override:shader_parameter/pull_intensity")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(1.5, 3),
"transitions": PackedFloat32Array(2, 1),
"update": 0,
"values": [0.0, 1.0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_smxoj"]
_data = {
"RESET": SubResource("Animation_m6s3y"),
"default": SubResource("Animation_2tqdi")
}

[node name="HolePreview" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0.01)
script = ExtResource("1_uniox")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_2m44q")
camera_attributes = SubResource("CameraAttributesPhysical_ubj2o")

[node name="GroundMesh" type="MeshInstance3D" parent="."]
material_override = SubResource("ShaderMaterial_t058v")
mesh = SubResource("PlaneMesh_aq72j")
metadata/_edit_lock_ = true

[node name="Hole" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, 0.001, 0)
material_override = ExtResource("4_8vccs")
cast_shadow = 0
mesh = SubResource("QuadMesh_end5x")

[node name="HoleBody" type="MeshInstance3D" parent="Hole"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0, -2.025)
material_override = ExtResource("5_oiel3")
mesh = SubResource("CylinderMesh_ku4rn")
skeleton = NodePath("../..")

[node name="Target" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.5, 0)
material_override = ExtResource("6_og3s2")
mesh = SubResource("BoxMesh_t7whr")

[node name="HaloLight" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.7, 0)
material_override = ExtResource("7_jal61")
mesh = SubResource("CylinderMesh_gmeoj")

[node name="TrailsParticles" type="GPUParticles3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.5, 0)
material_override = ExtResource("8_lptxy")
cast_shadow = 0
emitting = false
amount = 64
lifetime = 2.0
transform_align = 3
trail_enabled = true
process_material = SubResource("ParticleProcessMaterial_0egtw")
draw_pass_1 = SubResource("RibbonTrailMesh_ppf3r")

[node name="SmallParticles" type="GPUParticles3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.5, 0)
material_override = SubResource("ShaderMaterial_sdv0b")
cast_shadow = 0
emitting = false
amount = 1024
lifetime = 2.0
transform_align = 1
process_material = ExtResource("10_i86j3")
draw_pass_1 = SubResource("QuadMesh_g34lk")

[node name="GPUParticlesAttractorSphere3D" type="GPUParticlesAttractorSphere3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -1, 0)
strength = 0.0
attenuation = 0.5
directionality = 0.1
radius = 10.0

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 0, 0)
shadow_enabled = true

[node name="Anchor" parent="." instance=ExtResource("9_wj2xm")]
transform = Transform3D(1, 0, 0, 0, 0.906308, 0.422618, 0, -0.422618, 0.906308, 0, 1, 0)
lock_x = true

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
unique_name_in_owner = true
libraries = {
"": SubResource("AnimationLibrary_smxoj")
}
