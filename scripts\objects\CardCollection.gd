class_name CardCollection

var ownedPlayer:Player
var cards:Array[Card] = []

func _init():
	pass

func getOwner() -> Player:
	return self.ownedPlayer

func getOwnerName() -> String:
	if self.ownedPlayer:
		return self.ownedPlayer.getName()
	else:
		return "N"

func getCards() -> Array[Card]:
	return self.cards

func getCardCount() -> int:
	return self.cards.size()

func getCardAt(index:int) -> Card:
	if getCardCount() > index:
		return self.cards[index]
	else:
		return null

func getTopCardPower() -> int:
	return self.cards[0].getPower()
	
func setOwner(player:Player):
	self.ownedPlayer = player
	for c in cards:
		c.setOwner(player)

func isEmpty():
	return self.cards.is_empty()

func has(card:Card) -> bool:
	return self.cards.has(card)

# add cards into this CardCollection
func add(p_cards:Array[Card]) -> void:
	self.cards.append_array(p_cards)

func addSingle(p_cards:Card) -> void:
	self.cards.append(p_cards)

#func push_front(p_cards:Card) -> void:
	#self.cards.push_back(p_cards)
# move cards to another CardCollection
func removeCards(p_cards:Array[Card]) -> Array[Card]:
	var removedCards:Array[Card] = []
	for card in p_cards:
		if self.cards.has(card):
			var cardIndex = self.cards.find(card)
			removedCards.append(self.cards.pop_at(cardIndex))
	return removedCards

func removeSingle(p_cards:Card) -> Card:
	var cardIndex = self.cards.find(p_cards)
	var removedCard = self.cards.pop_at(cardIndex)
	return removedCard

# move top x cards to another CardCollection
func pull(amount:int) -> Array[Card]:
	var pulledCards:Array[Card] = []
	for i in amount:
		if not self.cards.is_empty():
			pulledCards.append(self.cards.pop_front())
	return pulledCards

# remove card at index from this CardCollection
func removeAt(index:int) -> Card:
	var removedCard = self.cards.pop_at(index)
	return removedCard

func shuffle() -> void:
	self.cards.shuffle()

# empty this CardCollection
func empty() -> Array[Card]:
	var removedCards:Array[Card] = self.cards
	self.cards = []
	#self.ownedPlayer = null
	return removedCards
