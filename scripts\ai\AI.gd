class_name AI
extends Player

const NEGATIVE_EFFECT_KEYWORDS = [
	Keyword.SWAP_OWNER,
	Keyword.LOWER_POWER_WINS
	# DISABLE_SLOT removed from negative effects
]

var arena:Arena
signal aiCastCard(card:Card, laneIndex:int, slotIndex:int)
signal aiEndTurn

func _ready():
	super()

func setArena(_arena:Arena) -> void:
	self.arena = _arena

func isLaneSafe(lane: LaneUIController) -> bool:
	var laneLocation = lane.getLocationByPlayer(TurnService.getOpponent(self))
	if not laneLocation:
		return true
		
	for ability in laneLocation.getAbilities():
		var keyword = ability.getKeyword().getName()
		if NEGATIVE_EFFECT_KEYWORDS.has(keyword):
			return false
	
	return true

func think() -> void:
	var legalCasts = getLegalCasts()
	
	# Check if we've already won 2 lanes - if so, just end turn
	var winningLanes = 0
	for i in Const.LANE_COUNT:
		var lane = arena.getLane(i)
		if lane.isPlayerWinning(self):
			winningLanes += 1
	
	if winningLanes >= 2:
		aiEndTurn.emit()
		return
	
	# Filter out moves on lanes with negative effects
	var safeCasts: Array[Dictionary] = []
	for castOption in legalCasts:
		var lane = arena.getLane(castOption["laneIndex"])
		var isSafe = isLaneSafe(lane)
		
		#if castOption["card"].getCastCondition() == CastCondition.BOUNCE:
			#if lane.isPlayerWinning(self):
				#isSafe = false
		
		if isSafe:
			safeCasts.append(castOption)
	
	# If no safe moves, end turn
	if safeCasts.size() == 0:
		aiEndTurn.emit()
		return
	
	# Play only safe moves
	while safeCasts.size() > 0:
		var bestCast = getBestCast(safeCasts)
		await arena.cardCastEvent(bestCast["card"], bestCast["laneIndex"], bestCast["slotIndex"], self)
		
		# Recalculate legal and safe moves
		legalCasts = getLegalCasts()
		safeCasts = []
		for castOption in legalCasts:
			var lane = arena.getLane(castOption["laneIndex"])
			var laneLocation = lane.getLocationByPlayer(TurnService.getOpponent(self))
			var isSafe = true
			
			if laneLocation:
				for ability in laneLocation.getAbilities():
					var keyword = ability.getKeyword().getName()
					if keyword == Keyword.DISABLE_SLOT or keyword == Keyword.SWAP_OWNER or keyword == Keyword.LOWER_POWER_WINS:
						isSafe = false
						break
			
			if castOption["card"].getCastCondition() == CastCondition.BOUNCE:
				if lane.isPlayerWinning(self):
					isSafe = false
			
			if isSafe:
				safeCasts.append(castOption)
	
	aiEndTurn.emit()

func getBestCast(legalCasts:Array[Dictionary]) -> Dictionary:
	# First, filter out obviously bad moves like sacrificing a stronger card for a weaker one
	var filteredCasts = []
	for cast in legalCasts:
		var shouldKeep = true
		if cast["card"].getCastCondition() == CastCondition.SACRIFICE:
			var slotCard = arena.getLane(cast["laneIndex"]).getSlotTopCard(cast["slotIndex"])
			if slotCard and slotCard.getPower() > cast["card"].getPower() and legalCasts.size() > 1:
				shouldKeep = false
		
		# Filter out moves on lanes with negative effects if there are other options
		var lane = arena.getLane(cast["laneIndex"])
		if not isLaneSafe(lane) and legalCasts.size() > 1:
			shouldKeep = false
		
		if shouldKeep:
			filteredCasts.append(cast)
	
	if filteredCasts.size() == 0:
		return legalCasts[0]  # If all moves are bad, pick the first one
	
	# Evaluate each move based on a simple scoring system
	var bestScore = -999
	var bestCast = filteredCasts[0]
	
	for cast in filteredCasts:
		var score = evaluateCast(cast)
		if score > bestScore:
			bestScore = score
			bestCast = cast
	
	return bestCast

func evaluateCast(cast:Dictionary) -> float:
	var score = 0.0
	var card = cast["card"]
	var laneIndex = cast["laneIndex"]
	var slotIndex = cast["slotIndex"]
	
	# Base score is the card's power
	score += card.getPower()
	
	# Check for negative lane effects and heavily penalize playing in those lanes
	var lane = arena.getLane(laneIndex)
	if not isLaneSafe(lane):
		score -= 20.0
	
	# Check if this lane is close to winning or losing
	var laneCards = lane.getAllCards()
	var myPower = 0
	var opponentPower = 0
	
	for laneCard in laneCards:
		if laneCard.getOwner() == self:
			myPower += laneCard.getPower()
		else:
			opponentPower += laneCard.getPower()
	
	# Check if we're already winning this lane by a significant margin
	if lane.isPlayerWinning(self) and myPower > opponentPower + 5:
		# Heavily penalize playing more cards in a lane we're already dominating
		score -= 10.0
	# Prioritize lanes where we're close to winning but not yet winning
	elif myPower >= opponentPower - 3 and myPower <= opponentPower + 3:
		score += 5.0
	# Prioritize lanes where we're losing but can catch up
	elif myPower < opponentPower and opponentPower - myPower < 8:
		score += 4.0
	# Prioritize lanes where opponent is very strong (to counter them)
	elif opponentPower > 8:
		score += 3.0
	
	# Count how many lanes we're already winning
	var winningLanes = 0
	for i in Const.LANE_COUNT:
		if arena.getLane(i).isPlayerWinning(self):
			winningLanes += 1
	
	# If we're already winning 2 lanes, focus on defense
	if winningLanes >= 2:
		# Prioritize defensive plays
		if card.getCastCondition() == CastCondition.SACRIFICE or card.getCastCondition() == CastCondition.BOUNCE:
			score += 2.0
	# If we're not winning any lanes, be more aggressive
	elif winningLanes == 0:
		# Prioritize high power cards
		score += card.getPower() * 0.5
	
	# Consider card abilities
	for ability in card.getAbilities():
		if ability.getKeyword().name == Keyword.PROD_MANA:
			score += 5.0  # Permanent mana generation is very valuable
		elif ability.getKeyword().name == Keyword.TEMP_MANA:
			score += 4.0  # Temporary mana generation is valuable
		elif ability.getKeyword().name == Keyword.WEAKEN:
			score += 3.0  # Weakening opponents is valuable
		elif ability.getKeyword().name == Keyword.DESTROY:
			score += 3.5  # Removal is very valuable
		elif ability.getKeyword().name == Keyword.STRENGTHEN:
			score += 2.0  # Buffs are good
	
	# Consider card cost efficiency (power/cost ratio)
	if card.getCost() > 0:
		score += (card.getPower() / card.getCost()) * 1.5
	
	# Penalize bounce cards that would create infinite loops
	# if card.getCastCondition() == CastCondition.BOUNCE:
	# 	score -= 2.0
	
	# Add some randomness to avoid predictable play
	score += randf_range(0, 1.0)
	
	return score

func getLegalCasts() -> Array[Dictionary]:
	var legalCasts:Array[Dictionary] = []
	for card in self.getHand().getCardCollection().cards:
		for laneIndex in Const.LANE_COUNT:
			for slotIndex in Const.CARD_PER_LANE:
				if arena.isCastable(card, laneIndex, slotIndex, self):
					legalCasts.append({
						"card": card,
						"laneIndex": laneIndex,
						"slotIndex": slotIndex
					})
	return legalCasts
