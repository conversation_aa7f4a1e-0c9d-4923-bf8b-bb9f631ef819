class_name TargetCondition
enum {
	NONE,
	RA<PERSON>OM,
	ALON<PERSON>,
	STRONGEST,
	WEAKEST,
	STRENGTHENED,
	WEAKENED,
	ODD_POWER,
	EVEN_POWER,
	BASE_POWER_AT_LEAST,
	COST_LOWER_THAN,
	COST_HIGHER_THAN,
	HAS_EMPOWER,
	HAS_SACRIFICE,
	HAS_ON_ENTER,
	HAS_ON_DEAD,
	HAS_ON_ARENA,
	EXACT_TURN_STAY,
	ALL
}

static var labelDict = {
	NONE: "",
	RANDOM: "Rand",
	ALONE: "Alone",
	STRONGEST: "Strongest",
	WEAKEST: "Weakest",
	STRENGTHENED: "Strengthened",
	WEAKENED: "Weakened",
	ODD_POWER: "Odd power",
	EVEN_POWER: "Even power",
	BASE_POWER_AT_LEAST: "Power at least",
	COST_LOWER_THAN: "Cost lower than",
	COST_HIGHER_THAN: "Cost higher than",
	HAS_EMPOWER: "Has Empower",
	HAS_SACRIFICE: "Has 🩸",
	HAS_ON_ENTER: "Has 🔽",
	HAS_ON_DEAD: "Has 💀",
	HAS_ON_ARENA: "Has ⚔️",
	EXACT_TURN_STAY: "On arena for",
	ALL: "All"
}

static func filterTargetCardCondition(_cards:Array[Card], _target:Target) -> Array[Card]:
	var filteredCards: Array[Card] = []
	match _target.getCondition():
		RANDOM:
			if not _cards.is_empty():
				filteredCards.append(_cards[randi() % _cards.size()])
		ALONE:
			if _cards.size() == 1:
				filteredCards.append(_cards[0])
		STRONGEST:
			if not _cards.is_empty():
				var strongestCard: Card = _cards[0]
				for card in _cards:
					if card.getPower() > strongestCard.getPower():
						strongestCard = card
				filteredCards.append(strongestCard)
		WEAKEST:
			if not _cards.is_empty():
				var weakestCard: Card = _cards[0]
				for card in _cards:
					if card.getPower() < weakestCard.getPower():
						weakestCard = card
				filteredCards.append(weakestCard)
		STRENGTHENED:
			for card in _cards:
				if card.getDiffPower() > 0:
					filteredCards.append(card)
		WEAKENED:
			for card in _cards:
				if card.getDiffPower() < 0:
					filteredCards.append(card)
		EVEN_POWER:
			for card in _cards:
				if card.getPower() % 2 == 0:
					filteredCards.append(card)
		ODD_POWER:
			for card in _cards:
				if card.getPower() % 2 != 0:
					filteredCards.append(card)
		BASE_POWER_AT_LEAST:
			for card in _cards:
				if card.getBasePower() >= _target.getConditionTier():
					filteredCards.append(card)
		COST_LOWER_THAN:
			for card in _cards:
				if card.getCost() < _target.getConditionTier():
					filteredCards.append(card)
		COST_HIGHER_THAN:
			for card in _cards:
				if card.getCost() > _target.getConditionTier():
					filteredCards.append(card)
		HAS_EMPOWER:
			for card in _cards:
				if card.hasAbilityKeyword(Keyword.EMPOWER):
					filteredCards.append(card)
		HAS_SACRIFICE:
			for card in _cards:
				if card.hasCastCondition(CastCondition.SACRIFICE):
					filteredCards.append(card)
		HAS_ON_ENTER:
			for card in _cards:
				for ability in card.getAbilities():
					if ability and ability.getTrigger().getName() == Trigger.ON_ENTER:
						filteredCards.append(card)
						break #Avoid adding same card when it has 2 on enter abilities
		HAS_ON_DEAD:
			for card in _cards:
				for ability in card.getAbilities():
					if ability and ability.getTrigger().getName() == Trigger.ON_DEAD:
						filteredCards.append(card)
						break
		HAS_ON_ARENA:
			for card in _cards:
				for ability in card.getAbilities():
					if ability and ability.getTrigger().getName() == Trigger.ON_ARENA:
						filteredCards.append(card)
						break
		EXACT_TURN_STAY:
			for card in _cards:
				if TurnService.turnCountfromPresent(card.getEnterArenaOnTurn()) == _target.getConditionTier() * 2:
					filteredCards.append(card)
		ALL:
			filteredCards = _cards
		_:
			filteredCards = _cards
	return filteredCards
