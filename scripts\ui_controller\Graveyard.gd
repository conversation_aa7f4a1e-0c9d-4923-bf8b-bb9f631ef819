class_name <PERSON><PERSON>
extends Card<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>roller

# Called when the node enters the scene tree for the first time.
func _ready():
	pass # Replace with function body.


func add(p_cards:Array[Card]) -> void:
	cardCollection.add(p_cards)
	for card in p_cards:
		var card3d = card_3dui.instantiate()
		card3d.displayCard(card)
		card3d.scale = Vector3.ONE * 0.4
		add_child(card3d)
		card3d.set_layer(3, true)
		#card3d.set_layer(1, false)
		#card3d.set_flipup(true)
		var movingQueue
		if getOwner().isOnMainSide():
			movingQueue = battleNode.find_child("Player").movingQueue
		else:
			movingQueue = battleNode.find_child("Enemy").movingQueue
		if movingQueue.get_child_count() > 0:
			card3d.transform.origin = movingQueue.get_child(0).global_position
			movingQueue.get_child(0).free()
		card3d.cardSelected.connect(battleNode.inspect_card)
		battleNode.inspecting.connect(card3d.change_collision_mode)
	stackCard()

func addSingle(p_cards: Card) -> void:
	self.cardCollection.addSingle(p_cards)
	var card3d = card_3dui.instantiate()
	card3d.displayCard(p_cards)
	card3d.scale = Vector3.ONE * 0.4
	add_child(card3d)
	card3d.set_layer(3, true)
	#card3d.set_flipup(true)
	var movingQueue
	if getOwner().isOnMainSide():
		movingQueue = battleNode.find_child("Player").movingQueue
	else:
		movingQueue = battleNode.find_child("Enemy").movingQueue
	if movingQueue.get_child_count() > 0:
		card3d.transform.origin = movingQueue.get_child(0).global_position
		movingQueue.get_child(0).free()
	#if movingQueue.get_child_count() > 0:
		#card3d.transform.origin = movingQueue.get_child(0).global_position
		#movingQueue.get_child(0).queue_free()
	card3d.cardSelected.connect(battleNode.inspect_card)
	battleNode.inspecting.connect(card3d.change_collision_mode)
	stackCard()

# move cards to another CardCollection
func removeCards(p_cards: Array[Card]) -> Array[Card]:
	super(p_cards)
	stackCard()
	return self.cardCollection.removeCards(p_cards)

func removeSingle(p_cards: Card) -> Card:
	super(p_cards)
	stackCard()
	return self.cardCollection.removeSingle(p_cards)

# move top x cards to another CardCollection
func pull(amount: int) -> Array[Card]:
	super(amount)
	stackCard()
	return self.cardCollection.pull(amount)

# remove card at index from this CardCollection
func removeAt(index: int) -> Card:
	super(index)
	stackCard()
	return self.cardCollection.removeAt(index)

# empty this CardCollection
func empty() -> Array[Card]:
	super()
	stackCard()
	return self.cardCollection.empty()

func stackCard() -> void:
	if get_parent().onMainSide:
		battleNode.graveyardListPanelMP.update_graveyard_list(get_parent())
	else:
		battleNode.graveyardListPanelOP.update_graveyard_list(get_parent())
	var camera = get_viewport().get_camera_3d()
	var destination := global_transform
	for card in get_children():
		card.set_layer(20, false)
		card.name_visible(true)
		destination.basis = camera.global_transform.basis
		#destination.origin += camera.basis * Vector3.UP * 0.1
		destination.origin += camera.basis * Vector3.BACK * 0.03
		#destination.origin += camera.basis * Vector3.LEFT * 1
		card.target_transform.origin = destination.origin
		card.target_rotation = deg_to_rad(randf_range(-0.2, 0.2))
		#card.target_transform.basis.rotated(Vector3.FORWARD, deg_to_rad(180))
		#card.rotation.y = deg_to_rad(180)
		#card.transform.origin.z = positionZ
