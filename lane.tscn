[gd_scene load_steps=36 format=3 uid="uid://c0ium68frd1r0"]

[ext_resource type="Script" path="res://scripts/ui_controller/LaneUIController.gd" id="1_srndj"]
[ext_resource type="PackedScene" uid="uid://b8nvtpi41igew" path="res://card_collection.tscn" id="2_fpni3"]
[ext_resource type="Script" path="res://scripts/objects/LaneCardSlot.gd" id="3_8wdlj"]
[ext_resource type="FontFile" uid="uid://c761juhjl1ha0" path="res://Assets/Fonts/FAV/Chicalo.otf" id="5_isyxg"]
[ext_resource type="PackedScene" uid="uid://bli2bf40bd7lh" path="res://Location.tscn" id="5_k5ip2"]
[ext_resource type="Texture2D" uid="uid://b3srpmvvyah5d" path="res://Assets/DCN_Playfield_PA_Export-assets/DCN_PF_PA/DCN_PF_PA_PF/DCN_PF_Playfield.png" id="7_bux2g"]
[ext_resource type="Shader" uid="uid://dmb18tjtgw5aq" path="res://igniteGlow.tres" id="7_isibn"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_aippo"]
transparency = 1
blend_mode = 1
shading_mode = 0
albedo_color = Color(0.380392, 0.847059, 0.258824, 0.0980392)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_hl3uc"]
resource_name = "Material"
cull_mode = 2
albedo_color = Color(0.906332, 0.906332, 0.906332, 1)
roughness = 0.4

[sub_resource type="ArrayMesh" id="ArrayMesh_0dym6"]
_surfaces = [{
"aabb": AABB(-1, -1, -1, 2, 2, 2),
"format": 34896613377,
"index_count": 900,
"index_data": PackedByteArray(97, 0, 59, 0, 19, 0, 97, 0, 133, 0, 59, 0, 135, 0, 77, 0, 114, 0, 135, 0, 95, 0, 77, 0, 58, 0, 115, 0, 40, 0, 58, 0, 134, 0, 115, 0, 1, 0, 116, 0, 78, 0, 1, 0, 39, 0, 116, 0, 21, 0, 38, 0, 2, 0, 21, 0, 57, 0, 38, 0, 0, 0, 6, 0, 3, 0, 0, 0, 5, 0, 6, 0, 3, 0, 7, 0, 4, 0, 3, 0, 6, 0, 7, 0, 5, 0, 18, 0, 6, 0, 5, 0, 15, 0, 18, 0, 6, 0, 8, 0, 7, 0, 6, 0, 18, 0, 8, 0, 1, 0, 12, 0, 9, 0, 1, 0, 11, 0, 12, 0, 9, 0, 13, 0, 10, 0, 9, 0, 12, 0, 13, 0, 11, 0, 7, 0, 12, 0, 11, 0, 4, 0, 7, 0, 12, 0, 8, 0, 13, 0, 12, 0, 7, 0, 8, 0, 2, 0, 17, 0, 14, 0, 2, 0, 16, 0, 17, 0, 14, 0, 18, 0, 15, 0, 14, 0, 17, 0, 18, 0, 16, 0, 13, 0, 17, 0, 16, 0, 10, 0, 13, 0, 17, 0, 8, 0, 18, 0, 17, 0, 13, 0, 8, 0, 19, 0, 25, 0, 22, 0, 19, 0, 24, 0, 25, 0, 22, 0, 26, 0, 23, 0, 22, 0, 25, 0, 26, 0, 24, 0, 37, 0, 25, 0, 24, 0, 34, 0, 37, 0, 25, 0, 27, 0, 26, 0, 25, 0, 37, 0, 27, 0, 20, 0, 31, 0, 28, 0, 20, 0, 30, 0, 31, 0, 28, 0, 32, 0, 29, 0, 28, 0, 31, 0, 32, 0, 30, 0, 26, 0, 31, 0, 30, 0, 23, 0, 26, 0, 31, 0, 27, 0, 32, 0, 31, 0, 26, 0, 27, 0, 21, 0, 36, 0, 33, 0, 21, 0, 35, 0, 36, 0, 33, 0, 37, 0, 34, 0, 33, 0, 36, 0, 37, 0, 35, 0, 32, 0, 36, 0, 35, 0, 29, 0, 32, 0, 36, 0, 27, 0, 37, 0, 36, 0, 32, 0, 27, 0, 38, 0, 44, 0, 41, 0, 38, 0, 43, 0, 44, 0, 41, 0, 45, 0, 42, 0, 41, 0, 44, 0, 45, 0, 43, 0, 56, 0, 44, 0, 43, 0, 53, 0, 56, 0, 44, 0, 46, 0, 45, 0, 44, 0, 56, 0, 46, 0, 39, 0, 50, 0, 47, 0, 39, 0, 49, 0, 50, 0, 47, 0, 51, 0, 48, 0, 47, 0, 50, 0, 51, 0, 49, 0, 45, 0, 50, 0, 49, 0, 42, 0, 45, 0, 50, 0, 46, 0, 51, 0, 50, 0, 45, 0, 46, 0, 40, 0, 55, 0, 52, 0, 40, 0, 54, 0, 55, 0, 52, 0, 56, 0, 53, 0, 52, 0, 55, 0, 56, 0, 54, 0, 51, 0, 55, 0, 54, 0, 48, 0, 51, 0, 55, 0, 46, 0, 56, 0, 55, 0, 51, 0, 46, 0, 57, 0, 63, 0, 60, 0, 57, 0, 62, 0, 63, 0, 60, 0, 64, 0, 61, 0, 60, 0, 63, 0, 64, 0, 62, 0, 75, 0, 63, 0, 62, 0, 72, 0, 75, 0, 63, 0, 65, 0, 64, 0, 63, 0, 75, 0, 65, 0, 58, 0, 69, 0, 66, 0, 58, 0, 68, 0, 69, 0, 66, 0, 70, 0, 67, 0, 66, 0, 69, 0, 70, 0, 68, 0, 64, 0, 69, 0, 68, 0, 61, 0, 64, 0, 69, 0, 65, 0, 70, 0, 69, 0, 64, 0, 65, 0, 59, 0, 74, 0, 71, 0, 59, 0, 73, 0, 74, 0, 71, 0, 75, 0, 72, 0, 71, 0, 74, 0, 75, 0, 73, 0, 70, 0, 74, 0, 73, 0, 67, 0, 70, 0, 74, 0, 65, 0, 75, 0, 74, 0, 70, 0, 65, 0, 76, 0, 82, 0, 79, 0, 76, 0, 81, 0, 82, 0, 79, 0, 83, 0, 80, 0, 79, 0, 82, 0, 83, 0, 81, 0, 94, 0, 82, 0, 81, 0, 91, 0, 94, 0, 82, 0, 84, 0, 83, 0, 82, 0, 94, 0, 84, 0, 77, 0, 88, 0, 85, 0, 77, 0, 87, 0, 88, 0, 85, 0, 89, 0, 86, 0, 85, 0, 88, 0, 89, 0, 87, 0, 83, 0, 88, 0, 87, 0, 80, 0, 83, 0, 88, 0, 84, 0, 89, 0, 88, 0, 83, 0, 84, 0, 78, 0, 93, 0, 90, 0, 78, 0, 92, 0, 93, 0, 90, 0, 94, 0, 91, 0, 90, 0, 93, 0, 94, 0, 92, 0, 89, 0, 93, 0, 92, 0, 86, 0, 89, 0, 93, 0, 84, 0, 94, 0, 93, 0, 89, 0, 84, 0, 95, 0, 101, 0, 98, 0, 95, 0, 100, 0, 101, 0, 98, 0, 102, 0, 99, 0, 98, 0, 101, 0, 102, 0, 100, 0, 113, 0, 101, 0, 100, 0, 110, 0, 113, 0, 101, 0, 103, 0, 102, 0, 101, 0, 113, 0, 103, 0, 96, 0, 107, 0, 104, 0, 96, 0, 106, 0, 107, 0, 104, 0, 108, 0, 105, 0, 104, 0, 107, 0, 108, 0, 106, 0, 102, 0, 107, 0, 106, 0, 99, 0, 102, 0, 107, 0, 103, 0, 108, 0, 107, 0, 102, 0, 103, 0, 97, 0, 112, 0, 109, 0, 97, 0, 111, 0, 112, 0, 109, 0, 113, 0, 110, 0, 109, 0, 112, 0, 113, 0, 111, 0, 108, 0, 112, 0, 111, 0, 105, 0, 108, 0, 112, 0, 103, 0, 113, 0, 112, 0, 108, 0, 103, 0, 114, 0, 120, 0, 117, 0, 114, 0, 119, 0, 120, 0, 117, 0, 121, 0, 118, 0, 117, 0, 120, 0, 121, 0, 119, 0, 132, 0, 120, 0, 119, 0, 129, 0, 132, 0, 120, 0, 122, 0, 121, 0, 120, 0, 132, 0, 122, 0, 115, 0, 126, 0, 123, 0, 115, 0, 125, 0, 126, 0, 123, 0, 127, 0, 124, 0, 123, 0, 126, 0, 127, 0, 125, 0, 121, 0, 126, 0, 125, 0, 118, 0, 121, 0, 126, 0, 122, 0, 127, 0, 126, 0, 121, 0, 122, 0, 116, 0, 131, 0, 128, 0, 116, 0, 130, 0, 131, 0, 128, 0, 132, 0, 129, 0, 128, 0, 131, 0, 132, 0, 130, 0, 127, 0, 131, 0, 130, 0, 124, 0, 127, 0, 131, 0, 122, 0, 132, 0, 131, 0, 127, 0, 122, 0, 133, 0, 139, 0, 136, 0, 133, 0, 138, 0, 139, 0, 136, 0, 140, 0, 137, 0, 136, 0, 139, 0, 140, 0, 138, 0, 151, 0, 139, 0, 138, 0, 148, 0, 151, 0, 139, 0, 141, 0, 140, 0, 139, 0, 151, 0, 141, 0, 134, 0, 145, 0, 142, 0, 134, 0, 144, 0, 145, 0, 142, 0, 146, 0, 143, 0, 142, 0, 145, 0, 146, 0, 144, 0, 140, 0, 145, 0, 144, 0, 137, 0, 140, 0, 145, 0, 141, 0, 146, 0, 145, 0, 140, 0, 141, 0, 135, 0, 150, 0, 147, 0, 135, 0, 149, 0, 150, 0, 147, 0, 151, 0, 148, 0, 147, 0, 150, 0, 151, 0, 149, 0, 146, 0, 150, 0, 149, 0, 143, 0, 146, 0, 150, 0, 141, 0, 151, 0, 150, 0, 146, 0, 141, 0, 97, 0, 138, 0, 133, 0, 97, 0, 109, 0, 138, 0, 109, 0, 148, 0, 138, 0, 109, 0, 110, 0, 148, 0, 110, 0, 147, 0, 148, 0, 110, 0, 100, 0, 147, 0, 100, 0, 135, 0, 147, 0, 100, 0, 95, 0, 135, 0, 19, 0, 111, 0, 97, 0, 19, 0, 22, 0, 111, 0, 22, 0, 105, 0, 111, 0, 22, 0, 23, 0, 105, 0, 23, 0, 104, 0, 105, 0, 23, 0, 30, 0, 104, 0, 30, 0, 96, 0, 104, 0, 30, 0, 20, 0, 96, 0, 2, 0, 35, 0, 21, 0, 2, 0, 14, 0, 35, 0, 14, 0, 29, 0, 35, 0, 14, 0, 15, 0, 29, 0, 15, 0, 28, 0, 29, 0, 15, 0, 5, 0, 28, 0, 5, 0, 20, 0, 28, 0, 5, 0, 0, 0, 20, 0, 134, 0, 125, 0, 115, 0, 134, 0, 142, 0, 125, 0, 142, 0, 118, 0, 125, 0, 142, 0, 143, 0, 118, 0, 143, 0, 117, 0, 118, 0, 143, 0, 149, 0, 117, 0, 149, 0, 114, 0, 117, 0, 149, 0, 135, 0, 114, 0, 40, 0, 68, 0, 58, 0, 40, 0, 52, 0, 68, 0, 52, 0, 61, 0, 68, 0, 52, 0, 53, 0, 61, 0, 53, 0, 60, 0, 61, 0, 53, 0, 43, 0, 60, 0, 43, 0, 57, 0, 60, 0, 43, 0, 38, 0, 57, 0, 76, 0, 106, 0, 96, 0, 76, 0, 79, 0, 106, 0, 79, 0, 99, 0, 106, 0, 79, 0, 80, 0, 99, 0, 80, 0, 98, 0, 99, 0, 80, 0, 87, 0, 98, 0, 87, 0, 95, 0, 98, 0, 87, 0, 77, 0, 95, 0, 39, 0, 130, 0, 116, 0, 39, 0, 47, 0, 130, 0, 47, 0, 124, 0, 130, 0, 47, 0, 48, 0, 124, 0, 48, 0, 123, 0, 124, 0, 48, 0, 54, 0, 123, 0, 54, 0, 115, 0, 123, 0, 54, 0, 40, 0, 115, 0, 1, 0, 49, 0, 39, 0, 1, 0, 9, 0, 49, 0, 9, 0, 42, 0, 49, 0, 9, 0, 10, 0, 42, 0, 10, 0, 41, 0, 42, 0, 10, 0, 16, 0, 41, 0, 16, 0, 38, 0, 41, 0, 16, 0, 2, 0, 38, 0, 133, 0, 73, 0, 59, 0, 133, 0, 136, 0, 73, 0, 136, 0, 67, 0, 73, 0, 136, 0, 137, 0, 67, 0, 137, 0, 66, 0, 67, 0, 137, 0, 144, 0, 66, 0, 144, 0, 58, 0, 66, 0, 144, 0, 134, 0, 58, 0, 116, 0, 92, 0, 78, 0, 116, 0, 128, 0, 92, 0, 128, 0, 86, 0, 92, 0, 128, 0, 129, 0, 86, 0, 129, 0, 85, 0, 86, 0, 129, 0, 119, 0, 85, 0, 119, 0, 77, 0, 85, 0, 119, 0, 114, 0, 77, 0, 78, 0, 11, 0, 1, 0, 78, 0, 90, 0, 11, 0, 90, 0, 4, 0, 11, 0, 90, 0, 91, 0, 4, 0, 91, 0, 3, 0, 4, 0, 91, 0, 81, 0, 3, 0, 81, 0, 0, 0, 3, 0, 81, 0, 76, 0, 0, 0, 59, 0, 24, 0, 19, 0, 59, 0, 71, 0, 24, 0, 71, 0, 34, 0, 24, 0, 71, 0, 72, 0, 34, 0, 72, 0, 33, 0, 34, 0, 72, 0, 62, 0, 33, 0, 62, 0, 21, 0, 33, 0, 62, 0, 57, 0, 21, 0, 96, 0, 0, 0, 76, 0, 96, 0, 20, 0, 0, 0),
"lods": [0.0179063, PackedByteArray(97, 0, 59, 0, 19, 0, 19, 0, 23, 0, 97, 0, 23, 0, 27, 0, 26, 0, 19, 0, 27, 0, 23, 0, 23, 0, 105, 0, 97, 0, 19, 0, 21, 0, 27, 0, 59, 0, 21, 0, 19, 0, 97, 0, 105, 0, 108, 0, 97, 0, 108, 0, 113, 0, 113, 0, 108, 0, 103, 0, 97, 0, 113, 0, 110, 0, 97, 0, 110, 0, 133, 0, 97, 0, 133, 0, 59, 0, 110, 0, 148, 0, 133, 0, 133, 0, 148, 0, 151, 0, 133, 0, 151, 0, 137, 0, 133, 0, 137, 0, 59, 0, 137, 0, 151, 0, 140, 0, 151, 0, 141, 0, 140, 0, 137, 0, 67, 0, 59, 0, 59, 0, 67, 0, 70, 0, 70, 0, 65, 0, 59, 0, 57, 0, 59, 0, 65, 0, 59, 0, 57, 0, 21, 0, 58, 0, 57, 0, 65, 0, 58, 0, 65, 0, 67, 0, 67, 0, 65, 0, 70, 0, 134, 0, 58, 0, 67, 0, 137, 0, 134, 0, 67, 0, 134, 0, 137, 0, 140, 0, 134, 0, 140, 0, 146, 0, 146, 0, 140, 0, 141, 0, 134, 0, 146, 0, 143, 0, 134, 0, 143, 0, 115, 0, 58, 0, 134, 0, 115, 0, 143, 0, 118, 0, 115, 0, 115, 0, 118, 0, 121, 0, 115, 0, 121, 0, 127, 0, 127, 0, 121, 0, 122, 0, 115, 0, 127, 0, 124, 0, 58, 0, 115, 0, 40, 0, 40, 0, 115, 0, 124, 0, 48, 0, 40, 0, 124, 0, 40, 0, 48, 0, 51, 0, 40, 0, 57, 0, 58, 0, 51, 0, 46, 0, 40, 0, 40, 0, 38, 0, 57, 0, 38, 0, 40, 0, 46, 0, 21, 0, 57, 0, 38, 0, 39, 0, 38, 0, 46, 0, 21, 0, 38, 0, 2, 0, 2, 0, 20, 0, 21, 0, 1, 0, 38, 0, 39, 0, 1, 0, 2, 0, 38, 0, 39, 0, 46, 0, 48, 0, 48, 0, 46, 0, 51, 0, 39, 0, 48, 0, 116, 0, 1, 0, 39, 0, 116, 0, 48, 0, 124, 0, 116, 0, 116, 0, 124, 0, 127, 0, 116, 0, 127, 0, 132, 0, 132, 0, 127, 0, 122, 0, 116, 0, 132, 0, 129, 0, 116, 0, 129, 0, 78, 0, 1, 0, 116, 0, 78, 0, 129, 0, 86, 0, 78, 0, 78, 0, 86, 0, 89, 0, 78, 0, 89, 0, 94, 0, 94, 0, 89, 0, 84, 0, 78, 0, 94, 0, 91, 0, 78, 0, 91, 0, 1, 0, 91, 0, 4, 0, 1, 0, 1, 0, 4, 0, 7, 0, 1, 0, 7, 0, 8, 0, 2, 0, 1, 0, 8, 0, 0, 0, 2, 0, 8, 0, 0, 0, 8, 0, 4, 0, 2, 0, 0, 0, 20, 0, 4, 0, 8, 0, 7, 0, 76, 0, 0, 0, 4, 0, 91, 0, 76, 0, 4, 0, 76, 0, 91, 0, 94, 0, 96, 0, 0, 0, 76, 0, 96, 0, 20, 0, 0, 0, 76, 0, 94, 0, 83, 0, 94, 0, 84, 0, 83, 0, 77, 0, 76, 0, 83, 0, 89, 0, 83, 0, 84, 0, 77, 0, 83, 0, 89, 0, 77, 0, 89, 0, 86, 0, 76, 0, 77, 0, 95, 0, 76, 0, 95, 0, 96, 0, 114, 0, 77, 0, 86, 0, 129, 0, 114, 0, 86, 0, 114, 0, 129, 0, 132, 0, 132, 0, 122, 0, 121, 0, 118, 0, 132, 0, 121, 0, 114, 0, 132, 0, 118, 0, 135, 0, 77, 0, 114, 0, 135, 0, 114, 0, 118, 0, 135, 0, 95, 0, 77, 0, 143, 0, 135, 0, 118, 0, 135, 0, 143, 0, 146, 0, 135, 0, 146, 0, 151, 0, 151, 0, 146, 0, 141, 0, 135, 0, 151, 0, 148, 0, 95, 0, 135, 0, 148, 0, 110, 0, 95, 0, 148, 0, 95, 0, 110, 0, 113, 0, 95, 0, 113, 0, 102, 0, 113, 0, 103, 0, 102, 0, 96, 0, 95, 0, 102, 0, 96, 0, 102, 0, 108, 0, 108, 0, 102, 0, 103, 0, 96, 0, 108, 0, 105, 0, 20, 0, 96, 0, 105, 0, 23, 0, 20, 0, 105, 0, 20, 0, 23, 0, 26, 0, 20, 0, 26, 0, 27, 0, 21, 0, 20, 0, 27, 0), 0.0210464, PackedByteArray(105, 0, 59, 0, 27, 0, 110, 0, 105, 0, 103, 0, 105, 0, 110, 0, 148, 0, 105, 0, 148, 0, 59, 0, 148, 0, 141, 0, 137, 0, 148, 0, 137, 0, 59, 0, 137, 0, 67, 0, 59, 0, 67, 0, 65, 0, 59, 0, 59, 0, 21, 0, 27, 0, 59, 0, 65, 0, 21, 0, 21, 0, 65, 0, 46, 0, 21, 0, 46, 0, 2, 0, 2, 0, 23, 0, 21, 0, 48, 0, 46, 0, 65, 0, 48, 0, 65, 0, 58, 0, 58, 0, 65, 0, 67, 0, 137, 0, 58, 0, 67, 0, 58, 0, 118, 0, 48, 0, 58, 0, 137, 0, 118, 0, 48, 0, 118, 0, 124, 0, 124, 0, 118, 0, 122, 0, 137, 0, 143, 0, 118, 0, 143, 0, 137, 0, 141, 0, 2, 0, 4, 0, 23, 0, 96, 0, 23, 0, 4, 0, 96, 0, 4, 0, 83, 0, 23, 0, 96, 0, 105, 0, 96, 0, 102, 0, 105, 0, 83, 0, 102, 0, 96, 0, 105, 0, 102, 0, 103, 0, 91, 0, 84, 0, 83, 0, 91, 0, 83, 0, 4, 0, 4, 0, 2, 0, 8, 0, 86, 0, 83, 0, 84, 0, 77, 0, 83, 0, 86, 0, 83, 0, 77, 0, 102, 0, 129, 0, 77, 0, 86, 0, 110, 0, 103, 0, 102, 0, 110, 0, 102, 0, 148, 0, 143, 0, 102, 0, 77, 0, 102, 0, 143, 0, 148, 0, 143, 0, 77, 0, 129, 0, 148, 0, 143, 0, 141, 0, 143, 0, 129, 0, 118, 0, 129, 0, 122, 0, 118, 0, 1, 0, 124, 0, 86, 0, 124, 0, 129, 0, 86, 0, 129, 0, 124, 0, 122, 0, 86, 0, 91, 0, 1, 0, 91, 0, 86, 0, 84, 0, 91, 0, 4, 0, 1, 0, 1, 0, 4, 0, 8, 0, 2, 0, 1, 0, 8, 0, 1, 0, 2, 0, 46, 0, 1, 0, 39, 0, 124, 0, 1, 0, 46, 0, 39, 0, 39, 0, 48, 0, 124, 0, 39, 0, 46, 0, 48, 0, 21, 0, 23, 0, 27, 0, 27, 0, 23, 0, 105, 0), 0.215511, PackedByteArray(23, 0, 67, 0, 27, 0, 67, 0, 65, 0, 27, 0, 23, 0, 148, 0, 67, 0, 148, 0, 141, 0, 67, 0, 148, 0, 23, 0, 103, 0, 143, 0, 102, 0, 86, 0, 83, 0, 86, 0, 102, 0, 86, 0, 122, 0, 143, 0, 102, 0, 143, 0, 148, 0, 148, 0, 143, 0, 141, 0, 148, 0, 103, 0, 102, 0, 86, 0, 83, 0, 84, 0, 4, 0, 84, 0, 83, 0, 83, 0, 8, 0, 4, 0, 83, 0, 102, 0, 23, 0, 23, 0, 102, 0, 103, 0, 23, 0, 8, 0, 83, 0, 8, 0, 23, 0, 27, 0, 67, 0, 143, 0, 48, 0, 143, 0, 67, 0, 141, 0, 48, 0, 143, 0, 122, 0, 48, 0, 65, 0, 67, 0, 48, 0, 46, 0, 65, 0, 4, 0, 48, 0, 86, 0, 86, 0, 48, 0, 122, 0, 4, 0, 86, 0, 84, 0, 4, 0, 46, 0, 48, 0, 4, 0, 8, 0, 46, 0, 27, 0, 46, 0, 8, 0, 27, 0, 65, 0, 46, 0)],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 152,
"vertex_data": PackedByteArray(177, 248, 177, 248, 0, 0, 0, 0, 177, 248, 255, 255, 77, 7, 0, 0, 255, 255, 177, 248, 77, 7, 0, 0, 177, 248, 125, 251, 142, 0, 0, 0, 177, 248, 219, 253, 35, 2, 0, 0, 125, 251, 177, 248, 142, 0, 0, 0, 143, 251, 143, 251, 58, 1, 0, 0, 105, 251, 124, 253, 130, 2, 0, 0, 232, 252, 232, 252, 22, 3, 0, 0, 125, 251, 112, 255, 77, 7, 0, 0, 219, 253, 219, 253, 77, 7, 0, 0, 177, 248, 112, 255, 129, 4, 0, 0, 143, 251, 196, 254, 111, 4, 0, 0, 124, 253, 124, 253, 149, 4, 0, 0, 112, 255, 177, 248, 129, 4, 0, 0, 219, 253, 177, 248, 35, 2, 0, 0, 112, 255, 125, 251, 77, 7, 0, 0, 196, 254, 143, 251, 111, 4, 0, 0, 124, 253, 105, 251, 130, 2, 0, 0, 177, 248, 0, 0, 77, 7, 0, 0, 177, 248, 77, 7, 0, 0, 0, 0, 255, 255, 77, 7, 77, 7, 0, 0, 177, 248, 142, 0, 129, 4, 0, 0, 177, 248, 35, 2, 35, 2, 0, 0, 125, 251, 142, 0, 77, 7, 0, 0, 143, 251, 58, 1, 111, 4, 0, 0, 105, 251, 130, 2, 130, 2, 0, 0, 232, 252, 22, 3, 22, 3, 0, 0, 125, 251, 77, 7, 142, 0, 0, 0, 219, 253, 77, 7, 35, 2, 0, 0, 177, 248, 129, 4, 142, 0, 0, 0, 143, 251, 111, 4, 58, 1, 0, 0, 124, 253, 149, 4, 130, 2, 0, 0, 112, 255, 129, 4, 77, 7, 0, 0, 219, 253, 35, 2, 77, 7, 0, 0, 112, 255, 77, 7, 129, 4, 0, 0, 196, 254, 111, 4, 111, 4, 0, 0, 124, 253, 130, 2, 149, 4, 0, 0, 255, 255, 177, 248, 177, 248, 0, 0, 177, 248, 255, 255, 177, 248, 0, 0, 177, 248, 177, 248, 255, 255, 0, 0, 112, 255, 125, 251, 177, 248, 0, 0, 219, 253, 219, 253, 177, 248, 0, 0, 112, 255, 177, 248, 125, 251, 0, 0, 196, 254, 143, 251, 143, 251, 0, 0, 124, 253, 124, 253, 105, 251, 0, 0, 232, 252, 232, 252, 232, 252, 0, 0, 177, 248, 112, 255, 125, 251, 0, 0, 177, 248, 219, 253, 219, 253, 0, 0, 125, 251, 112, 255, 177, 248, 0, 0, 143, 251, 196, 254, 143, 251, 0, 0, 105, 251, 124, 253, 124, 253, 0, 0, 125, 251, 177, 248, 112, 255, 0, 0, 219, 253, 177, 248, 219, 253, 0, 0, 177, 248, 125, 251, 112, 255, 0, 0, 143, 251, 143, 251, 196, 254, 0, 0, 124, 253, 105, 251, 124, 253, 0, 0, 255, 255, 77, 7, 177, 248, 0, 0, 177, 248, 77, 7, 255, 255, 0, 0, 177, 248, 0, 0, 177, 248, 0, 0, 112, 255, 77, 7, 125, 251, 0, 0, 219, 253, 77, 7, 219, 253, 0, 0, 112, 255, 129, 4, 177, 248, 0, 0, 196, 254, 111, 4, 143, 251, 0, 0, 124, 253, 149, 4, 124, 253, 0, 0, 232, 252, 22, 3, 232, 252, 0, 0, 177, 248, 129, 4, 112, 255, 0, 0, 177, 248, 35, 2, 219, 253, 0, 0, 125, 251, 77, 7, 112, 255, 0, 0, 143, 251, 111, 4, 196, 254, 0, 0, 105, 251, 130, 2, 124, 253, 0, 0, 125, 251, 142, 0, 177, 248, 0, 0, 219, 253, 35, 2, 177, 248, 0, 0, 177, 248, 142, 0, 125, 251, 0, 0, 143, 251, 58, 1, 143, 251, 0, 0, 124, 253, 130, 2, 105, 251, 0, 0, 77, 7, 177, 248, 0, 0, 0, 0, 0, 0, 177, 248, 77, 7, 0, 0, 77, 7, 255, 255, 77, 7, 0, 0, 129, 4, 177, 248, 142, 0, 0, 0, 35, 2, 177, 248, 35, 2, 0, 0, 77, 7, 125, 251, 142, 0, 0, 0, 111, 4, 143, 251, 58, 1, 0, 0, 130, 2, 105, 251, 130, 2, 0, 0, 22, 3, 232, 252, 22, 3, 0, 0, 142, 0, 125, 251, 77, 7, 0, 0, 35, 2, 219, 253, 77, 7, 0, 0, 142, 0, 177, 248, 129, 4, 0, 0, 58, 1, 143, 251, 111, 4, 0, 0, 130, 2, 124, 253, 149, 4, 0, 0, 77, 7, 112, 255, 129, 4, 0, 0, 77, 7, 219, 253, 35, 2, 0, 0, 129, 4, 112, 255, 77, 7, 0, 0, 111, 4, 196, 254, 111, 4, 0, 0, 149, 4, 124, 253, 130, 2, 0, 0, 0, 0, 77, 7, 77, 7, 0, 0, 77, 7, 77, 7, 0, 0, 0, 0, 77, 7, 0, 0, 77, 7, 0, 0, 142, 0, 77, 7, 129, 4, 0, 0, 35, 2, 77, 7, 35, 2, 0, 0, 142, 0, 129, 4, 77, 7, 0, 0, 58, 1, 111, 4, 111, 4, 0, 0, 130, 2, 149, 4, 130, 2, 0, 0, 22, 3, 22, 3, 22, 3, 0, 0, 77, 7, 129, 4, 142, 0, 0, 0, 77, 7, 35, 2, 35, 2, 0, 0, 129, 4, 77, 7, 142, 0, 0, 0, 111, 4, 111, 4, 58, 1, 0, 0, 149, 4, 130, 2, 130, 2, 0, 0, 129, 4, 142, 0, 77, 7, 0, 0, 35, 2, 35, 2, 77, 7, 0, 0, 77, 7, 142, 0, 129, 4, 0, 0, 111, 4, 58, 1, 111, 4, 0, 0, 130, 2, 130, 2, 149, 4, 0, 0, 0, 0, 177, 248, 177, 248, 0, 0, 77, 7, 177, 248, 255, 255, 0, 0, 77, 7, 255, 255, 177, 248, 0, 0, 142, 0, 177, 248, 125, 251, 0, 0, 35, 2, 177, 248, 219, 253, 0, 0, 142, 0, 125, 251, 177, 248, 0, 0, 58, 1, 143, 251, 143, 251, 0, 0, 130, 2, 105, 251, 124, 253, 0, 0, 22, 3, 232, 252, 232, 252, 0, 0, 77, 7, 125, 251, 112, 255, 0, 0, 77, 7, 219, 253, 219, 253, 0, 0, 129, 4, 177, 248, 112, 255, 0, 0, 111, 4, 143, 251, 196, 254, 0, 0, 149, 4, 124, 253, 124, 253, 0, 0, 129, 4, 112, 255, 177, 248, 0, 0, 35, 2, 219, 253, 177, 248, 0, 0, 77, 7, 112, 255, 125, 251, 0, 0, 111, 4, 196, 254, 143, 251, 0, 0, 130, 2, 124, 253, 105, 251, 0, 0, 77, 7, 0, 0, 177, 248, 0, 0, 77, 7, 77, 7, 255, 255, 0, 0, 0, 0, 77, 7, 177, 248, 0, 0, 77, 7, 142, 0, 125, 251, 0, 0, 77, 7, 35, 2, 219, 253, 0, 0, 129, 4, 142, 0, 177, 248, 0, 0, 111, 4, 58, 1, 143, 251, 0, 0, 149, 4, 130, 2, 124, 253, 0, 0, 22, 3, 22, 3, 232, 252, 0, 0, 129, 4, 77, 7, 112, 255, 0, 0, 35, 2, 77, 7, 219, 253, 0, 0, 77, 7, 129, 4, 112, 255, 0, 0, 111, 4, 111, 4, 196, 254, 0, 0, 130, 2, 149, 4, 124, 253, 0, 0, 142, 0, 129, 4, 177, 248, 0, 0, 35, 2, 35, 2, 177, 248, 0, 0, 142, 0, 77, 7, 125, 251, 0, 0, 58, 1, 111, 4, 143, 251, 0, 0, 130, 2, 130, 2, 105, 251, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_208t2"]
resource_name = "card_Cube"
_surfaces = [{
"aabb": AABB(-1, -1, -1, 2, 2, 2),
"attribute_data": PackedByteArray(44, 158, 210, 129, 210, 161, 44, 126, 44, 158, 44, 126, 222, 158, 210, 129, 255, 159, 210, 129, 210, 161, 255, 127, 44, 158, 31, 129, 227, 158, 27, 129, 255, 159, 36, 129, 36, 161, 255, 127, 189, 159, 255, 127, 31, 161, 44, 126, 255, 159, 44, 126, 210, 161, 223, 126, 27, 161, 227, 126, 255, 159, 218, 126, 255, 159, 218, 126, 44, 158, 223, 126, 44, 158, 255, 127, 222, 158, 44, 126, 227, 158, 227, 126, 218, 158, 255, 127, 44, 94, 44, 126, 210, 97, 210, 129, 210, 97, 44, 126, 44, 94, 223, 126, 44, 94, 255, 127, 255, 95, 210, 129, 223, 94, 44, 126, 227, 94, 227, 126, 218, 94, 255, 127, 255, 95, 36, 129, 255, 95, 189, 127, 210, 97, 31, 129, 210, 97, 255, 127, 32, 97, 210, 129, 27, 97, 27, 129, 36, 97, 255, 127, 32, 97, 44, 126, 255, 95, 44, 126, 210, 97, 223, 126, 27, 97, 227, 126, 255, 95, 218, 126, 44, 158, 211, 65, 210, 161, 211, 65, 44, 158, 44, 62, 222, 158, 211, 65, 255, 159, 211, 65, 44, 158, 32, 65, 227, 158, 27, 65, 255, 159, 37, 65, 255, 159, 65, 64, 210, 161, 32, 65, 255, 159, 44, 62, 210, 161, 255, 63, 31, 161, 211, 65, 27, 161, 27, 65, 255, 159, 218, 62, 36, 161, 255, 63, 44, 158, 223, 62, 44, 158, 255, 63, 222, 158, 44, 62, 227, 158, 227, 62, 218, 158, 255, 63, 210, 97, 211, 65, 210, 97, 44, 62, 44, 94, 211, 65, 210, 97, 32, 65, 210, 97, 255, 63, 32, 97, 211, 65, 27, 97, 27, 65, 36, 97, 255, 63, 65, 96, 255, 63, 32, 97, 44, 62, 44, 94, 255, 63, 255, 95, 44, 62, 210, 97, 223, 62, 27, 97, 227, 62, 218, 94, 255, 63, 255, 95, 218, 62, 223, 94, 211, 65, 255, 95, 211, 65, 44, 94, 32, 65, 227, 94, 27, 65, 255, 95, 37, 65, 44, 158, 43, 190, 44, 158, 210, 193, 43, 222, 44, 126, 44, 158, 222, 190, 44, 158, 255, 191, 222, 158, 43, 190, 227, 158, 227, 190, 218, 158, 255, 191, 57, 159, 255, 191, 255, 159, 196, 192, 57, 223, 255, 127, 222, 158, 210, 193, 255, 159, 210, 193, 255, 223, 44, 126, 44, 158, 31, 193, 227, 158, 27, 193, 255, 159, 36, 193, 255, 223, 218, 126, 43, 222, 223, 126, 255, 159, 43, 190, 43, 222, 255, 127, 222, 222, 44, 126, 227, 222, 227, 126, 255, 159, 217, 190, 217, 222, 255, 127, 210, 97, 210, 193, 210, 97, 43, 190, 211, 33, 44, 126, 210, 97, 31, 193, 210, 97, 255, 191, 32, 97, 210, 193, 27, 97, 27, 193, 36, 97, 255, 191, 255, 31, 57, 127, 255, 95, 57, 191, 197, 96, 255, 191, 32, 97, 43, 190, 211, 33, 255, 127, 255, 95, 43, 190, 210, 97, 222, 190, 27, 97, 227, 190, 37, 33, 255, 127, 255, 95, 217, 190, 32, 33, 44, 126, 255, 31, 44, 126, 255, 95, 210, 193, 211, 33, 223, 126, 27, 33, 227, 126, 255, 31, 218, 126, 255, 95, 36, 193, 44, 158, 43, 254, 44, 158, 211, 1, 43, 222, 211, 65, 44, 158, 222, 254, 44, 158, 0, 0, 44, 158, 255, 255, 222, 158, 43, 254, 227, 158, 227, 254, 218, 158, 0, 0, 218, 158, 255, 255, 57, 159, 255, 255, 255, 159, 197, 0, 255, 223, 197, 64, 222, 158, 211, 1, 255, 159, 211, 1, 43, 222, 255, 63, 44, 158, 32, 1, 227, 158, 27, 1, 255, 159, 37, 1, 217, 222, 255, 63, 222, 222, 211, 65, 255, 159, 43, 254, 255, 223, 211, 65, 43, 222, 32, 65, 227, 222, 27, 65, 255, 159, 217, 254, 255, 223, 37, 65, 211, 33, 211, 65, 210, 97, 211, 1, 210, 97, 43, 254, 211, 33, 32, 65, 211, 33, 255, 63, 255, 95, 211, 1, 32, 33, 211, 65, 27, 33, 27, 65, 37, 33, 255, 63, 255, 95, 37, 1, 197, 32, 255, 63, 255, 95, 57, 255, 197, 96, 0, 0, 210, 97, 32, 1, 210, 97, 0, 0, 210, 97, 255, 255, 32, 97, 211, 1, 27, 97, 27, 1, 36, 97, 255, 255, 36, 97, 0, 0, 32, 97, 43, 254, 255, 31, 211, 65, 255, 95, 43, 254, 210, 97, 222, 254, 27, 97, 227, 254, 255, 31, 37, 65, 255, 95, 217, 254, 210, 161, 255, 127, 189, 159, 255, 127, 189, 159, 255, 127, 189, 159, 255, 127, 44, 94, 255, 127, 255, 95, 210, 129, 255, 95, 189, 127, 255, 95, 189, 127, 255, 95, 189, 127, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 44, 62, 210, 161, 255, 63, 65, 96, 255, 63, 65, 96, 255, 63, 65, 96, 255, 63, 44, 94, 255, 63, 255, 95, 44, 62, 218, 158, 255, 191, 218, 158, 255, 191, 57, 159, 255, 191, 57, 223, 255, 127, 255, 159, 210, 193, 255, 223, 44, 126, 36, 97, 255, 191, 36, 97, 255, 191, 255, 31, 57, 127, 255, 95, 57, 191, 197, 96, 255, 191, 57, 159, 255, 255, 255, 159, 197, 0, 255, 223, 197, 64, 197, 32, 255, 63, 197, 96, 0, 0, 210, 97, 0, 0, 210, 97, 255, 255, 255, 31, 211, 65, 44, 158, 44, 126, 255, 159, 210, 129, 189, 159, 255, 127, 210, 97, 44, 126, 255, 95, 210, 129, 255, 95, 210, 129, 255, 95, 189, 127, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 44, 62, 65, 96, 255, 63, 65, 96, 255, 63, 65, 96, 255, 63, 218, 158, 255, 191, 218, 158, 255, 191, 255, 223, 44, 126, 36, 97, 255, 191, 36, 97, 255, 191, 211, 33, 255, 127, 44, 158, 0, 0, 43, 222, 255, 63, 255, 159, 43, 254, 255, 95, 211, 1, 210, 97, 255, 255, 255, 31, 211, 65, 210, 161, 44, 126, 44, 158, 44, 126, 189, 159, 255, 127, 210, 97, 210, 129, 210, 97, 44, 126, 255, 95, 189, 127, 44, 158, 211, 65, 44, 158, 44, 62, 255, 159, 65, 64, 255, 159, 65, 64, 210, 97, 211, 65, 44, 94, 211, 65, 65, 96, 255, 63, 44, 158, 43, 190, 210, 97, 210, 193),
"format": 34896613399,
"index_count": 900,
"index_data": PackedByteArray(112, 0, 66, 0, 22, 0, 112, 0, 162, 0, 66, 0, 164, 0, 86, 0, 135, 0, 164, 0, 110, 0, 86, 0, 65, 0, 136, 0, 45, 0, 65, 0, 163, 0, 136, 0, 1, 0, 137, 0, 87, 0, 1, 0, 44, 0, 137, 0, 24, 0, 43, 0, 2, 0, 24, 0, 64, 0, 43, 0, 0, 0, 7, 0, 3, 0, 0, 0, 6, 0, 7, 0, 3, 0, 8, 0, 4, 0, 3, 0, 7, 0, 8, 0, 6, 0, 21, 0, 7, 0, 6, 0, 18, 0, 21, 0, 7, 0, 10, 0, 8, 0, 7, 0, 21, 0, 10, 0, 1, 0, 14, 0, 11, 0, 1, 0, 13, 0, 14, 0, 11, 0, 16, 0, 12, 0, 11, 0, 14, 0, 16, 0, 13, 0, 9, 0, 14, 0, 13, 0, 5, 0, 9, 0, 14, 0, 10, 0, 16, 0, 14, 0, 9, 0, 10, 0, 2, 0, 20, 0, 17, 0, 2, 0, 19, 0, 20, 0, 17, 0, 21, 0, 18, 0, 17, 0, 20, 0, 21, 0, 19, 0, 15, 0, 20, 0, 19, 0, 12, 0, 15, 0, 20, 0, 10, 0, 21, 0, 20, 0, 15, 0, 10, 0, 22, 0, 29, 0, 25, 0, 22, 0, 28, 0, 29, 0, 25, 0, 30, 0, 26, 0, 25, 0, 29, 0, 30, 0, 28, 0, 42, 0, 29, 0, 28, 0, 39, 0, 42, 0, 29, 0, 32, 0, 30, 0, 29, 0, 42, 0, 32, 0, 23, 0, 36, 0, 33, 0, 23, 0, 35, 0, 36, 0, 33, 0, 37, 0, 34, 0, 33, 0, 36, 0, 37, 0, 35, 0, 31, 0, 36, 0, 35, 0, 27, 0, 31, 0, 36, 0, 32, 0, 37, 0, 36, 0, 31, 0, 32, 0, 24, 0, 41, 0, 38, 0, 24, 0, 40, 0, 41, 0, 38, 0, 42, 0, 39, 0, 38, 0, 41, 0, 42, 0, 40, 0, 37, 0, 41, 0, 40, 0, 34, 0, 37, 0, 41, 0, 32, 0, 42, 0, 41, 0, 37, 0, 32, 0, 43, 0, 49, 0, 46, 0, 43, 0, 48, 0, 49, 0, 46, 0, 50, 0, 47, 0, 46, 0, 49, 0, 50, 0, 48, 0, 63, 0, 49, 0, 48, 0, 60, 0, 63, 0, 49, 0, 51, 0, 50, 0, 49, 0, 63, 0, 51, 0, 44, 0, 56, 0, 52, 0, 44, 0, 55, 0, 56, 0, 52, 0, 58, 0, 54, 0, 52, 0, 56, 0, 58, 0, 55, 0, 50, 0, 56, 0, 55, 0, 47, 0, 50, 0, 56, 0, 51, 0, 58, 0, 56, 0, 50, 0, 51, 0, 45, 0, 62, 0, 59, 0, 45, 0, 61, 0, 62, 0, 59, 0, 63, 0, 60, 0, 59, 0, 62, 0, 63, 0, 61, 0, 57, 0, 62, 0, 61, 0, 53, 0, 57, 0, 62, 0, 51, 0, 63, 0, 62, 0, 57, 0, 51, 0, 64, 0, 70, 0, 67, 0, 64, 0, 69, 0, 70, 0, 67, 0, 71, 0, 68, 0, 67, 0, 70, 0, 71, 0, 69, 0, 84, 0, 70, 0, 69, 0, 81, 0, 84, 0, 70, 0, 72, 0, 71, 0, 70, 0, 84, 0, 72, 0, 65, 0, 77, 0, 73, 0, 65, 0, 76, 0, 77, 0, 73, 0, 79, 0, 75, 0, 73, 0, 77, 0, 79, 0, 76, 0, 71, 0, 77, 0, 76, 0, 68, 0, 71, 0, 77, 0, 72, 0, 79, 0, 77, 0, 71, 0, 72, 0, 66, 0, 83, 0, 80, 0, 66, 0, 82, 0, 83, 0, 80, 0, 84, 0, 81, 0, 80, 0, 83, 0, 84, 0, 82, 0, 78, 0, 83, 0, 82, 0, 74, 0, 78, 0, 83, 0, 72, 0, 84, 0, 83, 0, 78, 0, 72, 0, 85, 0, 91, 0, 88, 0, 85, 0, 90, 0, 91, 0, 88, 0, 92, 0, 89, 0, 88, 0, 91, 0, 92, 0, 90, 0, 108, 0, 91, 0, 90, 0, 104, 0, 108, 0, 91, 0, 93, 0, 92, 0, 91, 0, 108, 0, 93, 0, 86, 0, 100, 0, 96, 0, 86, 0, 99, 0, 100, 0, 96, 0, 101, 0, 97, 0, 96, 0, 100, 0, 101, 0, 99, 0, 92, 0, 100, 0, 99, 0, 89, 0, 92, 0, 100, 0, 94, 0, 101, 0, 100, 0, 92, 0, 94, 0, 87, 0, 107, 0, 103, 0, 87, 0, 106, 0, 107, 0, 103, 0, 109, 0, 105, 0, 103, 0, 107, 0, 109, 0, 106, 0, 102, 0, 107, 0, 106, 0, 98, 0, 102, 0, 107, 0, 95, 0, 109, 0, 107, 0, 102, 0, 95, 0, 110, 0, 116, 0, 113, 0, 110, 0, 115, 0, 116, 0, 113, 0, 117, 0, 114, 0, 113, 0, 116, 0, 117, 0, 115, 0, 134, 0, 116, 0, 115, 0, 130, 0, 134, 0, 116, 0, 120, 0, 117, 0, 116, 0, 134, 0, 120, 0, 111, 0, 125, 0, 121, 0, 111, 0, 124, 0, 125, 0, 121, 0, 127, 0, 123, 0, 121, 0, 125, 0, 127, 0, 124, 0, 117, 0, 125, 0, 124, 0, 114, 0, 117, 0, 125, 0, 119, 0, 127, 0, 125, 0, 117, 0, 119, 0, 112, 0, 132, 0, 128, 0, 112, 0, 131, 0, 132, 0, 128, 0, 133, 0, 129, 0, 128, 0, 132, 0, 133, 0, 131, 0, 126, 0, 132, 0, 131, 0, 122, 0, 126, 0, 132, 0, 118, 0, 133, 0, 132, 0, 126, 0, 118, 0, 135, 0, 142, 0, 138, 0, 135, 0, 141, 0, 142, 0, 138, 0, 144, 0, 140, 0, 138, 0, 142, 0, 144, 0, 141, 0, 160, 0, 142, 0, 141, 0, 156, 0, 160, 0, 142, 0, 145, 0, 144, 0, 142, 0, 160, 0, 145, 0, 136, 0, 152, 0, 148, 0, 136, 0, 151, 0, 152, 0, 148, 0, 153, 0, 149, 0, 148, 0, 152, 0, 153, 0, 151, 0, 143, 0, 152, 0, 151, 0, 139, 0, 143, 0, 152, 0, 146, 0, 153, 0, 152, 0, 143, 0, 146, 0, 137, 0, 159, 0, 155, 0, 137, 0, 158, 0, 159, 0, 155, 0, 161, 0, 157, 0, 155, 0, 159, 0, 161, 0, 158, 0, 154, 0, 159, 0, 158, 0, 150, 0, 154, 0, 159, 0, 147, 0, 161, 0, 159, 0, 154, 0, 147, 0, 162, 0, 169, 0, 165, 0, 162, 0, 168, 0, 169, 0, 165, 0, 170, 0, 166, 0, 165, 0, 169, 0, 170, 0, 168, 0, 187, 0, 169, 0, 168, 0, 183, 0, 187, 0, 169, 0, 172, 0, 170, 0, 169, 0, 187, 0, 172, 0, 163, 0, 179, 0, 175, 0, 163, 0, 178, 0, 179, 0, 175, 0, 181, 0, 176, 0, 175, 0, 179, 0, 181, 0, 178, 0, 171, 0, 179, 0, 178, 0, 167, 0, 171, 0, 179, 0, 174, 0, 181, 0, 179, 0, 171, 0, 174, 0, 164, 0, 186, 0, 182, 0, 164, 0, 185, 0, 186, 0, 182, 0, 188, 0, 184, 0, 182, 0, 186, 0, 188, 0, 185, 0, 180, 0, 186, 0, 185, 0, 177, 0, 180, 0, 186, 0, 173, 0, 188, 0, 186, 0, 180, 0, 173, 0, 112, 0, 168, 0, 162, 0, 112, 0, 128, 0, 168, 0, 128, 0, 183, 0, 168, 0, 128, 0, 129, 0, 183, 0, 130, 0, 182, 0, 184, 0, 130, 0, 115, 0, 182, 0, 115, 0, 164, 0, 182, 0, 115, 0, 110, 0, 164, 0, 22, 0, 131, 0, 112, 0, 22, 0, 25, 0, 131, 0, 25, 0, 122, 0, 131, 0, 25, 0, 26, 0, 122, 0, 27, 0, 121, 0, 123, 0, 27, 0, 35, 0, 121, 0, 35, 0, 111, 0, 121, 0, 35, 0, 23, 0, 111, 0, 2, 0, 40, 0, 24, 0, 2, 0, 17, 0, 40, 0, 17, 0, 34, 0, 40, 0, 17, 0, 18, 0, 34, 0, 18, 0, 33, 0, 34, 0, 18, 0, 6, 0, 33, 0, 6, 0, 23, 0, 33, 0, 6, 0, 0, 0, 23, 0, 163, 0, 151, 0, 136, 0, 163, 0, 175, 0, 151, 0, 175, 0, 139, 0, 151, 0, 175, 0, 176, 0, 139, 0, 177, 0, 138, 0, 140, 0, 177, 0, 185, 0, 138, 0, 185, 0, 135, 0, 138, 0, 185, 0, 164, 0, 135, 0, 45, 0, 76, 0, 65, 0, 45, 0, 59, 0, 76, 0, 59, 0, 68, 0, 76, 0, 59, 0, 60, 0, 68, 0, 60, 0, 67, 0, 68, 0, 60, 0, 48, 0, 67, 0, 48, 0, 64, 0, 67, 0, 48, 0, 43, 0, 64, 0, 85, 0, 124, 0, 111, 0, 85, 0, 88, 0, 124, 0, 88, 0, 114, 0, 124, 0, 88, 0, 89, 0, 114, 0, 89, 0, 113, 0, 114, 0, 89, 0, 99, 0, 113, 0, 99, 0, 110, 0, 113, 0, 99, 0, 86, 0, 110, 0, 44, 0, 158, 0, 137, 0, 44, 0, 52, 0, 158, 0, 52, 0, 150, 0, 158, 0, 52, 0, 54, 0, 150, 0, 53, 0, 148, 0, 149, 0, 53, 0, 61, 0, 148, 0, 61, 0, 136, 0, 148, 0, 61, 0, 45, 0, 136, 0, 1, 0, 55, 0, 44, 0, 1, 0, 11, 0, 55, 0, 11, 0, 47, 0, 55, 0, 11, 0, 12, 0, 47, 0, 12, 0, 46, 0, 47, 0, 12, 0, 19, 0, 46, 0, 19, 0, 43, 0, 46, 0, 19, 0, 2, 0, 43, 0, 162, 0, 82, 0, 66, 0, 162, 0, 165, 0, 82, 0, 165, 0, 74, 0, 82, 0, 165, 0, 166, 0, 74, 0, 167, 0, 73, 0, 75, 0, 167, 0, 178, 0, 73, 0, 178, 0, 65, 0, 73, 0, 178, 0, 163, 0, 65, 0, 137, 0, 106, 0, 87, 0, 137, 0, 155, 0, 106, 0, 155, 0, 98, 0, 106, 0, 155, 0, 157, 0, 98, 0, 156, 0, 96, 0, 97, 0, 156, 0, 141, 0, 96, 0, 141, 0, 86, 0, 96, 0, 141, 0, 135, 0, 86, 0, 87, 0, 13, 0, 1, 0, 87, 0, 103, 0, 13, 0, 103, 0, 5, 0, 13, 0, 103, 0, 105, 0, 5, 0, 104, 0, 3, 0, 4, 0, 104, 0, 90, 0, 3, 0, 90, 0, 0, 0, 3, 0, 90, 0, 85, 0, 0, 0, 66, 0, 28, 0, 22, 0, 66, 0, 80, 0, 28, 0, 80, 0, 39, 0, 28, 0, 80, 0, 81, 0, 39, 0, 81, 0, 38, 0, 39, 0, 81, 0, 69, 0, 38, 0, 69, 0, 24, 0, 38, 0, 69, 0, 64, 0, 24, 0, 111, 0, 0, 0, 85, 0, 111, 0, 23, 0, 0, 0),
"lods": [0.0179063, PackedByteArray(112, 0, 8, 1, 22, 0, 22, 0, 26, 0, 112, 0, 26, 0, 32, 0, 30, 0, 22, 0, 2, 1, 26, 0, 26, 0, 122, 0, 112, 0, 22, 0, 1, 1, 2, 1, 8, 1, 1, 1, 22, 0, 112, 0, 122, 0, 126, 0, 112, 0, 126, 0, 133, 0, 133, 0, 126, 0, 118, 0, 112, 0, 133, 0, 129, 0, 112, 0, 129, 0, 162, 0, 112, 0, 162, 0, 8, 1, 129, 0, 183, 0, 162, 0, 162, 0, 183, 0, 187, 0, 162, 0, 187, 0, 166, 0, 162, 0, 166, 0, 8, 1, 166, 0, 187, 0, 170, 0, 187, 0, 172, 0, 170, 0, 166, 0, 74, 0, 8, 1, 8, 1, 74, 0, 78, 0, 78, 0, 9, 1, 8, 1, 7, 1, 8, 1, 9, 1, 8, 1, 7, 1, 1, 1, 65, 0, 7, 1, 9, 1, 65, 0, 9, 1, 75, 0, 75, 0, 72, 0, 79, 0, 163, 0, 65, 0, 75, 0, 167, 0, 163, 0, 75, 0, 163, 0, 167, 0, 171, 0, 163, 0, 171, 0, 181, 0, 181, 0, 171, 0, 174, 0, 163, 0, 181, 0, 176, 0, 163, 0, 176, 0, 136, 0, 65, 0, 163, 0, 136, 0, 176, 0, 139, 0, 136, 0, 136, 0, 139, 0, 143, 0, 136, 0, 143, 0, 153, 0, 153, 0, 143, 0, 146, 0, 136, 0, 153, 0, 149, 0, 65, 0, 136, 0, 4, 1, 4, 1, 136, 0, 149, 0, 53, 0, 4, 1, 149, 0, 4, 1, 53, 0, 57, 0, 4, 1, 7, 1, 65, 0, 57, 0, 5, 1, 4, 1, 4, 1, 3, 1, 7, 1, 3, 1, 4, 1, 5, 1, 1, 1, 7, 1, 3, 1, 44, 0, 3, 1, 5, 1, 1, 1, 3, 1, 254, 0, 254, 0, 0, 1, 1, 1, 253, 0, 3, 1, 44, 0, 253, 0, 254, 0, 3, 1, 44, 0, 6, 1, 54, 0, 54, 0, 51, 0, 58, 0, 44, 0, 54, 0, 137, 0, 253, 0, 44, 0, 137, 0, 54, 0, 150, 0, 137, 0, 137, 0, 150, 0, 154, 0, 137, 0, 154, 0, 161, 0, 161, 0, 154, 0, 147, 0, 137, 0, 161, 0, 157, 0, 137, 0, 157, 0, 87, 0, 253, 0, 137, 0, 87, 0, 157, 0, 98, 0, 87, 0, 87, 0, 98, 0, 102, 0, 87, 0, 102, 0, 109, 0, 109, 0, 102, 0, 95, 0, 87, 0, 109, 0, 105, 0, 87, 0, 105, 0, 253, 0, 105, 0, 5, 0, 253, 0, 253, 0, 5, 0, 9, 0, 253, 0, 9, 0, 255, 0, 254, 0, 253, 0, 255, 0, 0, 0, 254, 0, 255, 0, 0, 0, 255, 0, 4, 0, 254, 0, 0, 0, 23, 0, 4, 0, 10, 0, 8, 0, 10, 1, 0, 0, 4, 0, 104, 0, 10, 1, 4, 0, 10, 1, 104, 0, 108, 0, 111, 0, 0, 0, 10, 1, 111, 0, 23, 0, 0, 0, 10, 1, 108, 0, 92, 0, 108, 0, 93, 0, 92, 0, 86, 0, 10, 1, 92, 0, 101, 0, 92, 0, 94, 0, 86, 0, 92, 0, 101, 0, 86, 0, 101, 0, 97, 0, 10, 1, 86, 0, 11, 1, 10, 1, 11, 1, 111, 0, 135, 0, 86, 0, 97, 0, 156, 0, 135, 0, 97, 0, 135, 0, 156, 0, 160, 0, 160, 0, 145, 0, 144, 0, 140, 0, 160, 0, 144, 0, 135, 0, 160, 0, 140, 0, 164, 0, 86, 0, 135, 0, 164, 0, 135, 0, 140, 0, 164, 0, 11, 1, 86, 0, 177, 0, 164, 0, 140, 0, 164, 0, 177, 0, 180, 0, 164, 0, 180, 0, 188, 0, 188, 0, 180, 0, 173, 0, 164, 0, 188, 0, 184, 0, 11, 1, 164, 0, 184, 0, 130, 0, 11, 1, 184, 0, 11, 1, 130, 0, 134, 0, 11, 1, 134, 0, 117, 0, 134, 0, 120, 0, 117, 0, 111, 0, 11, 1, 117, 0, 111, 0, 117, 0, 127, 0, 127, 0, 117, 0, 119, 0, 111, 0, 127, 0, 123, 0, 23, 0, 111, 0, 123, 0, 27, 0, 23, 0, 123, 0, 23, 0, 27, 0, 31, 0, 23, 0, 31, 0, 2, 1, 1, 1, 23, 0, 2, 1), 0.0210464, PackedByteArray(246, 0, 66, 0, 233, 0, 129, 0, 246, 0, 118, 0, 246, 0, 129, 0, 252, 0, 246, 0, 252, 0, 66, 0, 252, 0, 172, 0, 166, 0, 252, 0, 166, 0, 66, 0, 166, 0, 74, 0, 66, 0, 74, 0, 239, 0, 66, 0, 66, 0, 230, 0, 233, 0, 66, 0, 240, 0, 230, 0, 230, 0, 238, 0, 234, 0, 230, 0, 234, 0, 227, 0, 227, 0, 232, 0, 230, 0, 237, 0, 236, 0, 239, 0, 237, 0, 239, 0, 65, 0, 65, 0, 239, 0, 75, 0, 250, 0, 65, 0, 75, 0, 65, 0, 247, 0, 237, 0, 65, 0, 250, 0, 247, 0, 237, 0, 247, 0, 149, 0, 149, 0, 247, 0, 146, 0, 250, 0, 176, 0, 247, 0, 176, 0, 250, 0, 174, 0, 227, 0, 228, 0, 231, 0, 111, 0, 231, 0, 228, 0, 111, 0, 228, 0, 242, 0, 231, 0, 111, 0, 123, 0, 111, 0, 245, 0, 123, 0, 241, 0, 245, 0, 111, 0, 123, 0, 244, 0, 119, 0, 104, 0, 93, 0, 241, 0, 104, 0, 241, 0, 4, 0, 4, 0, 2, 0, 10, 0, 97, 0, 241, 0, 94, 0, 86, 0, 241, 0, 97, 0, 241, 0, 86, 0, 244, 0, 249, 0, 86, 0, 97, 0, 130, 0, 120, 0, 244, 0, 130, 0, 244, 0, 184, 0, 251, 0, 244, 0, 86, 0, 244, 0, 251, 0, 184, 0, 251, 0, 86, 0, 249, 0, 184, 0, 251, 0, 173, 0, 251, 0, 249, 0, 140, 0, 249, 0, 145, 0, 140, 0, 1, 0, 248, 0, 243, 0, 248, 0, 157, 0, 243, 0, 157, 0, 248, 0, 147, 0, 243, 0, 105, 0, 1, 0, 105, 0, 243, 0, 95, 0, 105, 0, 5, 0, 1, 0, 1, 0, 5, 0, 229, 0, 227, 0, 1, 0, 229, 0, 1, 0, 227, 0, 235, 0, 1, 0, 44, 0, 248, 0, 1, 0, 235, 0, 44, 0, 44, 0, 54, 0, 248, 0, 44, 0, 235, 0, 54, 0, 24, 0, 27, 0, 32, 0, 32, 0, 26, 0, 122, 0), 0.215511, PackedByteArray(193, 0, 206, 0, 195, 0, 206, 0, 205, 0, 195, 0, 193, 0, 226, 0, 206, 0, 226, 0, 222, 0, 206, 0, 226, 0, 193, 0, 216, 0, 225, 0, 214, 0, 212, 0, 209, 0, 212, 0, 214, 0, 212, 0, 219, 0, 225, 0, 214, 0, 225, 0, 184, 0, 184, 0, 225, 0, 173, 0, 184, 0, 218, 0, 214, 0, 212, 0, 208, 0, 94, 0, 4, 0, 210, 0, 208, 0, 92, 0, 10, 0, 4, 0, 208, 0, 215, 0, 194, 0, 194, 0, 215, 0, 217, 0, 194, 0, 191, 0, 208, 0, 191, 0, 194, 0, 197, 0, 207, 0, 224, 0, 201, 0, 224, 0, 207, 0, 223, 0, 201, 0, 224, 0, 220, 0, 201, 0, 204, 0, 207, 0, 201, 0, 199, 0, 204, 0, 189, 0, 202, 0, 213, 0, 213, 0, 202, 0, 221, 0, 189, 0, 213, 0, 211, 0, 189, 0, 200, 0, 202, 0, 189, 0, 192, 0, 200, 0, 196, 0, 198, 0, 190, 0, 196, 0, 203, 0, 198, 0)],
"material": SubResource("StandardMaterial3D_hl3uc"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 268,
"vertex_data": PackedByteArray(177, 248, 177, 248, 0, 0, 251, 255, 177, 248, 255, 255, 77, 7, 60, 253, 255, 255, 177, 248, 77, 7, 253, 217, 177, 248, 125, 251, 142, 0, 149, 247, 177, 248, 219, 253, 35, 2, 116, 236, 177, 248, 219, 253, 35, 2, 136, 252, 125, 251, 177, 248, 142, 0, 190, 247, 143, 251, 143, 251, 58, 1, 165, 255, 105, 251, 124, 253, 130, 2, 141, 244, 105, 251, 124, 253, 130, 2, 227, 243, 232, 252, 232, 252, 22, 3, 26, 251, 125, 251, 112, 255, 77, 7, 83, 245, 219, 253, 219, 253, 77, 7, 190, 234, 177, 248, 112, 255, 129, 4, 49, 253, 143, 251, 196, 254, 111, 4, 176, 245, 124, 253, 124, 253, 149, 4, 182, 239, 124, 253, 124, 253, 149, 4, 8, 236, 112, 255, 177, 248, 129, 4, 53, 225, 219, 253, 177, 248, 35, 2, 57, 236, 112, 255, 125, 251, 77, 7, 207, 224, 196, 254, 143, 251, 111, 4, 78, 231, 124, 253, 105, 251, 130, 2, 65, 243, 177, 248, 0, 0, 77, 7, 48, 196, 177, 248, 77, 7, 0, 0, 49, 250, 255, 255, 77, 7, 77, 7, 142, 213, 177, 248, 142, 0, 129, 4, 242, 207, 177, 248, 35, 2, 35, 2, 8, 224, 177, 248, 35, 2, 35, 2, 243, 230, 125, 251, 142, 0, 77, 7, 102, 197, 143, 251, 58, 1, 111, 4, 132, 208, 105, 251, 130, 2, 130, 2, 80, 223, 105, 251, 130, 2, 130, 2, 228, 223, 232, 252, 22, 3, 22, 3, 248, 218, 125, 251, 77, 7, 142, 0, 223, 241, 219, 253, 77, 7, 35, 2, 34, 231, 177, 248, 129, 4, 142, 0, 248, 241, 143, 251, 111, 4, 58, 1, 189, 233, 124, 253, 149, 4, 130, 2, 122, 225, 112, 255, 129, 4, 77, 7, 191, 207, 219, 253, 35, 2, 77, 7, 61, 201, 112, 255, 77, 7, 129, 4, 230, 220, 196, 254, 111, 4, 111, 4, 254, 215, 124, 253, 130, 2, 149, 4, 52, 210, 255, 255, 177, 248, 177, 248, 148, 213, 177, 248, 255, 255, 177, 248, 241, 252, 177, 248, 177, 248, 255, 255, 255, 191, 112, 255, 125, 251, 177, 248, 92, 221, 219, 253, 219, 253, 177, 248, 162, 232, 112, 255, 177, 248, 125, 251, 131, 207, 196, 254, 143, 251, 143, 251, 44, 217, 124, 253, 124, 253, 105, 251, 22, 230, 232, 252, 232, 252, 232, 252, 79, 221, 177, 248, 112, 255, 125, 251, 252, 252, 177, 248, 219, 253, 219, 253, 89, 197, 177, 248, 219, 253, 219, 253, 143, 254, 125, 251, 112, 255, 177, 248, 61, 244, 143, 251, 196, 254, 143, 251, 184, 242, 105, 251, 124, 253, 124, 253, 215, 196, 105, 251, 124, 253, 124, 253, 146, 243, 125, 251, 177, 248, 112, 255, 123, 193, 219, 253, 177, 248, 219, 253, 99, 199, 177, 248, 125, 251, 112, 255, 225, 192, 143, 251, 143, 251, 196, 254, 223, 194, 124, 253, 105, 251, 124, 253, 185, 208, 255, 255, 77, 7, 177, 248, 130, 208, 177, 248, 77, 7, 255, 255, 104, 192, 177, 248, 0, 0, 177, 248, 3, 188, 112, 255, 77, 7, 125, 251, 65, 202, 219, 253, 77, 7, 219, 253, 182, 196, 112, 255, 129, 4, 177, 248, 243, 201, 196, 254, 111, 4, 143, 251, 21, 195, 124, 253, 149, 4, 124, 253, 249, 193, 232, 252, 22, 3, 232, 252, 244, 183, 177, 248, 129, 4, 112, 255, 95, 194, 177, 248, 35, 2, 219, 253, 52, 160, 177, 248, 35, 2, 219, 253, 190, 198, 125, 251, 77, 7, 112, 255, 196, 193, 143, 251, 111, 4, 196, 254, 45, 196, 105, 251, 130, 2, 124, 253, 23, 163, 105, 251, 130, 2, 124, 253, 229, 200, 125, 251, 142, 0, 177, 248, 174, 189, 219, 253, 35, 2, 177, 248, 196, 194, 177, 248, 142, 0, 125, 251, 69, 176, 143, 251, 58, 1, 143, 251, 146, 178, 124, 253, 130, 2, 105, 251, 77, 186, 77, 7, 177, 248, 0, 0, 49, 250, 0, 0, 177, 248, 77, 7, 142, 213, 77, 7, 255, 255, 77, 7, 60, 253, 129, 4, 177, 248, 142, 0, 223, 241, 35, 2, 177, 248, 35, 2, 34, 231, 77, 7, 125, 251, 142, 0, 248, 241, 111, 4, 143, 251, 58, 1, 190, 233, 130, 2, 105, 251, 130, 2, 204, 225, 22, 3, 232, 252, 22, 3, 25, 217, 22, 3, 232, 252, 22, 3, 42, 221, 22, 3, 232, 252, 22, 3, 233, 235, 142, 0, 125, 251, 77, 7, 191, 207, 35, 2, 219, 253, 77, 7, 255, 200, 35, 2, 219, 253, 77, 7, 67, 234, 142, 0, 177, 248, 129, 4, 230, 220, 58, 1, 143, 251, 111, 4, 222, 216, 130, 2, 124, 253, 149, 4, 149, 209, 130, 2, 124, 253, 149, 4, 8, 236, 77, 7, 112, 255, 129, 4, 49, 253, 77, 7, 219, 253, 35, 2, 243, 230, 77, 7, 219, 253, 35, 2, 136, 252, 129, 4, 112, 255, 77, 7, 83, 245, 111, 4, 196, 254, 111, 4, 176, 245, 149, 4, 124, 253, 130, 2, 231, 223, 149, 4, 124, 253, 130, 2, 227, 243, 0, 0, 77, 7, 77, 7, 253, 217, 77, 7, 77, 7, 0, 0, 251, 255, 77, 7, 0, 0, 77, 7, 48, 196, 142, 0, 77, 7, 129, 4, 53, 225, 35, 2, 77, 7, 35, 2, 57, 236, 142, 0, 129, 4, 77, 7, 207, 224, 58, 1, 111, 4, 111, 4, 78, 231, 130, 2, 149, 4, 130, 2, 23, 246, 22, 3, 22, 3, 22, 3, 114, 219, 22, 3, 22, 3, 22, 3, 23, 249, 22, 3, 22, 3, 22, 3, 150, 247, 77, 7, 129, 4, 142, 0, 149, 247, 77, 7, 35, 2, 35, 2, 8, 224, 77, 7, 35, 2, 35, 2, 116, 236, 129, 4, 77, 7, 142, 0, 190, 247, 111, 4, 111, 4, 58, 1, 151, 254, 149, 4, 130, 2, 130, 2, 81, 222, 149, 4, 130, 2, 130, 2, 51, 244, 129, 4, 142, 0, 77, 7, 102, 197, 35, 2, 35, 2, 77, 7, 124, 201, 35, 2, 35, 2, 77, 7, 57, 235, 77, 7, 142, 0, 129, 4, 242, 207, 111, 4, 58, 1, 111, 4, 50, 208, 130, 2, 130, 2, 149, 4, 222, 210, 130, 2, 130, 2, 149, 4, 182, 239, 0, 0, 177, 248, 177, 248, 130, 208, 77, 7, 177, 248, 255, 255, 104, 192, 77, 7, 255, 255, 177, 248, 241, 252, 142, 0, 177, 248, 125, 251, 65, 202, 35, 2, 177, 248, 219, 253, 182, 196, 35, 2, 177, 248, 219, 253, 182, 196, 142, 0, 125, 251, 177, 248, 243, 201, 58, 1, 143, 251, 143, 251, 21, 195, 130, 2, 105, 251, 124, 253, 155, 208, 130, 2, 105, 251, 124, 253, 104, 192, 22, 3, 232, 252, 232, 252, 143, 186, 22, 3, 232, 252, 232, 252, 44, 213, 22, 3, 232, 252, 232, 252, 229, 223, 77, 7, 125, 251, 112, 255, 95, 194, 77, 7, 219, 253, 219, 253, 190, 198, 77, 7, 219, 253, 219, 253, 143, 254, 129, 4, 177, 248, 112, 255, 197, 193, 111, 4, 143, 251, 196, 254, 211, 199, 149, 4, 124, 253, 124, 253, 170, 201, 149, 4, 124, 253, 124, 253, 14, 235, 129, 4, 112, 255, 177, 248, 61, 244, 35, 2, 219, 253, 177, 248, 15, 195, 35, 2, 219, 253, 177, 248, 42, 233, 77, 7, 112, 255, 125, 251, 252, 252, 111, 4, 196, 254, 143, 251, 176, 240, 130, 2, 124, 253, 105, 251, 247, 187, 130, 2, 124, 253, 105, 251, 12, 232, 77, 7, 0, 0, 177, 248, 3, 188, 77, 7, 77, 7, 255, 255, 255, 191, 0, 0, 77, 7, 177, 248, 148, 213, 77, 7, 142, 0, 125, 251, 69, 176, 77, 7, 35, 2, 219, 253, 52, 160, 77, 7, 35, 2, 219, 253, 89, 197, 129, 4, 142, 0, 177, 248, 174, 189, 111, 4, 58, 1, 143, 251, 146, 178, 149, 4, 130, 2, 124, 253, 23, 163, 149, 4, 130, 2, 124, 253, 197, 196, 22, 3, 22, 3, 232, 252, 191, 170, 22, 3, 22, 3, 232, 252, 154, 231, 22, 3, 22, 3, 232, 252, 11, 203, 129, 4, 77, 7, 112, 255, 123, 193, 35, 2, 77, 7, 219, 253, 99, 199, 35, 2, 77, 7, 219, 253, 99, 199, 77, 7, 129, 4, 112, 255, 225, 192, 111, 4, 111, 4, 196, 254, 193, 193, 130, 2, 149, 4, 124, 253, 218, 218, 130, 2, 149, 4, 124, 253, 36, 202, 142, 0, 129, 4, 177, 248, 92, 221, 35, 2, 35, 2, 177, 248, 123, 194, 35, 2, 35, 2, 177, 248, 27, 232, 142, 0, 77, 7, 125, 251, 131, 207, 58, 1, 111, 4, 143, 251, 24, 219, 130, 2, 130, 2, 105, 251, 195, 184, 130, 2, 130, 2, 105, 251, 34, 228, 177, 248, 219, 253, 35, 2, 28, 251, 232, 252, 232, 252, 22, 3, 223, 241, 232, 252, 232, 252, 22, 3, 255, 255, 232, 252, 232, 252, 22, 3, 31, 232, 177, 248, 35, 2, 35, 2, 151, 193, 177, 248, 35, 2, 35, 2, 49, 242, 232, 252, 22, 3, 22, 3, 50, 199, 232, 252, 22, 3, 22, 3, 216, 196, 232, 252, 22, 3, 22, 3, 160, 235, 232, 252, 232, 252, 232, 252, 137, 224, 232, 252, 232, 252, 232, 252, 218, 219, 232, 252, 232, 252, 232, 252, 19, 228, 177, 248, 219, 253, 219, 253, 157, 189, 177, 248, 219, 253, 219, 253, 255, 255, 232, 252, 22, 3, 232, 252, 199, 203, 232, 252, 22, 3, 232, 252, 253, 170, 232, 252, 22, 3, 232, 252, 62, 200, 177, 248, 35, 2, 219, 253, 119, 185, 177, 248, 35, 2, 219, 253, 211, 193, 130, 2, 105, 251, 130, 2, 131, 241, 130, 2, 105, 251, 130, 2, 182, 219, 22, 3, 232, 252, 22, 3, 91, 233, 22, 3, 232, 252, 22, 3, 233, 237, 35, 2, 219, 253, 77, 7, 144, 205, 35, 2, 219, 253, 77, 7, 227, 233, 130, 2, 149, 4, 130, 2, 157, 229, 130, 2, 149, 4, 130, 2, 167, 248, 22, 3, 22, 3, 22, 3, 164, 200, 22, 3, 22, 3, 22, 3, 232, 236, 22, 3, 22, 3, 22, 3, 12, 236, 22, 3, 232, 252, 232, 252, 156, 206, 22, 3, 232, 252, 232, 252, 89, 197, 22, 3, 232, 252, 232, 252, 108, 231, 22, 3, 22, 3, 232, 252, 214, 176, 22, 3, 22, 3, 232, 252, 193, 204, 35, 2, 77, 7, 219, 253, 223, 194, 35, 2, 77, 7, 219, 253, 193, 209, 35, 2, 35, 2, 177, 248, 168, 191, 255, 255, 177, 248, 77, 7, 221, 222, 177, 248, 219, 253, 35, 2, 255, 255, 232, 252, 232, 252, 22, 3, 13, 240, 255, 255, 77, 7, 77, 7, 16, 213, 177, 248, 35, 2, 35, 2, 150, 237, 177, 248, 35, 2, 35, 2, 127, 211, 232, 252, 22, 3, 22, 3, 6, 200, 232, 252, 232, 252, 232, 252, 113, 224, 232, 252, 232, 252, 232, 252, 87, 226, 232, 252, 232, 252, 232, 252, 219, 219, 177, 248, 219, 253, 219, 253, 2, 190, 232, 252, 22, 3, 232, 252, 161, 203, 232, 252, 22, 3, 232, 252, 69, 174, 232, 252, 22, 3, 232, 252, 149, 200, 130, 2, 105, 251, 130, 2, 141, 226, 130, 2, 105, 251, 130, 2, 169, 252, 35, 2, 219, 253, 77, 7, 168, 234, 130, 2, 149, 4, 130, 2, 175, 233, 130, 2, 149, 4, 130, 2, 255, 255, 77, 7, 35, 2, 35, 2, 183, 198, 35, 2, 177, 248, 219, 253, 150, 190, 77, 7, 219, 253, 219, 253, 255, 255, 35, 2, 219, 253, 177, 248, 67, 190, 77, 7, 35, 2, 219, 253, 148, 190, 35, 2, 77, 7, 219, 253, 58, 208, 35, 2, 35, 2, 177, 248, 19, 192, 177, 248, 255, 255, 77, 7, 109, 252, 255, 255, 177, 248, 77, 7, 145, 223, 232, 252, 232, 252, 22, 3, 4, 250, 177, 248, 77, 7, 0, 0, 144, 225, 255, 255, 77, 7, 77, 7, 152, 214, 232, 252, 22, 3, 22, 3, 15, 218, 255, 255, 177, 248, 177, 248, 35, 211, 177, 248, 177, 248, 255, 255, 205, 192, 232, 252, 232, 252, 232, 252, 210, 220, 232, 252, 232, 252, 232, 252, 226, 224, 255, 255, 77, 7, 177, 248, 238, 202, 177, 248, 0, 0, 177, 248, 76, 184, 232, 252, 22, 3, 232, 252, 148, 182, 77, 7, 177, 248, 0, 0, 195, 242, 0, 0, 77, 7, 77, 7, 180, 222, 20, 189, 196, 188, 122, 65, 79, 3, 149, 45, 50, 41, 127, 185, 105, 184, 120, 180, 112, 179, 77, 89, 180, 1, 207, 56, 5, 57, 69, 182, 143, 180, 206, 178, 217, 175, 140, 84, 6, 8, 4, 53, 174, 40, 214, 60, 8, 12, 6, 55, 202, 22, 164, 74, 82, 3, 251, 68, 138, 12, 86, 58, 9, 27, 121, 62, 231, 22, 77, 49, 25, 45, 4, 53, 8, 51, 175, 49, 70, 33, 121, 53, 5, 37, 4, 53, 244, 45, 231, 5, 192, 116, 43, 64, 216, 63, 131, 42, 11, 47, 63, 5, 221, 117, 74, 2, 43, 120, 99, 74, 80, 73, 97, 18, 237, 92, 14, 19, 66, 95, 149, 16, 76, 98, 235, 71, 146, 68, 145, 39, 86, 75, 193, 59, 137, 59, 4, 53, 20, 55, 255, 68, 220, 67, 22, 64, 240, 63, 16, 48, 255, 63, 168, 37, 58, 56, 150, 29, 109, 71, 133, 46, 219, 50, 100, 40, 151, 60, 16, 31, 255, 74, 135, 42, 111, 38, 90, 59, 0, 3, 61, 244, 7, 0, 43, 46, 229, 30, 250, 50, 29, 21, 50, 38, 191, 33, 40, 41, 199, 26, 55, 45, 186, 18, 65, 41, 215, 12, 31, 50, 25, 3, 89, 223, 160, 25, 78, 36, 9, 4, 155, 55, 5, 11, 171, 47, 22, 10, 68, 213, 138, 15, 109, 35, 229, 10, 222, 22, 104, 13, 227, 31, 129, 25, 44, 233, 187, 13, 168, 219, 11, 0, 167, 36, 53, 18, 216, 39, 22, 44, 7, 0, 75, 244, 217, 5, 173, 115, 14, 35, 173, 39, 11, 26, 143, 32, 145, 35, 39, 53, 168, 30, 251, 48, 158, 17, 186, 42, 187, 15, 136, 56, 140, 13, 123, 233, 76, 12, 155, 110, 89, 25, 179, 223, 95, 13, 207, 22, 146, 0, 63, 220, 207, 30, 59, 79, 20, 15, 168, 214, 232, 17, 93, 90, 96, 28, 129, 68, 3, 7, 114, 113, 163, 19, 85, 85, 127, 26, 248, 63, 211, 191, 38, 192, 123, 213, 243, 208, 175, 124, 122, 193, 61, 196, 117, 196, 250, 202, 234, 200, 255, 186, 34, 188, 198, 190, 208, 192, 80, 209, 53, 191, 23, 195, 143, 190, 164, 227, 116, 175, 124, 110, 47, 204, 86, 218, 196, 199, 180, 225, 183, 184, 159, 105, 113, 183, 121, 209, 35, 205, 163, 217, 124, 194, 151, 222, 199, 181, 24, 105, 120, 190, 172, 124, 164, 202, 155, 181, 174, 182, 75, 126, 77, 217, 247, 115, 213, 188, 116, 115, 250, 196, 0, 184, 118, 187, 248, 119, 140, 212, 105, 210, 204, 214, 235, 66, 58, 67, 62, 11, 231, 133, 177, 206, 229, 210, 250, 202, 246, 204, 79, 206, 184, 222, 133, 202, 249, 218, 194, 207, 240, 205, 88, 51, 188, 164, 95, 72, 235, 89, 96, 198, 234, 219, 126, 70, 149, 71, 211, 7, 74, 130, 134, 75, 142, 76, 47, 199, 249, 198, 41, 71, 0, 78, 229, 34, 252, 154, 153, 76, 167, 80, 17, 35, 96, 146, 106, 56, 226, 156, 98, 201, 201, 232, 34, 10, 62, 133, 147, 33, 226, 149, 56, 52, 188, 156, 168, 197, 245, 228, 39, 216, 232, 211, 247, 255, 179, 11, 255, 124, 89, 187, 240, 220, 81, 216, 243, 229, 111, 223, 243, 229, 111, 223, 109, 220, 215, 202, 86, 225, 3, 207, 54, 248, 70, 213, 49, 237, 67, 213, 26, 243, 231, 199, 118, 247, 144, 45, 233, 113, 2, 168, 114, 242, 131, 22, 165, 230, 74, 32, 246, 123, 78, 164, 159, 242, 47, 233, 207, 253, 34, 34, 149, 240, 214, 40, 243, 120, 255, 165, 249, 116, 155, 183, 80, 228, 102, 187, 127, 106, 151, 178, 230, 124, 30, 178, 63, 119, 145, 176, 139, 232, 7, 192, 38, 108, 24, 172, 82, 12, 217, 133, 193, 11, 247, 255, 119, 213, 143, 217, 141, 14, 3, 135, 100, 17, 75, 140, 165, 32, 94, 230, 162, 37, 232, 145, 169, 42, 162, 147, 196, 48, 207, 158, 186, 42, 104, 240, 24, 68, 130, 165, 108, 219, 120, 238, 197, 203, 31, 254, 32, 233, 150, 242, 27, 224, 125, 230, 27, 224, 125, 230, 210, 22, 67, 242, 146, 36, 105, 255, 124, 224, 88, 234, 117, 215, 97, 240, 211, 209, 25, 225, 148, 59, 18, 157, 161, 204, 68, 235, 204, 217, 63, 222, 42, 216, 83, 228, 6, 64, 165, 157, 161, 209, 105, 238, 87, 81, 119, 3, 42, 34, 50, 50, 27, 61, 62, 164, 230, 233, 5, 3, 37, 1, 57, 119, 45, 101, 9, 53, 101, 22, 117, 80, 178, 50, 143, 44, 133, 69, 198, 54, 170, 50, 225, 42, 168, 32, 95, 20, 133, 45, 229, 5, 197, 41, 77, 7, 124, 113, 123, 9, 61, 38, 166, 49, 92, 13, 57, 23, 65, 9, 99, 76, 49, 13, 32, 114, 231, 28, 167, 240, 49, 214, 206, 173, 234, 201, 120, 205, 139, 195, 97, 175, 253, 127, 67, 180, 0, 209, 102, 200, 88, 129, 87, 137, 148, 216, 186, 212, 94, 77, 63, 64, 43, 34, 234, 134, 55, 78, 254, 74, 177, 210, 224, 225, 1, 220, 80, 206, 31, 219, 216, 4, 149, 129, 155, 145, 91, 42, 226, 143, 16, 28, 210, 239, 56, 5, 196, 248, 36, 212, 151, 221, 190, 39, 111, 130, 180, 51, 27, 37, 214, 103, 131, 196, 29, 211, 152, 8, 164, 38, 85, 52, 140, 96, 160, 53, 241, 79, 254, 34, 211, 21, 165, 81, 101, 50, 200, 42, 103, 44, 66, 6, 142, 32, 87, 20, 219, 44, 55, 0, 68, 38, 112, 49, 167, 10, 96, 43, 91, 15, 3, 72, 147, 210, 29, 189, 221, 174, 55, 206, 73, 123, 106, 167, 189, 211, 204, 214, 130, 230, 202, 161, 136, 14, 168, 135, 125, 247, 26, 235, 126, 118, 111, 226, 99, 218, 152, 202, 252, 43, 160, 250, 129, 213, 96, 222, 57, 46, 240, 139, 121, 89, 35, 13, 24, 51, 1, 40, 55, 53, 204, 38, 107, 53, 70, 49, 232, 38, 206, 53, 122, 37, 17, 77, 29, 42, 229, 30, 146, 20, 30, 3, 128, 41, 98, 16, 174, 227, 62, 4, 4, 34, 59, 44, 242, 14, 102, 106, 76, 14, 2, 56, 119, 202, 79, 187, 220, 205, 144, 215)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_0dym6")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_rvvo1"]
blend_mode = 1
shading_mode = 0
albedo_color = Color(0.494118, 0.470588, 0.298039, 0.164706)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1toma"]
shading_mode = 0
vertex_color_use_as_albedo = true
albedo_color = Color(0.0627451, 0.027451, 0.027451, 1)

[sub_resource type="CylinderMesh" id="CylinderMesh_oe2rb"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_4js5s"]
render_priority = 0
shader = ExtResource("7_isibn")

[sub_resource type="QuadMesh" id="QuadMesh_r6kdk"]
size = Vector2(3.12, 3.145)

[sub_resource type="Gradient" id="Gradient_e8r5r"]
colors = PackedColorArray(0.694118, 0.694118, 0.694118, 1, 0.694118, 0.694118, 0.694118, 1)

[sub_resource type="QuadMesh" id="QuadMesh_1asx3"]
size = Vector2(3.12, 3.145)

[sub_resource type="Gradient" id="Gradient_eenqv"]
colors = PackedColorArray(0.694118, 0.694118, 0.694118, 1, 0.694118, 0.694118, 0.694118, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_khy2i"]
resource_local_to_scene = true
transparency = 1
shading_mode = 0
albedo_color = Color(1, 1, 1, 0.00392157)

[sub_resource type="ArrayMesh" id="ArrayMesh_hflnm"]
_surfaces = [{
"aabb": AABB(-1.05, -1.05, -1.04798, 2.09999, 2.09999, 2.09797),
"attribute_data": PackedByteArray(158, 44, 30, 63, 130, 210, 1, 63, 162, 210, 33, 63, 252, 88, 252, 62, 158, 44, 30, 63, 252, 88, 252, 62, 159, 222, 30, 63, 130, 210, 1, 63, 160, 255, 31, 63, 130, 210, 1, 63, 162, 210, 33, 63, 0, 255, 255, 62, 158, 44, 30, 63, 129, 31, 1, 63, 159, 227, 30, 63, 129, 27, 1, 63, 160, 255, 31, 63, 129, 36, 1, 63, 161, 36, 33, 63, 0, 255, 255, 62, 160, 189, 31, 63, 0, 255, 255, 62, 161, 31, 33, 63, 252, 88, 252, 62, 160, 255, 31, 63, 252, 88, 252, 62, 162, 210, 33, 63, 254, 190, 253, 62, 161, 27, 33, 63, 254, 198, 253, 62, 160, 255, 31, 63, 254, 180, 253, 62, 160, 255, 31, 63, 254, 180, 253, 62, 158, 44, 30, 63, 254, 190, 253, 62, 158, 44, 30, 63, 0, 255, 255, 62, 159, 222, 30, 63, 252, 88, 252, 62, 159, 227, 30, 63, 254, 198, 253, 62, 159, 218, 30, 63, 0, 255, 255, 62, 188, 88, 188, 62, 252, 88, 252, 62, 196, 164, 195, 62, 130, 210, 1, 63, 196, 164, 195, 62, 252, 88, 252, 62, 188, 88, 188, 62, 254, 190, 253, 62, 188, 88, 188, 62, 0, 255, 255, 62, 192, 254, 191, 62, 130, 210, 1, 63, 190, 190, 189, 62, 252, 88, 252, 62, 190, 198, 189, 62, 254, 198, 253, 62, 190, 180, 189, 62, 0, 255, 255, 62, 192, 254, 191, 62, 129, 36, 1, 63, 192, 254, 191, 62, 255, 122, 255, 62, 196, 164, 195, 62, 129, 31, 1, 63, 196, 164, 195, 62, 0, 255, 255, 62, 194, 64, 194, 62, 130, 210, 1, 63, 194, 54, 194, 62, 129, 27, 1, 63, 194, 72, 194, 62, 0, 255, 255, 62, 194, 64, 194, 62, 252, 88, 252, 62, 192, 254, 191, 62, 252, 88, 252, 62, 196, 164, 195, 62, 254, 190, 253, 62, 194, 54, 194, 62, 254, 198, 253, 62, 192, 254, 191, 62, 254, 180, 253, 62, 158, 44, 30, 63, 132, 166, 131, 62, 162, 210, 33, 63, 132, 166, 131, 62, 158, 44, 30, 63, 249, 176, 120, 62, 159, 222, 30, 63, 132, 166, 131, 62, 160, 255, 31, 63, 132, 166, 131, 62, 158, 44, 30, 63, 130, 64, 130, 62, 159, 227, 30, 63, 130, 54, 130, 62, 160, 255, 31, 63, 130, 74, 130, 62, 160, 255, 31, 63, 129, 130, 128, 62, 162, 210, 33, 63, 130, 64, 130, 62, 160, 255, 31, 63, 249, 176, 120, 62, 162, 210, 33, 63, 0, 253, 127, 62, 161, 31, 33, 63, 132, 166, 131, 62, 161, 27, 33, 63, 130, 54, 130, 62, 160, 255, 31, 63, 251, 104, 123, 62, 161, 36, 33, 63, 0, 253, 127, 62, 158, 44, 30, 63, 251, 124, 123, 62, 158, 44, 30, 63, 0, 253, 127, 62, 159, 222, 30, 63, 249, 176, 120, 62, 159, 227, 30, 63, 252, 140, 123, 62, 159, 218, 30, 63, 0, 253, 127, 62, 196, 164, 195, 62, 132, 166, 131, 62, 196, 164, 195, 62, 249, 176, 120, 62, 188, 88, 188, 62, 132, 166, 131, 62, 196, 164, 195, 62, 130, 64, 130, 62, 196, 164, 195, 62, 0, 253, 127, 62, 194, 64, 194, 62, 132, 166, 131, 62, 194, 54, 194, 62, 130, 54, 130, 62, 194, 72, 194, 62, 0, 253, 127, 62, 193, 130, 192, 62, 0, 253, 127, 62, 194, 64, 194, 62, 249, 176, 120, 62, 188, 88, 188, 62, 0, 253, 127, 62, 192, 254, 191, 62, 249, 176, 120, 62, 196, 164, 195, 62, 251, 124, 123, 62, 194, 54, 194, 62, 252, 140, 123, 62, 190, 180, 189, 62, 0, 253, 127, 62, 192, 254, 191, 62, 251, 104, 123, 62, 190, 190, 189, 62, 132, 166, 131, 62, 192, 254, 191, 62, 132, 166, 131, 62, 188, 88, 188, 62, 130, 64, 130, 62, 190, 198, 189, 62, 130, 54, 130, 62, 192, 254, 191, 62, 130, 74, 130, 62, 158, 44, 30, 63, 190, 43, 62, 63, 158, 44, 30, 63, 194, 210, 65, 63, 222, 43, 94, 63, 252, 88, 252, 62, 158, 44, 30, 63, 191, 222, 62, 63, 158, 44, 30, 63, 192, 255, 63, 63, 159, 222, 30, 63, 190, 43, 62, 63, 159, 227, 30, 63, 191, 227, 62, 63, 159, 218, 30, 63, 192, 255, 63, 63, 159, 57, 31, 63, 192, 255, 63, 63, 160, 255, 31, 63, 193, 196, 64, 63, 223, 57, 95, 63, 0, 255, 255, 62, 159, 222, 30, 63, 194, 210, 65, 63, 160, 255, 31, 63, 194, 210, 65, 63, 224, 255, 95, 63, 252, 88, 252, 62, 158, 44, 30, 63, 193, 31, 65, 63, 159, 227, 30, 63, 193, 27, 65, 63, 160, 255, 31, 63, 193, 36, 65, 63, 224, 255, 95, 63, 254, 180, 253, 62, 222, 43, 94, 63, 254, 190, 253, 62, 160, 255, 31, 63, 190, 43, 62, 63, 222, 43, 94, 63, 0, 255, 255, 62, 223, 222, 94, 63, 252, 88, 252, 62, 223, 227, 94, 63, 254, 198, 253, 62, 160, 255, 31, 63, 191, 217, 62, 63, 223, 217, 94, 63, 0, 255, 255, 62, 196, 164, 195, 62, 194, 210, 65, 63, 196, 164, 195, 62, 190, 43, 62, 63, 135, 76, 7, 62, 252, 88, 252, 62, 196, 164, 195, 62, 193, 31, 65, 63, 196, 164, 195, 62, 192, 255, 63, 63, 194, 64, 194, 62, 194, 210, 65, 63, 194, 54, 194, 62, 193, 27, 65, 63, 194, 72, 194, 62, 192, 255, 63, 63, 0, 249, 255, 61, 254, 114, 254, 62, 192, 254, 191, 62, 191, 57, 63, 63, 194, 138, 193, 62, 192, 255, 63, 63, 194, 64, 194, 62, 190, 43, 62, 63, 135, 76, 7, 62, 0, 255, 255, 62, 192, 254, 191, 62, 190, 43, 62, 63, 196, 164, 195, 62, 191, 222, 62, 63, 194, 54, 194, 62, 191, 227, 62, 63, 133, 148, 4, 62, 0, 255, 255, 62, 192, 254, 191, 62, 191, 217, 62, 63, 133, 128, 4, 62, 252, 88, 252, 62, 0, 249, 255, 61, 252, 88, 252, 62, 192, 254, 191, 62, 194, 210, 65, 63, 135, 76, 7, 62, 254, 190, 253, 62, 132, 108, 4, 62, 254, 198, 253, 62, 0, 249, 255, 61, 254, 180, 253, 62, 192, 254, 191, 62, 193, 36, 65, 63, 158, 44, 30, 63, 254, 43, 126, 63, 158, 44, 30, 63, 234, 128, 233, 59, 222, 43, 94, 63, 132, 166, 131, 62, 158, 44, 30, 63, 255, 222, 126, 63, 158, 44, 30, 63, 0, 0, 0, 0, 158, 44, 30, 63, 0, 0, 128, 63, 159, 222, 30, 63, 254, 43, 126, 63, 159, 227, 30, 63, 255, 227, 126, 63, 159, 218, 30, 63, 0, 0, 0, 0, 159, 218, 30, 63, 0, 0, 128, 63, 159, 57, 31, 63, 0, 0, 128, 63, 160, 255, 31, 63, 197, 0, 69, 59, 224, 255, 95, 63, 130, 138, 129, 62, 159, 222, 30, 63, 234, 128, 233, 59, 160, 255, 31, 63, 234, 128, 233, 59, 222, 43, 94, 63, 0, 253, 127, 62, 158, 44, 30, 63, 144, 0, 144, 59, 159, 227, 30, 63, 142, 128, 141, 59, 160, 255, 31, 63, 147, 128, 146, 59, 223, 217, 94, 63, 0, 253, 127, 62, 223, 222, 94, 63, 132, 166, 131, 62, 160, 255, 31, 63, 254, 43, 126, 63, 224, 255, 95, 63, 132, 166, 131, 62, 222, 43, 94, 63, 130, 64, 130, 62, 223, 227, 94, 63, 130, 54, 130, 62, 160, 255, 31, 63, 255, 217, 126, 63, 224, 255, 95, 63, 130, 74, 130, 62, 135, 76, 7, 62, 132, 166, 131, 62, 196, 164, 195, 62, 234, 128, 233, 59, 196, 164, 195, 62, 254, 43, 126, 63, 135, 76, 7, 62, 130, 64, 130, 62, 135, 76, 7, 62, 0, 253, 127, 62, 192, 254, 191, 62, 234, 128, 233, 59, 133, 128, 4, 62, 132, 166, 131, 62, 132, 108, 4, 62, 130, 54, 130, 62, 133, 148, 4, 62, 0, 253, 127, 62, 192, 254, 191, 62, 147, 128, 146, 59, 131, 20, 3, 62, 0, 253, 127, 62, 192, 254, 191, 62, 255, 57, 127, 63, 194, 138, 193, 62, 0, 0, 0, 0, 196, 164, 195, 62, 144, 0, 144, 59, 196, 164, 195, 62, 0, 0, 0, 0, 196, 164, 195, 62, 0, 0, 128, 63, 194, 64, 194, 62, 234, 128, 233, 59, 194, 54, 194, 62, 142, 128, 141, 59, 194, 72, 194, 62, 0, 0, 128, 63, 194, 72, 194, 62, 0, 0, 0, 0, 194, 64, 194, 62, 254, 43, 126, 63, 0, 249, 255, 61, 132, 166, 131, 62, 192, 254, 191, 62, 254, 43, 126, 63, 196, 164, 195, 62, 255, 222, 126, 63, 194, 54, 194, 62, 255, 227, 126, 63, 0, 249, 255, 61, 130, 74, 130, 62, 192, 254, 191, 62, 255, 217, 126, 63, 162, 210, 33, 63, 0, 255, 255, 62, 160, 189, 31, 63, 0, 255, 255, 62, 160, 189, 31, 63, 0, 255, 255, 62, 160, 189, 31, 63, 0, 255, 255, 62, 188, 88, 188, 62, 0, 255, 255, 62, 192, 254, 191, 62, 130, 210, 1, 63, 192, 254, 191, 62, 255, 122, 255, 62, 192, 254, 191, 62, 255, 122, 255, 62, 192, 254, 191, 62, 255, 122, 255, 62, 160, 255, 31, 63, 129, 130, 128, 62, 160, 255, 31, 63, 129, 130, 128, 62, 160, 255, 31, 63, 129, 130, 128, 62, 160, 255, 31, 63, 249, 176, 120, 62, 162, 210, 33, 63, 0, 253, 127, 62, 193, 130, 192, 62, 0, 253, 127, 62, 193, 130, 192, 62, 0, 253, 127, 62, 193, 130, 192, 62, 0, 253, 127, 62, 188, 88, 188, 62, 0, 253, 127, 62, 192, 254, 191, 62, 249, 176, 120, 62, 159, 218, 30, 63, 192, 255, 63, 63, 159, 218, 30, 63, 192, 255, 63, 63, 159, 57, 31, 63, 192, 255, 63, 63, 223, 57, 95, 63, 0, 255, 255, 62, 160, 255, 31, 63, 194, 210, 65, 63, 224, 255, 95, 63, 252, 88, 252, 62, 194, 72, 194, 62, 192, 255, 63, 63, 194, 72, 194, 62, 192, 255, 63, 63, 0, 249, 255, 61, 254, 114, 254, 62, 192, 254, 191, 62, 191, 57, 63, 63, 194, 138, 193, 62, 192, 255, 63, 63, 159, 57, 31, 63, 0, 0, 128, 63, 160, 255, 31, 63, 197, 0, 69, 59, 224, 255, 95, 63, 130, 138, 129, 62, 131, 20, 3, 62, 0, 253, 127, 62, 194, 138, 193, 62, 0, 0, 0, 0, 196, 164, 195, 62, 0, 0, 0, 0, 196, 164, 195, 62, 0, 0, 128, 63, 0, 249, 255, 61, 132, 166, 131, 62, 158, 44, 30, 63, 252, 88, 252, 62, 160, 255, 31, 63, 130, 210, 1, 63, 160, 189, 31, 63, 0, 255, 255, 62, 196, 164, 195, 62, 252, 88, 252, 62, 192, 254, 191, 62, 130, 210, 1, 63, 192, 254, 191, 62, 130, 210, 1, 63, 192, 254, 191, 62, 255, 122, 255, 62, 160, 255, 31, 63, 129, 130, 128, 62, 160, 255, 31, 63, 129, 130, 128, 62, 160, 255, 31, 63, 129, 130, 128, 62, 160, 255, 31, 63, 249, 176, 120, 62, 193, 130, 192, 62, 0, 253, 127, 62, 193, 130, 192, 62, 0, 253, 127, 62, 193, 130, 192, 62, 0, 253, 127, 62, 159, 218, 30, 63, 192, 255, 63, 63, 159, 218, 30, 63, 192, 255, 63, 63, 224, 255, 95, 63, 252, 88, 252, 62, 194, 72, 194, 62, 192, 255, 63, 63, 194, 72, 194, 62, 192, 255, 63, 63, 135, 76, 7, 62, 0, 255, 255, 62, 158, 44, 30, 63, 0, 0, 0, 0, 222, 43, 94, 63, 0, 253, 127, 62, 160, 255, 31, 63, 254, 43, 126, 63, 192, 254, 191, 62, 234, 128, 233, 59, 196, 164, 195, 62, 0, 0, 128, 63, 0, 249, 255, 61, 132, 166, 131, 62, 162, 210, 33, 63, 252, 88, 252, 62, 158, 44, 30, 63, 252, 88, 252, 62, 160, 189, 31, 63, 0, 255, 255, 62, 196, 164, 195, 62, 130, 210, 1, 63, 196, 164, 195, 62, 252, 88, 252, 62, 192, 254, 191, 62, 255, 122, 255, 62, 158, 44, 30, 63, 132, 166, 131, 62, 158, 44, 30, 63, 249, 176, 120, 62, 160, 255, 31, 63, 129, 130, 128, 62, 160, 255, 31, 63, 129, 130, 128, 62, 196, 164, 195, 62, 132, 166, 131, 62, 188, 88, 188, 62, 132, 166, 131, 62, 193, 130, 192, 62, 0, 253, 127, 62, 158, 44, 30, 63, 190, 43, 62, 63, 196, 164, 195, 62, 194, 210, 65, 63),
"format": 34359742487,
"index_count": 900,
"index_data": PackedByteArray(112, 0, 22, 0, 66, 0, 112, 0, 66, 0, 162, 0, 164, 0, 135, 0, 86, 0, 164, 0, 86, 0, 110, 0, 65, 0, 45, 0, 136, 0, 65, 0, 136, 0, 163, 0, 1, 0, 87, 0, 137, 0, 1, 0, 137, 0, 44, 0, 24, 0, 2, 0, 43, 0, 24, 0, 43, 0, 64, 0, 0, 0, 3, 0, 7, 0, 0, 0, 7, 0, 6, 0, 3, 0, 4, 0, 8, 0, 3, 0, 8, 0, 7, 0, 6, 0, 7, 0, 21, 0, 6, 0, 21, 0, 18, 0, 7, 0, 8, 0, 10, 0, 7, 0, 10, 0, 21, 0, 1, 0, 11, 0, 14, 0, 1, 0, 14, 0, 13, 0, 11, 0, 12, 0, 16, 0, 11, 0, 16, 0, 14, 0, 13, 0, 14, 0, 9, 0, 13, 0, 9, 0, 5, 0, 14, 0, 16, 0, 10, 0, 14, 0, 10, 0, 9, 0, 2, 0, 17, 0, 20, 0, 2, 0, 20, 0, 19, 0, 17, 0, 18, 0, 21, 0, 17, 0, 21, 0, 20, 0, 19, 0, 20, 0, 15, 0, 19, 0, 15, 0, 12, 0, 20, 0, 21, 0, 10, 0, 20, 0, 10, 0, 15, 0, 22, 0, 25, 0, 29, 0, 22, 0, 29, 0, 28, 0, 25, 0, 26, 0, 30, 0, 25, 0, 30, 0, 29, 0, 28, 0, 29, 0, 42, 0, 28, 0, 42, 0, 39, 0, 29, 0, 30, 0, 32, 0, 29, 0, 32, 0, 42, 0, 23, 0, 33, 0, 36, 0, 23, 0, 36, 0, 35, 0, 33, 0, 34, 0, 37, 0, 33, 0, 37, 0, 36, 0, 35, 0, 36, 0, 31, 0, 35, 0, 31, 0, 27, 0, 36, 0, 37, 0, 32, 0, 36, 0, 32, 0, 31, 0, 24, 0, 38, 0, 41, 0, 24, 0, 41, 0, 40, 0, 38, 0, 39, 0, 42, 0, 38, 0, 42, 0, 41, 0, 40, 0, 41, 0, 37, 0, 40, 0, 37, 0, 34, 0, 41, 0, 42, 0, 32, 0, 41, 0, 32, 0, 37, 0, 43, 0, 46, 0, 49, 0, 43, 0, 49, 0, 48, 0, 46, 0, 47, 0, 50, 0, 46, 0, 50, 0, 49, 0, 48, 0, 49, 0, 63, 0, 48, 0, 63, 0, 60, 0, 49, 0, 50, 0, 51, 0, 49, 0, 51, 0, 63, 0, 44, 0, 52, 0, 56, 0, 44, 0, 56, 0, 55, 0, 52, 0, 54, 0, 58, 0, 52, 0, 58, 0, 56, 0, 55, 0, 56, 0, 50, 0, 55, 0, 50, 0, 47, 0, 56, 0, 58, 0, 51, 0, 56, 0, 51, 0, 50, 0, 45, 0, 59, 0, 62, 0, 45, 0, 62, 0, 61, 0, 59, 0, 60, 0, 63, 0, 59, 0, 63, 0, 62, 0, 61, 0, 62, 0, 57, 0, 61, 0, 57, 0, 53, 0, 62, 0, 63, 0, 51, 0, 62, 0, 51, 0, 57, 0, 64, 0, 67, 0, 70, 0, 64, 0, 70, 0, 69, 0, 67, 0, 68, 0, 71, 0, 67, 0, 71, 0, 70, 0, 69, 0, 70, 0, 84, 0, 69, 0, 84, 0, 81, 0, 70, 0, 71, 0, 72, 0, 70, 0, 72, 0, 84, 0, 65, 0, 73, 0, 77, 0, 65, 0, 77, 0, 76, 0, 73, 0, 75, 0, 79, 0, 73, 0, 79, 0, 77, 0, 76, 0, 77, 0, 71, 0, 76, 0, 71, 0, 68, 0, 77, 0, 79, 0, 72, 0, 77, 0, 72, 0, 71, 0, 66, 0, 80, 0, 83, 0, 66, 0, 83, 0, 82, 0, 80, 0, 81, 0, 84, 0, 80, 0, 84, 0, 83, 0, 82, 0, 83, 0, 78, 0, 82, 0, 78, 0, 74, 0, 83, 0, 84, 0, 72, 0, 83, 0, 72, 0, 78, 0, 85, 0, 88, 0, 91, 0, 85, 0, 91, 0, 90, 0, 88, 0, 89, 0, 92, 0, 88, 0, 92, 0, 91, 0, 90, 0, 91, 0, 108, 0, 90, 0, 108, 0, 104, 0, 91, 0, 92, 0, 93, 0, 91, 0, 93, 0, 108, 0, 86, 0, 96, 0, 100, 0, 86, 0, 100, 0, 99, 0, 96, 0, 97, 0, 101, 0, 96, 0, 101, 0, 100, 0, 99, 0, 100, 0, 92, 0, 99, 0, 92, 0, 89, 0, 100, 0, 101, 0, 94, 0, 100, 0, 94, 0, 92, 0, 87, 0, 103, 0, 107, 0, 87, 0, 107, 0, 106, 0, 103, 0, 105, 0, 109, 0, 103, 0, 109, 0, 107, 0, 106, 0, 107, 0, 102, 0, 106, 0, 102, 0, 98, 0, 107, 0, 109, 0, 95, 0, 107, 0, 95, 0, 102, 0, 110, 0, 113, 0, 116, 0, 110, 0, 116, 0, 115, 0, 113, 0, 114, 0, 117, 0, 113, 0, 117, 0, 116, 0, 115, 0, 116, 0, 134, 0, 115, 0, 134, 0, 130, 0, 116, 0, 117, 0, 120, 0, 116, 0, 120, 0, 134, 0, 111, 0, 121, 0, 125, 0, 111, 0, 125, 0, 124, 0, 121, 0, 123, 0, 127, 0, 121, 0, 127, 0, 125, 0, 124, 0, 125, 0, 117, 0, 124, 0, 117, 0, 114, 0, 125, 0, 127, 0, 119, 0, 125, 0, 119, 0, 117, 0, 112, 0, 128, 0, 132, 0, 112, 0, 132, 0, 131, 0, 128, 0, 129, 0, 133, 0, 128, 0, 133, 0, 132, 0, 131, 0, 132, 0, 126, 0, 131, 0, 126, 0, 122, 0, 132, 0, 133, 0, 118, 0, 132, 0, 118, 0, 126, 0, 135, 0, 138, 0, 142, 0, 135, 0, 142, 0, 141, 0, 138, 0, 140, 0, 144, 0, 138, 0, 144, 0, 142, 0, 141, 0, 142, 0, 160, 0, 141, 0, 160, 0, 156, 0, 142, 0, 144, 0, 145, 0, 142, 0, 145, 0, 160, 0, 136, 0, 148, 0, 152, 0, 136, 0, 152, 0, 151, 0, 148, 0, 149, 0, 153, 0, 148, 0, 153, 0, 152, 0, 151, 0, 152, 0, 143, 0, 151, 0, 143, 0, 139, 0, 152, 0, 153, 0, 146, 0, 152, 0, 146, 0, 143, 0, 137, 0, 155, 0, 159, 0, 137, 0, 159, 0, 158, 0, 155, 0, 157, 0, 161, 0, 155, 0, 161, 0, 159, 0, 158, 0, 159, 0, 154, 0, 158, 0, 154, 0, 150, 0, 159, 0, 161, 0, 147, 0, 159, 0, 147, 0, 154, 0, 162, 0, 165, 0, 169, 0, 162, 0, 169, 0, 168, 0, 165, 0, 166, 0, 170, 0, 165, 0, 170, 0, 169, 0, 168, 0, 169, 0, 187, 0, 168, 0, 187, 0, 183, 0, 169, 0, 170, 0, 172, 0, 169, 0, 172, 0, 187, 0, 163, 0, 175, 0, 179, 0, 163, 0, 179, 0, 178, 0, 175, 0, 176, 0, 181, 0, 175, 0, 181, 0, 179, 0, 178, 0, 179, 0, 171, 0, 178, 0, 171, 0, 167, 0, 179, 0, 181, 0, 174, 0, 179, 0, 174, 0, 171, 0, 164, 0, 182, 0, 186, 0, 164, 0, 186, 0, 185, 0, 182, 0, 184, 0, 188, 0, 182, 0, 188, 0, 186, 0, 185, 0, 186, 0, 180, 0, 185, 0, 180, 0, 177, 0, 186, 0, 188, 0, 173, 0, 186, 0, 173, 0, 180, 0, 112, 0, 162, 0, 168, 0, 112, 0, 168, 0, 128, 0, 128, 0, 168, 0, 183, 0, 128, 0, 183, 0, 129, 0, 130, 0, 184, 0, 182, 0, 130, 0, 182, 0, 115, 0, 115, 0, 182, 0, 164, 0, 115, 0, 164, 0, 110, 0, 22, 0, 112, 0, 131, 0, 22, 0, 131, 0, 25, 0, 25, 0, 131, 0, 122, 0, 25, 0, 122, 0, 26, 0, 27, 0, 123, 0, 121, 0, 27, 0, 121, 0, 35, 0, 35, 0, 121, 0, 111, 0, 35, 0, 111, 0, 23, 0, 2, 0, 24, 0, 40, 0, 2, 0, 40, 0, 17, 0, 17, 0, 40, 0, 34, 0, 17, 0, 34, 0, 18, 0, 18, 0, 34, 0, 33, 0, 18, 0, 33, 0, 6, 0, 6, 0, 33, 0, 23, 0, 6, 0, 23, 0, 0, 0, 163, 0, 136, 0, 151, 0, 163, 0, 151, 0, 175, 0, 175, 0, 151, 0, 139, 0, 175, 0, 139, 0, 176, 0, 177, 0, 140, 0, 138, 0, 177, 0, 138, 0, 185, 0, 185, 0, 138, 0, 135, 0, 185, 0, 135, 0, 164, 0, 45, 0, 65, 0, 76, 0, 45, 0, 76, 0, 59, 0, 59, 0, 76, 0, 68, 0, 59, 0, 68, 0, 60, 0, 60, 0, 68, 0, 67, 0, 60, 0, 67, 0, 48, 0, 48, 0, 67, 0, 64, 0, 48, 0, 64, 0, 43, 0, 85, 0, 111, 0, 124, 0, 85, 0, 124, 0, 88, 0, 88, 0, 124, 0, 114, 0, 88, 0, 114, 0, 89, 0, 89, 0, 114, 0, 113, 0, 89, 0, 113, 0, 99, 0, 99, 0, 113, 0, 110, 0, 99, 0, 110, 0, 86, 0, 44, 0, 137, 0, 158, 0, 44, 0, 158, 0, 52, 0, 52, 0, 158, 0, 150, 0, 52, 0, 150, 0, 54, 0, 53, 0, 149, 0, 148, 0, 53, 0, 148, 0, 61, 0, 61, 0, 148, 0, 136, 0, 61, 0, 136, 0, 45, 0, 1, 0, 44, 0, 55, 0, 1, 0, 55, 0, 11, 0, 11, 0, 55, 0, 47, 0, 11, 0, 47, 0, 12, 0, 12, 0, 47, 0, 46, 0, 12, 0, 46, 0, 19, 0, 19, 0, 46, 0, 43, 0, 19, 0, 43, 0, 2, 0, 162, 0, 66, 0, 82, 0, 162, 0, 82, 0, 165, 0, 165, 0, 82, 0, 74, 0, 165, 0, 74, 0, 166, 0, 167, 0, 75, 0, 73, 0, 167, 0, 73, 0, 178, 0, 178, 0, 73, 0, 65, 0, 178, 0, 65, 0, 163, 0, 137, 0, 87, 0, 106, 0, 137, 0, 106, 0, 155, 0, 155, 0, 106, 0, 98, 0, 155, 0, 98, 0, 157, 0, 156, 0, 97, 0, 96, 0, 156, 0, 96, 0, 141, 0, 141, 0, 96, 0, 86, 0, 141, 0, 86, 0, 135, 0, 87, 0, 1, 0, 13, 0, 87, 0, 13, 0, 103, 0, 103, 0, 13, 0, 5, 0, 103, 0, 5, 0, 105, 0, 104, 0, 4, 0, 3, 0, 104, 0, 3, 0, 90, 0, 90, 0, 3, 0, 0, 0, 90, 0, 0, 0, 85, 0, 66, 0, 22, 0, 28, 0, 66, 0, 28, 0, 80, 0, 80, 0, 28, 0, 39, 0, 80, 0, 39, 0, 81, 0, 81, 0, 39, 0, 38, 0, 81, 0, 38, 0, 69, 0, 69, 0, 38, 0, 24, 0, 69, 0, 24, 0, 64, 0, 111, 0, 85, 0, 0, 0, 111, 0, 0, 0, 23, 0),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 268,
"vertex_data": PackedByteArray(172, 33, 116, 63, 43, 189, 115, 63, 231, 35, 134, 191, 253, 125, 113, 63, 72, 102, 134, 63, 218, 131, 113, 191, 72, 102, 134, 63, 219, 129, 113, 63, 252, 127, 113, 191, 148, 33, 116, 63, 214, 217, 121, 63, 196, 135, 133, 191, 2, 190, 115, 63, 147, 134, 129, 63, 187, 249, 130, 191, 2, 190, 115, 63, 147, 134, 129, 63, 187, 249, 130, 191, 206, 219, 121, 63, 206, 31, 116, 63, 188, 135, 133, 191, 11, 10, 122, 63, 113, 12, 122, 63, 217, 212, 132, 191, 224, 120, 121, 63, 241, 17, 129, 63, 231, 160, 130, 191, 224, 120, 121, 63, 241, 17, 129, 63, 231, 160, 130, 191, 134, 246, 127, 63, 87, 66, 128, 63, 182, 107, 129, 191, 37, 221, 121, 63, 103, 134, 133, 63, 80, 35, 116, 191, 146, 134, 129, 63, 188, 248, 130, 63, 2, 192, 115, 191, 143, 31, 116, 63, 94, 134, 133, 63, 26, 225, 121, 191, 173, 15, 122, 63, 121, 211, 132, 63, 233, 11, 122, 191, 241, 17, 129, 63, 231, 159, 130, 63, 225, 122, 121, 191, 241, 17, 129, 63, 231, 159, 130, 63, 225, 122, 121, 191, 103, 134, 133, 63, 82, 33, 116, 63, 37, 223, 121, 191, 147, 134, 129, 63, 2, 190, 115, 63, 187, 249, 130, 191, 94, 134, 133, 63, 26, 223, 121, 63, 141, 33, 116, 191, 121, 211, 132, 63, 236, 9, 122, 63, 170, 17, 122, 191, 108, 29, 129, 63, 77, 99, 121, 63, 146, 155, 130, 191, 183, 129, 113, 63, 72, 102, 134, 191, 173, 127, 113, 191, 43, 189, 115, 63, 171, 35, 116, 191, 231, 35, 134, 191, 72, 102, 134, 63, 252, 127, 113, 191, 218, 131, 113, 191, 149, 33, 116, 63, 196, 135, 133, 191, 215, 219, 121, 191, 1, 190, 115, 63, 187, 249, 130, 191, 147, 135, 129, 191, 1, 190, 115, 63, 187, 249, 130, 191, 147, 135, 129, 191, 206, 219, 121, 63, 188, 135, 133, 191, 205, 33, 116, 191, 12, 10, 122, 63, 217, 212, 132, 191, 111, 14, 122, 191, 223, 120, 121, 63, 231, 160, 130, 191, 242, 18, 129, 191, 223, 120, 121, 63, 231, 160, 130, 191, 242, 18, 129, 191, 135, 246, 127, 63, 183, 107, 129, 191, 86, 67, 128, 191, 214, 217, 121, 63, 147, 35, 116, 191, 196, 135, 133, 191, 147, 134, 129, 63, 0, 192, 115, 191, 188, 249, 130, 191, 207, 31, 116, 63, 206, 221, 121, 191, 188, 135, 133, 191, 113, 12, 122, 63, 10, 12, 122, 191, 217, 212, 132, 191, 241, 17, 129, 63, 224, 122, 121, 191, 231, 160, 130, 191, 103, 134, 133, 63, 37, 223, 121, 191, 81, 35, 116, 191, 147, 134, 129, 63, 188, 249, 130, 191, 0, 192, 115, 191, 94, 134, 133, 63, 142, 33, 116, 191, 26, 225, 121, 191, 121, 211, 132, 63, 170, 17, 122, 191, 235, 11, 122, 191, 108, 29, 129, 63, 146, 155, 130, 191, 79, 101, 121, 191, 72, 102, 134, 63, 253, 125, 113, 63, 219, 129, 113, 63, 219, 129, 113, 63, 72, 102, 134, 63, 253, 125, 113, 63, 253, 125, 113, 63, 219, 129, 113, 63, 72, 102, 134, 63, 103, 134, 133, 63, 37, 221, 121, 63, 82, 33, 116, 63, 188, 248, 130, 63, 146, 134, 129, 63, 4, 190, 115, 63, 94, 134, 133, 63, 142, 31, 116, 63, 26, 223, 121, 63, 121, 211, 132, 63, 173, 15, 122, 63, 235, 9, 122, 63, 231, 159, 130, 63, 241, 17, 129, 63, 224, 120, 121, 63, 182, 106, 129, 63, 88, 66, 128, 63, 132, 246, 127, 63, 82, 33, 116, 63, 103, 134, 133, 63, 37, 221, 121, 63, 4, 190, 115, 63, 188, 248, 130, 63, 146, 134, 129, 63, 4, 190, 115, 63, 188, 248, 130, 63, 146, 134, 129, 63, 26, 223, 121, 63, 94, 134, 133, 63, 142, 31, 116, 63, 234, 9, 122, 63, 121, 211, 132, 63, 173, 15, 122, 63, 224, 120, 121, 63, 231, 159, 130, 63, 241, 17, 129, 63, 224, 120, 121, 63, 231, 159, 130, 63, 241, 17, 129, 63, 37, 221, 121, 63, 82, 33, 116, 63, 103, 134, 133, 63, 188, 248, 130, 63, 4, 190, 115, 63, 146, 134, 129, 63, 142, 31, 116, 63, 26, 223, 121, 63, 94, 134, 133, 63, 173, 15, 122, 63, 235, 9, 122, 63, 121, 211, 132, 63, 145, 154, 130, 63, 75, 99, 121, 63, 109, 29, 129, 63, 72, 102, 134, 63, 218, 131, 113, 191, 253, 125, 113, 63, 219, 129, 113, 63, 252, 127, 113, 191, 72, 102, 134, 63, 174, 125, 113, 63, 72, 102, 134, 191, 183, 129, 113, 63, 103, 134, 133, 63, 80, 35, 116, 191, 37, 221, 121, 63, 188, 248, 130, 63, 2, 192, 115, 191, 146, 134, 129, 63, 94, 134, 133, 63, 26, 225, 121, 191, 143, 31, 116, 63, 121, 211, 132, 63, 233, 11, 122, 191, 173, 15, 122, 63, 231, 159, 130, 63, 225, 122, 121, 191, 241, 17, 129, 63, 183, 106, 129, 63, 133, 248, 127, 191, 87, 66, 128, 63, 82, 33, 116, 63, 37, 223, 121, 191, 103, 134, 133, 63, 3, 190, 115, 63, 146, 135, 129, 191, 188, 248, 130, 63, 3, 190, 115, 63, 146, 135, 129, 191, 188, 248, 130, 63, 26, 223, 121, 63, 141, 33, 116, 191, 94, 134, 133, 63, 236, 9, 122, 63, 170, 17, 122, 191, 121, 211, 132, 63, 224, 120, 121, 63, 242, 18, 129, 191, 231, 159, 130, 63, 224, 120, 121, 63, 242, 18, 129, 191, 231, 159, 130, 63, 214, 217, 121, 63, 196, 135, 133, 191, 148, 33, 116, 63, 188, 248, 130, 63, 146, 135, 129, 191, 3, 190, 115, 63, 206, 31, 116, 63, 188, 135, 133, 191, 206, 219, 121, 63, 113, 12, 122, 63, 217, 212, 132, 191, 11, 10, 122, 63, 145, 154, 130, 63, 108, 30, 129, 191, 78, 99, 121, 63, 172, 201, 115, 191, 231, 22, 116, 63, 21, 36, 134, 191, 72, 102, 134, 191, 174, 125, 113, 63, 182, 131, 113, 191, 218, 131, 113, 191, 72, 102, 134, 63, 252, 127, 113, 191, 215, 219, 121, 191, 149, 33, 116, 63, 196, 135, 133, 191, 147, 135, 129, 191, 1, 190, 115, 63, 187, 249, 130, 191, 205, 33, 116, 191, 206, 219, 121, 63, 188, 135, 133, 191, 111, 14, 122, 191, 12, 10, 122, 63, 217, 212, 132, 191, 242, 18, 129, 191, 223, 120, 121, 63, 231, 160, 130, 191, 86, 67, 128, 191, 135, 246, 127, 63, 183, 107, 129, 191, 86, 67, 128, 191, 135, 246, 127, 63, 183, 107, 129, 191, 86, 67, 128, 191, 135, 246, 127, 63, 183, 107, 129, 191, 196, 135, 133, 191, 214, 217, 121, 63, 147, 35, 116, 191, 187, 249, 130, 191, 147, 134, 129, 63, 0, 192, 115, 191, 187, 249, 130, 191, 147, 134, 129, 63, 0, 192, 115, 191, 188, 135, 133, 191, 207, 31, 116, 63, 206, 221, 121, 191, 217, 212, 132, 191, 113, 12, 122, 63, 10, 12, 122, 191, 231, 160, 130, 191, 241, 17, 129, 63, 224, 122, 121, 191, 231, 160, 130, 191, 241, 17, 129, 63, 224, 122, 121, 191, 81, 35, 116, 191, 103, 134, 133, 63, 37, 223, 121, 191, 0, 192, 115, 191, 147, 134, 129, 63, 188, 249, 130, 191, 0, 192, 115, 191, 147, 134, 129, 63, 188, 249, 130, 191, 26, 225, 121, 191, 94, 134, 133, 63, 142, 33, 116, 191, 234, 11, 122, 191, 121, 211, 132, 63, 170, 17, 122, 191, 79, 101, 121, 191, 108, 29, 129, 63, 146, 155, 130, 191, 79, 101, 121, 191, 108, 29, 129, 63, 146, 155, 130, 191, 72, 102, 134, 191, 182, 131, 113, 191, 173, 127, 113, 191, 230, 24, 116, 191, 172, 201, 115, 191, 21, 36, 134, 191, 173, 127, 113, 191, 72, 102, 134, 191, 182, 131, 113, 191, 196, 135, 133, 191, 148, 35, 116, 191, 215, 219, 121, 191, 187, 249, 130, 191, 255, 191, 115, 191, 147, 135, 129, 191, 188, 135, 133, 191, 206, 221, 121, 191, 206, 33, 116, 191, 217, 212, 132, 191, 11, 12, 122, 191, 111, 14, 122, 191, 231, 160, 130, 191, 224, 122, 121, 191, 242, 18, 129, 191, 183, 107, 129, 191, 136, 248, 127, 191, 84, 67, 128, 191, 183, 107, 129, 191, 136, 248, 127, 191, 84, 67, 128, 191, 183, 107, 129, 191, 136, 248, 127, 191, 84, 67, 128, 191, 148, 35, 116, 191, 215, 219, 121, 191, 196, 135, 133, 191, 255, 191, 115, 191, 147, 135, 129, 191, 187, 249, 130, 191, 255, 191, 115, 191, 147, 135, 129, 191, 187, 249, 130, 191, 206, 221, 121, 191, 206, 33, 116, 191, 188, 135, 133, 191, 11, 12, 122, 191, 111, 14, 122, 191, 217, 212, 132, 191, 224, 122, 121, 191, 242, 18, 129, 191, 231, 160, 130, 191, 224, 122, 121, 191, 242, 18, 129, 191, 231, 160, 130, 191, 215, 219, 121, 191, 196, 135, 133, 191, 148, 35, 116, 191, 187, 249, 130, 191, 147, 135, 129, 191, 255, 191, 115, 191, 187, 249, 130, 191, 147, 135, 129, 191, 255, 191, 115, 191, 206, 33, 116, 191, 188, 135, 133, 191, 206, 221, 121, 191, 111, 14, 122, 191, 217, 212, 132, 191, 11, 12, 122, 191, 146, 155, 130, 191, 107, 30, 129, 191, 82, 101, 121, 191, 146, 155, 130, 191, 107, 30, 129, 191, 82, 101, 121, 191, 72, 102, 134, 191, 183, 129, 113, 63, 174, 125, 113, 63, 218, 131, 113, 191, 253, 125, 113, 63, 72, 102, 134, 63, 252, 127, 113, 191, 72, 102, 134, 63, 219, 129, 113, 63, 196, 135, 133, 191, 148, 33, 116, 63, 214, 217, 121, 63, 187, 249, 130, 191, 2, 190, 115, 63, 147, 134, 129, 63, 187, 249, 130, 191, 2, 190, 115, 63, 147, 134, 129, 63, 188, 135, 133, 191, 206, 219, 121, 63, 206, 31, 116, 63, 217, 212, 132, 191, 11, 10, 122, 63, 113, 12, 122, 63, 231, 160, 130, 191, 223, 120, 121, 63, 241, 17, 129, 63, 231, 160, 130, 191, 223, 120, 121, 63, 241, 17, 129, 63, 182, 107, 129, 191, 134, 246, 127, 63, 87, 66, 128, 63, 182, 107, 129, 191, 134, 246, 127, 63, 87, 66, 128, 63, 182, 107, 129, 191, 134, 246, 127, 63, 87, 66, 128, 63, 80, 35, 116, 191, 37, 221, 121, 63, 103, 134, 133, 63, 2, 192, 115, 191, 146, 134, 129, 63, 188, 248, 130, 63, 2, 192, 115, 191, 146, 134, 129, 63, 188, 248, 130, 63, 26, 225, 121, 191, 143, 31, 116, 63, 94, 134, 133, 63, 233, 11, 122, 191, 173, 15, 122, 63, 121, 211, 132, 63, 225, 122, 121, 191, 241, 17, 129, 63, 231, 159, 130, 63, 225, 122, 121, 191, 241, 17, 129, 63, 231, 159, 130, 63, 37, 223, 121, 191, 103, 134, 133, 63, 82, 33, 116, 63, 187, 249, 130, 191, 147, 134, 129, 63, 2, 190, 115, 63, 187, 249, 130, 191, 147, 134, 129, 63, 2, 190, 115, 63, 141, 33, 116, 191, 94, 134, 133, 63, 26, 223, 121, 63, 170, 17, 122, 191, 121, 211, 132, 63, 236, 9, 122, 63, 146, 155, 130, 191, 108, 29, 129, 63, 77, 99, 121, 63, 146, 155, 130, 191, 108, 29, 129, 63, 77, 99, 121, 63, 182, 131, 113, 191, 72, 102, 134, 191, 174, 125, 113, 63, 252, 127, 113, 191, 218, 131, 113, 191, 72, 102, 134, 63, 72, 102, 134, 191, 173, 127, 113, 191, 183, 129, 113, 63, 147, 35, 116, 191, 196, 135, 133, 191, 214, 217, 121, 63, 0, 192, 115, 191, 188, 249, 130, 191, 147, 134, 129, 63, 0, 192, 115, 191, 188, 249, 130, 191, 147, 134, 129, 63, 206, 221, 121, 191, 188, 135, 133, 191, 207, 31, 116, 63, 10, 12, 122, 191, 217, 212, 132, 191, 113, 12, 122, 63, 224, 122, 121, 191, 231, 160, 130, 191, 241, 17, 129, 63, 224, 122, 121, 191, 231, 160, 130, 191, 241, 17, 129, 63, 135, 248, 127, 191, 183, 107, 129, 191, 86, 66, 128, 63, 135, 248, 127, 191, 183, 107, 129, 191, 86, 66, 128, 63, 135, 248, 127, 191, 183, 107, 129, 191, 86, 66, 128, 63, 37, 223, 121, 191, 81, 35, 116, 191, 103, 134, 133, 63, 146, 135, 129, 191, 1, 192, 115, 191, 188, 248, 130, 63, 146, 135, 129, 191, 1, 192, 115, 191, 188, 248, 130, 63, 142, 33, 116, 191, 26, 225, 121, 191, 94, 134, 133, 63, 170, 17, 122, 191, 234, 11, 122, 191, 121, 211, 132, 63, 242, 18, 129, 191, 225, 122, 121, 191, 231, 159, 130, 63, 242, 18, 129, 191, 225, 122, 121, 191, 231, 159, 130, 63, 196, 135, 133, 191, 215, 219, 121, 191, 149, 33, 116, 63, 147, 135, 129, 191, 187, 249, 130, 191, 1, 190, 115, 63, 147, 135, 129, 191, 187, 249, 130, 191, 1, 190, 115, 63, 188, 135, 133, 191, 205, 33, 116, 191, 206, 219, 121, 63, 217, 212, 132, 191, 111, 14, 122, 191, 12, 10, 122, 63, 108, 30, 129, 191, 146, 155, 130, 191, 80, 99, 121, 63, 108, 30, 129, 191, 146, 155, 130, 191, 80, 99, 121, 63, 2, 190, 115, 63, 147, 134, 129, 63, 187, 249, 130, 191, 134, 246, 127, 63, 87, 66, 128, 63, 182, 107, 129, 191, 134, 246, 127, 63, 87, 66, 128, 63, 182, 107, 129, 191, 134, 246, 127, 63, 87, 66, 128, 63, 182, 107, 129, 191, 1, 190, 115, 63, 187, 249, 130, 191, 147, 135, 129, 191, 1, 190, 115, 63, 187, 249, 130, 191, 147, 135, 129, 191, 135, 246, 127, 63, 183, 107, 129, 191, 86, 67, 128, 191, 135, 246, 127, 63, 183, 107, 129, 191, 86, 67, 128, 191, 135, 246, 127, 63, 183, 107, 129, 191, 86, 67, 128, 191, 182, 106, 129, 63, 88, 66, 128, 63, 132, 246, 127, 63, 182, 106, 129, 63, 88, 66, 128, 63, 132, 246, 127, 63, 182, 106, 129, 63, 88, 66, 128, 63, 132, 246, 127, 63, 4, 190, 115, 63, 188, 248, 130, 63, 146, 134, 129, 63, 4, 190, 115, 63, 188, 248, 130, 63, 146, 134, 129, 63, 183, 106, 129, 63, 133, 248, 127, 191, 87, 66, 128, 63, 183, 106, 129, 63, 133, 248, 127, 191, 87, 66, 128, 63, 183, 106, 129, 63, 133, 248, 127, 191, 87, 66, 128, 63, 3, 190, 115, 63, 146, 135, 129, 191, 188, 248, 130, 63, 3, 190, 115, 63, 146, 135, 129, 191, 188, 248, 130, 63, 242, 18, 129, 191, 223, 120, 121, 63, 231, 160, 130, 191, 242, 18, 129, 191, 223, 120, 121, 63, 231, 160, 130, 191, 86, 67, 128, 191, 135, 246, 127, 63, 183, 107, 129, 191, 86, 67, 128, 191, 135, 246, 127, 63, 183, 107, 129, 191, 187, 249, 130, 191, 147, 134, 129, 63, 0, 192, 115, 191, 187, 249, 130, 191, 147, 134, 129, 63, 0, 192, 115, 191, 231, 160, 130, 191, 224, 122, 121, 191, 242, 18, 129, 191, 231, 160, 130, 191, 224, 122, 121, 191, 242, 18, 129, 191, 183, 107, 129, 191, 136, 248, 127, 191, 84, 67, 128, 191, 183, 107, 129, 191, 136, 248, 127, 191, 84, 67, 128, 191, 183, 107, 129, 191, 136, 248, 127, 191, 84, 67, 128, 191, 182, 107, 129, 191, 134, 246, 127, 63, 87, 66, 128, 63, 182, 107, 129, 191, 134, 246, 127, 63, 87, 66, 128, 63, 182, 107, 129, 191, 134, 246, 127, 63, 87, 66, 128, 63, 135, 248, 127, 191, 183, 107, 129, 191, 86, 66, 128, 63, 135, 248, 127, 191, 183, 107, 129, 191, 86, 66, 128, 63, 146, 135, 129, 191, 1, 192, 115, 191, 188, 248, 130, 63, 146, 135, 129, 191, 1, 192, 115, 191, 188, 248, 130, 63, 147, 135, 129, 191, 187, 249, 130, 191, 1, 190, 115, 63, 72, 102, 134, 63, 219, 129, 113, 63, 252, 127, 113, 191, 2, 190, 115, 63, 147, 134, 129, 63, 187, 249, 130, 191, 134, 246, 127, 63, 87, 66, 128, 63, 182, 107, 129, 191, 72, 102, 134, 63, 252, 127, 113, 191, 218, 131, 113, 191, 1, 190, 115, 63, 187, 249, 130, 191, 147, 135, 129, 191, 1, 190, 115, 63, 187, 249, 130, 191, 147, 135, 129, 191, 135, 246, 127, 63, 183, 107, 129, 191, 86, 67, 128, 191, 182, 106, 129, 63, 88, 66, 128, 63, 132, 246, 127, 63, 182, 106, 129, 63, 88, 66, 128, 63, 132, 246, 127, 63, 182, 106, 129, 63, 88, 66, 128, 63, 132, 246, 127, 63, 4, 190, 115, 63, 188, 248, 130, 63, 146, 134, 129, 63, 183, 106, 129, 63, 133, 248, 127, 191, 87, 66, 128, 63, 183, 106, 129, 63, 133, 248, 127, 191, 87, 66, 128, 63, 183, 106, 129, 63, 133, 248, 127, 191, 87, 66, 128, 63, 242, 18, 129, 191, 223, 120, 121, 63, 231, 160, 130, 191, 242, 18, 129, 191, 223, 120, 121, 63, 231, 160, 130, 191, 187, 249, 130, 191, 147, 134, 129, 63, 0, 192, 115, 191, 231, 160, 130, 191, 224, 122, 121, 191, 242, 18, 129, 191, 231, 160, 130, 191, 224, 122, 121, 191, 242, 18, 129, 191, 255, 191, 115, 191, 147, 135, 129, 191, 187, 249, 130, 191, 187, 249, 130, 191, 2, 190, 115, 63, 147, 134, 129, 63, 2, 192, 115, 191, 146, 134, 129, 63, 188, 248, 130, 63, 187, 249, 130, 191, 147, 134, 129, 63, 2, 190, 115, 63, 0, 192, 115, 191, 188, 249, 130, 191, 147, 134, 129, 63, 146, 135, 129, 191, 1, 192, 115, 191, 188, 248, 130, 63, 147, 135, 129, 191, 187, 249, 130, 191, 1, 190, 115, 63, 253, 125, 113, 63, 72, 102, 134, 63, 218, 131, 113, 191, 72, 102, 134, 63, 219, 129, 113, 63, 252, 127, 113, 191, 134, 246, 127, 63, 87, 66, 128, 63, 182, 107, 129, 191, 43, 189, 115, 63, 171, 35, 116, 191, 231, 35, 134, 191, 72, 102, 134, 63, 252, 127, 113, 191, 218, 131, 113, 191, 135, 246, 127, 63, 183, 107, 129, 191, 86, 67, 128, 191, 72, 102, 134, 63, 253, 125, 113, 63, 219, 129, 113, 63, 253, 125, 113, 63, 219, 129, 113, 63, 72, 102, 134, 63, 182, 106, 129, 63, 88, 66, 128, 63, 132, 246, 127, 63, 182, 106, 129, 63, 88, 66, 128, 63, 132, 246, 127, 63, 72, 102, 134, 63, 218, 131, 113, 191, 253, 125, 113, 63, 174, 125, 113, 63, 72, 102, 134, 191, 183, 129, 113, 63, 183, 106, 129, 63, 133, 248, 127, 191, 87, 66, 128, 63, 172, 201, 115, 191, 231, 22, 116, 63, 21, 36, 134, 191, 72, 102, 134, 191, 182, 131, 113, 191, 173, 127, 113, 191, 50, 245, 51, 245, 0, 128, 25, 250, 144, 149, 52, 245, 199, 11, 227, 197, 52, 245, 145, 149, 54, 116, 26, 250, 64, 221, 255, 246, 244, 127, 76, 237, 233, 195, 39, 248, 94, 126, 163, 223, 233, 195, 39, 248, 99, 7, 99, 199, 0, 247, 65, 221, 40, 126, 85, 249, 103, 226, 103, 226, 231, 123, 239, 235, 216, 204, 76, 230, 78, 121, 41, 223, 214, 204, 76, 230, 99, 24, 101, 213, 83, 213, 82, 213, 254, 95, 254, 223, 183, 171, 66, 221, 87, 37, 176, 210, 232, 195, 234, 195, 254, 63, 254, 223, 182, 171, 1, 247, 120, 11, 168, 198, 43, 187, 103, 226, 4, 36, 14, 212, 214, 204, 215, 204, 5, 69, 41, 223, 216, 204, 215, 204, 248, 58, 212, 224, 1, 247, 182, 171, 133, 116, 86, 249, 38, 248, 233, 195, 155, 120, 154, 248, 65, 221, 184, 171, 166, 90, 77, 237, 103, 226, 43, 187, 249, 91, 239, 235, 76, 230, 215, 204, 153, 106, 152, 234, 149, 149, 203, 10, 50, 244, 228, 197, 53, 245, 204, 10, 200, 139, 254, 255, 51, 245, 108, 106, 200, 139, 25, 250, 183, 171, 253, 8, 173, 242, 187, 197, 235, 195, 217, 7, 52, 241, 176, 195, 236, 195, 216, 7, 180, 192, 46, 255, 184, 171, 188, 34, 156, 218, 171, 210, 43, 187, 150, 29, 28, 217, 239, 210, 216, 204, 179, 25, 48, 213, 230, 208, 218, 204, 180, 25, 178, 193, 146, 252, 85, 213, 171, 42, 254, 191, 35, 230, 2, 247, 190, 34, 79, 141, 19, 255, 39, 248, 22, 60, 197, 142, 77, 252, 68, 221, 255, 8, 96, 165, 249, 255, 105, 226, 151, 29, 29, 169, 175, 252, 76, 230, 40, 51, 204, 170, 202, 240, 66, 221, 74, 84, 95, 165, 83, 237, 235, 195, 21, 60, 253, 191, 255, 223, 3, 247, 73, 84, 76, 141, 66, 250, 105, 226, 212, 68, 224, 166, 14, 237, 216, 204, 40, 51, 253, 191, 254, 223, 104, 234, 201, 138, 55, 116, 26, 250, 201, 138, 104, 234, 200, 11, 228, 197, 203, 138, 202, 138, 201, 139, 254, 255, 69, 212, 186, 162, 158, 90, 83, 237, 17, 188, 17, 188, 255, 63, 255, 223, 69, 212, 252, 136, 174, 114, 66, 250, 209, 196, 149, 157, 29, 89, 14, 237, 35, 179, 37, 179, 254, 63, 255, 223, 168, 170, 169, 170, 255, 63, 35, 230, 252, 136, 69, 212, 79, 13, 187, 197, 216, 135, 18, 188, 75, 63, 47, 255, 214, 135, 18, 188, 199, 14, 177, 195, 186, 162, 67, 212, 96, 37, 171, 210, 148, 157, 209, 196, 224, 38, 240, 210, 178, 153, 38, 179, 75, 62, 146, 252, 177, 153, 37, 179, 204, 42, 231, 208, 186, 162, 252, 136, 174, 114, 19, 255, 17, 188, 215, 135, 55, 113, 77, 252, 253, 136, 186, 162, 158, 90, 249, 255, 150, 157, 149, 157, 226, 86, 177, 252, 36, 179, 178, 153, 49, 85, 202, 240, 104, 234, 53, 117, 200, 139, 26, 250, 202, 138, 52, 117, 2, 128, 25, 250, 202, 138, 151, 21, 52, 244, 227, 197, 69, 212, 2, 119, 120, 139, 86, 249, 18, 188, 40, 120, 98, 135, 155, 248, 67, 212, 69, 93, 86, 165, 77, 237, 209, 196, 106, 98, 3, 164, 240, 235, 36, 179, 77, 102, 101, 149, 152, 234, 168, 170, 85, 85, 255, 175, 255, 223, 253, 136, 67, 93, 10, 128, 77, 237, 215, 135, 236, 67, 155, 248, 98, 199, 216, 135, 235, 67, 162, 129, 163, 223, 185, 162, 1, 119, 217, 129, 85, 249, 150, 157, 105, 98, 24, 132, 239, 235, 177, 153, 217, 76, 153, 231, 101, 213, 178, 153, 217, 76, 178, 134, 41, 223, 186, 162, 186, 43, 166, 218, 175, 210, 18, 188, 236, 67, 255, 191, 255, 223, 252, 136, 185, 43, 133, 244, 167, 198, 150, 157, 45, 59, 249, 219, 14, 212, 37, 179, 217, 76, 255, 191, 254, 223, 205, 10, 54, 245, 0, 128, 24, 250, 205, 10, 153, 149, 203, 139, 23, 250, 105, 106, 50, 245, 202, 11, 25, 186, 255, 8, 67, 221, 215, 129, 84, 249, 217, 7, 235, 195, 100, 135, 153, 248, 189, 34, 2, 247, 9, 128, 77, 237, 151, 29, 105, 226, 24, 132, 239, 235, 180, 25, 217, 204, 0, 161, 151, 234, 172, 42, 84, 213, 85, 149, 254, 223, 170, 42, 85, 213, 144, 199, 254, 223, 170, 42, 83, 213, 0, 64, 169, 170, 189, 34, 184, 171, 87, 165, 77, 237, 20, 60, 237, 195, 19, 191, 163, 223, 17, 60, 233, 195, 182, 64, 116, 160, 254, 8, 186, 171, 121, 139, 85, 249, 152, 29, 46, 187, 202, 170, 191, 236, 39, 51, 216, 204, 68, 188, 96, 223, 38, 51, 214, 204, 171, 65, 130, 162, 68, 84, 0, 247, 82, 13, 65, 186, 22, 60, 41, 248, 159, 129, 163, 223, 19, 60, 38, 248, 200, 14, 76, 188, 71, 84, 65, 221, 99, 37, 82, 173, 208, 68, 102, 226, 32, 40, 251, 173, 40, 51, 77, 230, 176, 134, 40, 223, 37, 51, 75, 230, 206, 42, 204, 179, 199, 10, 107, 106, 57, 116, 27, 250, 202, 10, 201, 10, 53, 116, 254, 255, 106, 106, 202, 10, 54, 244, 27, 186, 250, 8, 71, 84, 179, 114, 67, 250, 213, 7, 20, 60, 59, 113, 77, 252, 186, 34, 69, 84, 159, 90, 83, 237, 148, 29, 210, 68, 227, 87, 252, 237, 176, 25, 38, 51, 54, 85, 25, 251, 172, 42, 169, 42, 109, 184, 0, 160, 170, 42, 167, 42, 144, 199, 53, 252, 168, 42, 171, 42, 1, 64, 170, 234, 187, 34, 251, 8, 160, 90, 250, 255, 20, 60, 215, 7, 155, 248, 155, 184, 20, 60, 212, 7, 75, 63, 47, 255, 251, 8, 187, 34, 177, 114, 19, 255, 150, 29, 149, 29, 203, 170, 215, 253, 40, 51, 177, 25, 125, 204, 132, 169, 38, 51, 176, 25, 197, 62, 132, 253, 72, 84, 186, 34, 168, 218, 78, 173, 21, 60, 18, 60, 235, 192, 165, 159, 16, 60, 19, 60, 76, 63, 117, 224, 71, 84, 253, 8, 134, 244, 86, 185, 211, 68, 150, 29, 52, 213, 193, 172, 39, 51, 38, 51, 185, 195, 97, 159, 37, 51, 38, 51, 86, 62, 130, 226, 150, 21, 203, 138, 202, 139, 25, 250, 53, 117, 202, 138, 202, 139, 253, 255, 52, 117, 102, 234, 201, 11, 26, 186, 183, 43, 253, 136, 80, 141, 65, 250, 234, 67, 215, 135, 199, 142, 76, 252, 234, 67, 215, 135, 199, 142, 76, 252, 186, 43, 188, 162, 97, 165, 82, 237, 44, 59, 150, 157, 28, 168, 251, 237, 216, 76, 178, 153, 126, 76, 186, 252, 215, 76, 178, 153, 205, 170, 203, 243, 85, 85, 170, 170, 255, 191, 169, 234, 86, 85, 170, 170, 110, 56, 54, 252, 84, 85, 168, 170, 146, 71, 254, 159, 3, 119, 188, 162, 98, 165, 249, 255, 41, 120, 19, 188, 179, 192, 46, 255, 39, 120, 18, 188, 101, 7, 154, 184, 66, 93, 253, 136, 81, 141, 18, 255, 106, 98, 150, 157, 53, 85, 215, 253, 78, 102, 37, 179, 58, 193, 131, 253, 76, 102, 36, 179, 131, 51, 130, 169, 65, 93, 68, 212, 89, 37, 77, 173, 236, 67, 21, 188, 180, 192, 115, 224, 235, 67, 17, 188, 21, 63, 164, 159, 2, 119, 68, 212, 121, 11, 86, 185, 103, 98, 208, 196, 205, 42, 192, 172, 217, 76, 38, 179, 169, 193, 129, 226, 217, 76, 36, 179, 69, 60, 96, 159, 53, 117, 151, 21, 54, 244, 26, 186, 53, 117, 52, 117, 0, 128, 26, 250, 143, 21, 55, 117, 58, 116, 27, 250, 2, 119, 185, 43, 175, 242, 66, 186, 40, 120, 236, 67, 56, 241, 77, 188, 40, 120, 235, 67, 96, 126, 165, 223, 68, 93, 185, 43, 158, 218, 83, 173, 105, 98, 44, 59, 226, 215, 253, 173, 77, 102, 217, 76, 50, 213, 205, 179, 77, 102, 217, 76, 80, 121, 42, 223, 86, 85, 84, 85, 0, 192, 171, 170, 84, 85, 84, 85, 111, 56, 255, 223, 84, 85, 85, 85, 170, 106, 255, 223, 66, 93, 2, 119, 40, 126, 86, 249, 234, 67, 40, 120, 157, 120, 155, 248, 234, 67, 40, 120, 157, 120, 155, 248, 3, 119, 67, 93, 245, 127, 78, 237, 105, 98, 105, 98, 231, 123, 240, 235, 215, 76, 77, 102, 126, 76, 131, 233, 215, 76, 77, 102, 153, 109, 152, 234, 181, 43, 69, 93, 170, 90, 78, 237, 237, 67, 235, 67, 75, 191, 117, 160, 234, 67, 236, 67, 236, 64, 165, 223, 181, 43, 3, 119, 136, 116, 87, 249, 43, 59, 105, 98, 53, 85, 193, 236, 218, 76, 216, 76, 86, 190, 131, 162, 214, 76, 217, 76, 187, 67, 97, 223, 194, 186, 28, 244, 57, 13, 108, 202, 11, 231, 13, 177, 58, 122, 172, 226, 102, 22, 168, 214, 22, 198, 46, 143, 6, 134, 128, 164, 85, 44, 44, 221, 202, 140, 195, 8, 44, 241, 48, 196, 193, 200, 189, 8, 72, 34, 200, 227, 60, 184, 107, 49, 244, 200, 222, 216, 133, 221, 183, 102, 159, 182, 133, 244, 103, 236, 114, 35, 5, 84, 202, 248, 181, 243, 39, 172, 20, 109, 153, 247, 113, 177, 120, 156, 248, 79, 185, 229, 19, 160, 166, 183, 120, 48, 43, 225, 234, 169, 3, 152, 248, 170, 253, 252, 175, 245, 34, 255, 25, 19, 35, 201, 30, 220, 165, 100, 221, 159, 149, 241, 231, 150, 76, 114, 226, 176, 181, 231, 159, 180, 106, 59, 144, 181, 12, 214, 8, 140, 72, 28, 35, 245, 54, 203, 196, 114, 193, 86, 168, 126, 165, 232, 50, 24, 78, 235, 218, 179, 123, 226, 241, 13, 209, 182, 62, 134, 142, 241, 200, 51, 72, 250, 193, 155, 138, 221, 111, 107, 237, 202, 245, 50, 234, 176, 172, 35, 98, 179, 154, 158, 179, 229, 207, 125, 216, 144, 112, 51, 14, 169, 152, 22, 100, 93, 230, 82, 208, 247, 68, 34, 225, 5, 92, 78, 199, 245, 51, 82, 70, 35, 244, 225, 54, 166, 109, 61, 174, 7, 3, 59, 186, 251, 236, 55, 116, 74, 23, 62, 157, 232, 67, 33, 241, 152, 247, 158, 123, 243, 143, 154, 78, 164, 232, 77, 97, 250, 176, 121, 207, 156, 137, 57, 54, 168, 9, 101, 100, 60, 108, 219, 38, 173, 51, 121, 141, 79, 180, 106, 158, 224, 118, 130, 116, 115, 189, 120, 67, 246, 188, 33, 136, 114, 49, 115, 125, 244, 99, 94, 154, 47, 211, 222, 36, 162, 72, 233, 227, 170, 235, 94, 150, 242, 146, 58, 87, 235, 215, 40, 11, 161, 47, 131, 26, 209, 147, 19, 29, 217, 17, 234, 153, 93, 187, 151, 181, 240, 101, 206, 38, 16, 104, 38, 148, 232, 114, 242, 64, 77, 80, 52, 203, 246, 147, 184, 179, 47, 173, 201, 60, 216, 106, 243, 165, 171, 134, 109, 138, 247, 212, 160, 249, 180, 239, 50, 116, 226, 84, 177, 99, 156, 243, 79, 174, 229, 215, 164, 143, 158, 172, 175, 196, 253, 231, 219, 0, 101, 204, 159, 181, 241, 212, 156, 119, 96, 227, 175, 70, 226, 154, 186, 234, 64, 54, 180, 125, 217, 252, 26, 111, 207, 189, 164, 90, 233, 248, 10, 195, 251, 132, 86, 182, 230, 214, 104, 174, 181, 137, 53, 182, 170, 214, 29, 155, 77, 117, 79, 246, 243, 187, 4, 127, 17, 171, 213, 13, 220, 61, 98, 176, 13, 115, 240, 4, 184, 190, 101, 255, 139, 61, 148, 223, 251, 222, 36, 111, 252, 140, 19, 59, 180, 127, 60, 75, 166, 57, 172, 246, 226, 148, 94, 28, 92, 115, 131, 8, 230, 76, 38, 177, 115, 68, 116, 49, 245, 116, 86, 255, 53, 121, 214, 77, 162, 125, 213, 138, 242, 139, 29, 44, 207, 160, 238, 18, 171, 225, 101, 246, 244, 136, 211, 210, 211, 107, 92, 17, 223, 118, 252, 77, 75, 63, 113, 176, 254, 247, 233, 80, 88, 68, 153, 193, 239, 51, 211, 231, 41, 52, 194, 50, 228, 50, 209, 120, 147, 160, 99, 47, 245, 37, 149, 149, 143, 249, 107, 246, 254, 142, 176, 125, 168, 211, 68, 233, 230, 231, 138, 5, 171, 116, 50, 183, 227, 122, 212, 25, 111, 167, 146, 140, 243, 192, 145, 252, 35, 58, 238, 91, 205, 61, 168, 168, 85, 21, 176, 179, 223, 162, 15, 251, 228, 49, 153, 207, 238, 20, 16, 238, 87, 244, 107, 76, 244)
}]

[sub_resource type="BoxShape3D" id="BoxShape3D_p0k37"]
size = Vector3(1.64258, 2.39618, 0.509521)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_r73ri"]
resource_local_to_scene = true
transparency = 1
shading_mode = 0
albedo_color = Color(1, 1, 1, 0.00392157)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_3k3s5"]
resource_local_to_scene = true
transparency = 1
shading_mode = 0
albedo_color = Color(1, 1, 0, 0.0156863)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_nymiv"]
resource_local_to_scene = true
transparency = 1
shading_mode = 0
albedo_color = Color(1, 1, 0, 0.0156863)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_81oja"]
resource_local_to_scene = true
transparency = 1
shading_mode = 0
albedo_color = Color(1, 1, 1, 0.00392157)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_mubot"]
resource_local_to_scene = true
transparency = 1
shading_mode = 0
albedo_color = Color(1, 1, 1, 0.00392157)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_rwyeo"]
transparency = 1
albedo_color = Color(1, 0.423529, 0.458824, 0.0431373)

[sub_resource type="BoxMesh" id="BoxMesh_1iiqr"]
size = Vector3(1.115, 0.4, 1)
subdivide_width = 10
subdivide_height = 10
subdivide_depth = 10

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_mrf03"]
albedo_color = Color(1, 0.423529, 0.458824, 0.0431373)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ocsne"]
transparency = 1
shading_mode = 0
albedo_color = Color(0.54902, 0.8, 0.447059, 0.0392157)

[sub_resource type="ViewportTexture" id="ViewportTexture_5rnbb"]
viewport_path = NodePath("Wall/SubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_5jb4p"]
resource_local_to_scene = true
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
albedo_color = Color(1, 1, 0.94902, 1)
albedo_texture = SubResource("ViewportTexture_5rnbb")

[sub_resource type="PlaneMesh" id="PlaneMesh_ean7k"]

[sub_resource type="AtlasTexture" id="AtlasTexture_aeni1"]
atlas = ExtResource("7_bux2g")
region = Rect2(0, 0, 526, 651)

[node name="Lane" type="Node3D"]
script = ExtResource("1_srndj")

[node name="Slot0" parent="." groups=["laneCardSlot"] instance=ExtResource("2_fpni3")]
transform = Transform3D(1.2, 0, 0, 0, -5.24537e-08, 1.2, 0, -1.2, -5.24537e-08, -1.75844, -0.0488393, -10.5106)
script = ExtResource("3_8wdlj")

[node name="Slot1" parent="." groups=["laneCardSlot"] instance=ExtResource("2_fpni3")]
transform = Transform3D(1.2, 0, 0, 0, -5.24537e-08, 1.2, 0, -1.2, -5.24537e-08, 2.32609, -0.0488393, -10.4412)
script = ExtResource("3_8wdlj")

[node name="Slot2" parent="." groups=["laneCardSlot"] instance=ExtResource("2_fpni3")]
transform = Transform3D(1.2, 0, 0, 0, -5.24537e-08, 1.2, 0, -1.2, -5.24537e-08, -5.71554, -0.0488393, -3.35077)
script = ExtResource("3_8wdlj")

[node name="Slot3" parent="." groups=["laneCardSlot"] instance=ExtResource("2_fpni3")]
transform = Transform3D(1.2, 0, 0, 0, -5.24537e-08, 1.2, 0, -1.2, -5.24537e-08, 6.13665, -0.0488393, -3.42447)
script = ExtResource("3_8wdlj")

[node name="Slot4" parent="." groups=["laneCardSlot"] instance=ExtResource("2_fpni3")]
transform = Transform3D(1.2, 0, 0, 0, -5.24537e-08, 1.2, 0, -1.2, -5.24537e-08, -1.79954, -0.0488393, 3.94239)
script = ExtResource("3_8wdlj")

[node name="Slot5" parent="." groups=["laneCardSlot"] instance=ExtResource("2_fpni3")]
transform = Transform3D(1.2, 0, 0, 0, -5.24537e-08, 1.2, 0, -1.2, -5.24537e-08, 2.14135, -0.0488393, 3.94239)
script = ExtResource("3_8wdlj")

[node name="Location" parent="." instance=ExtResource("5_k5ip2")]
transform = Transform3D(0.794289, 0, 0, 0, 0.735, 0, 0, 0, 0.735, 0, 0, -2.57406)

[node name="PlayAreaLit" type="MeshInstance3D" parent="."]
transform = Transform3D(2.402, 0, 0, 0, -1.95766e-07, 0.029, 0, -4.4786, -1.26763e-09, 0, 0.020805, -3.83536)
visible = false
material_override = SubResource("StandardMaterial3D_aippo")
mesh = SubResource("ArrayMesh_208t2")
skeleton = NodePath("")

[node name="PlayAreaUnlit" type="MeshInstance3D" parent="."]
transform = Transform3D(2.402, 0, 0, 0, -1.95766e-07, 0.029, 0, -4.4786, -1.26763e-09, 0, 0.020805, -3.83536)
visible = false
material_override = SubResource("StandardMaterial3D_rvvo1")
mesh = SubResource("ArrayMesh_208t2")
skeleton = NodePath("")

[node name="PowerEnemy" type="Label3D" parent="."]
transform = Transform3D(0.975, 0, 0, 0, -4.37114e-08, 1.02, 0, -1, -4.45856e-08, 0, 0.117, -7.96755)
texture_filter = 1
modulate = Color(1, 0.368627, 0.301961, 1)
text = "10"
font = ExtResource("5_isyxg")
font_size = 147

[node name="PowerBgEnemy" type="MeshInstance3D" parent="."]
transform = Transform3D(1.4, -7.54979e-10, 1.06549e-14, -2.11394e-07, -0.005, -2.33146e-14, -1.06549e-14, -8.32667e-17, 1.4, -0.00644016, 0.104419, -8.08066)
material_override = SubResource("StandardMaterial3D_1toma")
cast_shadow = 0
mesh = SubResource("CylinderMesh_oe2rb")
skeleton = NodePath("../PowerEnemy")

[node name="EnemyParticle" type="CPUParticles3D" parent="PowerBgEnemy"]
transform = Transform3D(0.559, -4.14384e-08, -7.28353e-15, 0, 2.07192e-08, -1.906, -4.88693e-08, -0.474, -8.33139e-08, 0.00644016, 3.52424, 0.00527)
visible = false
material_override = SubResource("ShaderMaterial_4js5s")
amount = 1
lifetime = 3.0
local_coords = true
mesh = SubResource("QuadMesh_r6kdk")
gravity = Vector3(0, 0, 0)
color = Color(1, 0, 0, 1)
color_ramp = SubResource("Gradient_e8r5r")

[node name="EnemyParticleW" type="CPUParticles3D" parent="PowerBgEnemy"]
transform = Transform3D(0.532594, -0.365197, -7.39125e-08, 2.12053e-12, 2.45802e-08, -2.604, -0.454764, -0.427697, -8.65619e-08, 0.0149501, 3.52424, -0.0790191)
visible = false
material_override = SubResource("ShaderMaterial_4js5s")
amount = 1
lifetime = 3.0
local_coords = true
mesh = SubResource("QuadMesh_r6kdk")
gravity = Vector3(0, 0, 0)
color = Color(1, 0, 0, 1)
color_ramp = SubResource("Gradient_e8r5r")

[node name="PowerPl" type="Label3D" parent="."]
transform = Transform3D(0.975, 0, 0, 0, -4.37114e-08, 1.02, 0, -1, -4.45856e-08, 0, 0.117, 2.40395)
texture_filter = 1
modulate = Color(0.513726, 1, 0.482353, 1)
text = "0"
font = ExtResource("5_isyxg")
font_size = 147

[node name="PowerBgPlayer" type="MeshInstance3D" parent="."]
transform = Transform3D(1.4, -7.54979e-10, 1.06549e-14, -2.11394e-07, -0.005, -2.33146e-14, -1.06549e-14, -8.32667e-17, 1.4, -0.00644016, 0.104419, 2.32895)
material_override = SubResource("StandardMaterial3D_1toma")
cast_shadow = 0
mesh = SubResource("CylinderMesh_oe2rb")
skeleton = NodePath("../PowerEnemy")

[node name="PlayerParticle" type="CPUParticles3D" parent="PowerBgPlayer"]
transform = Transform3D(0.659, -4.31869e-08, -7.10543e-15, 3.63798e-12, 2.15934e-08, -1.906, -5.76116e-08, -0.494, -8.33139e-08, 0.00644016, 3.52424, -0.0327599)
visible = false
material_override = SubResource("ShaderMaterial_4js5s")
amount = 1
lifetime = 3.0
local_coords = true
mesh = SubResource("QuadMesh_1asx3")
gravity = Vector3(0, 0, 0)
color = Color(0, 0.839216, 0.0823529, 0.156863)
color_ramp = SubResource("Gradient_eenqv")

[node name="PlayerParticleW" type="CPUParticles3D" parent="PowerBgPlayer"]
transform = Transform3D(-0.449811, -0.394566, -7.17517e-08, -8.6659e-12, 2.35869e-08, -2.245, -0.482109, 0.368132, 6.69447e-08, -0.0351152, 3.52424, 0.0403686)
visible = false
material_override = SubResource("ShaderMaterial_4js5s")
amount = 1
lifetime = 3.0
local_coords = true
mesh = SubResource("QuadMesh_1asx3")
gravity = Vector3(0, 0, 0)
color = Color(0, 0.839216, 0.0823529, 1)
color_ramp = SubResource("Gradient_eenqv")

[node name="PreviewPosition" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, -1.02098, -0.0257653, -6.64243)

[node name="Slot0P" type="Area3D" parent="PreviewPosition"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.723585, 4.68285, -0.262316)

[node name="Slot0P" type="MeshInstance3D" parent="PreviewPosition/Slot0P"]
transform = Transform3D(0.97, 0, 0, 0, 1, 0, 0, 0, 0.029, -0.00300002, -1.191, 0.221)
layers = 4
material_override = SubResource("StandardMaterial3D_khy2i")
mesh = SubResource("ArrayMesh_hflnm")
skeleton = NodePath("")

[node name="CollisionShape3D" type="CollisionShape3D" parent="PreviewPosition/Slot0P"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00275171, -1.19068, 0.220828)
shape = SubResource("BoxShape3D_p0k37")

[node name="Slot1P" type="Area3D" parent="PreviewPosition"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3.34543, 1.73764, -1.67638e-07)

[node name="Slot1P" type="MeshInstance3D" parent="PreviewPosition/Slot1P"]
transform = Transform3D(0.97, 0, 0, 0, 1, 0, 0, 0, 0.029, -0.00300002, 1.685, -0.002)
layers = 4
material_override = SubResource("StandardMaterial3D_r73ri")
mesh = SubResource("ArrayMesh_hflnm")
skeleton = NodePath("")

[node name="CollisionShape3D" type="CollisionShape3D" parent="PreviewPosition/Slot1P"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00275171, 1.68514, -0.00243875)
shape = SubResource("BoxShape3D_p0k37")

[node name="Slot2P" type="Area3D" parent="PreviewPosition"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4.5155, -2.99974, -1.3411e-07)

[node name="Slot2P" type="MeshInstance3D" parent="PreviewPosition/Slot2P"]
transform = Transform3D(0.97, 0, 0, 0, 1, 0, 0, 0, 0.029, -0.129893, -0.492332, -0.002)
layers = 4
material_override = SubResource("StandardMaterial3D_3k3s5")
mesh = SubResource("ArrayMesh_hflnm")
skeleton = NodePath("")

[node name="CollisionShape3D" type="CollisionShape3D" parent="PreviewPosition/Slot2P"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.129644, -0.492774, -0.00243882)
shape = SubResource("BoxShape3D_p0k37")

[node name="Slot3P" type="Area3D" parent="PreviewPosition"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 7.07267, -2.92604, -3.85568e-07)

[node name="Slot3P" type="MeshInstance3D" parent="PreviewPosition/Slot3P"]
transform = Transform3D(0.97, 0, 0, 0, 1, 0, 0, 0, 0.029, 0.0698483, -0.492332, -0.002)
layers = 4
material_override = SubResource("StandardMaterial3D_nymiv")
mesh = SubResource("ArrayMesh_hflnm")
skeleton = NodePath("")

[node name="CollisionShape3D" type="CollisionShape3D" parent="PreviewPosition/Slot3P"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0700965, -0.492774, -0.00243881)
shape = SubResource("BoxShape3D_p0k37")

[node name="Slot4P" type="Area3D" parent="PreviewPosition"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.764679, -11.0137, -4.91738e-07)

[node name="Slot4P" type="MeshInstance3D" parent="PreviewPosition/Slot4P"]
transform = Transform3D(0.97, 0, 0, 0, 1, 0, 0, 0, 0.029, -0.00300002, 0.212357, -0.002)
layers = 4
material_override = SubResource("StandardMaterial3D_81oja")
mesh = SubResource("ArrayMesh_hflnm")
skeleton = NodePath("")

[node name="CollisionShape3D" type="CollisionShape3D" parent="PreviewPosition/Slot4P"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00275171, 0.212738, -0.00243882)
shape = SubResource("BoxShape3D_p0k37")

[node name="Slot5P" type="Area3D" parent="PreviewPosition"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3.16069, -11.0137, -7.30157e-07)

[node name="Slot5P" type="MeshInstance3D" parent="PreviewPosition/Slot5P"]
transform = Transform3D(0.97, 0, 0, 0, 1, 0, 0, 0, 0.029, -0.00300002, 0.212357, -0.002)
layers = 4
material_override = SubResource("StandardMaterial3D_mubot")
mesh = SubResource("ArrayMesh_hflnm")
skeleton = NodePath("")

[node name="CollisionShape3D" type="CollisionShape3D" parent="PreviewPosition/Slot5P"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00275171, 0.212738, -0.00243882)
shape = SubResource("BoxShape3D_p0k37")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
transform = Transform3D(1.066, 0, 0, 0, 0.160407, 0, 0, 0, 1.063, 0, 0.0580999, -7.89485)
visible = false
material_override = SubResource("StandardMaterial3D_rwyeo")
mesh = SubResource("BoxMesh_1iiqr")

[node name="MeshInstance3D3" type="MeshInstance3D" parent="."]
transform = Transform3D(24.4058, 0, 0, 0, 0.160407, 0, 0, 0, 39.0232, 0, 0, -7.895)
visible = false
material_override = SubResource("StandardMaterial3D_mrf03")
mesh = SubResource("BoxMesh_1iiqr")

[node name="MeshInstance3D2" type="MeshInstance3D" parent="."]
transform = Transform3D(1.066, 0, 0, 0, 0.160407, 0, 0, 0, 1.063, 0, 0.0580999, 1.20637)
visible = false
material_override = SubResource("StandardMaterial3D_ocsne")
mesh = SubResource("BoxMesh_1iiqr")

[node name="Wall" type="MeshInstance3D" parent="."]
transform = Transform3D(29.127, 0, 0, 0, 2.004, 0, 0, 0, 16.528, 21.297, -0.49297, 3.803)
material_override = SubResource("StandardMaterial3D_5jb4p")
cast_shadow = 0
mesh = SubResource("PlaneMesh_ean7k")
skeleton = NodePath("../..")
metadata/_edit_lock_ = true

[node name="SubViewport" type="SubViewport" parent="Wall"]
transparent_bg = true
size = Vector2i(1920, 1080)

[node name="TextureRect" type="TextureRect" parent="Wall/SubViewport"]
offset_right = 254.0
offset_bottom = 188.0
texture = SubResource("AtlasTexture_aeni1")

[connection signal="input_event" from="PreviewPosition/Slot0P" to="." method="_on_slot_0p_input_event"]
[connection signal="mouse_entered" from="PreviewPosition/Slot0P" to="." method="_on_slot_0p_mouse_entered"]
[connection signal="mouse_exited" from="PreviewPosition/Slot0P" to="." method="_on_slot_0p_mouse_exited"]
[connection signal="input_event" from="PreviewPosition/Slot1P" to="." method="_on_slot_1p_input_event"]
[connection signal="mouse_entered" from="PreviewPosition/Slot1P" to="." method="_on_slot_1p_mouse_entered"]
[connection signal="mouse_exited" from="PreviewPosition/Slot1P" to="." method="_on_slot_1p_mouse_exited"]
[connection signal="input_event" from="PreviewPosition/Slot2P" to="." method="_on_slot_2p_input_event"]
[connection signal="mouse_entered" from="PreviewPosition/Slot2P" to="." method="_on_slot_2p_mouse_entered"]
[connection signal="mouse_exited" from="PreviewPosition/Slot2P" to="." method="_on_slot_2p_mouse_exited"]
[connection signal="input_event" from="PreviewPosition/Slot3P" to="." method="_on_slot_3p_input_event"]
[connection signal="mouse_entered" from="PreviewPosition/Slot3P" to="." method="_on_slot_3p_mouse_entered"]
[connection signal="mouse_exited" from="PreviewPosition/Slot3P" to="." method="_on_slot_3p_mouse_exited"]
[connection signal="input_event" from="PreviewPosition/Slot4P" to="." method="_on_slot_4p_input_event"]
[connection signal="mouse_entered" from="PreviewPosition/Slot4P" to="." method="_on_slot_4p_mouse_entered"]
[connection signal="mouse_exited" from="PreviewPosition/Slot4P" to="." method="_on_slot_4p_mouse_exited"]
[connection signal="input_event" from="PreviewPosition/Slot5P" to="." method="_on_slot_5p_input_event"]
[connection signal="mouse_entered" from="PreviewPosition/Slot5P" to="." method="_on_slot_5p_mouse_entered"]
[connection signal="mouse_exited" from="PreviewPosition/Slot5P" to="." method="_on_slot_5p_mouse_exited"]
