extends Node3D

var selectedHandIndex = 0

@onready var arena = $Arena
@onready var playerMana = $UI/ManaPlayer/ManaBG/Label
@onready var playerTempMana = $UI/ManaPlayer/TempManaBG/TempLabel
@onready var playerTempManaBG = $UI/ManaPlayer/TempManaBG
@onready var enemyMana = $UI/ManaEnemy/ManaBG/Label
@onready var enemyTempMana = $UI/ManaEnemy/TempManaBG/TempLabel
@onready var enemyTempManaBG = $UI/ManaEnemy/TempManaBG
@onready var snapALabel = $UI/Score/ScoreSnap/ScoreA
@onready var snapBLabel = $UI/Score/ScoreSnap/ScoreB
@onready var snapDrawLabel = $UI/Score/ScoreSnap/ScoreDraw
@onready var qbALabel = $UI/Score/ScoreQB/ScoreA
@onready var qbBLabel = $UI/Score/ScoreQB/ScoreB
@onready var qbDrawLabel = $UI/Score/ScoreQB/ScoreDraw
@onready var sumALabel = $UI/Score/ScoreSum/ScoreA
@onready var sumBLabel = $UI/Score/ScoreSum/ScoreB
@onready var sumDrawLabel = $UI/Score/ScoreSum/ScoreDraw
@onready var alert = $UI/Alert
@onready var alertLabel = $UI/Alert/Label
@onready var endTurnButton = %EndButtonY
@onready var endTurnButtonGreen = %EndButtonG
@onready var inspectBg = $UI/InspectBg
@onready var inspectUI = $UI/InspectUI
#@onready var debugButton = $UI/DebugButton
@onready var speedButton = $UI/SpeedButton
@onready var eventLog = $UI/EventLogBg
@onready var eventLogLabel = $UI/EventLogBg/LogScroll/EventLog
@onready var opponentDialog = $UI/OpponentDialog
@onready var opponentDialogLabel = $UI/OpponentDialog/Label
@onready var playerDialog = $UI/PlayerDialog
@onready var playerDialogLabel = $UI/PlayerDialog/Label
@onready var camera = $Camera
@onready var cameraInspect = $UI/InspectUI/SubViewportInspect/CameraInspect
@onready var cardAboveUI = $UI/CardAboveUI
@onready var playerPfp = $UI/PlayerPfp
@onready var enemyPfp = $UI/EnemyPfp
@onready var panelContainer: PanelContainer = $UI/PanelContainer
@onready var card_3dui = load("res://card3D.tscn")
@onready var howToPlayUI = load("res://howtoplay.tscn")
@onready var deckListPanel: Panel = $UI/DeckListPanel
@onready var graveyardListPanelMP: Panel = $UI/GraveyardListPanelMP
@onready var graveyardListPanelOP: Panel = $UI/GraveyardListPanelOP
@onready var option_screen: Control = $UI/OptionScreen
@onready var fps: Label = $FPS


var gStarterDeckPic = load("res://Assets/Menu2/Deck Green Starter.png")
var rStarterDeckPic = load("res://Assets/Menu2/Deck Red Starter.png")
var greenDeckPic = load("res://Assets/Menu2/Deck Green.png")
var redDeckPic = load("res://Assets/Menu2/Deck Red.png")
var pyragonPic = load("res://Assets/Menu2/Deck Dragon.png")

var is_inspecting = false

signal inspecting(bool)
signal inspecting_location(location)

# Called when the node enters the scene tree. Initializes UI elements, connects signals,
# and sets up player/enemy profiles and deck images
func _ready() -> void:
	#get_viewport().size = DisplayServer.screen_get_size()
	Const.MODE = 1
	Const.AWAIT_TIME = 1
	Const.INSPECT_TIME = 0.13
	Const.ANIM_SPEED = 8
	var playerDeck = DeckResource.MainDeck
	var enemyDeck = DeckResource.OppoDeck
	var playerName = $UI/ProfilePlayer.get_child(2)
	var enemyName = $UI/ProfileEnemy.get_child(2)
	cardAboveUI.get_child(0).size = get_viewport().size
	inspectUI.get_child(0).size = get_viewport().size
	enemyName.text = DeckResource.getDeckName(DeckResource.OppoDeck)
	playerName.text = MiscService.PlayerName
	match playerDeck:
		DeckResource.GREEN_BLUE:
			self.playerPfp.texture = gStarterDeckPic
		DeckResource.RED_BLUE:
			self.playerPfp.texture = rStarterDeckPic
		DeckResource.GREEN:
			self.playerPfp.texture = greenDeckPic
		DeckResource.RED:
			self.playerPfp.texture = redDeckPic
		DeckResource.GREEN_RED:
			self.playerPfp.texture = pyragonPic
	match enemyDeck:
		DeckResource.GREEN_BLUE:
			self.enemyPfp.texture = gStarterDeckPic
		DeckResource.RED_BLUE:
			self.enemyPfp.texture = rStarterDeckPic
		DeckResource.GREEN:
			self.enemyPfp.texture = greenDeckPic
		DeckResource.RED:
			self.enemyPfp.texture = redDeckPic
		DeckResource.GREEN_RED:
			self.enemyPfp.texture = pyragonPic
	
	self.alert.modulate.a = 0
	self.arena.playerManaChanged.connect(self.onPlayerManaChanged)
	self.arena.alertMessage.connect(self.onAlertMessage)
	self.arena.changeEndTurnButton.connect(self.onEndTurnButtonChanged)
	self.arena.updateLog.connect(self.refreshLog)
	self.arena.abilityStack.updateLog.connect(self.refreshLog)
	self.arena.opponentDialog.connect(self.showOpponentDialog)
	self.arena.playerDialog.connect(self.showPlayerDialog)
	self.inspecting_location.connect(self.inspect_location)
	self.snapALabel.text = str(ScoreService.snapScore[0])
	self.snapBLabel.text = str(ScoreService.snapScore[1])
	self.snapDrawLabel.text = str(ScoreService.snapScore[2])
	self.qbALabel.text = str(ScoreService.qbScore[0])
	self.qbBLabel.text = str(ScoreService.qbScore[1])
	self.qbDrawLabel.text = str(ScoreService.qbScore[2])
	self.sumALabel.text = str(ScoreService.sumScore[0])
	self.sumBLabel.text = str(ScoreService.sumScore[1])
	self.sumDrawLabel.text = str(ScoreService.sumScore[2])
	self.cardAboveUI.visible = true
	syncSpeedButton()

#func _physics_process(delta):
	#Engine.max_fps = 6
	#fps.text = "Current FPS: " + str(Engine.get_frames_per_second())


# Updates the speed button text based on the current AWAIT_TIME setting
func syncSpeedButton() -> void:
	if Const.AWAIT_TIME == 2:
		speedButton.text = "Speed 1x"
	elif Const.AWAIT_TIME == 1:
		speedButton.text = "Speed 2x"
	elif Const.AWAIT_TIME == 0:
		speedButton.text = "Instant"


# Updates mana display when a player's mana changes
func onPlayerManaChanged(_player:Player) -> void:
	if _player.isOnMainSide():
		playerMana.text = str(_player.getMana()) + "/" + str(_player.getMaxMana())
		if _player.getDiffMaxMana() > 0:
			playerMana.modulate = Color.GREEN_YELLOW
		elif _player.getDiffMaxMana() < 0:
			playerMana.modulate = Color.LIGHT_CORAL
		
		if _player.getTempMana() == 0:
			playerTempManaBG.visible = false
		else:
			playerTempManaBG.visible = true
		playerTempMana.text = str(_player.getTempMana())
	else:
		enemyMana.text = str(_player.getMana()) + "/" + str(_player.getMaxMana())
		if _player.getDiffMaxMana() > 0:
			enemyMana.modulate = Color.GREEN_YELLOW
		elif _player.getDiffMaxMana() < 0:
			enemyMana.modulate = Color.LIGHT_CORAL
		
		if _player.getTempMana() == 0:
			enemyTempManaBG.visible = false
		else:
			enemyTempManaBG.visible = true
		enemyTempMana.text = str(_player.getTempMana())


func onAlertMessage(_message:String) -> void:
	self.alert.modulate.a = 1
	self.alertLabel.text = "[center]" + _message + "[/center]"
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	self.alert.modulate.a = 0


# Displays a dialog message for the opponent with a timeout
func showOpponentDialog(_message:String) -> void:
	self.opponentDialog.visible = true
	self.opponentDialogLabel.text = "[center]" + _message + "[/center]"
	await get_tree().create_timer(Const.DIALOG_AWAIT_TIME).timeout
	self.opponentDialog.visible = false


# Displays a dialog message for the player with a timeout
func showPlayerDialog(_message:String) -> void:
	self.playerDialog.visible = true
	self.playerDialogLabel.text = "[center]" + _message + "[/center]"
	await get_tree().create_timer(Const.DIALOG_AWAIT_TIME).timeout
	self.playerDialog.visible = false

# Handles keyboard input for debug/testing purposes
# D: Draw card, S: Shuffle deck, M: Mulligan, R: Game over test
func _input(event):
	if event is InputEventKey && event.pressed && !event.echo:
		var keyPressed = event.as_text_key_label()
		match keyPressed:
			"D": self.arena.drawEvent(1) # Test active player draw
			"S": self.arena.shuffleEvent() # Test active player shuffle deck
			"M": self.arena.mulliganEvent()
			"R": gameOver([1, 1, 1])

# Handles game over state, updates scores and reloads the scene
func gameOver(winner:Array[int]) -> void:
	ScoreService.snapScore[winner[0]] += 1
	ScoreService.qbScore[winner[1]] += 1
	ScoreService.sumScore[winner[2]] += 1
	ScoreService.mode = Mode.mode
	EventLogService.resetLog()
	get_tree().reload_current_scene()
	#if ScoreService.mode == Mode.BOT_VS_BOT:
		#_on_bot_vs_bot_button_toggled(true)

# Enables/disables the end turn buttons based on game state
func onEndTurnButtonChanged(_disabled:bool) -> void:
	self.endTurnButton.disabled = _disabled
	self.endTurnButtonGreen.disabled = _disabled
	
	if _disabled:
		self.endTurnButtonGreen.visible = false

# Updates the event log display with current events
func refreshLog():
	self.eventLogLabel.text = EventLogService.getEventLogs()

# Returns to menu scene
func _on_back_button_pressed() -> void:
	#Const.AWAIT_TIME = 2
	Mode.mode = Mode.MAN_VS_BOT 
	EventLogService.resetLog()
	get_tree().change_scene_to_file("res://menu.tscn")

# Exits the game
func _on_quit_button_pressed() -> void:
	get_tree().quit()

# Triggers end turn event
func _on_end_button_2_pressed():
	await self.arena.endTurnEvent()

# Debug function to print current global effects
func _on_debug_button_pressed():
	GlobalEffectService.printCurrentGlobalEffect()

# Toggles event log visibility and updates its content
func _on_event_log_button_pressed():
	EventLogService.printEventLogs()
	self.eventLogLabel.text = EventLogService.getEventLogs()
	self.eventLog.visible = !self.eventLog.visible

# Updates the event log display
func _on_refresh_log_button_pressed():
	refreshLog()

# Triggers game over with current arena winner
func _on_restart_button_pressed() -> void:
	gameOver(self.arena.getArenaWinner())

# Hides victory popup
func _on_view_arena_button_pressed():
	$UI/VictoryPopup.visible = false

# Switches to bot vs bot mode and triggers end turn
func _on_bot_vs_bot_button_pressed() -> void:
	#Const.AWAIT_TIME = 0
	Mode.mode = Mode.BOT_VS_BOT
	self.arena.endTurnEvent()

# Switches to manual vs bot mode
func _on_bot_vs_man_button_pressed() -> void:
	#Const.AWAIT_TIME = 2
	Mode.mode = Mode.MAN_VS_BOT 

# Shows how to play UI
func _on_how_to_play_pressed() -> void:
	var howToPlayNode = howToPlayUI.instantiate()
	howToPlayNode.onHowToPlayClose.connect(_on_how_to_play_closed)
	#inspectBg.visible = true
	#inspectUI.visible = true
	$UI.add_child(howToPlayNode)

# Cleans up how to play UI when closed
func _on_how_to_play_closed(howToPlayNode) -> void:
	# print("HTP CLOSE")
	#inspectBg.visible = false
	#inspectUI.visible = false
	howToPlayNode.queue_free()

# Cycles through game speed settings (2x, 1x, Instant)
func _on_speed_button_click():
	if Const.AWAIT_TIME == 2:
		Const.AWAIT_TIME = 1
	elif Const.AWAIT_TIME == 1:
		Const.AWAIT_TIME = 0
	elif Const.AWAIT_TIME == 0:
		Const.AWAIT_TIME = 2
	syncSpeedButton()

# Handles card inspection logic for viewing card details
func inspect_card(inspecting_card) -> void:
	if inspecting_card.get_parent() is Graveyard: return
	#return
	#if is_inspecting:
		#await get_tree().create_timer(Const.INSPECT_TIME).timeout
		#return
	#
	#await get_tree().create_timer(Const.INSPECT_TIME).timeout
	if !inspecting_card.isOnHand:
		start_inspecting(inspecting_card)
		return
	await get_tree().create_timer(Const.INSPECT_TIME).timeout
	if inspecting_card.get_parent().get_parent().name == "Enemy":
		inspecting_card.currentState = inspecting_card.states.HOVER
		return
	if !Input.is_action_pressed("clicked"):  
		start_inspecting(inspecting_card)

# Stops card inspection and cleans up inspection UI
func start_inspecting(inspecting_card : Card_3DUI):
	cardAboveUI.get_child(0).size = panelContainer.size
	inspectUI.get_child(0).size = panelContainer.size
	var destination := global_transform
	destination.basis = cameraInspect.global_transform.basis
	var unprojected = cameraInspect.unproject_position(destination.origin)
	# I fiddled with the y coordinate and distance here so the full card is visible
	destination.origin = cameraInspect.project_position(Vector2(panelContainer.size.x/2, panelContainer.size.y/2.4), 1.7)
	#destination.origin.x = 1#get_viewport().size.x /2
	#destination.origin.y = 1#get_viewport().size.y /2
	#destination.origin += cameraInspect.basis * Vector3.UP * 7.4
	#destination.origin += cameraInspect.basis * Vector3.BACK * 13.4
	#destination.origin += cameraInspect.basis * Vector3.LEFT * 0.65
	#var cache_card = card_3dui.instantiate()
	#self.add_child(cache_card)
	#cache_card.displayCard(arena.getCardByHandIndex(inspecting_card.get_index()))
	#cache_card.target_transform.origin = inspecting_card.target_transform.origin
	#cache_card.target_transform.basis = inspecting_card.target_transform.basis
	var card_dupe = inspecting_card.duplicate()
	inspectBg.visible = true
	inspectUI.visible = true
	inspectUI.get_child(0).add_child(card_dupe)
	#card_dupe.cardBorder.
	card_dupe.set_flipup(true)
	card_dupe.collision.disabled = true
	card_dupe.collisionHand.disabled = true
	card_dupe.abilityFloat.visible = false
	#card_dupe.abilityDescription.visible = false
	card_dupe.abilityDisplay.visible = true
	card_dupe.keywordNotes.visible = true
	card_dupe.cardBg.visible = true
	card_dupe.colorBar.visible = true
	card_dupe.cardPicGradient.visible = true
	card_dupe.playerParticle.visible = false
	card_dupe.enemyParticle.visible = false
	#card_dupe.scale = Vector3.ONE*10
	card_dupe.cost_visible(true)
	card_dupe.set_layer(1, false)
	card_dupe.set_layer(20, false)
	card_dupe.set_layer(19, true)
	#Prevent changes on inspecting_card material when card_dupe.britghrness_up(true) due to .duplicate()
	card_dupe.cardArt.material_override = card_dupe.cardArt.material_override.duplicate()
	#card_dupe.costBg.material_override = card_dupe.costBg.material_override.duplicate()
	#card_dupe.powerBg.material_override = card_dupe.powerBg.material_override.duplicate()
	card_dupe.cardBorder.material_override = card_dupe.cardBorder.material_override.duplicate()
	#card_dupe.cardName.outline_modulate = card_dupe.cardName.outline_modulate.duplicate()
	card_dupe.brightness_on = inspecting_card.brightness_on
	card_dupe.brightness_up(false)
	#card_dupe.global_position = inspectSlot.global_position
	#card_dupe.rotation.z = 0
	#card_dupe.target_transform.basis.rotated(Vector3.RIGHT, deg_to_rad(90))
	#card_dupe.scale = Vector3.ONE
	card_dupe.target_transform.origin = destination.origin
	card_dupe.target_transform.basis = destination.basis
	card_dupe.currentState = card_dupe.states.INSPECT
	inspecting_card.currentState = inspecting_card.states.IDLE
	inspecting.emit(true)
	await get_tree().create_timer(Const.AWAIT_TIME * 0.13).timeout
	is_inspecting = true

func abort_inspecting(inspecting_component):
	if is_inspecting:
		inspectBg.visible = false
		inspectUI.visible = false
		inspecting_component.queue_free()
		await get_tree().create_timer(Const.AWAIT_TIME * 0.15).timeout
		inspecting.emit(false)
		is_inspecting = false

# Handles location inspection, positioning the camera and UI elements
func inspect_location(location : Location_3DUI):
	cardAboveUI.get_child(0).size = panelContainer.size
	inspectUI.get_child(0).size = panelContainer.size
	var destination := global_transform
	destination.basis = cameraInspect.global_transform.basis
	var unprojected = cameraInspect.unproject_position(destination.origin)
	# I fiddled with the y coordinate and distance here so the full card is visible
	destination.origin = cameraInspect.project_position(Vector2(panelContainer.size.x/2, panelContainer.size.y/2.1), 9)
	#var destination := global_transform
	#destination.basis = cameraInspect.global_transform.basis
	#destination.origin += cameraInspect.basis * Vector3.UP * 7.2
	#destination.origin += cameraInspect.basis * Vector3.BACK * 9
	#destination.origin += cameraInspect.basis * Vector3.LEFT * 0.65
	#var cache_card = card_3dui.instantiate()
	#self.add_child(cache_card)
	#cache_card.displayCard(arena.getCardByHandIndex(inspecting_card.get_index()))
	#cache_card.target_transform.origin = inspecting_card.target_transform.origin
	#cache_card.target_transform.basis = inspecting_card.target_transform.basis
	var location_dupe: Location_3DUI = location.duplicate()
	inspectBg.visible = true
	inspectUI.visible = true
	inspectUI.get_child(0).add_child(location_dupe)
	#card_dupe.cardBorder.
	#location.collision.disabled = true
	location_dupe.inspecting = true
	location_dupe.set_layer(1, false)
	location_dupe.set_layer(20, false)
	location_dupe.set_layer(19, true)
	location_dupe.keywordNotes.visible = true
	location_dupe.eventModel.visible = false
	location_dupe.collision.disabled = true
	#location_dupe._on_area_3d_mouse_exited()
	#Prevent changes on inspecting_card material when card_dupe.britghrness_up(true) due to .duplicate()
	location_dupe.locationTexture.material_override = location_dupe.locationTexture.material_override.duplicate()
	#card_dupe.brightness_up(true)
	#card_dupe.global_position = inspectSlot.global_position
	#card_dupe.rotation.z = 0
	#card_dupe.target_transform.basis.rotated(Vector3.RIGHT, deg_to_rad(90))
	#card_dupe.scale = Vector3.ONE
	location_dupe.target_transform.origin = destination.origin
	location_dupe.target_transform.basis = destination.basis
	location_dupe.target_transform.basis = location_dupe.target_transform.basis.rotated(Vector3.RIGHT, deg_to_rad(90))
	inspecting.emit(true)
	await get_tree().create_timer(Const.AWAIT_TIME * 0.13).timeout
	is_inspecting = true

# Handles input events for game over screen
func _on_area_2d_input_event_game_over(viewport: Node, event: InputEvent, shape_idx: int) -> void:
	if event is InputEventMouseButton and event.is_action_pressed("clicked"):
		find_child("VictoryPopup").visible = false



func _on_area_2d_settings_input_event(viewport: Node, event: InputEvent, shape_idx: int) -> void:
	if event is InputEventMouseButton and event.is_action_pressed("clicked"):
		option_screen.visible = true
