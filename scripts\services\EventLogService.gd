class_name EventLogService

enum EventType {
	CAST_CARD,
	CARD_EFFECT,
	CARD_DIE,
	END_TURN
}

static var eventLogs = []

static func resetLog() -> void:
	eventLogs = []


static func LogEvent(event:Dictionary) -> void:
	var currentTurn = TurnService.TurnCount
	if eventLogs.size() <= currentTurn:
		for i in range(eventLogs.size(), currentTurn + 1, 1):
			eventLogs.append([])
	eventLogs[currentTurn].append(event)


static func LogCastCardEvent(_caster:Player, _card:Card) -> void:
	LogEvent({
		"type": EventType.CAST_CARD,
		"caster": _caster,
		"card": _card
	})


static func LogCardEffectEvent(_triggerCard:Card, _effectKeyword:int, _targetCards:Array[Card] = [], _effectTier = null) -> void:
	LogEvent({
		"type": EventType.CARD_EFFECT,
		"triggerCard": _triggerCard,
		"targetCards": _targetCards,
		"keyword": _effectKeyword,
		"tier": _effectTier
	})


static func LogCardDieEvent(_card:Card) -> void:
	LogEvent({
		"type": EventType.CARD_DIE,
		"card": _card
	})


static func LogEndTurnEvent(_player:Player) -> void:
	LogEvent({
		"type": EventType.END_TURN,
		"player": _player
	})


static func printEventLogs() -> void:
	print("== EVENT LOGS ==")
	for turn in eventLogs.size():
		if turn == 0: continue
		print("Turn " + str(turn))
		for event in eventLogs[turn]:
			match event["type"]:
				EventType.CAST_CARD:
					print(" - " + event["caster"].getName() + " casts " + event["card"].getName() + ".")
				EventType.CARD_EFFECT:
					var targetCardsList = getNameByCardList(event["targetCards"])
					var tier = ""
					if event["tier"]:
						tier += " for " + str(event["tier"])
					print(" - " + event["triggerCard"].getName() + " " + Keyword.KeywordInfo[event["keyword"]]["label"] + " " + targetCardsList + tier + ".")
				EventType.CARD_DIE:
					print(" - " + event["card"].getName() + " dies.")
				EventType.END_TURN:
					print(" - " + event["player"].getName() + " ends turn.")


static func getEventLogs() -> String:
	var logs:String = ""
	logs += "== EVENT LOGS ==\n"
	for turn in eventLogs.size():
		if turn == 0: continue
		logs += "\nTurn " + str(ceil(float(turn) / 2.0)) + "\n"
		for event in eventLogs[turn]:
			logs += translateLog(event)
	return logs

static func translateLog(event) -> String:
	var log = ""
	match event["type"]:
		EventType.CAST_CARD:
			log += " - " + getLogColorByPlayer(event["caster"]) + event["caster"].getName() + "[/color] casts " + getLogColorByPlayer(event["card"].getOwner()) + event["card"].getName() + "[/color]\n"
		EventType.CARD_EFFECT:
			var targetCardsList = getNameByCardList(event["targetCards"])
			var tier = ""
			if event["tier"]:
				tier += " for " + str(event["tier"])
			log += " - " + getLogColorByPlayer(event["triggerCard"].getOwner()) + event["triggerCard"].getName() + "[/color] " + Keyword.KeywordInfo[event["keyword"]]["label"] + " " + targetCardsList + tier + "." + "\n"
		EventType.CARD_DIE:
			log += " - " + getLogColorByPlayer(event["card"].getOwner()) + event["card"].getName() + "[/color] dies" + "\n"
		EventType.END_TURN:
			log += " - " + getLogColorByPlayer(event["player"]) + event["player"].getName() + "[/color] ends turn" + "\n"
	return log


static func getNameByCardList(cards:Array[Card]) -> String:
	var targetCardsList = ""
	if cards.size() == 1:
		targetCardsList = getLogColorByPlayer(cards[0].getOwner()) + cards[0].getName() + "[/color]"
	elif cards.size() > 1:
		targetCardsList += "["
		for card in cards:
			targetCardsList += getLogColorByPlayer(card.getOwner()) + card.getName() + "[/color]"
			if card != cards[cards.size() - 1]: targetCardsList += ", "
		targetCardsList += "]"
	return targetCardsList


static func getLogColorByPlayer(_player:Player) -> String:
	if _player.isOnMainSide():
		return "[color=#00ff00]"
	else:
		return "[color=#ff0000]"
