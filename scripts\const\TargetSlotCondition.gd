class_name TargetSlotCondition
enum {
	NONE,
	RANDOM,
	ALL
}

static var labelDict = {
	NONE: "",
	RANDOM: "Rand",
	ALL: "All"
}

static func filterTargetSlotCondition(_slot:Array[LaneCardSlot], _target:TargetSlot) -> Array[LaneCardSlot]:
	var filteredSlots: Array[LaneCardSlot] = []
	match _target.getCondition():
		RANDOM:
			if not _slot.is_empty():
				filteredSlots.append(_slot[randi() % _slot.size()])
		ALL:
			filteredSlots = _slot
		_:
			filteredSlots = _slot
	return filteredSlots
