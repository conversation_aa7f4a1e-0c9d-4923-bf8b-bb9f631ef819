class_name CardCollectionUIController
extends Node3D

@onready var card_3dui = load("res://card3D.tscn")
@onready var battleNode = get_tree().get_root().get_node("Battle")

var cardCollection = CardCollection.new()

func _ready():
	pass
	
func getCardCollection() -> CardCollection:
	return self.cardCollection

func getOwner() -> Player:
	return self.cardCollection.getOwner()

func getOwnerName() -> String:
	return self.cardCollection.getOwnerName()

func getCards() -> Array[Card]:
	return self.cardCollection.getCards()

func getCardCount() -> int:
	return self.cardCollection.getCardCount()

func getCardAt(index: int) -> Card:
	return self.cardCollection.getCardAt(index)

func getTopCardPower() -> int:
	return self.cardCollection.getTopCardPower()
	
func setOwner(player: Player):
	self.cardCollection.setOwner(player)

func isEmpty() -> bool:
	return self.cardCollection.isEmpty()

func has(card: Card) -> bool:
	return self.cardCollection.has(card)

# add cards into this CardCollection
func add(p_cards: Array[Card]) -> void:
	cardCollection.add(p_cards)
	for card in p_cards:
		var card3d = card_3dui.instantiate()
		card3d.displayCard(card)
		add_child(card3d)
		var movingQueue
		if getOwner().isOnMainSide():
			movingQueue = battleNode.find_child("Player").movingQueue
		else:
			movingQueue = battleNode.find_child("Enemy").movingQueue
		if movingQueue.get_child_count() > 0:
			card3d.transform.origin = movingQueue.get_child(0).global_position
			card3d.rotation = movingQueue.get_child(0).rotation
			movingQueue.get_child(0).free()
		#if movingQueue.get_child_count() > 0:
			#card3d.transform.origin = movingQueue.get_child(0).global_position
			#movingQueue.get_child(0).queue_free()
		#else:
		card3d.cardSelected.connect(battleNode.inspect_card)
		battleNode.inspecting.connect(card3d.change_collision_mode)
		#await get_tree().create_timer(Const.AWAIT_TIME * 5).timeout

func addSingle(p_cards:Card) -> void:
	cardCollection.addSingle(p_cards)
	var card3d = card_3dui.instantiate()
	card3d.displayCard(p_cards)
	add_child(card3d)
	var movingQueue
	if getOwner().isOnMainSide():
		movingQueue = battleNode.find_child("Player").movingQueue
	else:
		movingQueue = battleNode.find_child("Enemy").movingQueue
	if movingQueue.get_child_count() > 0:
		card3d.transform.origin = movingQueue.get_child(0).global_position
		card3d.rotation = movingQueue.get_child(0).rotation
		movingQueue.get_child(0).free()
	#if movingQueue.get_child_count() > 0:
		#card3d.transform.origin = movingQueue.get_child(0).global_position
		#movingQueue.get_child(0).queue_free()
	#else:
	card3d.cardSelected.connect(battleNode.inspect_card)
	battleNode.inspecting.connect(card3d.change_collision_mode)
	#await get_tree().create_timer(Const.AWAIT_TIME * 5).timeout

# move cards to another CardCollection
func removeCards(p_cards: Array[Card]) -> Array[Card]:
	for card in p_cards:
		if self.cardCollection.has(card):
			var cardIndex = self.cardCollection.cards.find(card)
			var movingQueue
			if getOwner().isOnMainSide():
				movingQueue = battleNode.find_child("Player").movingQueue
			else:
				movingQueue = battleNode.find_child("Enemy").movingQueue
				#if get_child_count()>0:
			get_child(cardIndex).reparent(movingQueue)
	return self.cardCollection.removeCards(p_cards)

func removeSingle(p_cards: Card) -> Card:
	if self.cardCollection.has(p_cards):
		var cardIndex = self.cardCollection.cards.find(p_cards)
		var movingQueue
		if getOwner().isOnMainSide():
			movingQueue = battleNode.find_child("Player").movingQueue
		else:
			movingQueue = battleNode.find_child("Enemy").movingQueue
			#if get_child_count()>0:
		get_child(cardIndex).reparent(movingQueue)
	return self.cardCollection.removeSingle(p_cards)

# move top x cards to another CardCollection
func pull(amount: int) -> Array[Card]:
	for i in amount:
		if not self.cardCollection.isEmpty():
			var movingQueue
			if getOwner().isOnMainSide():
				movingQueue = battleNode.find_child("Player").movingQueue
			else:
				movingQueue = battleNode.find_child("Enemy").movingQueue
				#if get_child_count()>0:
			get_child(0).reparent(movingQueue)
				#remove_child(get_child(0))
	return self.cardCollection.pull(amount)

# remove card at index from this CardCollection
func removeAt(index: int) -> Card:
	var movingQueue
	if getOwner().isOnMainSide():
		movingQueue = battleNode.find_child("Player").movingQueue
	else:
		movingQueue = battleNode.find_child("Enemy").movingQueue
		#if get_child_count()>0:
	get_child(index).reparent(movingQueue)
	#remove_child(get_child(index))
	return self.cardCollection.removeAt(index)

# empty this CardCollection
func empty() -> Array[Card]:
	var movingQueue
	#print("empty")
	#print(getOwner().getName())
	if getOwner().isOnMainSide():
		movingQueue = battleNode.find_child("Player").movingQueue
	else:
		movingQueue = battleNode.find_child("Enemy").movingQueue
		#if get_child_count()>0:
	for n in get_children():
		n.reparent(movingQueue)
		#n.free()
	return self.cardCollection.empty()
	
func shuffle() -> void:
	self.cardCollection.shuffle()

func emptyMQ() -> void:
	var movingQueue
	if getOwner().isOnMainSide():
		movingQueue = battleNode.find_child("Player").movingQueue
	else:
		movingQueue = battleNode.find_child("Enemy").movingQueue
	
	for n in movingQueue.get_children():
		n.free()

