[gd_resource type="ParticleProcessMaterial" load_steps=5 format=3 uid="uid://cyxfa2tlqgn0k"]

[sub_resource type="Gradient" id="Gradient_wb2op"]
colors = PackedColorArray(1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_7l2i5"]
gradient = SubResource("Gradient_wb2op")

[sub_resource type="Curve" id="Curve_4ma6n"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.4, 1), 0.0, 0.0, 0, 0, Vector2(1, 0.8), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_og8y2"]
curve = SubResource("Curve_4ma6n")

[resource]
lifetime_randomness = 0.4
emission_shape = 1
emission_sphere_radius = 0.15
direction = Vector3(0, 0, -1)
spread = 20.0
initial_velocity_min = 1.0
initial_velocity_max = 2.0
gravity = Vector3(0, 1, 0)
scale_min = 0.8
scale_max = 1.2
scale_curve = SubResource("CurveTexture_og8y2")
color_ramp = SubResource("GradientTexture1D_7l2i5")
anim_speed_min = 0.1
anim_speed_max = 0.1
anim_offset_max = 1.0
