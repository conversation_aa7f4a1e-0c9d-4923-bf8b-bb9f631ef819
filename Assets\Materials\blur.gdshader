shader_type canvas_item;

uniform sampler2D SCREEN_TEXTURE : hint_screen_texture, filter_linear_mipmap;
uniform float blur: hint_range(0.0, 1.0);
uniform float brightness: hint_range(0.0, 1.0);

void fragment() {
	float mix_rate = smoothstep(0.0, texture(TEXTURE, UV).r, blur);
	// Apply a mipmap blur to the viewport
	COLOR = textureLod(SCREEN_TEXTURE, SCREEN_UV, mix(0, 3.0, mix_rate));
	// Mix darkness into the blurred area
	COLOR.rgb = mix(COLOR.rgb, COLOR.rgb * brightness, mix_rate);
	// Mix the wipe over the transition border
	float wipe = smoothstep(0.6, 0.5, mix_rate) - step(mix_rate, 0.4);
	COLOR.rgb = mix(COLOR.rgb, vec3(1.0), wipe);
	COLOR.a = 1.0;
}
