[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://d200qq4ut3l7b"
path.s3tc="res://.godot/imported/DCN_playField_school.jpg-6de64aa660cd6d03c2cc9dd149125ad9.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}

[deps]

source_file="res://Assets/DCN_playField_school.jpg"
dest_files=["res://.godot/imported/DCN_playField_school.jpg-6de64aa660cd6d03c2cc9dd149125ad9.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
