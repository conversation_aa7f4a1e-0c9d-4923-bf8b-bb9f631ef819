class_name Ability
extends Object

var ownedCard:Card
var text:String
var trigger:Trigger
var keyword:Keyword
var target:Target
var targetSlot:TargetSlot

func _init(_ownedCard:Card, _text:String = "", _keyword:Keyword = Keyword.new(), _trigger:Trigger = Trigger.new(), _target:Target = Target.new(), _targetSlot:TargetSlot = TargetSlot.new()):
	self.ownedCard = _ownedCard
	self.text = _text
	self.keyword = _keyword
	self.trigger = _trigger
	self.target = _target
	self.targetSlot = _targetSlot
	validateAbility(_keyword, _trigger)

func getParentCard() -> Card:
	return self.ownedCard

func getText() -> String:
	return self.text

func getTrigger() -> Trigger:
	return self.trigger

func getKeyword() -> Keyword:
	return self.keyword

func getTarget() -> Target:
	return self.target

func getTargetSlot() -> TargetSlot:
	return self.targetSlot

func printAbility() -> String:
	if not self.text == "":
		return self.text
	
	var cardText:String = ""
	if not Trigger.TriggerInfo[self.trigger.getName()]["label"] == "":
		cardText += Trigger.TriggerInfo[self.trigger.getName()]["label"]
		cardText += " "
	if not Keyword.KeywordInfo[self.keyword.getName()]["label"] == "":
		cardText += Keyword.KeywordInfo[self.keyword.getName()]["label"]
		cardText += " "
	if not self.keyword.getTier() == 0:
		cardText += str(self.keyword.getTier())
		cardText += " "
	if not self.keyword.getDynamicTier() == 0:
		cardText += DynamicTier.labelDict[self.keyword.getDynamicTier()]
		cardText += " "
	if not TargetCondition.labelDict[self.target.getCondition()] == "":
		cardText += TargetCondition.labelDict[self.target.getCondition()]
		cardText += " "
	if not self.target.getConditionTier() == 0:
		cardText += str(self.target.getConditionTier())
		cardText += " "
	if not Target.labelDict[self.target.getName()] == "":
		cardText += Target.labelDict[self.target.getName()]
		cardText += " "
	if not TriggerCondition.labelDict[self.trigger.getCondition()] == "":
		cardText += TriggerCondition.labelDict[self.trigger.getCondition()]
		cardText += " "
	if not self.trigger.getConditionTier() == 0:
		cardText += str(self.trigger.getConditionTier())
		cardText += " "
	return cardText.trim_suffix(" ")

func validateAbility(_keyword:Keyword, _trigger:Trigger) -> void:
	#print(Keyword.KeywordInfo[keyword.getName()]["label"] + ' & ' + Trigger.TriggerInfo[trigger.getName()]["label"])
	assert(Keyword.KeywordInfo[_keyword.getName()]["type"] == Trigger.TriggerInfo[_trigger.getName()]["type"])
