class_name CardUI
extends But<PERSON>

@onready var abilityLabel = $AbilityLabel
@onready var costLabel = $CostLabel
@onready var powerLabel = $PowerLabel
#@onready var powerLabelArena: Label3D = $OnArena/PowerText2
@onready var cardColor = $CardColor
@onready var cardRarity = $CardRarity

func displayCard(card:Card, castable:bool = true) -> void:
	if not is_node_ready():
		await ready
	
	if card:
		var abilitiesText = ""
		for ability in card.getAbilities():
			abilitiesText += ability.printAbility()
			abilitiesText += "\n"
		self.abilityLabel.text = abilitiesText
		self.costLabel.text = str(card.getCost()) + CastCondition.labelDict[card.getCastCondition()]
		self.powerLabel.text = str(card.getPower())
		#self.powerLabelArena.text = str(card.getPower())
		self.cardColor.color = CardColor.labelDict[card.getColor()]
		self.cardRarity.color = Rarity.labelDict[card.getRarity()]
		
		if(card.getDiffPower() > 0): self.powerLabel.modulate = Color.GREEN_YELLOW
		elif(card.getDiffPower() < 0): self.powerLabel.modulate = Color.LIGHT_CORAL
		
		if(card.getDiffCost() < 0): self.costLabel.modulate = Color.GREEN_YELLOW
		elif(card.getDiffCost() > 0): self.costLabel.modulate = Color.LIGHT_CORAL
		
		if not castable:
			self.modulate.a = 0.2
	else:
		self.cardColor.color = Color("#222222")
