extends Node3D


@onready var animation_player: AnimationPlayer = $AnimationPlayer
@onready var shot_timeout: Timer = $"../../FireBallPreview/ShotTimeout"


# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	pass # Replace with function body.


# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	if Input.is_action_pressed("ui_accept") && shot_timeout.time_left == 0.0:
		animation_player.play("Shoot")
