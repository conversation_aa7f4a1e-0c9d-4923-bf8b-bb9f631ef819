[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://cndbqos5215b8"
path="res://.godot/imported/ground_cracks.png-f438022916af74c1b5f706632b537385.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://effects/halloween_explosion/materials/ground_cracks.png"
dest_files=["res://.godot/imported/ground_cracks.png-f438022916af74c1b5f706632b537385.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
