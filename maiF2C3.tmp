[gd_scene load_steps=11 format=3 uid="uid://div6wuxesh8rn"]

[ext_resource type="Script" path="res://root.gd" id="1_ujs5q"]
[ext_resource type="Script" path="res://scripts/Card.gd" id="2_4ba8w"]
[ext_resource type="Script" path="res://scripts/CardResrouceService.gd" id="2_q6hrp"]
[ext_resource type="Script" path="res://scripts/TurnService.gd" id="2_xck3x"]
[ext_resource type="Script" path="res://scripts/CardCollection.gd" id="3_owkgx"]
[ext_resource type="Script" path="res://scripts/Player.gd" id="3_qjfg2"]
[ext_resource type="Script" path="res://scripts/Arena.gd" id="4_2hj85"]
[ext_resource type="Script" path="res://scripts/Deck.gd" id="5_b3jfg"]
[ext_resource type="Script" path="res://scripts/Hand.gd" id="6_go1bb"]
[ext_resource type="Script" path="res://scripts/Graveyard.gd" id="7_iyuam"]

[node name="Root" type="Node"]
script = ExtResource("1_ujs5q")

[node name="TurnService" type="Node" parent="."]
script = ExtResource("2_xck3x")

[node name="Card" type="Node" parent="."]
script = ExtResource("2_4ba8w")

[node name="CardCollection" type="Node" parent="."]
script = ExtResource("3_owkgx")

[node name="Player" type="Node" parent="."]
script = ExtResource("3_qjfg2")

[node name="Arena" type="Node" parent="."]
script = ExtResource("4_2hj85")

[node name="CardResrouceService" type="Node" parent="Arena"]
script = ExtResource("2_q6hrp")

[node name="Deck" type="Node" parent="Arena"]
script = ExtResource("5_b3jfg")

[node name="Hand" type="Node" parent="Arena"]
script = ExtResource("6_go1bb")

[node name="Graveyard" type="Node" parent="Arena"]
script = ExtResource("7_iyuam")

[node name="Label" type="Label" parent="Arena"]
offset_left = 36.0
offset_top = 20.0
offset_right = 76.0
offset_bottom = 43.0
text = "Hello"
