; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="PrototypeV2"
config/tags=PackedStringArray("4.2", "dracana", "prototype")
run/main_scene="res://main_test.tscn"
config/features=PackedStringArray("4.2", "Forward Plus")
boot_splash/bg_color=Color(0.141176, 0.141176, 0.141176, 1)
config/icon="res://PrototypeV2_SS.png"

[autoload]

PlayerProfile="*res://scripts/autoload/player_profile.gd"
Draw3d="*res://scripts/autoload/Draw3D.gd"
DragSnap="*res://addons/drag_and_snap/nodes/drag_snap.gd"
GameModeService="*res://scripts/GameModeService.gd"

[display]

window/size/viewport_width=1920
window/size/viewport_height=1080
window/size/borderless=true
window/stretch/mode="canvas_items"
window/defaults/default_clear_color=Color(0, 0, 0, 1)
window/image=""
window/bg_color=Color(0.141176, 0.141176, 0.141176, 1)

[editor_plugins]

enabled=PackedStringArray("res://addons/drag_and_snap/plugin.cfg")

[gui]

theme/custom="res://theme.tres"

[input]

clicked={
"deadzone": 0.5,
"events": [Object(InputEventMouseButton,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"button_mask":0,"position":Vector2(0, 0),"global_position":Vector2(0, 0),"factor":1.0,"button_index":1,"canceled":false,"pressed":false,"double_click":false,"script":null)
]
}

[layer_names]

3d_render/layer_3="SlotAbovePlayedCard"
3d_render/layer_4="LaneCardAboveEvent"
3d_render/layer_19="Inspect"
3d_render/layer_20="Hand"

[physics]

common/physics_ticks_per_second=120

[rendering]

viewport/hdr_2d=true
anti_aliasing/quality/use_taa=true
