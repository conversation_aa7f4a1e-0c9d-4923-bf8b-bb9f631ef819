class_name LaneUIController
extends Node3D

@onready var _cardSlots = [$Slot0, $Slot1, $Slot2, $Slot3, $Slot4, $Slot5]
@onready var slotPreview = [$PreviewPosition/Slot0P/Slot0P, $PreviewPosition/Slot1P/Slot1P, $PreviewPosition/Slot2P/Slot2P, $PreviewPosition/Slot3P/Slot3P, $PreviewPosition/Slot4P/Slot4P, $PreviewPosition/Slot5P/Slot5P]
@onready var locationDisplay = $Location
@onready var playerPower = $PowerPl
@onready var playerPowerW = $PowerBgPlayer/PlayerParticleW
@onready var enemyPower = $PowerEnemy
@onready var enemyPowerW = $PowerBgEnemy/EnemyParticleW
@onready var card_3dui = load("res://card3D.tscn")
@onready var battleNode = get_tree().get_root().get_node("Battle")
#@onready var arenaNode = get_tree().get_root().get_node("Arena") 

var lane: Lane

var slotDefaultColor
var slotDefaultColorPlayer
var slotDefaultColorEnemy
var slotCastableColor
var slotCastableColorPlayer
signal laneSlotSelected(index: int)

func setslot(index: int, mainPlayer: Player, oppoPlayer: Player, neutral : Player):
	var laneCardSlot: Array[LaneCardSlot]
	for cs in _cardSlots:
		laneCardSlot.append(cs)
	self.lane = Lane.new(laneCardSlot, index, mainPlayer, oppoPlayer, neutral)

	slotDefaultColor = slotPreview[2].material_override.albedo_color
	slotDefaultColorPlayer = slotDefaultColor + Color(-0.1, 0.5, 0, -0.01)
	slotDefaultColorEnemy = slotDefaultColor + Color(0.8, 0, 0, -0.01)
	slotCastableColor = slotDefaultColor + Color.from_hsv(0, 0, 0, 0.05)
	slotCastableColorPlayer = slotCastableColor + Color(0, 0.4, 0, 0)
	for i in Const.CARD_PER_LANE:
		if i < (Const.CARD_PER_LANE - Const.NEUTRAL_PER_LANE) / 2:
			self.lane.oppoSlots.append(i)
			set_slot_state(i, slotPreview[i], LaneCardSlot.IDLE_ENEMY_SIDE)
		elif i >= (Const.CARD_PER_LANE - Const.NEUTRAL_PER_LANE) / 2 + Const.NEUTRAL_PER_LANE:
			self.lane.mainSlots.append(i)
			set_slot_state(i, slotPreview[i], LaneCardSlot.IDLE_PLAYER_SIDE)
		else:
			self.lane.neutralSlots.append(i)
			set_slot_state(i, slotPreview[i], LaneCardSlot.IDLE)
		
		# if i >= 4:
		# 	set_slot_state(i, slotPreview[i], LaneCardSlot.IDLE_PLAYER_SIDE)
		# elif i <= 1:
		# 	set_slot_state(i, slotPreview[i], LaneCardSlot.IDLE_ENEMY_SIDE)
		# else:
		# 	set_slot_state(i, slotPreview[i], LaneCardSlot.IDLE)
	
	for i in _cardSlots.size():
		_cardSlots[i].slotStateChange.connect(self.onSlotStateChange.bind(i))
		battleNode.inspecting.connect(self.change_collision_mode)


func isEmpty() -> bool:
	return getAllCards().size() == 0


func isFull() -> bool:
	return self.lane.isFull()


func isTopCard(card: Card) -> bool:
	return self.lane.getTopCards().has(card)


func getLocationByPlayer(_player: Player) -> Card:
	return self.lane.getLocationByPlayer(_player)


func getAllSlots() -> Array[LaneCardSlot]:
	return self.lane.getAllSlots()


func getSlot(slotIndex: int) -> LaneCardSlot:
	return self.lane.cardSlots[slotIndex]


func getSlotOwner(slotIndex: int) -> Player:
	return self.lane.cardSlots[slotIndex].getOwner()


func getSlotCards(slotIndex: int) -> Array[Card]:
	return self.lane.cardSlots[slotIndex].getCards()


func getSlotTopCard(slotIndex: int) -> Card:
	return self.lane.cardSlots[slotIndex].getCardAt(0)


func getSlotCardCount(slotIndex: int) -> int:
	return self.lane.cardSlots[slotIndex].getCardCount()


func getAllCards() -> Array[Card]:
	return self.lane.getAllCards()


# Return all cards owned by owner
func getAllCardsByPlayer(_player: Player) -> Array[Card]:
	return self.lane.getAllCardsByPlayer(_player)


func getTopCards() -> Array[Card]:
	return self.lane.getTopCards()


# Return all cards owned by owner
func getTopCardsByPlayer(_player: Player) -> Array[Card]:
	return self.lane.getTopCardsByPlayer(_player)


# Return cardSlot (CardCollection) of a card
func getCardSlotByCard(card: Card) -> CardCollection:
	return self.lane.getCardSlotByCard(card)

# Return cardSlotIndex of a card
func getCardSlotIndexByCard(card: Card) -> int:
	return self.lane.getCardSlotIndexByCard(card)


# Return first CardSlot index owned by a player that is empty
func getFirstEmptyCastableSlotIndex(player: Player) -> int:
	return self.lane.getFirstEmptyCastableSlotIndex(player)


# Return an array of a player owned slots and neutral slots
func getAllowedSlot(player: Player) -> Array[int]:
	return self.lane.getAllowedSlot(player)


#Return int array of occupied slot
func getOccupiedSlotsIndex(_player: Player) -> Array[int]:
	return self.lane.getOccupiedSlotsIndex(_player)


# Return total power of the player's 'top' cards of this lane
func getPlayerTotalPower(player: Player) -> int:
	return self.lane.getPlayerTotalPower(player)


func isPlayerWinning(_player: Player) -> bool:
	return self.lane.isPlayerWinning(_player)


func setUnrevealedLocationText(laneIndex: int) -> void:
	if laneIndex == 0:
		$Location/UnrevealDesc.text = "Reveal on turn 1-3"
	elif laneIndex == 1:
		$Location/UnrevealDesc.text = "Reveal on turn 3-5"
	elif laneIndex == 2:
		$Location/UnrevealDesc.text = "Reveal on turn 5-7"


func setLocationByPlayer(_location: Card, _player: Player) -> void:
	self.lane.setLocationByPlayer(_location, _player)
	if _player.isOnMainSide():
		var card3d = card_3dui.instantiate()
		card3d.displayCard(self.lane.playerLocation)
		add_child(card3d)
		card3d.global_position = self.locationDisplay.global_position
		card3d.visible = false
	else:
		var card3d = card_3dui.instantiate()
		card3d.displayCard(self.lane.enemyLocation)
		add_child(card3d)
		card3d.global_position = self.locationDisplay.global_position
		card3d.visible = false


func revealLocation() -> void:
	self.lane.revealLocation()
	self.locationDisplay.displayLocation(self.lane.playerLocation)
	self.locationDisplay.locationName.visible = true
	self.locationDisplay.unrevealName.visible = false
	self.locationDisplay.abilityLabel.visible = true
	self.locationDisplay.unrevealDesc.visible = false


# Check if the card of a player can be added to a slot and add it
func addAt(player: Player, cards: Array[Card], slotIndex: int) -> void:
	####!!need to fixed this!!!####
	self.lane.addAt(player, cards, slotIndex)
	self._cardSlots[slotIndex].addUI(cards, true)
	self._cardSlots[slotIndex].setOwner(player)

# Remove cards from a slot and rearrange to fill empty slot
func removeAt(slot: int, rearrange: bool) -> Array[Card]:
	return self.lane.removeAt(slot, rearrange)


# Remove by specific cards and rearrange to fill empty slot
func removeCards(cards: Array[Card], rearrange: bool) -> Array[Card]:
	return self.lane.removeCards(cards, rearrange)


# Rearrange card from neutral slots to a player owned slot if available
func rearrangeCard(player: Player) -> void:
	self.lane.rearrangeCard(player)


# Swap cards between two slots
func swap(slotA: int, slotB: int) -> void:
	self.lane.swap(slotA, slotB)


func onSlotStateChange(_state: int, _slotIndex: int) -> void:
	if _state == LaneCardSlot.DISABLED:
		slotPreview[_slotIndex].visible = false
		#slotPreview[_slotIndex].get_parent().visible = false
	else:
		slotPreview[_slotIndex].visible = true
		#slotPreview[_slotIndex].get_parent().visible = true


func _on_slot_0p_input_event(_camera, _event, _position, _normal, _shape_idx):
	if _event.is_action_pressed("clicked"):
		var slot = get_child(0)
		if slot.get_child(0):
			var cardInSlot = slot.get_child(0)
			cardInSlot.cardSelected.emit(cardInSlot)


func _on_slot_1p_input_event(_camera, _event, _position, _normal, _shape_idx):
	if _event.is_action_pressed("clicked"):
		var slot = get_child(1)
		if slot.get_child(0):
			var cardInSlot = slot.get_child(0)
			cardInSlot.cardSelected.emit(cardInSlot)


func _on_slot_2p_input_event(_camera, _event, _position, _normal, _shape_idx):
	if _event.is_action_pressed("clicked"):
		var slot = get_child(2)
		if slot.get_child(0):
			var cardInSlot = slot.get_child(0)
			cardInSlot.cardSelected.emit(cardInSlot)


func _on_slot_3p_input_event(_camera, _event, _position, _normal, _shape_idx):
	if _event.is_action_pressed("clicked"):
		var slot = get_child(3)
		if slot.get_child(0):
			var cardInSlot = slot.get_child(0)
			cardInSlot.cardSelected.emit(cardInSlot)


func _on_slot_4p_input_event(_camera, _event, _position, _normal, _shape_idx):
	if _event.is_action_pressed("clicked"):
		var slot = get_child(4)
		if slot.get_child(0):
			var cardInSlot = slot.get_child(0)
			cardInSlot.cardSelected.emit(cardInSlot)


func _on_slot_5p_input_event(_camera, _event, _position, _normal, _shape_idx):
	if _event.is_action_pressed("clicked"):
		var slot = get_child(5)
		if slot.get_child(0):
			var cardInSlot = slot.get_child(0)
			cardInSlot.cardSelected.emit(cardInSlot)


func _on_slot_0p_mouse_entered():
	#if get_parent().handSelectedIndex != -1:
	slotPreview[0].transform.origin.y += 0.1
	if _cardSlots[0].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[0].get_child(0).currentState = 6

func _on_slot_0p_mouse_exited():
	slotPreview[0].transform.origin.y -= 0.1
	if _cardSlots[0].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[0].get_child(0).currentState = 0


func _on_slot_1p_mouse_entered():
	slotPreview[1].transform.origin.y += 0.1
	if _cardSlots[1].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[1].get_child(0).currentState = 6

func _on_slot_1p_mouse_exited():
	slotPreview[1].transform.origin.y -= 0.1
	if _cardSlots[1].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[1].get_child(0).currentState = 0


func _on_slot_2p_mouse_entered():
	slotPreview[2].transform.origin.y += 0.1
	if _cardSlots[2].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[2].get_child(0).currentState = 6

func _on_slot_2p_mouse_exited():
	slotPreview[2].transform.origin.y -= 0.1
	if _cardSlots[2].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[2].get_child(0).currentState = 0


func _on_slot_3p_mouse_entered():
	slotPreview[3].transform.origin.y += 0.1
	if _cardSlots[3].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[3].get_child(0).currentState = 6

func _on_slot_3p_mouse_exited():
	slotPreview[3].transform.origin.y -= 0.1
	if _cardSlots[3].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[3].get_child(0).currentState = 0


func _on_slot_4p_mouse_entered():
	slotPreview[4].transform.origin.y += 0.1
	if _cardSlots[4].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[4].get_child(0).currentState = 6

func _on_slot_4p_mouse_exited():
	slotPreview[4].transform.origin.y -= 0.1
	if _cardSlots[4].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[4].get_child(0).currentState = 0


func _on_slot_5p_mouse_entered():
	#if slotPreview[5].material_override.albedo_color == slotCastableColor:
		#slotPreview[5].material_override.albedo_color = Color.CHARTREUSE
	slotPreview[5].transform.origin.y += 0.1
	if _cardSlots[5].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[5].get_child(0).currentState = 6

func _on_slot_5p_mouse_exited():
	#slotPreview[5].material_override.albedo_color = slotCastableColor
	slotPreview[5].transform.origin.y -= 0.1
	if _cardSlots[5].get_child(0) and !battleNode.arena.is_dragging:
		_cardSlots[5].get_child(0).currentState = 0

func set_slot_state(slotIndex, slotModel, state):
	self.lane.set_slot_state(slotIndex, state)
	match state:
		LaneCardSlot.CASTABLE:
			slotModel.material_override.albedo_color = slotCastableColor
		LaneCardSlot.CASTABLE_PLAYER_SIDE:
			slotModel.material_override.albedo_color = slotCastableColorPlayer
		LaneCardSlot.READY_TO_CAST:
			slotModel.material_override.albedo_color = Color.KHAKI + Color.from_hsv(0, 0, 0, 0.5) # Color.CHARTREUSE - Color.from_hsv(0,0,0, 0.5)#+= Color.from_hsv(0,0,0, 0.1)
		LaneCardSlot.READY_TO_CAST_PLAYER_SIDE:
			slotModel.material_override.albedo_color = Color.KHAKI + Color.from_hsv(0, 0, 0, 0.5) # Color.CHARTREUSE - Color.from_hsv(0,0,0, 0.5)#+= Color.from_hsv(0,0,0, 0.1)
		LaneCardSlot.SACRIFIABLE:
			slotModel.material_override.albedo_color = Color.BROWN + Color(0.6, 0, 0, -0.2)
		LaneCardSlot.READY_TO_SAC:
			slotModel.material_override.albedo_color = Color.KHAKI + Color.from_hsv(0, 0, 0, 0.5)
		LaneCardSlot.SYMBIONTABLE:
			slotModel.material_override.albedo_color = Color.DARK_GREEN + Color(0, 0.4, 0, -0.2)
		LaneCardSlot.READY_TO_SYM:
			slotModel.material_override.albedo_color = Color.KHAKI + Color.from_hsv(0, 0, 0, 0.5)
		LaneCardSlot.BOUNCEABLE:
			slotModel.material_override.albedo_color = Color.BLUE + Color(0, 0.6, 0, -0.2)
		LaneCardSlot.IDLE_PLAYER_SIDE:
			slotModel.material_override.albedo_color = slotDefaultColorPlayer
		LaneCardSlot.IDLE_ENEMY_SIDE:
			slotModel.material_override.albedo_color = slotDefaultColorEnemy
		_:
			slotModel.material_override.albedo_color = slotDefaultColor

func change_collision_mode(inspecting: bool):
	for i in slotPreview.size():
		if slotPreview[i].get_parent().get_child(1):
			if inspecting:
				slotPreview[i].get_parent().get_child(1).disabled = true
			else:
				slotPreview[i].get_parent().get_child(1).disabled = false
