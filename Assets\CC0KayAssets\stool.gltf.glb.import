[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://dsaiqw7pihqoi"
path="res://.godot/imported/stool.gltf.glb-4b90ea0fea6e539a1ea821a78cf51cd7.scn"

[deps]

source_file="res://Assets/CC0KayAssets/stool.gltf.glb"
dest_files=["res://.godot/imported/stool.gltf.glb-4b90ea0fea6e539a1ea821a78cf51cd7.scn"]

[params]

nodes/root_type="Node3D"
nodes/root_name="Scene Root"
nodes/apply_root_scale=true
nodes/root_scale=1.0
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
import_script/path=""
_subresources={}
gltf/naming_version=0
gltf/embedded_image_handling=1
