class_name Player
extends Node3D
enum {
	MAIN,
	OPPO,
	NEUTRAL
}

var hasCastThisTurn:bool
var playerName:String = "name"
var onMainSide:bool
var mana:int = 0
var maxMana:int = 0
var baseMaxMana:int = 0
var capMana:int = Const.CAP_MANA
var tempMana:int = 0

@onready var hand = $Hand
@onready var deck = $Deck
@onready var graveyard = $Graveyard
@onready var movingQueue = $MovingQueue

signal manaChanged()

func _ready():
	if get_child_count()>0:
		self.hand.setOwner(self)
		self.deck.setOwner(self)
		self.graveyard.setOwner(self)

func setName(p_name:String) -> void:
	self.playerName = p_name

func setSide(p_onMainSide:bool) -> void:
	self.onMainSide = p_onMainSide

func setDeck(p_cards:Array[Card]) -> void:
	self.deck.add(p_cards)

func setMana(p_mana:int) -> void:
	self.mana = p_mana
	manaChanged.emit()

func setMaxMana(p_maxMana:int) -> void:
	self.maxMana = p_maxMana
	self.baseMaxMana = p_maxMana
	manaChanged.emit()

func setHandBrightness(on:bool) -> void:
	if !on:
		for card in hand.get_children():
			card.brightness_up(false)
	else:
		for card in hand.get_children():
			card.brightness_up(true)

func getName() -> String:
	return self.playerName

func getMana() -> int:
	return self.mana

func getMaxMana() -> int:
	return self.maxMana

func getBaseMaxMana() -> int:
	return self.baseMaxMana

func getTempMana() -> int:
	return self.tempMana

func getManaAndTempMana() -> int:
	return getMana() + getTempMana()

func getDiffMaxMana() -> int:
	return getMaxMana() - getBaseMaxMana()

func getDeck() -> Deck:
	return self.deck

func getHand() -> Hand:
	return self.hand
	
func getGraveyard() -> Graveyard:
	return self.graveyard
	
# Returns whether this Player is on the main (bottom) side of the game screen or not
func isOnMainSide() -> bool:
	return self.onMainSide

func fillMana() -> void:
	setMana(self.maxMana)
	syncMana()

func useMana(amount:int) -> void:
	setMana(self.mana - amount)
	if self.mana < 0:
		modifyTempMana(self.mana)
		setMana(0)
	syncMana()
	
func modifyMaxMana(modifier:int) -> void:
	var newMaxMana = self.maxMana + modifier
	setMaxMana(newMaxMana)
	syncMana()

func modifyTempMana(modifier:int) -> void:
	self.tempMana += modifier
	manaChanged.emit()

func modifyEffectMaxMana(amount:int) -> void:
	self.capMana = self.capMana + amount
	self.maxMana = self.baseMaxMana + amount
	syncMana()
	manaChanged.emit()

func syncMana() -> void:
	if self.baseMaxMana > Const.CAP_MANA:
		self.baseMaxMana = Const.CAP_MANA
	elif self.baseMaxMana < 0:
		self.baseMaxMana = 0
		
	if self.maxMana > self.capMana: 
		setMaxMana(self.capMana)
	elif self.maxMana < 0:
		setMaxMana(0)
		
	if self.mana > self.maxMana: 
		setMana(self.maxMana)
	if self.mana < 0:
		setMana(0)
