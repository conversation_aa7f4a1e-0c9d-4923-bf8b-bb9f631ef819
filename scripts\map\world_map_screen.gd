extends Control

@onready var option_screen: Control = $OptionScreen

var pm = profile_management.new()
var reward_chance: Dictionary = {
	"1": {
		"ink_min": 7,
		"ink_max": 11,
		"rare_shard": 0.5,
		"epic_shard": 0.1,
		"legendary_shard": 0.01,
		"powder_min": 0,
		"powder_max": 2,
		"crystal_min": 0,
		"crystal_max": 1
		},
	"2": {
		"ink_min": 8,
		"ink_max": 13,
		"rare_shard": 0.5,
		"epic_shard": 0.1,
		"legendary_shard": 0.01,
		"powder_min": 0,
		"powder_max": 2,
		"crystal_min": 0,
		"crystal_max": 1
		},
	"3": {
		"ink_min": 10,
		"ink_max": 15,
		"rare_shard": 0.5,
		"epic_shard": 0.1,
		"legendary_shard": 0.01,
		"powder_min": 0,
		"powder_max": 2,
		"crystal_min": 0,
		"crystal_max": 1
		}
	}

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	Const.CURRENT_SCREEN = "world_map_screen"
	pass # Replace with function body.

# Called every frame. 'delta' is the elapsed time since the previo@onready var option_screen: Control = $OptionScreen

func _process(delta: float) -> void:
	pass # Replace with function body.

#logic functions
func finish_level(stage: String, status: int) -> void:
	pm.update_progress(stage, status)
	claim_reward(stage)
	

func claim_reward(stage: String) -> void:
	#rng for rewards
	var random_float = randf()
	var rewards = reward_chance[stage]
	var ink_amount = randi_range(rewards["ink_min"], rewards["ink_max"])
	var powder_amount = randi_range(rewards["powder_min"], rewards["powder_max"])
	var crystal_amount = randi_range(rewards["crystal_min"], rewards["crystal_max"])
	var shard = "none"
	if random_float < rewards["legendary_shard"]:
		print("legendary_shard")
		shard = "legendary_shard"
	elif random_float < rewards["epic_shard"]:
		print("epic_shard")
		shard = "epic_shard"
	elif random_float < rewards["rare_shard"]:
		print("rare_shard")
		shard = "rare_shard"

	#update resources
	#how to ink????
	pm.update_resource("green_ink", ink_amount)
	pm.update_resource("red_ink", ink_amount)
	pm.update_resource("blue_ink", ink_amount)

	pm.update_resource("powder", powder_amount)
	pm.update_resource("combine_crystal", crystal_amount)
	pm.update_resource(shard, 1)
	
	

func _on_start_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/title/starter_deck_selection_screen.tscn")

func _on_my_library_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/my_lib/my_library_screen.tscn")

func _on_settings_button_pressed() -> void:
	option_screen.visible = true

func _on_l_1_pressed() -> void:
	get_tree().change_scene_to_file("res://main_alpha.tscn")
