class_name Lane

enum {
	LEFT,
	CENTER,
	RIGHT
}

var cardSlots: Array[LaneCardSlot]

var isLocationRevealed: bool = false
var playerLocation: Card
var enemyLocation: Card
var mainSlots: Array[int] # Store index of cardSlot belongs to Main player
var neutralSlots: Array[int] # Store index of neutral cardSlots
var oppoSlots: Array[int] # Store index of cardSlot belongs to Oppo player
var lane_index: int

func _init(p_cardslot: Array[LaneCardSlot], index: int, mainPlayer: Player, oppoPlayer: Player, neutral: Player):
	lane_index = index
	cardSlots = p_cardslot
	for slot in cardSlots:
		slot.lane_index = index
	for i in Const.CARD_PER_LANE:
		if i < (Const.CARD_PER_LANE - Const.NEUTRAL_PER_LANE) / 2:
			self.oppoSlots.append(i)
			cardSlots[i].setOwner(oppoPlayer)
			set_slot_state(i, LaneCardSlot.IDLE_ENEMY_SIDE)
		elif i >= (Const.CARD_PER_LANE - Const.NEUTRAL_PER_LANE) / 2 + Const.NEUTRAL_PER_LANE:
			self.mainSlots.append(i)
			cardSlots[i].setOwner(mainPlayer)
			set_slot_state(i, LaneCardSlot.IDLE_PLAYER_SIDE)
		else:
			self.neutralSlots.append(i)
			cardSlots[i].setOwner(neutral)
			set_slot_state(i, LaneCardSlot.IDLE)

func isEmpty() -> bool:
	return getAllCards().size() == 0

func isFull() -> bool:
	for cardSlot in cardSlots:
		if cardSlot.getCardCount() == 0:
			return false
	return true

func isTopCard(card: Card) -> bool:
	return getTopCards().has(card)

func getLocationByPlayer(_player: Player) -> Card:
	if _player.isOnMainSide():
		return self.playerLocation
	else:
		return self.enemyLocation

func getAllSlots() -> Array[LaneCardSlot]:
	var returnedSlots: Array[LaneCardSlot] = []
	for i in Const.CARD_PER_LANE:
		returnedSlots.append(getSlot(i))
	return returnedSlots

func getSlot(slotIndex: int) -> LaneCardSlot:
	return self.cardSlots[slotIndex]

func getSlotOwner(slotIndex: int) -> Player:
	return self.cardSlots[slotIndex].getOwner()

func getSlotCards(slotIndex: int) -> Array[Card]:
	return self.cardSlots[slotIndex].getCards()

func getSlotTopCard(slotIndex: int) -> Card:
	return self.cardSlots[slotIndex].getCardAt(0)

func getSlotCardCount(slotIndex: int) -> int:
	return self.cardSlots[slotIndex].getCardCount()

func getAllCards() -> Array[Card]:
	var returnedCards: Array[Card] = []
	for cardSlot in self.cardSlots:
		returnedCards.append_array(cardSlot.getCards())
	return returnedCards

# Return all cards owned by owner
func getAllCardsByPlayer(_player: Player) -> Array[Card]:
	var returnedCards: Array[Card] = []
	for cardSlot in self.cardSlots:
		if cardSlot.getOwner() == _player:
			returnedCards.append_array(cardSlot.getCards())
	if _player.isOnMainSide(): returnedCards.reverse() # Put the bottom card first for Stack resolve
	return returnedCards

func getTopCards() -> Array[Card]:
	var returnedCards: Array[Card] = []
	for cardSlot in self.cardSlots:
		if not cardSlot.isEmpty():
			returnedCards.append(cardSlot.getCardAt(0))
	return returnedCards

# Return all cards owned by owner
func getTopCardsByPlayer(_player: Player) -> Array[Card]:
	var returnedCards: Array[Card] = []
	for cardSlot in self.cardSlots:
		if cardSlot.getOwner() == _player && not cardSlot.isEmpty():
			returnedCards.append(cardSlot.getCardAt(0))
	if _player.isOnMainSide(): returnedCards.reverse() # Put the bottom card first for Stack resolve
	return returnedCards

# Return cardSlot (CardCollection) of a card
func getCardSlotByCard(card: Card) -> CardCollection:
	for cardSlot in self.cardSlots:
		if cardSlot.has(card):
			return cardSlot.getCardCollection()
	return null

# Return cardSlotIndex of a card
func getCardSlotIndexByCard(card: Card) -> int:
	for i in Const.CARD_PER_LANE:
		if self.cardSlots[i].has(card):
			return i
	return -1

# Return first CardSlot index owned by a player that is empty
func getEmptyOwnSlotIndex(player: Player) -> int:
	var allowedSlots: Array[int] = []
	if player.isOnMainSide():
		allowedSlots = mainSlots.duplicate()
	else:
		allowedSlots = oppoSlots.duplicate()
	
	allowedSlots.append_array(neutralSlots)
	
	for slot in allowedSlots:
		if self.cardSlots[slot].isEmpty():
			return slot
	return -1

# Return an array of a player owned slots and neutral slots
func getAllowedSlot(player: Player) -> Array[int]:
	var allowedSlots: Array[int] = [] # Prevent pass by reference
	if player.isOnMainSide():
		allowedSlots.append_array(self.mainSlots)
		if not self.mainSlots.has(getEmptyOwnSlotIndex(player)):
			allowedSlots.append_array(self.neutralSlots.duplicate())
	else:
		allowedSlots.append_array(self.oppoSlots)
		if not self.oppoSlots.has(getEmptyOwnSlotIndex(player)):
			allowedSlots.append_array(self.neutralSlots.duplicate())
	
	return allowedSlots

#Return int array of occupied slot
func getOccupiedSlotsIndex(_player: Player) -> Array[int]:
	var result: Array[int] = []
	for i in getAllowedSlot(_player):
		if getSlotOwner(i) == _player and getSlotCardCount(i) > 0:
			result.append(i)
	return result

# Return total power of the player's 'top' cards of this lane
func getPlayerTotalPower(player: Player) -> int:
	var power = 0
	for cardSlot in self.cardSlots:
		if not cardSlot.isEmpty():
			if cardSlot.getOwner() == player:
				power += cardSlot.getTopCardPower()
	
	if getLocationByPlayer(player):
		power += getLocationByPlayer(player).getPower()
	return power

func isPlayerWinning(_player: Player) -> bool:
	match GlobalEffectService.getLaneWinConditionEffect(self):
		LaneWinCondition.MORE_POWER:
			return getPlayerTotalPower(_player) > getPlayerTotalPower(TurnService.getOpponent(_player))
		LaneWinCondition.LESS_POWER:
			return getPlayerTotalPower(_player) < getPlayerTotalPower(TurnService.getOpponent(_player))
	return getPlayerTotalPower(_player) > getPlayerTotalPower(TurnService.getOpponent(_player))

func setLocationByPlayer(_location: Card, _player: Player) -> void:
	if _player.isOnMainSide():
		self.playerLocation = _location
	else:
		self.enemyLocation = _location

func revealLocation() -> void:
	self.isLocationRevealed = true
	self.playerLocation.setEnableAbilities(true)
	self.enemyLocation.setEnableAbilities(true)
	self.playerLocation.setEnterArenaTurn()
	self.enemyLocation.setEnterArenaTurn()

# Check if the card of a player can be added to a slot and add it
func addAt(player: Player, cards: Array[Card], slotIndex: int) -> void:
	####!!need to fixed this!!!####
	self.cardSlots[slotIndex].setOwner(player)
	# self.cardSlots[slotIndex].add(cards)
	#rearrangeCard(player)
	for card in cards:
		card.setEnterArenaTurn()

# Remove cards from a slot and rearrange to fill empty slot
func removeAt(slot: int, rearrange: bool) -> Array[Card]:
	if cardSlots[slot].isEmpty():
		return []

	var prevOwner: Player = self.cardSlots[slot].getOwner()
	var removedCards: Array[Card] = self.cardSlots[slot].empty()

	if rearrange: rearrangeCard(prevOwner)
	return removedCards

# Remove by specific cards and rearrange to fill empty slot
func removeCards(cards: Array[Card], rearrange: bool) -> Array[Card]:
	var removedCards: Array[Card] = []
	for card in cards:
		for i in Const.CARD_PER_LANE:
			if self.cardSlots[i].has(card):
				if self.cardSlots[i].getCardAt(0) == card: # Is top card
					removedCards.append_array(removeAt(i, rearrange))
				else:
					removedCards.append(card)
					self.cardSlots[i].getCards().erase(card)
	return removedCards

# Rearrange card from neutral slots to a player owned slot if available
func rearrangeCard(player: Player) -> void:
	if getEmptyOwnSlotIndex(player) == -1: return
	
	var ownSlots = []
	var enemySlots = []
		
	if player.isOnMainSide():
		ownSlots.append_array(self.mainSlots)
		enemySlots.append_array(self.oppoSlots)
	else:
		ownSlots.append_array(self.oppoSlots)
		enemySlots.append_array(self.mainSlots)
	
	for slot in neutralSlots:
		if self.cardSlots[slot].getOwner() == player and ownSlots.has(getEmptyOwnSlotIndex(player)):
			swap(getEmptyOwnSlotIndex(player), slot)
	for slot in enemySlots:
		if self.cardSlots[slot].getOwner() == player:
			swap(getEmptyOwnSlotIndex(player), slot)

# Swap cards between two slots (B > A)
func swap(slotA: int, slotB: int) -> void:
	var tempOwner = self.cardSlots[slotA].getOwner()
	var temp = self.cardSlots[slotA].empty()
	self.cardSlots[slotA].setOwner(self.cardSlots[slotB].getOwner())
	self.cardSlots[slotA].addUI(self.cardSlots[slotB].empty(), false)
	self.cardSlots[slotB].setOwner(tempOwner)
	self.cardSlots[slotB].addUI(temp, false)
	if temp.size() == 0:
		self.cardSlots[slotB].emptyMQ()

func set_slot_state(slotIndex, state):
	match state:
		LaneCardSlot.CASTABLE:
			cardSlots[slotIndex].setState(LaneCardSlot.CASTABLE)
		LaneCardSlot.CASTABLE_PLAYER_SIDE:
			cardSlots[slotIndex].setState(LaneCardSlot.CASTABLE_PLAYER_SIDE)
		LaneCardSlot.READY_TO_CAST:
			cardSlots[slotIndex].setState(LaneCardSlot.READY_TO_CAST)
		LaneCardSlot.READY_TO_CAST_PLAYER_SIDE:
			cardSlots[slotIndex].setState(LaneCardSlot.READY_TO_CAST_PLAYER_SIDE)
		LaneCardSlot.SACRIFIABLE:
			cardSlots[slotIndex].setState(LaneCardSlot.SACRIFIABLE)
		LaneCardSlot.READY_TO_SAC:
			cardSlots[slotIndex].setState(LaneCardSlot.READY_TO_SAC)
		LaneCardSlot.SYMBIONTABLE:
			cardSlots[slotIndex].setState(LaneCardSlot.SYMBIONTABLE)
		LaneCardSlot.READY_TO_SYM:
			cardSlots[slotIndex].setState(LaneCardSlot.READY_TO_SYM)
		LaneCardSlot.IDLE_PLAYER_SIDE:
			cardSlots[slotIndex].setState(LaneCardSlot.IDLE_PLAYER_SIDE)
		LaneCardSlot.IDLE_ENEMY_SIDE:
			cardSlots[slotIndex].setState(LaneCardSlot.IDLE_ENEMY_SIDE)
		_:
			cardSlots[slotIndex].setState(LaneCardSlot.IDLE)
