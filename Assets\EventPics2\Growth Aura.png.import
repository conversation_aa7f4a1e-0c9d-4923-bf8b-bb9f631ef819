[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://bbf8xwuecxxm8"
path="res://.godot/imported/Growth Aura.png-93deb26c45650ebc27ad96565cc1a551.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://Assets/EventPics2/Growth Aura.png"
dest_files=["res://.godot/imported/Growth Aura.png-93deb26c45650ebc27ad96565cc1a551.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
