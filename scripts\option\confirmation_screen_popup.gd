extends Control

@onready var confirmation_screen: Control = $"."
@onready var popup_label: Label = $VBoxContainer/PopupLabel

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	#if Const.CURRENT_SCREEN == "continue_screen":
		#return_button.visible = false
	pass # Replace with function body.

# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass

func _on_close_button_pressed() -> void:
	confirmation_screen.visible = false


func _on_confirm_button_pressed() -> void:
	match popup_label.text:
		"Return to Desktop?":
			get_tree().quit()
		"Return to Title Screen?":
			get_tree().change_scene_to_file("res://scripts/title/title_screen.tscn")
		"Load game?":
			get_tree().change_scene_to_file("res://scripts/map/world_map_screen.tscn")
	pass # Replace with function body.


func _on_cancel_button_pressed() -> void:
	confirmation_screen.visible = false
	pass # Replace with function body.

