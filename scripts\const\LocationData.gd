extends Object
class_name LocationData
enum difficulty{
	TEST,
	SIMPLE,
	<PERSON><PERSON><PERSON>RD,
	CHALLENGE
}
enum event{
	Battlecry,
	Rampancy,
	BlessofNature,
	FairyDust,
	ManaSurge,
	Ambition,
	EnergyAura,
	WildCaller,
	Reinforcement,
	LoneGiant,
	MirrorImage,
	GrowthAura,
	PowerPortal,
	TrapHole,
	Renewal,
	HarmonicAura,
	Stampede,
	Spikes,
	MagicAlluring,
	Voidlands,
	MindShift,
	BetrayalAura
}
static var DifficultySet = LocationData.difficulty.STANDARD

static var LocationList = {
	LocationData.difficulty.TEST: {
		"events": {
			LocationData.event.Battlecry:{
				"name": "Battlecry",
				"rarity": 0,
				"abilities": [
					{
						"text": "Give +3 Power to the winning player's other lanes.",
						"trigger": {
							"name": 2,
							"condition": 20
						},
						"keyword": {
							"name": 16,
							"tier": 3
						},
						"target": {
							"name": 19
						}
					}
				]
			},
			LocationData.event.Rampancy:{
				"name": "Rampancy",
				"rarity": 0,
				"abilities": [
					{
						"text": "Give +5 Power to each player's weakest card here.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"tier": 5
						},
						"target": {
							"name": 9,
							"condition": 4
						}
					}
				]
			}
		},
			LocationData.event.BlessofNature:{
				"name": "Bless of Nature",
				"rarity": 0,
				"abilities": [
					{
						"text": "Give +3 Power to each card here.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"tier": 3
						},
						"target": {
							"name": 9
						}
					}
				]
			},
			LocationData.event.ManaSurge:{
				"name": "Mana Surge",
				"rarity": 0,
				"abilities": [
					{
						"text": "On reveal: Each player gains 3 Temp mana.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 9,
							"tier": 3
						}
					}
				]
			}
	},
	LocationData.difficulty.SIMPLE: {
		"events": {
			LocationData.event.Battlecry:{
				"name": "Battlecry",
				"rarity": 0,
				"abilities": [
					{
						"text": "Give +3 Power to the winning player's other lanes.",
						"trigger": {
							"name": 2,
							"condition": 20
						},
						"keyword": {
							"name": 16,
							"tier": 3
						},
						"target": {
							"name": 19
						}
					}
				]
			},
			LocationData.event.Rampancy:{
				"name": "Rampancy",
				"rarity": 0,
				"abilities": [
					{
						"text": "Give +5 Power to each player's weakest card here.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"tier": 5
						},
						"target": {
							"name": 9,
							"condition": 4
						}
					}
				]
			},
			LocationData.event.BlessofNature:{
				"name": "Bless of Nature",
				"rarity": 0,
				"abilities": [
					{
						"text": "Give +3 Power to each card here.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"tier": 3
						},
						"target": {
							"name": 9
						}
					}
				]
			},
			LocationData.event.FairyDust:{
				"name": "Fairy Dust",
				"rarity": 0,
				"abilities": [
					{
						"text": "On reveal: Each player gains 1 Permanent mana.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 2,
							"tier": 1
						}
					}
				]
			},
			LocationData.event.ManaSurge:{
				"name": "Mana Surge",
				"rarity": 0,
				"abilities": [
					{
						"text": "On reveal: Each player gains 3 Temp mana.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 9,
							"tier": 3
						}
					}
				]
			},
			LocationData.event.Ambition:{
				"name": "Ambition",
				"rarity": 0,
				"abilities": [
					{
						"text": "At the end of the turn, the winning player draw a card.",
						"trigger": {
							"name": 6,
							"condition": 20
						},
						"keyword": {
							"name": 14,
							"tier": 1
						}
					}
				]
			},
			LocationData.event.EnergyAura:{
				"name": "Energy Aura",
				"rarity": 0,
				"abilities": [
					{
						"text": "On enter abilities effects here are doubled.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 25,
							"tier": 2
						},
						"target": {
							"name": 9,
							"condition": 14
						}
					}
				]
			},
			LocationData.event.WildCaller:{
				"name": "Wild Caller",
				"rarity": 1,
				"abilities": [
					{
						"text": "Draw a card whenever you play a card here.",
						"trigger": {
							"name": 13
						},
						"keyword": {
							"name": 14,
							"tier": 1
						}
					}
				]
			},
			LocationData.event.Reinforcement:{
				"name": "Reinforcement",
				"rarity": 1,
				"abilities": [
					{
						"text": "On reveal: Each player draws 2 cards.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 14,
							"tier": 2
						}
					}
				]
			},
			LocationData.event.LoneGiant:{
				"name": "Lone Giant",
				"rarity": 2,
				"abilities": [
					{
						"text": "If you own only one card here, it gets +10 Power.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"tier": 10
						},
						"target": {
							"name": 9,
							"condition": 2
						}
					}
				]
			},
			LocationData.event.MirrorImage:{
				"name": "Mirror Image",
				"rarity": 2,
				"abilities": [
					{
						"text": "After you play a card here, add a copy of it to another lane.",
						"trigger": {
							"name": 13
						},
						"keyword": {
							"name": 15
						},
						"target": {
							"name": 18
						},
						"targetSlot": {
							"name": 4,
							"condition": 1
						}
					}
				]
			},
			LocationData.event.GrowthAura:{
				"name": "Growth Aura",
				"rarity": 2,
				"abilities": [
					{
						"text": "At the start of the turn, give +1 Power to each card here.",
						"trigger": {
							"name": 4
						},
						"keyword": {
							"name": 4,
							"tier": 1
						},
						"target": {
							"name": 9
						}
					}
				]
			},
			LocationData.event.PowerPortal:{
				"name": "Power Portal",
				"rarity": 3,
				"abilities": [
					{
						"text": "Grants this lane's power to other lanes.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"dynamicTier": 2
						},
						"target": {
							"name": 19
						}
					}
				]
			}
		}
	},
	LocationData.difficulty.STANDARD: {
		"events": {
			LocationData.event.Battlecry:{
				"name": "Battlecry",
				"rarity": 0,
				"abilities": [
					{
						"text": "Give +3 Power to the winning player's other lanes.",
						"trigger": {
							"name": 2,
							"condition": 20
						},
						"keyword": {
							"name": 16,
							"tier": 3
						},
						"target": {
							"name": 19
						}
					}
				]
			},
			LocationData.event.TrapHole:{
				"name": "Trap Hole",
				"rarity": 0,
				"abilities": [
					{
						"text": "Your first card played here is destroyed.",
						"trigger": {
							"name": 13,
							"condition": 2,
							"conditionTier": 1
						},
						"keyword": {
							"name": 11
						},
						"target": {
							"name": 18
						}
					}
				]
			},
			LocationData.event.Rampancy:{
				"name": "Rampancy",
				"rarity": 0,
				"abilities": [
					{
						"text": "Give +5 Power to each player's weakest card here.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"tier": 5
						},
						"target": {
							"name": 9,
							"condition": 4
						}
					}
				]
			},
			LocationData.event.Renewal:{
				"name": "Renewal",
				"rarity": 0,
				"abilities": [
					{
						"text": "On reveal: Trigger On enter abilities of all cards here again.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 26
						},
						"target": {
							"name": 9,
							"condition": 14
						}
					}
				]
			},
			LocationData.event.BlessofNature:{
				"name": "Bless of Nature",
				"rarity": 0,
				"abilities": [
					{
						"text": "Give +3 Power to each card here.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"tier": 3
						},
						"target": {
							"name": 9
						}
					}
				]
			},
			LocationData.event.FairyDust:{
				"name": "Fairy Dust",
				"rarity": 0,
				"abilities": [
					{
						"text": "On reveal: Each player gains 1 Permanent mana.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 2,
							"tier": 1
						}
					}
				]
			},
			LocationData.event.ManaSurge:{
				"name": "Mana Surge",
				"rarity": 0,
				"abilities": [
					{
						"text": "On reveal: Each player gains 3 Temp mana.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 9,
							"tier": 3
						}
					}
				]
			},
			LocationData.event.Ambition:{
				"name": "Ambition",
				"rarity": 0,
				"abilities": [
					{
						"text": "At the end of the turn, the winning player draw a card.",
						"trigger": {
							"name": 6,
							"condition": 20
						},
						"keyword": {
							"name": 14,
							"tier": 1
						}
					}
				]
			},
			LocationData.event.HarmonicAura:{
				"name": "Harmonic Aura",
				"rarity": 0,
				"abilities": [
					{
						"text": "Passive abilities effects here are doubled.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 24,
							"tier": 2
						},
						"target": {
							"name": 9,
							"condition": 16
						}
					}
				]
			},
			LocationData.event.EnergyAura:{
				"name": "Energy Aura",
				"rarity": 0,
				"abilities": [
					{
						"text": "On enter abilities effects here are doubled.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 25,
							"tier": 2
						},
						"target": {
							"name": 9,
							"condition": 14
						}
					}
				]
			},
			LocationData.event.WildCaller:{
				"name": "Wild Caller",
				"rarity": 1,
				"abilities": [
					{
						"text": "Draw a card whenever you play a card here.",
						"trigger": {
							"name": 13
						},
						"keyword": {
							"name": 14,
							"tier": 1
						}
					}
				]
			},
			LocationData.event.Reinforcement:{
				"name": "Reinforcement",
				"rarity": 1,
				"abilities": [
					{
						"text": "On reveal: Each player draws 2 cards.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 14,
							"tier": 2
						}
					}
				]
			},
			LocationData.event.Stampede:{
				"name": "Stampede",
				"rarity": 1,
				"abilities": [
					{
						"text": "After you play a card here that costs 2 or less, destroy it.",
						"trigger": {
							"name": 13
						},
						"keyword": {
							"name": 11
						},
						"target": {
							"name": 18,
							"condition": 10,
							"conditionTier": 3
						}
					}
				]
			},
			LocationData.event.Spikes:{
				"name": "Spikes",
				"rarity": 1,
				"abilities": [
					{
						"text": "After you play a card here that costs 5 or more, destroy it.",
						"trigger": {
							"name": 13
						},
						"keyword": {
							"name": 11
						},
						"target": {
							"name": 18,
							"condition": 11,
							"conditionTier": 4
						}
					}
				]
			},
			LocationData.event.MagicAlluring:{
				"name": "Magic Alluring",
				"rarity": 1,
				"abilities": [
					{
						"text": "All card must be played here the turn this event revealed.",
						"trigger": {
							"name": 2,
							"condition": 7,
							"conditionTier": 0
						},
						"keyword": {
							"name": 29
						},
						"targetSlot": {
							"name": 6
						}
					}
				]
			},
			LocationData.event.Voidlands:{
				"name": "Voidlands",
				"rarity": 1,
				"abilities": [
					{
						"text": "Cards can't be played here after turn 4.",
						"trigger": {
							"name": 2,
							"condition": 3,
							"conditionTier": 5
						},
						"keyword": {
							"name": 29,
							"tier": 999
						},
						"targetSlot": {
							"name": 3
						}
					}
				]
			},
			LocationData.event.LoneGiant:{
				"name": "Lone Giant",
				"rarity": 2,
				"abilities": [
					{
						"text": "If you own only one card here, it gets +10 Power.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"tier": 10
						},
						"target": {
							"name": 9,
							"condition": 2
						}
					}
				]
			},
			LocationData.event.MirrorImage:{
				"name": "Mirror Image",
				"rarity": 2,
				"abilities": [
					{
						"text": "After you play a card here, add a copy of it to another lane.",
						"trigger": {
							"name": 13
						},
						"keyword": {
							"name": 15
						},
						"target": {
							"name": 18
						},
						"targetSlot": {
							"name": 4,
							"condition": 1
						}
					}
				]
			},
			LocationData.event.MindShift:{
				"name": "Mind Shift",
				"rarity": 2,
				"abilities": [
					{
						"text": "Player with the least power wins here.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 31
						}
					}
				]
			},
			LocationData.event.GrowthAura:{
				"name": "Growth Aura",
				"rarity": 2,
				"abilities": [
					{
						"text": "At the start of the turn, give +1 Power to each card here.",
						"trigger": {
							"name": 4
						},
						"keyword": {
							"name": 4,
							"tier": 1
						},
						"target": {
							"name": 9
						}
					}
				]
			},
			LocationData.event.BetrayalAura:{
				"name": "Betrayal Aura",
				"rarity": 3,
				"abilities": [
					{
						"text": "Card entering here swap owners.",
						"trigger": {
							"name": 13
						},
						"keyword": {
							"name": 30
						},
						"target": {
							"name": 18
						}
					}
				]
			},
			LocationData.event.PowerPortal:{
				"name": "Power Portal",
				"rarity": 3,
				"abilities": [
					{
						"text": "Grants this lane's power to other lanes.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"dynamicTier": 2
						},
						"target": {
							"name": 19
						}
					}
				]
			}
		}
	},
	LocationData.difficulty.CHALLENGE: {
		"events": {
			LocationData.event.Battlecry:{
				"name": "Battlecry",
				"rarity": 0,
				"abilities": [
					{
						"text": "Give +3 Power to the winning player's other lanes.",
						"trigger": {
							"name": 2,
							"condition": 20
						},
						"keyword": {
							"name": 16,
							"tier": 3
						},
						"target": {
							"name": 19
						}
					}
				]
			},
			LocationData.event.TrapHole:{
				"name": "Trap Hole",
				"rarity": 0,
				"abilities": [
					{
						"text": "Your first card played here is destroyed.",
						"trigger": {
							"name": 13,
							"condition": 2,
							"conditionTier": 1
						},
						"keyword": {
							"name": 11
						},
						"target": {
							"name": 18
						}
					}
				]
			},
			LocationData.event.Rampancy:{
				"name": "Rampancy",
				"rarity": 0,
				"abilities": [
					{
						"text": "Give +5 Power to each player's weakest card here.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"tier": 5
						},
						"target": {
							"name": 9,
							"condition": 4
						}
					}
				]
			},
			LocationData.event.Renewal:{
				"name": "Renewal",
				"rarity": 0,
				"abilities": [
					{
						"text": "On reveal: Trigger On enter abilities of all cards here again.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 26
						},
						"target": {
							"name": 9,
							"condition": 14
						}
					}
				]
			},
			LocationData.event.FairyDust:{
				"name": "Fairy Dust",
				"rarity": 0,
				"abilities": [
					{
						"text": "On reveal: Each player gains 1 Permanent mana.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 2,
							"tier": 1
						}
					}
				]
			},
			LocationData.event.ManaSurge:{
				"name": "Mana Surge",
				"rarity": 0,
				"abilities": [
					{
						"text": "On reveal: Each player gains 3 Temp mana.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 9,
							"tier": 3
						}
					}
				]
			},
			LocationData.event.Ambition:{
				"name": "Ambition",
				"rarity": 0,
				"abilities": [
					{
						"text": "At the end of the turn, the winning player draw a card.",
						"trigger": {
							"name": 6,
							"condition": 20
						},
						"keyword": {
							"name": 14,
							"tier": 1
						}
					}
				]
			},
			LocationData.event.HarmonicAura:{
				"name": "Harmonic Aura",
				"rarity": 0,
				"abilities": [
					{
						"text": "Passive abilities effects here are doubled.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 24,
							"tier": 2
						},
						"target": {
							"name": 9,
							"condition": 16
						}
					}
				]
			},
			LocationData.event.EnergyAura:{
				"name": "Energy Aura",
				"rarity": 0,
				"abilities": [
					{
						"text": "On enter abilities effects here are doubled.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 25,
							"tier": 2
						},
						"target": {
							"name": 9,
							"condition": 14
						}
					}
				]
			},
			LocationData.event.WildCaller:{
				"name": "Wild Caller",
				"rarity": 1,
				"abilities": [
					{
						"text": "Draw a card whenever you play a card here.",
						"trigger": {
							"name": 13
						},
						"keyword": {
							"name": 14,
							"tier": 1
						}
					}
				]
			},
			LocationData.event.Reinforcement:{
				"name": "Reinforcement",
				"rarity": 1,
				"abilities": [
					{
						"text": "On reveal: Each player draws 2 cards.",
						"trigger": {
							"name": 3
						},
						"keyword": {
							"name": 14,
							"tier": 2
						}
					}
				]
			},
			LocationData.event.Stampede:{
				"name": "Stampede",
				"rarity": 1,
				"abilities": [
					{
						"text": "After you play a card here that costs 2 or less, destroy it.",
						"trigger": {
							"name": 13
						},
						"keyword": {
							"name": 11
						},
						"target": {
							"name": 18,
							"condition": 10,
							"conditionTier": 3
						}
					}
				]
			},
			LocationData.event.MagicAlluring:{
				"name": "Magic Alluring",
				"rarity": 1,
				"abilities": [
					{
						"text": "All card must be played here the turn this event revealed.",
						"trigger": {
							"name": 2,
							"condition": 7,
							"conditionTier": 0
						},
						"keyword": {
							"name": 29
						},
						"targetSlot": {
							"name": 6
						}
					}
				]
			},
			LocationData.event.LoneGiant:{
				"name": "Lone Giant",
				"rarity": 2,
				"abilities": [
					{
						"text": "If you own only one card here, it gets +10 Power.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"tier": 10
						},
						"target": {
							"name": 9,
							"condition": 2
						}
					}
				]
			},
			LocationData.event.MirrorImage:{
				"name": "Mirror Image",
				"rarity": 2,
				"abilities": [
					{
						"text": "After you play a card here, add a copy of it to another lane.",
						"trigger": {
							"name": 13
						},
						"keyword": {
							"name": 15
						},
						"target": {
							"name": 18
						},
						"targetSlot": {
							"name": 4,
							"condition": 1
						}
					}
				]
			},
			LocationData.event.GrowthAura:{
				"name": "Growth Aura",
				"rarity": 2,
				"abilities": [
					{
						"text": "At the start of the turn, give +1 Power to each card here.",
						"trigger": {
							"name": 4
						},
						"keyword": {
							"name": 4,
							"tier": 1
						},
						"target": {
							"name": 9
						}
					}
				]
			},
			LocationData.event.PowerPortal:{
				"name": "Power Portal",
				"rarity": 3,
				"abilities": [
					{
						"text": "Grants this lane's power to other lanes.",
						"trigger": {
							"name": 2
						},
						"keyword": {
							"name": 16,
							"dynamicTier": 2
						},
						"target": {
							"name": 19
						}
					}
				]
			}
		}
	}
}
