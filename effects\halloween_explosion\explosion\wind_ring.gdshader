shader_type spatial;
render_mode cull_front, shadows_disabled, unshaded;

uniform sampler2D noise_sampler : filter_linear_mipmap, repeat_enable;
uniform float alpha : hint_range(0.0, 1.0, 0.1) = 1.0;
uniform float offset = 0.0;
uniform float dissolve : hint_range(0.0, 1.0, 0.1) = 0.5;
uniform float dissolve_smoothness : hint_range(0.0, 1.0, 0.1) = 0.5;

float fresnel(float amount, vec3 normal, vec3 view)
{
	return pow((1.0 - clamp(dot(normalize(normal), normalize(view)), 0.0, 1.0 )), amount);
}

// https://gist.github.com/companje/29408948f1e8be54dd5733a74ca49bb9
float map(float value, float min1, float max1, float min2, float max2) {
	return min2 + (value - min1) * (max2 - min2) / (max1 - min1);
}

void fragment() {
	float noise = texture(noise_sampler, UV * vec2(4.0, 0.25) + vec2(0.0, offset)).x;
	
	float map_dissolve_progress = map(dissolve, 0.0, 1.0, - dissolve_smoothness, 1.0 + dissolve_smoothness);
	float alpha_mask = clamp(smoothstep(map_dissolve_progress - dissolve_smoothness, map_dissolve_progress + dissolve_smoothness, noise - (1.0 - sin(UV.y * PI))), 0.0, 1.0);
	
	float f = (1.0 - fresnel(1.0, NORMAL, VIEW));
	ALPHA = f * UV.y * alpha * alpha_mask;
}
