class_name CardGrid
extends GridContainer

signal cardPressed
signal cardPressedIndex(index:int)
@onready var card_ui = preload("res://card_ui.tscn")

func displayCardCollection(cardCollection:CardCollection, player:Player = null, playerCardCount:int = -1):
	clearAll()
	
	var index = 0
	for card in cardCollection.getCards():
		var newCard = card_ui.instantiate()
		newCard.displayCard(card, isCardCastable(card, player, playerCardCount))
		newCard.pressed.connect(self.onCardPressed.bind(index))
		add_child(newCard)
		index += 1

func isCardCastable(card:Card, player:Player, playerCardCount:int) -> bool:
	if not player || playerCardCount == -1:
		return true
	if card.getCost() > player.getManaAndTempMana():
		return false
	if not card.getCastCondition() == CastCondition.NORMAL && playerCardCount <= 0:
		return false
	return true

func clearAll():
	for i in range(0, get_child_count()):
		get_child(i).queue_free()

func onCardPressed(index:int):
	cardPressed.emit()
	cardPressedIndex.emit(index)
