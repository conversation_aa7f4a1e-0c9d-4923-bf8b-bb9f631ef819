[gd_scene load_steps=4 format=3 uid="uid://div6wuxesh8rn"]

[ext_resource type="Script" path="res://scripts/BattleScene.gd" id="1_ujs5q"]
[ext_resource type="Script" path="res://scripts/ui/CardGridUI.gd" id="2_3jikp"]
[ext_resource type="Script" path="res://scripts/ui/LaneLabelGroup.gd" id="13_ljtfs"]

[node name="BattleScene" type="CanvasLayer"]
script = ExtResource("1_ujs5q")

[node name="DeckMain" type="Control" parent="."]
custom_minimum_size = Vector2(70, 90)
layout_mode = 3
anchor_left = 0.043
anchor_top = 0.901
anchor_right = 0.043
anchor_bottom = 0.901
offset_left = -46.536
offset_top = -36.8481
offset_right = 23.464
offset_bottom = 53.1519
grow_vertical = 0

[node name="ColorRectShadow" type="ColorRect" parent="DeckMain"]
custom_minimum_size = Vector2(70, 5)
layout_mode = 1
anchors_preset = -1
anchor_left = -0.286
anchor_top = -0.371
anchor_right = -0.286
anchor_bottom = -0.371
offset_left = 20.02
offset_top = 123.39
offset_right = 90.02
offset_bottom = 128.39
color = Color(0.0431373, 0.121569, 0.0431373, 1)

[node name="ColorRect" type="ColorRect" parent="DeckMain"]
custom_minimum_size = Vector2(70, 90)
layout_mode = 1
anchors_preset = -1
anchor_left = -0.286
anchor_top = -0.371
anchor_right = -0.286
anchor_bottom = -0.371
offset_left = 20.02
offset_top = 33.39
offset_right = 90.02
offset_bottom = 123.39
color = Color(0, 0.392157, 0, 1)

[node name="CardCount" type="Label" parent="DeckMain"]
layout_mode = 0
offset_left = 16.0
offset_top = 28.0
offset_right = 57.0
offset_bottom = 62.0
theme_override_font_sizes/font_size = 16
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="DeckOppo" type="Control" parent="."]
custom_minimum_size = Vector2(70, 90)
layout_mode = 3
anchor_left = 0.043
anchor_top = 0.093
anchor_right = 0.043
anchor_bottom = 0.093
offset_left = -46.536
offset_top = -69.264
offset_right = 23.464
offset_bottom = 20.736

[node name="ColorRectShadow" type="ColorRect" parent="DeckOppo"]
custom_minimum_size = Vector2(70, 5)
layout_mode = 1
anchors_preset = -1
anchor_left = -0.286
anchor_top = -0.371
anchor_right = -0.286
anchor_bottom = -0.371
offset_left = 20.02
offset_top = 118.39
offset_right = 90.02
offset_bottom = 123.39
color = Color(0.152941, 0.0431373, 0.0431373, 1)

[node name="ColorRect" type="ColorRect" parent="DeckOppo"]
custom_minimum_size = Vector2(70, 90)
layout_mode = 1
anchors_preset = -1
anchor_left = -0.286
anchor_top = -0.371
anchor_right = -0.286
anchor_bottom = -0.371
offset_left = 20.02
offset_top = 28.39
offset_right = 90.02
offset_bottom = 118.39
color = Color(0.545098, 0, 0, 1)

[node name="CardCount" type="Label" parent="DeckOppo"]
layout_mode = 0
offset_left = 14.0
offset_top = 22.0
offset_right = 55.0
offset_bottom = 56.0
theme_override_font_sizes/font_size = 16
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="GraveyardMain" type="Control" parent="."]
custom_minimum_size = Vector2(70, 90)
layout_mode = 3
anchor_left = 0.957
anchor_top = 0.909
anchor_right = 0.957
anchor_bottom = 0.909
offset_left = -34.464
offset_top = -37.032
offset_right = 35.536
offset_bottom = 52.968

[node name="ColorRect" type="ColorRect" parent="GraveyardMain"]
custom_minimum_size = Vector2(70, 90)
layout_mode = 1
anchors_preset = -1
anchor_left = -0.286
anchor_top = -0.371
anchor_right = -0.286
anchor_bottom = -0.371
offset_left = 20.02
offset_top = 33.39
offset_right = 90.02
offset_bottom = 123.39
color = Color(0.00820202, 0.00820203, 0.00820202, 1)

[node name="CardCount" type="Label" parent="GraveyardMain"]
layout_mode = 0
offset_left = 14.0
offset_top = 27.0
offset_right = 55.0
offset_bottom = 61.0
theme_override_font_sizes/font_size = 16
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="GraveyardOppo" type="Control" parent="."]
custom_minimum_size = Vector2(70, 90)
layout_mode = 3
anchor_left = 0.957
anchor_top = 0.093
anchor_right = 0.957
anchor_bottom = 0.093
offset_left = -34.464
offset_top = -69.264
offset_right = 35.536
offset_bottom = 20.736

[node name="ColorRect" type="ColorRect" parent="GraveyardOppo"]
custom_minimum_size = Vector2(70, 90)
layout_mode = 1
anchors_preset = -1
anchor_left = -0.286
anchor_top = -0.371
anchor_right = -0.286
anchor_bottom = -0.371
offset_left = 20.02
offset_top = 33.39
offset_right = 90.02
offset_bottom = 123.39
color = Color(0.00820202, 0.00820203, 0.00820202, 1)

[node name="CardCount" type="Label" parent="GraveyardOppo"]
layout_mode = 0
offset_left = 14.0
offset_top = 27.0
offset_right = 55.0
offset_bottom = 61.0
theme_override_font_sizes/font_size = 16
text = "0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HandMain" type="GridContainer" parent="."]
anchors_preset = -1
anchor_left = 0.358
anchor_top = 0.907
anchor_right = 0.358
anchor_bottom = 0.907
offset_left = -555.36
offset_top = -36.56
offset_right = 754.64
offset_bottom = 53.44
grow_horizontal = 2
grow_vertical = 0
size_flags_horizontal = 4
size_flags_vertical = 8
columns = 20
script = ExtResource("2_3jikp")

[node name="HandOppo" type="GridContainer" parent="."]
anchors_preset = -1
anchor_left = 0.355
anchor_top = 0.091
anchor_right = 0.355
anchor_bottom = 0.091
offset_left = -552.6
offset_top = -69.28
offset_right = 749.4
offset_bottom = 20.72
grow_horizontal = 2
size_flags_horizontal = 4
size_flags_vertical = 8
columns = 20
script = ExtResource("2_3jikp")

[node name="ManaA" type="Label" parent="."]
anchors_preset = -1
anchor_left = 0.894
anchor_top = 0.959
anchor_right = 0.894
anchor_bottom = 0.959
offset_left = -278.888
offset_top = -27.432
offset_right = 21.1119
offset_bottom = -4.43201
text = "Mana"
horizontal_alignment = 2
vertical_alignment = 2

[node name="ManaB" type="Label" parent="."]
anchors_preset = -1
anchor_left = 0.894
anchor_top = 0.041
anchor_right = 0.894
anchor_bottom = 0.041
offset_left = -289.888
offset_top = -11.568
offset_right = 23.1119
offset_bottom = 11.432
text = "Mana"
horizontal_alignment = 2

[node name="ScoreSnap" type="Control" parent="."]
layout_mode = 3
anchor_left = 0.035
anchor_top = 0.443
anchor_right = 0.035
anchor_bottom = 0.443
offset_left = -30.32
offset_top = -254.064
offset_right = -30.32
offset_bottom = -254.064

[node name="Method" type="Label" parent="ScoreSnap"]
layout_mode = 0
offset_left = 13.0
offset_top = 210.0
offset_right = 63.0
offset_bottom = 233.0
size_flags_horizontal = 0
text = "Snap"

[node name="ScoreA" type="Label" parent="ScoreSnap"]
layout_mode = 0
offset_left = 63.0
offset_top = 210.0
offset_right = 91.0
offset_bottom = 233.0
theme_override_colors/font_color = Color(0.513726, 1, 0.482353, 1)
text = "0"
horizontal_alignment = 1

[node name="ScoreB" type="Label" parent="ScoreSnap"]
layout_mode = 0
offset_left = 123.0
offset_top = 210.0
offset_right = 151.0
offset_bottom = 233.0
theme_override_colors/font_color = Color(1, 0.368627, 0.301961, 1)
text = "0"
horizontal_alignment = 1

[node name="ScoreDraw" type="Label" parent="ScoreSnap"]
layout_mode = 0
offset_left = 93.0
offset_top = 210.0
offset_right = 121.0
offset_bottom = 233.0
text = "0"
horizontal_alignment = 1

[node name="ScoreQB" type="Control" parent="."]
layout_mode = 3
anchor_left = 0.032
anchor_top = 0.489
anchor_right = 0.032
anchor_bottom = 0.489
offset_left = -26.864
offset_top = -251.872
offset_right = -26.864
offset_bottom = -251.872

[node name="Method" type="Label" parent="ScoreQB"]
layout_mode = 0
offset_left = 15.0
offset_top = 194.0
offset_right = 65.0
offset_bottom = 217.0
text = "QB"

[node name="ScoreA" type="Label" parent="ScoreQB"]
layout_mode = 0
offset_left = 65.0
offset_top = 194.0
offset_right = 93.0
offset_bottom = 217.0
theme_override_colors/font_color = Color(0.513726, 1, 0.482353, 1)
text = "0"
horizontal_alignment = 1

[node name="ScoreB" type="Label" parent="ScoreQB"]
layout_mode = 0
offset_left = 125.0
offset_top = 194.0
offset_right = 153.0
offset_bottom = 217.0
theme_override_colors/font_color = Color(1, 0.368627, 0.301961, 1)
text = "0"
horizontal_alignment = 1

[node name="ScoreDraw" type="Label" parent="ScoreQB"]
layout_mode = 0
offset_left = 95.0
offset_top = 194.0
offset_right = 123.0
offset_bottom = 217.0
text = "0"
horizontal_alignment = 1

[node name="ScoreSum" type="Control" parent="."]
layout_mode = 3
anchor_left = 0.033
anchor_top = 0.544
anchor_right = 0.033
anchor_bottom = 0.544
offset_left = -28.016
offset_top = -255.512
offset_right = -28.016
offset_bottom = -255.512

[node name="Method" type="Label" parent="ScoreSum"]
layout_mode = 0
offset_left = 14.0
offset_top = 175.0
offset_right = 64.0
offset_bottom = 198.0
text = "Sum"

[node name="ScoreA" type="Label" parent="ScoreSum"]
layout_mode = 0
offset_left = 64.0
offset_top = 175.0
offset_right = 92.0
offset_bottom = 198.0
theme_override_colors/font_color = Color(0.513726, 1, 0.482353, 1)
text = "0"
horizontal_alignment = 1

[node name="ScoreB" type="Label" parent="ScoreSum"]
layout_mode = 0
offset_left = 124.0
offset_top = 175.0
offset_right = 152.0
offset_bottom = 198.0
theme_override_colors/font_color = Color(1, 0.368627, 0.301961, 1)
text = "0"
horizontal_alignment = 1

[node name="ScoreDraw" type="Label" parent="ScoreSum"]
layout_mode = 0
offset_left = 94.0
offset_top = 175.0
offset_right = 122.0
offset_bottom = 198.0
text = "0"
horizontal_alignment = 1

[node name="LaneLeft" type="Control" parent="."]
layout_mode = 3
anchor_left = 0.274
anchor_top = 0.5
anchor_right = 0.274
anchor_bottom = 0.5
offset_left = -315.648
offset_top = -324.0
offset_right = -315.648
offset_bottom = -324.0
script = ExtResource("13_ljtfs")

[node name="CardSlotUI" type="GridContainer" parent="LaneLeft"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 246.0
offset_top = 183.0
offset_right = 392.0
offset_bottom = 463.0
grow_horizontal = 2
grow_vertical = 0
size_flags_horizontal = 0
size_flags_vertical = 8
columns = 2
script = ExtResource("2_3jikp")

[node name="MainPower" type="Label" parent="LaneLeft"]
layout_mode = 0
offset_left = 299.0
offset_top = 475.0
offset_right = 340.0
offset_bottom = 509.0
theme_override_colors/font_color = Color(0.513678, 1, 0.481751, 1)
theme_override_font_sizes/font_size = 24
text = "MP"
horizontal_alignment = 1

[node name="OppoPower" type="Label" parent="LaneLeft"]
layout_mode = 0
offset_left = 299.0
offset_top = 136.0
offset_right = 340.0
offset_bottom = 170.0
theme_override_colors/font_color = Color(1, 0.367788, 0.300217, 1)
theme_override_font_sizes/font_size = 24
text = "OP"
horizontal_alignment = 1

[node name="LaneCen" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -576.0
offset_top = -324.0
offset_right = -576.0
offset_bottom = -324.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("13_ljtfs")

[node name="CardSlotUI" type="GridContainer" parent="LaneCen"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 498.0
offset_top = 183.0
offset_right = 644.0
offset_bottom = 463.0
grow_horizontal = 2
grow_vertical = 0
size_flags_horizontal = 0
size_flags_vertical = 8
columns = 2
script = ExtResource("2_3jikp")

[node name="MainPower" type="Label" parent="LaneCen"]
layout_mode = 0
offset_left = 551.0
offset_top = 475.0
offset_right = 592.0
offset_bottom = 509.0
theme_override_colors/font_color = Color(0.513678, 1, 0.481751, 1)
theme_override_font_sizes/font_size = 24
text = "MP"
horizontal_alignment = 1

[node name="OppoPower" type="Label" parent="LaneCen"]
layout_mode = 0
offset_left = 551.0
offset_top = 136.0
offset_right = 592.0
offset_bottom = 170.0
theme_override_colors/font_color = Color(1, 0.368627, 0.301961, 1)
theme_override_font_sizes/font_size = 24
text = "OP"
horizontal_alignment = 1

[node name="LaneRight" type="Control" parent="."]
layout_mode = 3
anchor_left = 0.717
anchor_top = 0.5
anchor_right = 0.717
anchor_bottom = 0.5
offset_left = -825.984
offset_top = -324.0
offset_right = -825.984
offset_bottom = -324.0
script = ExtResource("13_ljtfs")

[node name="CardSlotUI" type="GridContainer" parent="LaneRight"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 751.0
offset_top = 183.0
offset_right = 897.0
offset_bottom = 463.0
grow_horizontal = 2
grow_vertical = 0
size_flags_horizontal = 0
size_flags_vertical = 8
columns = 2
script = ExtResource("2_3jikp")

[node name="MainPower" type="Label" parent="LaneRight"]
layout_mode = 0
offset_left = 805.0
offset_top = 475.0
offset_right = 846.0
offset_bottom = 509.0
theme_override_colors/font_color = Color(0.513678, 1, 0.481751, 1)
theme_override_font_sizes/font_size = 24
text = "MP"
horizontal_alignment = 1

[node name="OppoPower" type="Label" parent="LaneRight"]
layout_mode = 0
offset_left = 805.0
offset_top = 136.0
offset_right = 846.0
offset_bottom = 170.0
theme_override_colors/font_color = Color(1, 0.368627, 0.301961, 1)
theme_override_font_sizes/font_size = 24
text = "OP"
horizontal_alignment = 1

[node name="PassTurnButton" type="Button" parent="."]
anchors_preset = -1
anchor_left = 0.921
anchor_top = 0.496
anchor_right = 0.921
anchor_bottom = 0.496
offset_left = -84.3201
offset_top = -31.68
offset_right = 81.6799
offset_bottom = 34.32

[node name="BackButton" type="Button" parent="."]
anchors_preset = -1
anchor_left = 0.053
anchor_top = 0.692
anchor_right = 0.053
anchor_bottom = 0.692
offset_left = -56.76
offset_top = -111.36
offset_right = 58.24
offset_bottom = -80.36
text = "Main menu"
metadata/_edit_use_anchors_ = true

[node name="QuitButton" type="Button" parent="."]
anchors_preset = -1
anchor_left = 0.053
anchor_top = 0.737
anchor_right = 0.053
anchor_bottom = 0.737
offset_left = -56.76
offset_top = -111.96
offset_right = 58.24
offset_bottom = -80.96
text = "Quit"
metadata/_edit_use_anchors_ = true

[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="QuitButton" to="." method="_on_quit_button_pressed"]
