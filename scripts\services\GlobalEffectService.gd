class_name GlobalEffectService

static var CardPowerModifier: Dictionary = {}
static var CardCostModifier: Dictionary = {}
static var EffectTierMultiplier: Dictionary = {}
static var EffectTierModifier: Dictionary = {}
static var LaneCardSlotStatus: Dictionary = {}
static var LaneWinConditionEffect: Dictionary = {}

# Set everything to zero before resolve the stack 
static func resetAll() -> void:
	CardPowerModifier = {}
	CardCostModifier = {}
	EffectTierMultiplier = {}
	EffectTierModifier = {}
	# LaneCardSlotStatus = {}
	LaneWinConditionEffect = {}


static func setLaneWinConditionEffect(sourceAbility: Ability, lane: Lane, winCondition: int) -> void:
	LaneWinConditionEffect[sourceAbility] = {
		"lane": lane,
		"winCondition": winCondition
	}


static func getLaneWinConditionEffect(lane: Lane) -> int:
	var winCondition = LaneWinCondition.MORE_POWER
	for source in LaneWinConditionEffect:
		if LaneWinConditionEffect[source]["lane"] == lane:
			winCondition = LaneWinConditionEffect[source]["winCondition"]
	return winCondition


static func addEffectTierMultiplier(sourceAbility: Ability, targetCards: Array[Card], keywordName: int, triggerName: int, multiplier: int) -> void:
	EffectTierMultiplier[sourceAbility] = {
		"targetCards": targetCards,
		"keywordName": keywordName,
		"triggerName": triggerName,
		"multiplier": multiplier
	}


static func getEffectTierMultiplier(triggerCard: Card, keywordName: int, triggerName: int) -> int:
	var effectTierMultiplier = 1
	for source in EffectTierMultiplier:
		if EffectTierMultiplier[source]["targetCards"].has(triggerCard):
			if EffectTierMultiplier[source]["keywordName"] == keywordName or EffectTierMultiplier[source]["triggerName"] == triggerName:
				effectTierMultiplier *= EffectTierMultiplier[source]["multiplier"]
	return effectTierMultiplier


static func addEffectTierModifier(sourceAbility: Ability, targetCards: Array[Card], keywordName: int, triggerName: int, modifier: int) -> void:
	# print("addEffectTierModifier: " + str(keywordName) + " " + str(modifier))
	EffectTierModifier[sourceAbility] = {
		"targetCards": targetCards,
		"keywordName": keywordName,
		"triggerName": triggerName,
		"modifier": modifier
	}


static func getEffectTierModifier(triggerCard: Card, keywordName: int, triggerName: int) -> int:
	var effectTierModifier = 0
	for source in EffectTierModifier:
		if EffectTierModifier[source]["targetCards"].has(triggerCard):
			if EffectTierModifier[source]["keywordName"] == keywordName or EffectTierModifier[source]["triggerName"] == triggerName:
				# print("EffectTierModifier: " + str(EffectTierModifier[source]["modifier"]))
				effectTierModifier += EffectTierModifier[source]["modifier"]
	return effectTierModifier


# Modify CardPowerModifier value
static func modifyCardPowerModifier(sourceAbility: Ability, targetCards: Array[Card], modifier: int) -> void:
	CardPowerModifier[sourceAbility] = {
		"targetCards": targetCards,
		"modifier": modifier
	}


# Return CardPowerModifier that affect the card
static func getCardPowerModifier(targetCard: Card) -> int:
	var powerModifier = 0
	for source in CardPowerModifier:
		if CardPowerModifier[source]["targetCards"].has(targetCard):
			powerModifier += CardPowerModifier[source]["modifier"]
	return powerModifier


# Modify CardCostModifier value
static func modifyCardCostModifier(sourceAbility: Ability, targetCards: Array[Card], modifier: int) -> void:
	CardCostModifier[sourceAbility] = {
		"targetCards": targetCards,
		"modifier": modifier
	}


# Return CardCostModifier that affect the card
static func getCardCostModifier(targetCard: Card) -> int:
	var costModifier = 0
	for source in CardCostModifier:
		if CardCostModifier[source]["targetCards"].has(targetCard):
			costModifier += CardCostModifier[source]["modifier"]
	return costModifier


# For EMPOWER, GROWTH ability
static func modifyCardEffectPowerEvent(_sourceAbility: Ability, _targetCards: Array[Card], _amount: int) -> void:
	modifyCardPowerModifier(_sourceAbility, _targetCards, _amount)

# For CONVOKE ability
static func modifyCardEffectCostEvent(_sourceAbility: Ability, _targetCards: Array[Card], _amount: int) -> void:
	modifyCardCostModifier(_sourceAbility, _targetCards, _amount)


static func updateLaneCardSlotStatus(_sourceAbility: Ability, _targetSlots: Array[LaneCardSlot], _status: int, _turn: int) -> void:
	# print("updateLaneCardSlotStatus: " + str(_targetSlots[0].getSlotIndex()))
	for s in _targetSlots:
		# print("updateLaneCardSlotStatus: " + str(s))
		LaneCardSlotStatus[s] = {
			"sourceAbility": _sourceAbility,
			"status": _status,
			"expiredTurn": _turn
		}

static func getLaneCardSlotStatus(targetSlot: LaneCardSlot) -> int:
	var status: int = LaneCardSlot.IDLE
	# print("expire targetSlot lane "+str(LaneCardSlotStatus))
	if LaneCardSlotStatus.has(targetSlot):
		# print("status "+str(LaneCardSlotStatus[targetSlot]["status"]))
		if LaneCardSlotStatus[targetSlot]["expiredTurn"] <= TurnService.TurnCount:
			# print("in expired turn "+str(LaneCardSlotStatus[targetSlot]["expiredTurn"])+" "+str(TurnService.TurnCount))
			LaneCardSlotStatus[targetSlot]["status"] = LaneCardSlot.IDLE
		else:
			status = LaneCardSlotStatus[targetSlot]["status"]
	# print("getLaneCardSlotStatus: " + str(status))
	return status


static func printCurrentGlobalEffect():
	print("== GLOBAL EFFECTS ==")
	if EffectTierMultiplier.size() > 0:
		print("EffectTierMultiplier")
		for source in EffectTierMultiplier:
			if EffectTierMultiplier[source]["targetCards"].size() > 0:
				var targetCardNames: String = "["
				for card: Card in EffectTierMultiplier[source]["targetCards"]:
					targetCardNames += card.getName() + ", "
				targetCardNames += "]"
				print(" - " + source.getParentCard().getName() + ": K" + Keyword.KeywordInfo[EffectTierMultiplier[source]["keywordName"]]["label"] + " T" + Trigger.TriggerInfo[EffectTierMultiplier[source]["triggerName"]]["label"] + " *" + str(EffectTierMultiplier[source]["multiplier"]) + " " + targetCardNames)
	
	if EffectTierModifier.size() > 0:
		print("EffectTierModifier")
		for source in EffectTierModifier:
			if EffectTierModifier[source]["targetCards"].size() > 0:
				var targetCardNames: String = "["
				for card: Card in EffectTierModifier[source]["targetCards"]:
					targetCardNames += card.getName() + ", "
				targetCardNames += "]"
				print(" - " + source.getParentCard().getName() + ": K" + Keyword.KeywordInfo[EffectTierModifier[source]["keywordName"]]["label"] + " T" + Trigger.TriggerInfo[EffectTierModifier[source]["triggerName"]]["label"] + " +" + str(EffectTierModifier[source]["modifier"]) + " " + targetCardNames)

	if CardPowerModifier.size() > 0:
		print("CardPowerModifier")
		for source in CardPowerModifier:
			if CardPowerModifier[source]["targetCards"].size() > 0:
				var targetNames: String = "["
				for card: Card in CardPowerModifier[source]["targetCards"]:
					targetNames += card.getName()
					if card != CardPowerModifier[source]["targetCards"][CardPowerModifier[source]["targetCards"].size() - 1]: targetNames += ", "
				targetNames += "]"
				print(" - " + source.getParentCard().getName() + ": +" + str(CardPowerModifier[source]["modifier"]) + " " + targetNames)
	
	if CardCostModifier.size() > 0:
		print("CardCostModifier")
		for source in CardCostModifier:
			if CardCostModifier[source]["targetCards"].size() > 0:
				var targetNames: String = "["
				for card: Card in CardCostModifier[source]["targetCards"]:
					targetNames += card.getName()
					if card != CardCostModifier[source]["targetCards"][CardCostModifier[source]["targetCards"].size() - 1]: targetNames += ", "
				targetNames += "]"
				print(" - " + source.getParentCard().getName() + ": +" + str(CardCostModifier[source]["modifier"]) + " " + targetNames)
