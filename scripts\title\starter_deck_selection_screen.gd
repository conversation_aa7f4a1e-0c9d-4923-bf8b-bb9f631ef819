extends Control

@onready var option_screen: Control = $OptionScreen

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	Const.CURRENT_SCREEN = "starter_deck_selection_screen"
	pass # Replace with function body.

# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass

func _on_start_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/map/world_map_screen.tscn")

func _on_quit_button_pressed() -> void:
	get_tree().quit()


func _on_settings_button_pressed() -> void:
	option_screen.visible = true
	pass # Replace with function body.
