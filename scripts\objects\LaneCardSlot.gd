class_name LaneCardSlot
extends CardCollectionUIController

enum {
	IDLE,
	IDLE_PLAYER_SIDE,
	IDLE_ENEMY_SIDE,
	DISABLED,
	CASTABLE,
	CASTABLE_PLAYER_SIDE,
	READY_TO_CAST,
	READY_TO_CAST_PLAYER_SIDE,
	SACRIFIABLE,
	READY_TO_SAC,
	SYMBIONTABLE,
	READY_TO_SYM,
	BOUNCEABLE
	#OCCUPIED
}

var state:int = IDLE
signal slotStateChange(state:int)
var lane_index:int = 0

# Called when the node enters the scene tree for the first time.w
func _ready():
	super()

func addUI(p_cards:Array[Card], fromHand: bool) -> void:
	add(p_cards)
	#self.cardCollection.add(p_cards) <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< tmr
	for card in p_cards:
		var card3d = card_3dui.instantiate()
		card3d.displayCard(card)
		add_child(card3d)
		var movingQueue
		if getOwner().isOnMainSide():
			movingQueue = battleNode.find_child("Player").movingQueue
		else:
			movingQueue = battleNode.find_child("Enemy").movingQueue
		if movingQueue.get_child_count() > 0 and fromHand:
			card3d.currentState = card3d.states.PLAYING
			card3d.transform.origin = movingQueue.get_child(0).global_position
			card3d.rotation = movingQueue.get_child(0).rotation
			#card3d.scale = movingQueue.get_child(0).scale
			movingQueue.get_child(0).free()
			card3d.animationPlayer.play("PlayCard")
			if Const.MODE == 0:
				card3d.animationPlayer.advance(1.0)
			elif Const.MODE == 1:
				pass
				#await card3d.animationPlayer.animation_finished
			card3d.currentState = card3d.states.IDLE
		else:
			if movingQueue.get_child_count() > 0:
				card3d.currentState = card3d.states.PLAYING
				card3d.transform.origin = movingQueue.get_child(0).global_position
				movingQueue.get_child(0).free()
			card3d.animationPlayer.play("PlayCard")
			if Const.MODE == 0:
				card3d.animationPlayer.advance(1.0)
			elif Const.MODE == 1:
				await card3d.animationPlayer.animation_finished
			card3d.currentState = card3d.states.IDLE
		#var movingQueue = battleNode.find_child("MovingQueue")
		#if movingQueue.get_child_count() > 0:
			#card3d.transform.origin = movingQueue.get_child(0).global_position
			#movingQueue.get_child(0).queue_free()
		#card3d.transform.origin = TurnService.ActivePlayer.getHand().global_position
		card3d.cardSelected.connect(battleNode.inspect_card)
		battleNode.inspecting.connect(card3d.change_collision_mode)
		#await get_tree().create_timer(Const.AWAIT_TIME * 0.1).timeout
	for card in get_children():
		#var tween = card.create_tween()
		if card.get_index() > 0:
			card.visible = false
		#card.cardBorder.visible = false
		card.playerParticle.scale_amount_max = 1
		card.playerParticle.scale_amount_min = 1
		card.enemyParticle.scale_amount_max = 1
		card.enemyParticle.scale_amount_min = 1
		#if card.cardOwner.name == "Player":
			#card.playerParticle.visible = true
			#card.enemyParticle.visible = false
		#else:
			#card.enemyParticle.visible = true
			#card.playerParticle.visible = false
		#card.powerParticle.visible = true
		card.brightness_up(false)
		card.name_visible(false)
		card.lightBorder(true)
		card.cost_visible(false)
		card.set_layer(20, false)
		#card.set_layer(1, true)
		#card3d.transform.origin = getOwner().getHand().global_position
		#await get_tree().create_timer(Const.AWAIT_TIME * 0.1).timeout

func addUISingle(p_cards:Card) -> void:
	addSingle(p_cards)
	#self.cardCollection.add(p_cards) <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< tmr
	var card3d = card_3dui.instantiate()
	card3d.displayCard(p_cards)
	add_child(card3d)
	var movingQueue
	if getOwner().isOnMainSide():
		movingQueue = battleNode.find_child("Player").movingQueue
	else:
		movingQueue = battleNode.find_child("Enemy").movingQueue
	if movingQueue.get_child_count() > 0:
		card3d.currentState = card3d.states.PLAYING
		card3d.transform.origin = movingQueue.get_child(0).global_position
		card3d.rotation = movingQueue.get_child(0).rotation
		#card3d.scale = movingQueue.get_child(0).scale
		movingQueue.get_child(0).free()
		card3d.animationPlayer.play("PlayCard")
		if Const.MODE == 0:
				card3d.animationPlayer.advance(1.0)
		elif Const.MODE == 1:
				await card3d.animationPlayer.animation_finished
		card3d.currentState = card3d.states.IDLE
	else:
		card3d.animationPlayer.play("PlayCard")
		if Const.MODE == 0:
			card3d.animationPlayer.advance(1.0)
		elif Const.MODE == 1:
			await card3d.animationPlayer.animation_finished
		card3d.currentState = card3d.states.IDLE
	#var movingQueue = battleNode.find_child("MovingQueue")
	#if movingQueue.get_child_count() > 0:
		#card3d.transform.origin = movingQueue.get_child(0).global_position
		#movingQueue.get_child(0).queue_free()
	#card3d.transform.origin = TurnService.ActivePlayer.getHand().global_position
	card3d.cardSelected.connect(battleNode.inspect_card)
	battleNode.inspecting.connect(card3d.change_collision_mode)
	#await get_tree().create_timer(Const.AWAIT_TIME * 0.1).timeout
	for card in get_children():
		#var tween = card.create_tween()
		if card.get_index() > 0:
			card.visible = false
		#card.cardBorder.visible = false
		card.playerParticle.scale_amount_max = 1
		card.playerParticle.scale_amount_min = 1
		card.enemyParticle.scale_amount_max = 1
		card.enemyParticle.scale_amount_min = 1
		#if card.cardOwner.name == "Player":
			#card.playerParticle.visible = true
			#card.enemyParticle.visible = false
		#else:
			#card.enemyParticle.visible = true
			#card.playerParticle.visible = false
		#card.powerParticle.visible = true
		card.brightness_up(false)
		card.name_visible(false)
		card.lightBorder(true)
		card.cost_visible(false)
		card.set_layer(20, false)
		#card.set_layer(1, true)
		#card3d.transform.origin = getOwner().getHand().global_position
		#await get_tree().create_timer(Const.AWAIT_TIME * 0.1).timeout

func add(p_cards:Array[Card]) -> void:
	cardCollection.add(p_cards)

func addSingle(p_cards:Card) -> void:
	cardCollection.addSingle(p_cards)

func getState() -> int:
	return self.state

func setState(_state:int) -> void:
	self.state = _state
	slotStateChange.emit(_state)

func getIndex() -> int:
	return self.lane_index
