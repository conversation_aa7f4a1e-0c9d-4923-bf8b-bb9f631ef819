[gd_scene load_steps=118 format=3 uid="uid://g21njb2a0uei"]

[ext_resource type="Script" path="res://scripts/ui/Card3DUI.gd" id="1_dlg4w"]
[ext_resource type="Texture2D" uid="uid://bb07iqk0lgfiv" path="res://Assets/3D models/DRC_Card_Design_08_UI_CMN_TagIcon.png" id="3_f8aow"]
[ext_resource type="FontFile" uid="uid://ousmejoyyg35" path="res://Assets/Fonts/Montserrat-Black.ttf" id="3_hshn5"]
[ext_resource type="FontFile" uid="uid://qgdno03mkq4f" path="res://Assets/Fonts/bBumbuSushi.ttf" id="3_lfmlx"]
[ext_resource type="Texture2D" uid="uid://dori38csh2wmu" path="res://Assets/3D models/DRC_Card_Design_08_UI_CMN_TagBg.png" id="3_siap0"]
[ext_resource type="Shader" uid="uid://dmb18tjtgw5aq" path="res://igniteGlow.tres" id="3_tjup3"]
[ext_resource type="Texture2D" uid="uid://6xflbkhvbqi" path="res://Assets/Particle/InlaneVfx3.png" id="4_muwd4"]
[ext_resource type="FontFile" uid="uid://b5jcvhncb6lk8" path="res://Assets/Fonts/Montserrat-BlackItalic.ttf" id="4_ql56j"]
[ext_resource type="FontFile" uid="uid://c7t1s61ijoui8" path="res://Assets/Fonts/AnekDevanagari_Expanded-Bold.ttf" id="4_tpole"]
[ext_resource type="Script" path="res://addons/drag_and_snap/nodes/draggable.gd" id="5_4eqpv"]
[ext_resource type="Texture2D" uid="uid://bwgxwvwgl2ydk" path="res://Assets/SilencedVFX.png" id="8_heioj"]
[ext_resource type="Texture2D" uid="uid://b8ipf1rkx81l0" path="res://Assets/CardPics/GL.jpg" id="8_jexhf"]
[ext_resource type="Texture2D" uid="uid://busm5teuncg02" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardCost.png" id="14_ewff6"]
[ext_resource type="Texture2D" uid="uid://cupg7kqh2h68u" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardHeader.png" id="15_e14iv"]
[ext_resource type="Texture2D" uid="uid://gxyksdbb8n6j" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardPower.png" id="16_jh8xd"]
[ext_resource type="Texture2D" uid="uid://de12upeq5a5b5" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardFaction.png" id="17_qhp24"]
[ext_resource type="Texture2D" uid="uid://bnkfwry6xp8g4" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_CardRarity_Texture_Test01.png" id="18_l5ddj"]
[ext_resource type="Texture2D" uid="uid://cbupndnm5yqi5" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardCompanion.png" id="19_8h4f7"]
[ext_resource type="Texture2D" uid="uid://d0nibat8fxxxo" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_CardPicFG_Texture_Test01.png" id="20_yxnhn"]
[ext_resource type="Texture2D" uid="uid://5umvgofvyapu" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardBack.png" id="21_bglvw"]
[ext_resource type="Texture2D" uid="uid://dpa6sdjskuro3" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardGradient.png" id="22_b4rcw"]
[ext_resource type="Material" uid="uid://bn560w5oht3yf" path="res://effects/force_field/materials/force_field_mat.tres" id="25_hogme"]
[ext_resource type="Texture2D" uid="uid://cfani3jl2h0dg" path="res://Assets/3D models/DRC_Card_Model_Test03_CardBack_DRC_Card_Design_08_UI_CMN_CardBack.png" id="28_kxw4s"]
[ext_resource type="Texture2D" uid="uid://bxlo1dluc4077" path="res://Assets/3D models/DRC_Card_Model_Test03_CardBase_DRC_Card_Design_08_UI_CMN_CardBack.png" id="29_x61ul"]
[ext_resource type="Texture2D" uid="uid://rbxlqmm5j2r4" path="res://Assets/3D models/DRC_Card_Model_Test03_CardPicFG_DRC_Card_CardPicFG_Texture_Test01.png" id="31_c864i"]
[ext_resource type="Texture2D" uid="uid://bc882dp6j0iwa" path="res://Assets/3D models/DRC_Card_Model_Test03_CardPicFgPLayed_DRC_Card_CardPicFG_Texture_Test01.png" id="32_bo0v1"]

[sub_resource type="Animation" id="Animation_rbt1r"]
length = 0.001
tracks/0/type = "bezier"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:position:z")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(-0.816845, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/1/type = "bezier"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:modulate:a")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath(".:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_rq8gn"]
resource_name = "float"
tracks/0/type = "bezier"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:position:z")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"handle_modes": PackedInt32Array(0, 0),
"points": PackedFloat32Array(-0.152449, -0.25, 0, 0.25, 0, 0.931496, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0, 1)
}
tracks/1/type = "bezier"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:modulate:a")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"handle_modes": PackedInt32Array(0, 0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0, 0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0, 1)
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath(".:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_28t7y"]
_data = {
"RESET": SubResource("Animation_rbt1r"),
"float": SubResource("Animation_rq8gn")
}

[sub_resource type="CanvasItemMaterial" id="CanvasItemMaterial_odv6a"]
blend_mode = 1
light_mode = 1

[sub_resource type="ViewportTexture" id="ViewportTexture_04poi"]
resource_name = "_albedo"
viewport_path = NodePath("AbilityOnCard/AbilitySubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_titv1"]
resource_local_to_scene = true
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_04poi")
texture_filter = 1

[sub_resource type="PlaneMesh" id="PlaneMesh_8wjqt"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_2mthu"]
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("14_ewff6")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_2vtsw"]
resource_name = "CardCost"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("14_ewff6")

[sub_resource type="ArrayMesh" id="ArrayMesh_o7oa7"]
_surfaces = [{
"aabb": AABB(0.0452289, 1.57067, 0.593072, 1.00508e-05, 0.933744, 1.18816),
"format": 34896613377,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1.29048e-08, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"name": "CardCost",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(84, 1, 46, 241, 106, 243, 0, 0, 0, 0, 0, 0, 255, 255, 0, 0, 84, 1, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 84, 1, 255, 255, 255, 127, 0, 0, 194, 0, 255, 127, 255, 255, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 194, 0, 255, 127, 0, 0, 0, 0, 194, 0, 255, 127, 255, 127, 0, 0, 84, 1, 255, 255, 255, 191, 0, 0, 145, 0, 255, 63, 255, 255, 0, 0, 0, 0, 0, 0, 255, 63, 0, 0, 242, 0, 255, 191, 0, 0, 0, 0, 84, 1, 255, 255, 255, 63, 0, 0, 242, 0, 255, 191, 255, 255, 0, 0, 0, 0, 0, 0, 255, 191, 0, 0, 145, 0, 255, 63, 0, 0, 0, 0, 194, 0, 255, 127, 255, 63, 0, 0, 194, 0, 255, 127, 255, 191, 0, 0, 242, 0, 255, 191, 255, 127, 0, 0, 145, 0, 255, 63, 255, 127, 0, 0, 145, 0, 255, 63, 255, 191, 0, 0, 242, 0, 255, 191, 255, 191, 0, 0, 242, 0, 255, 191, 255, 63, 0, 0, 145, 0, 255, 63, 255, 63, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_7urhl"]
resource_name = "DRC_Card_Modell_Test01_Plane_001"
_surfaces = [{
"aabb": AABB(0.0452289, 1.57067, 0.593072, 1.00508e-05, 0.933744, 1.18816),
"attribute_data": PackedByteArray(207, 34, 212, 26, 125, 23, 236, 242, 212, 253, 142, 13, 212, 253, 236, 242, 169, 138, 142, 13, 125, 23, 61, 128, 169, 138, 236, 242, 212, 253, 61, 128, 169, 138, 61, 128, 19, 81, 142, 13, 125, 23, 148, 185, 62, 196, 236, 242, 212, 253, 229, 70, 62, 196, 142, 13, 125, 23, 229, 70, 19, 81, 236, 242, 212, 253, 148, 185, 62, 196, 61, 128, 19, 81, 61, 128, 169, 138, 229, 70, 169, 138, 148, 185, 19, 81, 148, 185, 19, 81, 229, 70, 62, 196, 229, 70, 62, 196, 148, 185),
"format": 34896613399,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1.29048e-08, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"material": SubResource("StandardMaterial3D_2vtsw"),
"name": "CardCost",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(84, 1, 46, 241, 106, 243, 255, 191, 0, 0, 0, 0, 255, 255, 255, 191, 84, 1, 255, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 84, 1, 255, 255, 255, 127, 255, 191, 194, 0, 255, 127, 255, 255, 255, 191, 0, 0, 0, 0, 255, 127, 255, 191, 194, 0, 255, 127, 0, 0, 255, 191, 194, 0, 255, 127, 255, 127, 255, 191, 84, 1, 255, 255, 255, 191, 255, 191, 145, 0, 255, 63, 255, 255, 255, 191, 0, 0, 0, 0, 255, 63, 255, 191, 242, 0, 255, 191, 0, 0, 255, 191, 84, 1, 255, 255, 255, 63, 255, 191, 242, 0, 255, 191, 255, 255, 255, 191, 0, 0, 0, 0, 255, 191, 255, 191, 145, 0, 255, 63, 0, 0, 255, 191, 194, 0, 255, 127, 255, 63, 255, 191, 194, 0, 255, 127, 255, 191, 255, 191, 242, 0, 255, 191, 255, 127, 255, 191, 145, 0, 255, 63, 255, 127, 255, 191, 145, 0, 255, 63, 255, 191, 255, 191, 242, 0, 255, 191, 255, 191, 255, 191, 242, 0, 255, 191, 255, 63, 255, 191, 145, 0, 255, 63, 255, 63, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_o7oa7")

[sub_resource type="ViewportTexture" id="ViewportTexture_jwjgq"]
resource_name = "_albedo"
viewport_path = NodePath("CostOnCard/CostSubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_bqove"]
resource_local_to_scene = true
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_jwjgq")
texture_filter = 1

[sub_resource type="ViewportTexture" id="ViewportTexture_yoww3"]
resource_name = "_albedo"
viewport_path = NodePath("PowerOnCard/PowerSubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_7u0ls"]
resource_local_to_scene = true
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_yoww3")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_3tcp4"]
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("16_jh8xd")
texture_filter = 1
texture_repeat = false

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_c8kxe"]
resource_name = "CardPower"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("16_jh8xd")

[sub_resource type="ArrayMesh" id="ArrayMesh_8535f"]
_surfaces = [{
"aabb": AABB(0.0435666, -2.40294, 0.887948, 1.00583e-05, 1.52653, 0.922637),
"format": 34896613377,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1.25188e-10, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"name": "CardPower",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(132, 1, 18, 255, 254, 255, 0, 0, 0, 0, 0, 0, 254, 255, 0, 0, 132, 1, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 132, 1, 255, 255, 255, 127, 0, 0, 194, 0, 255, 127, 254, 255, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 194, 0, 255, 127, 0, 0, 0, 0, 194, 0, 255, 127, 255, 127, 0, 0, 132, 1, 255, 255, 254, 191, 0, 0, 97, 0, 255, 63, 254, 255, 0, 0, 0, 0, 0, 0, 255, 63, 0, 0, 35, 1, 255, 191, 0, 0, 0, 0, 132, 1, 255, 255, 255, 63, 0, 0, 35, 1, 255, 191, 254, 255, 0, 0, 0, 0, 0, 0, 254, 191, 0, 0, 97, 0, 255, 63, 0, 0, 0, 0, 194, 0, 255, 127, 255, 63, 0, 0, 194, 0, 255, 127, 254, 191, 0, 0, 35, 1, 255, 191, 255, 127, 0, 0, 97, 0, 255, 63, 255, 127, 0, 0, 97, 0, 255, 63, 254, 191, 0, 0, 35, 1, 255, 191, 254, 191, 0, 0, 35, 1, 255, 191, 255, 63, 0, 0, 97, 0, 255, 63, 255, 63, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_atnjb"]
resource_name = "DRC_Card_Modell_Test01_Plane_003"
_surfaces = [{
"aabb": AABB(0.0435666, -2.40294, 0.887948, 1.00583e-05, 1.52653, 0.922637),
"attribute_data": PackedByteArray(152, 144, 29, 126, 152, 144, 255, 255, 255, 255, 164, 125, 255, 255, 255, 255, 75, 200, 164, 125, 152, 144, 209, 190, 75, 200, 255, 255, 255, 255, 209, 190, 75, 200, 209, 190, 113, 172, 164, 125, 152, 144, 104, 223, 37, 228, 255, 255, 255, 255, 59, 158, 37, 228, 164, 125, 152, 144, 59, 158, 113, 172, 255, 255, 255, 255, 104, 223, 37, 228, 209, 190, 113, 172, 209, 190, 75, 200, 59, 158, 75, 200, 104, 223, 113, 172, 104, 223, 113, 172, 59, 158, 37, 228, 59, 158, 37, 228, 104, 223),
"format": 34896613399,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1.25188e-10, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"material": SubResource("StandardMaterial3D_c8kxe"),
"name": "CardPower",
"primitive": 3,
"uv_scale": Vector4(1.81925, 2.02753, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(132, 1, 18, 255, 254, 255, 255, 191, 0, 0, 0, 0, 254, 255, 255, 191, 132, 1, 255, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 132, 1, 255, 255, 255, 127, 255, 191, 194, 0, 255, 127, 254, 255, 255, 191, 0, 0, 0, 0, 255, 127, 255, 191, 194, 0, 255, 127, 0, 0, 255, 191, 194, 0, 255, 127, 255, 127, 255, 191, 132, 1, 255, 255, 254, 191, 255, 191, 97, 0, 255, 63, 254, 255, 255, 191, 0, 0, 0, 0, 255, 63, 255, 191, 35, 1, 255, 191, 0, 0, 255, 191, 132, 1, 255, 255, 255, 63, 255, 191, 35, 1, 255, 191, 254, 255, 255, 191, 0, 0, 0, 0, 254, 191, 255, 191, 97, 0, 255, 63, 0, 0, 255, 191, 194, 0, 255, 127, 255, 63, 255, 191, 194, 0, 255, 127, 254, 191, 255, 191, 35, 1, 255, 191, 255, 127, 255, 191, 97, 0, 255, 63, 255, 127, 255, 191, 97, 0, 255, 63, 254, 191, 255, 191, 35, 1, 255, 191, 254, 191, 255, 191, 35, 1, 255, 191, 255, 63, 255, 191, 97, 0, 255, 63, 255, 63, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_8535f")

[sub_resource type="ViewportTexture" id="ViewportTexture_bct5c"]
resource_name = "_albedo"
viewport_path = NodePath("NameOnCard/NameSubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ujq4y"]
resource_local_to_scene = true
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_bct5c")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_u0viu"]
resource_local_to_scene = true
transparency = 1
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("15_e14iv")
texture_filter = 1
texture_repeat = false

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_qgdib"]
resource_name = "CardHeader"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("15_e14iv")

[sub_resource type="ArrayMesh" id="ArrayMesh_jumra"]
_surfaces = [{
"aabb": AABB(0.0423165, 1.69666, -1.694, 1.00285e-05, 0.65001, 3.38801),
"format": 34896613377,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1e-10, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"name": "CardHeader",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(194, 0, 253, 255, 254, 255, 0, 0, 0, 0, 0, 0, 254, 255, 0, 0, 194, 0, 253, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 194, 0, 253, 255, 255, 127, 0, 0, 97, 0, 254, 127, 254, 255, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 97, 0, 254, 127, 0, 0, 0, 0, 97, 0, 254, 127, 255, 127, 0, 0, 194, 0, 253, 255, 255, 191, 0, 0, 48, 0, 255, 63, 254, 255, 0, 0, 0, 0, 0, 0, 255, 63, 0, 0, 146, 0, 254, 191, 0, 0, 0, 0, 194, 0, 253, 255, 255, 63, 0, 0, 146, 0, 254, 191, 254, 255, 0, 0, 0, 0, 0, 0, 255, 191, 0, 0, 48, 0, 255, 63, 0, 0, 0, 0, 97, 0, 254, 127, 255, 63, 0, 0, 97, 0, 254, 127, 255, 191, 0, 0, 146, 0, 254, 191, 255, 127, 0, 0, 48, 0, 255, 63, 255, 127, 0, 0, 48, 0, 255, 63, 255, 191, 0, 0, 146, 0, 254, 191, 255, 191, 0, 0, 146, 0, 254, 191, 255, 63, 0, 0, 48, 0, 255, 63, 255, 63, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_jj8nb"]
resource_name = "DRC_Card_Modell_Test01_Plane_002"
_surfaces = [{
"aabb": AABB(0.0423165, 1.69666, -1.694, 1.00285e-05, 0.65001, 3.38801),
"attribute_data": PackedByteArray(199, 127, 132, 125, 199, 127, 255, 255, 255, 255, 132, 125, 255, 255, 255, 255, 227, 191, 132, 125, 199, 127, 193, 190, 227, 191, 255, 255, 255, 255, 193, 190, 227, 191, 193, 190, 213, 159, 132, 125, 199, 127, 96, 223, 241, 223, 255, 255, 255, 255, 34, 158, 241, 223, 132, 125, 199, 127, 34, 158, 213, 159, 255, 255, 255, 255, 96, 223, 241, 223, 193, 190, 213, 159, 193, 190, 227, 191, 34, 158, 227, 191, 96, 223, 213, 159, 96, 223, 213, 159, 34, 158, 241, 223, 34, 158, 241, 223, 96, 223),
"format": 34896613399,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1e-10, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"material": SubResource("StandardMaterial3D_qgdib"),
"name": "CardHeader",
"primitive": 3,
"uv_scale": Vector4(1.99957, 2.00723, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(194, 0, 253, 255, 254, 255, 255, 191, 0, 0, 0, 0, 254, 255, 255, 191, 194, 0, 253, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 194, 0, 253, 255, 255, 127, 255, 191, 97, 0, 254, 127, 254, 255, 255, 191, 0, 0, 0, 0, 255, 127, 255, 191, 97, 0, 254, 127, 0, 0, 255, 191, 97, 0, 254, 127, 255, 127, 255, 191, 194, 0, 253, 255, 255, 191, 255, 191, 48, 0, 255, 63, 254, 255, 255, 191, 0, 0, 0, 0, 255, 63, 255, 191, 146, 0, 254, 191, 0, 0, 255, 191, 194, 0, 253, 255, 255, 63, 255, 191, 146, 0, 254, 191, 254, 255, 255, 191, 0, 0, 0, 0, 255, 191, 255, 191, 48, 0, 255, 63, 0, 0, 255, 191, 97, 0, 254, 127, 255, 63, 255, 191, 97, 0, 254, 127, 255, 191, 255, 191, 146, 0, 254, 191, 255, 127, 255, 191, 48, 0, 255, 63, 255, 127, 255, 191, 48, 0, 255, 63, 255, 191, 255, 191, 146, 0, 254, 191, 255, 191, 255, 191, 146, 0, 254, 191, 255, 63, 255, 191, 48, 0, 255, 63, 255, 63, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_jumra")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_4q0or"]
resource_local_to_scene = true
transparency = 1
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("20_yxnhn")
uv1_scale = Vector3(1.075, 1.075, 1.075)
uv1_offset = Vector3(-0.035, -0.135, 0)
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_4vwjp"]
resource_name = "CardPicFG"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("31_c864i")

[sub_resource type="ArrayMesh" id="ArrayMesh_qdx37"]
_surfaces = [{
"aabb": AABB(0.0495976, -2.18494, -1.66119, 9.99868e-06, 3.89915, 3.34224),
"format": 34896613377,
"index_count": 18,
"index_data": PackedByteArray(2, 0, 7, 0, 1, 0, 2, 0, 3, 0, 7, 0, 3, 0, 6, 0, 7, 0, 3, 0, 0, 0, 6, 0, 0, 0, 4, 0, 6, 0, 0, 0, 5, 0, 4, 0),
"name": "CardPicFG",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray(0, 0, 189, 2, 41, 4, 0, 0, 0, 0, 255, 255, 255, 255, 0, 0, 0, 0, 255, 255, 0, 0, 0, 0, 0, 0, 68, 9, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 0, 0, 0, 0, 0, 0, 165, 11, 0, 0, 0, 0, 189, 2, 255, 255, 0, 0, 0, 0, 68, 9, 255, 255, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_imadi"]
resource_name = "DRC_Card_Model_Test03_CardPicFG_Cube_002"
_surfaces = [{
"aabb": AABB(0.0495976, -2.18494, -1.66119, 9.99868e-06, 3.89915, 3.34224),
"attribute_data": PackedByteArray(106, 210, 141, 252, 192, 42, 82, 34, 48, 213, 82, 34, 48, 213, 237, 246, 192, 42, 233, 254, 111, 205, 233, 254, 192, 42, 141, 252, 192, 42, 237, 246),
"format": 34896613399,
"index_count": 18,
"index_data": PackedByteArray(2, 0, 7, 0, 1, 0, 2, 0, 3, 0, 7, 0, 3, 0, 6, 0, 7, 0, 3, 0, 0, 0, 6, 0, 0, 0, 4, 0, 6, 0, 0, 0, 5, 0, 4, 0),
"material": SubResource("StandardMaterial3D_4vwjp"),
"name": "CardPicFG",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray(0, 0, 189, 2, 41, 4, 255, 191, 0, 0, 255, 255, 255, 255, 255, 191, 0, 0, 255, 255, 0, 0, 255, 191, 0, 0, 68, 9, 0, 0, 255, 191, 0, 0, 0, 0, 255, 255, 255, 191, 0, 0, 0, 0, 165, 11, 255, 191, 0, 0, 189, 2, 255, 255, 255, 191, 0, 0, 68, 9, 255, 255, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_qdx37")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_47vpe"]
resource_local_to_scene = true
transparency = 1
cull_mode = 1
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("21_bglvw")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_mqgww"]
resource_name = "CardBack"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("28_kxw4s")

[sub_resource type="ArrayMesh" id="ArrayMesh_7w64u"]
_surfaces = [{
"aabb": AABB(-0.0386337, -2.49985, -1.78976, 9.99868e-06, 4.9997, 3.57952),
"format": 34896613377,
"index_count": 174,
"index_data": PackedByteArray(3, 0, 7, 0, 12, 0, 3, 0, 0, 0, 7, 0, 1, 0, 23, 0, 5, 0, 1, 0, 22, 0, 23, 0, 13, 0, 27, 0, 14, 0, 13, 0, 26, 0, 27, 0, 14, 0, 28, 0, 12, 0, 14, 0, 27, 0, 28, 0, 17, 0, 30, 0, 15, 0, 17, 0, 29, 0, 30, 0, 16, 0, 29, 0, 17, 0, 16, 0, 31, 0, 29, 0, 8, 0, 33, 0, 6, 0, 8, 0, 32, 0, 33, 0, 7, 0, 32, 0, 8, 0, 7, 0, 34, 0, 32, 0, 10, 0, 36, 0, 11, 0, 10, 0, 35, 0, 36, 0, 11, 0, 37, 0, 9, 0, 11, 0, 36, 0, 37, 0, 0, 0, 34, 0, 7, 0, 0, 0, 19, 0, 34, 0, 6, 0, 20, 0, 38, 0, 6, 0, 33, 0, 20, 0, 9, 0, 22, 0, 1, 0, 9, 0, 37, 0, 22, 0, 2, 0, 35, 0, 10, 0, 2, 0, 21, 0, 35, 0, 12, 0, 18, 0, 3, 0, 12, 0, 28, 0, 18, 0, 39, 0, 26, 0, 13, 0, 39, 0, 25, 0, 26, 0, 5, 0, 31, 0, 16, 0, 5, 0, 23, 0, 31, 0, 15, 0, 24, 0, 4, 0, 15, 0, 30, 0, 24, 0, 2, 0, 15, 0, 4, 0, 2, 0, 10, 0, 15, 0, 10, 0, 17, 0, 15, 0, 10, 0, 11, 0, 17, 0, 11, 0, 16, 0, 17, 0, 11, 0, 9, 0, 16, 0, 9, 0, 5, 0, 16, 0, 9, 0, 1, 0, 5, 0, 3, 0, 19, 0, 0, 0, 3, 0, 18, 0, 19, 0, 7, 0, 14, 0, 12, 0, 7, 0, 8, 0, 14, 0, 8, 0, 13, 0, 14, 0, 8, 0, 6, 0, 13, 0, 39, 0, 24, 0, 25, 0, 39, 0, 4, 0, 24, 0, 38, 0, 21, 0, 2, 0, 38, 0, 20, 0, 21, 0, 6, 0, 39, 0, 13, 0, 6, 0, 38, 0, 39, 0, 38, 0, 4, 0, 39, 0, 38, 0, 2, 0, 4, 0),
"lods": [1e-10, PackedByteArray(27, 0, 28, 0, 34, 0, 34, 0, 28, 0, 18, 0, 34, 0, 18, 0, 19, 0, 34, 0, 32, 0, 27, 0, 32, 0, 26, 0, 27, 0, 32, 0, 33, 0, 26, 0, 33, 0, 25, 0, 26, 0, 33, 0, 20, 0, 25, 0, 20, 0, 24, 0, 25, 0, 20, 0, 21, 0, 24, 0, 21, 0, 30, 0, 24, 0, 21, 0, 35, 0, 30, 0, 35, 0, 29, 0, 30, 0, 35, 0, 36, 0, 29, 0, 36, 0, 31, 0, 29, 0, 36, 0, 37, 0, 31, 0, 37, 0, 23, 0, 31, 0, 37, 0, 22, 0, 23, 0)],
"name": "CardBack",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray(0, 0, 185, 0, 147, 244, 0, 0, 0, 0, 69, 255, 146, 244, 0, 0, 0, 0, 174, 244, 69, 255, 0, 0, 0, 0, 185, 0, 108, 11, 0, 0, 0, 0, 174, 244, 185, 0, 0, 0, 0, 0, 69, 255, 108, 11, 0, 0, 0, 0, 232, 7, 251, 254, 0, 0, 0, 0, 44, 2, 37, 249, 0, 0, 0, 0, 148, 4, 156, 252, 0, 0, 0, 0, 209, 253, 9, 249, 0, 0, 0, 0, 28, 248, 56, 255, 0, 0, 0, 0, 102, 251, 229, 252, 0, 0, 0, 0, 44, 2, 217, 6, 0, 0, 0, 0, 232, 7, 3, 1, 0, 0, 0, 0, 148, 4, 98, 3, 0, 0, 0, 0, 28, 248, 198, 0, 0, 0, 0, 0, 209, 253, 245, 6, 0, 0, 0, 0, 102, 251, 25, 3, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 79, 11, 255, 255, 0, 0, 0, 0, 175, 244, 255, 255, 0, 0, 0, 0, 255, 255, 175, 244, 0, 0, 0, 0, 255, 255, 79, 11, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 166, 7, 79, 0, 0, 0, 0, 0, 14, 4, 221, 2, 0, 0, 0, 0, 131, 1, 134, 6, 0, 0, 0, 0, 240, 251, 151, 2, 0, 0, 0, 0, 88, 248, 13, 0, 0, 0, 0, 0, 123, 254, 166, 6, 0, 0, 0, 0, 14, 4, 33, 253, 0, 0, 0, 0, 166, 7, 175, 255, 0, 0, 0, 0, 131, 1, 120, 249, 0, 0, 0, 0, 88, 248, 241, 255, 0, 0, 0, 0, 240, 251, 103, 253, 0, 0, 0, 0, 123, 254, 88, 249, 0, 0, 0, 0, 87, 11, 69, 255, 0, 0, 0, 0, 87, 11, 185, 0, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_5cvg6"]
resource_name = "DRC_Card_Model_Test03_CardBack_Cube_004"
_surfaces = [{
"aabb": AABB(-0.0386337, -2.49985, -1.78976, 9.99868e-06, 4.9997, 3.57952),
"attribute_data": PackedByteArray(26, 242, 239, 251, 25, 242, 149, 4, 139, 252, 223, 14, 118, 14, 239, 251, 5, 4, 223, 14, 119, 14, 149, 4, 67, 252, 243, 244, 145, 246, 134, 250, 243, 249, 48, 248, 117, 246, 254, 5, 127, 252, 138, 11, 58, 250, 87, 8, 255, 9, 134, 250, 77, 4, 243, 244, 157, 6, 48, 248, 17, 4, 138, 11, 27, 10, 254, 5, 86, 6, 87, 8, 90, 14, 163, 252, 54, 242, 163, 252, 65, 253, 166, 241, 65, 253, 222, 14, 54, 242, 224, 3, 90, 14, 224, 3, 79, 3, 222, 14, 79, 3, 166, 241, 157, 3, 52, 245, 28, 6, 178, 248, 174, 9, 42, 251, 215, 5, 209, 7, 93, 3, 80, 11, 205, 9, 89, 5, 116, 250, 178, 248, 243, 252, 52, 245, 226, 246, 42, 251, 51, 253, 80, 11, 185, 250, 209, 7, 195, 246, 89, 5, 139, 252, 158, 241, 5, 4, 158, 241),
"format": 34896613399,
"index_count": 174,
"index_data": PackedByteArray(3, 0, 7, 0, 12, 0, 3, 0, 0, 0, 7, 0, 1, 0, 23, 0, 5, 0, 1, 0, 22, 0, 23, 0, 13, 0, 27, 0, 14, 0, 13, 0, 26, 0, 27, 0, 14, 0, 28, 0, 12, 0, 14, 0, 27, 0, 28, 0, 17, 0, 30, 0, 15, 0, 17, 0, 29, 0, 30, 0, 16, 0, 29, 0, 17, 0, 16, 0, 31, 0, 29, 0, 8, 0, 33, 0, 6, 0, 8, 0, 32, 0, 33, 0, 7, 0, 32, 0, 8, 0, 7, 0, 34, 0, 32, 0, 10, 0, 36, 0, 11, 0, 10, 0, 35, 0, 36, 0, 11, 0, 37, 0, 9, 0, 11, 0, 36, 0, 37, 0, 0, 0, 34, 0, 7, 0, 0, 0, 19, 0, 34, 0, 6, 0, 20, 0, 38, 0, 6, 0, 33, 0, 20, 0, 9, 0, 22, 0, 1, 0, 9, 0, 37, 0, 22, 0, 2, 0, 35, 0, 10, 0, 2, 0, 21, 0, 35, 0, 12, 0, 18, 0, 3, 0, 12, 0, 28, 0, 18, 0, 39, 0, 26, 0, 13, 0, 39, 0, 25, 0, 26, 0, 5, 0, 31, 0, 16, 0, 5, 0, 23, 0, 31, 0, 15, 0, 24, 0, 4, 0, 15, 0, 30, 0, 24, 0, 2, 0, 15, 0, 4, 0, 2, 0, 10, 0, 15, 0, 10, 0, 17, 0, 15, 0, 10, 0, 11, 0, 17, 0, 11, 0, 16, 0, 17, 0, 11, 0, 9, 0, 16, 0, 9, 0, 5, 0, 16, 0, 9, 0, 1, 0, 5, 0, 3, 0, 19, 0, 0, 0, 3, 0, 18, 0, 19, 0, 7, 0, 14, 0, 12, 0, 7, 0, 8, 0, 14, 0, 8, 0, 13, 0, 14, 0, 8, 0, 6, 0, 13, 0, 39, 0, 24, 0, 25, 0, 39, 0, 4, 0, 24, 0, 38, 0, 21, 0, 2, 0, 38, 0, 20, 0, 21, 0, 6, 0, 39, 0, 13, 0, 6, 0, 38, 0, 39, 0, 38, 0, 4, 0, 39, 0, 38, 0, 2, 0, 4, 0),
"lods": [1e-10, PackedByteArray(27, 0, 28, 0, 34, 0, 34, 0, 28, 0, 18, 0, 34, 0, 18, 0, 19, 0, 34, 0, 32, 0, 27, 0, 32, 0, 26, 0, 27, 0, 32, 0, 33, 0, 26, 0, 33, 0, 25, 0, 26, 0, 33, 0, 20, 0, 25, 0, 20, 0, 24, 0, 25, 0, 20, 0, 21, 0, 24, 0, 21, 0, 30, 0, 24, 0, 21, 0, 35, 0, 30, 0, 35, 0, 29, 0, 30, 0, 35, 0, 36, 0, 29, 0, 36, 0, 31, 0, 29, 0, 36, 0, 37, 0, 31, 0, 37, 0, 23, 0, 31, 0, 37, 0, 22, 0, 23, 0)],
"material": SubResource("StandardMaterial3D_mqgww"),
"name": "CardBack",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray(0, 0, 185, 0, 147, 244, 0, 0, 0, 0, 69, 255, 146, 244, 0, 0, 0, 0, 174, 244, 69, 255, 0, 0, 0, 0, 185, 0, 108, 11, 0, 0, 0, 0, 174, 244, 185, 0, 0, 0, 0, 0, 69, 255, 108, 11, 0, 0, 0, 0, 232, 7, 251, 254, 0, 0, 0, 0, 44, 2, 37, 249, 0, 0, 0, 0, 148, 4, 156, 252, 0, 0, 0, 0, 209, 253, 9, 249, 0, 0, 0, 0, 28, 248, 56, 255, 0, 0, 0, 0, 102, 251, 229, 252, 0, 0, 0, 0, 44, 2, 217, 6, 0, 0, 0, 0, 232, 7, 3, 1, 0, 0, 0, 0, 148, 4, 98, 3, 0, 0, 0, 0, 28, 248, 198, 0, 0, 0, 0, 0, 209, 253, 245, 6, 0, 0, 0, 0, 102, 251, 25, 3, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 79, 11, 255, 255, 0, 0, 0, 0, 175, 244, 255, 255, 0, 0, 0, 0, 255, 255, 175, 244, 0, 0, 0, 0, 255, 255, 79, 11, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 166, 7, 79, 0, 0, 0, 0, 0, 14, 4, 221, 2, 0, 0, 0, 0, 131, 1, 134, 6, 0, 0, 0, 0, 240, 251, 151, 2, 0, 0, 0, 0, 88, 248, 13, 0, 0, 0, 0, 0, 123, 254, 166, 6, 0, 0, 0, 0, 14, 4, 33, 253, 0, 0, 0, 0, 166, 7, 175, 255, 0, 0, 0, 0, 131, 1, 120, 249, 0, 0, 0, 0, 88, 248, 241, 255, 0, 0, 0, 0, 240, 251, 103, 253, 0, 0, 0, 0, 123, 254, 88, 249, 0, 0, 0, 0, 87, 11, 69, 255, 0, 0, 0, 0, 87, 11, 185, 0, 0, 0, 255, 191, 255, 127, 255, 191, 255, 127, 0, 0, 255, 191, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 0, 0, 255, 191, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 0, 0, 255, 191, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 0, 0, 255, 191, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 0, 0, 255, 191, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_7w64u")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_bwgkt"]
resource_local_to_scene = true
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
shading_mode = 0
albedo_texture = ExtResource("17_qhp24")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_i62hh"]
resource_name = "CardFaction"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("17_qhp24")

[sub_resource type="ArrayMesh" id="ArrayMesh_1m3qx"]
_surfaces = [{
"aabb": AABB(0.0398071, 0.880924, 1.04766, 1.00471e-05, 1.07111, 0.708228),
"format": 34896613377,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [9.12507e-09, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"name": "CardFaction",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(59, 1, 254, 255, 254, 255, 0, 0, 0, 0, 0, 0, 254, 255, 0, 0, 59, 1, 254, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 1, 254, 255, 255, 127, 0, 0, 145, 0, 69, 145, 254, 255, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 145, 0, 69, 145, 0, 0, 0, 0, 145, 0, 69, 145, 255, 127, 0, 0, 59, 1, 254, 255, 254, 191, 0, 0, 48, 0, 162, 72, 254, 255, 0, 0, 0, 0, 0, 0, 255, 63, 0, 0, 11, 1, 232, 217, 0, 0, 0, 0, 59, 1, 254, 255, 255, 63, 0, 0, 11, 1, 232, 217, 254, 255, 0, 0, 0, 0, 0, 0, 254, 191, 0, 0, 48, 0, 162, 72, 0, 0, 0, 0, 145, 0, 69, 145, 255, 63, 0, 0, 145, 0, 69, 145, 254, 191, 0, 0, 11, 1, 232, 217, 255, 127, 0, 0, 48, 0, 162, 72, 255, 127, 0, 0, 48, 0, 162, 72, 254, 191, 0, 0, 11, 1, 232, 217, 254, 191, 0, 0, 11, 1, 232, 217, 255, 63, 0, 0, 48, 0, 162, 72, 255, 63, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_k0ke4"]
resource_name = "DRC_Card_Modell_Test01_Plane_004"
_surfaces = [{
"aabb": AABB(0.0398071, 0.880924, 1.04766, 1.00471e-05, 1.07111, 0.708228),
"attribute_data": PackedByteArray(25, 2, 138, 2, 25, 2, 112, 244, 34, 253, 138, 2, 34, 253, 112, 244, 157, 127, 138, 2, 25, 2, 42, 107, 157, 127, 112, 244, 34, 253, 42, 107, 157, 127, 42, 107, 219, 64, 138, 2, 25, 2, 206, 175, 96, 190, 112, 244, 34, 253, 136, 38, 96, 190, 138, 2, 25, 2, 136, 38, 219, 64, 112, 244, 34, 253, 206, 175, 96, 190, 42, 107, 219, 64, 42, 107, 157, 127, 136, 38, 157, 127, 206, 175, 219, 64, 206, 175, 219, 64, 136, 38, 96, 190, 136, 38, 96, 190, 206, 175),
"format": 34896613399,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [9.12507e-09, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"material": SubResource("StandardMaterial3D_i62hh"),
"name": "CardFaction",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(59, 1, 254, 255, 254, 255, 255, 191, 0, 0, 0, 0, 254, 255, 255, 191, 59, 1, 254, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 59, 1, 254, 255, 255, 127, 255, 191, 145, 0, 69, 145, 254, 255, 255, 191, 0, 0, 0, 0, 255, 127, 255, 191, 145, 0, 69, 145, 0, 0, 255, 191, 145, 0, 69, 145, 255, 127, 255, 191, 59, 1, 254, 255, 254, 191, 255, 191, 48, 0, 162, 72, 254, 255, 255, 191, 0, 0, 0, 0, 255, 63, 255, 191, 11, 1, 232, 217, 0, 0, 255, 191, 59, 1, 254, 255, 255, 63, 255, 191, 11, 1, 232, 217, 254, 255, 255, 191, 0, 0, 0, 0, 254, 191, 255, 191, 48, 0, 162, 72, 0, 0, 255, 191, 145, 0, 69, 145, 255, 63, 255, 191, 145, 0, 69, 145, 254, 191, 255, 191, 11, 1, 232, 217, 255, 127, 255, 191, 48, 0, 162, 72, 255, 127, 255, 191, 48, 0, 162, 72, 254, 191, 255, 191, 11, 1, 232, 217, 254, 191, 255, 191, 11, 1, 232, 217, 255, 63, 255, 191, 48, 0, 162, 72, 255, 63, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_1m3qx")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_610e1"]
transparency = 1
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("18_l5ddj")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_72yu4"]
resource_name = "CardRarity"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("18_l5ddj")

[sub_resource type="ArrayMesh" id="ArrayMesh_72eop"]
_surfaces = [{
"aabb": AABB(0.0378236, -2.47757, -1.60525, 1.00136e-05, 0.39001, 0.22801),
"format": 34896613377,
"index_count": 24,
"index_data": PackedByteArray(1, 0, 8, 0, 6, 0, 1, 0, 5, 0, 8, 0, 5, 0, 4, 0, 8, 0, 5, 0, 0, 0, 4, 0, 4, 0, 7, 0, 8, 0, 4, 0, 2, 0, 7, 0, 8, 0, 3, 0, 6, 0, 8, 0, 7, 0, 3, 0),
"name": "CardRarity",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 9,
"vertex_data": PackedByteArray(97, 0, 253, 255, 252, 255, 0, 0, 0, 0, 0, 0, 252, 255, 0, 0, 97, 0, 253, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 0, 253, 255, 254, 127, 0, 0, 48, 0, 254, 127, 252, 255, 0, 0, 0, 0, 0, 0, 254, 127, 0, 0, 48, 0, 254, 127, 0, 0, 0, 0, 48, 0, 254, 127, 254, 127, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_cpfdt"]
resource_name = "DRC_Card_Modell_Test01_Plane_005"
_surfaces = [{
"aabb": AABB(0.0378236, -2.47757, -1.60525, 1.00136e-05, 0.39001, 0.22801),
"attribute_data": PackedByteArray(252, 118, 96, 110, 252, 118, 92, 141, 25, 137, 96, 110, 25, 137, 92, 141, 10, 128, 96, 110, 252, 118, 222, 125, 10, 128, 92, 141, 25, 137, 222, 125, 10, 128, 222, 125),
"format": 34896613399,
"index_count": 24,
"index_data": PackedByteArray(1, 0, 8, 0, 6, 0, 1, 0, 5, 0, 8, 0, 5, 0, 4, 0, 8, 0, 5, 0, 0, 0, 4, 0, 4, 0, 7, 0, 8, 0, 4, 0, 2, 0, 7, 0, 8, 0, 3, 0, 6, 0, 8, 0, 7, 0, 3, 0),
"material": SubResource("StandardMaterial3D_72yu4"),
"name": "CardRarity",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 9,
"vertex_data": PackedByteArray(97, 0, 253, 255, 252, 255, 255, 191, 0, 0, 0, 0, 252, 255, 255, 191, 97, 0, 253, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 97, 0, 253, 255, 254, 127, 255, 191, 48, 0, 254, 127, 252, 255, 255, 191, 0, 0, 0, 0, 254, 127, 255, 191, 48, 0, 254, 127, 0, 0, 255, 191, 48, 0, 254, 127, 254, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_72eop")

[sub_resource type="ViewportTexture" id="ViewportTexture_akcsu"]
resource_name = "_albedo"
viewport_path = NodePath("CardBg/SubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_uadp2"]
resource_local_to_scene = true
cull_mode = 2
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_akcsu")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_3spdg"]
resource_name = "CardBase"
cull_mode = 2
albedo_color = Color(1, 0.47451, 1, 0.0392157)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("29_x61ul")

[sub_resource type="ArrayMesh" id="ArrayMesh_4aff2"]
_surfaces = [{
"aabb": AABB(-0.0370001, -2.49985, -1.78976, 0.0740101, 4.9997, 3.57952),
"format": 34896613377,
"index_count": 828,
"index_data": PackedByteArray(62, 0, 139, 0, 138, 0, 62, 0, 64, 0, 139, 0, 40, 0, 31, 0, 23, 0, 40, 0, 51, 0, 31, 0, 49, 0, 24, 0, 30, 0, 49, 0, 41, 0, 24, 0, 66, 0, 139, 0, 73, 0, 66, 0, 138, 0, 139, 0, 52, 0, 20, 0, 33, 0, 52, 0, 43, 0, 20, 0, 3, 0, 7, 0, 12, 0, 3, 0, 0, 0, 7, 0, 46, 0, 27, 0, 26, 0, 46, 0, 47, 0, 27, 0, 47, 0, 28, 0, 27, 0, 47, 0, 48, 0, 28, 0, 55, 0, 36, 0, 35, 0, 55, 0, 56, 0, 36, 0, 56, 0, 37, 0, 36, 0, 56, 0, 57, 0, 37, 0, 42, 0, 34, 0, 19, 0, 42, 0, 54, 0, 34, 0, 38, 0, 19, 0, 18, 0, 38, 0, 42, 0, 19, 0, 39, 0, 24, 0, 41, 0, 39, 0, 25, 0, 24, 0, 1, 0, 23, 0, 5, 0, 1, 0, 22, 0, 23, 0, 2, 0, 59, 0, 58, 0, 2, 0, 4, 0, 59, 0, 13, 0, 27, 0, 14, 0, 13, 0, 26, 0, 27, 0, 14, 0, 28, 0, 12, 0, 14, 0, 27, 0, 28, 0, 17, 0, 30, 0, 15, 0, 17, 0, 29, 0, 30, 0, 16, 0, 29, 0, 17, 0, 16, 0, 31, 0, 29, 0, 8, 0, 33, 0, 6, 0, 8, 0, 32, 0, 33, 0, 7, 0, 32, 0, 8, 0, 7, 0, 34, 0, 32, 0, 10, 0, 36, 0, 11, 0, 10, 0, 35, 0, 36, 0, 11, 0, 37, 0, 9, 0, 11, 0, 36, 0, 37, 0, 0, 0, 34, 0, 7, 0, 0, 0, 19, 0, 34, 0, 6, 0, 20, 0, 58, 0, 6, 0, 33, 0, 20, 0, 9, 0, 22, 0, 1, 0, 9, 0, 37, 0, 22, 0, 2, 0, 35, 0, 10, 0, 2, 0, 21, 0, 35, 0, 12, 0, 18, 0, 3, 0, 12, 0, 28, 0, 18, 0, 59, 0, 26, 0, 13, 0, 59, 0, 25, 0, 26, 0, 5, 0, 31, 0, 16, 0, 5, 0, 23, 0, 31, 0, 15, 0, 24, 0, 4, 0, 15, 0, 30, 0, 24, 0, 122, 0, 54, 0, 42, 0, 122, 0, 134, 0, 54, 0, 136, 0, 57, 0, 56, 0, 136, 0, 137, 0, 57, 0, 135, 0, 56, 0, 55, 0, 135, 0, 136, 0, 56, 0, 32, 0, 54, 0, 53, 0, 32, 0, 34, 0, 54, 0, 33, 0, 53, 0, 52, 0, 33, 0, 32, 0, 53, 0, 29, 0, 51, 0, 50, 0, 29, 0, 31, 0, 51, 0, 30, 0, 50, 0, 49, 0, 30, 0, 29, 0, 50, 0, 127, 0, 48, 0, 47, 0, 127, 0, 128, 0, 48, 0, 126, 0, 47, 0, 46, 0, 126, 0, 127, 0, 47, 0, 18, 0, 48, 0, 38, 0, 18, 0, 28, 0, 48, 0, 35, 0, 45, 0, 55, 0, 35, 0, 21, 0, 45, 0, 132, 0, 43, 0, 52, 0, 132, 0, 123, 0, 43, 0, 129, 0, 41, 0, 49, 0, 129, 0, 121, 0, 41, 0, 120, 0, 51, 0, 40, 0, 120, 0, 131, 0, 51, 0, 26, 0, 39, 0, 46, 0, 26, 0, 25, 0, 39, 0, 45, 0, 123, 0, 125, 0, 45, 0, 43, 0, 123, 0, 22, 0, 57, 0, 44, 0, 22, 0, 37, 0, 57, 0, 23, 0, 44, 0, 40, 0, 23, 0, 22, 0, 44, 0, 59, 0, 24, 0, 25, 0, 59, 0, 4, 0, 24, 0, 2, 0, 15, 0, 4, 0, 2, 0, 10, 0, 15, 0, 10, 0, 17, 0, 15, 0, 10, 0, 11, 0, 17, 0, 11, 0, 16, 0, 17, 0, 11, 0, 9, 0, 16, 0, 9, 0, 5, 0, 16, 0, 9, 0, 1, 0, 5, 0, 3, 0, 19, 0, 0, 0, 3, 0, 18, 0, 19, 0, 7, 0, 14, 0, 12, 0, 7, 0, 8, 0, 14, 0, 8, 0, 13, 0, 14, 0, 8, 0, 6, 0, 13, 0, 20, 0, 45, 0, 21, 0, 20, 0, 43, 0, 45, 0, 58, 0, 21, 0, 2, 0, 58, 0, 20, 0, 21, 0, 40, 0, 124, 0, 120, 0, 40, 0, 44, 0, 124, 0, 44, 0, 137, 0, 124, 0, 44, 0, 57, 0, 137, 0, 46, 0, 119, 0, 126, 0, 46, 0, 39, 0, 119, 0, 55, 0, 125, 0, 135, 0, 55, 0, 45, 0, 125, 0, 38, 0, 128, 0, 118, 0, 38, 0, 48, 0, 128, 0, 49, 0, 130, 0, 129, 0, 49, 0, 50, 0, 130, 0, 50, 0, 131, 0, 130, 0, 50, 0, 51, 0, 131, 0, 52, 0, 133, 0, 132, 0, 52, 0, 53, 0, 133, 0, 53, 0, 134, 0, 133, 0, 53, 0, 54, 0, 134, 0, 118, 0, 42, 0, 38, 0, 118, 0, 122, 0, 42, 0, 138, 0, 81, 0, 62, 0, 138, 0, 80, 0, 81, 0, 105, 0, 123, 0, 125, 0, 105, 0, 103, 0, 123, 0, 100, 0, 91, 0, 83, 0, 100, 0, 111, 0, 91, 0, 109, 0, 84, 0, 90, 0, 109, 0, 101, 0, 84, 0, 112, 0, 80, 0, 93, 0, 112, 0, 103, 0, 80, 0, 63, 0, 67, 0, 72, 0, 63, 0, 60, 0, 67, 0, 106, 0, 87, 0, 86, 0, 106, 0, 107, 0, 87, 0, 107, 0, 88, 0, 87, 0, 107, 0, 108, 0, 88, 0, 115, 0, 96, 0, 95, 0, 115, 0, 116, 0, 96, 0, 116, 0, 97, 0, 96, 0, 116, 0, 117, 0, 97, 0, 102, 0, 94, 0, 79, 0, 102, 0, 114, 0, 94, 0, 98, 0, 79, 0, 78, 0, 98, 0, 102, 0, 79, 0, 99, 0, 84, 0, 101, 0, 99, 0, 85, 0, 84, 0, 61, 0, 83, 0, 65, 0, 61, 0, 82, 0, 83, 0, 64, 0, 85, 0, 139, 0, 64, 0, 84, 0, 85, 0, 73, 0, 87, 0, 74, 0, 73, 0, 86, 0, 87, 0, 74, 0, 88, 0, 72, 0, 74, 0, 87, 0, 88, 0, 77, 0, 90, 0, 75, 0, 77, 0, 89, 0, 90, 0, 76, 0, 89, 0, 77, 0, 76, 0, 91, 0, 89, 0, 68, 0, 93, 0, 66, 0, 68, 0, 92, 0, 93, 0, 67, 0, 92, 0, 68, 0, 67, 0, 94, 0, 92, 0, 70, 0, 96, 0, 71, 0, 70, 0, 95, 0, 96, 0, 71, 0, 97, 0, 69, 0, 71, 0, 96, 0, 97, 0, 60, 0, 94, 0, 67, 0, 60, 0, 79, 0, 94, 0, 66, 0, 80, 0, 138, 0, 66, 0, 93, 0, 80, 0, 69, 0, 82, 0, 61, 0, 69, 0, 97, 0, 82, 0, 62, 0, 95, 0, 70, 0, 62, 0, 81, 0, 95, 0, 72, 0, 78, 0, 63, 0, 72, 0, 88, 0, 78, 0, 139, 0, 86, 0, 73, 0, 139, 0, 85, 0, 86, 0, 65, 0, 91, 0, 76, 0, 65, 0, 83, 0, 91, 0, 75, 0, 84, 0, 64, 0, 75, 0, 90, 0, 84, 0, 122, 0, 114, 0, 102, 0, 122, 0, 134, 0, 114, 0, 136, 0, 117, 0, 116, 0, 136, 0, 137, 0, 117, 0, 135, 0, 116, 0, 115, 0, 135, 0, 136, 0, 116, 0, 92, 0, 114, 0, 113, 0, 92, 0, 94, 0, 114, 0, 93, 0, 113, 0, 112, 0, 93, 0, 92, 0, 113, 0, 89, 0, 111, 0, 110, 0, 89, 0, 91, 0, 111, 0, 90, 0, 110, 0, 109, 0, 90, 0, 89, 0, 110, 0, 127, 0, 108, 0, 107, 0, 127, 0, 128, 0, 108, 0, 126, 0, 107, 0, 106, 0, 126, 0, 127, 0, 107, 0, 63, 0, 79, 0, 60, 0, 63, 0, 78, 0, 79, 0, 78, 0, 108, 0, 98, 0, 78, 0, 88, 0, 108, 0, 95, 0, 105, 0, 115, 0, 95, 0, 81, 0, 105, 0, 132, 0, 103, 0, 112, 0, 132, 0, 123, 0, 103, 0, 129, 0, 101, 0, 109, 0, 129, 0, 121, 0, 101, 0, 120, 0, 111, 0, 100, 0, 120, 0, 131, 0, 111, 0, 86, 0, 99, 0, 106, 0, 86, 0, 85, 0, 99, 0, 82, 0, 117, 0, 104, 0, 82, 0, 97, 0, 117, 0, 83, 0, 104, 0, 100, 0, 83, 0, 82, 0, 104, 0, 121, 0, 99, 0, 101, 0, 121, 0, 119, 0, 99, 0, 62, 0, 75, 0, 64, 0, 62, 0, 70, 0, 75, 0, 70, 0, 77, 0, 75, 0, 70, 0, 71, 0, 77, 0, 71, 0, 76, 0, 77, 0, 71, 0, 69, 0, 76, 0, 69, 0, 65, 0, 76, 0, 69, 0, 61, 0, 65, 0, 118, 0, 102, 0, 98, 0, 118, 0, 122, 0, 102, 0, 67, 0, 74, 0, 72, 0, 67, 0, 68, 0, 74, 0, 68, 0, 73, 0, 74, 0, 68, 0, 66, 0, 73, 0, 80, 0, 105, 0, 81, 0, 80, 0, 103, 0, 105, 0, 119, 0, 41, 0, 121, 0, 119, 0, 39, 0, 41, 0, 100, 0, 124, 0, 120, 0, 100, 0, 104, 0, 124, 0, 104, 0, 137, 0, 124, 0, 104, 0, 117, 0, 137, 0, 106, 0, 119, 0, 126, 0, 106, 0, 99, 0, 119, 0, 115, 0, 125, 0, 135, 0, 115, 0, 105, 0, 125, 0, 98, 0, 128, 0, 118, 0, 98, 0, 108, 0, 128, 0, 109, 0, 130, 0, 129, 0, 109, 0, 110, 0, 130, 0, 110, 0, 131, 0, 130, 0, 110, 0, 111, 0, 131, 0, 112, 0, 133, 0, 132, 0, 112, 0, 113, 0, 133, 0, 113, 0, 134, 0, 133, 0, 113, 0, 114, 0, 134, 0, 6, 0, 59, 0, 13, 0, 6, 0, 58, 0, 59, 0),
"lods": [0.0409402, PackedByteArray(70, 0, 73, 0, 66, 0, 73, 0, 107, 0, 72, 0, 66, 0, 73, 0, 72, 0, 72, 0, 98, 0, 63, 0, 63, 0, 66, 0, 72, 0, 63, 0, 60, 0, 66, 0, 63, 0, 79, 0, 60, 0, 63, 0, 98, 0, 79, 0, 60, 0, 79, 0, 114, 0, 60, 0, 114, 0, 66, 0, 66, 0, 114, 0, 113, 0, 66, 0, 113, 0, 93, 0, 98, 0, 114, 0, 79, 0, 66, 0, 93, 0, 95, 0, 93, 0, 105, 0, 95, 0, 93, 0, 103, 0, 105, 0, 66, 0, 95, 0, 70, 0, 70, 0, 95, 0, 96, 0, 93, 0, 113, 0, 103, 0, 103, 0, 113, 0, 133, 0, 103, 0, 133, 0, 132, 0, 70, 0, 96, 0, 61, 0, 61, 0, 96, 0, 104, 0, 135, 0, 136, 0, 104, 0, 135, 0, 104, 0, 105, 0, 61, 0, 104, 0, 83, 0, 110, 0, 83, 0, 111, 0, 75, 0, 110, 0, 109, 0, 61, 0, 83, 0, 65, 0, 65, 0, 83, 0, 75, 0, 61, 0, 65, 0, 75, 0, 75, 0, 83, 0, 110, 0, 70, 0, 61, 0, 75, 0, 70, 0, 75, 0, 73, 0, 75, 0, 109, 0, 73, 0, 99, 0, 73, 0, 109, 0, 121, 0, 99, 0, 109, 0, 99, 0, 107, 0, 73, 0, 126, 0, 107, 0, 99, 0, 126, 0, 127, 0, 107, 0, 127, 0, 108, 0, 107, 0, 72, 0, 107, 0, 108, 0, 72, 0, 108, 0, 98, 0, 99, 0, 119, 0, 126, 0, 121, 0, 119, 0, 99, 0, 118, 0, 114, 0, 98, 0, 118, 0, 122, 0, 114, 0, 122, 0, 134, 0, 114, 0, 98, 0, 128, 0, 118, 0, 3, 0, 6, 0, 12, 0, 12, 0, 38, 0, 3, 0, 3, 0, 38, 0, 19, 0, 12, 0, 48, 0, 38, 0, 3, 0, 19, 0, 0, 0, 3, 0, 0, 0, 6, 0, 0, 0, 19, 0, 54, 0, 0, 0, 54, 0, 6, 0, 6, 0, 54, 0, 53, 0, 12, 0, 27, 0, 48, 0, 6, 0, 53, 0, 33, 0, 13, 0, 27, 0, 12, 0, 6, 0, 13, 0, 12, 0, 13, 0, 26, 0, 27, 0, 6, 0, 33, 0, 35, 0, 126, 0, 48, 0, 39, 0, 6, 0, 35, 0, 10, 0, 10, 0, 13, 0, 6, 0, 10, 0, 15, 0, 13, 0, 13, 0, 15, 0, 26, 0, 39, 0, 26, 0, 15, 0, 39, 0, 15, 0, 49, 0, 49, 0, 50, 0, 130, 0, 49, 0, 130, 0, 129, 0, 15, 0, 50, 0, 49, 0, 129, 0, 121, 0, 49, 0, 119, 0, 49, 0, 121, 0, 17, 0, 50, 0, 15, 0, 10, 0, 17, 0, 15, 0, 17, 0, 23, 0, 50, 0, 50, 0, 23, 0, 51, 0, 51, 0, 124, 0, 120, 0, 51, 0, 44, 0, 124, 0, 5, 0, 23, 0, 17, 0, 1, 0, 44, 0, 23, 0, 1, 0, 23, 0, 5, 0, 1, 0, 5, 0, 17, 0, 10, 0, 1, 0, 17, 0, 1, 0, 57, 0, 44, 0, 10, 0, 57, 0, 1, 0, 10, 0, 56, 0, 57, 0, 10, 0, 35, 0, 56, 0, 56, 0, 123, 0, 125, 0, 33, 0, 43, 0, 56, 0, 132, 0, 123, 0, 43, 0, 56, 0, 43, 0, 123, 0, 56, 0, 125, 0, 135, 0, 135, 0, 136, 0, 56, 0, 136, 0, 57, 0, 56, 0, 44, 0, 57, 0, 137, 0, 39, 0, 27, 0, 26, 0, 39, 0, 48, 0, 27, 0, 38, 0, 54, 0, 19, 0, 122, 0, 134, 0, 54, 0, 136, 0, 137, 0, 57, 0, 33, 0, 53, 0, 43, 0, 53, 0, 54, 0, 134, 0, 33, 0, 56, 0, 35, 0, 127, 0, 128, 0, 48, 0, 126, 0, 127, 0, 48, 0, 120, 0, 131, 0, 51, 0, 23, 0, 44, 0, 51, 0, 44, 0, 137, 0, 124, 0, 39, 0, 119, 0, 126, 0, 38, 0, 128, 0, 118, 0, 38, 0, 48, 0, 128, 0, 50, 0, 131, 0, 130, 0, 50, 0, 51, 0, 131, 0, 43, 0, 133, 0, 132, 0, 43, 0, 53, 0, 133, 0, 53, 0, 134, 0, 133, 0, 118, 0, 54, 0, 38, 0, 118, 0, 122, 0, 54, 0, 105, 0, 123, 0, 125, 0, 105, 0, 103, 0, 123, 0, 105, 0, 96, 0, 95, 0, 105, 0, 104, 0, 96, 0, 136, 0, 137, 0, 104, 0, 127, 0, 128, 0, 108, 0, 132, 0, 123, 0, 103, 0, 129, 0, 121, 0, 109, 0, 120, 0, 131, 0, 111, 0, 83, 0, 104, 0, 111, 0, 119, 0, 39, 0, 49, 0, 111, 0, 124, 0, 120, 0, 111, 0, 104, 0, 124, 0, 104, 0, 137, 0, 124, 0, 105, 0, 125, 0, 135, 0, 98, 0, 108, 0, 128, 0, 109, 0, 130, 0, 129, 0, 109, 0, 110, 0, 130, 0, 110, 0, 131, 0, 130, 0, 110, 0, 111, 0, 131, 0, 113, 0, 134, 0, 133, 0, 113, 0, 114, 0, 134, 0), 0.0674687, PackedByteArray(96, 0, 119, 0, 66, 0, 66, 0, 103, 0, 105, 0, 66, 0, 105, 0, 96, 0, 135, 0, 104, 0, 105, 0, 96, 0, 75, 0, 119, 0, 96, 0, 104, 0, 75, 0, 135, 0, 136, 0, 104, 0, 104, 0, 65, 0, 75, 0, 75, 0, 65, 0, 110, 0, 110, 0, 124, 0, 120, 0, 65, 0, 104, 0, 110, 0, 105, 0, 104, 0, 96, 0, 126, 0, 127, 0, 119, 0, 119, 0, 127, 0, 118, 0, 118, 0, 127, 0, 128, 0, 66, 0, 119, 0, 118, 0, 118, 0, 60, 0, 66, 0, 60, 0, 133, 0, 66, 0, 118, 0, 122, 0, 133, 0, 122, 0, 134, 0, 133, 0, 118, 0, 133, 0, 60, 0, 3, 0, 0, 0, 33, 0, 0, 0, 53, 0, 33, 0, 3, 0, 53, 0, 0, 0, 118, 0, 122, 0, 53, 0, 33, 0, 27, 0, 3, 0, 10, 0, 27, 0, 33, 0, 33, 0, 136, 0, 10, 0, 10, 0, 136, 0, 124, 0, 10, 0, 15, 0, 27, 0, 39, 0, 27, 0, 15, 0, 126, 0, 3, 0, 39, 0, 126, 0, 127, 0, 3, 0, 39, 0, 3, 0, 27, 0, 39, 0, 15, 0, 130, 0, 5, 0, 124, 0, 130, 0, 10, 0, 124, 0, 15, 0, 124, 0, 5, 0, 15, 0, 15, 0, 5, 0, 130, 0, 136, 0, 137, 0, 124, 0, 136, 0, 125, 0, 135, 0, 136, 0, 123, 0, 125, 0, 130, 0, 124, 0, 120, 0, 120, 0, 131, 0, 130, 0, 129, 0, 121, 0, 130, 0, 119, 0, 130, 0, 121, 0, 122, 0, 134, 0, 53, 0, 127, 0, 128, 0, 3, 0, 132, 0, 123, 0, 33, 0, 136, 0, 33, 0, 123, 0, 39, 0, 119, 0, 126, 0, 3, 0, 128, 0, 118, 0, 33, 0, 133, 0, 132, 0, 33, 0, 53, 0, 133, 0, 53, 0, 134, 0, 133, 0, 118, 0, 53, 0, 3, 0, 105, 0, 123, 0, 125, 0, 105, 0, 103, 0, 123, 0, 136, 0, 137, 0, 104, 0, 66, 0, 133, 0, 103, 0, 103, 0, 133, 0, 132, 0, 132, 0, 123, 0, 103, 0, 129, 0, 121, 0, 75, 0, 120, 0, 131, 0, 110, 0, 121, 0, 119, 0, 75, 0, 119, 0, 39, 0, 130, 0, 110, 0, 104, 0, 124, 0, 104, 0, 137, 0, 124, 0, 105, 0, 125, 0, 135, 0, 75, 0, 130, 0, 129, 0, 75, 0, 110, 0, 130, 0, 110, 0, 131, 0, 130, 0), 0.195405, PackedByteArray(125, 0, 119, 0, 133, 0, 125, 0, 133, 0, 123, 0, 132, 0, 123, 0, 133, 0, 133, 0, 122, 0, 134, 0, 118, 0, 122, 0, 133, 0, 133, 0, 119, 0, 118, 0, 119, 0, 127, 0, 118, 0, 118, 0, 127, 0, 128, 0, 126, 0, 127, 0, 119, 0, 125, 0, 129, 0, 119, 0, 121, 0, 119, 0, 129, 0, 125, 0, 137, 0, 129, 0, 135, 0, 137, 0, 125, 0, 135, 0, 136, 0, 137, 0, 137, 0, 131, 0, 129, 0, 129, 0, 131, 0, 130, 0, 131, 0, 137, 0, 124, 0, 131, 0, 124, 0, 120, 0, 136, 0, 126, 0, 132, 0, 126, 0, 127, 0, 128, 0, 132, 0, 126, 0, 128, 0, 118, 0, 132, 0, 128, 0, 118, 0, 122, 0, 132, 0, 122, 0, 134, 0, 132, 0, 132, 0, 134, 0, 133, 0, 136, 0, 132, 0, 123, 0, 136, 0, 123, 0, 125, 0, 136, 0, 125, 0, 135, 0, 136, 0, 130, 0, 126, 0, 119, 0, 126, 0, 130, 0, 119, 0, 130, 0, 121, 0, 129, 0, 121, 0, 130, 0, 136, 0, 124, 0, 130, 0, 136, 0, 137, 0, 124, 0, 130, 0, 124, 0, 120, 0, 120, 0, 131, 0, 130, 0)],
"name": "CardBase",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 140,
"vertex_data": PackedByteArray(246, 255, 185, 0, 146, 244, 0, 0, 246, 255, 69, 255, 146, 244, 0, 0, 246, 255, 174, 244, 69, 255, 0, 0, 246, 255, 185, 0, 108, 11, 0, 0, 246, 255, 174, 244, 185, 0, 0, 0, 246, 255, 69, 255, 108, 11, 0, 0, 246, 255, 232, 7, 251, 254, 0, 0, 246, 255, 44, 2, 37, 249, 0, 0, 246, 255, 148, 4, 156, 252, 0, 0, 246, 255, 209, 253, 9, 249, 0, 0, 246, 255, 28, 248, 56, 255, 0, 0, 246, 255, 102, 251, 229, 252, 0, 0, 246, 255, 44, 2, 217, 6, 0, 0, 246, 255, 232, 7, 3, 1, 0, 0, 246, 255, 148, 4, 98, 3, 0, 0, 246, 255, 28, 248, 198, 0, 0, 0, 246, 255, 209, 253, 245, 6, 0, 0, 246, 255, 102, 251, 25, 3, 0, 0, 246, 255, 0, 0, 79, 11, 0, 0, 246, 255, 0, 0, 175, 244, 0, 0, 246, 255, 79, 11, 255, 255, 0, 0, 246, 255, 175, 244, 255, 255, 0, 0, 246, 255, 255, 255, 175, 244, 0, 0, 246, 255, 255, 255, 79, 11, 0, 0, 246, 255, 175, 244, 0, 0, 0, 0, 246, 255, 79, 11, 0, 0, 0, 0, 246, 255, 166, 7, 79, 0, 0, 0, 246, 255, 14, 4, 221, 2, 0, 0, 246, 255, 131, 1, 134, 6, 0, 0, 246, 255, 240, 251, 151, 2, 0, 0, 246, 255, 88, 248, 13, 0, 0, 0, 246, 255, 123, 254, 166, 6, 0, 0, 246, 255, 14, 4, 33, 253, 0, 0, 246, 255, 166, 7, 175, 255, 0, 0, 246, 255, 131, 1, 120, 249, 0, 0, 246, 255, 88, 248, 241, 255, 0, 0, 246, 255, 240, 251, 103, 253, 0, 0, 246, 255, 123, 254, 88, 249, 0, 0, 234, 217, 0, 0, 79, 11, 0, 0, 234, 217, 79, 11, 0, 0, 0, 0, 234, 217, 255, 255, 79, 11, 0, 0, 234, 217, 175, 244, 0, 0, 0, 0, 234, 217, 0, 0, 175, 244, 0, 0, 234, 217, 79, 11, 255, 255, 0, 0, 234, 217, 255, 255, 175, 244, 0, 0, 234, 217, 175, 244, 255, 255, 0, 0, 234, 217, 166, 7, 79, 0, 0, 0, 234, 217, 14, 4, 221, 2, 0, 0, 234, 217, 131, 1, 134, 6, 0, 0, 234, 217, 88, 248, 13, 0, 0, 0, 234, 217, 240, 251, 151, 2, 0, 0, 234, 217, 123, 254, 166, 6, 0, 0, 234, 217, 166, 7, 175, 255, 0, 0, 234, 217, 14, 4, 33, 253, 0, 0, 234, 217, 131, 1, 120, 249, 0, 0, 234, 217, 88, 248, 241, 255, 0, 0, 234, 217, 240, 251, 103, 253, 0, 0, 234, 217, 123, 254, 88, 249, 0, 0, 246, 255, 87, 11, 69, 255, 0, 0, 246, 255, 87, 11, 185, 0, 0, 0, 0, 0, 185, 0, 146, 244, 0, 0, 0, 0, 69, 255, 146, 244, 0, 0, 0, 0, 174, 244, 69, 255, 0, 0, 0, 0, 185, 0, 108, 11, 0, 0, 0, 0, 174, 244, 185, 0, 0, 0, 0, 0, 69, 255, 108, 11, 0, 0, 0, 0, 232, 7, 251, 254, 0, 0, 0, 0, 44, 2, 37, 249, 0, 0, 0, 0, 148, 4, 156, 252, 0, 0, 0, 0, 209, 253, 9, 249, 0, 0, 0, 0, 28, 248, 56, 255, 0, 0, 0, 0, 102, 251, 229, 252, 0, 0, 0, 0, 44, 2, 217, 6, 0, 0, 0, 0, 232, 7, 3, 1, 0, 0, 0, 0, 148, 4, 98, 3, 0, 0, 0, 0, 28, 248, 198, 0, 0, 0, 0, 0, 209, 253, 245, 6, 0, 0, 0, 0, 102, 251, 25, 3, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 79, 11, 255, 255, 0, 0, 0, 0, 175, 244, 255, 255, 0, 0, 0, 0, 255, 255, 175, 244, 0, 0, 0, 0, 255, 255, 79, 11, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 166, 7, 79, 0, 0, 0, 0, 0, 14, 4, 221, 2, 0, 0, 0, 0, 131, 1, 134, 6, 0, 0, 0, 0, 240, 251, 151, 2, 0, 0, 0, 0, 88, 248, 13, 0, 0, 0, 0, 0, 123, 254, 166, 6, 0, 0, 0, 0, 14, 4, 33, 253, 0, 0, 0, 0, 166, 7, 175, 255, 0, 0, 0, 0, 131, 1, 120, 249, 0, 0, 0, 0, 88, 248, 241, 255, 0, 0, 0, 0, 240, 251, 103, 253, 0, 0, 0, 0, 123, 254, 88, 249, 0, 0, 11, 38, 0, 0, 79, 11, 0, 0, 11, 38, 79, 11, 0, 0, 0, 0, 11, 38, 255, 255, 79, 11, 0, 0, 11, 38, 175, 244, 0, 0, 0, 0, 11, 38, 0, 0, 175, 244, 0, 0, 11, 38, 79, 11, 255, 255, 0, 0, 11, 38, 255, 255, 175, 244, 0, 0, 11, 38, 175, 244, 255, 255, 0, 0, 11, 38, 166, 7, 79, 0, 0, 0, 11, 38, 14, 4, 221, 2, 0, 0, 11, 38, 131, 1, 134, 6, 0, 0, 11, 38, 88, 248, 13, 0, 0, 0, 11, 38, 240, 251, 151, 2, 0, 0, 11, 38, 123, 254, 166, 6, 0, 0, 11, 38, 166, 7, 175, 255, 0, 0, 11, 38, 14, 4, 33, 253, 0, 0, 11, 38, 131, 1, 120, 249, 0, 0, 11, 38, 88, 248, 241, 255, 0, 0, 11, 38, 240, 251, 103, 253, 0, 0, 11, 38, 123, 254, 88, 249, 0, 0, 251, 127, 0, 0, 79, 11, 0, 0, 251, 127, 79, 11, 0, 0, 0, 0, 251, 127, 255, 255, 79, 11, 0, 0, 251, 127, 175, 244, 0, 0, 0, 0, 251, 127, 0, 0, 175, 244, 0, 0, 251, 127, 79, 11, 255, 255, 0, 0, 251, 127, 255, 255, 175, 244, 0, 0, 251, 127, 175, 244, 255, 255, 0, 0, 251, 127, 166, 7, 79, 0, 0, 0, 251, 127, 14, 4, 221, 2, 0, 0, 251, 127, 131, 1, 134, 6, 0, 0, 251, 127, 88, 248, 13, 0, 0, 0, 251, 127, 240, 251, 151, 2, 0, 0, 251, 127, 123, 254, 166, 6, 0, 0, 251, 127, 166, 7, 175, 255, 0, 0, 251, 127, 14, 4, 33, 253, 0, 0, 251, 127, 131, 1, 120, 249, 0, 0, 251, 127, 88, 248, 241, 255, 0, 0, 251, 127, 240, 251, 103, 253, 0, 0, 251, 127, 123, 254, 88, 249, 0, 0, 0, 0, 87, 11, 69, 255, 0, 0, 0, 0, 87, 11, 185, 0, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_hsh2j"]
resource_name = "DRC_Card_Model_Test03_CardBase_Cube_001"
_surfaces = [{
"aabb": AABB(-0.0370001, -2.49985, -1.78976, 0.0740101, 4.9997, 3.57952),
"attribute_data": PackedByteArray(174, 134, 163, 255, 174, 134, 122, 129, 95, 129, 186, 134, 84, 250, 163, 255, 162, 255, 186, 134, 84, 250, 122, 129, 132, 129, 19, 252, 105, 132, 235, 254, 177, 130, 185, 253, 119, 132, 50, 130, 102, 129, 7, 133, 141, 130, 101, 131, 153, 252, 235, 254, 126, 255, 19, 252, 81, 254, 185, 253, 156, 255, 7, 133, 139, 252, 50, 130, 117, 254, 101, 131, 98, 250, 255, 255, 159, 134, 255, 255, 3, 129, 100, 250, 3, 129, 185, 134, 159, 134, 30, 129, 98, 250, 30, 129, 255, 255, 185, 134, 255, 255, 100, 250, 215, 255, 52, 252, 147, 254, 252, 253, 194, 252, 62, 255, 182, 254, 33, 131, 248, 255, 233, 132, 178, 252, 222, 129, 111, 130, 252, 253, 43, 129, 52, 252, 64, 132, 62, 255, 10, 129, 233, 132, 76, 130, 33, 131, 80, 132, 222, 129, 98, 250, 255, 255, 255, 255, 100, 250, 98, 250, 30, 129, 255, 255, 185, 134, 159, 134, 255, 255, 3, 129, 100, 250, 159, 134, 30, 129, 3, 129, 185, 134, 215, 255, 52, 252, 147, 254, 252, 253, 194, 252, 62, 255, 248, 255, 233, 132, 182, 254, 33, 131, 178, 252, 222, 129, 43, 129, 52, 252, 111, 130, 252, 253, 64, 132, 62, 255, 10, 129, 233, 132, 76, 130, 33, 131, 80, 132, 222, 129, 95, 129, 96, 250, 162, 255, 96, 250, 140, 134, 163, 255, 140, 134, 122, 129, 61, 129, 186, 134, 50, 250, 163, 255, 128, 255, 186, 134, 50, 250, 122, 129, 98, 129, 19, 252, 71, 132, 235, 254, 143, 130, 185, 253, 85, 132, 50, 130, 68, 129, 7, 133, 107, 130, 101, 131, 119, 252, 235, 254, 92, 255, 19, 252, 47, 254, 185, 253, 122, 255, 7, 133, 105, 252, 50, 130, 83, 254, 101, 131, 64, 250, 255, 255, 125, 134, 255, 255, 225, 128, 100, 250, 225, 128, 185, 134, 125, 134, 30, 129, 64, 250, 30, 129, 220, 255, 185, 134, 220, 255, 100, 250, 181, 255, 52, 252, 113, 254, 252, 253, 160, 252, 62, 255, 148, 254, 33, 131, 214, 255, 233, 132, 144, 252, 222, 129, 77, 130, 252, 253, 9, 129, 52, 252, 30, 132, 62, 255, 232, 128, 233, 132, 42, 130, 33, 131, 46, 132, 222, 129, 64, 250, 255, 255, 220, 255, 100, 250, 64, 250, 30, 129, 220, 255, 185, 134, 125, 134, 255, 255, 225, 128, 100, 250, 125, 134, 30, 129, 225, 128, 185, 134, 181, 255, 52, 252, 113, 254, 252, 253, 160, 252, 62, 255, 214, 255, 233, 132, 148, 254, 33, 131, 144, 252, 222, 129, 9, 129, 52, 252, 77, 130, 252, 253, 30, 132, 62, 255, 232, 128, 233, 132, 42, 130, 33, 131, 46, 132, 222, 129, 64, 250, 255, 255, 98, 250, 255, 255, 220, 255, 100, 250, 255, 255, 100, 250, 64, 250, 30, 129, 98, 250, 30, 129, 220, 255, 185, 134, 255, 255, 185, 134, 125, 134, 255, 255, 159, 134, 255, 255, 225, 128, 100, 250, 3, 129, 100, 250, 125, 134, 30, 129, 159, 134, 30, 129, 225, 128, 185, 134, 3, 129, 185, 134, 181, 255, 52, 252, 215, 255, 52, 252, 113, 254, 252, 253, 147, 254, 252, 253, 160, 252, 62, 255, 194, 252, 62, 255, 214, 255, 233, 132, 248, 255, 233, 132, 148, 254, 33, 131, 182, 254, 33, 131, 144, 252, 222, 129, 178, 252, 222, 129, 9, 129, 52, 252, 43, 129, 52, 252, 77, 130, 252, 253, 111, 130, 252, 253, 30, 132, 62, 255, 64, 132, 62, 255, 232, 128, 233, 132, 10, 129, 233, 132, 42, 130, 33, 131, 76, 130, 33, 131, 46, 132, 222, 129, 80, 132, 222, 129, 61, 129, 96, 250, 128, 255, 96, 250, 64, 250, 255, 255, 98, 250, 255, 255, 220, 255, 100, 250, 255, 255, 100, 250, 64, 250, 30, 129, 98, 250, 30, 129, 220, 255, 185, 134, 255, 255, 185, 134, 125, 134, 255, 255, 159, 134, 255, 255, 225, 128, 100, 250, 3, 129, 100, 250, 125, 134, 30, 129, 159, 134, 30, 129, 225, 128, 185, 134, 3, 129, 185, 134, 181, 255, 52, 252, 215, 255, 52, 252, 113, 254, 252, 253, 147, 254, 252, 253, 160, 252, 62, 255, 194, 252, 62, 255, 214, 255, 233, 132, 248, 255, 233, 132, 148, 254, 33, 131, 182, 254, 33, 131, 144, 252, 222, 129, 178, 252, 222, 129, 9, 129, 52, 252, 43, 129, 52, 252, 77, 130, 252, 253, 111, 130, 252, 253, 30, 132, 62, 255, 64, 132, 62, 255, 232, 128, 233, 132, 10, 129, 233, 132, 42, 130, 33, 131, 76, 130, 33, 131, 46, 132, 222, 129, 80, 132, 222, 129, 84, 250, 163, 255, 84, 250, 163, 255, 84, 250, 163, 255, 84, 250, 122, 129, 147, 254, 252, 253, 43, 129, 52, 252, 43, 129, 52, 252, 43, 129, 52, 252, 43, 129, 52, 252, 43, 129, 52, 252, 255, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 98, 129, 19, 252, 122, 255, 7, 133, 122, 255, 7, 133, 122, 255, 7, 133, 122, 255, 7, 133, 42, 130, 33, 131, 225, 128, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 64, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 220, 255, 100, 250, 220, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 64, 250, 30, 129, 64, 250, 30, 129, 98, 250, 30, 129, 220, 255, 185, 134, 220, 255, 185, 134, 255, 255, 185, 134, 125, 134, 255, 255, 159, 134, 255, 255, 159, 134, 255, 255, 225, 128, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 3, 129, 100, 250, 3, 129, 100, 250, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 159, 134, 30, 129, 159, 134, 30, 129, 225, 128, 185, 134, 225, 128, 185, 134, 3, 129, 185, 134, 181, 255, 52, 252, 215, 255, 52, 252, 215, 255, 52, 252, 113, 254, 252, 253, 147, 254, 252, 253, 147, 254, 252, 253, 160, 252, 62, 255, 194, 252, 62, 255, 194, 252, 62, 255, 214, 255, 233, 132, 214, 255, 233, 132, 248, 255, 233, 132, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 182, 254, 33, 131, 182, 254, 33, 131, 182, 254, 33, 131, 144, 252, 222, 129, 144, 252, 222, 129, 178, 252, 222, 129, 9, 129, 52, 252, 9, 129, 52, 252, 43, 129, 52, 252, 43, 129, 52, 252, 77, 130, 252, 253, 77, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 30, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 232, 128, 233, 132, 232, 128, 233, 132, 232, 128, 233, 132, 10, 129, 233, 132, 42, 130, 33, 131, 42, 130, 33, 131, 76, 130, 33, 131, 76, 130, 33, 131, 76, 130, 33, 131, 46, 132, 222, 129, 46, 132, 222, 129, 80, 132, 222, 129, 174, 134, 122, 129, 84, 250, 163, 255, 153, 252, 235, 254, 156, 255, 7, 133, 159, 134, 255, 255, 98, 250, 30, 129, 215, 255, 52, 252, 147, 254, 252, 253, 43, 129, 52, 252, 10, 129, 233, 132, 98, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 255, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 3, 129, 100, 250, 3, 129, 100, 250, 3, 129, 100, 250, 159, 134, 30, 129, 159, 134, 30, 129, 159, 134, 30, 129, 194, 252, 62, 255, 194, 252, 62, 255, 194, 252, 62, 255, 194, 252, 62, 255, 248, 255, 233, 132, 248, 255, 233, 132, 248, 255, 233, 132, 248, 255, 233, 132, 248, 255, 233, 132, 182, 254, 33, 131, 182, 254, 33, 131, 182, 254, 33, 131, 178, 252, 222, 129, 178, 252, 222, 129, 178, 252, 222, 129, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 64, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 76, 130, 33, 131, 76, 130, 33, 131, 80, 132, 222, 129, 80, 132, 222, 129, 80, 132, 222, 129, 119, 252, 235, 254, 92, 255, 19, 252, 122, 255, 7, 133, 125, 134, 255, 255, 125, 134, 255, 255, 64, 250, 30, 129, 64, 250, 30, 129, 9, 129, 52, 252, 9, 129, 52, 252, 232, 128, 233, 132, 232, 128, 233, 132, 42, 130, 33, 131, 42, 130, 33, 131, 64, 250, 255, 255, 64, 250, 255, 255, 64, 250, 255, 255, 64, 250, 255, 255, 220, 255, 100, 250, 220, 255, 100, 250, 220, 255, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 113, 254, 252, 253, 113, 254, 252, 253, 160, 252, 62, 255, 160, 252, 62, 255, 160, 252, 62, 255, 214, 255, 233, 132, 214, 255, 233, 132, 214, 255, 233, 132, 214, 255, 233, 132, 214, 255, 233, 132, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 144, 252, 222, 129, 144, 252, 222, 129, 144, 252, 222, 129, 144, 252, 222, 129, 144, 252, 222, 129, 77, 130, 252, 253, 77, 130, 252, 253, 77, 130, 252, 253, 30, 132, 62, 255, 30, 132, 62, 255, 30, 132, 62, 255, 30, 132, 62, 255, 64, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 220, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 64, 250, 30, 129, 64, 250, 30, 129, 98, 250, 30, 129, 220, 255, 185, 134, 220, 255, 185, 134, 220, 255, 185, 134, 255, 255, 185, 134, 125, 134, 255, 255, 159, 134, 255, 255, 159, 134, 255, 255, 225, 128, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 3, 129, 100, 250, 3, 129, 100, 250, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 159, 134, 30, 129, 225, 128, 185, 134, 225, 128, 185, 134, 3, 129, 185, 134, 3, 129, 185, 134, 181, 255, 52, 252, 215, 255, 52, 252, 215, 255, 52, 252, 215, 255, 52, 252, 113, 254, 252, 253, 113, 254, 252, 253, 147, 254, 252, 253, 147, 254, 252, 253, 160, 252, 62, 255, 160, 252, 62, 255, 160, 252, 62, 255, 194, 252, 62, 255, 194, 252, 62, 255, 194, 252, 62, 255, 214, 255, 233, 132, 214, 255, 233, 132, 248, 255, 233, 132, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 182, 254, 33, 131, 144, 252, 222, 129, 144, 252, 222, 129, 144, 252, 222, 129, 178, 252, 222, 129, 178, 252, 222, 129, 178, 252, 222, 129, 9, 129, 52, 252, 9, 129, 52, 252, 43, 129, 52, 252, 43, 129, 52, 252, 77, 130, 252, 253, 77, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 30, 132, 62, 255, 30, 132, 62, 255, 30, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 232, 128, 233, 132, 232, 128, 233, 132, 232, 128, 233, 132, 10, 129, 233, 132, 10, 129, 233, 132, 42, 130, 33, 131, 42, 130, 33, 131, 76, 130, 33, 131, 76, 130, 33, 131, 46, 132, 222, 129, 46, 132, 222, 129, 80, 132, 222, 129, 80, 132, 222, 129, 80, 132, 222, 129),
"format": 34896613399,
"index_count": 828,
"index_data": PackedByteArray(62, 0, 159, 0, 158, 0, 62, 0, 64, 0, 159, 0, 40, 0, 31, 0, 23, 0, 40, 0, 51, 0, 31, 0, 49, 0, 24, 0, 30, 0, 49, 0, 41, 0, 24, 0, 66, 0, 159, 0, 73, 0, 66, 0, 158, 0, 159, 0, 52, 0, 20, 0, 33, 0, 52, 0, 43, 0, 20, 0, 3, 0, 7, 0, 12, 0, 3, 0, 0, 0, 7, 0, 46, 0, 27, 0, 26, 0, 46, 0, 47, 0, 27, 0, 47, 0, 28, 0, 27, 0, 47, 0, 48, 0, 28, 0, 55, 0, 36, 0, 35, 0, 55, 0, 56, 0, 36, 0, 56, 0, 37, 0, 36, 0, 56, 0, 57, 0, 37, 0, 42, 0, 34, 0, 19, 0, 42, 0, 54, 0, 34, 0, 38, 0, 19, 0, 18, 0, 38, 0, 42, 0, 19, 0, 39, 0, 24, 0, 41, 0, 39, 0, 25, 0, 24, 0, 1, 0, 23, 0, 5, 0, 1, 0, 22, 0, 23, 0, 2, 0, 59, 0, 58, 0, 2, 0, 4, 0, 59, 0, 13, 0, 27, 0, 14, 0, 13, 0, 26, 0, 27, 0, 14, 0, 28, 0, 12, 0, 14, 0, 27, 0, 28, 0, 17, 0, 30, 0, 15, 0, 17, 0, 29, 0, 30, 0, 16, 0, 29, 0, 17, 0, 16, 0, 31, 0, 29, 0, 8, 0, 33, 0, 6, 0, 8, 0, 32, 0, 33, 0, 7, 0, 32, 0, 8, 0, 7, 0, 34, 0, 32, 0, 10, 0, 36, 0, 11, 0, 10, 0, 35, 0, 36, 0, 11, 0, 37, 0, 9, 0, 11, 0, 36, 0, 37, 0, 0, 0, 34, 0, 7, 0, 0, 0, 19, 0, 34, 0, 6, 0, 20, 0, 58, 0, 6, 0, 33, 0, 20, 0, 9, 0, 22, 0, 1, 0, 9, 0, 37, 0, 22, 0, 2, 0, 35, 0, 10, 0, 2, 0, 21, 0, 35, 0, 12, 0, 18, 0, 3, 0, 12, 0, 28, 0, 18, 0, 59, 0, 26, 0, 13, 0, 59, 0, 25, 0, 26, 0, 5, 0, 31, 0, 16, 0, 5, 0, 23, 0, 31, 0, 15, 0, 24, 0, 4, 0, 15, 0, 30, 0, 24, 0, 127, 0, 54, 0, 42, 0, 127, 0, 151, 0, 54, 0, 155, 0, 57, 0, 56, 0, 155, 0, 157, 0, 57, 0, 153, 0, 56, 0, 55, 0, 153, 0, 155, 0, 56, 0, 32, 0, 54, 0, 53, 0, 32, 0, 34, 0, 54, 0, 33, 0, 53, 0, 52, 0, 33, 0, 32, 0, 53, 0, 29, 0, 51, 0, 50, 0, 29, 0, 31, 0, 51, 0, 30, 0, 50, 0, 49, 0, 30, 0, 29, 0, 50, 0, 137, 0, 48, 0, 47, 0, 137, 0, 139, 0, 48, 0, 135, 0, 47, 0, 46, 0, 135, 0, 137, 0, 47, 0, 18, 0, 48, 0, 38, 0, 18, 0, 28, 0, 48, 0, 35, 0, 45, 0, 55, 0, 35, 0, 21, 0, 45, 0, 147, 0, 43, 0, 52, 0, 147, 0, 129, 0, 43, 0, 141, 0, 41, 0, 49, 0, 141, 0, 125, 0, 41, 0, 123, 0, 51, 0, 40, 0, 123, 0, 145, 0, 51, 0, 26, 0, 39, 0, 46, 0, 26, 0, 25, 0, 39, 0, 45, 0, 129, 0, 133, 0, 45, 0, 43, 0, 129, 0, 22, 0, 57, 0, 44, 0, 22, 0, 37, 0, 57, 0, 23, 0, 44, 0, 40, 0, 23, 0, 22, 0, 44, 0, 59, 0, 24, 0, 25, 0, 59, 0, 4, 0, 24, 0, 2, 0, 15, 0, 4, 0, 2, 0, 10, 0, 15, 0, 10, 0, 17, 0, 15, 0, 10, 0, 11, 0, 17, 0, 11, 0, 16, 0, 17, 0, 11, 0, 9, 0, 16, 0, 9, 0, 5, 0, 16, 0, 9, 0, 1, 0, 5, 0, 3, 0, 19, 0, 0, 0, 3, 0, 18, 0, 19, 0, 7, 0, 14, 0, 12, 0, 7, 0, 8, 0, 14, 0, 8, 0, 13, 0, 14, 0, 8, 0, 6, 0, 13, 0, 20, 0, 45, 0, 21, 0, 20, 0, 43, 0, 45, 0, 58, 0, 21, 0, 2, 0, 58, 0, 20, 0, 21, 0, 40, 0, 131, 0, 123, 0, 40, 0, 44, 0, 131, 0, 44, 0, 157, 0, 131, 0, 44, 0, 57, 0, 157, 0, 46, 0, 121, 0, 135, 0, 46, 0, 39, 0, 121, 0, 55, 0, 133, 0, 153, 0, 55, 0, 45, 0, 133, 0, 38, 0, 139, 0, 119, 0, 38, 0, 48, 0, 139, 0, 49, 0, 143, 0, 141, 0, 49, 0, 50, 0, 143, 0, 50, 0, 145, 0, 143, 0, 50, 0, 51, 0, 145, 0, 52, 0, 149, 0, 147, 0, 52, 0, 53, 0, 149, 0, 53, 0, 151, 0, 149, 0, 53, 0, 54, 0, 151, 0, 119, 0, 42, 0, 38, 0, 119, 0, 127, 0, 42, 0, 158, 0, 81, 0, 62, 0, 158, 0, 80, 0, 81, 0, 105, 0, 128, 0, 132, 0, 105, 0, 103, 0, 128, 0, 100, 0, 91, 0, 83, 0, 100, 0, 111, 0, 91, 0, 109, 0, 84, 0, 90, 0, 109, 0, 101, 0, 84, 0, 112, 0, 80, 0, 93, 0, 112, 0, 103, 0, 80, 0, 63, 0, 67, 0, 72, 0, 63, 0, 60, 0, 67, 0, 106, 0, 87, 0, 86, 0, 106, 0, 107, 0, 87, 0, 107, 0, 88, 0, 87, 0, 107, 0, 108, 0, 88, 0, 115, 0, 96, 0, 95, 0, 115, 0, 116, 0, 96, 0, 116, 0, 97, 0, 96, 0, 116, 0, 117, 0, 97, 0, 102, 0, 94, 0, 79, 0, 102, 0, 114, 0, 94, 0, 98, 0, 79, 0, 78, 0, 98, 0, 102, 0, 79, 0, 99, 0, 84, 0, 101, 0, 99, 0, 85, 0, 84, 0, 61, 0, 83, 0, 65, 0, 61, 0, 82, 0, 83, 0, 64, 0, 85, 0, 159, 0, 64, 0, 84, 0, 85, 0, 73, 0, 87, 0, 74, 0, 73, 0, 86, 0, 87, 0, 74, 0, 88, 0, 72, 0, 74, 0, 87, 0, 88, 0, 77, 0, 90, 0, 75, 0, 77, 0, 89, 0, 90, 0, 76, 0, 89, 0, 77, 0, 76, 0, 91, 0, 89, 0, 68, 0, 93, 0, 66, 0, 68, 0, 92, 0, 93, 0, 67, 0, 92, 0, 68, 0, 67, 0, 94, 0, 92, 0, 70, 0, 96, 0, 71, 0, 70, 0, 95, 0, 96, 0, 71, 0, 97, 0, 69, 0, 71, 0, 96, 0, 97, 0, 60, 0, 94, 0, 67, 0, 60, 0, 79, 0, 94, 0, 66, 0, 80, 0, 158, 0, 66, 0, 93, 0, 80, 0, 69, 0, 82, 0, 61, 0, 69, 0, 97, 0, 82, 0, 62, 0, 95, 0, 70, 0, 62, 0, 81, 0, 95, 0, 72, 0, 78, 0, 63, 0, 72, 0, 88, 0, 78, 0, 159, 0, 86, 0, 73, 0, 159, 0, 85, 0, 86, 0, 65, 0, 91, 0, 76, 0, 65, 0, 83, 0, 91, 0, 75, 0, 84, 0, 64, 0, 75, 0, 90, 0, 84, 0, 126, 0, 114, 0, 102, 0, 126, 0, 150, 0, 114, 0, 154, 0, 117, 0, 116, 0, 154, 0, 156, 0, 117, 0, 152, 0, 116, 0, 115, 0, 152, 0, 154, 0, 116, 0, 92, 0, 114, 0, 113, 0, 92, 0, 94, 0, 114, 0, 93, 0, 113, 0, 112, 0, 93, 0, 92, 0, 113, 0, 89, 0, 111, 0, 110, 0, 89, 0, 91, 0, 111, 0, 90, 0, 110, 0, 109, 0, 90, 0, 89, 0, 110, 0, 136, 0, 108, 0, 107, 0, 136, 0, 138, 0, 108, 0, 134, 0, 107, 0, 106, 0, 134, 0, 136, 0, 107, 0, 63, 0, 79, 0, 60, 0, 63, 0, 78, 0, 79, 0, 78, 0, 108, 0, 98, 0, 78, 0, 88, 0, 108, 0, 95, 0, 105, 0, 115, 0, 95, 0, 81, 0, 105, 0, 146, 0, 103, 0, 112, 0, 146, 0, 128, 0, 103, 0, 140, 0, 101, 0, 109, 0, 140, 0, 124, 0, 101, 0, 122, 0, 111, 0, 100, 0, 122, 0, 144, 0, 111, 0, 86, 0, 99, 0, 106, 0, 86, 0, 85, 0, 99, 0, 82, 0, 117, 0, 104, 0, 82, 0, 97, 0, 117, 0, 83, 0, 104, 0, 100, 0, 83, 0, 82, 0, 104, 0, 124, 0, 99, 0, 101, 0, 124, 0, 120, 0, 99, 0, 62, 0, 75, 0, 64, 0, 62, 0, 70, 0, 75, 0, 70, 0, 77, 0, 75, 0, 70, 0, 71, 0, 77, 0, 71, 0, 76, 0, 77, 0, 71, 0, 69, 0, 76, 0, 69, 0, 65, 0, 76, 0, 69, 0, 61, 0, 65, 0, 118, 0, 102, 0, 98, 0, 118, 0, 126, 0, 102, 0, 67, 0, 74, 0, 72, 0, 67, 0, 68, 0, 74, 0, 68, 0, 73, 0, 74, 0, 68, 0, 66, 0, 73, 0, 80, 0, 105, 0, 81, 0, 80, 0, 103, 0, 105, 0, 121, 0, 41, 0, 125, 0, 121, 0, 39, 0, 41, 0, 100, 0, 130, 0, 122, 0, 100, 0, 104, 0, 130, 0, 104, 0, 156, 0, 130, 0, 104, 0, 117, 0, 156, 0, 106, 0, 120, 0, 134, 0, 106, 0, 99, 0, 120, 0, 115, 0, 132, 0, 152, 0, 115, 0, 105, 0, 132, 0, 98, 0, 138, 0, 118, 0, 98, 0, 108, 0, 138, 0, 109, 0, 142, 0, 140, 0, 109, 0, 110, 0, 142, 0, 110, 0, 144, 0, 142, 0, 110, 0, 111, 0, 144, 0, 112, 0, 148, 0, 146, 0, 112, 0, 113, 0, 148, 0, 113, 0, 150, 0, 148, 0, 113, 0, 114, 0, 150, 0, 6, 0, 59, 0, 13, 0, 6, 0, 58, 0, 59, 0),
"lods": [0.0409402, PackedByteArray(70, 0, 73, 0, 66, 0, 73, 0, 148, 1, 72, 0, 66, 0, 73, 0, 72, 0, 72, 0, 127, 1, 63, 0, 63, 0, 66, 0, 72, 0, 63, 0, 60, 0, 66, 0, 63, 0, 117, 1, 60, 0, 63, 0, 126, 1, 117, 1, 60, 0, 117, 1, 170, 1, 60, 0, 170, 1, 66, 0, 66, 0, 170, 1, 167, 1, 66, 0, 167, 1, 120, 1, 126, 1, 114, 0, 116, 1, 66, 0, 120, 1, 122, 1, 120, 1, 145, 1, 122, 1, 120, 1, 134, 1, 145, 1, 66, 0, 122, 1, 70, 0, 70, 0, 122, 1, 125, 1, 121, 1, 113, 0, 134, 1, 134, 1, 113, 0, 236, 1, 134, 1, 236, 1, 233, 1, 70, 0, 125, 1, 61, 0, 61, 0, 125, 1, 136, 1, 248, 1, 253, 1, 136, 1, 247, 1, 136, 1, 144, 1, 61, 0, 136, 1, 118, 1, 157, 1, 118, 1, 162, 1, 115, 1, 157, 1, 152, 1, 61, 0, 118, 1, 65, 0, 65, 0, 118, 1, 75, 0, 61, 0, 65, 0, 75, 0, 75, 0, 118, 1, 158, 1, 70, 0, 61, 0, 75, 0, 70, 0, 75, 0, 73, 0, 75, 0, 152, 1, 73, 0, 130, 1, 73, 0, 152, 1, 186, 1, 130, 1, 154, 1, 130, 1, 147, 1, 114, 1, 205, 1, 147, 1, 130, 1, 205, 1, 209, 1, 147, 1, 209, 1, 149, 1, 147, 1, 113, 1, 147, 1, 149, 1, 113, 1, 149, 1, 126, 1, 132, 1, 178, 1, 205, 1, 187, 1, 178, 1, 131, 1, 174, 1, 172, 1, 126, 1, 174, 1, 189, 1, 172, 1, 189, 1, 241, 1, 171, 1, 128, 1, 214, 1, 174, 1, 63, 1, 6, 0, 64, 1, 64, 1, 72, 1, 63, 1, 63, 1, 72, 1, 66, 1, 64, 1, 85, 1, 72, 1, 63, 1, 66, 1, 0, 0, 63, 1, 0, 0, 6, 0, 0, 0, 66, 1, 103, 1, 0, 0, 103, 1, 6, 0, 6, 0, 103, 1, 100, 1, 64, 1, 69, 1, 85, 1, 6, 0, 100, 1, 70, 1, 13, 0, 69, 1, 64, 1, 6, 0, 13, 0, 64, 1, 13, 0, 68, 1, 69, 1, 6, 0, 70, 1, 71, 1, 206, 1, 85, 1, 76, 1, 6, 0, 71, 1, 10, 0, 10, 0, 13, 0, 6, 0, 10, 0, 15, 0, 13, 0, 13, 0, 65, 1, 68, 1, 76, 1, 68, 1, 65, 1, 76, 1, 65, 1, 89, 1, 89, 1, 50, 0, 143, 0, 91, 1, 143, 0, 221, 1, 65, 1, 94, 1, 89, 1, 221, 1, 188, 1, 90, 1, 180, 1, 92, 1, 188, 1, 17, 0, 94, 1, 65, 1, 10, 0, 17, 0, 65, 1, 17, 0, 67, 1, 94, 1, 94, 1, 67, 1, 97, 1, 97, 1, 131, 0, 123, 0, 97, 1, 82, 1, 131, 0, 5, 0, 67, 1, 17, 0, 62, 1, 82, 1, 67, 1, 62, 1, 67, 1, 5, 0, 62, 1, 5, 0, 17, 0, 10, 0, 62, 1, 17, 0, 62, 1, 110, 1, 82, 1, 10, 0, 110, 1, 62, 1, 10, 0, 108, 1, 110, 1, 10, 0, 71, 1, 108, 1, 108, 1, 196, 1, 203, 1, 70, 1, 79, 1, 108, 1, 234, 1, 195, 1, 79, 1, 109, 1, 79, 1, 195, 1, 109, 1, 204, 1, 251, 1, 250, 1, 254, 1, 109, 1, 254, 1, 111, 1, 109, 1, 84, 1, 111, 1, 4, 2, 39, 0, 27, 0, 26, 0, 39, 0, 48, 0, 27, 0, 38, 0, 54, 0, 19, 0, 190, 1, 244, 1, 104, 1, 255, 1, 2, 2, 112, 1, 33, 0, 53, 0, 43, 0, 53, 0, 105, 1, 246, 1, 33, 0, 56, 0, 35, 0, 211, 1, 216, 1, 86, 1, 207, 1, 212, 1, 87, 1, 184, 1, 229, 1, 98, 1, 23, 0, 44, 0, 51, 0, 83, 1, 3, 2, 200, 1, 77, 1, 179, 1, 208, 1, 73, 1, 217, 1, 175, 1, 74, 1, 88, 1, 218, 1, 95, 1, 230, 1, 225, 1, 96, 1, 99, 1, 231, 1, 80, 1, 238, 1, 235, 1, 81, 1, 101, 1, 239, 1, 102, 1, 245, 1, 240, 1, 176, 1, 106, 1, 75, 1, 177, 1, 191, 1, 107, 1, 141, 1, 192, 1, 201, 1, 142, 1, 133, 1, 193, 1, 143, 1, 124, 1, 123, 1, 105, 0, 104, 0, 96, 0, 252, 1, 0, 2, 137, 1, 210, 1, 213, 1, 150, 1, 232, 1, 194, 1, 135, 1, 219, 1, 185, 1, 153, 1, 182, 1, 226, 1, 163, 1, 119, 1, 138, 1, 164, 1, 181, 1, 78, 1, 93, 1, 165, 1, 197, 1, 183, 1, 166, 1, 139, 1, 198, 1, 140, 1, 1, 2, 199, 1, 146, 1, 202, 1, 249, 1, 129, 1, 151, 1, 215, 1, 155, 1, 222, 1, 220, 1, 156, 1, 159, 1, 223, 1, 160, 1, 227, 1, 224, 1, 161, 1, 111, 0, 228, 1, 168, 1, 242, 1, 237, 1, 169, 1, 173, 1, 243, 1), 0.0674687, PackedByteArray(223, 0, 246, 0, 66, 0, 66, 0, 227, 0, 235, 0, 66, 0, 234, 0, 223, 0, 50, 1, 229, 0, 234, 0, 223, 0, 75, 0, 246, 0, 223, 0, 229, 0, 75, 0, 51, 1, 55, 1, 229, 0, 229, 0, 65, 0, 75, 0, 75, 0, 65, 0, 237, 0, 237, 0, 9, 1, 252, 0, 65, 0, 104, 0, 110, 0, 105, 0, 104, 0, 96, 0, 17, 1, 20, 1, 246, 0, 246, 0, 20, 1, 242, 0, 242, 0, 20, 1, 23, 1, 66, 0, 246, 0, 242, 0, 242, 0, 60, 0, 66, 0, 60, 0, 42, 1, 66, 0, 242, 0, 1, 1, 42, 1, 1, 1, 47, 1, 42, 1, 118, 0, 148, 0, 60, 0, 3, 0, 0, 0, 205, 0, 0, 0, 213, 0, 205, 0, 3, 0, 53, 0, 0, 0, 245, 0, 3, 1, 213, 0, 205, 0, 204, 0, 3, 0, 10, 0, 204, 0, 205, 0, 33, 0, 155, 0, 10, 0, 10, 0, 56, 1, 12, 1, 10, 0, 15, 0, 204, 0, 210, 0, 204, 0, 15, 0, 18, 1, 3, 0, 210, 0, 18, 1, 22, 1, 3, 0, 39, 0, 3, 0, 27, 0, 39, 0, 15, 0, 143, 0, 5, 0, 131, 0, 143, 0, 10, 0, 13, 1, 15, 0, 13, 1, 203, 0, 15, 0, 15, 0, 203, 0, 32, 1, 57, 1, 61, 1, 13, 1, 57, 1, 16, 1, 53, 1, 57, 1, 8, 1, 16, 1, 33, 1, 13, 1, 253, 0, 253, 0, 37, 1, 33, 1, 28, 1, 0, 1, 33, 1, 249, 0, 33, 1, 0, 1, 2, 1, 48, 1, 214, 0, 21, 1, 24, 1, 200, 0, 40, 1, 7, 1, 206, 0, 58, 1, 207, 0, 129, 0, 211, 0, 248, 0, 19, 1, 201, 0, 25, 1, 243, 0, 208, 0, 44, 1, 41, 1, 209, 0, 215, 0, 45, 1, 216, 0, 49, 1, 46, 1, 244, 0, 217, 0, 202, 0, 232, 0, 4, 1, 14, 1, 233, 0, 224, 0, 5, 1, 54, 1, 59, 1, 228, 0, 218, 0, 43, 1, 225, 0, 225, 0, 43, 1, 39, 1, 38, 1, 6, 1, 226, 0, 26, 1, 254, 0, 219, 0, 251, 0, 35, 1, 238, 0, 255, 0, 247, 0, 220, 0, 250, 0, 212, 0, 34, 1, 239, 0, 230, 0, 10, 1, 231, 0, 60, 1, 11, 1, 236, 0, 15, 1, 52, 1, 221, 0, 29, 1, 27, 1, 222, 0, 240, 0, 30, 1, 241, 0, 36, 1, 31, 1), 0.195405, PackedByteArray(174, 0, 162, 0, 190, 0, 174, 0, 190, 0, 170, 0, 188, 0, 170, 0, 190, 0, 190, 0, 168, 0, 192, 0, 160, 0, 168, 0, 190, 0, 190, 0, 162, 0, 160, 0, 162, 0, 178, 0, 160, 0, 160, 0, 178, 0, 180, 0, 176, 0, 178, 0, 162, 0, 174, 0, 182, 0, 162, 0, 166, 0, 162, 0, 182, 0, 174, 0, 198, 0, 182, 0, 194, 0, 198, 0, 174, 0, 194, 0, 196, 0, 198, 0, 198, 0, 186, 0, 182, 0, 182, 0, 186, 0, 184, 0, 186, 0, 198, 0, 172, 0, 186, 0, 172, 0, 164, 0, 197, 0, 177, 0, 189, 0, 177, 0, 179, 0, 181, 0, 189, 0, 177, 0, 181, 0, 161, 0, 189, 0, 181, 0, 161, 0, 169, 0, 189, 0, 169, 0, 193, 0, 189, 0, 189, 0, 193, 0, 191, 0, 197, 0, 189, 0, 171, 0, 197, 0, 171, 0, 175, 0, 197, 0, 175, 0, 195, 0, 197, 0, 185, 0, 177, 0, 163, 0, 177, 0, 185, 0, 163, 0, 185, 0, 167, 0, 183, 0, 167, 0, 185, 0, 197, 0, 173, 0, 185, 0, 197, 0, 199, 0, 173, 0, 185, 0, 173, 0, 165, 0, 165, 0, 187, 0, 185, 0)],
"material": SubResource("StandardMaterial3D_3spdg"),
"name": "CardBase",
"primitive": 3,
"uv_scale": Vector4(2.00224, 2.00019, 0, 0),
"vertex_count": 517,
"vertex_data": PackedByteArray(246, 255, 185, 0, 146, 244, 255, 191, 246, 255, 69, 255, 146, 244, 255, 191, 246, 255, 174, 244, 69, 255, 255, 191, 246, 255, 185, 0, 108, 11, 255, 191, 246, 255, 174, 244, 185, 0, 255, 191, 246, 255, 69, 255, 108, 11, 255, 191, 246, 255, 232, 7, 251, 254, 255, 191, 246, 255, 44, 2, 37, 249, 255, 191, 246, 255, 148, 4, 156, 252, 255, 191, 246, 255, 209, 253, 9, 249, 255, 191, 246, 255, 28, 248, 56, 255, 255, 191, 246, 255, 102, 251, 229, 252, 255, 191, 246, 255, 44, 2, 217, 6, 255, 191, 246, 255, 232, 7, 3, 1, 255, 191, 246, 255, 148, 4, 98, 3, 255, 191, 246, 255, 28, 248, 198, 0, 255, 191, 246, 255, 209, 253, 245, 6, 255, 191, 246, 255, 102, 251, 25, 3, 255, 191, 246, 255, 0, 0, 79, 11, 34, 204, 246, 255, 0, 0, 175, 244, 108, 193, 246, 255, 79, 11, 255, 255, 167, 159, 246, 255, 175, 244, 255, 255, 237, 159, 246, 255, 255, 255, 175, 244, 78, 193, 246, 255, 255, 255, 79, 11, 72, 204, 246, 255, 175, 244, 0, 0, 16, 224, 246, 255, 79, 11, 0, 0, 94, 224, 246, 255, 166, 7, 79, 0, 44, 225, 246, 255, 14, 4, 221, 2, 179, 219, 246, 255, 131, 1, 134, 6, 249, 212, 246, 255, 240, 251, 151, 2, 128, 219, 246, 255, 88, 248, 13, 0, 162, 225, 246, 255, 123, 254, 166, 6, 80, 212, 246, 255, 14, 4, 33, 253, 68, 172, 246, 255, 166, 7, 175, 255, 48, 161, 246, 255, 131, 1, 120, 249, 154, 182, 246, 255, 88, 248, 241, 255, 97, 160, 246, 255, 240, 251, 103, 253, 254, 172, 246, 255, 123, 254, 88, 249, 39, 183, 234, 217, 0, 0, 79, 11, 123, 200, 234, 217, 79, 11, 0, 0, 195, 254, 234, 217, 255, 255, 79, 11, 174, 200, 234, 217, 175, 244, 0, 0, 199, 255, 234, 217, 0, 0, 175, 244, 124, 72, 234, 217, 79, 11, 255, 255, 196, 126, 234, 217, 255, 255, 175, 244, 175, 72, 234, 217, 175, 244, 255, 255, 200, 127, 234, 217, 166, 7, 79, 0, 42, 245, 234, 217, 14, 4, 221, 2, 23, 230, 234, 217, 131, 1, 134, 6, 44, 216, 234, 217, 88, 248, 13, 0, 64, 246, 234, 217, 240, 251, 151, 2, 27, 229, 234, 217, 123, 254, 166, 6, 81, 215, 234, 217, 166, 7, 175, 255, 43, 117, 234, 217, 14, 4, 33, 253, 24, 102, 234, 217, 131, 1, 120, 249, 44, 88, 234, 217, 88, 248, 241, 255, 64, 118, 234, 217, 240, 251, 103, 253, 27, 101, 234, 217, 123, 254, 88, 249, 82, 87, 246, 255, 87, 11, 69, 255, 255, 191, 246, 255, 87, 11, 185, 0, 255, 191, 0, 0, 185, 0, 146, 244, 255, 191, 0, 0, 69, 255, 146, 244, 255, 191, 0, 0, 174, 244, 69, 255, 255, 191, 0, 0, 185, 0, 108, 11, 255, 191, 0, 0, 174, 244, 185, 0, 255, 191, 0, 0, 69, 255, 108, 11, 255, 191, 0, 0, 232, 7, 251, 254, 255, 191, 0, 0, 44, 2, 37, 249, 255, 191, 0, 0, 148, 4, 156, 252, 255, 191, 0, 0, 209, 253, 9, 249, 255, 191, 0, 0, 28, 248, 56, 255, 255, 191, 0, 0, 102, 251, 229, 252, 255, 191, 0, 0, 44, 2, 217, 6, 255, 191, 0, 0, 232, 7, 3, 1, 255, 191, 0, 0, 148, 4, 98, 3, 255, 191, 0, 0, 28, 248, 198, 0, 255, 191, 0, 0, 209, 253, 245, 6, 255, 191, 0, 0, 102, 251, 25, 3, 255, 191, 0, 0, 0, 0, 79, 11, 108, 193, 0, 0, 0, 0, 175, 244, 34, 204, 0, 0, 79, 11, 255, 255, 94, 224, 0, 0, 175, 244, 255, 255, 16, 224, 0, 0, 255, 255, 175, 244, 72, 204, 0, 0, 255, 255, 79, 11, 78, 193, 0, 0, 175, 244, 0, 0, 237, 159, 0, 0, 79, 11, 0, 0, 167, 159, 0, 0, 166, 7, 79, 0, 48, 161, 0, 0, 14, 4, 221, 2, 68, 172, 0, 0, 131, 1, 134, 6, 154, 182, 0, 0, 240, 251, 151, 2, 255, 172, 0, 0, 88, 248, 13, 0, 97, 160, 0, 0, 123, 254, 166, 6, 39, 183, 0, 0, 14, 4, 33, 253, 179, 219, 0, 0, 166, 7, 175, 255, 44, 225, 0, 0, 131, 1, 120, 249, 249, 212, 0, 0, 88, 248, 241, 255, 162, 225, 0, 0, 240, 251, 103, 253, 128, 219, 0, 0, 123, 254, 88, 249, 80, 212, 11, 38, 0, 0, 79, 11, 124, 72, 11, 38, 79, 11, 0, 0, 196, 126, 11, 38, 255, 255, 79, 11, 175, 72, 11, 38, 175, 244, 0, 0, 200, 127, 11, 38, 0, 0, 175, 244, 123, 200, 11, 38, 79, 11, 255, 255, 195, 254, 11, 38, 255, 255, 175, 244, 174, 200, 11, 38, 175, 244, 255, 255, 199, 255, 11, 38, 166, 7, 79, 0, 43, 117, 11, 38, 14, 4, 221, 2, 24, 102, 11, 38, 131, 1, 134, 6, 44, 88, 11, 38, 88, 248, 13, 0, 64, 118, 11, 38, 240, 251, 151, 2, 27, 101, 11, 38, 123, 254, 166, 6, 82, 87, 11, 38, 166, 7, 175, 255, 42, 245, 11, 38, 14, 4, 33, 253, 23, 230, 11, 38, 131, 1, 120, 249, 44, 216, 11, 38, 88, 248, 241, 255, 64, 246, 11, 38, 240, 251, 103, 253, 27, 229, 11, 38, 123, 254, 88, 249, 81, 215, 251, 127, 0, 0, 79, 11, 255, 191, 251, 127, 0, 0, 79, 11, 255, 191, 251, 127, 79, 11, 0, 0, 255, 191, 251, 127, 79, 11, 0, 0, 255, 191, 251, 127, 255, 255, 79, 11, 255, 191, 251, 127, 255, 255, 79, 11, 255, 191, 251, 127, 175, 244, 0, 0, 255, 191, 251, 127, 175, 244, 0, 0, 255, 191, 251, 127, 0, 0, 175, 244, 255, 191, 251, 127, 0, 0, 175, 244, 255, 191, 251, 127, 79, 11, 255, 255, 254, 127, 251, 127, 79, 11, 255, 255, 254, 127, 251, 127, 255, 255, 175, 244, 255, 191, 251, 127, 255, 255, 175, 244, 255, 191, 251, 127, 175, 244, 255, 255, 255, 191, 251, 127, 175, 244, 255, 255, 255, 191, 251, 127, 166, 7, 79, 0, 255, 191, 251, 127, 166, 7, 79, 0, 255, 191, 251, 127, 14, 4, 221, 2, 27, 109, 251, 127, 14, 4, 221, 2, 27, 109, 251, 127, 131, 1, 134, 6, 255, 255, 251, 127, 131, 1, 134, 6, 255, 255, 251, 127, 88, 248, 13, 0, 255, 191, 251, 127, 88, 248, 13, 0, 255, 191, 251, 127, 240, 251, 151, 2, 255, 223, 251, 127, 240, 251, 151, 2, 255, 223, 251, 127, 123, 254, 166, 6, 255, 191, 251, 127, 123, 254, 166, 6, 255, 191, 251, 127, 166, 7, 175, 255, 255, 191, 251, 127, 166, 7, 175, 255, 255, 191, 251, 127, 14, 4, 33, 253, 255, 223, 251, 127, 14, 4, 33, 253, 255, 223, 251, 127, 131, 1, 120, 249, 254, 127, 251, 127, 131, 1, 120, 249, 254, 127, 251, 127, 88, 248, 241, 255, 255, 191, 251, 127, 88, 248, 241, 255, 255, 191, 251, 127, 240, 251, 103, 253, 27, 109, 251, 127, 240, 251, 103, 253, 27, 109, 251, 127, 123, 254, 88, 249, 227, 82, 251, 127, 123, 254, 88, 249, 227, 82, 0, 0, 87, 11, 69, 255, 255, 191, 0, 0, 87, 11, 185, 0, 255, 191, 251, 127, 0, 0, 79, 11, 252, 184, 251, 127, 0, 0, 79, 11, 58, 196, 251, 127, 79, 11, 0, 0, 87, 166, 251, 127, 79, 11, 0, 0, 235, 165, 251, 127, 255, 255, 79, 11, 207, 186, 251, 127, 255, 255, 79, 11, 59, 180, 251, 127, 175, 244, 0, 0, 148, 150, 251, 127, 175, 244, 0, 0, 167, 164, 251, 127, 0, 0, 175, 244, 198, 203, 251, 127, 0, 0, 175, 244, 176, 201, 251, 127, 79, 11, 255, 255, 136, 32, 251, 127, 79, 11, 255, 255, 193, 39, 251, 127, 255, 255, 175, 244, 123, 192, 251, 127, 255, 255, 175, 244, 206, 201, 251, 127, 175, 244, 255, 255, 213, 215, 251, 127, 175, 244, 255, 255, 171, 217, 251, 127, 166, 7, 79, 0, 194, 160, 251, 127, 166, 7, 79, 0, 24, 171, 251, 127, 14, 4, 221, 2, 13, 84, 251, 127, 14, 4, 221, 2, 3, 89, 251, 127, 131, 1, 134, 6, 53, 176, 251, 127, 131, 1, 134, 6, 205, 174, 251, 127, 88, 248, 13, 0, 2, 166, 251, 127, 88, 248, 13, 0, 236, 161, 251, 127, 240, 251, 151, 2, 33, 164, 251, 127, 240, 251, 151, 2, 213, 171, 251, 127, 123, 254, 166, 6, 8, 177, 251, 127, 123, 254, 166, 6, 208, 173, 251, 127, 166, 7, 175, 255, 253, 224, 251, 127, 166, 7, 175, 255, 215, 212, 251, 127, 14, 4, 33, 253, 84, 212, 251, 127, 14, 4, 33, 253, 88, 221, 251, 127, 131, 1, 120, 249, 116, 44, 251, 127, 131, 1, 120, 249, 68, 43, 251, 127, 88, 248, 241, 255, 217, 216, 251, 127, 88, 248, 241, 255, 105, 224, 251, 127, 240, 251, 103, 253, 68, 39, 251, 127, 240, 251, 103, 253, 2, 46, 251, 127, 123, 254, 88, 249, 103, 49, 251, 127, 123, 254, 88, 249, 187, 46, 246, 255, 185, 0, 108, 11, 7, 219, 246, 255, 185, 0, 108, 11, 65, 228, 246, 255, 185, 0, 108, 11, 145, 207, 246, 255, 69, 255, 108, 11, 202, 194, 246, 255, 14, 4, 221, 2, 233, 203, 246, 255, 166, 7, 175, 255, 74, 178, 246, 255, 166, 7, 175, 255, 119, 153, 246, 255, 166, 7, 175, 255, 67, 152, 246, 255, 166, 7, 175, 255, 107, 152, 246, 255, 166, 7, 175, 255, 158, 159, 234, 217, 79, 11, 0, 0, 213, 217, 234, 217, 79, 11, 0, 0, 102, 227, 234, 217, 79, 11, 0, 0, 135, 218, 234, 217, 14, 4, 33, 253, 62, 82, 234, 217, 14, 4, 33, 253, 95, 104, 234, 217, 14, 4, 33, 253, 38, 109, 234, 217, 14, 4, 33, 253, 33, 122, 234, 217, 14, 4, 33, 253, 16, 61, 0, 0, 232, 7, 251, 254, 238, 235, 0, 0, 28, 248, 198, 0, 82, 183, 0, 0, 28, 248, 198, 0, 168, 188, 0, 0, 28, 248, 198, 0, 4, 172, 0, 0, 28, 248, 198, 0, 133, 186, 0, 0, 240, 251, 103, 253, 160, 203, 11, 38, 79, 11, 255, 255, 32, 255, 11, 38, 79, 11, 255, 255, 226, 238, 11, 38, 79, 11, 255, 255, 209, 195, 11, 38, 79, 11, 255, 255, 161, 240, 11, 38, 255, 255, 175, 244, 109, 191, 11, 38, 255, 255, 175, 244, 215, 208, 11, 38, 255, 255, 175, 244, 123, 194, 11, 38, 255, 255, 175, 244, 88, 190, 11, 38, 175, 244, 255, 255, 133, 228, 11, 38, 175, 244, 255, 255, 140, 255, 11, 38, 175, 244, 255, 255, 33, 217, 11, 38, 175, 244, 255, 255, 82, 240, 11, 38, 175, 244, 255, 255, 229, 221, 11, 38, 240, 251, 151, 2, 97, 76, 11, 38, 240, 251, 151, 2, 132, 85, 11, 38, 240, 251, 151, 2, 54, 66, 11, 38, 240, 251, 151, 2, 250, 108, 11, 38, 240, 251, 151, 2, 74, 78, 251, 127, 0, 0, 79, 11, 207, 186, 251, 127, 0, 0, 79, 11, 107, 235, 251, 127, 0, 0, 79, 11, 53, 199, 251, 127, 0, 0, 79, 11, 55, 196, 251, 127, 79, 11, 0, 0, 192, 170, 251, 127, 79, 11, 0, 0, 217, 152, 251, 127, 79, 11, 0, 0, 26, 206, 251, 127, 79, 11, 0, 0, 28, 161, 251, 127, 79, 11, 0, 0, 212, 204, 251, 127, 255, 255, 79, 11, 30, 171, 251, 127, 255, 255, 79, 11, 213, 188, 251, 127, 255, 255, 79, 11, 202, 179, 251, 127, 175, 244, 0, 0, 21, 170, 251, 127, 175, 244, 0, 0, 126, 153, 251, 127, 175, 244, 0, 0, 107, 162, 251, 127, 0, 0, 175, 244, 204, 203, 251, 127, 0, 0, 175, 244, 156, 146, 251, 127, 0, 0, 175, 244, 55, 196, 251, 127, 79, 11, 255, 255, 140, 94, 251, 127, 79, 11, 255, 255, 150, 0, 251, 127, 79, 11, 255, 255, 150, 83, 251, 127, 79, 11, 255, 255, 172, 96, 251, 127, 79, 11, 255, 255, 177, 40, 251, 127, 255, 255, 175, 244, 63, 193, 251, 127, 255, 255, 175, 244, 181, 188, 251, 127, 255, 255, 175, 244, 196, 176, 251, 127, 255, 255, 175, 244, 128, 166, 251, 127, 255, 255, 175, 244, 120, 200, 251, 127, 175, 244, 255, 255, 132, 205, 251, 127, 175, 244, 255, 255, 242, 211, 251, 127, 175, 244, 255, 255, 146, 214, 251, 127, 166, 7, 79, 0, 122, 159, 251, 127, 166, 7, 79, 0, 12, 214, 251, 127, 166, 7, 79, 0, 198, 207, 251, 127, 14, 4, 221, 2, 32, 82, 251, 127, 14, 4, 221, 2, 163, 41, 251, 127, 14, 4, 221, 2, 245, 45, 251, 127, 131, 1, 134, 6, 150, 177, 251, 127, 131, 1, 134, 6, 234, 224, 251, 127, 131, 1, 134, 6, 24, 229, 251, 127, 88, 248, 13, 0, 122, 162, 251, 127, 88, 248, 13, 0, 122, 160, 251, 127, 88, 248, 13, 0, 123, 158, 251, 127, 240, 251, 151, 2, 91, 191, 251, 127, 240, 251, 151, 2, 22, 130, 251, 127, 240, 251, 151, 2, 10, 251, 251, 127, 240, 251, 151, 2, 144, 224, 251, 127, 240, 251, 151, 2, 223, 169, 251, 127, 240, 251, 151, 2, 68, 202, 251, 127, 123, 254, 166, 6, 134, 178, 251, 127, 123, 254, 166, 6, 27, 189, 251, 127, 123, 254, 166, 6, 70, 174, 251, 127, 166, 7, 175, 255, 65, 182, 251, 127, 166, 7, 175, 255, 168, 226, 251, 127, 166, 7, 175, 255, 234, 166, 251, 127, 166, 7, 175, 255, 223, 179, 251, 127, 14, 4, 33, 253, 67, 211, 251, 127, 14, 4, 33, 253, 166, 233, 251, 127, 14, 4, 33, 253, 50, 177, 251, 127, 14, 4, 33, 253, 143, 139, 251, 127, 14, 4, 33, 253, 185, 157, 251, 127, 131, 1, 120, 249, 11, 46, 251, 127, 131, 1, 120, 249, 18, 113, 251, 127, 131, 1, 120, 249, 138, 122, 251, 127, 88, 248, 241, 255, 82, 219, 251, 127, 88, 248, 241, 255, 5, 230, 251, 127, 88, 248, 241, 255, 64, 213, 251, 127, 88, 248, 241, 255, 247, 222, 251, 127, 240, 251, 103, 253, 237, 84, 251, 127, 240, 251, 103, 253, 105, 27, 251, 127, 240, 251, 103, 253, 135, 96, 251, 127, 240, 251, 103, 253, 25, 44, 251, 127, 240, 251, 103, 253, 235, 117, 251, 127, 123, 254, 88, 249, 222, 76, 251, 127, 123, 254, 88, 249, 160, 76, 251, 127, 123, 254, 88, 249, 158, 45, 246, 255, 69, 255, 146, 244, 249, 191, 246, 255, 185, 0, 108, 11, 178, 192, 246, 255, 44, 2, 217, 6, 109, 194, 246, 255, 28, 248, 198, 0, 229, 201, 246, 255, 0, 0, 175, 244, 36, 191, 246, 255, 255, 255, 79, 11, 17, 199, 246, 255, 166, 7, 79, 0, 103, 218, 246, 255, 14, 4, 221, 2, 254, 211, 246, 255, 166, 7, 175, 255, 100, 166, 246, 255, 88, 248, 241, 255, 187, 170, 234, 217, 0, 0, 79, 11, 195, 200, 234, 217, 0, 0, 79, 11, 236, 213, 234, 217, 0, 0, 79, 11, 245, 217, 234, 217, 0, 0, 79, 11, 90, 195, 234, 217, 79, 11, 0, 0, 48, 239, 234, 217, 79, 11, 0, 0, 167, 217, 234, 217, 79, 11, 0, 0, 238, 235, 234, 217, 79, 11, 255, 255, 14, 121, 234, 217, 79, 11, 255, 255, 34, 117, 234, 217, 79, 11, 255, 255, 86, 127, 234, 217, 255, 255, 175, 244, 24, 69, 234, 217, 255, 255, 175, 244, 36, 86, 234, 217, 255, 255, 175, 244, 0, 88, 234, 217, 131, 1, 134, 6, 45, 216, 234, 217, 131, 1, 134, 6, 172, 215, 234, 217, 131, 1, 134, 6, 118, 226, 234, 217, 131, 1, 134, 6, 90, 218, 234, 217, 88, 248, 13, 0, 200, 240, 234, 217, 88, 248, 13, 0, 124, 217, 234, 217, 88, 248, 13, 0, 44, 227, 234, 217, 88, 248, 13, 0, 229, 212, 234, 217, 88, 248, 13, 0, 144, 232, 234, 217, 240, 251, 151, 2, 77, 223, 234, 217, 240, 251, 151, 2, 36, 241, 234, 217, 240, 251, 151, 2, 178, 235, 234, 217, 123, 254, 166, 6, 200, 201, 234, 217, 123, 254, 166, 6, 143, 207, 234, 217, 123, 254, 166, 6, 51, 230, 234, 217, 14, 4, 33, 253, 172, 93, 234, 217, 14, 4, 33, 253, 228, 119, 234, 217, 14, 4, 33, 253, 238, 114, 234, 217, 131, 1, 120, 249, 144, 79, 234, 217, 131, 1, 120, 249, 13, 120, 234, 217, 131, 1, 120, 249, 155, 102, 234, 217, 131, 1, 120, 249, 226, 70, 234, 217, 131, 1, 120, 249, 113, 71, 234, 217, 240, 251, 103, 253, 98, 98, 234, 217, 240, 251, 103, 253, 89, 107, 234, 217, 123, 254, 88, 249, 107, 78, 234, 217, 123, 254, 88, 249, 158, 92, 234, 217, 123, 254, 88, 249, 137, 120, 0, 0, 44, 2, 217, 6, 97, 189, 0, 0, 232, 7, 3, 1, 94, 186, 0, 0, 28, 248, 198, 0, 219, 177, 0, 0, 0, 0, 175, 244, 236, 216, 0, 0, 0, 0, 175, 244, 188, 198, 0, 0, 255, 255, 79, 11, 7, 189, 0, 0, 255, 255, 79, 11, 36, 199, 0, 0, 166, 7, 175, 255, 53, 224, 0, 0, 166, 7, 175, 255, 161, 236, 0, 0, 88, 248, 241, 255, 119, 219, 0, 0, 88, 248, 241, 255, 183, 234, 0, 0, 240, 251, 103, 253, 149, 232, 0, 0, 240, 251, 103, 253, 115, 208, 11, 38, 0, 0, 79, 11, 233, 69, 11, 38, 0, 0, 79, 11, 58, 73, 11, 38, 0, 0, 79, 11, 55, 43, 11, 38, 0, 0, 79, 11, 232, 57, 11, 38, 79, 11, 0, 0, 6, 115, 11, 38, 79, 11, 0, 0, 3, 95, 11, 38, 79, 11, 0, 0, 246, 84, 11, 38, 79, 11, 255, 255, 32, 255, 11, 38, 79, 11, 255, 255, 26, 243, 11, 38, 79, 11, 255, 255, 201, 223, 11, 38, 255, 255, 175, 244, 169, 201, 11, 38, 255, 255, 175, 244, 137, 195, 11, 38, 255, 255, 175, 244, 128, 192, 11, 38, 255, 255, 175, 244, 155, 193, 11, 38, 255, 255, 175, 244, 56, 192, 11, 38, 175, 244, 255, 255, 77, 207, 11, 38, 175, 244, 255, 255, 152, 255, 11, 38, 175, 244, 255, 255, 40, 238, 11, 38, 175, 244, 255, 255, 95, 225, 11, 38, 175, 244, 255, 255, 143, 246, 11, 38, 175, 244, 255, 255, 160, 234, 11, 38, 14, 4, 221, 2, 79, 101, 11, 38, 14, 4, 221, 2, 57, 84, 11, 38, 131, 1, 134, 6, 96, 91, 11, 38, 131, 1, 134, 6, 18, 88, 11, 38, 131, 1, 134, 6, 14, 70, 11, 38, 88, 248, 13, 0, 248, 110, 11, 38, 88, 248, 13, 0, 1, 94, 11, 38, 88, 248, 13, 0, 126, 114, 11, 38, 88, 248, 13, 0, 87, 98, 11, 38, 88, 248, 13, 0, 104, 115, 11, 38, 240, 251, 151, 2, 190, 97, 11, 38, 240, 251, 151, 2, 235, 84, 11, 38, 240, 251, 151, 2, 133, 112, 11, 38, 240, 251, 151, 2, 160, 81, 11, 38, 240, 251, 151, 2, 93, 86, 11, 38, 123, 254, 166, 6, 190, 83, 11, 38, 123, 254, 166, 6, 104, 93, 11, 38, 123, 254, 166, 6, 20, 74, 11, 38, 123, 254, 166, 6, 207, 72, 11, 38, 123, 254, 166, 6, 122, 69, 11, 38, 14, 4, 33, 253, 108, 220, 11, 38, 14, 4, 33, 253, 208, 215, 11, 38, 14, 4, 33, 253, 89, 215, 11, 38, 131, 1, 120, 249, 247, 206, 11, 38, 131, 1, 120, 249, 74, 181, 11, 38, 131, 1, 120, 249, 105, 194, 11, 38, 131, 1, 120, 249, 126, 208, 251, 127, 0, 0, 79, 11, 239, 195, 251, 127, 0, 0, 79, 11, 143, 254, 251, 127, 0, 0, 79, 11, 160, 191, 251, 127, 0, 0, 79, 11, 166, 171, 251, 127, 79, 11, 0, 0, 14, 178, 251, 127, 79, 11, 0, 0, 106, 206, 251, 127, 79, 11, 0, 0, 16, 200, 251, 127, 79, 11, 0, 0, 207, 225, 251, 127, 255, 255, 79, 11, 146, 167, 251, 127, 255, 255, 79, 11, 184, 162, 251, 127, 255, 255, 79, 11, 44, 202, 251, 127, 175, 244, 0, 0, 219, 175, 251, 127, 175, 244, 0, 0, 174, 162, 251, 127, 175, 244, 0, 0, 151, 181, 251, 127, 175, 244, 0, 0, 29, 204, 251, 127, 0, 0, 175, 244, 171, 189, 251, 127, 0, 0, 175, 244, 85, 138, 251, 127, 0, 0, 175, 244, 39, 172, 251, 127, 79, 11, 255, 255, 21, 85, 251, 127, 79, 11, 255, 255, 248, 1, 251, 127, 79, 11, 255, 255, 196, 86, 251, 127, 79, 11, 255, 255, 108, 122, 251, 127, 79, 11, 255, 255, 125, 104, 251, 127, 255, 255, 175, 244, 72, 179, 251, 127, 255, 255, 175, 244, 132, 189, 251, 127, 255, 255, 175, 244, 68, 191, 251, 127, 255, 255, 175, 244, 45, 172, 251, 127, 175, 244, 255, 255, 0, 198, 251, 127, 175, 244, 255, 255, 188, 217, 251, 127, 175, 244, 255, 255, 55, 147, 251, 127, 175, 244, 255, 255, 27, 148, 251, 127, 166, 7, 79, 0, 28, 172, 251, 127, 166, 7, 79, 0, 114, 234, 251, 127, 166, 7, 79, 0, 139, 214, 251, 127, 166, 7, 79, 0, 160, 205, 251, 127, 14, 4, 221, 2, 68, 99, 251, 127, 14, 4, 221, 2, 42, 93, 251, 127, 14, 4, 221, 2, 143, 76, 251, 127, 14, 4, 221, 2, 247, 112, 251, 127, 131, 1, 134, 6, 176, 187, 251, 127, 131, 1, 134, 6, 4, 225, 251, 127, 131, 1, 134, 6, 126, 211, 251, 127, 131, 1, 134, 6, 212, 249, 251, 127, 131, 1, 134, 6, 176, 250, 251, 127, 131, 1, 134, 6, 165, 229, 251, 127, 88, 248, 13, 0, 224, 174, 251, 127, 88, 248, 13, 0, 242, 185, 251, 127, 88, 248, 13, 0, 24, 211, 251, 127, 240, 251, 151, 2, 189, 192, 251, 127, 240, 251, 151, 2, 115, 185, 251, 127, 240, 251, 151, 2, 150, 200, 251, 127, 240, 251, 151, 2, 107, 242, 251, 127, 123, 254, 166, 6, 209, 171, 251, 127, 123, 254, 166, 6, 151, 186, 251, 127, 123, 254, 166, 6, 243, 175, 251, 127, 123, 254, 166, 6, 89, 207, 251, 127, 123, 254, 166, 6, 65, 247, 251, 127, 123, 254, 166, 6, 117, 253, 251, 127, 166, 7, 175, 255, 246, 202, 251, 127, 166, 7, 175, 255, 5, 226, 251, 127, 166, 7, 175, 255, 45, 144, 251, 127, 166, 7, 175, 255, 144, 158, 251, 127, 14, 4, 33, 253, 226, 233, 251, 127, 14, 4, 33, 253, 246, 218, 251, 127, 14, 4, 33, 253, 161, 147, 251, 127, 14, 4, 33, 253, 96, 167, 251, 127, 14, 4, 33, 253, 253, 138, 251, 127, 131, 1, 120, 249, 50, 93, 251, 127, 131, 1, 120, 249, 231, 59, 251, 127, 131, 1, 120, 249, 100, 70, 251, 127, 131, 1, 120, 249, 86, 123, 251, 127, 131, 1, 120, 249, 10, 126, 251, 127, 131, 1, 120, 249, 68, 112, 251, 127, 88, 248, 241, 255, 58, 228, 251, 127, 88, 248, 241, 255, 193, 222, 251, 127, 88, 248, 241, 255, 205, 212, 251, 127, 88, 248, 241, 255, 63, 171, 251, 127, 88, 248, 241, 255, 164, 155, 251, 127, 240, 251, 103, 253, 207, 90, 251, 127, 240, 251, 103, 253, 228, 35, 251, 127, 240, 251, 103, 253, 179, 97, 251, 127, 240, 251, 103, 253, 118, 123, 251, 127, 123, 254, 88, 249, 87, 76, 251, 127, 123, 254, 88, 249, 85, 70, 251, 127, 123, 254, 88, 249, 66, 117, 251, 127, 123, 254, 88, 249, 235, 127, 251, 127, 123, 254, 88, 249, 40, 104, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 254, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 254, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 221, 96, 197, 57, 17, 99, 190, 62, 31, 126, 115, 6, 43, 129, 86, 0, 215, 190, 234, 28, 191, 185, 45, 31, 123, 128, 88, 0, 17, 126, 184, 2, 151, 112, 154, 21, 165, 101, 156, 39, 147, 97, 249, 49, 200, 168, 8, 27, 251, 147, 83, 14, 35, 178, 75, 30, 207, 107, 75, 60, 174, 115, 239, 42, 0, 103, 227, 63, 214, 168, 144, 11, 78, 189, 172, 20, 114, 191, 29, 25, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 254, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 254, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 189, 190, 237, 28, 197, 185, 34, 31, 183, 130, 237, 1, 167, 127, 124, 0, 209, 96, 191, 57, 20, 99, 216, 62, 169, 127, 44, 1, 114, 134, 224, 1, 239, 170, 80, 12, 74, 188, 47, 20, 226, 191, 255, 24, 83, 107, 79, 61, 111, 116, 215, 40, 226, 102, 114, 63, 156, 167, 89, 26, 153, 149, 103, 15, 249, 177, 108, 30, 172, 113, 251, 19, 247, 100, 200, 40, 180, 97, 36, 50, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 127, 0, 0, 255, 127, 0, 0, 62, 195, 62, 67, 100, 191, 100, 63, 237, 141, 237, 13, 41, 132, 41, 4, 143, 55, 112, 72, 35, 57, 219, 70, 197, 129, 197, 1, 153, 116, 101, 11, 243, 198, 243, 70, 146, 190, 147, 62, 252, 141, 253, 13, 180, 129, 180, 1, 7, 57, 247, 70, 246, 60, 8, 67, 135, 119, 120, 8, 184, 103, 71, 24, 26, 177, 26, 49, 120, 170, 121, 42, 7, 187, 8, 59, 57, 196, 57, 68, 17, 204, 18, 76, 122, 184, 122, 56, 161, 108, 93, 19, 215, 97, 40, 30, 29, 63, 225, 64, 205, 78, 49, 49, 127, 69, 127, 58, 42, 49, 212, 78, 48, 164, 49, 36, 173, 168, 173, 40, 231, 178, 232, 50, 83, 192, 83, 64, 109, 205, 109, 77, 255, 188, 255, 60, 77, 80, 178, 47, 47, 81, 207, 46, 138, 61, 117, 66, 147, 73, 108, 54, 73, 70, 181, 57, 3, 50, 252, 77, 114, 93, 23, 75, 88, 92, 191, 80, 92, 92, 197, 71, 35, 142, 10, 7, 54, 92, 50, 19, 35, 144, 129, 28, 129, 171, 50, 63, 175, 131, 36, 52, 234, 135, 120, 48, 87, 89, 211, 81, 11, 113, 244, 14, 255, 255, 255, 127, 255, 255, 255, 127, 250, 80, 5, 47, 0, 0, 255, 127, 158, 1, 96, 126, 0, 0, 255, 127, 233, 45, 21, 82, 237, 199, 212, 19, 26, 192, 163, 13, 128, 197, 107, 15, 156, 125, 176, 9, 113, 108, 112, 68, 206, 91, 82, 19, 255, 255, 255, 127, 60, 253, 61, 125, 255, 255, 255, 127, 74, 136, 74, 8, 0, 0, 255, 127, 199, 73, 55, 54, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 161, 113, 93, 14, 22, 133, 22, 5, 255, 255, 255, 127, 154, 65, 100, 62, 0, 0, 255, 127, 0, 0, 255, 127, 230, 10, 24, 117, 0, 0, 255, 127, 165, 195, 165, 67, 44, 1, 211, 126, 197, 13, 57, 114, 79, 191, 80, 63, 151, 161, 151, 33, 154, 254, 155, 126, 255, 255, 255, 127, 116, 132, 117, 4, 255, 255, 255, 127, 0, 0, 255, 127, 247, 52, 7, 75, 16, 57, 239, 70, 89, 255, 90, 127, 42, 253, 43, 125, 140, 116, 114, 11, 226, 198, 226, 70, 0, 0, 255, 127, 80, 191, 80, 63, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 4, 255, 5, 127, 181, 128, 182, 0, 171, 50, 84, 77, 0, 0, 255, 127, 0, 0, 255, 127, 189, 216, 189, 88, 160, 61, 95, 66, 255, 255, 255, 127, 255, 255, 255, 127, 255, 96, 0, 31, 250, 171, 250, 43, 145, 88, 110, 39, 255, 255, 255, 127, 122, 189, 123, 61, 155, 8, 99, 119, 162, 72, 92, 55, 22, 205, 22, 77, 251, 2, 3, 125, 192, 1, 63, 126, 197, 253, 198, 125, 152, 246, 152, 118, 25, 90, 229, 37, 124, 251, 125, 123, 98, 108, 157, 19, 255, 255, 255, 127, 55, 214, 56, 86, 253, 74, 2, 53, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 38, 48, 216, 79, 255, 255, 255, 127, 216, 255, 217, 127, 177, 255, 177, 127, 149, 255, 150, 127, 184, 190, 185, 62, 46, 255, 47, 127, 152, 255, 152, 127, 194, 3, 60, 124, 255, 255, 255, 127, 85, 203, 85, 75, 0, 0, 255, 127, 255, 255, 255, 127, 202, 89, 53, 38, 157, 73, 97, 54, 255, 255, 255, 127, 176, 79, 78, 48, 0, 0, 255, 127, 149, 48, 105, 79, 18, 230, 19, 102, 187, 65, 68, 62, 249, 254, 250, 126, 0, 0, 255, 127, 0, 0, 255, 127, 151, 48, 103, 79, 151, 153, 203, 12, 66, 116, 122, 23, 164, 117, 194, 20, 205, 136, 29, 4, 121, 106, 229, 49, 149, 164, 96, 22, 184, 110, 237, 14, 246, 101, 118, 28, 77, 133, 10, 28, 63, 103, 23, 5, 185, 34, 69, 93, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 135, 96, 119, 31, 255, 255, 255, 127, 255, 255, 255, 127, 88, 178, 89, 50, 255, 255, 255, 127, 0, 0, 255, 127, 32, 235, 32, 107, 255, 255, 255, 127, 255, 255, 255, 127, 206, 41, 48, 86, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 20, 213, 21, 85, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 138, 205, 138, 77, 255, 255, 255, 127, 255, 255, 255, 127, 226, 239, 226, 111, 255, 255, 255, 127, 255, 255, 255, 127, 242, 72, 13, 55, 0, 0, 255, 127, 0, 0, 255, 127, 79, 58, 176, 69, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 154, 14, 101, 113, 167, 172, 167, 44, 222, 249, 222, 121, 190, 203, 190, 75, 255, 255, 255, 127, 255, 255, 255, 127, 146, 186, 191, 27, 184, 195, 185, 15, 201, 112, 82, 39, 45, 210, 111, 41, 42, 171, 44, 25, 212, 115, 34, 34, 251, 87, 237, 83, 255, 140, 224, 14, 234, 167, 199, 10, 112, 111, 87, 13, 177, 122, 239, 16, 22, 127, 142, 31, 38, 99, 72, 24, 90, 239, 90, 111, 139, 218, 139, 90, 255, 255, 255, 127, 255, 255, 255, 127, 233, 227, 233, 99, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 125, 240, 125, 112, 255, 255, 255, 127, 223, 53, 31, 74, 0, 0, 255, 127, 225, 2, 30, 125, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 227, 250, 228, 122, 25, 108, 230, 19, 12, 134, 12, 6, 255, 255, 255, 127, 58, 246, 59, 118, 18, 184, 19, 56, 140, 235, 140, 107, 255, 255, 255, 127, 255, 255, 255, 127, 63, 91, 192, 36, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 106, 47, 148, 80, 141, 76, 113, 51, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 91, 42, 164, 85, 0, 0, 255, 127, 5, 255, 6, 127, 111, 26, 144, 101, 0, 0, 255, 127, 69, 181, 70, 53, 255, 255, 255, 127, 255, 255, 255, 127, 248, 194, 248, 66, 255, 255, 255, 127, 44, 244, 45, 116, 255, 255, 255, 127, 27, 253, 28, 125, 0, 0, 255, 127, 255, 255, 255, 127, 219, 233, 219, 105, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 128, 18, 126, 109, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 179, 253, 179, 125, 255, 255, 255, 127, 109, 244, 109, 116, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 195, 133, 195, 5, 211, 11, 44, 116, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 50, 165, 51, 37, 145, 226, 145, 98, 158, 255, 159, 127, 82, 46, 172, 81, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 105, 255, 105, 127, 255, 255, 255, 127, 255, 255, 255, 127, 236, 252, 237, 124, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 55, 80, 200, 47, 83, 78, 172, 49, 255, 255, 255, 127, 255, 255, 255, 127, 180, 215, 180, 87, 0, 0, 255, 127, 21, 58, 233, 69, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_4aff2")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1v286"]
resource_local_to_scene = true
render_priority = 2
transparency = 1
cull_mode = 2
depth_draw_mode = 1
no_depth_test = true
shading_mode = 0
albedo_color = Color(1, 1, 1, 0)
texture_filter = 1

[sub_resource type="ViewportTexture" id="ViewportTexture_oshrb"]
resource_name = "_albedo"
viewport_path = NodePath("CardPicGradient/SubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_d4a2i"]
resource_local_to_scene = true
transparency = 1
cull_mode = 2
depth_draw_mode = 1
shading_mode = 0
albedo_color = Color(0, 0, 0, 1)
albedo_texture = SubResource("ViewportTexture_oshrb")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_opdvl"]
transparency = 1
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("19_8h4f7")
texture_filter = 1
texture_repeat = false

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cglip"]
resource_name = "CardCompanion"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("19_8h4f7")

[sub_resource type="ArrayMesh" id="ArrayMesh_8kah1"]
_surfaces = [{
"aabb": AABB(0.0393262, -2.5, -1.3296, 1.00136e-05, 0.39001, 0.36801),
"format": 34896613377,
"index_count": 24,
"index_data": PackedByteArray(1, 0, 8, 0, 6, 0, 1, 0, 5, 0, 8, 0, 5, 0, 4, 0, 8, 0, 5, 0, 0, 0, 4, 0, 4, 0, 7, 0, 8, 0, 4, 0, 2, 0, 7, 0, 8, 0, 3, 0, 6, 0, 8, 0, 7, 0, 3, 0),
"name": "CardCompanion",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 9,
"vertex_data": PackedByteArray(97, 0, 253, 255, 253, 255, 0, 0, 0, 0, 0, 0, 253, 255, 0, 0, 97, 0, 253, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 0, 253, 255, 254, 127, 0, 0, 48, 0, 254, 127, 253, 255, 0, 0, 0, 0, 0, 0, 254, 127, 0, 0, 48, 0, 254, 127, 0, 0, 0, 0, 48, 0, 254, 127, 254, 127, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_s5rux"]
resource_name = "DRC_Card_Modell_Test01_Plane_006"
_surfaces = [{
"aabb": AABB(0.0393262, -2.5, -1.3296, 1.00136e-05, 0.39001, 0.36801),
"attribute_data": PackedByteArray(43, 105, 118, 103, 43, 105, 255, 255, 255, 255, 118, 103, 255, 255, 255, 255, 149, 180, 118, 103, 43, 105, 186, 179, 149, 180, 255, 255, 255, 255, 186, 179, 149, 180, 186, 179),
"format": 34896613399,
"index_count": 24,
"index_data": PackedByteArray(1, 0, 8, 0, 6, 0, 1, 0, 5, 0, 8, 0, 5, 0, 4, 0, 8, 0, 5, 0, 0, 0, 4, 0, 4, 0, 7, 0, 8, 0, 4, 0, 2, 0, 7, 0, 8, 0, 3, 0, 6, 0, 8, 0, 7, 0, 3, 0),
"material": SubResource("StandardMaterial3D_cglip"),
"name": "CardCompanion",
"primitive": 3,
"uv_scale": Vector4(2.39877, 2.51373, 0, 0),
"vertex_count": 9,
"vertex_data": PackedByteArray(97, 0, 253, 255, 253, 255, 255, 191, 0, 0, 0, 0, 253, 255, 255, 191, 97, 0, 253, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 97, 0, 253, 255, 254, 127, 255, 191, 48, 0, 254, 127, 253, 255, 255, 191, 0, 0, 0, 0, 254, 127, 255, 191, 48, 0, 254, 127, 0, 0, 255, 191, 48, 0, 254, 127, 254, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_8kah1")

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_xld4q"]
expanded_output_ports = [0]
input_name = "color"

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_1ytms"]
expanded_output_ports = [0]
texture = ExtResource("4_muwd4")

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_2p0gx"]
operator = 2

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_yi8r4"]
operator = 2

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_ln56u"]
expanded_output_ports = [0]
texture = ExtResource("4_muwd4")

[sub_resource type="VisualShader" id="VisualShader_lph4a"]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_back, diffuse_lambert, specular_schlick_ggx, unshaded;

uniform sampler2D tex_frg_6;
uniform sampler2D tex_frg_3;



void fragment() {
// Input:2
	vec4 n_out2p0 = COLOR;
	float n_out2p4 = n_out2p0.a;


// Texture2D:6
	vec4 n_out6p0 = texture(tex_frg_6, UV);


// Texture2D:3
	vec4 n_out3p0 = texture(tex_frg_3, UV);


// FloatOp:4
	float n_out4p0 = n_out3p0.x * n_out2p4;


// FloatOp:5
	float n_out5p0 = n_out6p0.x * n_out4p0;


// Output:0
	ALBEDO = vec3(n_out2p0.xyz);
	ALPHA = n_out5p0;


}
"
flags/unshaded = true
nodes/fragment/0/position = Vector2(780, 160)
nodes/fragment/2/node = SubResource("VisualShaderNodeInput_xld4q")
nodes/fragment/2/position = Vector2(-100, 100)
nodes/fragment/3/node = SubResource("VisualShaderNodeTexture_1ytms")
nodes/fragment/3/position = Vector2(-120, 340)
nodes/fragment/4/node = SubResource("VisualShaderNodeFloatOp_2p0gx")
nodes/fragment/4/position = Vector2(200, 380)
nodes/fragment/5/node = SubResource("VisualShaderNodeFloatOp_yi8r4")
nodes/fragment/5/position = Vector2(540, 500)
nodes/fragment/6/node = SubResource("VisualShaderNodeTexture_ln56u")
nodes/fragment/6/position = Vector2(240, 560)
nodes/fragment/connections = PackedInt32Array(2, 0, 0, 0, 2, 4, 4, 1, 3, 0, 4, 0, 6, 0, 5, 0, 4, 0, 5, 1, 5, 0, 0, 1)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_cfp2b"]
render_priority = 0
shader = SubResource("VisualShader_lph4a")

[sub_resource type="QuadMesh" id="QuadMesh_wkgxl"]
size = Vector2(3.12, 3.145)

[sub_resource type="Gradient" id="Gradient_tgfil"]
offsets = PackedFloat32Array(0.504854)
colors = PackedColorArray(1, 1, 1, 1)

[sub_resource type="ConvexPolygonShape3D" id="ConvexPolygonShape3D_0tr8e"]
points = PackedVector3Array(-0.961016, -0.992092, -0.961789, 0.964394, 0.99578, 0.932916, 1, -0.624902, 0.937004, -0.995748, -0.964558, 0.932885, -0.961016, 0.992092, -0.961789, 0.992092, -0.961016, -0.961789, 0.960441, 0.960635, -0.992454, -0.995748, 0.964558, 0.932885, 0.964394, -0.99578, 0.932916, -0.992092, -0.961016, -0.961789, -0.960603, -0.960603, -0.992422, 0.992092, 0.961016, -0.961789, 0.960853, -0.992124, -0.96182, 0.960853, 0.992124, -0.96182, -0.992092, 0.961016, -0.961789, 0.960441, -0.960635, -0.992454, -0.960603, 0.960603, -0.992422, -0.964558, -0.995748, 0.932885, -0.964558, 0.995748, 0.932885, 0.995748, 0.964558, 0.932696, 0.995748, -0.964558, 0.932885)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_pq7ya"]
resource_local_to_scene = true
transparency = 4
shading_mode = 0
albedo_color = Color(0.619608, 0.933333, 0.85098, 0.113725)
grow = true
grow_amount = 0.035

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_hl3uc"]
resource_name = "Material"
cull_mode = 2
albedo_color = Color(0.906332, 0.906332, 0.906332, 1)
roughness = 0.4

[sub_resource type="ArrayMesh" id="ArrayMesh_0dym6"]
_surfaces = [{
"aabb": AABB(-1, -1, -1, 2, 2, 2),
"format": 34896613377,
"index_count": 900,
"index_data": PackedByteArray(97, 0, 59, 0, 19, 0, 97, 0, 133, 0, 59, 0, 135, 0, 77, 0, 114, 0, 135, 0, 95, 0, 77, 0, 58, 0, 115, 0, 40, 0, 58, 0, 134, 0, 115, 0, 1, 0, 116, 0, 78, 0, 1, 0, 39, 0, 116, 0, 21, 0, 38, 0, 2, 0, 21, 0, 57, 0, 38, 0, 0, 0, 6, 0, 3, 0, 0, 0, 5, 0, 6, 0, 3, 0, 7, 0, 4, 0, 3, 0, 6, 0, 7, 0, 5, 0, 18, 0, 6, 0, 5, 0, 15, 0, 18, 0, 6, 0, 8, 0, 7, 0, 6, 0, 18, 0, 8, 0, 1, 0, 12, 0, 9, 0, 1, 0, 11, 0, 12, 0, 9, 0, 13, 0, 10, 0, 9, 0, 12, 0, 13, 0, 11, 0, 7, 0, 12, 0, 11, 0, 4, 0, 7, 0, 12, 0, 8, 0, 13, 0, 12, 0, 7, 0, 8, 0, 2, 0, 17, 0, 14, 0, 2, 0, 16, 0, 17, 0, 14, 0, 18, 0, 15, 0, 14, 0, 17, 0, 18, 0, 16, 0, 13, 0, 17, 0, 16, 0, 10, 0, 13, 0, 17, 0, 8, 0, 18, 0, 17, 0, 13, 0, 8, 0, 19, 0, 25, 0, 22, 0, 19, 0, 24, 0, 25, 0, 22, 0, 26, 0, 23, 0, 22, 0, 25, 0, 26, 0, 24, 0, 37, 0, 25, 0, 24, 0, 34, 0, 37, 0, 25, 0, 27, 0, 26, 0, 25, 0, 37, 0, 27, 0, 20, 0, 31, 0, 28, 0, 20, 0, 30, 0, 31, 0, 28, 0, 32, 0, 29, 0, 28, 0, 31, 0, 32, 0, 30, 0, 26, 0, 31, 0, 30, 0, 23, 0, 26, 0, 31, 0, 27, 0, 32, 0, 31, 0, 26, 0, 27, 0, 21, 0, 36, 0, 33, 0, 21, 0, 35, 0, 36, 0, 33, 0, 37, 0, 34, 0, 33, 0, 36, 0, 37, 0, 35, 0, 32, 0, 36, 0, 35, 0, 29, 0, 32, 0, 36, 0, 27, 0, 37, 0, 36, 0, 32, 0, 27, 0, 38, 0, 44, 0, 41, 0, 38, 0, 43, 0, 44, 0, 41, 0, 45, 0, 42, 0, 41, 0, 44, 0, 45, 0, 43, 0, 56, 0, 44, 0, 43, 0, 53, 0, 56, 0, 44, 0, 46, 0, 45, 0, 44, 0, 56, 0, 46, 0, 39, 0, 50, 0, 47, 0, 39, 0, 49, 0, 50, 0, 47, 0, 51, 0, 48, 0, 47, 0, 50, 0, 51, 0, 49, 0, 45, 0, 50, 0, 49, 0, 42, 0, 45, 0, 50, 0, 46, 0, 51, 0, 50, 0, 45, 0, 46, 0, 40, 0, 55, 0, 52, 0, 40, 0, 54, 0, 55, 0, 52, 0, 56, 0, 53, 0, 52, 0, 55, 0, 56, 0, 54, 0, 51, 0, 55, 0, 54, 0, 48, 0, 51, 0, 55, 0, 46, 0, 56, 0, 55, 0, 51, 0, 46, 0, 57, 0, 63, 0, 60, 0, 57, 0, 62, 0, 63, 0, 60, 0, 64, 0, 61, 0, 60, 0, 63, 0, 64, 0, 62, 0, 75, 0, 63, 0, 62, 0, 72, 0, 75, 0, 63, 0, 65, 0, 64, 0, 63, 0, 75, 0, 65, 0, 58, 0, 69, 0, 66, 0, 58, 0, 68, 0, 69, 0, 66, 0, 70, 0, 67, 0, 66, 0, 69, 0, 70, 0, 68, 0, 64, 0, 69, 0, 68, 0, 61, 0, 64, 0, 69, 0, 65, 0, 70, 0, 69, 0, 64, 0, 65, 0, 59, 0, 74, 0, 71, 0, 59, 0, 73, 0, 74, 0, 71, 0, 75, 0, 72, 0, 71, 0, 74, 0, 75, 0, 73, 0, 70, 0, 74, 0, 73, 0, 67, 0, 70, 0, 74, 0, 65, 0, 75, 0, 74, 0, 70, 0, 65, 0, 76, 0, 82, 0, 79, 0, 76, 0, 81, 0, 82, 0, 79, 0, 83, 0, 80, 0, 79, 0, 82, 0, 83, 0, 81, 0, 94, 0, 82, 0, 81, 0, 91, 0, 94, 0, 82, 0, 84, 0, 83, 0, 82, 0, 94, 0, 84, 0, 77, 0, 88, 0, 85, 0, 77, 0, 87, 0, 88, 0, 85, 0, 89, 0, 86, 0, 85, 0, 88, 0, 89, 0, 87, 0, 83, 0, 88, 0, 87, 0, 80, 0, 83, 0, 88, 0, 84, 0, 89, 0, 88, 0, 83, 0, 84, 0, 78, 0, 93, 0, 90, 0, 78, 0, 92, 0, 93, 0, 90, 0, 94, 0, 91, 0, 90, 0, 93, 0, 94, 0, 92, 0, 89, 0, 93, 0, 92, 0, 86, 0, 89, 0, 93, 0, 84, 0, 94, 0, 93, 0, 89, 0, 84, 0, 95, 0, 101, 0, 98, 0, 95, 0, 100, 0, 101, 0, 98, 0, 102, 0, 99, 0, 98, 0, 101, 0, 102, 0, 100, 0, 113, 0, 101, 0, 100, 0, 110, 0, 113, 0, 101, 0, 103, 0, 102, 0, 101, 0, 113, 0, 103, 0, 96, 0, 107, 0, 104, 0, 96, 0, 106, 0, 107, 0, 104, 0, 108, 0, 105, 0, 104, 0, 107, 0, 108, 0, 106, 0, 102, 0, 107, 0, 106, 0, 99, 0, 102, 0, 107, 0, 103, 0, 108, 0, 107, 0, 102, 0, 103, 0, 97, 0, 112, 0, 109, 0, 97, 0, 111, 0, 112, 0, 109, 0, 113, 0, 110, 0, 109, 0, 112, 0, 113, 0, 111, 0, 108, 0, 112, 0, 111, 0, 105, 0, 108, 0, 112, 0, 103, 0, 113, 0, 112, 0, 108, 0, 103, 0, 114, 0, 120, 0, 117, 0, 114, 0, 119, 0, 120, 0, 117, 0, 121, 0, 118, 0, 117, 0, 120, 0, 121, 0, 119, 0, 132, 0, 120, 0, 119, 0, 129, 0, 132, 0, 120, 0, 122, 0, 121, 0, 120, 0, 132, 0, 122, 0, 115, 0, 126, 0, 123, 0, 115, 0, 125, 0, 126, 0, 123, 0, 127, 0, 124, 0, 123, 0, 126, 0, 127, 0, 125, 0, 121, 0, 126, 0, 125, 0, 118, 0, 121, 0, 126, 0, 122, 0, 127, 0, 126, 0, 121, 0, 122, 0, 116, 0, 131, 0, 128, 0, 116, 0, 130, 0, 131, 0, 128, 0, 132, 0, 129, 0, 128, 0, 131, 0, 132, 0, 130, 0, 127, 0, 131, 0, 130, 0, 124, 0, 127, 0, 131, 0, 122, 0, 132, 0, 131, 0, 127, 0, 122, 0, 133, 0, 139, 0, 136, 0, 133, 0, 138, 0, 139, 0, 136, 0, 140, 0, 137, 0, 136, 0, 139, 0, 140, 0, 138, 0, 151, 0, 139, 0, 138, 0, 148, 0, 151, 0, 139, 0, 141, 0, 140, 0, 139, 0, 151, 0, 141, 0, 134, 0, 145, 0, 142, 0, 134, 0, 144, 0, 145, 0, 142, 0, 146, 0, 143, 0, 142, 0, 145, 0, 146, 0, 144, 0, 140, 0, 145, 0, 144, 0, 137, 0, 140, 0, 145, 0, 141, 0, 146, 0, 145, 0, 140, 0, 141, 0, 135, 0, 150, 0, 147, 0, 135, 0, 149, 0, 150, 0, 147, 0, 151, 0, 148, 0, 147, 0, 150, 0, 151, 0, 149, 0, 146, 0, 150, 0, 149, 0, 143, 0, 146, 0, 150, 0, 141, 0, 151, 0, 150, 0, 146, 0, 141, 0, 97, 0, 138, 0, 133, 0, 97, 0, 109, 0, 138, 0, 109, 0, 148, 0, 138, 0, 109, 0, 110, 0, 148, 0, 110, 0, 147, 0, 148, 0, 110, 0, 100, 0, 147, 0, 100, 0, 135, 0, 147, 0, 100, 0, 95, 0, 135, 0, 19, 0, 111, 0, 97, 0, 19, 0, 22, 0, 111, 0, 22, 0, 105, 0, 111, 0, 22, 0, 23, 0, 105, 0, 23, 0, 104, 0, 105, 0, 23, 0, 30, 0, 104, 0, 30, 0, 96, 0, 104, 0, 30, 0, 20, 0, 96, 0, 2, 0, 35, 0, 21, 0, 2, 0, 14, 0, 35, 0, 14, 0, 29, 0, 35, 0, 14, 0, 15, 0, 29, 0, 15, 0, 28, 0, 29, 0, 15, 0, 5, 0, 28, 0, 5, 0, 20, 0, 28, 0, 5, 0, 0, 0, 20, 0, 134, 0, 125, 0, 115, 0, 134, 0, 142, 0, 125, 0, 142, 0, 118, 0, 125, 0, 142, 0, 143, 0, 118, 0, 143, 0, 117, 0, 118, 0, 143, 0, 149, 0, 117, 0, 149, 0, 114, 0, 117, 0, 149, 0, 135, 0, 114, 0, 40, 0, 68, 0, 58, 0, 40, 0, 52, 0, 68, 0, 52, 0, 61, 0, 68, 0, 52, 0, 53, 0, 61, 0, 53, 0, 60, 0, 61, 0, 53, 0, 43, 0, 60, 0, 43, 0, 57, 0, 60, 0, 43, 0, 38, 0, 57, 0, 76, 0, 106, 0, 96, 0, 76, 0, 79, 0, 106, 0, 79, 0, 99, 0, 106, 0, 79, 0, 80, 0, 99, 0, 80, 0, 98, 0, 99, 0, 80, 0, 87, 0, 98, 0, 87, 0, 95, 0, 98, 0, 87, 0, 77, 0, 95, 0, 39, 0, 130, 0, 116, 0, 39, 0, 47, 0, 130, 0, 47, 0, 124, 0, 130, 0, 47, 0, 48, 0, 124, 0, 48, 0, 123, 0, 124, 0, 48, 0, 54, 0, 123, 0, 54, 0, 115, 0, 123, 0, 54, 0, 40, 0, 115, 0, 1, 0, 49, 0, 39, 0, 1, 0, 9, 0, 49, 0, 9, 0, 42, 0, 49, 0, 9, 0, 10, 0, 42, 0, 10, 0, 41, 0, 42, 0, 10, 0, 16, 0, 41, 0, 16, 0, 38, 0, 41, 0, 16, 0, 2, 0, 38, 0, 133, 0, 73, 0, 59, 0, 133, 0, 136, 0, 73, 0, 136, 0, 67, 0, 73, 0, 136, 0, 137, 0, 67, 0, 137, 0, 66, 0, 67, 0, 137, 0, 144, 0, 66, 0, 144, 0, 58, 0, 66, 0, 144, 0, 134, 0, 58, 0, 116, 0, 92, 0, 78, 0, 116, 0, 128, 0, 92, 0, 128, 0, 86, 0, 92, 0, 128, 0, 129, 0, 86, 0, 129, 0, 85, 0, 86, 0, 129, 0, 119, 0, 85, 0, 119, 0, 77, 0, 85, 0, 119, 0, 114, 0, 77, 0, 78, 0, 11, 0, 1, 0, 78, 0, 90, 0, 11, 0, 90, 0, 4, 0, 11, 0, 90, 0, 91, 0, 4, 0, 91, 0, 3, 0, 4, 0, 91, 0, 81, 0, 3, 0, 81, 0, 0, 0, 3, 0, 81, 0, 76, 0, 0, 0, 59, 0, 24, 0, 19, 0, 59, 0, 71, 0, 24, 0, 71, 0, 34, 0, 24, 0, 71, 0, 72, 0, 34, 0, 72, 0, 33, 0, 34, 0, 72, 0, 62, 0, 33, 0, 62, 0, 21, 0, 33, 0, 62, 0, 57, 0, 21, 0, 96, 0, 0, 0, 76, 0, 96, 0, 20, 0, 0, 0),
"lods": [0.0179063, PackedByteArray(97, 0, 59, 0, 19, 0, 19, 0, 23, 0, 97, 0, 23, 0, 27, 0, 26, 0, 19, 0, 27, 0, 23, 0, 23, 0, 105, 0, 97, 0, 19, 0, 21, 0, 27, 0, 59, 0, 21, 0, 19, 0, 97, 0, 105, 0, 108, 0, 97, 0, 108, 0, 113, 0, 113, 0, 108, 0, 103, 0, 97, 0, 113, 0, 110, 0, 97, 0, 110, 0, 133, 0, 97, 0, 133, 0, 59, 0, 110, 0, 148, 0, 133, 0, 133, 0, 148, 0, 151, 0, 133, 0, 151, 0, 137, 0, 133, 0, 137, 0, 59, 0, 137, 0, 151, 0, 140, 0, 151, 0, 141, 0, 140, 0, 137, 0, 67, 0, 59, 0, 59, 0, 67, 0, 70, 0, 70, 0, 65, 0, 59, 0, 57, 0, 59, 0, 65, 0, 59, 0, 57, 0, 21, 0, 58, 0, 57, 0, 65, 0, 58, 0, 65, 0, 67, 0, 67, 0, 65, 0, 70, 0, 134, 0, 58, 0, 67, 0, 137, 0, 134, 0, 67, 0, 134, 0, 137, 0, 140, 0, 134, 0, 140, 0, 146, 0, 146, 0, 140, 0, 141, 0, 134, 0, 146, 0, 143, 0, 134, 0, 143, 0, 115, 0, 58, 0, 134, 0, 115, 0, 143, 0, 118, 0, 115, 0, 115, 0, 118, 0, 121, 0, 115, 0, 121, 0, 127, 0, 127, 0, 121, 0, 122, 0, 115, 0, 127, 0, 124, 0, 58, 0, 115, 0, 40, 0, 40, 0, 115, 0, 124, 0, 48, 0, 40, 0, 124, 0, 40, 0, 48, 0, 51, 0, 40, 0, 57, 0, 58, 0, 51, 0, 46, 0, 40, 0, 40, 0, 38, 0, 57, 0, 38, 0, 40, 0, 46, 0, 21, 0, 57, 0, 38, 0, 39, 0, 38, 0, 46, 0, 21, 0, 38, 0, 2, 0, 2, 0, 20, 0, 21, 0, 1, 0, 38, 0, 39, 0, 1, 0, 2, 0, 38, 0, 39, 0, 46, 0, 48, 0, 48, 0, 46, 0, 51, 0, 39, 0, 48, 0, 116, 0, 1, 0, 39, 0, 116, 0, 48, 0, 124, 0, 116, 0, 116, 0, 124, 0, 127, 0, 116, 0, 127, 0, 132, 0, 132, 0, 127, 0, 122, 0, 116, 0, 132, 0, 129, 0, 116, 0, 129, 0, 78, 0, 1, 0, 116, 0, 78, 0, 129, 0, 86, 0, 78, 0, 78, 0, 86, 0, 89, 0, 78, 0, 89, 0, 94, 0, 94, 0, 89, 0, 84, 0, 78, 0, 94, 0, 91, 0, 78, 0, 91, 0, 1, 0, 91, 0, 4, 0, 1, 0, 1, 0, 4, 0, 7, 0, 1, 0, 7, 0, 8, 0, 2, 0, 1, 0, 8, 0, 0, 0, 2, 0, 8, 0, 0, 0, 8, 0, 4, 0, 2, 0, 0, 0, 20, 0, 4, 0, 8, 0, 7, 0, 76, 0, 0, 0, 4, 0, 91, 0, 76, 0, 4, 0, 76, 0, 91, 0, 94, 0, 96, 0, 0, 0, 76, 0, 96, 0, 20, 0, 0, 0, 76, 0, 94, 0, 83, 0, 94, 0, 84, 0, 83, 0, 77, 0, 76, 0, 83, 0, 89, 0, 83, 0, 84, 0, 77, 0, 83, 0, 89, 0, 77, 0, 89, 0, 86, 0, 76, 0, 77, 0, 95, 0, 76, 0, 95, 0, 96, 0, 114, 0, 77, 0, 86, 0, 129, 0, 114, 0, 86, 0, 114, 0, 129, 0, 132, 0, 132, 0, 122, 0, 121, 0, 118, 0, 132, 0, 121, 0, 114, 0, 132, 0, 118, 0, 135, 0, 77, 0, 114, 0, 135, 0, 114, 0, 118, 0, 135, 0, 95, 0, 77, 0, 143, 0, 135, 0, 118, 0, 135, 0, 143, 0, 146, 0, 135, 0, 146, 0, 151, 0, 151, 0, 146, 0, 141, 0, 135, 0, 151, 0, 148, 0, 95, 0, 135, 0, 148, 0, 110, 0, 95, 0, 148, 0, 95, 0, 110, 0, 113, 0, 95, 0, 113, 0, 102, 0, 113, 0, 103, 0, 102, 0, 96, 0, 95, 0, 102, 0, 96, 0, 102, 0, 108, 0, 108, 0, 102, 0, 103, 0, 96, 0, 108, 0, 105, 0, 20, 0, 96, 0, 105, 0, 23, 0, 20, 0, 105, 0, 20, 0, 23, 0, 26, 0, 20, 0, 26, 0, 27, 0, 21, 0, 20, 0, 27, 0), 0.0210464, PackedByteArray(105, 0, 59, 0, 27, 0, 110, 0, 105, 0, 103, 0, 105, 0, 110, 0, 148, 0, 105, 0, 148, 0, 59, 0, 148, 0, 141, 0, 137, 0, 148, 0, 137, 0, 59, 0, 137, 0, 67, 0, 59, 0, 67, 0, 65, 0, 59, 0, 59, 0, 21, 0, 27, 0, 59, 0, 65, 0, 21, 0, 21, 0, 65, 0, 46, 0, 21, 0, 46, 0, 2, 0, 2, 0, 23, 0, 21, 0, 48, 0, 46, 0, 65, 0, 48, 0, 65, 0, 58, 0, 58, 0, 65, 0, 67, 0, 137, 0, 58, 0, 67, 0, 58, 0, 118, 0, 48, 0, 58, 0, 137, 0, 118, 0, 48, 0, 118, 0, 124, 0, 124, 0, 118, 0, 122, 0, 137, 0, 143, 0, 118, 0, 143, 0, 137, 0, 141, 0, 2, 0, 4, 0, 23, 0, 96, 0, 23, 0, 4, 0, 96, 0, 4, 0, 83, 0, 23, 0, 96, 0, 105, 0, 96, 0, 102, 0, 105, 0, 83, 0, 102, 0, 96, 0, 105, 0, 102, 0, 103, 0, 91, 0, 84, 0, 83, 0, 91, 0, 83, 0, 4, 0, 4, 0, 2, 0, 8, 0, 86, 0, 83, 0, 84, 0, 77, 0, 83, 0, 86, 0, 83, 0, 77, 0, 102, 0, 129, 0, 77, 0, 86, 0, 110, 0, 103, 0, 102, 0, 110, 0, 102, 0, 148, 0, 143, 0, 102, 0, 77, 0, 102, 0, 143, 0, 148, 0, 143, 0, 77, 0, 129, 0, 148, 0, 143, 0, 141, 0, 143, 0, 129, 0, 118, 0, 129, 0, 122, 0, 118, 0, 1, 0, 124, 0, 86, 0, 124, 0, 129, 0, 86, 0, 129, 0, 124, 0, 122, 0, 86, 0, 91, 0, 1, 0, 91, 0, 86, 0, 84, 0, 91, 0, 4, 0, 1, 0, 1, 0, 4, 0, 8, 0, 2, 0, 1, 0, 8, 0, 1, 0, 2, 0, 46, 0, 1, 0, 39, 0, 124, 0, 1, 0, 46, 0, 39, 0, 39, 0, 48, 0, 124, 0, 39, 0, 46, 0, 48, 0, 21, 0, 23, 0, 27, 0, 27, 0, 23, 0, 105, 0), 0.215511, PackedByteArray(23, 0, 67, 0, 27, 0, 67, 0, 65, 0, 27, 0, 23, 0, 148, 0, 67, 0, 148, 0, 141, 0, 67, 0, 148, 0, 23, 0, 103, 0, 143, 0, 102, 0, 86, 0, 83, 0, 86, 0, 102, 0, 86, 0, 122, 0, 143, 0, 102, 0, 143, 0, 148, 0, 148, 0, 143, 0, 141, 0, 148, 0, 103, 0, 102, 0, 86, 0, 83, 0, 84, 0, 4, 0, 84, 0, 83, 0, 83, 0, 8, 0, 4, 0, 83, 0, 102, 0, 23, 0, 23, 0, 102, 0, 103, 0, 23, 0, 8, 0, 83, 0, 8, 0, 23, 0, 27, 0, 67, 0, 143, 0, 48, 0, 143, 0, 67, 0, 141, 0, 48, 0, 143, 0, 122, 0, 48, 0, 65, 0, 67, 0, 48, 0, 46, 0, 65, 0, 4, 0, 48, 0, 86, 0, 86, 0, 48, 0, 122, 0, 4, 0, 86, 0, 84, 0, 4, 0, 46, 0, 48, 0, 4, 0, 8, 0, 46, 0, 27, 0, 46, 0, 8, 0, 27, 0, 65, 0, 46, 0)],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 152,
"vertex_data": PackedByteArray(177, 248, 177, 248, 0, 0, 0, 0, 177, 248, 255, 255, 77, 7, 0, 0, 255, 255, 177, 248, 77, 7, 0, 0, 177, 248, 125, 251, 142, 0, 0, 0, 177, 248, 219, 253, 35, 2, 0, 0, 125, 251, 177, 248, 142, 0, 0, 0, 143, 251, 143, 251, 58, 1, 0, 0, 105, 251, 124, 253, 130, 2, 0, 0, 232, 252, 232, 252, 22, 3, 0, 0, 125, 251, 112, 255, 77, 7, 0, 0, 219, 253, 219, 253, 77, 7, 0, 0, 177, 248, 112, 255, 129, 4, 0, 0, 143, 251, 196, 254, 111, 4, 0, 0, 124, 253, 124, 253, 149, 4, 0, 0, 112, 255, 177, 248, 129, 4, 0, 0, 219, 253, 177, 248, 35, 2, 0, 0, 112, 255, 125, 251, 77, 7, 0, 0, 196, 254, 143, 251, 111, 4, 0, 0, 124, 253, 105, 251, 130, 2, 0, 0, 177, 248, 0, 0, 77, 7, 0, 0, 177, 248, 77, 7, 0, 0, 0, 0, 255, 255, 77, 7, 77, 7, 0, 0, 177, 248, 142, 0, 129, 4, 0, 0, 177, 248, 35, 2, 35, 2, 0, 0, 125, 251, 142, 0, 77, 7, 0, 0, 143, 251, 58, 1, 111, 4, 0, 0, 105, 251, 130, 2, 130, 2, 0, 0, 232, 252, 22, 3, 22, 3, 0, 0, 125, 251, 77, 7, 142, 0, 0, 0, 219, 253, 77, 7, 35, 2, 0, 0, 177, 248, 129, 4, 142, 0, 0, 0, 143, 251, 111, 4, 58, 1, 0, 0, 124, 253, 149, 4, 130, 2, 0, 0, 112, 255, 129, 4, 77, 7, 0, 0, 219, 253, 35, 2, 77, 7, 0, 0, 112, 255, 77, 7, 129, 4, 0, 0, 196, 254, 111, 4, 111, 4, 0, 0, 124, 253, 130, 2, 149, 4, 0, 0, 255, 255, 177, 248, 177, 248, 0, 0, 177, 248, 255, 255, 177, 248, 0, 0, 177, 248, 177, 248, 255, 255, 0, 0, 112, 255, 125, 251, 177, 248, 0, 0, 219, 253, 219, 253, 177, 248, 0, 0, 112, 255, 177, 248, 125, 251, 0, 0, 196, 254, 143, 251, 143, 251, 0, 0, 124, 253, 124, 253, 105, 251, 0, 0, 232, 252, 232, 252, 232, 252, 0, 0, 177, 248, 112, 255, 125, 251, 0, 0, 177, 248, 219, 253, 219, 253, 0, 0, 125, 251, 112, 255, 177, 248, 0, 0, 143, 251, 196, 254, 143, 251, 0, 0, 105, 251, 124, 253, 124, 253, 0, 0, 125, 251, 177, 248, 112, 255, 0, 0, 219, 253, 177, 248, 219, 253, 0, 0, 177, 248, 125, 251, 112, 255, 0, 0, 143, 251, 143, 251, 196, 254, 0, 0, 124, 253, 105, 251, 124, 253, 0, 0, 255, 255, 77, 7, 177, 248, 0, 0, 177, 248, 77, 7, 255, 255, 0, 0, 177, 248, 0, 0, 177, 248, 0, 0, 112, 255, 77, 7, 125, 251, 0, 0, 219, 253, 77, 7, 219, 253, 0, 0, 112, 255, 129, 4, 177, 248, 0, 0, 196, 254, 111, 4, 143, 251, 0, 0, 124, 253, 149, 4, 124, 253, 0, 0, 232, 252, 22, 3, 232, 252, 0, 0, 177, 248, 129, 4, 112, 255, 0, 0, 177, 248, 35, 2, 219, 253, 0, 0, 125, 251, 77, 7, 112, 255, 0, 0, 143, 251, 111, 4, 196, 254, 0, 0, 105, 251, 130, 2, 124, 253, 0, 0, 125, 251, 142, 0, 177, 248, 0, 0, 219, 253, 35, 2, 177, 248, 0, 0, 177, 248, 142, 0, 125, 251, 0, 0, 143, 251, 58, 1, 143, 251, 0, 0, 124, 253, 130, 2, 105, 251, 0, 0, 77, 7, 177, 248, 0, 0, 0, 0, 0, 0, 177, 248, 77, 7, 0, 0, 77, 7, 255, 255, 77, 7, 0, 0, 129, 4, 177, 248, 142, 0, 0, 0, 35, 2, 177, 248, 35, 2, 0, 0, 77, 7, 125, 251, 142, 0, 0, 0, 111, 4, 143, 251, 58, 1, 0, 0, 130, 2, 105, 251, 130, 2, 0, 0, 22, 3, 232, 252, 22, 3, 0, 0, 142, 0, 125, 251, 77, 7, 0, 0, 35, 2, 219, 253, 77, 7, 0, 0, 142, 0, 177, 248, 129, 4, 0, 0, 58, 1, 143, 251, 111, 4, 0, 0, 130, 2, 124, 253, 149, 4, 0, 0, 77, 7, 112, 255, 129, 4, 0, 0, 77, 7, 219, 253, 35, 2, 0, 0, 129, 4, 112, 255, 77, 7, 0, 0, 111, 4, 196, 254, 111, 4, 0, 0, 149, 4, 124, 253, 130, 2, 0, 0, 0, 0, 77, 7, 77, 7, 0, 0, 77, 7, 77, 7, 0, 0, 0, 0, 77, 7, 0, 0, 77, 7, 0, 0, 142, 0, 77, 7, 129, 4, 0, 0, 35, 2, 77, 7, 35, 2, 0, 0, 142, 0, 129, 4, 77, 7, 0, 0, 58, 1, 111, 4, 111, 4, 0, 0, 130, 2, 149, 4, 130, 2, 0, 0, 22, 3, 22, 3, 22, 3, 0, 0, 77, 7, 129, 4, 142, 0, 0, 0, 77, 7, 35, 2, 35, 2, 0, 0, 129, 4, 77, 7, 142, 0, 0, 0, 111, 4, 111, 4, 58, 1, 0, 0, 149, 4, 130, 2, 130, 2, 0, 0, 129, 4, 142, 0, 77, 7, 0, 0, 35, 2, 35, 2, 77, 7, 0, 0, 77, 7, 142, 0, 129, 4, 0, 0, 111, 4, 58, 1, 111, 4, 0, 0, 130, 2, 130, 2, 149, 4, 0, 0, 0, 0, 177, 248, 177, 248, 0, 0, 77, 7, 177, 248, 255, 255, 0, 0, 77, 7, 255, 255, 177, 248, 0, 0, 142, 0, 177, 248, 125, 251, 0, 0, 35, 2, 177, 248, 219, 253, 0, 0, 142, 0, 125, 251, 177, 248, 0, 0, 58, 1, 143, 251, 143, 251, 0, 0, 130, 2, 105, 251, 124, 253, 0, 0, 22, 3, 232, 252, 232, 252, 0, 0, 77, 7, 125, 251, 112, 255, 0, 0, 77, 7, 219, 253, 219, 253, 0, 0, 129, 4, 177, 248, 112, 255, 0, 0, 111, 4, 143, 251, 196, 254, 0, 0, 149, 4, 124, 253, 124, 253, 0, 0, 129, 4, 112, 255, 177, 248, 0, 0, 35, 2, 219, 253, 177, 248, 0, 0, 77, 7, 112, 255, 125, 251, 0, 0, 111, 4, 196, 254, 143, 251, 0, 0, 130, 2, 124, 253, 105, 251, 0, 0, 77, 7, 0, 0, 177, 248, 0, 0, 77, 7, 77, 7, 255, 255, 0, 0, 0, 0, 77, 7, 177, 248, 0, 0, 77, 7, 142, 0, 125, 251, 0, 0, 77, 7, 35, 2, 219, 253, 0, 0, 129, 4, 142, 0, 177, 248, 0, 0, 111, 4, 58, 1, 143, 251, 0, 0, 149, 4, 130, 2, 124, 253, 0, 0, 22, 3, 22, 3, 232, 252, 0, 0, 129, 4, 77, 7, 112, 255, 0, 0, 35, 2, 77, 7, 219, 253, 0, 0, 77, 7, 129, 4, 112, 255, 0, 0, 111, 4, 111, 4, 196, 254, 0, 0, 130, 2, 149, 4, 124, 253, 0, 0, 142, 0, 129, 4, 177, 248, 0, 0, 35, 2, 35, 2, 177, 248, 0, 0, 142, 0, 77, 7, 125, 251, 0, 0, 58, 1, 111, 4, 143, 251, 0, 0, 130, 2, 130, 2, 105, 251, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_wagqq"]
resource_name = "card_Cube"
_surfaces = [{
"aabb": AABB(-1, -1, -1, 2, 2, 2),
"attribute_data": PackedByteArray(44, 158, 210, 129, 210, 161, 44, 126, 44, 158, 44, 126, 222, 158, 210, 129, 255, 159, 210, 129, 210, 161, 255, 127, 44, 158, 31, 129, 227, 158, 27, 129, 255, 159, 36, 129, 36, 161, 255, 127, 189, 159, 255, 127, 31, 161, 44, 126, 255, 159, 44, 126, 210, 161, 223, 126, 27, 161, 227, 126, 255, 159, 218, 126, 255, 159, 218, 126, 44, 158, 223, 126, 44, 158, 255, 127, 222, 158, 44, 126, 227, 158, 227, 126, 218, 158, 255, 127, 44, 94, 44, 126, 210, 97, 210, 129, 210, 97, 44, 126, 44, 94, 223, 126, 44, 94, 255, 127, 255, 95, 210, 129, 223, 94, 44, 126, 227, 94, 227, 126, 218, 94, 255, 127, 255, 95, 36, 129, 255, 95, 189, 127, 210, 97, 31, 129, 210, 97, 255, 127, 32, 97, 210, 129, 27, 97, 27, 129, 36, 97, 255, 127, 32, 97, 44, 126, 255, 95, 44, 126, 210, 97, 223, 126, 27, 97, 227, 126, 255, 95, 218, 126, 44, 158, 211, 65, 210, 161, 211, 65, 44, 158, 44, 62, 222, 158, 211, 65, 255, 159, 211, 65, 44, 158, 32, 65, 227, 158, 27, 65, 255, 159, 37, 65, 255, 159, 65, 64, 210, 161, 32, 65, 255, 159, 44, 62, 210, 161, 255, 63, 31, 161, 211, 65, 27, 161, 27, 65, 255, 159, 218, 62, 36, 161, 255, 63, 44, 158, 223, 62, 44, 158, 255, 63, 222, 158, 44, 62, 227, 158, 227, 62, 218, 158, 255, 63, 210, 97, 211, 65, 210, 97, 44, 62, 44, 94, 211, 65, 210, 97, 32, 65, 210, 97, 255, 63, 32, 97, 211, 65, 27, 97, 27, 65, 36, 97, 255, 63, 65, 96, 255, 63, 32, 97, 44, 62, 44, 94, 255, 63, 255, 95, 44, 62, 210, 97, 223, 62, 27, 97, 227, 62, 218, 94, 255, 63, 255, 95, 218, 62, 223, 94, 211, 65, 255, 95, 211, 65, 44, 94, 32, 65, 227, 94, 27, 65, 255, 95, 37, 65, 44, 158, 43, 190, 44, 158, 210, 193, 43, 222, 44, 126, 44, 158, 222, 190, 44, 158, 255, 191, 222, 158, 43, 190, 227, 158, 227, 190, 218, 158, 255, 191, 57, 159, 255, 191, 255, 159, 196, 192, 57, 223, 255, 127, 222, 158, 210, 193, 255, 159, 210, 193, 255, 223, 44, 126, 44, 158, 31, 193, 227, 158, 27, 193, 255, 159, 36, 193, 255, 223, 218, 126, 43, 222, 223, 126, 255, 159, 43, 190, 43, 222, 255, 127, 222, 222, 44, 126, 227, 222, 227, 126, 255, 159, 217, 190, 217, 222, 255, 127, 210, 97, 210, 193, 210, 97, 43, 190, 211, 33, 44, 126, 210, 97, 31, 193, 210, 97, 255, 191, 32, 97, 210, 193, 27, 97, 27, 193, 36, 97, 255, 191, 255, 31, 57, 127, 255, 95, 57, 191, 197, 96, 255, 191, 32, 97, 43, 190, 211, 33, 255, 127, 255, 95, 43, 190, 210, 97, 222, 190, 27, 97, 227, 190, 37, 33, 255, 127, 255, 95, 217, 190, 32, 33, 44, 126, 255, 31, 44, 126, 255, 95, 210, 193, 211, 33, 223, 126, 27, 33, 227, 126, 255, 31, 218, 126, 255, 95, 36, 193, 44, 158, 43, 254, 44, 158, 211, 1, 43, 222, 211, 65, 44, 158, 222, 254, 44, 158, 0, 0, 44, 158, 255, 255, 222, 158, 43, 254, 227, 158, 227, 254, 218, 158, 0, 0, 218, 158, 255, 255, 57, 159, 255, 255, 255, 159, 197, 0, 255, 223, 197, 64, 222, 158, 211, 1, 255, 159, 211, 1, 43, 222, 255, 63, 44, 158, 32, 1, 227, 158, 27, 1, 255, 159, 37, 1, 217, 222, 255, 63, 222, 222, 211, 65, 255, 159, 43, 254, 255, 223, 211, 65, 43, 222, 32, 65, 227, 222, 27, 65, 255, 159, 217, 254, 255, 223, 37, 65, 211, 33, 211, 65, 210, 97, 211, 1, 210, 97, 43, 254, 211, 33, 32, 65, 211, 33, 255, 63, 255, 95, 211, 1, 32, 33, 211, 65, 27, 33, 27, 65, 37, 33, 255, 63, 255, 95, 37, 1, 197, 32, 255, 63, 255, 95, 57, 255, 197, 96, 0, 0, 210, 97, 32, 1, 210, 97, 0, 0, 210, 97, 255, 255, 32, 97, 211, 1, 27, 97, 27, 1, 36, 97, 255, 255, 36, 97, 0, 0, 32, 97, 43, 254, 255, 31, 211, 65, 255, 95, 43, 254, 210, 97, 222, 254, 27, 97, 227, 254, 255, 31, 37, 65, 255, 95, 217, 254, 210, 161, 255, 127, 189, 159, 255, 127, 189, 159, 255, 127, 189, 159, 255, 127, 44, 94, 255, 127, 255, 95, 210, 129, 255, 95, 189, 127, 255, 95, 189, 127, 255, 95, 189, 127, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 44, 62, 210, 161, 255, 63, 65, 96, 255, 63, 65, 96, 255, 63, 65, 96, 255, 63, 44, 94, 255, 63, 255, 95, 44, 62, 218, 158, 255, 191, 218, 158, 255, 191, 57, 159, 255, 191, 57, 223, 255, 127, 255, 159, 210, 193, 255, 223, 44, 126, 36, 97, 255, 191, 36, 97, 255, 191, 255, 31, 57, 127, 255, 95, 57, 191, 197, 96, 255, 191, 57, 159, 255, 255, 255, 159, 197, 0, 255, 223, 197, 64, 197, 32, 255, 63, 197, 96, 0, 0, 210, 97, 0, 0, 210, 97, 255, 255, 255, 31, 211, 65, 44, 158, 44, 126, 255, 159, 210, 129, 189, 159, 255, 127, 210, 97, 44, 126, 255, 95, 210, 129, 255, 95, 210, 129, 255, 95, 189, 127, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 44, 62, 65, 96, 255, 63, 65, 96, 255, 63, 65, 96, 255, 63, 218, 158, 255, 191, 218, 158, 255, 191, 255, 223, 44, 126, 36, 97, 255, 191, 36, 97, 255, 191, 211, 33, 255, 127, 44, 158, 0, 0, 43, 222, 255, 63, 255, 159, 43, 254, 255, 95, 211, 1, 210, 97, 255, 255, 255, 31, 211, 65, 210, 161, 44, 126, 44, 158, 44, 126, 189, 159, 255, 127, 210, 97, 210, 129, 210, 97, 44, 126, 255, 95, 189, 127, 44, 158, 211, 65, 44, 158, 44, 62, 255, 159, 65, 64, 255, 159, 65, 64, 210, 97, 211, 65, 44, 94, 211, 65, 65, 96, 255, 63, 44, 158, 43, 190, 210, 97, 210, 193),
"format": 34896613399,
"index_count": 900,
"index_data": PackedByteArray(112, 0, 66, 0, 22, 0, 112, 0, 162, 0, 66, 0, 164, 0, 86, 0, 135, 0, 164, 0, 110, 0, 86, 0, 65, 0, 136, 0, 45, 0, 65, 0, 163, 0, 136, 0, 1, 0, 137, 0, 87, 0, 1, 0, 44, 0, 137, 0, 24, 0, 43, 0, 2, 0, 24, 0, 64, 0, 43, 0, 0, 0, 7, 0, 3, 0, 0, 0, 6, 0, 7, 0, 3, 0, 8, 0, 4, 0, 3, 0, 7, 0, 8, 0, 6, 0, 21, 0, 7, 0, 6, 0, 18, 0, 21, 0, 7, 0, 10, 0, 8, 0, 7, 0, 21, 0, 10, 0, 1, 0, 14, 0, 11, 0, 1, 0, 13, 0, 14, 0, 11, 0, 16, 0, 12, 0, 11, 0, 14, 0, 16, 0, 13, 0, 9, 0, 14, 0, 13, 0, 5, 0, 9, 0, 14, 0, 10, 0, 16, 0, 14, 0, 9, 0, 10, 0, 2, 0, 20, 0, 17, 0, 2, 0, 19, 0, 20, 0, 17, 0, 21, 0, 18, 0, 17, 0, 20, 0, 21, 0, 19, 0, 15, 0, 20, 0, 19, 0, 12, 0, 15, 0, 20, 0, 10, 0, 21, 0, 20, 0, 15, 0, 10, 0, 22, 0, 29, 0, 25, 0, 22, 0, 28, 0, 29, 0, 25, 0, 30, 0, 26, 0, 25, 0, 29, 0, 30, 0, 28, 0, 42, 0, 29, 0, 28, 0, 39, 0, 42, 0, 29, 0, 32, 0, 30, 0, 29, 0, 42, 0, 32, 0, 23, 0, 36, 0, 33, 0, 23, 0, 35, 0, 36, 0, 33, 0, 37, 0, 34, 0, 33, 0, 36, 0, 37, 0, 35, 0, 31, 0, 36, 0, 35, 0, 27, 0, 31, 0, 36, 0, 32, 0, 37, 0, 36, 0, 31, 0, 32, 0, 24, 0, 41, 0, 38, 0, 24, 0, 40, 0, 41, 0, 38, 0, 42, 0, 39, 0, 38, 0, 41, 0, 42, 0, 40, 0, 37, 0, 41, 0, 40, 0, 34, 0, 37, 0, 41, 0, 32, 0, 42, 0, 41, 0, 37, 0, 32, 0, 43, 0, 49, 0, 46, 0, 43, 0, 48, 0, 49, 0, 46, 0, 50, 0, 47, 0, 46, 0, 49, 0, 50, 0, 48, 0, 63, 0, 49, 0, 48, 0, 60, 0, 63, 0, 49, 0, 51, 0, 50, 0, 49, 0, 63, 0, 51, 0, 44, 0, 56, 0, 52, 0, 44, 0, 55, 0, 56, 0, 52, 0, 58, 0, 54, 0, 52, 0, 56, 0, 58, 0, 55, 0, 50, 0, 56, 0, 55, 0, 47, 0, 50, 0, 56, 0, 51, 0, 58, 0, 56, 0, 50, 0, 51, 0, 45, 0, 62, 0, 59, 0, 45, 0, 61, 0, 62, 0, 59, 0, 63, 0, 60, 0, 59, 0, 62, 0, 63, 0, 61, 0, 57, 0, 62, 0, 61, 0, 53, 0, 57, 0, 62, 0, 51, 0, 63, 0, 62, 0, 57, 0, 51, 0, 64, 0, 70, 0, 67, 0, 64, 0, 69, 0, 70, 0, 67, 0, 71, 0, 68, 0, 67, 0, 70, 0, 71, 0, 69, 0, 84, 0, 70, 0, 69, 0, 81, 0, 84, 0, 70, 0, 72, 0, 71, 0, 70, 0, 84, 0, 72, 0, 65, 0, 77, 0, 73, 0, 65, 0, 76, 0, 77, 0, 73, 0, 79, 0, 75, 0, 73, 0, 77, 0, 79, 0, 76, 0, 71, 0, 77, 0, 76, 0, 68, 0, 71, 0, 77, 0, 72, 0, 79, 0, 77, 0, 71, 0, 72, 0, 66, 0, 83, 0, 80, 0, 66, 0, 82, 0, 83, 0, 80, 0, 84, 0, 81, 0, 80, 0, 83, 0, 84, 0, 82, 0, 78, 0, 83, 0, 82, 0, 74, 0, 78, 0, 83, 0, 72, 0, 84, 0, 83, 0, 78, 0, 72, 0, 85, 0, 91, 0, 88, 0, 85, 0, 90, 0, 91, 0, 88, 0, 92, 0, 89, 0, 88, 0, 91, 0, 92, 0, 90, 0, 108, 0, 91, 0, 90, 0, 104, 0, 108, 0, 91, 0, 93, 0, 92, 0, 91, 0, 108, 0, 93, 0, 86, 0, 100, 0, 96, 0, 86, 0, 99, 0, 100, 0, 96, 0, 101, 0, 97, 0, 96, 0, 100, 0, 101, 0, 99, 0, 92, 0, 100, 0, 99, 0, 89, 0, 92, 0, 100, 0, 94, 0, 101, 0, 100, 0, 92, 0, 94, 0, 87, 0, 107, 0, 103, 0, 87, 0, 106, 0, 107, 0, 103, 0, 109, 0, 105, 0, 103, 0, 107, 0, 109, 0, 106, 0, 102, 0, 107, 0, 106, 0, 98, 0, 102, 0, 107, 0, 95, 0, 109, 0, 107, 0, 102, 0, 95, 0, 110, 0, 116, 0, 113, 0, 110, 0, 115, 0, 116, 0, 113, 0, 117, 0, 114, 0, 113, 0, 116, 0, 117, 0, 115, 0, 134, 0, 116, 0, 115, 0, 130, 0, 134, 0, 116, 0, 120, 0, 117, 0, 116, 0, 134, 0, 120, 0, 111, 0, 125, 0, 121, 0, 111, 0, 124, 0, 125, 0, 121, 0, 127, 0, 123, 0, 121, 0, 125, 0, 127, 0, 124, 0, 117, 0, 125, 0, 124, 0, 114, 0, 117, 0, 125, 0, 119, 0, 127, 0, 125, 0, 117, 0, 119, 0, 112, 0, 132, 0, 128, 0, 112, 0, 131, 0, 132, 0, 128, 0, 133, 0, 129, 0, 128, 0, 132, 0, 133, 0, 131, 0, 126, 0, 132, 0, 131, 0, 122, 0, 126, 0, 132, 0, 118, 0, 133, 0, 132, 0, 126, 0, 118, 0, 135, 0, 142, 0, 138, 0, 135, 0, 141, 0, 142, 0, 138, 0, 144, 0, 140, 0, 138, 0, 142, 0, 144, 0, 141, 0, 160, 0, 142, 0, 141, 0, 156, 0, 160, 0, 142, 0, 145, 0, 144, 0, 142, 0, 160, 0, 145, 0, 136, 0, 152, 0, 148, 0, 136, 0, 151, 0, 152, 0, 148, 0, 153, 0, 149, 0, 148, 0, 152, 0, 153, 0, 151, 0, 143, 0, 152, 0, 151, 0, 139, 0, 143, 0, 152, 0, 146, 0, 153, 0, 152, 0, 143, 0, 146, 0, 137, 0, 159, 0, 155, 0, 137, 0, 158, 0, 159, 0, 155, 0, 161, 0, 157, 0, 155, 0, 159, 0, 161, 0, 158, 0, 154, 0, 159, 0, 158, 0, 150, 0, 154, 0, 159, 0, 147, 0, 161, 0, 159, 0, 154, 0, 147, 0, 162, 0, 169, 0, 165, 0, 162, 0, 168, 0, 169, 0, 165, 0, 170, 0, 166, 0, 165, 0, 169, 0, 170, 0, 168, 0, 187, 0, 169, 0, 168, 0, 183, 0, 187, 0, 169, 0, 172, 0, 170, 0, 169, 0, 187, 0, 172, 0, 163, 0, 179, 0, 175, 0, 163, 0, 178, 0, 179, 0, 175, 0, 181, 0, 176, 0, 175, 0, 179, 0, 181, 0, 178, 0, 171, 0, 179, 0, 178, 0, 167, 0, 171, 0, 179, 0, 174, 0, 181, 0, 179, 0, 171, 0, 174, 0, 164, 0, 186, 0, 182, 0, 164, 0, 185, 0, 186, 0, 182, 0, 188, 0, 184, 0, 182, 0, 186, 0, 188, 0, 185, 0, 180, 0, 186, 0, 185, 0, 177, 0, 180, 0, 186, 0, 173, 0, 188, 0, 186, 0, 180, 0, 173, 0, 112, 0, 168, 0, 162, 0, 112, 0, 128, 0, 168, 0, 128, 0, 183, 0, 168, 0, 128, 0, 129, 0, 183, 0, 130, 0, 182, 0, 184, 0, 130, 0, 115, 0, 182, 0, 115, 0, 164, 0, 182, 0, 115, 0, 110, 0, 164, 0, 22, 0, 131, 0, 112, 0, 22, 0, 25, 0, 131, 0, 25, 0, 122, 0, 131, 0, 25, 0, 26, 0, 122, 0, 27, 0, 121, 0, 123, 0, 27, 0, 35, 0, 121, 0, 35, 0, 111, 0, 121, 0, 35, 0, 23, 0, 111, 0, 2, 0, 40, 0, 24, 0, 2, 0, 17, 0, 40, 0, 17, 0, 34, 0, 40, 0, 17, 0, 18, 0, 34, 0, 18, 0, 33, 0, 34, 0, 18, 0, 6, 0, 33, 0, 6, 0, 23, 0, 33, 0, 6, 0, 0, 0, 23, 0, 163, 0, 151, 0, 136, 0, 163, 0, 175, 0, 151, 0, 175, 0, 139, 0, 151, 0, 175, 0, 176, 0, 139, 0, 177, 0, 138, 0, 140, 0, 177, 0, 185, 0, 138, 0, 185, 0, 135, 0, 138, 0, 185, 0, 164, 0, 135, 0, 45, 0, 76, 0, 65, 0, 45, 0, 59, 0, 76, 0, 59, 0, 68, 0, 76, 0, 59, 0, 60, 0, 68, 0, 60, 0, 67, 0, 68, 0, 60, 0, 48, 0, 67, 0, 48, 0, 64, 0, 67, 0, 48, 0, 43, 0, 64, 0, 85, 0, 124, 0, 111, 0, 85, 0, 88, 0, 124, 0, 88, 0, 114, 0, 124, 0, 88, 0, 89, 0, 114, 0, 89, 0, 113, 0, 114, 0, 89, 0, 99, 0, 113, 0, 99, 0, 110, 0, 113, 0, 99, 0, 86, 0, 110, 0, 44, 0, 158, 0, 137, 0, 44, 0, 52, 0, 158, 0, 52, 0, 150, 0, 158, 0, 52, 0, 54, 0, 150, 0, 53, 0, 148, 0, 149, 0, 53, 0, 61, 0, 148, 0, 61, 0, 136, 0, 148, 0, 61, 0, 45, 0, 136, 0, 1, 0, 55, 0, 44, 0, 1, 0, 11, 0, 55, 0, 11, 0, 47, 0, 55, 0, 11, 0, 12, 0, 47, 0, 12, 0, 46, 0, 47, 0, 12, 0, 19, 0, 46, 0, 19, 0, 43, 0, 46, 0, 19, 0, 2, 0, 43, 0, 162, 0, 82, 0, 66, 0, 162, 0, 165, 0, 82, 0, 165, 0, 74, 0, 82, 0, 165, 0, 166, 0, 74, 0, 167, 0, 73, 0, 75, 0, 167, 0, 178, 0, 73, 0, 178, 0, 65, 0, 73, 0, 178, 0, 163, 0, 65, 0, 137, 0, 106, 0, 87, 0, 137, 0, 155, 0, 106, 0, 155, 0, 98, 0, 106, 0, 155, 0, 157, 0, 98, 0, 156, 0, 96, 0, 97, 0, 156, 0, 141, 0, 96, 0, 141, 0, 86, 0, 96, 0, 141, 0, 135, 0, 86, 0, 87, 0, 13, 0, 1, 0, 87, 0, 103, 0, 13, 0, 103, 0, 5, 0, 13, 0, 103, 0, 105, 0, 5, 0, 104, 0, 3, 0, 4, 0, 104, 0, 90, 0, 3, 0, 90, 0, 0, 0, 3, 0, 90, 0, 85, 0, 0, 0, 66, 0, 28, 0, 22, 0, 66, 0, 80, 0, 28, 0, 80, 0, 39, 0, 28, 0, 80, 0, 81, 0, 39, 0, 81, 0, 38, 0, 39, 0, 81, 0, 69, 0, 38, 0, 69, 0, 24, 0, 38, 0, 69, 0, 64, 0, 24, 0, 111, 0, 0, 0, 85, 0, 111, 0, 23, 0, 0, 0),
"lods": [0.0179063, PackedByteArray(112, 0, 8, 1, 22, 0, 22, 0, 26, 0, 112, 0, 26, 0, 32, 0, 30, 0, 22, 0, 2, 1, 26, 0, 26, 0, 122, 0, 112, 0, 22, 0, 1, 1, 2, 1, 8, 1, 1, 1, 22, 0, 112, 0, 122, 0, 126, 0, 112, 0, 126, 0, 133, 0, 133, 0, 126, 0, 118, 0, 112, 0, 133, 0, 129, 0, 112, 0, 129, 0, 162, 0, 112, 0, 162, 0, 8, 1, 129, 0, 183, 0, 162, 0, 162, 0, 183, 0, 187, 0, 162, 0, 187, 0, 166, 0, 162, 0, 166, 0, 8, 1, 166, 0, 187, 0, 170, 0, 187, 0, 172, 0, 170, 0, 166, 0, 74, 0, 8, 1, 8, 1, 74, 0, 78, 0, 78, 0, 9, 1, 8, 1, 7, 1, 8, 1, 9, 1, 8, 1, 7, 1, 1, 1, 65, 0, 7, 1, 9, 1, 65, 0, 9, 1, 75, 0, 75, 0, 72, 0, 79, 0, 163, 0, 65, 0, 75, 0, 167, 0, 163, 0, 75, 0, 163, 0, 167, 0, 171, 0, 163, 0, 171, 0, 181, 0, 181, 0, 171, 0, 174, 0, 163, 0, 181, 0, 176, 0, 163, 0, 176, 0, 136, 0, 65, 0, 163, 0, 136, 0, 176, 0, 139, 0, 136, 0, 136, 0, 139, 0, 143, 0, 136, 0, 143, 0, 153, 0, 153, 0, 143, 0, 146, 0, 136, 0, 153, 0, 149, 0, 65, 0, 136, 0, 4, 1, 4, 1, 136, 0, 149, 0, 53, 0, 4, 1, 149, 0, 4, 1, 53, 0, 57, 0, 4, 1, 7, 1, 65, 0, 57, 0, 5, 1, 4, 1, 4, 1, 3, 1, 7, 1, 3, 1, 4, 1, 5, 1, 1, 1, 7, 1, 3, 1, 44, 0, 3, 1, 5, 1, 1, 1, 3, 1, 254, 0, 254, 0, 0, 1, 1, 1, 253, 0, 3, 1, 44, 0, 253, 0, 254, 0, 3, 1, 44, 0, 6, 1, 54, 0, 54, 0, 51, 0, 58, 0, 44, 0, 54, 0, 137, 0, 253, 0, 44, 0, 137, 0, 54, 0, 150, 0, 137, 0, 137, 0, 150, 0, 154, 0, 137, 0, 154, 0, 161, 0, 161, 0, 154, 0, 147, 0, 137, 0, 161, 0, 157, 0, 137, 0, 157, 0, 87, 0, 253, 0, 137, 0, 87, 0, 157, 0, 98, 0, 87, 0, 87, 0, 98, 0, 102, 0, 87, 0, 102, 0, 109, 0, 109, 0, 102, 0, 95, 0, 87, 0, 109, 0, 105, 0, 87, 0, 105, 0, 253, 0, 105, 0, 5, 0, 253, 0, 253, 0, 5, 0, 9, 0, 253, 0, 9, 0, 255, 0, 254, 0, 253, 0, 255, 0, 0, 0, 254, 0, 255, 0, 0, 0, 255, 0, 4, 0, 254, 0, 0, 0, 23, 0, 4, 0, 10, 0, 8, 0, 10, 1, 0, 0, 4, 0, 104, 0, 10, 1, 4, 0, 10, 1, 104, 0, 108, 0, 111, 0, 0, 0, 10, 1, 111, 0, 23, 0, 0, 0, 10, 1, 108, 0, 92, 0, 108, 0, 93, 0, 92, 0, 86, 0, 10, 1, 92, 0, 101, 0, 92, 0, 94, 0, 86, 0, 92, 0, 101, 0, 86, 0, 101, 0, 97, 0, 10, 1, 86, 0, 11, 1, 10, 1, 11, 1, 111, 0, 135, 0, 86, 0, 97, 0, 156, 0, 135, 0, 97, 0, 135, 0, 156, 0, 160, 0, 160, 0, 145, 0, 144, 0, 140, 0, 160, 0, 144, 0, 135, 0, 160, 0, 140, 0, 164, 0, 86, 0, 135, 0, 164, 0, 135, 0, 140, 0, 164, 0, 11, 1, 86, 0, 177, 0, 164, 0, 140, 0, 164, 0, 177, 0, 180, 0, 164, 0, 180, 0, 188, 0, 188, 0, 180, 0, 173, 0, 164, 0, 188, 0, 184, 0, 11, 1, 164, 0, 184, 0, 130, 0, 11, 1, 184, 0, 11, 1, 130, 0, 134, 0, 11, 1, 134, 0, 117, 0, 134, 0, 120, 0, 117, 0, 111, 0, 11, 1, 117, 0, 111, 0, 117, 0, 127, 0, 127, 0, 117, 0, 119, 0, 111, 0, 127, 0, 123, 0, 23, 0, 111, 0, 123, 0, 27, 0, 23, 0, 123, 0, 23, 0, 27, 0, 31, 0, 23, 0, 31, 0, 2, 1, 1, 1, 23, 0, 2, 1), 0.0210464, PackedByteArray(246, 0, 66, 0, 233, 0, 129, 0, 246, 0, 118, 0, 246, 0, 129, 0, 252, 0, 246, 0, 252, 0, 66, 0, 252, 0, 172, 0, 166, 0, 252, 0, 166, 0, 66, 0, 166, 0, 74, 0, 66, 0, 74, 0, 239, 0, 66, 0, 66, 0, 230, 0, 233, 0, 66, 0, 240, 0, 230, 0, 230, 0, 238, 0, 234, 0, 230, 0, 234, 0, 227, 0, 227, 0, 232, 0, 230, 0, 237, 0, 236, 0, 239, 0, 237, 0, 239, 0, 65, 0, 65, 0, 239, 0, 75, 0, 250, 0, 65, 0, 75, 0, 65, 0, 247, 0, 237, 0, 65, 0, 250, 0, 247, 0, 237, 0, 247, 0, 149, 0, 149, 0, 247, 0, 146, 0, 250, 0, 176, 0, 247, 0, 176, 0, 250, 0, 174, 0, 227, 0, 228, 0, 231, 0, 111, 0, 231, 0, 228, 0, 111, 0, 228, 0, 242, 0, 231, 0, 111, 0, 123, 0, 111, 0, 245, 0, 123, 0, 241, 0, 245, 0, 111, 0, 123, 0, 244, 0, 119, 0, 104, 0, 93, 0, 241, 0, 104, 0, 241, 0, 4, 0, 4, 0, 2, 0, 10, 0, 97, 0, 241, 0, 94, 0, 86, 0, 241, 0, 97, 0, 241, 0, 86, 0, 244, 0, 249, 0, 86, 0, 97, 0, 130, 0, 120, 0, 244, 0, 130, 0, 244, 0, 184, 0, 251, 0, 244, 0, 86, 0, 244, 0, 251, 0, 184, 0, 251, 0, 86, 0, 249, 0, 184, 0, 251, 0, 173, 0, 251, 0, 249, 0, 140, 0, 249, 0, 145, 0, 140, 0, 1, 0, 248, 0, 243, 0, 248, 0, 157, 0, 243, 0, 157, 0, 248, 0, 147, 0, 243, 0, 105, 0, 1, 0, 105, 0, 243, 0, 95, 0, 105, 0, 5, 0, 1, 0, 1, 0, 5, 0, 229, 0, 227, 0, 1, 0, 229, 0, 1, 0, 227, 0, 235, 0, 1, 0, 44, 0, 248, 0, 1, 0, 235, 0, 44, 0, 44, 0, 54, 0, 248, 0, 44, 0, 235, 0, 54, 0, 24, 0, 27, 0, 32, 0, 32, 0, 26, 0, 122, 0), 0.215511, PackedByteArray(193, 0, 206, 0, 195, 0, 206, 0, 205, 0, 195, 0, 193, 0, 226, 0, 206, 0, 226, 0, 222, 0, 206, 0, 226, 0, 193, 0, 216, 0, 225, 0, 214, 0, 212, 0, 209, 0, 212, 0, 214, 0, 212, 0, 219, 0, 225, 0, 214, 0, 225, 0, 184, 0, 184, 0, 225, 0, 173, 0, 184, 0, 218, 0, 214, 0, 212, 0, 208, 0, 94, 0, 4, 0, 210, 0, 208, 0, 92, 0, 10, 0, 4, 0, 208, 0, 215, 0, 194, 0, 194, 0, 215, 0, 217, 0, 194, 0, 191, 0, 208, 0, 191, 0, 194, 0, 197, 0, 207, 0, 224, 0, 201, 0, 224, 0, 207, 0, 223, 0, 201, 0, 224, 0, 220, 0, 201, 0, 204, 0, 207, 0, 201, 0, 199, 0, 204, 0, 189, 0, 202, 0, 213, 0, 213, 0, 202, 0, 221, 0, 189, 0, 213, 0, 211, 0, 189, 0, 200, 0, 202, 0, 189, 0, 192, 0, 200, 0, 196, 0, 198, 0, 190, 0, 196, 0, 203, 0, 198, 0)],
"material": SubResource("StandardMaterial3D_hl3uc"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 268,
"vertex_data": PackedByteArray(177, 248, 177, 248, 0, 0, 251, 255, 177, 248, 255, 255, 77, 7, 60, 253, 255, 255, 177, 248, 77, 7, 253, 217, 177, 248, 125, 251, 142, 0, 149, 247, 177, 248, 219, 253, 35, 2, 116, 236, 177, 248, 219, 253, 35, 2, 136, 252, 125, 251, 177, 248, 142, 0, 190, 247, 143, 251, 143, 251, 58, 1, 165, 255, 105, 251, 124, 253, 130, 2, 141, 244, 105, 251, 124, 253, 130, 2, 227, 243, 232, 252, 232, 252, 22, 3, 26, 251, 125, 251, 112, 255, 77, 7, 83, 245, 219, 253, 219, 253, 77, 7, 190, 234, 177, 248, 112, 255, 129, 4, 49, 253, 143, 251, 196, 254, 111, 4, 176, 245, 124, 253, 124, 253, 149, 4, 182, 239, 124, 253, 124, 253, 149, 4, 8, 236, 112, 255, 177, 248, 129, 4, 53, 225, 219, 253, 177, 248, 35, 2, 57, 236, 112, 255, 125, 251, 77, 7, 207, 224, 196, 254, 143, 251, 111, 4, 78, 231, 124, 253, 105, 251, 130, 2, 65, 243, 177, 248, 0, 0, 77, 7, 48, 196, 177, 248, 77, 7, 0, 0, 49, 250, 255, 255, 77, 7, 77, 7, 142, 213, 177, 248, 142, 0, 129, 4, 242, 207, 177, 248, 35, 2, 35, 2, 8, 224, 177, 248, 35, 2, 35, 2, 243, 230, 125, 251, 142, 0, 77, 7, 102, 197, 143, 251, 58, 1, 111, 4, 132, 208, 105, 251, 130, 2, 130, 2, 80, 223, 105, 251, 130, 2, 130, 2, 228, 223, 232, 252, 22, 3, 22, 3, 248, 218, 125, 251, 77, 7, 142, 0, 223, 241, 219, 253, 77, 7, 35, 2, 34, 231, 177, 248, 129, 4, 142, 0, 248, 241, 143, 251, 111, 4, 58, 1, 189, 233, 124, 253, 149, 4, 130, 2, 122, 225, 112, 255, 129, 4, 77, 7, 191, 207, 219, 253, 35, 2, 77, 7, 61, 201, 112, 255, 77, 7, 129, 4, 230, 220, 196, 254, 111, 4, 111, 4, 254, 215, 124, 253, 130, 2, 149, 4, 52, 210, 255, 255, 177, 248, 177, 248, 148, 213, 177, 248, 255, 255, 177, 248, 241, 252, 177, 248, 177, 248, 255, 255, 255, 191, 112, 255, 125, 251, 177, 248, 92, 221, 219, 253, 219, 253, 177, 248, 162, 232, 112, 255, 177, 248, 125, 251, 131, 207, 196, 254, 143, 251, 143, 251, 44, 217, 124, 253, 124, 253, 105, 251, 22, 230, 232, 252, 232, 252, 232, 252, 79, 221, 177, 248, 112, 255, 125, 251, 252, 252, 177, 248, 219, 253, 219, 253, 89, 197, 177, 248, 219, 253, 219, 253, 143, 254, 125, 251, 112, 255, 177, 248, 61, 244, 143, 251, 196, 254, 143, 251, 184, 242, 105, 251, 124, 253, 124, 253, 215, 196, 105, 251, 124, 253, 124, 253, 146, 243, 125, 251, 177, 248, 112, 255, 123, 193, 219, 253, 177, 248, 219, 253, 99, 199, 177, 248, 125, 251, 112, 255, 225, 192, 143, 251, 143, 251, 196, 254, 223, 194, 124, 253, 105, 251, 124, 253, 185, 208, 255, 255, 77, 7, 177, 248, 130, 208, 177, 248, 77, 7, 255, 255, 104, 192, 177, 248, 0, 0, 177, 248, 3, 188, 112, 255, 77, 7, 125, 251, 65, 202, 219, 253, 77, 7, 219, 253, 182, 196, 112, 255, 129, 4, 177, 248, 243, 201, 196, 254, 111, 4, 143, 251, 21, 195, 124, 253, 149, 4, 124, 253, 249, 193, 232, 252, 22, 3, 232, 252, 244, 183, 177, 248, 129, 4, 112, 255, 95, 194, 177, 248, 35, 2, 219, 253, 52, 160, 177, 248, 35, 2, 219, 253, 190, 198, 125, 251, 77, 7, 112, 255, 196, 193, 143, 251, 111, 4, 196, 254, 45, 196, 105, 251, 130, 2, 124, 253, 23, 163, 105, 251, 130, 2, 124, 253, 229, 200, 125, 251, 142, 0, 177, 248, 174, 189, 219, 253, 35, 2, 177, 248, 196, 194, 177, 248, 142, 0, 125, 251, 69, 176, 143, 251, 58, 1, 143, 251, 146, 178, 124, 253, 130, 2, 105, 251, 77, 186, 77, 7, 177, 248, 0, 0, 49, 250, 0, 0, 177, 248, 77, 7, 142, 213, 77, 7, 255, 255, 77, 7, 60, 253, 129, 4, 177, 248, 142, 0, 223, 241, 35, 2, 177, 248, 35, 2, 34, 231, 77, 7, 125, 251, 142, 0, 248, 241, 111, 4, 143, 251, 58, 1, 190, 233, 130, 2, 105, 251, 130, 2, 204, 225, 22, 3, 232, 252, 22, 3, 25, 217, 22, 3, 232, 252, 22, 3, 42, 221, 22, 3, 232, 252, 22, 3, 233, 235, 142, 0, 125, 251, 77, 7, 191, 207, 35, 2, 219, 253, 77, 7, 255, 200, 35, 2, 219, 253, 77, 7, 67, 234, 142, 0, 177, 248, 129, 4, 230, 220, 58, 1, 143, 251, 111, 4, 222, 216, 130, 2, 124, 253, 149, 4, 149, 209, 130, 2, 124, 253, 149, 4, 8, 236, 77, 7, 112, 255, 129, 4, 49, 253, 77, 7, 219, 253, 35, 2, 243, 230, 77, 7, 219, 253, 35, 2, 136, 252, 129, 4, 112, 255, 77, 7, 83, 245, 111, 4, 196, 254, 111, 4, 176, 245, 149, 4, 124, 253, 130, 2, 231, 223, 149, 4, 124, 253, 130, 2, 227, 243, 0, 0, 77, 7, 77, 7, 253, 217, 77, 7, 77, 7, 0, 0, 251, 255, 77, 7, 0, 0, 77, 7, 48, 196, 142, 0, 77, 7, 129, 4, 53, 225, 35, 2, 77, 7, 35, 2, 57, 236, 142, 0, 129, 4, 77, 7, 207, 224, 58, 1, 111, 4, 111, 4, 78, 231, 130, 2, 149, 4, 130, 2, 23, 246, 22, 3, 22, 3, 22, 3, 114, 219, 22, 3, 22, 3, 22, 3, 23, 249, 22, 3, 22, 3, 22, 3, 150, 247, 77, 7, 129, 4, 142, 0, 149, 247, 77, 7, 35, 2, 35, 2, 8, 224, 77, 7, 35, 2, 35, 2, 116, 236, 129, 4, 77, 7, 142, 0, 190, 247, 111, 4, 111, 4, 58, 1, 151, 254, 149, 4, 130, 2, 130, 2, 81, 222, 149, 4, 130, 2, 130, 2, 51, 244, 129, 4, 142, 0, 77, 7, 102, 197, 35, 2, 35, 2, 77, 7, 124, 201, 35, 2, 35, 2, 77, 7, 57, 235, 77, 7, 142, 0, 129, 4, 242, 207, 111, 4, 58, 1, 111, 4, 50, 208, 130, 2, 130, 2, 149, 4, 222, 210, 130, 2, 130, 2, 149, 4, 182, 239, 0, 0, 177, 248, 177, 248, 130, 208, 77, 7, 177, 248, 255, 255, 104, 192, 77, 7, 255, 255, 177, 248, 241, 252, 142, 0, 177, 248, 125, 251, 65, 202, 35, 2, 177, 248, 219, 253, 182, 196, 35, 2, 177, 248, 219, 253, 182, 196, 142, 0, 125, 251, 177, 248, 243, 201, 58, 1, 143, 251, 143, 251, 21, 195, 130, 2, 105, 251, 124, 253, 155, 208, 130, 2, 105, 251, 124, 253, 104, 192, 22, 3, 232, 252, 232, 252, 143, 186, 22, 3, 232, 252, 232, 252, 44, 213, 22, 3, 232, 252, 232, 252, 229, 223, 77, 7, 125, 251, 112, 255, 95, 194, 77, 7, 219, 253, 219, 253, 190, 198, 77, 7, 219, 253, 219, 253, 143, 254, 129, 4, 177, 248, 112, 255, 197, 193, 111, 4, 143, 251, 196, 254, 211, 199, 149, 4, 124, 253, 124, 253, 170, 201, 149, 4, 124, 253, 124, 253, 14, 235, 129, 4, 112, 255, 177, 248, 61, 244, 35, 2, 219, 253, 177, 248, 15, 195, 35, 2, 219, 253, 177, 248, 42, 233, 77, 7, 112, 255, 125, 251, 252, 252, 111, 4, 196, 254, 143, 251, 176, 240, 130, 2, 124, 253, 105, 251, 247, 187, 130, 2, 124, 253, 105, 251, 12, 232, 77, 7, 0, 0, 177, 248, 3, 188, 77, 7, 77, 7, 255, 255, 255, 191, 0, 0, 77, 7, 177, 248, 148, 213, 77, 7, 142, 0, 125, 251, 69, 176, 77, 7, 35, 2, 219, 253, 52, 160, 77, 7, 35, 2, 219, 253, 89, 197, 129, 4, 142, 0, 177, 248, 174, 189, 111, 4, 58, 1, 143, 251, 146, 178, 149, 4, 130, 2, 124, 253, 23, 163, 149, 4, 130, 2, 124, 253, 197, 196, 22, 3, 22, 3, 232, 252, 191, 170, 22, 3, 22, 3, 232, 252, 154, 231, 22, 3, 22, 3, 232, 252, 11, 203, 129, 4, 77, 7, 112, 255, 123, 193, 35, 2, 77, 7, 219, 253, 99, 199, 35, 2, 77, 7, 219, 253, 99, 199, 77, 7, 129, 4, 112, 255, 225, 192, 111, 4, 111, 4, 196, 254, 193, 193, 130, 2, 149, 4, 124, 253, 218, 218, 130, 2, 149, 4, 124, 253, 36, 202, 142, 0, 129, 4, 177, 248, 92, 221, 35, 2, 35, 2, 177, 248, 123, 194, 35, 2, 35, 2, 177, 248, 27, 232, 142, 0, 77, 7, 125, 251, 131, 207, 58, 1, 111, 4, 143, 251, 24, 219, 130, 2, 130, 2, 105, 251, 195, 184, 130, 2, 130, 2, 105, 251, 34, 228, 177, 248, 219, 253, 35, 2, 28, 251, 232, 252, 232, 252, 22, 3, 223, 241, 232, 252, 232, 252, 22, 3, 255, 255, 232, 252, 232, 252, 22, 3, 31, 232, 177, 248, 35, 2, 35, 2, 151, 193, 177, 248, 35, 2, 35, 2, 49, 242, 232, 252, 22, 3, 22, 3, 50, 199, 232, 252, 22, 3, 22, 3, 216, 196, 232, 252, 22, 3, 22, 3, 160, 235, 232, 252, 232, 252, 232, 252, 137, 224, 232, 252, 232, 252, 232, 252, 218, 219, 232, 252, 232, 252, 232, 252, 19, 228, 177, 248, 219, 253, 219, 253, 157, 189, 177, 248, 219, 253, 219, 253, 255, 255, 232, 252, 22, 3, 232, 252, 199, 203, 232, 252, 22, 3, 232, 252, 253, 170, 232, 252, 22, 3, 232, 252, 62, 200, 177, 248, 35, 2, 219, 253, 119, 185, 177, 248, 35, 2, 219, 253, 211, 193, 130, 2, 105, 251, 130, 2, 131, 241, 130, 2, 105, 251, 130, 2, 182, 219, 22, 3, 232, 252, 22, 3, 91, 233, 22, 3, 232, 252, 22, 3, 233, 237, 35, 2, 219, 253, 77, 7, 144, 205, 35, 2, 219, 253, 77, 7, 227, 233, 130, 2, 149, 4, 130, 2, 157, 229, 130, 2, 149, 4, 130, 2, 167, 248, 22, 3, 22, 3, 22, 3, 164, 200, 22, 3, 22, 3, 22, 3, 232, 236, 22, 3, 22, 3, 22, 3, 12, 236, 22, 3, 232, 252, 232, 252, 156, 206, 22, 3, 232, 252, 232, 252, 89, 197, 22, 3, 232, 252, 232, 252, 108, 231, 22, 3, 22, 3, 232, 252, 214, 176, 22, 3, 22, 3, 232, 252, 193, 204, 35, 2, 77, 7, 219, 253, 223, 194, 35, 2, 77, 7, 219, 253, 193, 209, 35, 2, 35, 2, 177, 248, 168, 191, 255, 255, 177, 248, 77, 7, 221, 222, 177, 248, 219, 253, 35, 2, 255, 255, 232, 252, 232, 252, 22, 3, 13, 240, 255, 255, 77, 7, 77, 7, 16, 213, 177, 248, 35, 2, 35, 2, 150, 237, 177, 248, 35, 2, 35, 2, 127, 211, 232, 252, 22, 3, 22, 3, 6, 200, 232, 252, 232, 252, 232, 252, 113, 224, 232, 252, 232, 252, 232, 252, 87, 226, 232, 252, 232, 252, 232, 252, 219, 219, 177, 248, 219, 253, 219, 253, 2, 190, 232, 252, 22, 3, 232, 252, 161, 203, 232, 252, 22, 3, 232, 252, 69, 174, 232, 252, 22, 3, 232, 252, 149, 200, 130, 2, 105, 251, 130, 2, 141, 226, 130, 2, 105, 251, 130, 2, 169, 252, 35, 2, 219, 253, 77, 7, 168, 234, 130, 2, 149, 4, 130, 2, 175, 233, 130, 2, 149, 4, 130, 2, 255, 255, 77, 7, 35, 2, 35, 2, 183, 198, 35, 2, 177, 248, 219, 253, 150, 190, 77, 7, 219, 253, 219, 253, 255, 255, 35, 2, 219, 253, 177, 248, 67, 190, 77, 7, 35, 2, 219, 253, 148, 190, 35, 2, 77, 7, 219, 253, 58, 208, 35, 2, 35, 2, 177, 248, 19, 192, 177, 248, 255, 255, 77, 7, 109, 252, 255, 255, 177, 248, 77, 7, 145, 223, 232, 252, 232, 252, 22, 3, 4, 250, 177, 248, 77, 7, 0, 0, 144, 225, 255, 255, 77, 7, 77, 7, 152, 214, 232, 252, 22, 3, 22, 3, 15, 218, 255, 255, 177, 248, 177, 248, 35, 211, 177, 248, 177, 248, 255, 255, 205, 192, 232, 252, 232, 252, 232, 252, 210, 220, 232, 252, 232, 252, 232, 252, 226, 224, 255, 255, 77, 7, 177, 248, 238, 202, 177, 248, 0, 0, 177, 248, 76, 184, 232, 252, 22, 3, 232, 252, 148, 182, 77, 7, 177, 248, 0, 0, 195, 242, 0, 0, 77, 7, 77, 7, 180, 222, 20, 189, 196, 188, 122, 65, 79, 3, 149, 45, 50, 41, 127, 185, 105, 184, 120, 180, 112, 179, 77, 89, 180, 1, 207, 56, 5, 57, 69, 182, 143, 180, 206, 178, 217, 175, 140, 84, 6, 8, 4, 53, 174, 40, 214, 60, 8, 12, 6, 55, 202, 22, 164, 74, 82, 3, 251, 68, 138, 12, 86, 58, 9, 27, 121, 62, 231, 22, 77, 49, 25, 45, 4, 53, 8, 51, 175, 49, 70, 33, 121, 53, 5, 37, 4, 53, 244, 45, 231, 5, 192, 116, 43, 64, 216, 63, 131, 42, 11, 47, 63, 5, 221, 117, 74, 2, 43, 120, 99, 74, 80, 73, 97, 18, 237, 92, 14, 19, 66, 95, 149, 16, 76, 98, 235, 71, 146, 68, 145, 39, 86, 75, 193, 59, 137, 59, 4, 53, 20, 55, 255, 68, 220, 67, 22, 64, 240, 63, 16, 48, 255, 63, 168, 37, 58, 56, 150, 29, 109, 71, 133, 46, 219, 50, 100, 40, 151, 60, 16, 31, 255, 74, 135, 42, 111, 38, 90, 59, 0, 3, 61, 244, 7, 0, 43, 46, 229, 30, 250, 50, 29, 21, 50, 38, 191, 33, 40, 41, 199, 26, 55, 45, 186, 18, 65, 41, 215, 12, 31, 50, 25, 3, 89, 223, 160, 25, 78, 36, 9, 4, 155, 55, 5, 11, 171, 47, 22, 10, 68, 213, 138, 15, 109, 35, 229, 10, 222, 22, 104, 13, 227, 31, 129, 25, 44, 233, 187, 13, 168, 219, 11, 0, 167, 36, 53, 18, 216, 39, 22, 44, 7, 0, 75, 244, 217, 5, 173, 115, 14, 35, 173, 39, 11, 26, 143, 32, 145, 35, 39, 53, 168, 30, 251, 48, 158, 17, 186, 42, 187, 15, 136, 56, 140, 13, 123, 233, 76, 12, 155, 110, 89, 25, 179, 223, 95, 13, 207, 22, 146, 0, 63, 220, 207, 30, 59, 79, 20, 15, 168, 214, 232, 17, 93, 90, 96, 28, 129, 68, 3, 7, 114, 113, 163, 19, 85, 85, 127, 26, 248, 63, 211, 191, 38, 192, 123, 213, 243, 208, 175, 124, 122, 193, 61, 196, 117, 196, 250, 202, 234, 200, 255, 186, 34, 188, 198, 190, 208, 192, 80, 209, 53, 191, 23, 195, 143, 190, 164, 227, 116, 175, 124, 110, 47, 204, 86, 218, 196, 199, 180, 225, 183, 184, 159, 105, 113, 183, 121, 209, 35, 205, 163, 217, 124, 194, 151, 222, 199, 181, 24, 105, 120, 190, 172, 124, 164, 202, 155, 181, 174, 182, 75, 126, 77, 217, 247, 115, 213, 188, 116, 115, 250, 196, 0, 184, 118, 187, 248, 119, 140, 212, 105, 210, 204, 214, 235, 66, 58, 67, 62, 11, 231, 133, 177, 206, 229, 210, 250, 202, 246, 204, 79, 206, 184, 222, 133, 202, 249, 218, 194, 207, 240, 205, 88, 51, 188, 164, 95, 72, 235, 89, 96, 198, 234, 219, 126, 70, 149, 71, 211, 7, 74, 130, 134, 75, 142, 76, 47, 199, 249, 198, 41, 71, 0, 78, 229, 34, 252, 154, 153, 76, 167, 80, 17, 35, 96, 146, 106, 56, 226, 156, 98, 201, 201, 232, 34, 10, 62, 133, 147, 33, 226, 149, 56, 52, 188, 156, 168, 197, 245, 228, 39, 216, 232, 211, 247, 255, 179, 11, 255, 124, 89, 187, 240, 220, 81, 216, 243, 229, 111, 223, 243, 229, 111, 223, 109, 220, 215, 202, 86, 225, 3, 207, 54, 248, 70, 213, 49, 237, 67, 213, 26, 243, 231, 199, 118, 247, 144, 45, 233, 113, 2, 168, 114, 242, 131, 22, 165, 230, 74, 32, 246, 123, 78, 164, 159, 242, 47, 233, 207, 253, 34, 34, 149, 240, 214, 40, 243, 120, 255, 165, 249, 116, 155, 183, 80, 228, 102, 187, 127, 106, 151, 178, 230, 124, 30, 178, 63, 119, 145, 176, 139, 232, 7, 192, 38, 108, 24, 172, 82, 12, 217, 133, 193, 11, 247, 255, 119, 213, 143, 217, 141, 14, 3, 135, 100, 17, 75, 140, 165, 32, 94, 230, 162, 37, 232, 145, 169, 42, 162, 147, 196, 48, 207, 158, 186, 42, 104, 240, 24, 68, 130, 165, 108, 219, 120, 238, 197, 203, 31, 254, 32, 233, 150, 242, 27, 224, 125, 230, 27, 224, 125, 230, 210, 22, 67, 242, 146, 36, 105, 255, 124, 224, 88, 234, 117, 215, 97, 240, 211, 209, 25, 225, 148, 59, 18, 157, 161, 204, 68, 235, 204, 217, 63, 222, 42, 216, 83, 228, 6, 64, 165, 157, 161, 209, 105, 238, 87, 81, 119, 3, 42, 34, 50, 50, 27, 61, 62, 164, 230, 233, 5, 3, 37, 1, 57, 119, 45, 101, 9, 53, 101, 22, 117, 80, 178, 50, 143, 44, 133, 69, 198, 54, 170, 50, 225, 42, 168, 32, 95, 20, 133, 45, 229, 5, 197, 41, 77, 7, 124, 113, 123, 9, 61, 38, 166, 49, 92, 13, 57, 23, 65, 9, 99, 76, 49, 13, 32, 114, 231, 28, 167, 240, 49, 214, 206, 173, 234, 201, 120, 205, 139, 195, 97, 175, 253, 127, 67, 180, 0, 209, 102, 200, 88, 129, 87, 137, 148, 216, 186, 212, 94, 77, 63, 64, 43, 34, 234, 134, 55, 78, 254, 74, 177, 210, 224, 225, 1, 220, 80, 206, 31, 219, 216, 4, 149, 129, 155, 145, 91, 42, 226, 143, 16, 28, 210, 239, 56, 5, 196, 248, 36, 212, 151, 221, 190, 39, 111, 130, 180, 51, 27, 37, 214, 103, 131, 196, 29, 211, 152, 8, 164, 38, 85, 52, 140, 96, 160, 53, 241, 79, 254, 34, 211, 21, 165, 81, 101, 50, 200, 42, 103, 44, 66, 6, 142, 32, 87, 20, 219, 44, 55, 0, 68, 38, 112, 49, 167, 10, 96, 43, 91, 15, 3, 72, 147, 210, 29, 189, 221, 174, 55, 206, 73, 123, 106, 167, 189, 211, 204, 214, 130, 230, 202, 161, 136, 14, 168, 135, 125, 247, 26, 235, 126, 118, 111, 226, 99, 218, 152, 202, 252, 43, 160, 250, 129, 213, 96, 222, 57, 46, 240, 139, 121, 89, 35, 13, 24, 51, 1, 40, 55, 53, 204, 38, 107, 53, 70, 49, 232, 38, 206, 53, 122, 37, 17, 77, 29, 42, 229, 30, 146, 20, 30, 3, 128, 41, 98, 16, 174, 227, 62, 4, 4, 34, 59, 44, 242, 14, 102, 106, 76, 14, 2, 56, 119, 202, 79, 187, 220, 205, 144, 215)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_0dym6")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cw4lr"]
resource_local_to_scene = true
shading_mode = 0
vertex_color_use_as_albedo = true
albedo_texture = ExtResource("8_jexhf")
heightmap_enabled = true
heightmap_scale = -0.655
grow = true
grow_amount = 0.04

[sub_resource type="ShaderMaterial" id="ShaderMaterial_r0weg"]
render_priority = 0
shader = ExtResource("3_tjup3")

[sub_resource type="QuadMesh" id="QuadMesh_k0tpp"]
size = Vector2(3.12, 3.145)

[sub_resource type="Gradient" id="Gradient_d7a0i"]
offsets = PackedFloat32Array(0, 0.504854, 1)
colors = PackedColorArray(0, 0, 0, 0.470588, 1, 1, 1, 1, 0, 0, 0, 0.470588)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_cg4at"]
render_priority = 0
shader = ExtResource("3_tjup3")

[sub_resource type="QuadMesh" id="QuadMesh_mkmp3"]
size = Vector2(3.12, 3.145)

[sub_resource type="Gradient" id="Gradient_0u1n2"]
offsets = PackedFloat32Array(0, 0.504854, 1)
colors = PackedColorArray(0.694118, 0.694118, 0.694118, 0.709804, 1, 1, 1, 0.32549, 0.694118, 0.694118, 0.694118, 0.709804)

[sub_resource type="QuadMesh" id="QuadMesh_717bq"]
size = Vector2(3.12, 3.145)

[sub_resource type="Gradient" id="Gradient_etra5"]
offsets = PackedFloat32Array(0, 0.504854, 1)
colors = PackedColorArray(0.694118, 0.694118, 0.694118, 0.898039, 1, 1, 1, 0.32549, 0.694118, 0.694118, 0.694118, 0.901961)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_kt210"]

[sub_resource type="ArrayMesh" id="ArrayMesh_qs3xa"]
_surfaces = [{
"aabb": AABB(-0.0264038, -2.21294, -1.76026, 1.02241e-05, 4.03382, 3.52053),
"format": 34896613377,
"index_count": 384,
"index_data": PackedByteArray(80, 0, 3, 0, 27, 0, 80, 0, 44, 0, 3, 0, 79, 0, 7, 0, 33, 0, 79, 0, 40, 0, 7, 0, 78, 0, 8, 0, 34, 0, 78, 0, 47, 0, 8, 0, 77, 0, 6, 0, 31, 0, 77, 0, 48, 0, 6, 0, 76, 0, 20, 0, 49, 0, 76, 0, 36, 0, 20, 0, 75, 0, 21, 0, 50, 0, 75, 0, 51, 0, 21, 0, 74, 0, 15, 0, 43, 0, 74, 0, 52, 0, 15, 0, 73, 0, 19, 0, 53, 0, 73, 0, 35, 0, 19, 0, 72, 0, 22, 0, 54, 0, 72, 0, 55, 0, 22, 0, 71, 0, 18, 0, 46, 0, 71, 0, 56, 0, 18, 0, 70, 0, 12, 0, 57, 0, 70, 0, 28, 0, 12, 0, 69, 0, 23, 0, 58, 0, 69, 0, 59, 0, 23, 0, 68, 0, 17, 0, 45, 0, 68, 0, 60, 0, 17, 0, 67, 0, 16, 0, 61, 0, 67, 0, 32, 0, 16, 0, 66, 0, 24, 0, 62, 0, 66, 0, 63, 0, 24, 0, 65, 0, 11, 0, 39, 0, 65, 0, 64, 0, 11, 0, 62, 0, 64, 0, 65, 0, 62, 0, 24, 0, 64, 0, 20, 0, 65, 0, 48, 0, 20, 0, 62, 0, 65, 0, 48, 0, 39, 0, 6, 0, 48, 0, 65, 0, 39, 0, 45, 0, 63, 0, 66, 0, 45, 0, 17, 0, 63, 0, 8, 0, 66, 0, 36, 0, 8, 0, 45, 0, 66, 0, 36, 0, 62, 0, 20, 0, 36, 0, 66, 0, 62, 0, 33, 0, 32, 0, 67, 0, 33, 0, 7, 0, 32, 0, 17, 0, 67, 0, 63, 0, 17, 0, 33, 0, 67, 0, 63, 0, 61, 0, 24, 0, 63, 0, 67, 0, 61, 0, 58, 0, 60, 0, 68, 0, 58, 0, 23, 0, 60, 0, 19, 0, 68, 0, 47, 0, 19, 0, 58, 0, 68, 0, 47, 0, 45, 0, 8, 0, 47, 0, 68, 0, 45, 0, 29, 0, 59, 0, 69, 0, 29, 0, 13, 0, 59, 0, 4, 0, 69, 0, 35, 0, 4, 0, 29, 0, 69, 0, 35, 0, 58, 0, 19, 0, 35, 0, 69, 0, 58, 0, 41, 0, 28, 0, 70, 0, 41, 0, 2, 0, 28, 0, 13, 0, 70, 0, 59, 0, 13, 0, 41, 0, 70, 0, 59, 0, 57, 0, 23, 0, 59, 0, 70, 0, 57, 0, 54, 0, 56, 0, 71, 0, 54, 0, 22, 0, 56, 0, 14, 0, 71, 0, 30, 0, 14, 0, 54, 0, 71, 0, 30, 0, 46, 0, 5, 0, 30, 0, 71, 0, 46, 0, 25, 0, 55, 0, 72, 0, 25, 0, 9, 0, 55, 0, 0, 0, 72, 0, 42, 0, 0, 0, 25, 0, 72, 0, 42, 0, 54, 0, 14, 0, 42, 0, 72, 0, 54, 0, 37, 0, 35, 0, 73, 0, 37, 0, 4, 0, 35, 0, 9, 0, 73, 0, 55, 0, 9, 0, 37, 0, 73, 0, 55, 0, 53, 0, 22, 0, 55, 0, 73, 0, 53, 0, 50, 0, 52, 0, 74, 0, 50, 0, 21, 0, 52, 0, 10, 0, 74, 0, 26, 0, 10, 0, 50, 0, 74, 0, 26, 0, 43, 0, 1, 0, 26, 0, 74, 0, 43, 0, 46, 0, 51, 0, 75, 0, 46, 0, 18, 0, 51, 0, 5, 0, 75, 0, 38, 0, 5, 0, 46, 0, 75, 0, 38, 0, 50, 0, 10, 0, 38, 0, 75, 0, 50, 0, 34, 0, 36, 0, 76, 0, 34, 0, 8, 0, 36, 0, 18, 0, 76, 0, 51, 0, 18, 0, 34, 0, 76, 0, 51, 0, 49, 0, 21, 0, 51, 0, 76, 0, 49, 0, 49, 0, 48, 0, 77, 0, 49, 0, 20, 0, 48, 0, 21, 0, 77, 0, 52, 0, 21, 0, 49, 0, 77, 0, 52, 0, 31, 0, 15, 0, 52, 0, 77, 0, 31, 0, 53, 0, 47, 0, 78, 0, 53, 0, 19, 0, 47, 0, 22, 0, 78, 0, 56, 0, 22, 0, 53, 0, 78, 0, 56, 0, 34, 0, 18, 0, 56, 0, 78, 0, 34, 0, 57, 0, 40, 0, 79, 0, 57, 0, 12, 0, 40, 0, 23, 0, 79, 0, 60, 0, 23, 0, 57, 0, 79, 0, 60, 0, 33, 0, 17, 0, 60, 0, 79, 0, 33, 0, 61, 0, 44, 0, 80, 0, 61, 0, 16, 0, 44, 0, 24, 0, 80, 0, 64, 0, 24, 0, 61, 0, 80, 0, 64, 0, 27, 0, 11, 0, 64, 0, 80, 0, 27, 0),
"lods": [1.11759e-08, PackedByteArray(8, 0, 45, 0, 24, 0, 45, 0, 44, 0, 24, 0, 24, 0, 44, 0, 3, 0, 45, 0, 16, 0, 44, 0, 24, 0, 3, 0, 62, 0, 8, 0, 24, 0, 62, 0, 62, 0, 3, 0, 27, 0, 62, 0, 27, 0, 11, 0, 8, 0, 62, 0, 11, 0, 17, 0, 16, 0, 45, 0, 17, 0, 32, 0, 16, 0, 47, 0, 17, 0, 45, 0, 47, 0, 45, 0, 8, 0, 58, 0, 32, 0, 17, 0, 58, 0, 7, 0, 32, 0, 19, 0, 17, 0, 47, 0, 19, 0, 58, 0, 17, 0, 23, 0, 7, 0, 58, 0, 23, 0, 40, 0, 7, 0, 4, 0, 23, 0, 58, 0, 4, 0, 58, 0, 19, 0, 29, 0, 40, 0, 23, 0, 4, 0, 29, 0, 23, 0, 29, 0, 12, 0, 40, 0, 37, 0, 4, 0, 19, 0, 13, 0, 12, 0, 29, 0, 9, 0, 37, 0, 19, 0, 13, 0, 41, 0, 12, 0, 41, 0, 28, 0, 12, 0, 41, 0, 2, 0, 28, 0, 9, 0, 19, 0, 25, 0, 25, 0, 19, 0, 47, 0, 25, 0, 47, 0, 22, 0, 0, 0, 25, 0, 22, 0, 22, 0, 47, 0, 8, 0, 0, 0, 22, 0, 42, 0, 42, 0, 22, 0, 54, 0, 22, 0, 8, 0, 54, 0, 42, 0, 54, 0, 14, 0, 54, 0, 8, 0, 18, 0, 14, 0, 54, 0, 18, 0, 14, 0, 18, 0, 30, 0, 18, 0, 8, 0, 11, 0, 30, 0, 18, 0, 5, 0, 18, 0, 11, 0, 49, 0, 5, 0, 18, 0, 49, 0, 49, 0, 11, 0, 39, 0, 49, 0, 39, 0, 6, 0, 5, 0, 49, 0, 21, 0, 21, 0, 49, 0, 6, 0, 5, 0, 21, 0, 38, 0, 21, 0, 6, 0, 50, 0, 38, 0, 21, 0, 50, 0, 50, 0, 6, 0, 31, 0, 38, 0, 50, 0, 10, 0, 50, 0, 31, 0, 15, 0, 10, 0, 50, 0, 15, 0, 10, 0, 15, 0, 26, 0, 26, 0, 15, 0, 43, 0, 26, 0, 43, 0, 1, 0), 1.34317e-08, PackedByteArray(11, 0, 16, 0, 27, 0, 16, 0, 3, 0, 27, 0, 16, 0, 44, 0, 3, 0, 4, 0, 16, 0, 11, 0, 4, 0, 32, 0, 16, 0, 4, 0, 7, 0, 32, 0, 29, 0, 7, 0, 4, 0, 29, 0, 40, 0, 7, 0, 29, 0, 12, 0, 40, 0, 13, 0, 12, 0, 29, 0, 13, 0, 41, 0, 12, 0, 41, 0, 28, 0, 12, 0, 41, 0, 2, 0, 28, 0, 9, 0, 37, 0, 4, 0, 9, 0, 4, 0, 25, 0, 25, 0, 4, 0, 11, 0, 25, 0, 11, 0, 30, 0, 42, 0, 25, 0, 30, 0, 0, 0, 25, 0, 42, 0, 42, 0, 30, 0, 14, 0, 30, 0, 11, 0, 5, 0, 5, 0, 11, 0, 39, 0, 5, 0, 39, 0, 6, 0, 5, 0, 6, 0, 15, 0, 15, 0, 6, 0, 31, 0, 38, 0, 5, 0, 15, 0, 38, 0, 15, 0, 10, 0, 10, 0, 15, 0, 26, 0, 26, 0, 15, 0, 43, 0, 26, 0, 43, 0, 1, 0)],
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 81,
"vertex_data": PackedByteArray(152, 5, 254, 255, 254, 255, 0, 0, 0, 0, 0, 0, 254, 255, 0, 0, 152, 5, 254, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 152, 5, 254, 255, 255, 127, 0, 0, 204, 2, 255, 127, 254, 255, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 204, 2, 255, 127, 0, 0, 0, 0, 204, 2, 255, 127, 255, 127, 0, 0, 152, 5, 254, 255, 255, 191, 0, 0, 126, 1, 255, 63, 254, 255, 0, 0, 0, 0, 0, 0, 255, 63, 0, 0, 26, 4, 255, 191, 0, 0, 0, 0, 152, 5, 254, 255, 255, 63, 0, 0, 26, 4, 255, 191, 254, 255, 0, 0, 0, 0, 0, 0, 255, 191, 0, 0, 126, 1, 255, 63, 0, 0, 0, 0, 204, 2, 255, 127, 255, 63, 0, 0, 204, 2, 255, 127, 255, 191, 0, 0, 26, 4, 255, 191, 255, 127, 0, 0, 126, 1, 255, 63, 255, 127, 0, 0, 126, 1, 255, 63, 255, 191, 0, 0, 26, 4, 255, 191, 255, 191, 0, 0, 26, 4, 255, 191, 255, 63, 0, 0, 126, 1, 255, 63, 255, 63, 0, 0, 152, 5, 254, 255, 254, 223, 0, 0, 191, 0, 255, 31, 254, 255, 0, 0, 0, 0, 0, 0, 255, 31, 0, 0, 217, 4, 254, 223, 0, 0, 0, 0, 152, 5, 254, 255, 255, 95, 0, 0, 139, 3, 255, 159, 254, 255, 0, 0, 0, 0, 0, 0, 255, 159, 0, 0, 13, 2, 255, 95, 0, 0, 0, 0, 204, 2, 255, 127, 255, 31, 0, 0, 204, 2, 255, 127, 255, 159, 0, 0, 217, 4, 254, 223, 255, 127, 0, 0, 13, 2, 255, 95, 255, 127, 0, 0, 152, 5, 254, 255, 255, 159, 0, 0, 13, 2, 255, 95, 254, 255, 0, 0, 0, 0, 0, 0, 255, 95, 0, 0, 139, 3, 255, 159, 0, 0, 0, 0, 152, 5, 254, 255, 255, 31, 0, 0, 217, 4, 254, 223, 254, 255, 0, 0, 0, 0, 0, 0, 254, 223, 0, 0, 191, 0, 255, 31, 0, 0, 0, 0, 204, 2, 255, 127, 255, 95, 0, 0, 204, 2, 255, 127, 254, 223, 0, 0, 139, 3, 255, 159, 255, 127, 0, 0, 191, 0, 255, 31, 255, 127, 0, 0, 126, 1, 255, 63, 255, 159, 0, 0, 126, 1, 255, 63, 254, 223, 0, 0, 13, 2, 255, 95, 255, 191, 0, 0, 191, 0, 255, 31, 255, 191, 0, 0, 26, 4, 255, 191, 255, 159, 0, 0, 26, 4, 255, 191, 254, 223, 0, 0, 217, 4, 254, 223, 255, 191, 0, 0, 139, 3, 255, 159, 255, 191, 0, 0, 26, 4, 255, 191, 255, 31, 0, 0, 26, 4, 255, 191, 255, 95, 0, 0, 217, 4, 254, 223, 255, 63, 0, 0, 139, 3, 255, 159, 255, 63, 0, 0, 126, 1, 255, 63, 255, 31, 0, 0, 126, 1, 255, 63, 255, 95, 0, 0, 13, 2, 255, 95, 255, 63, 0, 0, 191, 0, 255, 31, 255, 63, 0, 0, 191, 0, 255, 31, 255, 95, 0, 0, 13, 2, 255, 95, 255, 95, 0, 0, 13, 2, 255, 95, 255, 31, 0, 0, 139, 3, 255, 159, 255, 95, 0, 0, 217, 4, 254, 223, 255, 95, 0, 0, 217, 4, 254, 223, 255, 31, 0, 0, 139, 3, 255, 159, 254, 223, 0, 0, 217, 4, 254, 223, 254, 223, 0, 0, 217, 4, 254, 223, 255, 159, 0, 0, 191, 0, 255, 31, 254, 223, 0, 0, 13, 2, 255, 95, 254, 223, 0, 0, 13, 2, 255, 95, 255, 159, 0, 0, 191, 0, 255, 31, 255, 159, 0, 0, 139, 3, 255, 159, 255, 159, 0, 0, 139, 3, 255, 159, 255, 31, 0, 0, 191, 0, 255, 31, 255, 31, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_k0we6"]
resource_name = "DRC_Card_Modell_Test01_Plane_008"
_surfaces = [{
"aabb": AABB(-0.0264038, -2.21294, -1.76026, 1.02241e-05, 4.03382, 3.52053),
"attribute_data": PackedByteArray(42, 54, 180, 58, 42, 54, 34, 230, 199, 203, 180, 58, 199, 203, 34, 230, 249, 128, 180, 58, 42, 54, 107, 144, 249, 128, 34, 230, 199, 203, 107, 144, 249, 128, 107, 144, 145, 91, 180, 58, 42, 54, 70, 187, 96, 166, 34, 230, 199, 203, 143, 101, 96, 166, 180, 58, 42, 54, 143, 101, 145, 91, 34, 230, 199, 203, 70, 187, 96, 166, 107, 144, 145, 91, 107, 144, 249, 128, 143, 101, 249, 128, 70, 187, 145, 91, 70, 187, 145, 91, 143, 101, 96, 166, 143, 101, 96, 166, 70, 187, 221, 72, 180, 58, 42, 54, 180, 208, 20, 185, 34, 230, 199, 203, 33, 80, 172, 147, 180, 58, 42, 54, 253, 122, 69, 110, 34, 230, 199, 203, 217, 165, 20, 185, 107, 144, 69, 110, 107, 144, 249, 128, 33, 80, 249, 128, 217, 165, 69, 110, 180, 58, 42, 54, 217, 165, 172, 147, 34, 230, 199, 203, 253, 122, 20, 185, 180, 58, 42, 54, 33, 80, 221, 72, 34, 230, 199, 203, 180, 208, 172, 147, 107, 144, 221, 72, 107, 144, 249, 128, 253, 122, 249, 128, 180, 208, 69, 110, 70, 187, 221, 72, 70, 187, 145, 91, 217, 165, 145, 91, 180, 208, 69, 110, 143, 101, 221, 72, 143, 101, 145, 91, 33, 80, 145, 91, 253, 122, 20, 185, 143, 101, 172, 147, 143, 101, 96, 166, 33, 80, 96, 166, 253, 122, 20, 185, 70, 187, 172, 147, 70, 187, 96, 166, 217, 165, 96, 166, 180, 208, 172, 147, 180, 208, 172, 147, 217, 165, 20, 185, 217, 165, 172, 147, 253, 122, 172, 147, 33, 80, 20, 185, 33, 80, 221, 72, 253, 122, 221, 72, 33, 80, 69, 110, 33, 80, 221, 72, 180, 208, 221, 72, 217, 165, 69, 110, 217, 165, 69, 110, 180, 208, 69, 110, 253, 122, 20, 185, 253, 122, 20, 185, 180, 208),
"format": 34896613399,
"index_count": 384,
"index_data": PackedByteArray(80, 0, 3, 0, 27, 0, 80, 0, 44, 0, 3, 0, 79, 0, 7, 0, 33, 0, 79, 0, 40, 0, 7, 0, 78, 0, 8, 0, 34, 0, 78, 0, 47, 0, 8, 0, 77, 0, 6, 0, 31, 0, 77, 0, 48, 0, 6, 0, 76, 0, 20, 0, 49, 0, 76, 0, 36, 0, 20, 0, 75, 0, 21, 0, 50, 0, 75, 0, 51, 0, 21, 0, 74, 0, 15, 0, 43, 0, 74, 0, 52, 0, 15, 0, 73, 0, 19, 0, 53, 0, 73, 0, 35, 0, 19, 0, 72, 0, 22, 0, 54, 0, 72, 0, 55, 0, 22, 0, 71, 0, 18, 0, 46, 0, 71, 0, 56, 0, 18, 0, 70, 0, 12, 0, 57, 0, 70, 0, 28, 0, 12, 0, 69, 0, 23, 0, 58, 0, 69, 0, 59, 0, 23, 0, 68, 0, 17, 0, 45, 0, 68, 0, 60, 0, 17, 0, 67, 0, 16, 0, 61, 0, 67, 0, 32, 0, 16, 0, 66, 0, 24, 0, 62, 0, 66, 0, 63, 0, 24, 0, 65, 0, 11, 0, 39, 0, 65, 0, 64, 0, 11, 0, 62, 0, 64, 0, 65, 0, 62, 0, 24, 0, 64, 0, 20, 0, 65, 0, 48, 0, 20, 0, 62, 0, 65, 0, 48, 0, 39, 0, 6, 0, 48, 0, 65, 0, 39, 0, 45, 0, 63, 0, 66, 0, 45, 0, 17, 0, 63, 0, 8, 0, 66, 0, 36, 0, 8, 0, 45, 0, 66, 0, 36, 0, 62, 0, 20, 0, 36, 0, 66, 0, 62, 0, 33, 0, 32, 0, 67, 0, 33, 0, 7, 0, 32, 0, 17, 0, 67, 0, 63, 0, 17, 0, 33, 0, 67, 0, 63, 0, 61, 0, 24, 0, 63, 0, 67, 0, 61, 0, 58, 0, 60, 0, 68, 0, 58, 0, 23, 0, 60, 0, 19, 0, 68, 0, 47, 0, 19, 0, 58, 0, 68, 0, 47, 0, 45, 0, 8, 0, 47, 0, 68, 0, 45, 0, 29, 0, 59, 0, 69, 0, 29, 0, 13, 0, 59, 0, 4, 0, 69, 0, 35, 0, 4, 0, 29, 0, 69, 0, 35, 0, 58, 0, 19, 0, 35, 0, 69, 0, 58, 0, 41, 0, 28, 0, 70, 0, 41, 0, 2, 0, 28, 0, 13, 0, 70, 0, 59, 0, 13, 0, 41, 0, 70, 0, 59, 0, 57, 0, 23, 0, 59, 0, 70, 0, 57, 0, 54, 0, 56, 0, 71, 0, 54, 0, 22, 0, 56, 0, 14, 0, 71, 0, 30, 0, 14, 0, 54, 0, 71, 0, 30, 0, 46, 0, 5, 0, 30, 0, 71, 0, 46, 0, 25, 0, 55, 0, 72, 0, 25, 0, 9, 0, 55, 0, 0, 0, 72, 0, 42, 0, 0, 0, 25, 0, 72, 0, 42, 0, 54, 0, 14, 0, 42, 0, 72, 0, 54, 0, 37, 0, 35, 0, 73, 0, 37, 0, 4, 0, 35, 0, 9, 0, 73, 0, 55, 0, 9, 0, 37, 0, 73, 0, 55, 0, 53, 0, 22, 0, 55, 0, 73, 0, 53, 0, 50, 0, 52, 0, 74, 0, 50, 0, 21, 0, 52, 0, 10, 0, 74, 0, 26, 0, 10, 0, 50, 0, 74, 0, 26, 0, 43, 0, 1, 0, 26, 0, 74, 0, 43, 0, 46, 0, 51, 0, 75, 0, 46, 0, 18, 0, 51, 0, 5, 0, 75, 0, 38, 0, 5, 0, 46, 0, 75, 0, 38, 0, 50, 0, 10, 0, 38, 0, 75, 0, 50, 0, 34, 0, 36, 0, 76, 0, 34, 0, 8, 0, 36, 0, 18, 0, 76, 0, 51, 0, 18, 0, 34, 0, 76, 0, 51, 0, 49, 0, 21, 0, 51, 0, 76, 0, 49, 0, 49, 0, 48, 0, 77, 0, 49, 0, 20, 0, 48, 0, 21, 0, 77, 0, 52, 0, 21, 0, 49, 0, 77, 0, 52, 0, 31, 0, 15, 0, 52, 0, 77, 0, 31, 0, 53, 0, 47, 0, 78, 0, 53, 0, 19, 0, 47, 0, 22, 0, 78, 0, 56, 0, 22, 0, 53, 0, 78, 0, 56, 0, 34, 0, 18, 0, 56, 0, 78, 0, 34, 0, 57, 0, 40, 0, 79, 0, 57, 0, 12, 0, 40, 0, 23, 0, 79, 0, 60, 0, 23, 0, 57, 0, 79, 0, 60, 0, 33, 0, 17, 0, 60, 0, 79, 0, 33, 0, 61, 0, 44, 0, 80, 0, 61, 0, 16, 0, 44, 0, 24, 0, 80, 0, 64, 0, 24, 0, 61, 0, 80, 0, 64, 0, 27, 0, 11, 0, 64, 0, 80, 0, 27, 0),
"lods": [1.11759e-08, PackedByteArray(8, 0, 45, 0, 24, 0, 45, 0, 44, 0, 24, 0, 24, 0, 44, 0, 3, 0, 45, 0, 16, 0, 44, 0, 24, 0, 3, 0, 62, 0, 8, 0, 24, 0, 62, 0, 62, 0, 3, 0, 27, 0, 62, 0, 27, 0, 11, 0, 8, 0, 62, 0, 11, 0, 17, 0, 16, 0, 45, 0, 17, 0, 32, 0, 16, 0, 47, 0, 17, 0, 45, 0, 47, 0, 45, 0, 8, 0, 58, 0, 32, 0, 17, 0, 58, 0, 7, 0, 32, 0, 19, 0, 17, 0, 47, 0, 19, 0, 58, 0, 17, 0, 23, 0, 7, 0, 58, 0, 23, 0, 40, 0, 7, 0, 4, 0, 23, 0, 58, 0, 4, 0, 58, 0, 19, 0, 29, 0, 40, 0, 23, 0, 4, 0, 29, 0, 23, 0, 29, 0, 12, 0, 40, 0, 37, 0, 4, 0, 19, 0, 13, 0, 12, 0, 29, 0, 9, 0, 37, 0, 19, 0, 13, 0, 41, 0, 12, 0, 41, 0, 28, 0, 12, 0, 41, 0, 2, 0, 28, 0, 9, 0, 19, 0, 25, 0, 25, 0, 19, 0, 47, 0, 25, 0, 47, 0, 22, 0, 0, 0, 25, 0, 22, 0, 22, 0, 47, 0, 8, 0, 0, 0, 22, 0, 42, 0, 42, 0, 22, 0, 54, 0, 22, 0, 8, 0, 54, 0, 42, 0, 54, 0, 14, 0, 54, 0, 8, 0, 18, 0, 14, 0, 54, 0, 18, 0, 14, 0, 18, 0, 30, 0, 18, 0, 8, 0, 11, 0, 30, 0, 18, 0, 5, 0, 18, 0, 11, 0, 49, 0, 5, 0, 18, 0, 49, 0, 49, 0, 11, 0, 39, 0, 49, 0, 39, 0, 6, 0, 5, 0, 49, 0, 21, 0, 21, 0, 49, 0, 6, 0, 5, 0, 21, 0, 38, 0, 21, 0, 6, 0, 50, 0, 38, 0, 21, 0, 50, 0, 50, 0, 6, 0, 31, 0, 38, 0, 50, 0, 10, 0, 50, 0, 31, 0, 15, 0, 10, 0, 50, 0, 15, 0, 10, 0, 15, 0, 26, 0, 26, 0, 15, 0, 43, 0, 26, 0, 43, 0, 1, 0), 1.34317e-08, PackedByteArray(11, 0, 16, 0, 27, 0, 16, 0, 3, 0, 27, 0, 16, 0, 44, 0, 3, 0, 4, 0, 16, 0, 11, 0, 4, 0, 32, 0, 16, 0, 4, 0, 7, 0, 32, 0, 29, 0, 7, 0, 4, 0, 29, 0, 40, 0, 7, 0, 29, 0, 12, 0, 40, 0, 13, 0, 12, 0, 29, 0, 13, 0, 41, 0, 12, 0, 41, 0, 28, 0, 12, 0, 41, 0, 2, 0, 28, 0, 9, 0, 37, 0, 4, 0, 9, 0, 4, 0, 25, 0, 25, 0, 4, 0, 11, 0, 25, 0, 11, 0, 30, 0, 42, 0, 25, 0, 30, 0, 0, 0, 25, 0, 42, 0, 42, 0, 30, 0, 14, 0, 30, 0, 11, 0, 5, 0, 5, 0, 11, 0, 39, 0, 5, 0, 39, 0, 6, 0, 5, 0, 6, 0, 15, 0, 15, 0, 6, 0, 31, 0, 38, 0, 5, 0, 15, 0, 38, 0, 15, 0, 10, 0, 10, 0, 15, 0, 26, 0, 26, 0, 15, 0, 43, 0, 26, 0, 43, 0, 1, 0)],
"material": SubResource("StandardMaterial3D_kt210"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 81,
"vertex_data": PackedByteArray(152, 5, 254, 255, 254, 255, 255, 191, 0, 0, 0, 0, 254, 255, 255, 191, 152, 5, 254, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 152, 5, 254, 255, 255, 127, 255, 191, 204, 2, 255, 127, 254, 255, 255, 191, 0, 0, 0, 0, 255, 127, 255, 191, 204, 2, 255, 127, 0, 0, 255, 191, 204, 2, 255, 127, 255, 127, 255, 191, 152, 5, 254, 255, 255, 191, 255, 191, 126, 1, 255, 63, 254, 255, 255, 191, 0, 0, 0, 0, 255, 63, 255, 191, 26, 4, 255, 191, 0, 0, 255, 191, 152, 5, 254, 255, 255, 63, 255, 191, 26, 4, 255, 191, 254, 255, 255, 191, 0, 0, 0, 0, 255, 191, 255, 191, 126, 1, 255, 63, 0, 0, 255, 191, 204, 2, 255, 127, 255, 63, 255, 191, 204, 2, 255, 127, 255, 191, 255, 191, 26, 4, 255, 191, 255, 127, 255, 191, 126, 1, 255, 63, 255, 127, 255, 191, 126, 1, 255, 63, 255, 191, 255, 191, 26, 4, 255, 191, 255, 191, 255, 191, 26, 4, 255, 191, 255, 63, 255, 191, 126, 1, 255, 63, 255, 63, 255, 191, 152, 5, 254, 255, 254, 223, 255, 191, 191, 0, 255, 31, 254, 255, 255, 191, 0, 0, 0, 0, 255, 31, 255, 191, 217, 4, 254, 223, 0, 0, 255, 191, 152, 5, 254, 255, 255, 95, 255, 191, 139, 3, 255, 159, 254, 255, 255, 191, 0, 0, 0, 0, 255, 159, 255, 191, 13, 2, 255, 95, 0, 0, 255, 191, 204, 2, 255, 127, 255, 31, 255, 191, 204, 2, 255, 127, 255, 159, 255, 191, 217, 4, 254, 223, 255, 127, 255, 191, 13, 2, 255, 95, 255, 127, 255, 191, 152, 5, 254, 255, 255, 159, 255, 191, 13, 2, 255, 95, 254, 255, 255, 191, 0, 0, 0, 0, 255, 95, 255, 191, 139, 3, 255, 159, 0, 0, 255, 191, 152, 5, 254, 255, 255, 31, 255, 191, 217, 4, 254, 223, 254, 255, 255, 191, 0, 0, 0, 0, 254, 223, 255, 191, 191, 0, 255, 31, 0, 0, 255, 191, 204, 2, 255, 127, 255, 95, 255, 191, 204, 2, 255, 127, 254, 223, 255, 191, 139, 3, 255, 159, 255, 127, 255, 191, 191, 0, 255, 31, 255, 127, 255, 191, 126, 1, 255, 63, 255, 159, 255, 191, 126, 1, 255, 63, 254, 223, 255, 191, 13, 2, 255, 95, 255, 191, 255, 191, 191, 0, 255, 31, 255, 191, 255, 191, 26, 4, 255, 191, 255, 159, 255, 191, 26, 4, 255, 191, 254, 223, 255, 191, 217, 4, 254, 223, 255, 191, 255, 191, 139, 3, 255, 159, 255, 191, 255, 191, 26, 4, 255, 191, 255, 31, 255, 191, 26, 4, 255, 191, 255, 95, 255, 191, 217, 4, 254, 223, 255, 63, 255, 191, 139, 3, 255, 159, 255, 63, 255, 191, 126, 1, 255, 63, 255, 31, 255, 191, 126, 1, 255, 63, 255, 95, 255, 191, 13, 2, 255, 95, 255, 63, 255, 191, 191, 0, 255, 31, 255, 63, 255, 191, 191, 0, 255, 31, 255, 95, 255, 191, 13, 2, 255, 95, 255, 95, 255, 191, 13, 2, 255, 95, 255, 31, 255, 191, 139, 3, 255, 159, 255, 95, 255, 191, 217, 4, 254, 223, 255, 95, 255, 191, 217, 4, 254, 223, 255, 31, 255, 191, 139, 3, 255, 159, 254, 223, 255, 191, 217, 4, 254, 223, 254, 223, 255, 191, 217, 4, 254, 223, 255, 159, 255, 191, 191, 0, 255, 31, 254, 223, 255, 191, 13, 2, 255, 95, 254, 223, 255, 191, 13, 2, 255, 95, 255, 159, 255, 191, 191, 0, 255, 31, 255, 159, 255, 191, 139, 3, 255, 159, 255, 159, 255, 191, 139, 3, 255, 159, 255, 31, 255, 191, 191, 0, 255, 31, 255, 31, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_qs3xa")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_yoaeo"]
transparency = 1
shading_mode = 0
albedo_texture = ExtResource("22_b4rcw")
uv1_scale = Vector3(1.635, 1.635, 1.635)
uv1_offset = Vector3(-0.35, -0.825, -0.1)
texture_repeat = false
grow_amount = 1.445

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_bml01"]
resource_name = "CardPicGradient"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("22_b4rcw")

[sub_resource type="ArrayMesh" id="ArrayMesh_12ib6"]
_surfaces = [{
"aabb": AABB(0.0275282, -2.27195, -1.73893, 1.017e-05, 4.03574, 3.47787),
"format": 34896613377,
"index_count": 384,
"index_data": PackedByteArray(80, 0, 3, 0, 27, 0, 80, 0, 44, 0, 3, 0, 79, 0, 7, 0, 33, 0, 79, 0, 40, 0, 7, 0, 78, 0, 8, 0, 34, 0, 78, 0, 47, 0, 8, 0, 77, 0, 6, 0, 31, 0, 77, 0, 48, 0, 6, 0, 76, 0, 20, 0, 49, 0, 76, 0, 36, 0, 20, 0, 75, 0, 21, 0, 50, 0, 75, 0, 51, 0, 21, 0, 74, 0, 15, 0, 43, 0, 74, 0, 52, 0, 15, 0, 73, 0, 19, 0, 53, 0, 73, 0, 35, 0, 19, 0, 72, 0, 22, 0, 54, 0, 72, 0, 55, 0, 22, 0, 71, 0, 18, 0, 46, 0, 71, 0, 56, 0, 18, 0, 70, 0, 12, 0, 57, 0, 70, 0, 28, 0, 12, 0, 69, 0, 23, 0, 58, 0, 69, 0, 59, 0, 23, 0, 68, 0, 17, 0, 45, 0, 68, 0, 60, 0, 17, 0, 67, 0, 16, 0, 61, 0, 67, 0, 32, 0, 16, 0, 66, 0, 24, 0, 62, 0, 66, 0, 63, 0, 24, 0, 65, 0, 11, 0, 39, 0, 65, 0, 64, 0, 11, 0, 62, 0, 64, 0, 65, 0, 62, 0, 24, 0, 64, 0, 20, 0, 65, 0, 48, 0, 20, 0, 62, 0, 65, 0, 48, 0, 39, 0, 6, 0, 48, 0, 65, 0, 39, 0, 45, 0, 63, 0, 66, 0, 45, 0, 17, 0, 63, 0, 8, 0, 66, 0, 36, 0, 8, 0, 45, 0, 66, 0, 36, 0, 62, 0, 20, 0, 36, 0, 66, 0, 62, 0, 33, 0, 32, 0, 67, 0, 33, 0, 7, 0, 32, 0, 17, 0, 67, 0, 63, 0, 17, 0, 33, 0, 67, 0, 63, 0, 61, 0, 24, 0, 63, 0, 67, 0, 61, 0, 58, 0, 60, 0, 68, 0, 58, 0, 23, 0, 60, 0, 19, 0, 68, 0, 47, 0, 19, 0, 58, 0, 68, 0, 47, 0, 45, 0, 8, 0, 47, 0, 68, 0, 45, 0, 29, 0, 59, 0, 69, 0, 29, 0, 13, 0, 59, 0, 4, 0, 69, 0, 35, 0, 4, 0, 29, 0, 69, 0, 35, 0, 58, 0, 19, 0, 35, 0, 69, 0, 58, 0, 41, 0, 28, 0, 70, 0, 41, 0, 2, 0, 28, 0, 13, 0, 70, 0, 59, 0, 13, 0, 41, 0, 70, 0, 59, 0, 57, 0, 23, 0, 59, 0, 70, 0, 57, 0, 54, 0, 56, 0, 71, 0, 54, 0, 22, 0, 56, 0, 14, 0, 71, 0, 30, 0, 14, 0, 54, 0, 71, 0, 30, 0, 46, 0, 5, 0, 30, 0, 71, 0, 46, 0, 25, 0, 55, 0, 72, 0, 25, 0, 9, 0, 55, 0, 0, 0, 72, 0, 42, 0, 0, 0, 25, 0, 72, 0, 42, 0, 54, 0, 14, 0, 42, 0, 72, 0, 54, 0, 37, 0, 35, 0, 73, 0, 37, 0, 4, 0, 35, 0, 9, 0, 73, 0, 55, 0, 9, 0, 37, 0, 73, 0, 55, 0, 53, 0, 22, 0, 55, 0, 73, 0, 53, 0, 50, 0, 52, 0, 74, 0, 50, 0, 21, 0, 52, 0, 10, 0, 74, 0, 26, 0, 10, 0, 50, 0, 74, 0, 26, 0, 43, 0, 1, 0, 26, 0, 74, 0, 43, 0, 46, 0, 51, 0, 75, 0, 46, 0, 18, 0, 51, 0, 5, 0, 75, 0, 38, 0, 5, 0, 46, 0, 75, 0, 38, 0, 50, 0, 10, 0, 38, 0, 75, 0, 50, 0, 34, 0, 36, 0, 76, 0, 34, 0, 8, 0, 36, 0, 18, 0, 76, 0, 51, 0, 18, 0, 34, 0, 76, 0, 51, 0, 49, 0, 21, 0, 51, 0, 76, 0, 49, 0, 49, 0, 48, 0, 77, 0, 49, 0, 20, 0, 48, 0, 21, 0, 77, 0, 52, 0, 21, 0, 49, 0, 77, 0, 52, 0, 31, 0, 15, 0, 52, 0, 77, 0, 31, 0, 53, 0, 47, 0, 78, 0, 53, 0, 19, 0, 47, 0, 22, 0, 78, 0, 56, 0, 22, 0, 53, 0, 78, 0, 56, 0, 34, 0, 18, 0, 56, 0, 78, 0, 34, 0, 57, 0, 40, 0, 79, 0, 57, 0, 12, 0, 40, 0, 23, 0, 79, 0, 60, 0, 23, 0, 57, 0, 79, 0, 60, 0, 33, 0, 17, 0, 60, 0, 79, 0, 33, 0, 61, 0, 44, 0, 80, 0, 61, 0, 16, 0, 44, 0, 24, 0, 80, 0, 64, 0, 24, 0, 61, 0, 80, 0, 64, 0, 27, 0, 11, 0, 64, 0, 80, 0, 27, 0),
"lods": [0.000358111, PackedByteArray(80, 0, 3, 0, 27, 0, 80, 0, 44, 0, 3, 0, 61, 0, 44, 0, 80, 0, 61, 0, 16, 0, 44, 0, 33, 0, 16, 0, 61, 0, 33, 0, 32, 0, 16, 0, 33, 0, 7, 0, 32, 0, 79, 0, 7, 0, 33, 0, 79, 0, 40, 0, 7, 0, 57, 0, 40, 0, 79, 0, 57, 0, 12, 0, 40, 0, 57, 0, 28, 0, 12, 0, 41, 0, 28, 0, 57, 0, 41, 0, 2, 0, 28, 0, 78, 0, 8, 0, 34, 0, 53, 0, 8, 0, 78, 0, 34, 0, 8, 0, 20, 0, 53, 0, 19, 0, 8, 0, 37, 0, 19, 0, 53, 0, 37, 0, 4, 0, 19, 0, 34, 0, 20, 0, 76, 0, 76, 0, 20, 0, 49, 0, 49, 0, 20, 0, 48, 0, 49, 0, 48, 0, 77, 0, 77, 0, 48, 0, 6, 0, 77, 0, 6, 0, 31, 0, 46, 0, 21, 0, 50, 0, 50, 0, 21, 0, 52, 0, 50, 0, 52, 0, 15, 0, 50, 0, 15, 0, 43, 0, 46, 0, 18, 0, 21, 0, 54, 0, 18, 0, 46, 0, 54, 0, 22, 0, 18, 0, 72, 0, 22, 0, 54, 0, 25, 0, 22, 0, 72, 0, 25, 0, 9, 0, 22, 0, 69, 0, 23, 0, 58, 0, 29, 0, 23, 0, 69, 0, 29, 0, 13, 0, 23, 0, 58, 0, 23, 0, 17, 0, 58, 0, 17, 0, 45, 0, 45, 0, 17, 0, 24, 0, 45, 0, 24, 0, 62, 0, 62, 0, 24, 0, 64, 0, 62, 0, 64, 0, 11, 0, 62, 0, 11, 0, 39, 0, 20, 0, 62, 0, 48, 0, 48, 0, 62, 0, 39, 0, 48, 0, 39, 0, 6, 0, 20, 0, 45, 0, 62, 0, 8, 0, 45, 0, 20, 0, 8, 0, 58, 0, 45, 0, 19, 0, 58, 0, 8, 0, 19, 0, 69, 0, 58, 0, 4, 0, 69, 0, 19, 0, 4, 0, 29, 0, 69, 0, 17, 0, 33, 0, 24, 0, 24, 0, 33, 0, 61, 0, 17, 0, 79, 0, 33, 0, 24, 0, 61, 0, 80, 0, 23, 0, 79, 0, 17, 0, 24, 0, 80, 0, 64, 0, 64, 0, 80, 0, 27, 0, 64, 0, 27, 0, 11, 0, 23, 0, 57, 0, 79, 0, 13, 0, 57, 0, 23, 0, 13, 0, 41, 0, 57, 0, 14, 0, 54, 0, 30, 0, 42, 0, 54, 0, 14, 0, 30, 0, 54, 0, 46, 0, 42, 0, 72, 0, 54, 0, 0, 0, 72, 0, 42, 0, 0, 0, 25, 0, 72, 0, 30, 0, 46, 0, 5, 0, 5, 0, 46, 0, 38, 0, 38, 0, 46, 0, 50, 0, 38, 0, 50, 0, 10, 0, 10, 0, 50, 0, 26, 0, 26, 0, 50, 0, 43, 0, 26, 0, 43, 0, 1, 0, 9, 0, 53, 0, 22, 0, 9, 0, 37, 0, 53, 0, 22, 0, 53, 0, 78, 0, 22, 0, 78, 0, 18, 0, 18, 0, 78, 0, 34, 0, 18, 0, 34, 0, 76, 0, 18, 0, 76, 0, 21, 0, 21, 0, 76, 0, 49, 0, 21, 0, 49, 0, 77, 0, 21, 0, 77, 0, 52, 0, 52, 0, 77, 0, 31, 0, 52, 0, 31, 0, 15, 0), 0.000413511, PackedByteArray(33, 0, 3, 0, 27, 0, 33, 0, 44, 0, 3, 0, 33, 0, 16, 0, 44, 0, 33, 0, 32, 0, 16, 0, 33, 0, 7, 0, 32, 0, 33, 0, 40, 0, 7, 0, 33, 0, 12, 0, 40, 0, 33, 0, 28, 0, 12, 0, 41, 0, 28, 0, 33, 0, 41, 0, 2, 0, 28, 0, 53, 0, 8, 0, 49, 0, 37, 0, 8, 0, 53, 0, 37, 0, 4, 0, 8, 0, 49, 0, 8, 0, 6, 0, 49, 0, 6, 0, 31, 0, 46, 0, 15, 0, 43, 0, 46, 0, 18, 0, 15, 0, 25, 0, 18, 0, 46, 0, 25, 0, 9, 0, 18, 0, 58, 0, 11, 0, 39, 0, 58, 0, 17, 0, 11, 0, 29, 0, 17, 0, 58, 0, 29, 0, 13, 0, 17, 0, 8, 0, 39, 0, 6, 0, 8, 0, 58, 0, 39, 0, 4, 0, 58, 0, 8, 0, 4, 0, 29, 0, 58, 0, 13, 0, 33, 0, 17, 0, 13, 0, 41, 0, 33, 0, 17, 0, 33, 0, 27, 0, 17, 0, 27, 0, 11, 0, 14, 0, 46, 0, 30, 0, 30, 0, 46, 0, 5, 0, 42, 0, 46, 0, 14, 0, 0, 0, 46, 0, 42, 0, 0, 0, 25, 0, 46, 0, 5, 0, 46, 0, 38, 0, 38, 0, 46, 0, 10, 0, 10, 0, 46, 0, 26, 0, 26, 0, 46, 0, 43, 0, 26, 0, 43, 0, 1, 0, 9, 0, 53, 0, 18, 0, 9, 0, 37, 0, 53, 0, 18, 0, 53, 0, 49, 0, 18, 0, 49, 0, 31, 0, 18, 0, 31, 0, 15, 0)],
"name": "CardPicGradient",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 81,
"vertex_data": PackedByteArray(68, 4, 254, 255, 254, 255, 0, 0, 0, 0, 0, 0, 254, 255, 0, 0, 68, 4, 254, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 4, 254, 255, 255, 127, 0, 0, 40, 2, 255, 127, 254, 255, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 40, 2, 255, 127, 0, 0, 0, 0, 40, 2, 255, 127, 255, 127, 0, 0, 68, 4, 254, 255, 255, 191, 0, 0, 252, 0, 255, 63, 254, 255, 0, 0, 0, 0, 0, 0, 255, 63, 0, 0, 84, 3, 255, 191, 0, 0, 0, 0, 68, 4, 254, 255, 255, 63, 0, 0, 84, 3, 255, 191, 254, 255, 0, 0, 0, 0, 0, 0, 255, 191, 0, 0, 252, 0, 255, 63, 0, 0, 0, 0, 40, 2, 255, 127, 255, 63, 0, 0, 40, 2, 255, 127, 255, 191, 0, 0, 84, 3, 255, 191, 255, 127, 0, 0, 252, 0, 255, 63, 255, 127, 0, 0, 252, 0, 255, 63, 255, 191, 0, 0, 84, 3, 255, 191, 255, 191, 0, 0, 84, 3, 255, 191, 255, 63, 0, 0, 252, 0, 255, 63, 255, 63, 0, 0, 68, 4, 254, 255, 254, 223, 0, 0, 96, 0, 255, 31, 254, 255, 0, 0, 0, 0, 0, 0, 255, 31, 0, 0, 228, 3, 254, 223, 0, 0, 0, 0, 68, 4, 254, 255, 255, 95, 0, 0, 184, 2, 255, 159, 254, 255, 0, 0, 0, 0, 0, 0, 255, 159, 0, 0, 140, 1, 255, 95, 0, 0, 0, 0, 40, 2, 255, 127, 255, 31, 0, 0, 40, 2, 255, 127, 255, 159, 0, 0, 228, 3, 254, 223, 255, 127, 0, 0, 140, 1, 255, 95, 255, 127, 0, 0, 68, 4, 254, 255, 255, 159, 0, 0, 140, 1, 255, 95, 254, 255, 0, 0, 0, 0, 0, 0, 255, 95, 0, 0, 184, 2, 255, 159, 0, 0, 0, 0, 68, 4, 254, 255, 255, 31, 0, 0, 228, 3, 254, 223, 254, 255, 0, 0, 0, 0, 0, 0, 254, 223, 0, 0, 96, 0, 255, 31, 0, 0, 0, 0, 40, 2, 255, 127, 255, 95, 0, 0, 40, 2, 255, 127, 254, 223, 0, 0, 184, 2, 255, 159, 255, 127, 0, 0, 96, 0, 255, 31, 255, 127, 0, 0, 252, 0, 255, 63, 255, 159, 0, 0, 252, 0, 255, 63, 254, 223, 0, 0, 140, 1, 255, 95, 255, 191, 0, 0, 96, 0, 255, 31, 255, 191, 0, 0, 84, 3, 255, 191, 255, 159, 0, 0, 84, 3, 255, 191, 254, 223, 0, 0, 228, 3, 254, 223, 255, 191, 0, 0, 184, 2, 255, 159, 255, 191, 0, 0, 84, 3, 255, 191, 255, 31, 0, 0, 84, 3, 255, 191, 255, 95, 0, 0, 228, 3, 254, 223, 255, 63, 0, 0, 184, 2, 255, 159, 255, 63, 0, 0, 252, 0, 255, 63, 255, 31, 0, 0, 252, 0, 255, 63, 255, 95, 0, 0, 140, 1, 255, 95, 255, 63, 0, 0, 96, 0, 255, 31, 255, 63, 0, 0, 96, 0, 255, 31, 255, 95, 0, 0, 140, 1, 255, 95, 255, 95, 0, 0, 140, 1, 255, 95, 255, 31, 0, 0, 184, 2, 255, 159, 255, 95, 0, 0, 228, 3, 254, 223, 255, 95, 0, 0, 228, 3, 254, 223, 255, 31, 0, 0, 184, 2, 255, 159, 254, 223, 0, 0, 228, 3, 254, 223, 254, 223, 0, 0, 228, 3, 254, 223, 255, 159, 0, 0, 96, 0, 255, 31, 254, 223, 0, 0, 140, 1, 255, 95, 254, 223, 0, 0, 140, 1, 255, 95, 255, 159, 0, 0, 96, 0, 255, 31, 255, 159, 0, 0, 184, 2, 255, 159, 255, 159, 0, 0, 184, 2, 255, 159, 255, 31, 0, 0, 96, 0, 255, 31, 255, 31, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_4w0hb"]
resource_name = "DRC_Card_Modell_Test01_Plane_009"
_surfaces = [{
"aabb": AABB(0.0275282, -2.27195, -1.73893, 1.017e-05, 4.03574, 3.47787),
"attribute_data": PackedByteArray(148, 133, 0, 0, 148, 133, 171, 203, 254, 255, 0, 0, 254, 255, 171, 203, 148, 133, 0, 0, 255, 255, 0, 0, 148, 133, 213, 101, 148, 133, 171, 203, 254, 255, 171, 203, 254, 255, 213, 101, 148, 133, 213, 101, 254, 255, 213, 101, 148, 133, 0, 0, 254, 255, 0, 0, 148, 133, 192, 152, 148, 133, 171, 203, 254, 255, 171, 203, 254, 255, 234, 50, 148, 133, 0, 0, 255, 255, 0, 0, 148, 133, 234, 50, 148, 133, 171, 203, 254, 255, 171, 203, 254, 255, 192, 152, 148, 133, 213, 101, 254, 255, 213, 101, 148, 133, 213, 101, 254, 255, 213, 101, 148, 133, 234, 50, 254, 255, 234, 50, 148, 133, 192, 152, 254, 255, 192, 152, 148, 133, 192, 152, 254, 255, 192, 152, 148, 133, 234, 50, 254, 255, 234, 50, 148, 133, 234, 50, 254, 255, 234, 50, 148, 133, 192, 152, 254, 255, 192, 152, 148, 133, 0, 0, 254, 255, 0, 0, 148, 133, 53, 178, 148, 133, 171, 203, 254, 255, 171, 203, 254, 255, 117, 25, 148, 133, 0, 0, 254, 255, 0, 0, 148, 133, 96, 76, 148, 133, 171, 203, 255, 255, 171, 203, 254, 255, 75, 127, 148, 133, 213, 101, 254, 255, 213, 101, 148, 133, 213, 101, 255, 255, 213, 101, 148, 133, 117, 25, 254, 255, 117, 25, 148, 133, 75, 127, 254, 255, 75, 127, 148, 133, 0, 0, 255, 255, 0, 0, 148, 133, 75, 127, 148, 133, 171, 203, 254, 255, 171, 203, 254, 255, 96, 76, 148, 133, 0, 0, 254, 255, 0, 0, 148, 133, 117, 25, 148, 133, 171, 203, 254, 255, 171, 203, 254, 255, 53, 178, 148, 133, 213, 101, 254, 255, 213, 101, 148, 133, 213, 101, 254, 255, 213, 101, 148, 133, 96, 76, 254, 255, 96, 76, 148, 133, 53, 178, 254, 255, 53, 178, 148, 133, 192, 152, 255, 255, 192, 152, 148, 133, 192, 152, 254, 255, 192, 152, 148, 133, 75, 127, 254, 255, 75, 127, 148, 133, 53, 178, 254, 255, 53, 178, 148, 133, 234, 50, 255, 255, 234, 50, 148, 133, 234, 50, 254, 255, 234, 50, 148, 133, 117, 25, 254, 255, 117, 25, 148, 133, 96, 76, 254, 255, 96, 76, 148, 133, 234, 50, 254, 255, 234, 50, 148, 133, 234, 50, 254, 255, 234, 50, 148, 133, 117, 25, 254, 255, 117, 25, 148, 133, 96, 76, 254, 255, 96, 76, 148, 133, 192, 152, 254, 255, 192, 152, 148, 133, 192, 152, 254, 255, 192, 152, 148, 133, 75, 127, 254, 255, 75, 127, 148, 133, 53, 178, 254, 255, 53, 178, 148, 133, 53, 178, 254, 255, 53, 178, 148, 133, 75, 127, 254, 255, 75, 127, 148, 133, 75, 127, 254, 255, 75, 127, 148, 133, 96, 76, 254, 255, 96, 76, 148, 133, 117, 25, 254, 255, 117, 25, 148, 133, 117, 25, 254, 255, 117, 25, 148, 133, 96, 76, 254, 255, 96, 76, 148, 133, 117, 25, 254, 255, 117, 25, 148, 133, 117, 25, 255, 255, 117, 25, 148, 133, 53, 178, 254, 255, 53, 178, 148, 133, 75, 127, 254, 255, 75, 127, 148, 133, 75, 127, 255, 255, 75, 127, 148, 133, 53, 178, 255, 255, 53, 178, 148, 133, 96, 76, 255, 255, 96, 76, 148, 133, 96, 76, 254, 255, 96, 76, 148, 133, 53, 178, 254, 255, 53, 178),
"format": 34896613399,
"index_count": 384,
"index_data": PackedByteArray(142, 0, 3, 0, 43, 0, 142, 0, 71, 0, 3, 0, 140, 0, 9, 0, 52, 0, 140, 0, 65, 0, 9, 0, 138, 0, 11, 0, 54, 0, 138, 0, 77, 0, 11, 0, 136, 0, 8, 0, 49, 0, 136, 0, 79, 0, 8, 0, 134, 0, 31, 0, 80, 0, 134, 0, 59, 0, 31, 0, 132, 0, 33, 0, 82, 0, 132, 0, 85, 0, 33, 0, 130, 0, 22, 0, 69, 0, 130, 0, 87, 0, 22, 0, 128, 0, 29, 0, 88, 0, 128, 0, 57, 0, 29, 0, 126, 0, 35, 0, 90, 0, 126, 0, 93, 0, 35, 0, 124, 0, 27, 0, 74, 0, 124, 0, 95, 0, 27, 0, 122, 0, 17, 0, 96, 0, 122, 0, 45, 0, 17, 0, 120, 0, 37, 0, 98, 0, 120, 0, 101, 0, 37, 0, 118, 0, 25, 0, 72, 0, 118, 0, 103, 0, 25, 0, 116, 0, 23, 0, 104, 0, 116, 0, 51, 0, 23, 0, 114, 0, 39, 0, 106, 0, 114, 0, 109, 0, 39, 0, 112, 0, 16, 0, 63, 0, 112, 0, 111, 0, 16, 0, 106, 0, 111, 0, 112, 0, 106, 0, 39, 0, 111, 0, 30, 0, 113, 0, 78, 0, 30, 0, 107, 0, 113, 0, 78, 0, 64, 0, 7, 0, 78, 0, 113, 0, 64, 0, 72, 0, 109, 0, 114, 0, 72, 0, 25, 0, 109, 0, 10, 0, 115, 0, 58, 0, 10, 0, 73, 0, 115, 0, 58, 0, 107, 0, 30, 0, 58, 0, 115, 0, 107, 0, 52, 0, 51, 0, 116, 0, 52, 0, 9, 0, 51, 0, 24, 0, 117, 0, 108, 0, 24, 0, 53, 0, 117, 0, 108, 0, 105, 0, 38, 0, 108, 0, 117, 0, 105, 0, 98, 0, 103, 0, 118, 0, 98, 0, 37, 0, 103, 0, 28, 0, 119, 0, 76, 0, 28, 0, 99, 0, 119, 0, 76, 0, 73, 0, 10, 0, 76, 0, 119, 0, 73, 0, 46, 0, 101, 0, 120, 0, 46, 0, 19, 0, 101, 0, 4, 0, 121, 0, 56, 0, 4, 0, 47, 0, 121, 0, 56, 0, 99, 0, 28, 0, 56, 0, 121, 0, 99, 0, 66, 0, 45, 0, 122, 0, 66, 0, 2, 0, 45, 0, 18, 0, 123, 0, 100, 0, 18, 0, 67, 0, 123, 0, 100, 0, 97, 0, 36, 0, 100, 0, 123, 0, 97, 0, 90, 0, 95, 0, 124, 0, 90, 0, 35, 0, 95, 0, 20, 0, 125, 0, 48, 0, 20, 0, 91, 0, 125, 0, 48, 0, 75, 0, 6, 0, 48, 0, 125, 0, 75, 0, 40, 0, 93, 0, 126, 0, 40, 0, 13, 0, 93, 0, 0, 0, 127, 0, 68, 0, 0, 0, 41, 0, 127, 0, 68, 0, 91, 0, 20, 0, 68, 0, 127, 0, 91, 0, 60, 0, 57, 0, 128, 0, 60, 0, 5, 0, 57, 0, 12, 0, 129, 0, 92, 0, 12, 0, 61, 0, 129, 0, 92, 0, 89, 0, 34, 0, 92, 0, 129, 0, 89, 0, 82, 0, 87, 0, 130, 0, 82, 0, 33, 0, 87, 0, 14, 0, 131, 0, 42, 0, 14, 0, 83, 0, 131, 0, 42, 0, 70, 0, 1, 0, 42, 0, 131, 0, 70, 0, 74, 0, 85, 0, 132, 0, 74, 0, 27, 0, 85, 0, 6, 0, 133, 0, 62, 0, 6, 0, 75, 0, 133, 0, 62, 0, 83, 0, 14, 0, 62, 0, 133, 0, 83, 0, 54, 0, 59, 0, 134, 0, 54, 0, 11, 0, 59, 0, 26, 0, 135, 0, 84, 0, 26, 0, 55, 0, 135, 0, 84, 0, 81, 0, 32, 0, 84, 0, 135, 0, 81, 0, 80, 0, 79, 0, 136, 0, 80, 0, 31, 0, 79, 0, 32, 0, 137, 0, 86, 0, 32, 0, 81, 0, 137, 0, 86, 0, 50, 0, 21, 0, 86, 0, 137, 0, 50, 0, 88, 0, 77, 0, 138, 0, 88, 0, 29, 0, 77, 0, 34, 0, 139, 0, 94, 0, 34, 0, 89, 0, 139, 0, 94, 0, 55, 0, 26, 0, 94, 0, 139, 0, 55, 0, 96, 0, 65, 0, 140, 0, 96, 0, 17, 0, 65, 0, 36, 0, 141, 0, 102, 0, 36, 0, 97, 0, 141, 0, 102, 0, 53, 0, 24, 0, 102, 0, 141, 0, 53, 0, 104, 0, 71, 0, 142, 0, 104, 0, 23, 0, 71, 0, 38, 0, 143, 0, 110, 0, 38, 0, 105, 0, 143, 0, 110, 0, 44, 0, 15, 0, 110, 0, 143, 0, 44, 0),
"lods": [0.000358111, PackedByteArray(142, 0, 3, 0, 43, 0, 142, 0, 71, 0, 3, 0, 104, 0, 71, 0, 142, 0, 104, 0, 23, 0, 71, 0, 52, 0, 23, 0, 104, 0, 52, 0, 51, 0, 23, 0, 52, 0, 9, 0, 51, 0, 140, 0, 9, 0, 52, 0, 140, 0, 65, 0, 9, 0, 96, 0, 65, 0, 140, 0, 96, 0, 17, 0, 65, 0, 96, 0, 45, 0, 17, 0, 66, 0, 45, 0, 96, 0, 66, 0, 2, 0, 45, 0, 138, 0, 11, 0, 54, 0, 88, 0, 11, 0, 138, 0, 54, 0, 11, 0, 31, 0, 88, 0, 29, 0, 11, 0, 60, 0, 29, 0, 88, 0, 60, 0, 5, 0, 29, 0, 54, 0, 31, 0, 134, 0, 134, 0, 31, 0, 80, 0, 80, 0, 31, 0, 79, 0, 80, 0, 79, 0, 136, 0, 136, 0, 79, 0, 8, 0, 136, 0, 8, 0, 49, 0, 74, 0, 33, 0, 82, 0, 82, 0, 33, 0, 87, 0, 82, 0, 87, 0, 22, 0, 82, 0, 22, 0, 69, 0, 74, 0, 27, 0, 33, 0, 90, 0, 27, 0, 74, 0, 90, 0, 35, 0, 27, 0, 126, 0, 35, 0, 90, 0, 40, 0, 35, 0, 126, 0, 40, 0, 13, 0, 35, 0, 120, 0, 37, 0, 98, 0, 46, 0, 37, 0, 120, 0, 46, 0, 19, 0, 37, 0, 98, 0, 37, 0, 25, 0, 98, 0, 25, 0, 72, 0, 72, 0, 25, 0, 39, 0, 72, 0, 39, 0, 106, 0, 106, 0, 39, 0, 111, 0, 106, 0, 111, 0, 16, 0, 106, 0, 16, 0, 63, 0, 30, 0, 107, 0, 78, 0, 78, 0, 107, 0, 64, 0, 78, 0, 64, 0, 7, 0, 30, 0, 73, 0, 107, 0, 10, 0, 73, 0, 30, 0, 10, 0, 99, 0, 73, 0, 28, 0, 99, 0, 10, 0, 28, 0, 121, 0, 99, 0, 4, 0, 121, 0, 28, 0, 4, 0, 47, 0, 121, 0, 24, 0, 53, 0, 38, 0, 38, 0, 53, 0, 105, 0, 24, 0, 141, 0, 53, 0, 38, 0, 105, 0, 143, 0, 36, 0, 141, 0, 24, 0, 38, 0, 143, 0, 110, 0, 110, 0, 143, 0, 44, 0, 110, 0, 44, 0, 15, 0, 36, 0, 97, 0, 141, 0, 18, 0, 97, 0, 36, 0, 18, 0, 67, 0, 97, 0, 20, 0, 91, 0, 48, 0, 68, 0, 91, 0, 20, 0, 48, 0, 91, 0, 75, 0, 68, 0, 127, 0, 91, 0, 0, 0, 127, 0, 68, 0, 0, 0, 41, 0, 127, 0, 48, 0, 75, 0, 6, 0, 6, 0, 75, 0, 62, 0, 62, 0, 75, 0, 83, 0, 62, 0, 83, 0, 14, 0, 14, 0, 83, 0, 42, 0, 42, 0, 83, 0, 70, 0, 42, 0, 70, 0, 1, 0, 12, 0, 89, 0, 34, 0, 12, 0, 61, 0, 89, 0, 34, 0, 89, 0, 139, 0, 34, 0, 139, 0, 26, 0, 26, 0, 139, 0, 55, 0, 26, 0, 55, 0, 135, 0, 26, 0, 135, 0, 32, 0, 32, 0, 135, 0, 81, 0, 32, 0, 81, 0, 137, 0, 32, 0, 137, 0, 86, 0, 86, 0, 137, 0, 50, 0, 86, 0, 50, 0, 21, 0), 0.000413511, PackedByteArray(52, 0, 3, 0, 43, 0, 52, 0, 71, 0, 3, 0, 52, 0, 23, 0, 71, 0, 52, 0, 51, 0, 23, 0, 52, 0, 9, 0, 51, 0, 52, 0, 65, 0, 9, 0, 52, 0, 17, 0, 65, 0, 52, 0, 45, 0, 17, 0, 66, 0, 45, 0, 52, 0, 66, 0, 2, 0, 45, 0, 88, 0, 11, 0, 80, 0, 60, 0, 11, 0, 88, 0, 60, 0, 5, 0, 11, 0, 80, 0, 11, 0, 8, 0, 80, 0, 8, 0, 49, 0, 74, 0, 22, 0, 69, 0, 74, 0, 27, 0, 22, 0, 40, 0, 27, 0, 74, 0, 40, 0, 13, 0, 27, 0, 98, 0, 16, 0, 63, 0, 98, 0, 25, 0, 16, 0, 46, 0, 25, 0, 98, 0, 46, 0, 19, 0, 25, 0, 10, 0, 64, 0, 7, 0, 10, 0, 99, 0, 64, 0, 4, 0, 99, 0, 10, 0, 4, 0, 47, 0, 99, 0, 18, 0, 53, 0, 24, 0, 18, 0, 67, 0, 53, 0, 24, 0, 53, 0, 44, 0, 24, 0, 44, 0, 15, 0, 20, 0, 75, 0, 48, 0, 48, 0, 75, 0, 6, 0, 68, 0, 75, 0, 20, 0, 0, 0, 75, 0, 68, 0, 0, 0, 41, 0, 75, 0, 6, 0, 75, 0, 62, 0, 62, 0, 75, 0, 14, 0, 14, 0, 75, 0, 42, 0, 42, 0, 75, 0, 70, 0, 42, 0, 70, 0, 1, 0, 12, 0, 89, 0, 26, 0, 12, 0, 61, 0, 89, 0, 26, 0, 89, 0, 81, 0, 26, 0, 81, 0, 50, 0, 26, 0, 50, 0, 21, 0)],
"material": SubResource("StandardMaterial3D_bml01"),
"name": "CardPicGradient",
"primitive": 3,
"uv_scale": Vector4(1.92522, 3.39241, 0, 0),
"vertex_count": 144,
"vertex_data": PackedByteArray(68, 4, 254, 255, 254, 255, 255, 191, 0, 0, 0, 0, 254, 255, 255, 191, 68, 4, 254, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 68, 4, 254, 255, 255, 127, 255, 191, 68, 4, 254, 255, 255, 127, 255, 191, 40, 2, 255, 127, 254, 255, 255, 191, 0, 0, 0, 0, 255, 127, 255, 191, 0, 0, 0, 0, 255, 127, 255, 191, 40, 2, 255, 127, 0, 0, 255, 191, 40, 2, 255, 127, 255, 127, 255, 191, 40, 2, 255, 127, 255, 127, 255, 191, 68, 4, 254, 255, 255, 191, 255, 191, 68, 4, 254, 255, 255, 191, 255, 191, 252, 0, 255, 63, 254, 255, 255, 191, 0, 0, 0, 0, 255, 63, 255, 191, 0, 0, 0, 0, 255, 63, 255, 191, 84, 3, 255, 191, 0, 0, 255, 191, 68, 4, 254, 255, 255, 63, 255, 191, 68, 4, 254, 255, 255, 63, 255, 191, 84, 3, 255, 191, 254, 255, 255, 191, 0, 0, 0, 0, 255, 191, 255, 191, 0, 0, 0, 0, 255, 191, 255, 191, 252, 0, 255, 63, 0, 0, 255, 191, 40, 2, 255, 127, 255, 63, 255, 191, 40, 2, 255, 127, 255, 63, 255, 191, 40, 2, 255, 127, 255, 191, 255, 191, 40, 2, 255, 127, 255, 191, 255, 191, 84, 3, 255, 191, 255, 127, 255, 191, 84, 3, 255, 191, 255, 127, 255, 191, 252, 0, 255, 63, 255, 127, 255, 191, 252, 0, 255, 63, 255, 127, 255, 191, 252, 0, 255, 63, 255, 191, 255, 191, 252, 0, 255, 63, 255, 191, 255, 191, 84, 3, 255, 191, 255, 191, 255, 191, 84, 3, 255, 191, 255, 191, 255, 191, 84, 3, 255, 191, 255, 63, 255, 191, 84, 3, 255, 191, 255, 63, 255, 191, 252, 0, 255, 63, 255, 63, 255, 191, 252, 0, 255, 63, 255, 63, 255, 191, 68, 4, 254, 255, 254, 223, 255, 191, 68, 4, 254, 255, 254, 223, 255, 191, 96, 0, 255, 31, 254, 255, 255, 191, 0, 0, 0, 0, 255, 31, 255, 191, 0, 0, 0, 0, 255, 31, 255, 191, 228, 3, 254, 223, 0, 0, 255, 191, 68, 4, 254, 255, 255, 95, 255, 191, 68, 4, 254, 255, 255, 95, 255, 191, 184, 2, 255, 159, 254, 255, 255, 191, 0, 0, 0, 0, 255, 159, 255, 191, 0, 0, 0, 0, 255, 159, 255, 191, 140, 1, 255, 95, 0, 0, 255, 191, 40, 2, 255, 127, 255, 31, 255, 191, 40, 2, 255, 127, 255, 31, 255, 191, 40, 2, 255, 127, 255, 159, 255, 191, 40, 2, 255, 127, 255, 159, 255, 191, 228, 3, 254, 223, 255, 127, 255, 191, 228, 3, 254, 223, 255, 127, 255, 191, 140, 1, 255, 95, 255, 127, 255, 191, 140, 1, 255, 95, 255, 127, 255, 191, 68, 4, 254, 255, 255, 159, 255, 191, 68, 4, 254, 255, 255, 159, 255, 191, 140, 1, 255, 95, 254, 255, 255, 191, 0, 0, 0, 0, 255, 95, 255, 191, 0, 0, 0, 0, 255, 95, 255, 191, 184, 2, 255, 159, 0, 0, 255, 191, 68, 4, 254, 255, 255, 31, 255, 191, 68, 4, 254, 255, 255, 31, 255, 191, 228, 3, 254, 223, 254, 255, 255, 191, 0, 0, 0, 0, 254, 223, 255, 191, 0, 0, 0, 0, 254, 223, 255, 191, 96, 0, 255, 31, 0, 0, 255, 191, 40, 2, 255, 127, 255, 95, 255, 191, 40, 2, 255, 127, 255, 95, 255, 191, 40, 2, 255, 127, 254, 223, 255, 191, 40, 2, 255, 127, 254, 223, 255, 191, 184, 2, 255, 159, 255, 127, 255, 191, 184, 2, 255, 159, 255, 127, 255, 191, 96, 0, 255, 31, 255, 127, 255, 191, 96, 0, 255, 31, 255, 127, 255, 191, 252, 0, 255, 63, 255, 159, 255, 191, 252, 0, 255, 63, 255, 159, 255, 191, 252, 0, 255, 63, 254, 223, 255, 191, 252, 0, 255, 63, 254, 223, 255, 191, 140, 1, 255, 95, 255, 191, 255, 191, 140, 1, 255, 95, 255, 191, 255, 191, 96, 0, 255, 31, 255, 191, 255, 191, 96, 0, 255, 31, 255, 191, 255, 191, 84, 3, 255, 191, 255, 159, 255, 191, 84, 3, 255, 191, 255, 159, 255, 191, 84, 3, 255, 191, 254, 223, 255, 191, 84, 3, 255, 191, 254, 223, 255, 191, 228, 3, 254, 223, 255, 191, 255, 191, 228, 3, 254, 223, 255, 191, 255, 191, 184, 2, 255, 159, 255, 191, 255, 191, 184, 2, 255, 159, 255, 191, 255, 191, 84, 3, 255, 191, 255, 31, 255, 191, 84, 3, 255, 191, 255, 31, 255, 191, 84, 3, 255, 191, 255, 95, 255, 191, 84, 3, 255, 191, 255, 95, 255, 191, 228, 3, 254, 223, 255, 63, 255, 191, 228, 3, 254, 223, 255, 63, 255, 191, 184, 2, 255, 159, 255, 63, 255, 191, 184, 2, 255, 159, 255, 63, 255, 191, 252, 0, 255, 63, 255, 31, 255, 191, 252, 0, 255, 63, 255, 31, 255, 191, 252, 0, 255, 63, 255, 95, 255, 191, 252, 0, 255, 63, 255, 95, 255, 191, 140, 1, 255, 95, 255, 63, 255, 191, 140, 1, 255, 95, 255, 63, 255, 191, 96, 0, 255, 31, 255, 63, 255, 191, 96, 0, 255, 31, 255, 63, 255, 191, 96, 0, 255, 31, 255, 95, 255, 191, 96, 0, 255, 31, 255, 95, 255, 191, 140, 1, 255, 95, 255, 95, 255, 191, 140, 1, 255, 95, 255, 95, 255, 191, 140, 1, 255, 95, 255, 31, 255, 191, 140, 1, 255, 95, 255, 31, 255, 191, 184, 2, 255, 159, 255, 95, 255, 191, 184, 2, 255, 159, 255, 95, 255, 191, 228, 3, 254, 223, 255, 95, 255, 191, 228, 3, 254, 223, 255, 95, 255, 191, 228, 3, 254, 223, 255, 31, 255, 191, 228, 3, 254, 223, 255, 31, 255, 191, 184, 2, 255, 159, 254, 223, 255, 191, 184, 2, 255, 159, 254, 223, 255, 191, 228, 3, 254, 223, 254, 223, 255, 191, 228, 3, 254, 223, 254, 223, 255, 191, 228, 3, 254, 223, 255, 159, 255, 191, 228, 3, 254, 223, 255, 159, 255, 191, 96, 0, 255, 31, 254, 223, 255, 191, 96, 0, 255, 31, 254, 223, 255, 191, 140, 1, 255, 95, 254, 223, 255, 191, 140, 1, 255, 95, 254, 223, 255, 191, 140, 1, 255, 95, 255, 159, 255, 191, 140, 1, 255, 95, 255, 159, 255, 191, 96, 0, 255, 31, 255, 159, 255, 191, 96, 0, 255, 31, 255, 159, 255, 191, 184, 2, 255, 159, 255, 159, 255, 191, 184, 2, 255, 159, 255, 159, 255, 191, 184, 2, 255, 159, 255, 31, 255, 191, 184, 2, 255, 159, 255, 31, 255, 191, 96, 0, 255, 31, 255, 31, 255, 191, 96, 0, 255, 31, 255, 31, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_12ib6")

[sub_resource type="Animation" id="Animation_wuqrw"]
resource_name = "PlayCard"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3, 0.7),
"transitions": PackedFloat32Array(2.63902, 1, 0.210224),
"update": 0,
"values": [Vector3(1, 1, 1), Vector3(1.195, 1.195, 1.195), Vector3(1.245, 1.245, 1.245)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CardBg2:material_override:albedo_color")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.5, 0.7, 1),
"transitions": PackedFloat32Array(1, 1, 2.2974, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1), Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("CardBg:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, false]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("CardBg2:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.7),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, true]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("CardBg2:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.5, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector3(0, -0.236273, 0), Vector3(0, -0.236273, 0), Vector3(0, -0.173783, 0)]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("CardBg2:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.5, 0.6, 0.7, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector3(1.459, 0.491, 0.5), Vector3(1.459, 0.491, 0.5), Vector3(1.459, 0.306601, 0.5), Vector3(1.459, 0.377163, 0.5), Vector3(1.459, 0.340927, 0.5)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("CardPicGradient:visible")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, false]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("CardFront:visible")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, false]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("CardBack:visible")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, false]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("RarityBar:visible")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, false]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("ColorBar:visible")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, false]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("PowerBg:visible")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, false]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("NameBg:visible")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, false]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("CostBg:visible")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, false]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("FloatText:visible")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, false]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("CardBg:visible")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [true, false]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("OnArena:visible")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.7),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, true]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("CardText:visible")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("CostText:visible")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("PowerText:visible")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("NameText:visible")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("CardCompanion:visible")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, false]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("CastableVFX:visible")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_dm6xb"]
resource_name = "RESET"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(2.63902),
"update": 0,
"values": [Vector3(1, 1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CardBg:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [true]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("CardBg2:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [true]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("CardBg2:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(0, -0.236273, 0)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("CardBg2:scale")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(1.459, 0.491, 0.5)]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("CardPicGradient:visible")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [true]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("CardFront:visible")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [true]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("CardBack:visible")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [true]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("RarityBar:visible")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [true]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("ColorBar:visible")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [true]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("PowerBg:visible")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [true]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("NameBg:visible")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [true]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("CostBg:visible")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [true]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("FloatText:visible")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [true]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("CardBg2:material_override:albedo_color")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 0)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("OnArena:visible")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("CardText:visible")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("CostText:visible")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("PowerText:visible")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("NameText:visible")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("CardCompanion:visible")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("CastableVFX:visible")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_c7mx1"]
_data = {
"PlayCard": SubResource("Animation_wuqrw"),
"RESET": SubResource("Animation_dm6xb")
}

[sub_resource type="ViewportTexture" id="ViewportTexture_4hi5t"]
resource_name = "_albedo"
viewport_path = NodePath("CardBg/SubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_8as5m"]
resource_local_to_scene = true
cull_mode = 2
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_4hi5t")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_6dglo"]
resource_name = "CardPicFG.001"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("32_bo0v1")

[sub_resource type="ArrayMesh" id="ArrayMesh_w1jay"]
_surfaces = [{
"aabb": AABB(0.0495976, -1.33334, -1.66119, 9.99868e-06, 3.04757, 3.34225),
"format": 34896613377,
"index_count": 6,
"index_data": PackedByteArray(1, 0, 3, 0, 0, 0, 1, 0, 2, 0, 3, 0),
"name": "CardPicFG.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 4,
"vertex_data": PackedByteArray(0, 0, 254, 255, 254, 255, 0, 0, 0, 0, 254, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 255, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_wyg3f"]
resource_name = "DRC_Card_Model_Test03_CardPicFgPLayed_Cube_003"
_surfaces = [{
"aabb": AABB(0.0495976, -1.33334, -1.66119, 9.99868e-06, 3.04757, 3.34225),
"attribute_data": PackedByteArray(192, 42, 82, 34, 48, 213, 82, 34, 48, 213, 187, 206, 192, 42, 187, 206),
"format": 34896613399,
"index_count": 6,
"index_data": PackedByteArray(1, 0, 3, 0, 0, 0, 1, 0, 2, 0, 3, 0),
"material": SubResource("StandardMaterial3D_6dglo"),
"name": "CardPicFG.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 4,
"vertex_data": PackedByteArray(0, 0, 254, 255, 254, 255, 255, 191, 0, 0, 254, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 0, 0, 0, 0, 254, 255, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_w1jay")

[node name="Card" type="CharacterBody3D"]
top_level = true
collision_mask = 2
script = ExtResource("1_dlg4w")

[node name="AbilityTextFloat" type="Label3D" parent="."]
transform = Transform3D(0.35, 0, 0, 0, 0.35, 3.73034e-15, 0, -3.73034e-15, 0.35, 0.979606, 0.865356, 0)
visible = false
text = "Keyword 1
This keyword means this and that, hope it clear.

Keyword 2
This keyword means this and that, hope it clear.
"
font = ExtResource("3_hshn5")
font_size = 65
outline_size = 44
horizontal_alignment = 0
vertical_alignment = 0
line_spacing = -1.735
autowrap_mode = 2
width = 700.0

[node name="KeywordNotes" type="Label3D" parent="."]
transform = Transform3D(0.35, 0, 0, 0, 0.35, 3.73034e-15, 0, -3.73034e-15, 0.35, 0.98, 0.863, 0)
visible = false
modulate = Color(0.65098, 0.65098, 0.65098, 1)
text = "Keyword 1
This keyword means this and that, hope it clear.

Keyword 2
This keyword means this and that, hope it clear."
font = ExtResource("3_hshn5")
font_size = 65
outline_size = 44
horizontal_alignment = 0
vertical_alignment = 0
line_spacing = -1.735
autowrap_mode = 2
width = 700.0

[node name="FloatText" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 1.06581e-14, 0, -1.06581e-14, 1, 0.957, 0.948511, -0.816845)
layers = 524289
modulate = Color(1, 1, 1, 0)
outline_modulate = Color(0, 0, 0, 0)
text = "1
"
font = ExtResource("3_lfmlx")
font_size = 84
outline_size = 42
horizontal_alignment = 2
autowrap_mode = 2
width = 250.0

[node name="AnimationPlayer" type="AnimationPlayer" parent="FloatText"]
libraries = {
"": SubResource("AnimationLibrary_28t7y")
}

[node name="AbilityOnCard" type="Node3D" parent="."]

[node name="AbilitySubViewport" type="SubViewport" parent="AbilityOnCard"]
transparent_bg = true
size = Vector2i(800, 530)

[node name="AbilityContainer" type="VBoxContainer" parent="AbilityOnCard/AbilitySubViewport"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -368.5
offset_top = -258.0
offset_right = 368.5
offset_bottom = 258.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = -1
alignment = 2

[node name="CastConContainer" type="HBoxContainer" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer"]
layout_mode = 2
alignment = 1

[node name="CastConIcon" type="TextureRect" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer/CastConContainer"]
z_index = 1
layout_mode = 2
size_flags_horizontal = 8
texture = ExtResource("3_f8aow")

[node name="CastConBanner" type="MarginContainer" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer/CastConContainer"]
layout_mode = 2
size_flags_horizontal = 0
theme_override_constants/margin_left = -55
theme_override_constants/margin_top = 1
theme_override_constants/margin_right = -50
theme_override_constants/margin_bottom = -1

[node name="CastConBG" type="NinePatchRect" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer/CastConContainer/CastConBanner"]
layout_mode = 2
texture = ExtResource("3_siap0")
region_rect = Rect2(2, 2, 274, 96)
patch_margin_left = 67
patch_margin_top = 24
patch_margin_right = 66
patch_margin_bottom = 38

[node name="CastConFG" type="MarginContainer" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer/CastConContainer/CastConBanner"]
layout_mode = 2
theme_override_constants/margin_left = 55
theme_override_constants/margin_top = -20
theme_override_constants/margin_right = 60

[node name="CastConText" type="Label" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer/CastConContainer/CastConBanner/CastConFG"]
layout_mode = 2
theme_override_colors/font_color = Color(0.882353, 0.513726, 0.937255, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_x = -2
theme_override_constants/shadow_offset_y = 5
theme_override_constants/outline_size = 12
theme_override_constants/shadow_outline_size = 15
theme_override_font_sizes/font_size = 46
text = "Bounce"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CastConText_TH" type="Label" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer/CastConContainer/CastConBanner/CastConFG"]
visible = false
layout_mode = 2
theme_override_colors/font_color = Color(0.882353, 0.513726, 0.937255, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_x = -2
theme_override_constants/shadow_offset_y = 5
theme_override_constants/outline_size = 12
theme_override_constants/shadow_outline_size = 15
theme_override_font_sizes/font_size = 61
text = " เด้ง "
horizontal_alignment = 1
vertical_alignment = 1

[node name="AbilityTextOnCard" type="MarginContainer" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer"]
layout_mode = 2
theme_override_constants/margin_left = 170
theme_override_constants/margin_right = 60

[node name="AbilityDetail" type="RichTextLabel" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer/AbilityTextOnCard"]
visible = false
clip_contents = false
layout_mode = 2
size_flags_horizontal = 3
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_x = 0
theme_override_constants/shadow_offset_y = 2
theme_override_constants/shadow_outline_size = 20
theme_override_constants/line_separation = -5
theme_override_constants/table_v_separation = -1
theme_override_fonts/normal_font = ExtResource("3_hshn5")
theme_override_fonts/italics_font = ExtResource("4_ql56j")
theme_override_font_sizes/normal_font_size = 33
bbcode_enabled = true
text = "[center]I'm [img]res://Assets/3D models/DRC_Card_Design_08_UI_CMN_CastConNoBg2.png[/img][color=e183ef]Bounce[/color]
[color=b2ada8]On enter:[/color] [color=ff9e1e]Unsummon[/color] random enemy, summon two 1/1 Boom Bots.
[i]WARNING: Bots may explode!![/i][/center]"
fit_content = true
scroll_active = false

[node name="AbilityDetailENG" type="RichTextLabel" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer/AbilityTextOnCard"]
clip_contents = false
layout_mode = 2
size_flags_horizontal = 3
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_x = 0
theme_override_constants/shadow_offset_y = 2
theme_override_constants/shadow_outline_size = 20
theme_override_constants/line_separation = -5
theme_override_constants/table_v_separation = -1
theme_override_fonts/normal_font = ExtResource("3_hshn5")
theme_override_fonts/italics_font = ExtResource("4_ql56j")
theme_override_font_sizes/normal_font_size = 40
bbcode_enabled = true
text = "[center]I'm [img]res://Assets/3D models/DRC_Card_Design_08_UI_CMN_CastConNoBg2.png[/img][color=e183ef]Bounce[/color]
[color=b2ada8]On enter:[/color] [color=ff9e1e]Unsummon[/color] random enemy, summon two 1/1 Boom Bots.
[i]WARNING: Bots may explode!![/i][/center]"
fit_content = true
scroll_active = false

[node name="AbilityDetailTH" type="RichTextLabel" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer/AbilityTextOnCard"]
visible = false
clip_contents = false
layout_mode = 2
size_flags_horizontal = 3
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/line_separation = -5
theme_override_constants/table_v_separation = -1
theme_override_constants/outline_size = 20
theme_override_font_sizes/normal_font_size = 33
bbcode_enabled = true
text = "[center]ฉัน [img]res://Assets/3D models/DRC_Card_Design_08_UI_CMN_CastConNoBg2.png[/img][color=e183ef]เด้ง[/color]
[color=b2ada8]ลงสนาม:[/color] [color=ff9e1e]เรียกคืน[/color] ศัตรูแบบสุ่ม, เรียกบูมบอท 1/1 สองตัว
[i]คำเตือน: หุ่นอาจระเบิดได้!![/i][/center]"
fit_content = true
scroll_active = false

[node name="SiliencedVFX" type="TextureRect" parent="AbilityOnCard/AbilitySubViewport/AbilityContainer/AbilityTextOnCard"]
visible = false
layout_mode = 2
size_flags_horizontal = 3
texture = ExtResource("8_heioj")
expand_mode = 1

[node name="CostOnCard" type="Node3D" parent="."]

[node name="CostSubViewport" type="SubViewport" parent="CostOnCard"]
transparent_bg = true
size = Vector2i(780, 347)

[node name="CostText2D" type="RichTextLabel" parent="CostOnCard/CostSubViewport"]
material = SubResource("CanvasItemMaterial_odv6a")
clip_contents = false
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 6
theme_override_constants/shadow_outline_size = 50
theme_override_constants/line_separation = -145
theme_override_constants/table_h_separation = 7835
theme_override_fonts/normal_font = ExtResource("4_tpole")
theme_override_font_sizes/normal_font_size = 223
bbcode_enabled = true
text = "[center]9"
fit_content = true
scroll_active = false
autowrap_mode = 0

[node name="PowerOnCard" type="Node3D" parent="."]

[node name="PowerSubViewport" type="SubViewport" parent="PowerOnCard"]
transparent_bg = true
size = Vector2i(780, 347)

[node name="PowerText2D" type="RichTextLabel" parent="PowerOnCard/PowerSubViewport"]
clip_contents = false
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 6
theme_override_constants/shadow_outline_size = 50
theme_override_constants/line_separation = -145
theme_override_constants/table_h_separation = 7835
theme_override_fonts/normal_font = ExtResource("4_tpole")
theme_override_font_sizes/normal_font_size = 223
bbcode_enabled = true
text = "[center]9"
fit_content = true
scroll_active = false
autowrap_mode = 0

[node name="NameOnCard" type="Node3D" parent="."]

[node name="NameSubViewport" type="SubViewport" parent="NameOnCard"]
transparent_bg = true
size = Vector2i(845, 210)

[node name="NameText2D" type="RichTextLabel" parent="NameOnCard/NameSubViewport"]
clip_contents = false
custom_minimum_size = Vector2(500, 0)
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -250.0
offset_top = -45.0
offset_right = 250.0
offset_bottom = 45.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
theme_override_colors/font_shadow_color = Color(0, 0, 0, 1)
theme_override_constants/shadow_offset_y = 6
theme_override_constants/shadow_outline_size = 45
theme_override_constants/line_separation = -10
theme_override_constants/table_h_separation = 7835
theme_override_fonts/normal_font = ExtResource("3_hshn5")
theme_override_font_sizes/normal_font_size = 73
bbcode_enabled = true
text = "[center]Axoxo Jr. "
fit_content = true
scroll_active = false

[node name="CardText" type="MeshInstance3D" parent="."]
transform = Transform3D(0.940427, 0, 0, 0, -4.02088e-08, -0.62237, 0, 0.91987, -2.72046e-08, 0.0632194, -0.634444, 0.0272855)
layers = 524289
material_override = SubResource("StandardMaterial3D_titv1")
mesh = SubResource("PlaneMesh_8wjqt")
skeleton = NodePath("../AbilityOnCard")

[node name="CostBg" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.235642, 0.00551444)
layers = 524289
material_override = SubResource("StandardMaterial3D_2mthu")
mesh = SubResource("ArrayMesh_7urhl")
skeleton = NodePath("")

[node name="CostText" type="MeshInstance3D" parent="."]
transform = Transform3D(0.478, 0, 0, 0, -1.37691e-08, -0.272, 0, 0.315, -1.18895e-08, -0.658149, 0.700551, 0.0404162)
layers = 524289
material_override = SubResource("StandardMaterial3D_bqove")
mesh = SubResource("PlaneMesh_8wjqt")
skeleton = NodePath("../AbilityOnCard")

[node name="PowerText" type="MeshInstance3D" parent="."]
transform = Transform3D(0.541726, 0, 0, 0, -1.37691e-08, -0.302863, 0, 0.315, -1.32386e-08, -0.666011, -1.21867, 0.0404162)
layers = 524289
material_override = SubResource("StandardMaterial3D_7u0ls")
mesh = SubResource("PlaneMesh_8wjqt")
skeleton = NodePath("../AbilityOnCard")

[node name="PowerBg" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, 0.004)
layers = 524289
material_override = SubResource("StandardMaterial3D_3tcp4")
mesh = SubResource("ArrayMesh_atnjb")
skeleton = NodePath("")

[node name="NameText" type="MeshInstance3D" parent="."]
transform = Transform3D(0.656323, 0, 0, 0, -1.81402e-08, -0.161951, 0, 0.415, -7.07912e-09, 0.144811, 0.753399, 0.0466198)
layers = 524289
material_override = SubResource("StandardMaterial3D_ujq4y")
mesh = SubResource("PlaneMesh_8wjqt")
skeleton = NodePath("../AbilityOnCard")

[node name="NameBg" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, -0.015, -0.236, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_u0viu")
mesh = SubResource("ArrayMesh_jj8nb")
skeleton = NodePath("")

[node name="CardFront" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, -0.003)
layers = 524289
material_override = SubResource("StandardMaterial3D_4q0or")
mesh = SubResource("ArrayMesh_imadi")
skeleton = NodePath("")

[node name="CardBack" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, 0)
material_override = SubResource("StandardMaterial3D_47vpe")
mesh = SubResource("ArrayMesh_5cvg6")
skeleton = NodePath("")

[node name="ColorBar" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.235642, 0.00666136)
layers = 524289
material_override = SubResource("StandardMaterial3D_bwgkt")
mesh = SubResource("ArrayMesh_k0ke4")
skeleton = NodePath("")

[node name="RarityBar" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.235642, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_610e1")
mesh = SubResource("ArrayMesh_cpfdt")
skeleton = NodePath("")

[node name="CardBg" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_uadp2")
mesh = SubResource("ArrayMesh_hsh2j")
skeleton = NodePath("")
metadata/_edit_lock_ = true

[node name="SubViewport" type="SubViewport" parent="CardBg"]
transparent_bg = true
size = Vector2i(254, 188)

[node name="NinePatchRect" type="NinePatchRect" parent="CardBg/SubViewport"]
offset_right = 254.0
offset_bottom = 188.0
texture = ExtResource("21_bglvw")
region_rect = Rect2(0, 0, 254, 188)
patch_margin_left = 47
patch_margin_top = 37
patch_margin_right = 35
patch_margin_bottom = 49

[node name="CardBg2" type="MeshInstance3D" parent="."]
transform = Transform3D(-6.37749e-08, 0, -0.5, 0, 0.491, 0, 1.459, 0, -2.18557e-08, 0, -0.236273, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_1v286")
mesh = SubResource("ArrayMesh_hsh2j")
skeleton = NodePath("")
metadata/_edit_lock_ = true

[node name="SubViewport" type="SubViewport" parent="CardBg2"]
transparent_bg = true
size = Vector2i(254, 188)

[node name="NinePatchRect" type="NinePatchRect" parent="CardBg2/SubViewport"]
offset_right = 254.0
offset_bottom = 188.0
texture = ExtResource("21_bglvw")
region_rect = Rect2(0, 0, 254, 188)
patch_margin_left = 47
patch_margin_top = 37
patch_margin_right = 35
patch_margin_bottom = 49

[node name="CardPicGradient" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, -6.29812e-05)
layers = 524289
material_override = SubResource("StandardMaterial3D_d4a2i")
mesh = SubResource("ArrayMesh_imadi")
skeleton = NodePath("")

[node name="SubViewport" type="SubViewport" parent="CardPicGradient"]
transparent_bg = true
size = Vector2i(1920, 1080)

[node name="NinePatchRect" type="NinePatchRect" parent="CardPicGradient/SubViewport"]
offset_top = 780.0
offset_right = 1920.0
offset_bottom = 1080.0
texture = ExtResource("22_b4rcw")
region_rect = Rect2(0.062, 72.3668, 88, 222.579)
axis_stretch_horizontal = 2

[node name="NinePatchRect2" type="NinePatchRect" parent="CardPicGradient/SubViewport"]
offset_top = 780.0
offset_right = 1920.0
offset_bottom = 1080.0
texture = ExtResource("22_b4rcw")
region_rect = Rect2(0.062, 72.3668, 88, 222.579)
axis_stretch_horizontal = 2

[node name="CardCompanion" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.235642, 0)
visible = false
material_override = SubResource("StandardMaterial3D_opdvl")
mesh = SubResource("ArrayMesh_s5rux")
skeleton = NodePath("")

[node name="CastableVFX" type="MeshInstance3D" parent="."]
transform = Transform3D(-1.1478e-09, 0, -0.515801, 0, 0.508655, 0, 0.0262587, 0, -2.25464e-08, 0, -0.237, 0)
visible = false
layers = 524289
material_override = ExtResource("25_hogme")
mesh = SubResource("ArrayMesh_hsh2j")
skeleton = NodePath("")

[node name="CastConditionText" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 1.06581e-14, 0, -1.06581e-14, 1, -0.482378, 0.616614, 0.0303328)
visible = false
alpha_cut = 1
text = "🩸
"
font_size = 26
outline_size = 14
width = 250.0

[node name="PowerParticle" type="CPUParticles3D" parent="."]
transform = Transform3D(0.493, 0, 0, 0, 0.314, -1.82324e-16, 0, 8.17852e-15, 0.007, -0.640688, -1.16494, 0.0192872)
visible = false
material_override = SubResource("ShaderMaterial_cfp2b")
amount = 1
lifetime = 3.0
local_coords = true
mesh = SubResource("QuadMesh_wkgxl")
gravity = Vector3(0, 0, 0)
color = Color(0, 0, 0, 1)
color_ramp = SubResource("Gradient_tgfil")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(0.942, 0, 0, 0, 1.326, 1.07171e-16, 0, -7.10543e-15, 0.02, -0.000188895, -0.334499, 0)
visible = false
shape = SubResource("ConvexPolygonShape3D_0tr8e")

[node name="CollisionHand" type="CollisionShape3D" parent="."]
transform = Transform3D(0.942, 0, 0, 0, 1.731, 2.22045e-16, 0, 0, 0.02, -0.000188895, -0.707316, 0)
visible = false
shape = SubResource("ConvexPolygonShape3D_0tr8e")
disabled = true

[node name="Current" type="MeshInstance3D" parent="."]
transform = Transform3D(1.008, 0, 0, 0, 1.364, -3.33067e-16, 0, 3.55271e-14, 0.011, 0, -0.259865, -0.198427)
visible = false
layers = 524289
material_override = SubResource("StandardMaterial3D_pq7ya")
mesh = SubResource("ArrayMesh_wagqq")
skeleton = NodePath("")

[node name="MostRecent" type="MeshInstance3D" parent="."]
transform = Transform3D(0.96674, 0, 0, 0, 1.32262, -3.33067e-16, 0, 3.44492e-14, 0.011, 7.15256e-07, -0.253103, -0.200617)
visible = false
layers = 524289
material_override = SubResource("StandardMaterial3D_cw4lr")
cast_shadow = 0
mesh = SubResource("ArrayMesh_wagqq")
skeleton = NodePath("")

[node name="IgniteParticle" type="CPUParticles3D" parent="."]
transform = Transform3D(1.12514, 0, 0, 0, 1.09063, -2.34416e-16, 0, 2.84067e-14, 0.009, 0, -0.155043, 0)
visible = false
material_override = SubResource("ShaderMaterial_r0weg")
amount = 1
local_coords = true
mesh = SubResource("QuadMesh_k0tpp")
gravity = Vector3(0, 0, 0)
color = Color(0.929412, 0.298039, 0, 1)
color_ramp = SubResource("Gradient_d7a0i")

[node name="DivineParticle" type="CPUParticles3D" parent="."]
transform = Transform3D(1.12514, 0, 0, 0, 1.09063, -2.34416e-16, 0, 2.84067e-14, 0.009, 0, -0.155043, 0)
visible = false
material_override = SubResource("ShaderMaterial_r0weg")
amount = 1
local_coords = true
mesh = SubResource("QuadMesh_k0tpp")
gravity = Vector3(0, 0, 0)
color = Color(0.595277, 0.548848, 0.320366, 1)
color_ramp = SubResource("Gradient_d7a0i")

[node name="DarkParticle" type="CPUParticles3D" parent="."]
transform = Transform3D(1.12514, 0, 0, 0, 1.09063, -2.34416e-16, 0, 2.84067e-14, 0.009, 0, -0.155043, 0)
visible = false
material_override = SubResource("ShaderMaterial_r0weg")
amount = 1
local_coords = true
mesh = SubResource("QuadMesh_k0tpp")
gravity = Vector3(0, 0, 0)
color = Color(0.115675, 0.000154139, 0.310341, 1)
color_ramp = SubResource("Gradient_d7a0i")

[node name="PlayerParticle" type="CPUParticles3D" parent="."]
transform = Transform3D(1.06557, 0.000893058, -2.60494e-06, -0.00100093, 0.950752, -7.13716e-07, 0.000277488, 6.80846e-05, 0.0100006, 0, -0.109456, 0)
visible = false
layers = 9
material_override = SubResource("ShaderMaterial_cg4at")
amount = 1
lifetime = 3.0
local_coords = true
mesh = SubResource("QuadMesh_mkmp3")
gravity = Vector3(0, 0, 0)
scale_amount_min = 0.0
scale_amount_max = 0.0
color = Color(0, 0.839216, 0.0823529, 1)
color_ramp = SubResource("Gradient_0u1n2")

[node name="EnemyParticle" type="CPUParticles3D" parent="."]
transform = Transform3D(1.07135, 0, 0, 0, 1.0162, -2.60462e-16, 0, 2.64683e-14, 0.01, 0, -0.13082, 0)
visible = false
layers = 9
material_override = SubResource("ShaderMaterial_cg4at")
amount = 1
lifetime = 3.0
local_coords = true
mesh = SubResource("QuadMesh_717bq")
gravity = Vector3(0, 0, 0)
scale_amount_min = 0.0
scale_amount_max = 0.0
color = Color(1, 0, 0, 1)
color_ramp = SubResource("Gradient_etra5")

[node name="Draggable" type="Node" parent="."]
script = ExtResource("5_4eqpv")

[node name="CardPicBG" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.235642, 0)
visible = false
mesh = SubResource("ArrayMesh_k0we6")
skeleton = NodePath("")

[node name="CardPicGradient2" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.235642, 0.0359613)
visible = false
layers = 524289
material_override = SubResource("StandardMaterial3D_yoaeo")
mesh = SubResource("ArrayMesh_4w0hb")
skeleton = NodePath("")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_c7mx1")
}

[node name="OnArena" type="Node3D" parent="."]
visible = false

[node name="PowerText" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(0.541726, 0, 0, 0, -1.37691e-08, -0.302863, 0, 0.315, -1.32386e-08, -0.666011, -0.819845, 0.0404162)
layers = 524297
material_override = SubResource("StandardMaterial3D_7u0ls")
mesh = SubResource("PlaneMesh_8wjqt")
skeleton = NodePath("../../AbilityOnCard")

[node name="PowerBg" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, 0.146876, 0.004)
layers = 524297
material_override = SubResource("StandardMaterial3D_3tcp4")
mesh = SubResource("ArrayMesh_atnjb")
skeleton = NodePath("")

[node name="ColorBar" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.309, 0.007)
layers = 524297
material_override = SubResource("StandardMaterial3D_bwgkt")
mesh = SubResource("ArrayMesh_k0ke4")
skeleton = NodePath("")

[node name="RarityBar" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, 0.184947, 0)
layers = 524297
material_override = SubResource("StandardMaterial3D_610e1")
mesh = SubResource("ArrayMesh_cpfdt")
skeleton = NodePath("")

[node name="CardCompanion" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, 0.184947, 0)
visible = false
layers = 524297
material_override = SubResource("StandardMaterial3D_opdvl")
mesh = SubResource("ArrayMesh_s5rux")
skeleton = NodePath("")

[node name="CardBack" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.33, 0, 0.49, 0, -2.14186e-08, 0, -0.18, 0)
layers = 9
material_override = SubResource("StandardMaterial3D_47vpe")
mesh = SubResource("ArrayMesh_5cvg6")
skeleton = NodePath("")

[node name="CardBg" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.339102, 0, 0.49, 0, -2.14186e-08, 0, -0.18454, 0)
layers = 524297
material_override = SubResource("StandardMaterial3D_8as5m")
mesh = SubResource("ArrayMesh_hsh2j")
skeleton = NodePath("")

[node name="SubViewport" type="SubViewport" parent="OnArena/CardBg"]
transparent_bg = true
size = Vector2i(254, 188)

[node name="NinePatchRect" type="NinePatchRect" parent="OnArena/CardBg/SubViewport"]
offset_right = 254.0
offset_bottom = 188.0
texture = ExtResource("21_bglvw")
region_rect = Rect2(0, 0, 254, 188)
patch_margin_left = 47
patch_margin_top = 37
patch_margin_right = 35
patch_margin_bottom = 49

[node name="CardPicFGPlayed" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, -0.003)
layers = 524297
material_override = SubResource("StandardMaterial3D_4q0or")
mesh = SubResource("ArrayMesh_wyg3f")
skeleton = NodePath("")

[connection signal="input_event" from="." to="." method="_on_input_event"]
[connection signal="mouse_entered" from="." to="." method="_on_mouse_entered"]
[connection signal="mouse_exited" from="." to="." method="_on_mouse_exited"]
