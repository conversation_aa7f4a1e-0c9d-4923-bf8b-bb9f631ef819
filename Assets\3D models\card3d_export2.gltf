{"accessors": [{"bufferView": 0, "byteOffset": 0, "componentType": 5126, "count": 4, "max": [1, 0, 1], "min": [-1, 0, -1], "normalized": false, "type": "VEC3"}, {"bufferView": 1, "byteOffset": 0, "componentType": 5126, "count": 4, "max": [1, 0, -1.5e-05, 1], "min": [1, 0, -1.5e-05, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 2, "byteOffset": 0, "componentType": 5126, "count": 4, "max": [0, 1, -1.5e-05], "min": [0, 1, -1.5e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 3, "byteOffset": 0, "componentType": 5126, "count": 4, "max": [1, 1], "min": [0, 0], "normalized": false, "type": "VEC2"}, {"bufferView": 4, "byteOffset": 0, "componentType": 5125, "count": 6, "max": [3], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.045229, 2.504414, 1.781232], "min": [0.045229, 1.57067, 0.593072], "normalized": false, "type": "VEC3"}, {"bufferView": 6, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [2.4e-05, 1.5e-05, -1, 1], "min": [2.4e-05, 1.5e-05, -1, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 7, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [1, 1.5e-05, 2.4e-05], "min": [1, 1.5e-05, 2.4e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 8, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.991531, 0.948928], "min": [0.091752, 0.052949], "normalized": false, "type": "VEC2"}, {"bufferView": 9, "byteOffset": 0, "componentType": 5125, "count": 96, "max": [24], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 10, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.042317, 2.34665, 1.693958], "min": [0.042317, 1.69666, -1.694], "normalized": false, "type": "VEC3"}, {"bufferView": 11, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [2.4e-05, 1.5e-05, -1, 1], "min": [2.4e-05, 1.5e-05, -1, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 12, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [1, 1.5e-05, 2.4e-05], "min": [1, 1.5e-05, 2.4e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 13, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.999785, 1.003615], "min": [-0.001724, -0.019464], "normalized": false, "type": "VEC2"}, {"bufferView": 14, "byteOffset": 0, "componentType": 5125, "count": 96, "max": [24], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 15, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.043567, -0.87641, 1.810571], "min": [0.043567, -2.40294, 0.887948], "normalized": false, "type": "VEC3"}, {"bufferView": 16, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [2.4e-05, 1.5e-05, -1, 1], "min": [2.4e-05, 1.5e-05, -1, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 17, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [1, 1.5e-05, 2.4e-05], "min": [1, 1.5e-05, 2.4e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 18, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.909625, 1.013765], "min": [0.117938, -0.018671], "normalized": false, "type": "VEC2"}, {"bufferView": 19, "byteOffset": 0, "componentType": 5125, "count": 96, "max": [24], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 20, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.039807, 1.952018, 1.755877], "min": [0.039807, 0.880924, 1.04766], "normalized": false, "type": "VEC3"}, {"bufferView": 21, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [2.4e-05, 1.5e-05, -1, 1], "min": [2.4e-05, 1.5e-05, -1, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 22, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [1, 1.5e-05, 2.4e-05], "min": [1, 1.5e-05, 2.4e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 23, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.988815, 0.954849], "min": [0.008194, 0.009918], "normalized": false, "type": "VEC2"}, {"bufferView": 24, "byteOffset": 0, "componentType": 5125, "count": 96, "max": [24], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 25, "byteOffset": 0, "componentType": 5126, "count": 9, "max": [0.037824, -2.087572, -1.37725], "min": [0.037824, -2.47757, -1.60525], "normalized": false, "type": "VEC3"}, {"bufferView": 26, "byteOffset": 0, "componentType": 5126, "count": 9, "max": [2.4e-05, 1.5e-05, -1, 1], "min": [2.4e-05, 1.5e-05, -1, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 27, "byteOffset": 0, "componentType": 5126, "count": 9, "max": [1, 1.5e-05, 2.4e-05], "min": [1, 1.5e-05, 2.4e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 28, "byteOffset": 0, "componentType": 5126, "count": 9, "max": [0.535546, 0.552193], "min": [0.46479, 0.431159], "normalized": false, "type": "VEC2"}, {"bufferView": 29, "byteOffset": 0, "componentType": 5125, "count": 24, "max": [8], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 30, "byteOffset": 0, "componentType": 5126, "count": 40, "max": [-0.038634, 2.49985, 1.78976], "min": [-0.038634, -2.49985, -1.78976], "normalized": false, "type": "VEC3"}, {"bufferView": 31, "byteOffset": 0, "componentType": 5126, "count": 40, "max": [3.1e-05, 0, 1, -1], "min": [0, -3e-05, 1, -1], "normalized": false, "type": "VEC4"}, {"bufferView": 32, "byteOffset": 0, "componentType": 5126, "count": 40, "max": [1, 0, 0], "min": [1, -3.1e-05, -3.1e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 33, "byteOffset": 0, "componentType": 5126, "count": 40, "max": [0.989288, 0.986877], "min": [0.012924, 0.015137], "normalized": false, "type": "VEC2"}, {"bufferView": 34, "byteOffset": 0, "componentType": 5125, "count": 174, "max": [39], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 35, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [0.037, 2.49985, 1.78976], "min": [-0.037, -2.49985, -1.78976], "normalized": false, "type": "VEC3"}, {"bufferView": 36, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [1, 0.86198, 0.000144, 1], "min": [-0.968453, -0.829741, -1, -1], "normalized": false, "type": "VEC4"}, {"bufferView": 37, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [1, 1, 1], "min": [-0.116215, -1, -1], "normalized": false, "type": "VEC3"}, {"bufferView": 38, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [1.00112, 1.000095], "min": [0.00689, 0.008744], "normalized": false, "type": "VEC2"}, {"bufferView": 39, "byteOffset": 0, "componentType": 5125, "count": 828, "max": [159], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 40, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [0.037, 2.49985, 1.78976], "min": [-0.037, -2.49985, -1.78976], "normalized": false, "type": "VEC3"}, {"bufferView": 41, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [1, 0.86198, 0.000144, 1], "min": [-0.968453, -0.829741, -1, -1], "normalized": false, "type": "VEC4"}, {"bufferView": 42, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [1, 1, 1], "min": [-0.116215, -1, -1], "normalized": false, "type": "VEC3"}, {"bufferView": 43, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [1.00112, 1.000095], "min": [0.00689, 0.008744], "normalized": false, "type": "VEC2"}, {"bufferView": 44, "byteOffset": 0, "componentType": 5125, "count": 828, "max": [159], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 45, "byteOffset": 0, "componentType": 5126, "count": 8, "max": [0.049598, 1.71421, 1.68105], "min": [0.049598, -2.18494, -1.66119], "normalized": false, "type": "VEC3"}, {"bufferView": 46, "byteOffset": 0, "componentType": 5126, "count": 8, "max": [2.4e-05, 1.5e-05, -1, 1], "min": [2.4e-05, 1.5e-05, -1, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 47, "byteOffset": 0, "componentType": 5126, "count": 8, "max": [1, 1.5e-05, 2.4e-05], "min": [1, 1.5e-05, 2.4e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 48, "byteOffset": 0, "componentType": 5126, "count": 8, "max": [0.832776, 0.995758], "min": [0.166995, 0.134066], "normalized": false, "type": "VEC2"}, {"bufferView": 49, "byteOffset": 0, "componentType": 5125, "count": 18, "max": [7], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 50, "byteOffset": 0, "componentType": 5126, "count": 8, "max": [0.049598, 1.71421, 1.68105], "min": [0.049598, -2.18494, -1.66119], "normalized": false, "type": "VEC3"}, {"bufferView": 51, "byteOffset": 0, "componentType": 5126, "count": 8, "max": [2.4e-05, 1.5e-05, -1, 1], "min": [2.4e-05, 1.5e-05, -1, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 52, "byteOffset": 0, "componentType": 5126, "count": 8, "max": [1, 1.5e-05, 2.4e-05], "min": [1, 1.5e-05, 2.4e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 53, "byteOffset": 0, "componentType": 5126, "count": 8, "max": [0.832776, 0.995758], "min": [0.166995, 0.134066], "normalized": false, "type": "VEC2"}, {"bufferView": 54, "byteOffset": 0, "componentType": 5125, "count": 18, "max": [7], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 55, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.043567, -0.87641, 1.810571], "min": [0.043567, -2.40294, 0.887948], "normalized": false, "type": "VEC3"}, {"bufferView": 56, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [2.4e-05, 1.5e-05, -1, 1], "min": [2.4e-05, 1.5e-05, -1, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 57, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [1, 1.5e-05, 2.4e-05], "min": [1, 1.5e-05, 2.4e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 58, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.909625, 1.013765], "min": [0.117938, -0.018671], "normalized": false, "type": "VEC2"}, {"bufferView": 59, "byteOffset": 0, "componentType": 5125, "count": 96, "max": [24], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 60, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.039807, 1.952018, 1.755877], "min": [0.039807, 0.880924, 1.04766], "normalized": false, "type": "VEC3"}, {"bufferView": 61, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [2.4e-05, 1.5e-05, -1, 1], "min": [2.4e-05, 1.5e-05, -1, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 62, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [1, 1.5e-05, 2.4e-05], "min": [1, 1.5e-05, 2.4e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 63, "byteOffset": 0, "componentType": 5126, "count": 25, "max": [0.988815, 0.954849], "min": [0.008194, 0.009918], "normalized": false, "type": "VEC2"}, {"bufferView": 64, "byteOffset": 0, "componentType": 5125, "count": 96, "max": [24], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 65, "byteOffset": 0, "componentType": 5126, "count": 9, "max": [0.037824, -2.087572, -1.37725], "min": [0.037824, -2.47757, -1.60525], "normalized": false, "type": "VEC3"}, {"bufferView": 66, "byteOffset": 0, "componentType": 5126, "count": 9, "max": [2.4e-05, 1.5e-05, -1, 1], "min": [2.4e-05, 1.5e-05, -1, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 67, "byteOffset": 0, "componentType": 5126, "count": 9, "max": [1, 1.5e-05, 2.4e-05], "min": [1, 1.5e-05, 2.4e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 68, "byteOffset": 0, "componentType": 5126, "count": 9, "max": [0.535546, 0.552193], "min": [0.46479, 0.431159], "normalized": false, "type": "VEC2"}, {"bufferView": 69, "byteOffset": 0, "componentType": 5125, "count": 24, "max": [8], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 70, "byteOffset": 0, "componentType": 5126, "count": 40, "max": [-0.038634, 2.49985, 1.78976], "min": [-0.038634, -2.49985, -1.78976], "normalized": false, "type": "VEC3"}, {"bufferView": 71, "byteOffset": 0, "componentType": 5126, "count": 40, "max": [3.1e-05, 0, 1, -1], "min": [0, -3e-05, 1, -1], "normalized": false, "type": "VEC4"}, {"bufferView": 72, "byteOffset": 0, "componentType": 5126, "count": 40, "max": [1, 0, 0], "min": [1, -3.1e-05, -3.1e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 73, "byteOffset": 0, "componentType": 5126, "count": 40, "max": [0.989288, 0.986877], "min": [0.012924, 0.015137], "normalized": false, "type": "VEC2"}, {"bufferView": 74, "byteOffset": 0, "componentType": 5125, "count": 174, "max": [39], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 75, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [0.037, 2.49985, 1.78976], "min": [-0.037, -2.49985, -1.78976], "normalized": false, "type": "VEC3"}, {"bufferView": 76, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [1, 0.86198, 0.000144, 1], "min": [-0.968453, -0.829741, -1, -1], "normalized": false, "type": "VEC4"}, {"bufferView": 77, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [1, 1, 1], "min": [-0.116215, -1, -1], "normalized": false, "type": "VEC3"}, {"bufferView": 78, "byteOffset": 0, "componentType": 5126, "count": 517, "max": [1.00112, 1.000095], "min": [0.00689, 0.008744], "normalized": false, "type": "VEC2"}, {"bufferView": 79, "byteOffset": 0, "componentType": 5125, "count": 828, "max": [159], "min": [0], "normalized": false, "type": "SCALAR"}, {"bufferView": 80, "byteOffset": 0, "componentType": 5126, "count": 4, "max": [0.049598, 1.714183, 1.681009], "min": [0.049598, -1.33334, -1.66119], "normalized": false, "type": "VEC3"}, {"bufferView": 81, "byteOffset": 0, "componentType": 5126, "count": 4, "max": [2.4e-05, 1.5e-05, -1, 1], "min": [2.4e-05, 1.5e-05, -1, 1], "normalized": false, "type": "VEC4"}, {"bufferView": 82, "byteOffset": 0, "componentType": 5126, "count": 4, "max": [1, 1.5e-05, 2.4e-05], "min": [1, 1.5e-05, 2.4e-05], "normalized": false, "type": "VEC3"}, {"bufferView": 83, "byteOffset": 0, "componentType": 5126, "count": 4, "max": [0.832776, 0.807553], "min": [0.166995, 0.134066], "normalized": false, "type": "VEC2"}, {"bufferView": 84, "byteOffset": 0, "componentType": 5125, "count": 6, "max": [3], "min": [0], "normalized": false, "type": "SCALAR"}], "asset": {"generator": "Godot Engine v4.2.stable.official@46dc277917a93cbf601bbcf0d27d00f6feeec0d5", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 48, "byteOffset": 0}, {"buffer": 0, "byteLength": 64, "byteOffset": 48}, {"buffer": 0, "byteLength": 48, "byteOffset": 112}, {"buffer": 0, "byteLength": 32, "byteOffset": 160}, {"buffer": 0, "byteLength": 24, "byteOffset": 192}, {"buffer": 0, "byteLength": 300, "byteOffset": 216}, {"buffer": 0, "byteLength": 400, "byteOffset": 516}, {"buffer": 0, "byteLength": 300, "byteOffset": 916}, {"buffer": 0, "byteLength": 200, "byteOffset": 1216}, {"buffer": 0, "byteLength": 384, "byteOffset": 1416}, {"buffer": 0, "byteLength": 300, "byteOffset": 1800}, {"buffer": 0, "byteLength": 400, "byteOffset": 2100}, {"buffer": 0, "byteLength": 300, "byteOffset": 2500}, {"buffer": 0, "byteLength": 200, "byteOffset": 2800}, {"buffer": 0, "byteLength": 384, "byteOffset": 3000}, {"buffer": 0, "byteLength": 300, "byteOffset": 3384}, {"buffer": 0, "byteLength": 400, "byteOffset": 3684}, {"buffer": 0, "byteLength": 300, "byteOffset": 4084}, {"buffer": 0, "byteLength": 200, "byteOffset": 4384}, {"buffer": 0, "byteLength": 384, "byteOffset": 4584}, {"buffer": 0, "byteLength": 300, "byteOffset": 4968}, {"buffer": 0, "byteLength": 400, "byteOffset": 5268}, {"buffer": 0, "byteLength": 300, "byteOffset": 5668}, {"buffer": 0, "byteLength": 200, "byteOffset": 5968}, {"buffer": 0, "byteLength": 384, "byteOffset": 6168}, {"buffer": 0, "byteLength": 108, "byteOffset": 6552}, {"buffer": 0, "byteLength": 144, "byteOffset": 6660}, {"buffer": 0, "byteLength": 108, "byteOffset": 6804}, {"buffer": 0, "byteLength": 72, "byteOffset": 6912}, {"buffer": 0, "byteLength": 96, "byteOffset": 6984}, {"buffer": 0, "byteLength": 480, "byteOffset": 7080}, {"buffer": 0, "byteLength": 640, "byteOffset": 7560}, {"buffer": 0, "byteLength": 480, "byteOffset": 8200}, {"buffer": 0, "byteLength": 320, "byteOffset": 8680}, {"buffer": 0, "byteLength": 696, "byteOffset": 9000}, {"buffer": 0, "byteLength": 6204, "byteOffset": 9696}, {"buffer": 0, "byteLength": 8272, "byteOffset": 15900}, {"buffer": 0, "byteLength": 6204, "byteOffset": 24172}, {"buffer": 0, "byteLength": 4136, "byteOffset": 30376}, {"buffer": 0, "byteLength": 3312, "byteOffset": 34512}, {"buffer": 0, "byteLength": 6204, "byteOffset": 37824}, {"buffer": 0, "byteLength": 8272, "byteOffset": 44028}, {"buffer": 0, "byteLength": 6204, "byteOffset": 52300}, {"buffer": 0, "byteLength": 4136, "byteOffset": 58504}, {"buffer": 0, "byteLength": 3312, "byteOffset": 62640}, {"buffer": 0, "byteLength": 96, "byteOffset": 65952}, {"buffer": 0, "byteLength": 128, "byteOffset": 66048}, {"buffer": 0, "byteLength": 96, "byteOffset": 66176}, {"buffer": 0, "byteLength": 64, "byteOffset": 66272}, {"buffer": 0, "byteLength": 72, "byteOffset": 66336}, {"buffer": 0, "byteLength": 96, "byteOffset": 66408}, {"buffer": 0, "byteLength": 128, "byteOffset": 66504}, {"buffer": 0, "byteLength": 96, "byteOffset": 66632}, {"buffer": 0, "byteLength": 64, "byteOffset": 66728}, {"buffer": 0, "byteLength": 72, "byteOffset": 66792}, {"buffer": 0, "byteLength": 300, "byteOffset": 66864}, {"buffer": 0, "byteLength": 400, "byteOffset": 67164}, {"buffer": 0, "byteLength": 300, "byteOffset": 67564}, {"buffer": 0, "byteLength": 200, "byteOffset": 67864}, {"buffer": 0, "byteLength": 384, "byteOffset": 68064}, {"buffer": 0, "byteLength": 300, "byteOffset": 68448}, {"buffer": 0, "byteLength": 400, "byteOffset": 68748}, {"buffer": 0, "byteLength": 300, "byteOffset": 69148}, {"buffer": 0, "byteLength": 200, "byteOffset": 69448}, {"buffer": 0, "byteLength": 384, "byteOffset": 69648}, {"buffer": 0, "byteLength": 108, "byteOffset": 70032}, {"buffer": 0, "byteLength": 144, "byteOffset": 70140}, {"buffer": 0, "byteLength": 108, "byteOffset": 70284}, {"buffer": 0, "byteLength": 72, "byteOffset": 70392}, {"buffer": 0, "byteLength": 96, "byteOffset": 70464}, {"buffer": 0, "byteLength": 480, "byteOffset": 70560}, {"buffer": 0, "byteLength": 640, "byteOffset": 71040}, {"buffer": 0, "byteLength": 480, "byteOffset": 71680}, {"buffer": 0, "byteLength": 320, "byteOffset": 72160}, {"buffer": 0, "byteLength": 696, "byteOffset": 72480}, {"buffer": 0, "byteLength": 6204, "byteOffset": 73176}, {"buffer": 0, "byteLength": 8272, "byteOffset": 79380}, {"buffer": 0, "byteLength": 6204, "byteOffset": 87652}, {"buffer": 0, "byteLength": 4136, "byteOffset": 93856}, {"buffer": 0, "byteLength": 3312, "byteOffset": 97992}, {"buffer": 0, "byteLength": 48, "byteOffset": 101304}, {"buffer": 0, "byteLength": 64, "byteOffset": 101352}, {"buffer": 0, "byteLength": 48, "byteOffset": 101416}, {"buffer": 0, "byteLength": 32, "byteOffset": 101464}, {"buffer": 0, "byteLength": 24, "byteOffset": 101496}], "buffers": [{"byteLength": 101520, "uri": "card3d_export20.bin"}], "extensionsUsed": ["GODOT_single_root", "KHR_materials_unlit", "OMI_physics_body"], "images": [{"uri": "textures%2F_albedo000.png"}, {"uri": "textures%2F_albedo002.png"}, {"uri": "textures%2F_albedo003.png"}, {"uri": "textures%2F_albedo004.png"}, {"uri": "textures%2F_albedo005.png"}, {"uri": "textures%2F_albedo006.png"}, {"uri": "textures%2F_albedo007.png"}, {"uri": "textures%2F_albedo008.png"}, {"uri": "textures%2F_albedo009.png"}, {"uri": "textures%2F_albedo010.png"}, {"uri": "textures%2F_albedo011.png"}], "materials": [{"alphaMode": "BLEND", "extensions": {}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 1], "baseColorTexture": {"index": 0}, "metallicFactor": 0, "roughnessFactor": 1}}, {"alphaCutoff": 0.5, "alphaMode": "MASK", "extensions": {"KHR_materials_unlit": {}}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 1], "baseColorTexture": {"index": 1}, "metallicFactor": 0, "roughnessFactor": 1}}, {"alphaMode": "BLEND", "extensions": {"KHR_materials_unlit": {}}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 1], "baseColorTexture": {"index": 2}, "metallicFactor": 0, "roughnessFactor": 1}}, {"alphaCutoff": 0.5, "alphaMode": "MASK", "extensions": {"KHR_materials_unlit": {}}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 1], "baseColorTexture": {"index": 3}, "metallicFactor": 0, "roughnessFactor": 1}}, {"alphaCutoff": 0.5, "alphaMode": "MASK", "extensions": {"KHR_materials_unlit": {}}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 1], "baseColorTexture": {"index": 4}, "metallicFactor": 0, "roughnessFactor": 1}}, {"alphaMode": "BLEND", "extensions": {"KHR_materials_unlit": {}}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 1], "baseColorTexture": {"index": 5}, "metallicFactor": 0, "roughnessFactor": 1}}, {"alphaMode": "BLEND", "extensions": {"KHR_materials_unlit": {}}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 1], "baseColorTexture": {"index": 6}, "metallicFactor": 0, "roughnessFactor": 1}}, {"doubleSided": true, "extensions": {"KHR_materials_unlit": {}}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 1], "baseColorTexture": {"index": 7}, "metallicFactor": 0, "roughnessFactor": 1}}, {"alphaMode": "BLEND", "doubleSided": true, "extensions": {"KHR_materials_unlit": {}}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 0], "metallicFactor": 0, "roughnessFactor": 1}}, {"alphaMode": "BLEND", "extensions": {"KHR_materials_unlit": {}}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 1], "baseColorTexture": {"index": 8}, "metallicFactor": 0, "roughnessFactor": 1}}, {"alphaMode": "BLEND", "doubleSided": true, "extensions": {"KHR_materials_unlit": {}}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 1], "baseColorTexture": {"index": 9}, "metallicFactor": 0, "roughnessFactor": 1}}, {"doubleSided": true, "extensions": {"KHR_materials_unlit": {}}, "pbrMetallicRoughness": {"baseColorFactor": [0.99999988079071, 0.99999988079071, 0.99999988079071, 1], "baseColorTexture": {"index": 10}, "metallicFactor": 0, "roughnessFactor": 1}}], "meshes": [{"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 2, "POSITION": 0, "TANGENT": 1, "TEXCOORD_0": 3}, "indices": 4, "material": 0, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 7, "POSITION": 5, "TANGENT": 6, "TEXCOORD_0": 8}, "indices": 9, "material": 1, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 12, "POSITION": 10, "TANGENT": 11, "TEXCOORD_0": 13}, "indices": 14, "material": 2, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 17, "POSITION": 15, "TANGENT": 16, "TEXCOORD_0": 18}, "indices": 19, "material": 3, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 22, "POSITION": 20, "TANGENT": 21, "TEXCOORD_0": 23}, "indices": 24, "material": 4, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 27, "POSITION": 25, "TANGENT": 26, "TEXCOORD_0": 28}, "indices": 29, "material": 5, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 32, "POSITION": 30, "TANGENT": 31, "TEXCOORD_0": 33}, "indices": 34, "material": 6, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 37, "POSITION": 35, "TANGENT": 36, "TEXCOORD_0": 38}, "indices": 39, "material": 7, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 42, "POSITION": 40, "TANGENT": 41, "TEXCOORD_0": 43}, "indices": 44, "material": 8, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 47, "POSITION": 45, "TANGENT": 46, "TEXCOORD_0": 48}, "indices": 49, "material": 9, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 52, "POSITION": 50, "TANGENT": 51, "TEXCOORD_0": 53}, "indices": 54, "material": 10, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 57, "POSITION": 55, "TANGENT": 56, "TEXCOORD_0": 58}, "indices": 59, "material": 3, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 62, "POSITION": 60, "TANGENT": 61, "TEXCOORD_0": 63}, "indices": 64, "material": 4, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 67, "POSITION": 65, "TANGENT": 66, "TEXCOORD_0": 68}, "indices": 69, "material": 5, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 72, "POSITION": 70, "TANGENT": 71, "TEXCOORD_0": 73}, "indices": 74, "material": 6, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 77, "POSITION": 75, "TANGENT": 76, "TEXCOORD_0": 78}, "indices": 79, "material": 11, "mode": 4}]}, {"extras": {"targetNames": []}, "primitives": [{"attributes": {"NORMAL": 82, "POSITION": 80, "TANGENT": 81, "TEXCOORD_0": 83}, "indices": 84, "material": 9, "mode": 4}]}], "nodes": [{"children": [1, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 37, 40, 41, 44], "extensions": {"OMI_physics_body": {"type": "character"}}, "name": "Card"}, {"children": [2, 24], "extensions": {}, "name": "AbilityOnCard", "scale": [0.595000028610229, 0.595000028610229, 0.595000028610229], "translation": [0.13586600124836, -0.62924897670746, 0.0862947031855583]}, {"children": [3], "extensions": {}, "name": "SubViewport"}, {"children": [4, 11], "extensions": {}, "name": "VBoxContainer"}, {"children": [5, 6], "extensions": {}, "name": "HBoxContainer"}, {"extensions": {}, "name": "TextureRect"}, {"children": [7, 8], "extensions": {}, "name": "CastConTagText"}, {"extensions": {}, "name": "NinePatchRect"}, {"children": [9, 10], "extensions": {}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"extensions": {}, "name": "Label"}, {"extensions": {}, "name": "Label2"}, {"children": [12, 16, 20], "extensions": {}, "name": "MarginContainer2"}, {"children": [13, 14, 15], "extensions": {}, "name": "AbilityDetail"}, {"extensions": {}, "name": "_VScrollBar_37069"}, {"extensions": {}, "name": "_VScrollBar_37066"}, {"extensions": {}, "name": "_VScrollBar_17440"}, {"children": [17, 18, 19], "extensions": {}, "name": "AbilityDetailENG"}, {"extensions": {}, "name": "_VScrollBar_37070"}, {"extensions": {}, "name": "_VScrollBar_37067"}, {"extensions": {}, "name": "_VScrollBar_17441"}, {"children": [21, 22, 23], "extensions": {}, "name": "AbilityDetailTH"}, {"extensions": {}, "name": "_VScrollBar_37071"}, {"extensions": {}, "name": "_VScrollBar_37068"}, {"extensions": {}, "name": "_VScrollBar_17442"}, {"extensions": {}, "mesh": 0, "name": "Sprite3D", "rotation": [0.70710676908493, 0, 0, 0.70710676908493], "scale": [0.394999951124191, 0.394999951124191, 0.394999951124191], "translation": [-0.01986284554005, -0.00463044643402, -0.09727139770985]}, {"extensions": {}, "name": "CostText", "scale": [0.889999985694885, 1.05900001525879, 0.889999985694885], "translation": [-0.65872597694397, 0.704746007919312, 0.0397017002105713]}, {"extensions": {}, "name": "PowerText", "scale": [0.889999985694885, 1.05900001525879, 0.889999985694885], "translation": [-0.6710000038147, -1.21000003814697, 0.0329999998211861]}, {"extensions": {}, "name": "CardName", "translation": [0.112680003046989, 0.66763699054718, 0.0319949984550476]}, {"extensions": {}, "mesh": 1, "name": "CostBg", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, -0.23564200103283, 0.00551444012671709]}, {"extensions": {}, "mesh": 2, "name": "NameBg", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [-0.01520430017263, -0.23564200103283, 0]}, {"extensions": {}, "mesh": 3, "name": "PowerBg", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, -0.23600000143051, 0.00400000018998981]}, {"extensions": {}, "mesh": 4, "name": "ColorBar", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, -0.23564200103283, 0.00666136015206575]}, {"extensions": {}, "mesh": 5, "name": "<PERSON><PERSON><PERSON><PERSON>", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, -0.23564200103283, 0]}, {"extensions": {}, "mesh": 6, "name": "CardBack3", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, -0.23600000143051, 0]}, {"children": [35], "extensions": {}, "mesh": 7, "name": "CardBg", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, -0.23600000143051, 0]}, {"children": [36], "extensions": {}, "name": "SubViewport2"}, {"extensions": {}, "name": "NinePatchRect2"}, {"children": [38], "extensions": {}, "mesh": 8, "name": "CardBg2", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [1.**************, 0.**************, 0.5], "translation": [0, -0.23627300560474, 0]}, {"children": [39], "extensions": {}, "name": "SubViewport3"}, {"extensions": {}, "name": "NinePatchRect3"}, {"extensions": {}, "mesh": 9, "name": "CardFront", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, -0.23600000143051, -0.00300000002608]}, {"children": [42], "extensions": {}, "mesh": 10, "name": "CardPicGradient", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, -0.23600000143051, -6.298119843e-05]}, {"children": [43], "extensions": {}, "name": "SubViewport4"}, {"extensions": {}, "name": "NinePatchRect4"}, {"children": [45, 46, 47, 48, 49, 50, 53], "extensions": {}, "name": "OnArena"}, {"extensions": {}, "name": "PowerText2", "scale": [0.889999985694885, 1.05900001525879, 0.889999985694885], "translation": [-0.66549199819565, -0.83310902118683, 0.0329999998211861]}, {"extensions": {}, "mesh": 11, "name": "PowerBg2", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, 0.146876007318497, 0.00400000018998981]}, {"extensions": {}, "mesh": 12, "name": "ColorBar2", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, -0.30899998545647, 0.00700000021606684]}, {"extensions": {}, "mesh": 13, "name": "RarityBar2", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, 0.184946998953819, 0]}, {"extensions": {}, "mesh": 14, "name": "CardBack4", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.330000013113022, 0.***************], "translation": [0, -0.18000000715256, 0]}, {"children": [51], "extensions": {}, "mesh": 15, "name": "CardBg22", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.339101999998093, 0.***************], "translation": [0, -0.18454000353813, 0]}, {"children": [52], "extensions": {}, "name": "SubViewport5"}, {"extensions": {}, "name": "NinePatchRect5"}, {"extensions": {}, "mesh": 16, "name": "CardPicFGPlayed", "rotation": [0, -0.70710676908493, 0, 0.70710676908493], "scale": [0.***************, 0.***************, 0.***************], "translation": [0, -0.23600000143051, -0.00300000002608]}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}, {"magFilter": 9729, "minFilter": 9729, "wrapS": 10497, "wrapT": 10497}], "scene": 0, "scenes": [{"nodes": [0]}], "textures": [{"sampler": 0, "source": 0}, {"sampler": 1, "source": 1}, {"sampler": 1, "source": 2}, {"sampler": 1, "source": 3}, {"sampler": 1, "source": 4}, {"sampler": 1, "source": 5}, {"sampler": 1, "source": 6}, {"sampler": 1, "source": 7}, {"sampler": 1, "source": 8}, {"sampler": 1, "source": 9}, {"sampler": 1, "source": 10}]}