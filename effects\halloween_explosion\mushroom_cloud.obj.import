[remap]

importer="wavefront_obj"
importer_version=1
type="Mesh"
uid="uid://ddnx5315i2i1m"
path="res://.godot/imported/mushroom_cloud.obj-fd111c0241a0ce2bedcbd51270c23791.mesh"

[deps]

files=["res://.godot/imported/mushroom_cloud.obj-fd111c0241a0ce2bedcbd51270c23791.mesh"]

source_file="res://effects/halloween_explosion/mushroom_cloud.obj"
dest_files=["res://.godot/imported/mushroom_cloud.obj-fd111c0241a0ce2bedcbd51270c23791.mesh", "res://.godot/imported/mushroom_cloud.obj-fd111c0241a0ce2bedcbd51270c23791.mesh"]

[params]

generate_tangents=true
scale_mesh=Vector3(1, 1, 1)
offset_mesh=Vector3(0, 0, 0)
optimize_mesh=true
force_disable_mesh_compression=false
