[gd_scene load_steps=12 format=3 uid="uid://cmd0w1rvl6fdf"]

[ext_resource type="Script" path="res://effects/halloween_explosion/halloween_explosion_preview.gd" id="1_2483p"]
[ext_resource type="Shader" path="res://assets/ground_checkerboard.gdshader" id="2_7hldt"]
[ext_resource type="PackedScene" uid="uid://ldem8o2w0156" path="res://effects/halloween_explosion/jack_o_lantern.tscn" id="3_eyjss"]
[ext_resource type="Texture2D" uid="uid://3ro0yvv3roj3" path="res://Assets/checkerboard.svg" id="3_wrg2j"]
[ext_resource type="PackedScene" path="res://turner/turner.tscn" id="6_8f17f"]

[sub_resource type="Environment" id="Environment_abe8a"]
background_mode = 1
background_color = Color(0.14902, 0.14902, 0.14902, 1)
ambient_light_source = 2
ambient_light_color = Color(0.368627, 0.368627, 0.368627, 1)
tonemap_mode = 2
glow_enabled = true

[sub_resource type="Shader" id="Shader_wnqas"]
code = "shader_type spatial;
render_mode depth_draw_opaque,cull_back,unshaded;

uniform vec3 albedo : source_color;
uniform float proximity_fade_distance;
uniform sampler2D depth_texture : hint_depth_texture, repeat_disable, filter_nearest;

void fragment() {
	ALBEDO = albedo;
	float depth_tex = textureLod(depth_texture,SCREEN_UV,0.0).r;
	vec4 world_pos = INV_PROJECTION_MATRIX * vec4(SCREEN_UV*2.0-1.0,depth_tex,1.0);
	world_pos.xyz/=world_pos.w;
	ALPHA*=clamp(1.0-smoothstep(world_pos.z+proximity_fade_distance,world_pos.z,VERTEX.z),0.0,1.0);
}
"

[sub_resource type="ShaderMaterial" id="ShaderMaterial_iebuq"]
render_priority = -128
shader = SubResource("Shader_wnqas")
shader_parameter/albedo = Color(0.14902, 0.14902, 0.14902, 1)
shader_parameter/proximity_fade_distance = 8.0

[sub_resource type="SphereMesh" id="SphereMesh_shy7e"]
flip_faces = true
radius = 1.0
is_hemisphere = true

[sub_resource type="ShaderMaterial" id="ShaderMaterial_r632p"]
render_priority = 0
shader = ExtResource("2_7hldt")
shader_parameter/albedo_color = Color(0.2, 0.2, 0.2, 1)
shader_parameter/uv_scale = 20.0
shader_parameter/checkerboard_sampler = ExtResource("3_wrg2j")

[sub_resource type="PlaneMesh" id="PlaneMesh_mqu4c"]
size = Vector2(40, 40)

[node name="HalloweenExplosionPreview" type="Node3D"]
script = ExtResource("1_2483p")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_abe8a")

[node name="DomeFade" type="MeshInstance3D" parent="."]
transform = Transform3D(10, 0, 0, 0, 10, 0, 0, 0, 10, 0, 0, 0)
material_override = SubResource("ShaderMaterial_iebuq")
cast_shadow = 0
mesh = SubResource("SphereMesh_shy7e")
metadata/_edit_lock_ = true

[node name="GroundMesh" type="MeshInstance3D" parent="."]
material_override = SubResource("ShaderMaterial_r632p")
mesh = SubResource("PlaneMesh_mqu4c")

[node name="Turner" parent="." instance=ExtResource("6_8f17f")]
transform = Transform3D(1, 0, 0, 0, 0.990268, 0.139173, 0, -0.139173, 0.990268, 0, 2, 0)
_zoom = 10.0
min_zoom = 8.0
max_zoom = 12.0

[node name="Camera3D" parent="Turner" index="0"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0)
fov = 50.0

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.906308, 0.365998, -0.211309, 0, 0.5, 0.866025, 0.422618, -0.784886, 0.453154, 0, 0, 0)
shadow_enabled = true
directional_shadow_max_distance = 40.0
directional_shadow_pancake_size = 10.0

[node name="JackOLantern" parent="." instance=ExtResource("3_eyjss")]
unique_name_in_owner = true

[editable path="Turner"]
