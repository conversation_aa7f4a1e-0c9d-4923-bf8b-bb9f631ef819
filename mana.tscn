[gd_scene load_steps=19 format=3 uid="uid://bsaa0cjd45vwt"]

[ext_resource type="Texture2D" uid="uid://cu65iclqsyliu" path="res://Assets/DCN_PF_PA_VFX/DCN_PF_PA_VFX_Mana/DCN_PF_PA_VFX_Mana_Fire.png" id="1_0xtnp"]
[ext_resource type="Texture2D" uid="uid://bani2rwcc635j" path="res://Assets/Mana.png" id="1_girrx"]
[ext_resource type="Script" path="res://mana.gd" id="1_rvjef"]
[ext_resource type="FontFile" uid="uid://c761juhjl1ha0" path="res://Assets/Fonts/FAV/Chicalo.otf" id="2_htxh4"]
[ext_resource type="Texture2D" uid="uid://cgd8bg58twk8l" path="res://Assets/DCN_PF_PA_VFX/DCN_PF_PA_VFX_Mana/DCN_PF_PA_VFX_Mana_Fire_Line.png" id="2_kvbal"]
[ext_resource type="Texture2D" uid="uid://ustro5vfisdt" path="res://Assets/TempMana.png" id="2_wcjnh"]

[sub_resource type="AtlasTexture" id="AtlasTexture_d5tdf"]
atlas = ExtResource("1_0xtnp")
region = Rect2(0, 0, 513, 165)

[sub_resource type="AtlasTexture" id="AtlasTexture_xenng"]
atlas = ExtResource("1_0xtnp")
region = Rect2(513, 0, 513, 165)

[sub_resource type="AtlasTexture" id="AtlasTexture_bcoae"]
atlas = ExtResource("1_0xtnp")
region = Rect2(0, 165, 513, 165)

[sub_resource type="AtlasTexture" id="AtlasTexture_rh1v8"]
atlas = ExtResource("1_0xtnp")
region = Rect2(513, 165, 513, 165)

[sub_resource type="SpriteFrames" id="SpriteFrames_ppfts"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_d5tdf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xenng")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bcoae")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rh1v8")
}],
"loop": true,
"name": &"Idle",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_55vqg"]
atlas = ExtResource("2_kvbal")
region = Rect2(0, 0, 513, 165)

[sub_resource type="AtlasTexture" id="AtlasTexture_m3iqh"]
atlas = ExtResource("2_kvbal")
region = Rect2(513, 0, 513, 165)

[sub_resource type="AtlasTexture" id="AtlasTexture_flvej"]
atlas = ExtResource("2_kvbal")
region = Rect2(0, 165, 513, 165)

[sub_resource type="AtlasTexture" id="AtlasTexture_002vu"]
atlas = ExtResource("2_kvbal")
region = Rect2(513, 165, 513, 165)

[sub_resource type="SpriteFrames" id="SpriteFrames_hkldw"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_55vqg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m3iqh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_flvej")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_002vu")
}],
"loop": true,
"name": &"Idle",
"speed": 5.0
}]

[sub_resource type="LabelSettings" id="LabelSettings_d2b42"]
font = ExtResource("2_htxh4")
font_size = 116
outline_size = 45
outline_color = Color(0, 0, 0, 1)

[sub_resource type="LabelSettings" id="LabelSettings_mdksd"]
font = ExtResource("2_htxh4")
font_size = 61
font_color = Color(0, 0, 0, 1)
outline_size = 25
outline_color = Color(0, 0.960784, 0.898039, 1)

[node name="ManaPlayer" type="Control"]
layout_mode = 3
anchor_left = 0.137
anchor_top = 0.453
anchor_right = 0.137
anchor_bottom = 0.453
offset_left = -108.04
offset_top = -447.24
offset_right = -108.04
offset_bottom = -447.24
scale = Vector2(2.68, 2.68)
script = ExtResource("1_rvjef")

[node name="ManaFire" type="AnimatedSprite2D" parent="."]
position = Vector2(808.318, 266.713)
scale = Vector2(3.355, 3.355)
sprite_frames = SubResource("SpriteFrames_ppfts")
animation = &"Idle"

[node name="ManaFireLine" type="AnimatedSprite2D" parent="."]
position = Vector2(808.318, 266.713)
scale = Vector2(3.355, 3.355)
sprite_frames = SubResource("SpriteFrames_hkldw")
animation = &"Idle"

[node name="ManaBG" type="TextureRect" parent="."]
layout_mode = 2
offset_left = 26.4926
offset_top = 141.418
offset_right = 357.493
offset_bottom = 494.418
texture = ExtResource("1_girrx")

[node name="Label" type="Label" parent="ManaBG"]
layout_mode = 2
offset_left = -25.6716
offset_top = 125.075
offset_right = 342.328
offset_bottom = 377.075
text = "0/0"
label_settings = SubResource("LabelSettings_d2b42")
horizontal_alignment = 1
vertical_alignment = 1

[node name="TempManaBG" type="TextureRect" parent="."]
layout_mode = 2
offset_left = 652.612
offset_top = 223.881
offset_right = 773.613
offset_bottom = 379.881
texture = ExtResource("2_wcjnh")
expand_mode = 1

[node name="TempLabel" type="Label" parent="TempManaBG"]
layout_mode = 0
offset_left = 6.7169
offset_top = 38.4328
offset_right = 116.717
offset_bottom = 195.433
text = "12"
label_settings = SubResource("LabelSettings_mdksd")
horizontal_alignment = 1
vertical_alignment = 1
