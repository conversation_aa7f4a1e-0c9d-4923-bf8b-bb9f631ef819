class_name CardUIController
extends Node3D

var card: Card
var card3dui: Card_3DUI

#TODO

# func _init(p_owner: Player, p_color: int = CardColor.NONE, p_name: String = "", p_rarity: int = Rarity.COMMON, p_cost: int = 0, p_power: int = 0, p_castCondition: int = CastCondition.NORMAL, p_abilities: Array[Ability] = [], p_givingAbilities: Array[Ability] = []):
# 	card = Card.new(p_owner, p_color, p_name, p_rarity, p_cost, p_power, p_castCondition, p_abilities, p_givingAbilities)

# func setCard(p_card: Card):
# 	card = p_card

# func getCard3DUI() -> Card_3DUI:
# 	return self.card3dui

# func setCard3DUI(_card3dui: Card_3DUI):
# 	self.card3dui = _card3dui
# 	self.card3dui.playerParticle.visible = self.card.getOwner().isOnMainSide()
# 	self.card3dui.enemyParticle.visible = !self.card.getOwner().isOnMainSide()
