[gd_resource type="Resource" load_steps=3 format=3 uid="uid://8yr6cvtjo607"]

[ext_resource type="Texture2D" uid="uid://cbjt8vqi084fa" path="res://EssentialGames/CardGame/Assets/dork.png" id="1_w1qfe"]
[ext_resource type="Script" path="res://EssentialGames/CardGame/CardList/CardResource.gd" id="2_2cot0"]

[resource]
resource_local_to_scene = true
script = ExtResource("2_2cot0")
card_name = "Dork"
card_power = 0
card_toughness = 1
berry_cost = 0
stomp = false
abilities = Array[int]([0])
card_art = ExtResource("1_w1qfe")
