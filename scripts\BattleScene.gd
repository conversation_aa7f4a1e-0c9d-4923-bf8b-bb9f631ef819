class_name BattleScene
extends <PERSON>vas<PERSON>ay<PERSON>

var arena
var selectedHandIndex = 0
var snapScore:Array[int] = [0, 0, 0]
var qbScore:Array[int] = [0, 0, 0]
var sumScore:Array[int] = [0, 0, 0]

#load nodes
@onready var handMain = $HandMain
@onready var handOppo = $HandOppo
@onready var deckMainLabel = $DeckMain/CardCount
@onready var deckOppoLabel = $DeckOppo/CardCount
@onready var graveyardMainLabel = $GraveyardMain/CardCount
@onready var graveyardOppoLabel = $GraveyardOppo/CardCount
@onready var manaALabel = $ManaA
@onready var manaBLabel = $ManaB
@onready var laneLeft = $LaneLeft
@onready var laneCen = $LaneCen
@onready var laneRight = $LaneRight
@onready var passTurnButton = $PassTurnButton

@onready var snapALabel = $ScoreSnap/ScoreA
@onready var snapBLabel = $ScoreSnap/ScoreB
@onready var snapDrawLabel = $ScoreSnap/ScoreDraw
@onready var qbALabel = $ScoreQB/ScoreA
@onready var qbBLabel = $ScoreQB/ScoreB
@onready var qbDrawLabel = $ScoreQB/ScoreDraw
@onready var sumALabel = $ScoreSum/ScoreA
@onready var sumBLabel = $ScoreSum/ScoreB
@onready var sumDrawLabel = $ScoreSum/ScoreDraw

# Called when the node enters the scene tree for the first time.
func _ready():
	#get_viewport().size = DisplayServer.screen_get_size()
	self.arena = Arena.new()
	self.passTurnButton.pressed.connect(self.turnButtonPressed)
	self.handMain.cardPressedIndex.connect(self.handCardPressed)
	self.handOppo.cardPressedIndex.connect(self.handCardPressed)
	self.laneLeft.cardSlotUI.cardPressedIndex.connect(self.castButtonPressed.bind(0))
	self.laneCen.cardSlotUI.cardPressedIndex.connect(self.castButtonPressed.bind(1))
	self.laneRight.cardSlotUI.cardPressedIndex.connect(self.castButtonPressed.bind(2))
	displayArenaState()

# Detect keyboard input keys and trigger event
func _input(event):
	if event is InputEventKey && event.pressed && !event.echo:
		var keyPressed = event.as_text_key_label()
		match keyPressed:
			"D": self.arena.drawEvent(1) # Test active player draw
			"S": self.arena.shuffleEvent() # Test active player shuffle deck
			"M": self.arena.mulliganEvent()
			"R": gameOver(self.arena.getArenaWinner())
			_: print(keyPressed)
		displayArenaState()


# Active player select the card to cast
func handCardPressed(index:int):
	self.selectedHandIndex = index


# Active player fire Cast event 
func castButtonPressed(slotIndex:int, laneIndex:int) -> void:
	#print(str(laneIndex) + " " + str(slotIndex))
	if self.arena.cardCastEvent(self.arena.getCardByHandIndex(self.selectedHandIndex), laneIndex, slotIndex):
		self.selectedHandIndex = 0
		displayArenaState()


# Active player fire the Next Round event if both player pass, fire Upkeep event each time
func turnButtonPressed() -> void:
	self.arena.endTurnEvent()
	displayArenaState()


func gameOver(winner:Array[int]) -> void:
	self.snapScore[winner[0]] += 1
	self.snapALabel.text = str(snapScore[0])
	self.snapBLabel.text = str(snapScore[1])
	self.snapDrawLabel.text = str(snapScore[2])
	
	self.qbScore[winner[1]] += 1
	self.qbALabel.text = str(qbScore[0])
	self.qbBLabel.text = str(qbScore[1])
	self.qbDrawLabel.text = str(qbScore[2])
	
	self.sumScore[winner[2]] += 1
	self.sumALabel.text = str(sumScore[0])
	self.sumBLabel.text = str(sumScore[1])
	self.sumDrawLabel.text = str(sumScore[2])
	self.arena = Arena.new()


# Update all HUD with latest arena state
func displayArenaState() -> void:
	var arenaState = self.arena.getArenaState()
	self.passTurnButton.text = arenaState["ActivePlayer"].getName() + " Turn" 
	self.handMain.displayCardCollection(arenaState["HandA"], arenaState["PlayerA"], arenaState["ArenaCardA"].size())
	self.handOppo.displayCardCollection(arenaState["HandB"], arenaState["PlayerB"], arenaState["ArenaCardB"].size())
	self.deckMainLabel.text = "📄" + str(arenaState["DeckA"])
	self.deckOppoLabel.text = "📄" + str(arenaState["DeckB"])
	self.graveyardMainLabel.text = "☠️" + str(arenaState["GraveyardA"])
	self.graveyardOppoLabel.text = "☠️" + str(arenaState["GraveyardB"])
	self.manaALabel.text = displayManaIcon(arenaState["PlayerA"])
	self.manaBLabel.text = displayManaIcon(arenaState["PlayerB"])
	self.laneLeft.displayLaneState(arenaState["LaneLeft"], arenaState["LeftPowerA"], arenaState["LeftPowerB"])
	self.laneCen.displayLaneState(arenaState["LaneCen"], arenaState["CenPowerA"], arenaState["CenPowerB"])
	self.laneRight.displayLaneState(arenaState["LaneRight"], arenaState["RightPowerA"], arenaState["RightPowerB"])
	
	if arenaState["ActivePlayer"].isOnMainSide():
		self.handMain.modulate.a = 1
		self.handOppo.modulate.a = 0
	else:
		self.handOppo.modulate.a = 1
		self.handMain.modulate.a = 0

# Util function to generate mana icons
func displayManaIcon(player:Player) -> String:
	var manaIcons = ""
	for i in player.getTempMana():
		manaIcons += "🔶"
	for i in player.getMana():
		manaIcons += "🔷"
	for i in  player.getMaxMana() - player.getMana():
		manaIcons += "🔹"
	return manaIcons


func _on_back_button_pressed() -> void:
	get_tree().change_scene_to_file("res://menu.tscn")


func _on_quit_button_pressed() -> void:
	get_tree().quit()
