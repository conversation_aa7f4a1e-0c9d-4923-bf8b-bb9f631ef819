[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://hmkclokcruo0"
path="res://.godot/imported/RedHatText-Regular.ttf-26d51c6f384dae5a588f82cd577f5d85.fontdata"

[deps]

source_file="res://Assets/Fonts/RedHatText-Regular.ttf"
dest_files=["res://.godot/imported/RedHatText-Regular.ttf-26d51c6f384dae5a588f82cd577f5d85.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=1
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
