[gd_scene load_steps=6 format=3 uid="uid://bwtilkatugylg"]

[ext_resource type="Script" path="res://scripts/title/title_screen.gd" id="1_ka10q"]
[ext_resource type="PackedScene" uid="uid://cs8ncblbej60o" path="res://scripts/option/confirmation_screen_popup.tscn" id="2_nftoj"]
[ext_resource type="PackedScene" uid="uid://cortelt5ba30d" path="res://scripts/option/option_screen_popup.tscn" id="3_ysskf"]

[sub_resource type="Gradient" id="Gradient_eleys"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0.400698, 0.400698, 0.400698, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_d8hm2"]
gradient = SubResource("Gradient_eleys")

[node name="TitleScreen" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
size_flags_vertical = 4
script = ExtResource("1_ka10q")
metadata/_edit_lock_ = true

[node name="BG" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = -0.92
offset_top = 1.0
offset_right = 1919.08
offset_bottom = 1081.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_d8hm2")
metadata/_edit_lock_ = true

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.5
anchor_top = 0.78
anchor_right = 0.5
anchor_bottom = 0.78
offset_left = -85.0
offset_top = -123.4
offset_right = 85.0
offset_bottom = 123.6
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
theme_override_constants/separation = 25
alignment = 1

[node name="ContinueButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 28
text = "Continue"

[node name="NewGameButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 28
text = "New Game"

[node name="SettingsButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 28
text = "Settings"

[node name="QuitButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 28
text = "Quit"

[node name="TestModeButton" type="Button" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -925.0
offset_top = -71.0
offset_right = -754.0
offset_bottom = -24.0
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(0.94, 0.94)
theme_override_font_sizes/font_size = 28
text = "Test Mode"

[node name="ConfirmationScreen" parent="." instance=ExtResource("2_nftoj")]
visible = false
layout_mode = 1

[node name="Control" type="Control" parent="."]
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="OptionScreen" parent="Control" instance=ExtResource("3_ysskf")]
visible = false
layout_mode = 1
offset_right = 1880.0
offset_bottom = 1040.0

[connection signal="pressed" from="VBoxContainer/ContinueButton" to="." method="_on_continue_button_pressed"]
[connection signal="pressed" from="VBoxContainer/NewGameButton" to="." method="_on_start_button_pressed"]
[connection signal="pressed" from="VBoxContainer/SettingsButton" to="." method="_on_settings_button_pressed"]
[connection signal="pressed" from="VBoxContainer/QuitButton" to="." method="_on_quit_button_pressed"]
[connection signal="pressed" from="TestModeButton" to="." method="_on_test_mode_button_pressed"]
