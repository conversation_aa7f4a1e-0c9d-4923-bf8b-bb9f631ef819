[gd_scene load_steps=19 format=3 uid="uid://d37wg3mc76fs7"]

[ext_resource type="Script" path="res://effects/fireball/fireball_core/fireball.gd" id="1_nfli8"]
[ext_resource type="Material" uid="uid://d1nm3jquvgrb6" path="res://effects/fireball/materials/fireball_mat.tres" id="2_kub5u"]
[ext_resource type="ArrayMesh" uid="uid://bhma7uxtf5nja" path="res://effects/fireball/mesh/fireball_shell_mesh.obj" id="3_e1dfy"]
[ext_resource type="Material" uid="uid://osm40gk0a5v0" path="res://effects/fireball/materials/fireball_fire_particles_mat.tres" id="5_11yv5"]
[ext_resource type="Shader" path="res://effects/fireball/materials/fireball_core.gdshader" id="6_lin42"]
[ext_resource type="Material" uid="uid://cyxfa2tlqgn0k" path="res://effects/fireball/materials/fireball_firetrail_particles_process.tres" id="6_obgtq"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_7vae4"]
render_priority = 0
shader = ExtResource("6_lin42")
shader_parameter/albedo = Color(0.129412, 0.203922, 0.294118, 1)
shader_parameter/emission = Color(0.0470588, 0.235294, 1, 1)

[sub_resource type="SphereMesh" id="SphereMesh_77e4v"]
radius = 0.4
height = 0.8

[sub_resource type="QuadMesh" id="QuadMesh_6lh4h"]
size = Vector2(0.5, 0.5)

[sub_resource type="Shader" id="Shader_q1xb5"]
code = "shader_type spatial;
render_mode unshaded, ambient_light_disabled;

uniform sampler2D voronoi_sampler : filter_linear_mipmap, repeat_enable;

varying float id;
varying vec4 custom;

void vertex() {
	
	mat4 mat_world = mat4(normalize(INV_VIEW_MATRIX[0]), normalize(INV_VIEW_MATRIX[1]) ,normalize(INV_VIEW_MATRIX[2]), MODEL_MATRIX[3]);
	mat_world = mat_world * mat4(vec4(cos(INSTANCE_CUSTOM.x), -sin(INSTANCE_CUSTOM.x), 0.0, 0.0), vec4(sin(INSTANCE_CUSTOM.x), cos(INSTANCE_CUSTOM.x), 0.0, 0.0), vec4(0.0, 0.0, 1.0, 0.0), vec4(0.0, 0.0, 0.0, 1.0));
	MODELVIEW_MATRIX = VIEW_MATRIX * mat_world;
	MODELVIEW_MATRIX = MODELVIEW_MATRIX * mat4(vec4(length(MODEL_MATRIX[0].xyz), 0.0, 0.0, 0.0),vec4(0.0, length(MODEL_MATRIX[1].xyz), 0.0, 0.0), vec4(0.0, 0.0, length(MODEL_MATRIX[2].xyz), 0.0), vec4(0.0, 0.0, 0.0, 1.0));
	MODELVIEW_NORMAL_MATRIX = mat3(MODELVIEW_MATRIX);
	
	id = float(INSTANCE_ID);
	custom = INSTANCE_CUSTOM;
}

void fragment() {
	float voronoi = texture(voronoi_sampler, UV * 0.3 + id * 0.1 + custom.b).x;
	float dist = distance(UV, vec2(0.5));
	float dist_mask = smoothstep(0.45, 0.0, dist);
	float mask = max(0.0, dist_mask - voronoi);
	ALBEDO = COLOR.rgb;
	ALPHA = custom.y * mask * COLOR.a;
}
"

[sub_resource type="FastNoiseLite" id="FastNoiseLite_gmm0f"]
noise_type = 2
fractal_octaves = 2

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_excd7"]
seamless = true
noise = SubResource("FastNoiseLite_gmm0f")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_wk0ro"]
render_priority = 0
shader = SubResource("Shader_q1xb5")
shader_parameter/voronoi_sampler = SubResource("NoiseTexture2D_excd7")

[sub_resource type="Gradient" id="Gradient_wgv17"]
colors = PackedColorArray(1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_ottve"]
gradient = SubResource("Gradient_wgv17")

[sub_resource type="Curve" id="Curve_ka18s"]
_data = [Vector2(0, 0.5), 0.0, 0.0, 0, 0, Vector2(0.8, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_078xr"]
curve = SubResource("Curve_ka18s")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_e8e44"]
lifetime_randomness = 0.1
emission_shape = 1
emission_sphere_radius = 0.15
direction = Vector3(0, 0, -1)
spread = 20.0
gravity = Vector3(0, 1, 0)
scale_min = 0.8
scale_max = 1.2
scale_curve = SubResource("CurveTexture_078xr")
color = Color(0.780392, 0.780392, 0.780392, 0.470588)
color_ramp = SubResource("GradientTexture1D_ottve")
anim_speed_min = 0.01
anim_speed_max = 0.02
anim_offset_max = 1.0

[node name="Fireball" type="Node3D"]
script = ExtResource("1_nfli8")

[node name="FireballShellMesh" type="MeshInstance3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(0.943989, 0, 0, 0, -3.92e-08, -0.471092, 0, 0.896791, -2.05921e-08, 0, 0, 0)
material_override = ExtResource("2_kub5u")
mesh = ExtResource("3_e1dfy")

[node name="Core" type="MeshInstance3D" parent="FireballShellMesh"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, 0, 0)
material_override = SubResource("ShaderMaterial_7vae4")
cast_shadow = 0
mesh = SubResource("SphereMesh_77e4v")
skeleton = NodePath("../..")

[node name="FireTrail" type="GPUParticles3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -0.13)
visible = false
material_override = ExtResource("5_11yv5")
amount = 16
lifetime = 0.5
process_material = ExtResource("6_obgtq")
draw_pass_1 = SubResource("QuadMesh_6lh4h")

[node name="FireSmoke" type="GPUParticles3D" parent="."]
unique_name_in_owner = true
visible = false
material_override = SubResource("ShaderMaterial_wk0ro")
amount = 16
explosiveness = 0.2
process_material = SubResource("ParticleProcessMaterial_e8e44")
draw_pass_1 = SubResource("QuadMesh_6lh4h")
