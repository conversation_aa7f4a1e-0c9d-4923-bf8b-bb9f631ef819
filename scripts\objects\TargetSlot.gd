class_name TargetSlot
extends Object

enum {
	NONE,
	LANE_ALLY,
	LANE_ENEMY,
	LANE,
	OTHER_LANES_ALLY,
	OTHER_LANES_ENEMY,
	OTHER_LANES,
	ARENA_ALLY,
	ARENA_ENEMY,
	ARENA
}

static var labelDict = {
	NONE: "",
	LANE_ALLY: "Your slots here",
	LANE_ENEMY: "Oppo slots here",
	LANE: "All slots here",
	OTHER_LANES_ALLY: "Your slots elsewhere",
	OTHER_LANES_ENEMY: "Oppo slots elsewhere",
	OTHER_LANES: "All slots elsewhere",
	ARENA_ALLY: "Your slots",
	ARENA_ENEMY: "Oppo slots",
	ARENA: "ALl slots",
}

var name:int
var condition:int
var conditionTier:int

func _init(p_name:int = NONE, p_condition:int = TargetCondition.NONE, p_conditionTier:int = 0):
	self.name = p_name
	self.condition = p_condition
	self.conditionTier = p_conditionTier

func getName() -> int:
	return self.name

func getCondition() -> int:
	return self.condition

func getConditionTier() -> int:
	return self.conditionTier
