class_name Keyword
extends Object

enum {
	NONE,
	GIVE_ABILITY,
	<PERSON><PERSON>_<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>, #Passive
	STRENGTHEN,
	<PERSON><PERSON><PERSON><PERSON>, #Passive
	REPRODUCE, 
	REG<PERSON>,
	THORN,
	TEMP_MANA, 
	WEAKE<PERSON>, 
	DESTROY, 
	IGNIT<PERSON>, 
	<PERSON><PERSON><PERSON><PERSON>, 
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	IN<PERSON>IR<PERSON>, #Passive
	SHIELD,
	
	# companion abilities
	MANA_EMPOWER, #Passive
	CONVOKE, #Passive
	MULTIPLY_WEAKEN, #Passive
	MULTIPLY_STRENGTHEN, #Passive
	MODIFY_WEAKEN, #Passive
	MODIFY_STRENGTHEN, #Passive
	MULTIPLY_ON_ARENA, #Passive 9
	MULTIPLY_ON_ENTER, #Passive
	TRIGGER_ON_ENTER,
	TRIGGER_ON_DEAD,
	COST_BECOME, #Passive
	
	# location abilities
	DISABLE_SLOT, #Passive
	SWAP_OWNER,
	LOWER_POWER_WINS, #Passive
	INDESTRUCTIBLE,

	#blue
	SILENT,
	UNSUMMONED,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>_SLOT_ACTIVE, #Active
	STRENGTHEN_PASSIVE
	
}

static var KeywordInfo = {
	NONE: { #0
		"label": "-",
		"type": KeywordType.NONE,
		"priority": 0
	},
	GIVE_ABILITY: { #1
		"label": "give ability to",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	PROD_MANA: { #2
		"label": "produce mana",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	EMPOWER: { #3
		"label": "empower",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	STRENGTHEN: { #4
		"label": "strengthen",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	GROWTH: { #5
		"label": "growth",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	REPRODUCE: { #6
		"label": "reproduce",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	REGEN: { #7
		"label": "heal",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	THORN: { #8
		"label": "thorn",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	TEMP_MANA:{ #9
		"label": "add temp mana",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	WEAKEN: { #10
		"label": "weaken",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	DESTROY: { #11
		"label": "destroy",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	IGNITE: { #12
		"label": "ignite",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	DISCARD: { #13
		"label": "discard",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	DRAW: { #14
		"label": "draw",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	DUPLICATE: { #15
		"label": "duplicate",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	INSPIRE: { #16
		"label": "inspire",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	SHIELD: { #17
		"label": "shield",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	
	# companion abilities
	MANA_EMPOWER: { #18
		"label": "mana empower",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	CONVOKE: { #19
		"label": "convoke",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	MULTIPLY_WEAKEN: { #20
		"label": "multiply weaken",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	MULTIPLY_STRENGTHEN: { #21
		"label": "multiply strengthen",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	MODIFY_WEAKEN: { #22
		"label": "modify weaken",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	MODIFY_STRENGTHEN: { #23
		"label": "modify strengthen",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	MULTIPLY_ON_ARENA: { #24
		"label": "modify on arena",
		"type": KeywordType.PASSIVE,
		"priority": 9
	},
	MULTIPLY_ON_ENTER: { #25
		"label": "modify on enter",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	TRIGGER_ON_ENTER: { #26
		"label": "trigger on enter",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	TRIGGER_ON_DEAD: { #27
		"label": "trigger on dead",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	COST_BECOME: { #28
		"label": "cost =",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	
	# companion abilities
	DISABLE_SLOT: { #29
		"label": "disable slot",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	SWAP_OWNER: { #30
		"label": "swap owner",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	LOWER_POWER_WINS: { #31
		"label": "lower power wins",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
	INDESTRUCTIBLE: { #32
		"label": "indestructible",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	SILENT: { #33
		"label": "silent",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	UNSUMMONED: { #34
		"label": "unsummoned",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	MIMIC: { #35
		"label": "mimic",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	DISABLE_SLOT_ACTIVE: { #36
		"label": "disable slot",
		"type": KeywordType.ACTIVE,
		"priority": 0
	},
	STRENGTHEN_PASSIVE:{ #37
		"label": "strengthen",
		"type": KeywordType.PASSIVE,
		"priority": 0
	},
}

var name:int
var tier:int
var dynamicTier:int

func _init(p_name:int = NONE, p_tier:int = 0, p_dynamicTier:int = DynamicTier.NONE):
	self.name = p_name
	self.tier = p_tier
	self.dynamicTier = p_dynamicTier

func getName() -> int:
	return self.name

func getTier() -> int:
	return self.tier

func getDynamicTier() -> int:
	return self.dynamicTier
