extends Control
@onready var option_screen: Control = $OptionScreen

#varibles
#crafting database
var material_amt: int = 10
var upgrade_1_amt: int = 10
var upgrade_2_amt: int = 20
var upgrade_3_amt: int = 40
var combine_amt: int = 1
var shard_amt: int = 1
var craft_chance: Dictionary = {
	"without_shard": {
		"common": 0.8,
		"rare": 0.15,
		"epic": 0.04,
		"legendary": 0.01
		},
	"with_rare_shard": {
		"common": 0.0,
		"rare": 0.9,
		"epic": 0.08,
		"legendary": 0.02
		},
	"with_epic_shard": {
		"common": 0.0,
		"rare": 0.0,
		"epic": 0.95,
		"legendary": 0.05
		},
	"with_legendary_shard": {
			"common": 0.0,
			"rare": 0.0,
			"epic": 0.0,
			"legendary": 1
		}
	}

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	pass # Replace with function body.

# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass


#logic functions
func craft_card(color: int, amount: int, shard: String) -> Array[int]:
	print("Crafting card...")
	#deduct resources
	if color == CardColor.GREEN:
		if PlayerProfile.resources.has("green_ink") and PlayerProfile.resources["green_ink"] >= material_amt * amount:
			PlayerProfile.resources["green_ink"] -= material_amt * amount
		else:
			print("Not enough green ink.")
			return []
	elif color == CardColor.RED:
		if PlayerProfile.resources.has("red_ink") and PlayerProfile.resources["red_ink"] >= material_amt * amount:
			PlayerProfile.resources["red_ink"] -= material_amt * amount
		else:
			print("Not enough red ink.")
			return []
	elif color == CardColor.BLUE:
		if PlayerProfile.resources.has("blue_ink") and PlayerProfile.resources["blue_ink"] >= material_amt * amount:
			PlayerProfile.resources["blue_ink"] -= material_amt * amount
		else:
			print("Not enough blue ink.")
			return []
	else:
		print("Invalid color.")
		return []
		
	#check for shard
	var chance = craft_chance["without_shard"]
	if shard == "rare":
		if PlayerProfile.resources.has("rare_shard") and PlayerProfile.resources["rare_shard"] >= shard_amt * amount:
			PlayerProfile.resources["rare_shard"] -= shard_amt * amount
			chance = craft_chance["with_rare_shard"]
		else:
			print("Not enough rare shards.")
			return []
	elif shard == "epic":
		if PlayerProfile.resources.has("epic_shard") and PlayerProfile.resources["epic_shard"] >= shard_amt * amount:
			PlayerProfile.resources["epic_shard"] -= shard_amt * amount
			chance = craft_chance["with_epic_shard"]
		else:
			print("Not enough epic shards.")
			return []
	elif shard == "legendary":
		if PlayerProfile.resources.has("legendary_shard") and PlayerProfile.resources["legendary_shard"] >= shard_amt * amount:
			PlayerProfile.resources["legendary_shard"] -= shard_amt * amount
			chance = craft_chance["with_legendary_shard"]
		else:
			print("Not enough legendary shards.")
			return []
	#craft card
	var random_float = randf()
	var crafted_cards: Array[int] = []
	for i in range(amount):
		if random_float < chance["legendary"]:
			print("legendary")
			crafted_cards.append(generate_card(color, "legendary"))
		elif random_float < chance["epic"]:
			print("epic")
			crafted_cards.append(generate_card(color, "epic"))
		elif random_float < chance["rare"]:
			print("rare")
			crafted_cards.append(generate_card(color, "rare"))
		else:
			print("common")
			crafted_cards.append(generate_card(color, "common"))
	
	return crafted_cards

func generate_card(color: int, rarity: String) -> int:
	print("Generating card...")
	return -1

#ui functions
func _on_my_library_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/my_lib/my_library_screen.tscn")
	pass # Replace with function body.


func _on_settings_button_pressed() -> void:
	option_screen.visible = true
	pass # Replace with function body.
