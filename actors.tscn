[gd_scene load_steps=9 format=3 uid="uid://b4v8ylsjefgbd"]

[ext_resource type="Script" path="res://scripts/objects/Player.gd" id="1_7ys3e"]
[ext_resource type="PackedScene" uid="uid://b8nvtpi41igew" path="res://card_collection.tscn" id="1_e1stu"]
[ext_resource type="Script" path="res://scripts/ui_controller/Hand.gd" id="2_rc826"]
[ext_resource type="Script" path="res://scripts/ui_controller/Deck.gd" id="3_ctnwd"]
[ext_resource type="Script" path="res://scripts/ui_controller/Graveyard.gd" id="4_5nggm"]

[sub_resource type="Curve" id="Curve_qgxra"]
min_value = -1.0
_data = [Vector2(0, -1), 0.0, 2.0, 0, 1, Vector2(1, 1), 2.0, 0.0, 1, 0]
point_count = 2
metadata/_snap_count = 2

[sub_resource type="Curve" id="Curve_n7lk5"]
min_value = -1.0
max_value = 0.0
_data = [Vector2(0, 0), -1.93031, -0.102465, 0, 0, Vector2(1, -0.8), -1.97985, 0.0, 0, 0]
point_count = 2

[sub_resource type="Curve" id="Curve_y7sr1"]
min_value = -1.0
_data = [Vector2(0, -0.2), 0.0, -0.6, 0, 1, Vector2(1, -0.8), -0.6, 0.0, 1, 0]
point_count = 2

[node name="Actor" type="Node3D"]
script = ExtResource("1_7ys3e")

[node name="Hand" parent="." instance=ExtResource("1_e1stu")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.626074, 10.6963, -2.672)
script = ExtResource("2_rc826")
deck_path = NodePath("../Deck")
spread_curve = SubResource("Curve_qgxra")
height_curve = SubResource("Curve_n7lk5")
rotation_curve = SubResource("Curve_y7sr1")
width_array = Array[float]([0.0, 0.8, 1.6, 2.4, 2.75, 3.0])

[node name="Deck" parent="." instance=ExtResource("1_e1stu")]
transform = Transform3D(-1, -1.50996e-07, 6.60024e-15, 0, -4.37114e-08, -1, 1.50996e-07, -1, 4.37114e-08, 9.64522, 8.359, -1.16203)
script = ExtResource("3_ctnwd")

[node name="Graveyard" parent="." instance=ExtResource("1_e1stu")]
transform = Transform3D(1, -1.50996e-07, 8.74228e-08, -8.74228e-08, 4.37114e-08, 1, -1.50996e-07, -1, 4.37114e-08, -10.46, 8.359, -0.732)
script = ExtResource("4_5nggm")

[node name="MovingQueue" type="Node3D" parent="."]
