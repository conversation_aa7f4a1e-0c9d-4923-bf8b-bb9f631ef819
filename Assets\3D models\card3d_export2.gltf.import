[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://b7h5tn7ktx4ko"
path="res://.godot/imported/card3d_export2.gltf-c96b3db2841fb5f49fb85d99c7bfc959.scn"

[deps]

source_file="res://Assets/3D models/card3d_export2.gltf"
dest_files=["res://.godot/imported/card3d_export2.gltf-c96b3db2841fb5f49fb85d99c7bfc959.scn"]

[params]

nodes/root_type=""
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
import_script/path=""
_subresources={}
gltf/naming_version=1
gltf/embedded_image_handling=1
