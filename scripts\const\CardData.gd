extends Object
class_name CardData

# CardData is a singleton that stores all the card data in the game.
enum Card {
	GreenSlime,
	RedSlime,
	BlueSlime,
	LeafSlime,
	FireSlime,
	Crimslime,
	CaveSlime,
	ForestSlime,
	MagmaSlime,
	MutantSlime,
	GreatSlime,
	HellSlime,
	Fairy,
	Sunshroom,
	Daddylion,
	Entling,
	LeafDragonling,
	MudGolem,
	GreenMage,
	Healshroom,
	Blesshroom,
	YoungDryad,
	StoneGolem,
	Elderbloom,
	ElderEnt,
	GeneralPrixa,
	Terragon,
	Butterfire,
	HellButterfire,
	Bomberkid,
	Imp,
	PitImp,
	RedMage,
	Larva,
	FireDragonling,
	LadyLamia,
	Flamecoat,
	DragonKnightTitus,
	Explodus,
	LordDarkflame,
	BloodLord,
	Nymora,
	RotWyrm,
	Pyros,
	Fireshadow,
	KingKrotos,
	Pyragon,
	Skelegon,
	Siren,
	MagicShell,
	SnowFairy,
	Metamore,
	Mermage,
	BlueMage,
	PearlAngler,
	Levisia,
	IceGolem,
	MasterTrine,
	WaterElemental,
	Phantarion,
	Alcar,
	Morco,
	DrakeX
}

static func getCardByName(cardName:int):
	return StandardCard[cardName]

static var StandardCard = {
	Card.GreenSlime: {
		"name": "Green Slime",
		"id": Card.GreenSlime,
		"color": CardColor.GREEN,
		"cost": 1,
		"power": 2,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Green Slime.png",
	},
	Card.RedSlime: {
		"name": "Red Slime",
		"id": Card.RedSlime,
		"color": CardColor.RED,
		"cost": 1,
		"power": 2,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Red Slime.png",
	},
	Card.BlueSlime: {
		"name": "Blue Slime",
		"id": Card.BlueSlime,
		"color": CardColor.BLUE,
		"cost": 1,
		"power": 2,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Blue Slime.png",
	},
	Card.LeafSlime: {
		"name": "Leaf Slime",
		"id": Card.LeafSlime,
		"color": CardColor.GREEN,
		"cost": 2,
		"power": 3,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Leaf Slime.png",
	},
	Card.FireSlime: {
		"name": "Fire Slime",
		"id": Card.FireSlime,
		"color": CardColor.RED,
		"cost": 3,
		"power": 4,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Fire Slime.png",
	},
	Card.Fairy: {
		"name": "Fairy",
		"id": Card.Fairy,
		"color": CardColor.GREEN,
		"cost": 1,
		"power": 1,
		"rarity": Rarity.COMMON,
		"abilities": [
			{
				"text": "On enter: Gain 1 Permanent mana.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 2,
					"tier": 1
				}
			}
		],
		"description": "On enter: Gain 1 Permanent mana.",
		"image": "res://Assets/CardPics2/Fairy.png",
	},
	Card.Butterfire: {
		"name": "Butterfire",
		"id": Card.Butterfire,
		"color": CardColor.RED,
		"cost": 1,
		"power": 1,
		"rarity": Rarity.COMMON,
		"abilities": [
			{
				"text": "On enter: Gain 1 Temp mana.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 9,
					"tier": 1
				}
			}
		],
		"description": "On enter: Gain 1 Temp mana.",
		"image": "res://Assets/CardPics2/Butterfire.png",
	},
	Card.Bomberkid: {
		"name": "Bomberkid",
		"id": Card.Bomberkid,
		"color": CardColor.RED,
		"cost": 1,
		"power": 1,
		"rarity": Rarity.COMMON,
		"abilities": [
			{
				"text": "On dead: Afflict -2 Power to a random enemy.",
				"trigger": {
					"name": 4
				},
				"keyword": {
					"name": 1,
					"tier": 2
				},
				"target": {
					"name": 2
				}
			}
		],
		"description": "On dead: Afflict -2 Power to a random enemy.",
		"image": "res://Assets/CardPics2/Bomberkid.png",
	},
	Card.Sunshroom: {
		"name": "Sunshroom",
		"id": Card.Sunshroom,
		"color": CardColor.GREEN,
		"cost": 1,
		"power": 1,
		"rarity": Rarity.COMMON,
		"abilities": [
			{
				"text": "On enter: Give +3 Power to a host.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 4,
					"tier": 3
				},
				"target": {
					"name": 5
				}
			}
		],
		"description": "On enter: Give +3 Power to a host.",
		"image": "res://Assets/CardPics2/Sunshroom.png",
	},
	Card.Morco: {
		"name": "Mor'co",
		"id": Card.Morco,
		"color": 6,
		"cost": 6,
		"power": 48,
		"rarity": Rarity.UNIQUE,
		"abilities": [
			{
				"text": "On enter: You gain control of all enemies",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 27,
					"tier": 0
				},
				"target": {
					"name": 3
				}
			}
		],
		"description": "On enter: You gain control of all enemies",
		"image": "res://Assets/CardPics2/Morco.png",
	},
	Card.Entling: {
		"name": "Entling",
		"id": Card.Entling,
		"color": CardColor.GREEN,
		"cost": 2,
		"power": 2,
		"rarity": Rarity.RARE,
		"abilities": [
			{
				"text": "Passive: +1 Power for each ally here.",
				"trigger": {
					"name": 1
				},
				"keyword": {
					"name": 3,
					"tier": 1
				},
				"target": {
					"name": 1
				}
			}
		],
		"description": "Passive: +1 Power for each ally here.",
		"image": "res://Assets/CardPics2/Entling.png",
	},
	Card.CaveSlime: {
		"name": "Cave Slime",
		"id": Card.CaveSlime,
		"color": CardColor.GREEN,
		"cost": 3,
		"power": 4,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Cave Slime.png",
	},
	Card.GreenMage: {
		"name": "Green Mage",
		"id": Card.GreenMage,
		"color": CardColor.GREEN,
		"cost": 3,
		"power": 4,
		"rarity": Rarity.EPIC,
		"abilities": [
			{
				"text": "On enter: Give +2 Power to allies played last turn.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 4,
					"tier": 2
				},
				"target": {
					"name": 1,
					"condition": 5
				}
			}
		],
		"description": "On enter: Give +2 Power to allies played last turn.",
		"image": "res://Assets/CardPics2/Green Mage.png",
	},
	Card.ForestSlime: {
		"name": "Forest Slime",
		"id": Card.ForestSlime,
		"color": CardColor.GREEN,
		"cost": 4,
		"power": 5,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Forest Slime.png",
	},
	Card.YoungDryad: {
		"name": "Young Dryad",
		"id": Card.YoungDryad,
		"color": CardColor.GREEN,
		"cost": 4,
		"power": 3,
		"rarity": Rarity.EPIC,
		"abilities": [
			{
				"text": "Passive: +2 Power for each ally here.",
				"trigger": {
					"name": 1
				},
				"keyword": {
					"name": 3,
					"tier": 2
				},
				"target": {
					"name": 1
				}
			}
		],
		"description": "Passive: +2 Power for each ally here.",
		"image": "res://Assets/CardPics2/Young Dryad.png",
	},
	Card.GreatSlime: {
		"name": "Great Slime",
		"id": Card.GreatSlime,
		"color": CardColor.GREEN,
		"cost": 5,
		"power": 6,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Great Slime.png",
	},
	Card.StoneGolem: {
		"name": "Stone Golem",
		"id": Card.StoneGolem,
		"color": CardColor.GREEN,
		"cost": 5,
		"power": 4,
		"rarity": Rarity.RARE,
		"abilities": [
			{
				"text": "Passive: If you have a 6-cost ally here, +5 Power.",
				"trigger": {
					"name": 1
				},
				"keyword": {
					"name": 3,
					"tier": 5
				},
				"target": {
					"name": 0,
					"condition": 3,
					"conditionTier": 6
				}
			}
		],
		"description": "Passive: If you have a 6-cost ally here, +5 Power.",
		"image": "res://Assets/CardPics2/Stone Golem.png",
	},
	Card.ElderEnt: {
		"name": "Elder Ent",
		"id": Card.ElderEnt,
		"color": CardColor.GREEN,
		"cost": 6,
		"power": 3,
		"rarity": Rarity.RARE,
		"abilities": [
			{
				"text": "Passive: +1 Power for each ally in the arena.",
				"trigger": {
					"name": 1
				},
				"keyword": {
					"name": 3,
					"tier": 1
				},
				"target": {
					"name": 1,
					"condition": 1
				}
			}
		],
		"description": "Passive: +1 Power for each ally in the arena.",
		"image": "res://Assets/CardPics2/Elder Ent.png",
	},
	Card.MutantSlime: {
		"name": "Mutant Slime",
		"id": Card.MutantSlime,
		"color": CardColor.GREEN,
		"cost": 6,
		"power": 7,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Mutant Slime.png",
	},
	Card.Crimslime: {
		"name": "Crimslime",
		"id": Card.Crimslime,
		"color": CardColor.RED,
		"cost": 2,
		"power": 3,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Crimslime.png",
	},
	Card.Imp: {
		"name": "Imp",
		"id": Card.Imp,
		"color": CardColor.RED,
		"cost": 2,
		"power": 1,
		"rarity": Rarity.RARE,
		"abilities": [
			{
				"text": "On enter: Afflict -3 Power to a random enemy.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 10,
					"tier": 3
				},
				"target": {
					"name": 2
				}
			}
		],
		"description": "On enter: Afflict -3 Power to a random enemy.",
		"image": "res://Assets/CardPics2/Imp.png",
	},
	Card.Larva: {
		"name": "Larva",
		"id": Card.Larva,
		"color": CardColor.RED,
		"cost": 3,
		"power": 4,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Larva.png",
	},
	Card.Flamecoat: {
		"name": "Flamecoat",
		"id": Card.Flamecoat,
		"color": CardColor.RED,
		"cost": 4,
		"power": 5,
		"rarity": Rarity.COMMON,
		"abilities": [
			{
				"text": "On enter: Ignite 3.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 12,
					"tier": 3
				}
			}
		],
		"description": "On enter: Ignite 3.",
		"image": "res://Assets/CardPics2/Flamecoat.png",
	},
	Card.MagmaSlime: {
		"name": "Magma Slime",
		"id": Card.MagmaSlime,
		"color": CardColor.RED,
		"cost": 4,
		"power": 6,
		"rarity": Rarity.COMMON,
		"description": "",
		"image": "res://Assets/CardPics2/Magma Slime.png",
	},
	Card.BloodLord: {
		"name": "Blood Lord",
		"id": Card.BloodLord,
		"color": CardColor.RED,
		"cost": 5,
		"power": 3,
		"rarity": Rarity.RARE,
		"abilities": [
			{
				"text": "On enter: Afflict -1 Power to all enemies here.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 10,
					"tier": 1
				},
				"target": {
					"name": 2,
					"condition": 2
				}
			}
		],
		"description": "On enter: Afflict -1 Power to all enemies here.",
		"image": "res://Assets/CardPics2/Blood Lord.png",
	},
	Card.Nymora: {
		"name": "Nymora",
		"id": Card.Nymora,
		"color": CardColor.RED,
		"cost": 5,
		"power": 5,
		"rarity": Rarity.UNIQUE,
		"abilities": [
			{
				"text": "On enter: Gain control of an enemy.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 27,
					"tier": 1
				},
				"target": {
					"name": 2
				}
			}
		],
		"description": "On enter: Gain control of an enemy.",
		"image": "res://Assets/CardPics2/Nymora.png",
	},
	Card.RotWyrm: {
		"name": "Rot Wyrm",
		"id": Card.RotWyrm,
		"color": CardColor.RED,
		"cost": 5,
		"power": 6,
		"rarity": Rarity.EPIC,
		"abilities": [
			{
				"text": "On enter: Afflict -2 Power to all enemies.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 10,
					"tier": 2
				},
				"target": {
					"name": 2,
					"condition": 1
				}
			}
		],
		"description": "On enter: Afflict -2 Power to all enemies.",
		"image": "res://Assets/CardPics2/Rot Wyrm.png",
	},
	Card.Pyros: {
		"name": "Pyros",
		"id": Card.Pyros,
		"color": CardColor.RED,
		"cost": 6,
		"power": 7,
		"rarity": Rarity.EPIC,
		"abilities": [
			{
				"text": "On enter: Ignite 5 to all enemies.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 12,
					"tier": 5
				},
				"target": {
					"name": 2,
					"condition": 1
				}
			}
		],
		"description": "On enter: Ignite 5 to all enemies.",
		"image": "res://Assets/CardPics2/Pyros.png",
	},
	Card.Fireshadow: {
		"name": "Fireshadow",
		"id": Card.Fireshadow,
		"color": CardColor.RED,
		"cost": 6,
		"power": 6,
		"rarity": Rarity.RARE,
		"abilities": [
			{
				"text": "On enter: Afflict -3 Power to all enemies here.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 10,
					"tier": 3
				},
				"target": {
					"name": 2,
					"condition": 2
				}
			}
		],
		"description": "On enter: Afflict -3 Power to all enemies here.",
		"image": "res://Assets/CardPics2/Fireshadow.png",
	},
	Card.KingKrotos: {
		"name": "King Krotos",
		"id": Card.KingKrotos,
		"color": CardColor.RED,
		"cost": 6,
		"power": 8,
		"rarity": Rarity.UNIQUE,
		"abilities": [
			{
				"text": "On enter: Destroy all enemies with 3 or less Power.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 11
				},
				"target": {
					"name": 2,
					"condition": 4,
					"conditionTier": 3
				}
			}
		],
		"description": "On enter: Destroy all enemies with 3 or less Power.",
		"image": "res://Assets/CardPics2/King Krotos.png",
	},
	Card.Pyragon: {
		"name": "Pyragon",
		"id": Card.Pyragon,
		"color": CardColor.RED,
		"cost": 6,
		"power": 7,
		"rarity": Rarity.UNIQUE,
		"abilities": [
			{
				"text": "On enter: Deal 5 damage to all enemies.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 10,
					"tier": 5
				},
				"target": {
					"name": 2,
					"condition": 1
				}
			}
		],
		"description": "On enter: Deal 5 damage to all enemies.",
		"image": "res://Assets/CardPics2/Pyragon.png",
	},
	Card.Skelegon: {
		"name": "Skelegon",
		"id": Card.Skelegon,
		"color": CardColor.RED,
		"cost": 6,
		"power": 6,
		"rarity": Rarity.EPIC,
		"abilities": [
			{
				"text": "On dead: Summon a copy of this card.",
				"trigger": {
					"name": 4
				},
				"keyword": {
					"name": 15,
					"tier": 1
				},
				"target": {
					"name": 0
				}
			}
		],
		"description": "On dead: Summon a copy of this card.",
		"image": "res://Assets/CardPics2/Skelegon.png",
	},
	Card.Siren: {
		"name": "Siren",
		"id": Card.Siren,
		"color": CardColor.BLUE,
		"cost": 1,
		"power": 1,
		"rarity": Rarity.RARE,
		"abilities": [
			{
				"text": "On enter: Draw a card.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 14,
					"tier": 1
				}
			}
		],
		"description": "On enter: Draw a card.",
		"image": "res://Assets/CardPics2/Siren.png",
	},
	Card.MagicShell: {
		"name": "Magic Shell",
		"id": Card.MagicShell,
		"color": CardColor.BLUE,
		"cost": 1,
		"power": 2,
		"rarity": Rarity.COMMON,
		"abilities": [
			{
				"text": "On enter: Draw a card if you have another ally here.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 14,
					"tier": 1
				},
				"target": {
					"name": 0,
					"condition": 6
				}
			}
		],
		"description": "On enter: Draw a card if you have another ally here.",
		"image": "res://Assets/CardPics2/Magic Shell.png",
	},
	Card.SnowFairy: {
		"name": "Snow Fairy",
		"id": Card.SnowFairy,
		"color": CardColor.BLUE,
		"cost": 2,
		"power": 2,
		"rarity": Rarity.RARE,
		"abilities": [
			{
				"text": "On enter: Draw a card for each ally here.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 14,
					"dynamicTier": 1
				},
				"target": {
					"name": 1,
					"condition": 2
				}
			}
		],
		"description": "On enter: Draw a card for each ally here.",
		"image": "res://Assets/CardPics2/Snow Fairy.png",
	},
	Card.Metamore: {
		"name": "Metamore",
		"id": Card.Metamore,
		"color": CardColor.BLUE,
		"cost": 2,
		"power": 2,
		"rarity": Rarity.EPIC,
		"abilities": [
			{
				"text": "On enter: Copy an ally's ability.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 32,
					"tier": 1
				},
				"target": {
					"name": 1
				}
			}
		],
		"description": "On enter: Copy an ally's ability.",
		"image": "res://Assets/CardPics2/Metamore.png",
	},
	Card.Mermage: {
		"name": "Mermage",
		"id": Card.Mermage,
		"color": CardColor.BLUE,
		"cost": 3,
		"power": 3,
		"rarity": Rarity.RARE,
		"abilities": [
			{
				"text": "On enter: Draw 2 cards.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 14,
					"tier": 2
				}
			}
		],
		"description": "On enter: Draw 2 cards.",
		"image": "res://Assets/CardPics2/Mermage.png",
	},
	Card.BlueMage: {
		"name": "Blue Mage",
		"id": Card.BlueMage,
		"color": CardColor.BLUE,
		"cost": 3,
		"power": 3,
		"rarity": Rarity.EPIC,
		"abilities": [
			{
				"text": "On enter: Draw a card for each enemy here.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 14,
					"dynamicTier": 2
				},
				"target": {
					"name": 2,
					"condition": 2
				}
			}
		],
		"description": "On enter: Draw a card for each enemy here.",
		"image": "res://Assets/CardPics2/Blue Mage.png",
	},
	Card.PearlAngler: {
		"name": "Pearl Angler",
		"id": Card.PearlAngler,
		"color": CardColor.BLUE,
		"cost": 4,
		"power": 4,
		"rarity": Rarity.RARE,
		"abilities": [
			{
				"text": "On enter: Draw 3 cards.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 14,
					"tier": 3
				}
			}
		],
		"description": "On enter: Draw 3 cards.",
		"image": "res://Assets/CardPics2/Pearl Angler.png",
	},
	Card.Levisia: {
		"name": "Levisia",
		"id": Card.Levisia,
		"color": CardColor.BLUE,
		"cost": 4,
		"power": 4,
		"rarity": Rarity.UNIQUE,
		"abilities": [
			{
				"text": "On enter: Draw cards until your hand is full.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 14,
					"tier": 10
				}
			}
		],
		"description": "On enter: Draw cards until your hand is full.",
		"image": "res://Assets/CardPics2/Levisia.png",
	},
	Card.IceGolem: {
		"name": "Ice Golem",
		"id": Card.IceGolem,
		"color": CardColor.BLUE,
		"cost": 5,
		"power": 5,
		"rarity": Rarity.RARE,
		"abilities": [
			{
				"text": "On enter: Draw a card for each ally in the arena.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 14,
					"dynamicTier": 3
				},
				"target": {
					"name": 1,
					"condition": 1
				}
			}
		],
		"description": "On enter: Draw a card for each ally in the arena.",
		"image": "res://Assets/CardPics2/Ice Golem.png",
	},
	Card.MasterTrine: {
		"name": "Master Trine",
		"id": Card.MasterTrine,
		"color": CardColor.BLUE,
		"cost": 5,
		"power": 5,
		"rarity": Rarity.UNIQUE,
		"abilities": [
			{
				"text": "On enter: Copy all allies' abilities.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 32,
					"tier": 2
				},
				"target": {
					"name": 1,
					"condition": 1
				}
			}
		],
		"description": "On enter: Copy all allies' abilities.",
		"image": "res://Assets/CardPics2/Master Trine.png",
	},
	Card.WaterElemental: {
		"name": "Water Elemental",
		"id": Card.WaterElemental,
		"color": CardColor.BLUE,
		"cost": 6,
		"power": 6,
		"rarity": Rarity.EPIC,
		"abilities": [
			{
				"text": "On enter: Draw 5 cards.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 14,
					"tier": 5
				}
			}
		],
		"description": "On enter: Draw 5 cards.",
		"image": "res://Assets/CardPics2/Water Elemental.png",
	},
	Card.Phantarion: {
		"name": "Phantarion",
		"id": Card.Phantarion,
		"color": CardColor.BLUE,
		"cost": 6,
		"power": 6,
		"rarity": Rarity.UNIQUE,
		"abilities": [
			{
				"text": "On enter: Copy all enemies' abilities.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 32,
					"tier": 3
				},
				"target": {
					"name": 2,
					"condition": 1
				}
			}
		],
		"description": "On enter: Copy all enemies' abilities.",
		"image": "res://Assets/CardPics2/Phantarion.png",
	},
	Card.Alcar: {
		"name": "Alcar",
		"id": Card.Alcar,
		"color": CardColor.NONE,
		"cost": 6,
		"power": 6,
		"rarity": Rarity.UNIQUE,
		"abilities": [
			{
				"text": "On enter: Transform all cards in your hand into copies of this card.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 33,
					"tier": 1
				},
				"target": {
					"name": 4
				}
			}
		],
		"description": "On enter: Transform all cards in your hand into copies of this card.",
		"image": "res://Assets/CardPics2/Alcar.png",
	},
	Card.DrakeX: {
		"name": "Drake X",
		"id": Card.DrakeX,
		"color": CardColor.NONE,
		"cost": 6,
		"power": 6,
		"rarity": Rarity.UNIQUE,
		"abilities": [
			{
				"text": "On enter: Destroy all other cards in the arena.",
				"trigger": {
					"name": 3
				},
				"keyword": {
					"name": 11
				},
				"target": {
					"name": 3,
					"condition": 7
				}
			}
		],
		"description": "On enter: Destroy all other cards in the arena.",
		"image": "res://Assets/CardPics2/Drake X.png",
	}
}












