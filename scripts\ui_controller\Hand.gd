class_name Hand
extends CardCollectionUIController

signal handSelectedIndex(int)
signal draggedIndex(bool)

@export var is_player := true
@export_node_path var deck_path
@onready var deck := get_node(deck_path)

@export_node_path var camera_path
@onready var camera := get_node(camera_path)

@export var spread_curve: Curve
@export var height_curve: Curve
@export var rotation_curve: Curve

@export var width_array: Array[float]

@onready var untuck_position := Vector3(-0.579, 10.556, -3.946)
@onready var enemy_untuck_position := Vector3(-0.626, 13.565, -1.112)
var tuck_position := Vector3(-0.579, 10.556, -3.432)
var enemy_tuck_position := Vector3(-0.626, 13.565, -0.62)
var target_transform

var hand_order = 0


# Called when the node enters the scene tree for the first time.
func _ready():
	super()
	if is_player:
		position = tuck_position
	else:
		position = enemy_tuck_position

#func _physics_process(delta):
	#if is_player:
		#transform = transform.interpolate_with(target_transform, Const.ANIM_SPEED * delta)

func onCardSelected(_card: Card_3DUI) -> void:
	handSelectedIndex.emit(_card.get_index())

func highlightSelectedCard(_index: int) -> void:
	for card in get_children():
	# for card in super.getCardCollection().getCards():
		if card.get_index() == _index:
			card.scale = Vector3.ONE * 1.2
		else:
			card.scale = Vector3.ONE

# add cards into this CardCollection
func add(p_cards: Array[Card]) -> void:
	#var tween = create_tween()
	self.cardCollection.add(p_cards)
	for card in p_cards:
		var card3d = card_3dui.instantiate()
		var battleNode = get_tree().get_root().get_node("Battle")
		card3d.setOnHand(true)
		card3d.displayCard(card)
		add_child(card3d)
		if card.getName() == "Al\'car":
			card3d.divineParticle.visible = true
		if card.getName() == "Mor\'co":
			card3d.darkParticle.visible = true
		if card.is_ignited:
			card3d.igniteParticle.visible = true
		if !is_player:
			card3d.set_flipup(false)
		card3d.set_layer(20, true)
		card3d.set_layer(1, false)
		card3d.collisionHand.disabled = false
		var movingQueue
		if getOwner().isOnMainSide():
			movingQueue = battleNode.find_child("Player").movingQueue
		else:
			movingQueue = battleNode.find_child("Enemy").movingQueue
		if movingQueue.get_child_count() > 0:
			card3d.transform.origin = movingQueue.get_child(0).global_position
			card3d.rotation = movingQueue.get_child(0).rotation
			movingQueue.get_child(0).free()
		#card3d.transform.origin = getOwner().getDeck().global_position
		#await get_tree().create_timer(0.4).timeout
		#card3d.transform.origin.x = get_viewport().get_camera_3d().transform.origin.x +7
		#card3d.transform.origin.y = get_viewport().get_camera_3d().transform.origin.y -18
		#card3d.transform.origin.z = get_viewport().get_camera_3d().transform.origin.z +4.5
		card3d.cardSelected.connect(onCardSelected)
		card3d.cardSelected.connect(battleNode.inspect_card)
		battleNode.inspecting.connect(card3d.change_collision_mode)
		await spanCard()
	
	#for card3d in get_children():
		#var cache_transform = card3d.cardArt.global_transform
		#card3d.cardArt.top_level = true
		#card3d.cardArt.global_transform = cache_transform

# add cards into this CardCollection
func addSingle(p_cards:Card) -> void:
	#var tween = create_tween()
	self.cardCollection.addSingle(p_cards)
	var card3d = card_3dui.instantiate()
	var battleNode = get_tree().get_root().get_node("Battle")
	card3d.setOnHand(true)
	card3d.displayCard(p_cards)
	add_child(card3d)
	if p_cards.getName() == "Al\'car":
		card3d.divineParticle.visible = true
	if p_cards.getName() == "Mor\'co":
		card3d.darkParticle.visible = true
	if p_cards.is_ignited:
		card3d.igniteParticle.visible = true
	if !is_player:
		card3d.set_flipup(false)
	card3d.set_layer(20, true)
	card3d.set_layer(1, false)
	card3d.collisionHand.disabled = false
	var movingQueue
	if getOwner().isOnMainSide():
		movingQueue = battleNode.find_child("Player").movingQueue
	else:
		movingQueue = battleNode.find_child("Enemy").movingQueue
	if movingQueue.get_child_count() > 0:
		card3d.transform.origin = movingQueue.get_child(0).global_position
		card3d.rotation = movingQueue.get_child(0).rotation
		movingQueue.get_child(0).free()
	#card3d.transform.origin = getOwner().getDeck().global_position
	#await get_tree().create_timer(0.4).timeout
	#card3d.transform.origin.x = get_viewport().get_camera_3d().transform.origin.x +7
	#card3d.transform.origin.y = get_viewport().get_camera_3d().transform.origin.y -18
	#card3d.transform.origin.z = get_viewport().get_camera_3d().transform.origin.z +4.5
	card3d.cardSelected.connect(onCardSelected)
	card3d.cardSelected.connect(battleNode.inspect_card)
	battleNode.inspecting.connect(card3d.change_collision_mode)
	await spanCard()
	

#func add_3D(card3d : Card_3DUI):
	#add_child(card3d)
	#card3d.set_layer(20, true)
	#card3d.collisionHand.disabled = false
	#card3d.card_order = hand_order
	#hand_order+=1
	#spanCard()

# move cards to another CardCollection
func removeCards(p_cards: Array[Card]) -> Array[Card]:
	for card in p_cards:
		if self.cardCollection.has(card):
			var cardIndex = self.cardCollection.cards.find(card)
			var card3d = get_child(cardIndex)
			card3d.setOnHand(false)
			var movingQueue
			if getOwner().isOnMainSide():
				movingQueue = battleNode.find_child("Player").movingQueue
			else:
				movingQueue = battleNode.find_child("Enemy").movingQueue
				#if get_child_count()>0:
			card3d.reparent(movingQueue)
			#remove_child(card3d)
	await spanCard()
	return self.cardCollection.removeCards(p_cards)

func removeSingle(p_cards: Card) -> Card:
	var cardIndex = self.cardCollection.cards.find(p_cards)
	var card3d = get_child(cardIndex)
	card3d.setOnHand(false)
	var movingQueue
	if getOwner().isOnMainSide():
		movingQueue = battleNode.find_child("Player").movingQueue
	else:
		movingQueue = battleNode.find_child("Enemy").movingQueue
		#if get_child_count()>0:
	card3d.reparent(movingQueue)
	#remove_child(card3d)
	await spanCard()
	return self.cardCollection.removeSingle(p_cards)
	
# move top x cards to another CardCollection
func pull(amount: int) -> Array[Card]:
	for i in amount:
		if not self.cardCollection.isEmpty():
			get_child(0).setOnHand(false)
			var movingQueue
			if getOwner().isOnMainSide():
				movingQueue = battleNode.find_child("Player").movingQueue
			else:
				movingQueue = battleNode.find_child("Enemy").movingQueue
				#if get_child_count()>0:
			get_child(0).reparent(movingQueue)
				#remove_child(get_child(0))
	await spanCard()
	return self.cardCollection.pull(amount)

# remove card at index from this CardCollection
func removeAt(index: int) -> Card:
	get_child(index).setOnHand(false)
	var movingQueue
	if getOwner().isOnMainSide():
		movingQueue = battleNode.find_child("Player").movingQueue
	else:
		movingQueue = battleNode.find_child("Enemy").movingQueue
		#if get_child_count()>0:
	get_child(index).reparent(movingQueue)
	#remove_child(get_child(index))
	await spanCard()
	return self.cardCollection.removeAt(index)


func spanCard() -> void:
	var max_width
	if get_child_count() >= width_array.size(): max_width = width_array.back()
	else: max_width = width_array[get_child_count()]
	
	#var sorted_hand_order := get_children()
	#sorted_hand_order.sort_custom(sort_hand_order)
	#for card in get_children():
		#move_child(card, sorted_hand_order.find(card))
	
	for card in get_children():
		var tween = card.create_tween()
		var destination := global_transform
		var rotation
		
		var hand_ratio = 0.5
		if get_child_count() > 1:
			hand_ratio = float(card.get_index()) / float(get_child_count() - 1)
		
		if is_player:
			#destination.basis.rotated(Vector3.RIGHT, deg_to_rad(90))
			destination.basis = camera.global_transform.basis # .rotated(Vector3.FORWARD, deg_to_rad(90))
			destination.origin.x += spread_curve.sample(hand_ratio) * max_width
			rotation = rotation_curve.sample(hand_ratio) * 0.5
			destination.origin += camera.basis * (Vector3.UP * height_curve.sample(hand_ratio) +  Vector3.UP * (1+spread_curve.sample(hand_ratio)) *0.2)
			destination.origin += camera.basis * Vector3.BACK * (1+spread_curve.sample(hand_ratio)) *0.2
			#destination.origin += camera.basis * Vector3.BACK * 1.2
		else:
			destination.origin.x -= 2
			destination.origin.y -= 5
			destination.basis = camera.global_transform.basis.rotated(Vector3.RIGHT, deg_to_rad(169))
			destination.origin.x += spread_curve.sample(hand_ratio) * max_width
			rotation = rotation_curve.sample(hand_ratio) * 0.5
			destination.origin -= camera.basis * (Vector3.UP * height_curve.sample(hand_ratio) +  Vector3.UP * (1+spread_curve.sample(hand_ratio)) *0.2)
			destination.origin += camera.basis * Vector3.BACK * (1+spread_curve.sample(hand_ratio)) *0.2
			#destination.origin -= camera.basis * Vector3.UP * height_curve.sample(hand_ratio)
			#destination.origin += camera.basis * Vector3.BACK * hand_ratio * 0.1
			#destination.basis = global_transform.basis
			#destination.origin.x += spread_curve.sample(hand_ratio) * max_width
			#destination.origin += global_transform.basis * Vector3.UP * height_curve.sample(hand_ratio) * 0.5
			#destination.origin += global_transform.basis * Vector3.BACK * hand_ratio * 0.1
		
		#tween.tween_property(card, "transform.origin", destination.origin, 0.1)
		#tween.tween_property(card, "transform.basis", destination.basis, 0.1)
		#tween.tween_property(card, "rotation_degrees", rotation, 0.1)
	
		tween.tween_callback(self.tween_cb.bind(card, destination, rotation))
		tween.tween_interval(0.3)
	
	var arenaNode = get_parent().get_parent()
	var cardOwner = get_parent()
	if cardOwner.get_name() == TurnService.ActivePlayer.get_name():
		arenaNode.situationUpdate.emit(cardOwner)
	await get_tree().create_timer(Const.AWAIT_TIME * 0.1).timeout
		
		#card.rotation.x = deg_to_rad(-90)
		#card.target_transform = card.global_position
	#var positionX = -2 * get_child_count() / 2
	#for card in get_children():
		#var tween = card.create_tween()
		#tween.tween_property(card, "position", Vector3(positionX, card.transform.origin.y, card.transform.origin.z), Const.AWAIT_TIME / 10)
		#positionX += 2
	##for card in get_children():
		##card.target_transform = card.global_transform
	#await get_tree().create_timer(Const.AWAIT_TIME / 10).timeout

func tween_cb(card: Card_3DUI, destination, rotation):
	card.target_transform.origin = destination.origin
	card.target_transform.basis = destination.basis
	card.target_rotation = rotation
	#card.rotate_y(deg_to_rad(90))

func sort_hand_order(a, b) -> bool:
	return a.card_order < b.card_order

func tuck_cards() -> void:
	position = tuck_position
	spanCard()
	
func untuck_cards() -> void:
	position = untuck_position
	spanCard()

func enemy_tuck_cards() -> void:
	position = enemy_tuck_position
	spanCard()
	
func enemy_untuck_cards() -> void:
	position = enemy_untuck_position
	spanCard()
#func lightCastableCard(player : Player):
	#if player.get_name() != TurnService.ActivePlayer.get_name():
		#return
	#var arenaNode = get_parent().get_parent()
	#var cardIndex = 0
	#for card in get_children():
		#var laneIndex = 0
		#for lane in get_tree().get_nodes_in_group("lane"):
			#var slotIndex = 0
			#for slot in lane.getAllSlots():
				#if arenaNode.isCastable(arenaNode.getCardByHandIndex(cardIndex), laneIndex, slotIndex, player):
					#card.castableVfx.visible = true
				#else:
					#card.castableVfx.visible = false
				#slotIndex += 1
			#laneIndex += 1
		#cardIndex += 1


func _on_area_2d_mouse_entered() -> void:
	untuck_cards()


func _on_area_2d_mouse_exited() -> void:
	tuck_cards()


func _on_area_2d_enemy_mouse_entered() -> void:
	enemy_untuck_cards()


func _on_area_2d_enemy_mouse_exited() -> void:
	enemy_tuck_cards()
