[gd_scene load_steps=21 format=3 uid="uid://dvcjlds4ecrbx"]

[ext_resource type="Texture2D" uid="uid://b6p86xxc7yair" path="res://Assets/card-v2_Untitled_Artwork.png" id="1_fqpum"]
[ext_resource type="FontFile" uid="uid://2k4s1sms2p1h" path="res://Assets/Fonts/Samurai Warrior.ttf" id="2_rfw4o"]
[ext_resource type="Script" path="res://addons/drag_and_snap/nodes/draggable.gd" id="3_wkr8g"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_d4sqk"]
resource_name = "Material.001"
cull_mode = 2
vertex_color_use_as_albedo = true
albedo_color = Color(0.166169, 0.166169, 0.166169, 1)
roughness = 0.4

[sub_resource type="ArrayMesh" id="ArrayMesh_18hhm"]
_surfaces = [{
"aabb": AABB(-1, -1, -1, 2, 2, 1.94295),
"format": 34359742465,
"index_count": 510,
"index_data": PackedByteArray(69, 0, 44, 0, 19, 0, 69, 0, 91, 0, 44, 0, 92, 0, 49, 0, 86, 0, 92, 0, 67, 0, 49, 0, 1, 0, 87, 0, 50, 0, 1, 0, 39, 0, 87, 0, 21, 0, 38, 0, 2, 0, 21, 0, 43, 0, 38, 0, 0, 0, 6, 0, 3, 0, 0, 0, 5, 0, 6, 0, 3, 0, 7, 0, 4, 0, 3, 0, 6, 0, 7, 0, 5, 0, 18, 0, 6, 0, 5, 0, 15, 0, 18, 0, 6, 0, 8, 0, 7, 0, 6, 0, 18, 0, 8, 0, 1, 0, 12, 0, 9, 0, 1, 0, 11, 0, 12, 0, 9, 0, 13, 0, 10, 0, 9, 0, 12, 0, 13, 0, 11, 0, 7, 0, 12, 0, 11, 0, 4, 0, 7, 0, 12, 0, 8, 0, 13, 0, 12, 0, 7, 0, 8, 0, 2, 0, 17, 0, 14, 0, 2, 0, 16, 0, 17, 0, 14, 0, 18, 0, 15, 0, 14, 0, 17, 0, 18, 0, 16, 0, 13, 0, 17, 0, 16, 0, 10, 0, 13, 0, 17, 0, 8, 0, 18, 0, 17, 0, 13, 0, 8, 0, 19, 0, 25, 0, 22, 0, 19, 0, 24, 0, 25, 0, 22, 0, 26, 0, 23, 0, 22, 0, 25, 0, 26, 0, 24, 0, 37, 0, 25, 0, 24, 0, 34, 0, 37, 0, 25, 0, 27, 0, 26, 0, 25, 0, 37, 0, 27, 0, 20, 0, 31, 0, 28, 0, 20, 0, 30, 0, 31, 0, 28, 0, 32, 0, 29, 0, 28, 0, 31, 0, 32, 0, 30, 0, 26, 0, 31, 0, 30, 0, 23, 0, 26, 0, 31, 0, 27, 0, 32, 0, 31, 0, 26, 0, 27, 0, 21, 0, 36, 0, 33, 0, 21, 0, 35, 0, 36, 0, 33, 0, 37, 0, 34, 0, 33, 0, 36, 0, 37, 0, 35, 0, 32, 0, 36, 0, 35, 0, 29, 0, 32, 0, 36, 0, 27, 0, 37, 0, 36, 0, 32, 0, 27, 0, 48, 0, 54, 0, 51, 0, 48, 0, 53, 0, 54, 0, 51, 0, 55, 0, 52, 0, 51, 0, 54, 0, 55, 0, 53, 0, 66, 0, 54, 0, 53, 0, 63, 0, 66, 0, 54, 0, 56, 0, 55, 0, 54, 0, 66, 0, 56, 0, 49, 0, 60, 0, 57, 0, 49, 0, 59, 0, 60, 0, 57, 0, 61, 0, 58, 0, 57, 0, 60, 0, 61, 0, 59, 0, 55, 0, 60, 0, 59, 0, 52, 0, 55, 0, 60, 0, 56, 0, 61, 0, 60, 0, 55, 0, 56, 0, 50, 0, 65, 0, 62, 0, 50, 0, 64, 0, 65, 0, 62, 0, 66, 0, 63, 0, 62, 0, 65, 0, 66, 0, 64, 0, 61, 0, 65, 0, 64, 0, 58, 0, 61, 0, 65, 0, 56, 0, 66, 0, 65, 0, 61, 0, 56, 0, 67, 0, 73, 0, 70, 0, 67, 0, 72, 0, 73, 0, 70, 0, 74, 0, 71, 0, 70, 0, 73, 0, 74, 0, 72, 0, 85, 0, 73, 0, 72, 0, 82, 0, 85, 0, 73, 0, 75, 0, 74, 0, 73, 0, 85, 0, 75, 0, 68, 0, 79, 0, 76, 0, 68, 0, 78, 0, 79, 0, 76, 0, 80, 0, 77, 0, 76, 0, 79, 0, 80, 0, 78, 0, 74, 0, 79, 0, 78, 0, 71, 0, 74, 0, 79, 0, 75, 0, 80, 0, 79, 0, 74, 0, 75, 0, 69, 0, 84, 0, 81, 0, 69, 0, 83, 0, 84, 0, 81, 0, 85, 0, 82, 0, 81, 0, 84, 0, 85, 0, 83, 0, 80, 0, 84, 0, 83, 0, 77, 0, 80, 0, 84, 0, 75, 0, 85, 0, 84, 0, 80, 0, 75, 0, 69, 0, 93, 0, 91, 0, 69, 0, 81, 0, 93, 0, 81, 0, 95, 0, 93, 0, 81, 0, 82, 0, 95, 0, 82, 0, 94, 0, 95, 0, 82, 0, 72, 0, 94, 0, 72, 0, 92, 0, 94, 0, 72, 0, 67, 0, 92, 0, 19, 0, 83, 0, 69, 0, 19, 0, 22, 0, 83, 0, 22, 0, 77, 0, 83, 0, 22, 0, 23, 0, 77, 0, 23, 0, 76, 0, 77, 0, 23, 0, 30, 0, 76, 0, 30, 0, 68, 0, 76, 0, 30, 0, 20, 0, 68, 0, 2, 0, 35, 0, 21, 0, 2, 0, 14, 0, 35, 0, 14, 0, 29, 0, 35, 0, 14, 0, 15, 0, 29, 0, 15, 0, 28, 0, 29, 0, 15, 0, 5, 0, 28, 0, 5, 0, 20, 0, 28, 0, 5, 0, 0, 0, 20, 0, 48, 0, 78, 0, 68, 0, 48, 0, 51, 0, 78, 0, 51, 0, 71, 0, 78, 0, 51, 0, 52, 0, 71, 0, 52, 0, 70, 0, 71, 0, 52, 0, 59, 0, 70, 0, 59, 0, 67, 0, 70, 0, 59, 0, 49, 0, 67, 0, 1, 0, 42, 0, 39, 0, 1, 0, 9, 0, 42, 0, 9, 0, 41, 0, 42, 0, 9, 0, 10, 0, 41, 0, 10, 0, 40, 0, 41, 0, 10, 0, 16, 0, 40, 0, 16, 0, 38, 0, 40, 0, 16, 0, 2, 0, 38, 0, 87, 0, 64, 0, 50, 0, 87, 0, 89, 0, 64, 0, 89, 0, 58, 0, 64, 0, 89, 0, 90, 0, 58, 0, 90, 0, 57, 0, 58, 0, 90, 0, 88, 0, 57, 0, 88, 0, 49, 0, 57, 0, 88, 0, 86, 0, 49, 0, 50, 0, 11, 0, 1, 0, 50, 0, 62, 0, 11, 0, 62, 0, 4, 0, 11, 0, 62, 0, 63, 0, 4, 0, 63, 0, 3, 0, 4, 0, 63, 0, 53, 0, 3, 0, 53, 0, 0, 0, 3, 0, 53, 0, 48, 0, 0, 0, 44, 0, 24, 0, 19, 0, 44, 0, 46, 0, 24, 0, 46, 0, 34, 0, 24, 0, 46, 0, 47, 0, 34, 0, 47, 0, 33, 0, 34, 0, 47, 0, 45, 0, 33, 0, 45, 0, 21, 0, 33, 0, 45, 0, 43, 0, 21, 0, 68, 0, 0, 0, 48, 0, 68, 0, 20, 0, 0, 0),
"lods": [0.0150424, PackedByteArray(69, 0, 44, 0, 19, 0, 69, 0, 91, 0, 44, 0, 69, 0, 93, 0, 91, 0, 44, 0, 46, 0, 19, 0, 69, 0, 95, 0, 93, 0, 69, 0, 82, 0, 95, 0, 69, 0, 85, 0, 82, 0, 69, 0, 75, 0, 85, 0, 69, 0, 80, 0, 75, 0, 69, 0, 77, 0, 80, 0, 23, 0, 77, 0, 69, 0, 19, 0, 23, 0, 69, 0, 23, 0, 27, 0, 26, 0, 19, 0, 27, 0, 23, 0, 46, 0, 21, 0, 19, 0, 19, 0, 21, 0, 27, 0, 46, 0, 47, 0, 21, 0, 47, 0, 45, 0, 21, 0, 45, 0, 43, 0, 21, 0, 21, 0, 43, 0, 38, 0, 21, 0, 38, 0, 2, 0, 21, 0, 20, 0, 27, 0, 2, 0, 20, 0, 21, 0, 20, 0, 26, 0, 27, 0, 20, 0, 23, 0, 26, 0, 23, 0, 20, 0, 77, 0, 2, 0, 38, 0, 40, 0, 20, 0, 68, 0, 77, 0, 68, 0, 80, 0, 77, 0, 2, 0, 0, 0, 20, 0, 68, 0, 20, 0, 0, 0, 1, 0, 2, 0, 40, 0, 1, 0, 40, 0, 41, 0, 0, 0, 2, 0, 8, 0, 2, 0, 1, 0, 8, 0, 0, 0, 8, 0, 4, 0, 1, 0, 41, 0, 42, 0, 1, 0, 7, 0, 8, 0, 1, 0, 42, 0, 39, 0, 1, 0, 4, 0, 7, 0, 1, 0, 39, 0, 87, 0, 63, 0, 4, 0, 1, 0, 1, 0, 87, 0, 50, 0, 50, 0, 63, 0, 1, 0, 87, 0, 89, 0, 50, 0, 50, 0, 58, 0, 63, 0, 89, 0, 58, 0, 50, 0, 89, 0, 90, 0, 58, 0, 63, 0, 58, 0, 66, 0, 58, 0, 56, 0, 66, 0, 4, 0, 8, 0, 7, 0, 48, 0, 0, 0, 4, 0, 63, 0, 48, 0, 4, 0, 68, 0, 0, 0, 48, 0, 48, 0, 63, 0, 66, 0, 48, 0, 66, 0, 55, 0, 66, 0, 56, 0, 55, 0, 49, 0, 48, 0, 55, 0, 49, 0, 55, 0, 56, 0, 49, 0, 56, 0, 58, 0, 90, 0, 49, 0, 58, 0, 90, 0, 88, 0, 49, 0, 88, 0, 86, 0, 49, 0, 92, 0, 49, 0, 86, 0, 48, 0, 49, 0, 67, 0, 92, 0, 67, 0, 49, 0, 48, 0, 67, 0, 68, 0, 67, 0, 92, 0, 94, 0, 82, 0, 67, 0, 94, 0, 82, 0, 94, 0, 95, 0, 67, 0, 82, 0, 85, 0, 68, 0, 67, 0, 74, 0, 67, 0, 85, 0, 74, 0, 85, 0, 75, 0, 74, 0, 68, 0, 74, 0, 80, 0, 80, 0, 74, 0, 75, 0), 0.238994, PackedByteArray(82, 0, 44, 0, 23, 0, 82, 0, 23, 0, 75, 0, 44, 0, 46, 0, 23, 0, 46, 0, 27, 0, 23, 0, 46, 0, 47, 0, 27, 0, 82, 0, 91, 0, 44, 0, 82, 0, 93, 0, 91, 0, 82, 0, 95, 0, 93, 0, 47, 0, 45, 0, 27, 0, 45, 0, 43, 0, 27, 0, 27, 0, 43, 0, 38, 0, 27, 0, 38, 0, 8, 0, 8, 0, 38, 0, 40, 0, 4, 0, 8, 0, 40, 0, 4, 0, 40, 0, 41, 0, 4, 0, 41, 0, 42, 0, 4, 0, 42, 0, 39, 0, 4, 0, 39, 0, 87, 0, 4, 0, 87, 0, 58, 0, 58, 0, 56, 0, 4, 0, 87, 0, 89, 0, 58, 0, 89, 0, 90, 0, 58, 0, 92, 0, 56, 0, 86, 0, 88, 0, 86, 0, 56, 0, 90, 0, 88, 0, 56, 0, 92, 0, 74, 0, 56, 0, 90, 0, 56, 0, 58, 0, 55, 0, 56, 0, 74, 0, 74, 0, 92, 0, 94, 0, 82, 0, 74, 0, 94, 0, 82, 0, 75, 0, 74, 0, 82, 0, 94, 0, 95, 0, 4, 0, 56, 0, 55, 0, 23, 0, 4, 0, 55, 0, 8, 0, 4, 0, 23, 0, 8, 0, 23, 0, 27, 0, 55, 0, 74, 0, 23, 0, 23, 0, 74, 0, 75, 0)],
"name": "Material.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 96,
"vertex_data": PackedByteArray(118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 63, 35, 252, 118, 63, 108, 227, 126, 191, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 191, 35, 252, 118, 63, 118, 101, 113, 63, 108, 227, 126, 191, 254, 31, 119, 63, 254, 31, 119, 63, 248, 139, 125, 191, 149, 213, 118, 63, 229, 250, 122, 63, 229, 250, 122, 191, 232, 211, 121, 63, 232, 211, 121, 63, 232, 211, 121, 191, 35, 252, 118, 63, 108, 227, 126, 63, 118, 101, 113, 191, 2, 185, 123, 63, 2, 185, 123, 63, 118, 101, 113, 191, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 191, 254, 31, 119, 63, 248, 139, 125, 63, 254, 31, 119, 191, 229, 250, 122, 63, 229, 250, 122, 63, 149, 213, 118, 191, 108, 227, 126, 63, 118, 101, 113, 63, 35, 252, 118, 191, 2, 185, 123, 63, 118, 101, 113, 63, 2, 185, 123, 191, 108, 227, 126, 63, 35, 252, 118, 63, 118, 101, 113, 191, 248, 139, 125, 63, 254, 31, 119, 63, 254, 31, 119, 191, 229, 250, 122, 63, 149, 213, 118, 63, 229, 250, 122, 191, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 191, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 191, 0, 0, 128, 63, 118, 101, 113, 191, 118, 101, 113, 191, 118, 101, 113, 63, 108, 227, 126, 191, 35, 252, 118, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 35, 252, 118, 63, 108, 227, 126, 191, 118, 101, 113, 191, 254, 31, 119, 63, 248, 139, 125, 191, 254, 31, 119, 191, 149, 213, 118, 63, 229, 250, 122, 191, 229, 250, 122, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 35, 252, 118, 63, 118, 101, 113, 191, 108, 227, 126, 191, 2, 185, 123, 63, 118, 101, 113, 191, 2, 185, 123, 191, 118, 101, 113, 63, 35, 252, 118, 191, 108, 227, 126, 191, 254, 31, 119, 63, 254, 31, 119, 191, 248, 139, 125, 191, 229, 250, 122, 63, 149, 213, 118, 191, 229, 250, 122, 191, 108, 227, 126, 63, 35, 252, 118, 191, 118, 101, 113, 191, 2, 185, 123, 63, 2, 185, 123, 191, 118, 101, 113, 191, 108, 227, 126, 63, 118, 101, 113, 191, 35, 252, 118, 191, 248, 139, 125, 63, 254, 31, 119, 191, 254, 31, 119, 191, 229, 250, 122, 63, 229, 250, 122, 191, 149, 213, 118, 191, 0, 0, 128, 63, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 63, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 63, 118, 101, 113, 63, 35, 252, 118, 63, 108, 227, 126, 63, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 191, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 191, 118, 101, 113, 63, 35, 252, 118, 63, 108, 227, 126, 191, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 191, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 63, 0, 0, 128, 191, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 191, 35, 252, 118, 191, 118, 101, 113, 63, 108, 227, 126, 191, 2, 185, 123, 191, 118, 101, 113, 63, 2, 185, 123, 191, 118, 101, 113, 191, 35, 252, 118, 63, 108, 227, 126, 191, 254, 31, 119, 191, 254, 31, 119, 63, 248, 139, 125, 191, 229, 250, 122, 191, 149, 213, 118, 63, 229, 250, 122, 191, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 191, 108, 227, 126, 191, 35, 252, 118, 63, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 191, 108, 227, 126, 191, 118, 101, 113, 63, 35, 252, 118, 191, 248, 139, 125, 191, 254, 31, 119, 63, 254, 31, 119, 191, 229, 250, 122, 191, 229, 250, 122, 63, 149, 213, 118, 191, 118, 101, 113, 191, 108, 227, 126, 63, 35, 252, 118, 191, 118, 101, 113, 191, 2, 185, 123, 63, 2, 185, 123, 191, 35, 252, 118, 191, 108, 227, 126, 63, 118, 101, 113, 191, 254, 31, 119, 191, 248, 139, 125, 63, 254, 31, 119, 191, 149, 213, 118, 191, 229, 250, 122, 63, 229, 250, 122, 191, 0, 0, 128, 191, 118, 101, 113, 191, 118, 101, 113, 191, 118, 101, 113, 191, 118, 101, 113, 191, 0, 0, 128, 191, 118, 101, 113, 191, 0, 0, 128, 191, 118, 101, 113, 191, 108, 227, 126, 191, 118, 101, 113, 191, 35, 252, 118, 191, 2, 185, 123, 191, 118, 101, 113, 191, 2, 185, 123, 191, 108, 227, 126, 191, 35, 252, 118, 191, 118, 101, 113, 191, 248, 139, 125, 191, 254, 31, 119, 191, 254, 31, 119, 191, 229, 250, 122, 191, 149, 213, 118, 191, 229, 250, 122, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 118, 101, 113, 191, 35, 252, 118, 191, 108, 227, 126, 191, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 191, 35, 252, 118, 191, 118, 101, 113, 191, 108, 227, 126, 191, 254, 31, 119, 191, 254, 31, 119, 191, 248, 139, 125, 191, 149, 213, 118, 191, 229, 250, 122, 191, 229, 250, 122, 191, 35, 252, 118, 191, 108, 227, 126, 191, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 191, 118, 101, 113, 191, 108, 227, 126, 191, 35, 252, 118, 191, 254, 31, 119, 191, 248, 139, 125, 191, 254, 31, 119, 191, 229, 250, 122, 191, 229, 250, 122, 191, 149, 213, 118, 191, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 63, 108, 227, 126, 191, 35, 252, 118, 63, 118, 101, 113, 63, 35, 252, 118, 191, 108, 227, 126, 63, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 191, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 191, 118, 101, 113, 63, 35, 252, 118, 191, 108, 227, 126, 191, 118, 101, 113, 63, 108, 227, 126, 191, 35, 252, 118, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 63)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_3v6u2"]
resource_name = "card-v2_Cube_001"
_surfaces = [{
"aabb": AABB(-1, -1, -1, 2, 2, 1.94295),
"attribute_data": PackedByteArray(174, 44, 30, 63, 82, 211, 1, 63, 82, 211, 33, 63, 94, 89, 252, 62, 175, 44, 30, 63, 94, 89, 252, 62, 132, 223, 30, 63, 81, 211, 1, 63, 0, 0, 32, 63, 81, 211, 1, 63, 81, 211, 33, 63, 0, 0, 0, 63, 175, 44, 30, 63, 124, 32, 1, 63, 255, 227, 30, 63, 0, 28, 1, 63, 0, 0, 32, 63, 78, 37, 1, 63, 77, 37, 33, 63, 0, 0, 0, 63, 42, 190, 31, 63, 0, 0, 0, 63, 42, 190, 31, 63, 0, 0, 0, 63, 124, 32, 33, 63, 94, 89, 252, 62, 0, 0, 32, 63, 94, 89, 252, 62, 81, 211, 33, 63, 8, 191, 253, 62, 0, 28, 33, 63, 0, 200, 253, 62, 255, 255, 31, 63, 102, 181, 253, 62, 0, 0, 32, 63, 100, 181, 253, 62, 175, 44, 30, 63, 8, 191, 253, 62, 175, 44, 30, 63, 0, 0, 0, 63, 132, 223, 30, 63, 94, 89, 252, 62, 0, 228, 30, 63, 0, 200, 253, 62, 178, 218, 30, 63, 0, 0, 0, 63, 94, 89, 188, 62, 94, 89, 252, 62, 163, 166, 195, 62, 82, 211, 1, 63, 163, 166, 195, 62, 94, 89, 252, 62, 94, 89, 188, 62, 8, 191, 253, 62, 93, 89, 188, 62, 0, 0, 0, 63, 0, 0, 192, 62, 81, 211, 1, 63, 10, 191, 189, 62, 94, 89, 252, 62, 255, 199, 189, 62, 0, 200, 253, 62, 102, 181, 189, 62, 0, 0, 0, 63, 0, 0, 192, 62, 78, 37, 1, 63, 0, 0, 192, 62, 82, 124, 255, 62, 163, 166, 195, 62, 124, 32, 1, 63, 162, 166, 195, 62, 0, 0, 0, 63, 248, 64, 194, 62, 81, 211, 1, 63, 0, 56, 194, 62, 0, 28, 1, 63, 156, 74, 194, 62, 0, 0, 0, 63, 248, 64, 194, 62, 94, 89, 252, 62, 0, 0, 192, 62, 94, 89, 252, 62, 163, 166, 195, 62, 8, 191, 253, 62, 0, 56, 194, 62, 0, 200, 253, 62, 0, 0, 192, 62, 100, 181, 253, 62, 174, 44, 30, 63, 162, 166, 131, 62, 81, 211, 33, 63, 162, 166, 131, 62, 132, 223, 30, 63, 162, 166, 131, 62, 0, 0, 32, 63, 164, 166, 131, 62, 124, 32, 33, 63, 162, 166, 131, 62, 163, 166, 195, 62, 162, 166, 131, 62, 94, 89, 188, 62, 162, 166, 131, 62, 248, 64, 194, 62, 162, 166, 131, 62, 10, 191, 189, 62, 162, 166, 131, 62, 0, 0, 192, 62, 164, 166, 131, 62, 175, 44, 30, 63, 175, 44, 62, 63, 174, 44, 30, 63, 81, 211, 65, 63, 176, 44, 94, 63, 94, 89, 252, 62, 175, 44, 30, 63, 132, 223, 62, 63, 175, 44, 30, 63, 0, 0, 64, 63, 132, 223, 30, 63, 175, 44, 62, 63, 0, 228, 30, 63, 0, 228, 62, 63, 178, 218, 30, 63, 0, 0, 64, 63, 124, 58, 31, 63, 0, 0, 64, 63, 0, 0, 32, 63, 131, 197, 64, 63, 126, 58, 95, 63, 0, 0, 0, 63, 132, 223, 30, 63, 81, 211, 65, 63, 0, 0, 32, 63, 81, 211, 65, 63, 0, 0, 96, 63, 94, 89, 252, 62, 175, 44, 30, 63, 124, 32, 65, 63, 255, 227, 30, 63, 0, 28, 65, 63, 0, 0, 32, 63, 78, 37, 65, 63, 0, 0, 96, 63, 100, 181, 253, 62, 175, 44, 94, 63, 8, 191, 253, 62, 0, 0, 32, 63, 175, 44, 62, 63, 175, 44, 94, 63, 0, 0, 0, 63, 132, 223, 94, 63, 94, 89, 252, 62, 0, 228, 94, 63, 0, 200, 253, 62, 255, 255, 31, 63, 179, 218, 62, 63, 179, 218, 94, 63, 0, 0, 0, 63, 163, 166, 195, 62, 81, 211, 65, 63, 163, 166, 195, 62, 175, 44, 62, 63, 68, 77, 7, 62, 94, 89, 252, 62, 163, 166, 195, 62, 124, 32, 65, 63, 162, 166, 195, 62, 0, 0, 64, 63, 248, 64, 194, 62, 81, 211, 65, 63, 0, 56, 194, 62, 0, 28, 65, 63, 156, 74, 194, 62, 0, 0, 64, 63, 0, 0, 0, 62, 250, 116, 254, 62, 0, 0, 192, 62, 125, 58, 63, 63, 6, 139, 193, 62, 0, 0, 64, 63, 248, 64, 194, 62, 175, 44, 62, 63, 69, 77, 7, 62, 0, 0, 0, 63, 0, 0, 192, 62, 175, 44, 62, 63, 163, 166, 195, 62, 132, 223, 62, 63, 0, 56, 194, 62, 0, 228, 62, 63, 54, 149, 4, 62, 0, 0, 0, 63, 0, 0, 192, 62, 178, 218, 62, 63, 238, 129, 4, 62, 94, 89, 252, 62, 0, 0, 0, 62, 94, 89, 252, 62, 0, 0, 192, 62, 81, 211, 65, 63, 69, 77, 7, 62, 8, 191, 253, 62, 1, 112, 4, 62, 0, 200, 253, 62, 0, 0, 0, 62, 100, 181, 253, 62, 0, 0, 192, 62, 78, 37, 65, 63, 175, 44, 30, 63, 175, 44, 126, 63, 175, 44, 94, 63, 162, 166, 131, 62, 132, 223, 30, 63, 175, 44, 126, 63, 132, 223, 94, 63, 162, 166, 131, 62, 0, 0, 32, 63, 175, 44, 126, 63, 0, 0, 96, 63, 164, 166, 131, 62, 69, 77, 7, 62, 162, 166, 131, 62, 163, 166, 195, 62, 175, 44, 126, 63, 239, 129, 4, 62, 162, 166, 131, 62, 248, 64, 194, 62, 175, 44, 126, 63, 0, 0, 0, 62, 164, 166, 131, 62, 0, 0, 192, 62, 175, 44, 126, 63, 0, 0, 32, 63, 81, 211, 1, 63, 81, 211, 33, 63, 0, 0, 0, 63, 42, 190, 31, 63, 0, 0, 0, 63, 42, 190, 31, 63, 0, 0, 0, 63, 42, 190, 31, 63, 0, 0, 0, 63, 93, 89, 188, 62, 0, 0, 0, 63, 0, 0, 192, 62, 81, 211, 1, 63, 0, 0, 192, 62, 82, 124, 255, 62, 0, 0, 192, 62, 82, 124, 255, 62, 0, 0, 192, 62, 82, 124, 255, 62, 178, 218, 30, 63, 0, 0, 64, 63, 124, 58, 31, 63, 0, 0, 64, 63, 0, 0, 32, 63, 131, 197, 64, 63, 126, 58, 95, 63, 0, 0, 0, 63, 0, 0, 96, 63, 94, 89, 252, 62, 156, 74, 194, 62, 0, 0, 64, 63, 156, 74, 194, 62, 0, 0, 64, 63, 0, 0, 0, 62, 250, 116, 254, 62, 0, 0, 192, 62, 125, 58, 63, 63, 0, 0, 0, 62, 94, 89, 252, 62, 175, 44, 30, 63, 94, 89, 252, 62, 42, 190, 31, 63, 0, 0, 0, 63, 163, 166, 195, 62, 82, 211, 1, 63, 163, 166, 195, 62, 94, 89, 252, 62, 0, 0, 192, 62, 82, 124, 255, 62, 175, 44, 30, 63, 175, 44, 62, 63, 163, 166, 195, 62, 81, 211, 65, 63),
"format": 34359742487,
"index_count": 510,
"index_data": PackedByteArray(81, 0, 50, 0, 23, 0, 81, 0, 110, 0, 50, 0, 111, 0, 55, 0, 104, 0, 111, 0, 79, 0, 55, 0, 1, 0, 105, 0, 56, 0, 1, 0, 45, 0, 105, 0, 25, 0, 44, 0, 2, 0, 25, 0, 49, 0, 44, 0, 0, 0, 7, 0, 3, 0, 0, 0, 6, 0, 7, 0, 3, 0, 8, 0, 4, 0, 3, 0, 7, 0, 8, 0, 6, 0, 22, 0, 7, 0, 6, 0, 19, 0, 22, 0, 7, 0, 10, 0, 8, 0, 7, 0, 22, 0, 10, 0, 1, 0, 15, 0, 12, 0, 1, 0, 14, 0, 15, 0, 12, 0, 17, 0, 13, 0, 12, 0, 15, 0, 17, 0, 14, 0, 9, 0, 15, 0, 14, 0, 5, 0, 9, 0, 15, 0, 11, 0, 17, 0, 15, 0, 9, 0, 11, 0, 2, 0, 21, 0, 18, 0, 2, 0, 20, 0, 21, 0, 18, 0, 22, 0, 19, 0, 18, 0, 21, 0, 22, 0, 20, 0, 16, 0, 21, 0, 20, 0, 13, 0, 16, 0, 21, 0, 10, 0, 22, 0, 21, 0, 16, 0, 10, 0, 23, 0, 30, 0, 26, 0, 23, 0, 29, 0, 30, 0, 26, 0, 31, 0, 27, 0, 26, 0, 30, 0, 31, 0, 29, 0, 43, 0, 30, 0, 29, 0, 40, 0, 43, 0, 30, 0, 33, 0, 31, 0, 30, 0, 43, 0, 33, 0, 24, 0, 37, 0, 34, 0, 24, 0, 36, 0, 37, 0, 34, 0, 38, 0, 35, 0, 34, 0, 37, 0, 38, 0, 36, 0, 32, 0, 37, 0, 36, 0, 28, 0, 32, 0, 37, 0, 33, 0, 38, 0, 37, 0, 32, 0, 33, 0, 25, 0, 42, 0, 39, 0, 25, 0, 41, 0, 42, 0, 39, 0, 43, 0, 40, 0, 39, 0, 42, 0, 43, 0, 41, 0, 38, 0, 42, 0, 41, 0, 35, 0, 38, 0, 42, 0, 33, 0, 43, 0, 42, 0, 38, 0, 33, 0, 54, 0, 60, 0, 57, 0, 54, 0, 59, 0, 60, 0, 57, 0, 61, 0, 58, 0, 57, 0, 60, 0, 61, 0, 59, 0, 77, 0, 60, 0, 59, 0, 73, 0, 77, 0, 60, 0, 62, 0, 61, 0, 60, 0, 77, 0, 62, 0, 55, 0, 69, 0, 65, 0, 55, 0, 68, 0, 69, 0, 65, 0, 70, 0, 66, 0, 65, 0, 69, 0, 70, 0, 68, 0, 61, 0, 69, 0, 68, 0, 58, 0, 61, 0, 69, 0, 63, 0, 70, 0, 69, 0, 61, 0, 63, 0, 56, 0, 76, 0, 72, 0, 56, 0, 75, 0, 76, 0, 72, 0, 78, 0, 74, 0, 72, 0, 76, 0, 78, 0, 75, 0, 71, 0, 76, 0, 75, 0, 67, 0, 71, 0, 76, 0, 64, 0, 78, 0, 76, 0, 71, 0, 64, 0, 79, 0, 85, 0, 82, 0, 79, 0, 84, 0, 85, 0, 82, 0, 86, 0, 83, 0, 82, 0, 85, 0, 86, 0, 84, 0, 103, 0, 85, 0, 84, 0, 99, 0, 103, 0, 85, 0, 89, 0, 86, 0, 85, 0, 103, 0, 89, 0, 80, 0, 94, 0, 90, 0, 80, 0, 93, 0, 94, 0, 90, 0, 96, 0, 92, 0, 90, 0, 94, 0, 96, 0, 93, 0, 86, 0, 94, 0, 93, 0, 83, 0, 86, 0, 94, 0, 88, 0, 96, 0, 94, 0, 86, 0, 88, 0, 81, 0, 101, 0, 97, 0, 81, 0, 100, 0, 101, 0, 97, 0, 102, 0, 98, 0, 97, 0, 101, 0, 102, 0, 100, 0, 95, 0, 101, 0, 100, 0, 91, 0, 95, 0, 101, 0, 87, 0, 102, 0, 101, 0, 95, 0, 87, 0, 81, 0, 112, 0, 110, 0, 81, 0, 97, 0, 112, 0, 97, 0, 114, 0, 112, 0, 97, 0, 98, 0, 114, 0, 99, 0, 113, 0, 115, 0, 99, 0, 84, 0, 113, 0, 84, 0, 111, 0, 113, 0, 84, 0, 79, 0, 111, 0, 23, 0, 100, 0, 81, 0, 23, 0, 26, 0, 100, 0, 26, 0, 91, 0, 100, 0, 26, 0, 27, 0, 91, 0, 28, 0, 90, 0, 92, 0, 28, 0, 36, 0, 90, 0, 36, 0, 80, 0, 90, 0, 36, 0, 24, 0, 80, 0, 2, 0, 41, 0, 25, 0, 2, 0, 18, 0, 41, 0, 18, 0, 35, 0, 41, 0, 18, 0, 19, 0, 35, 0, 19, 0, 34, 0, 35, 0, 19, 0, 6, 0, 34, 0, 6, 0, 24, 0, 34, 0, 6, 0, 0, 0, 24, 0, 54, 0, 93, 0, 80, 0, 54, 0, 57, 0, 93, 0, 57, 0, 83, 0, 93, 0, 57, 0, 58, 0, 83, 0, 58, 0, 82, 0, 83, 0, 58, 0, 68, 0, 82, 0, 68, 0, 79, 0, 82, 0, 68, 0, 55, 0, 79, 0, 1, 0, 48, 0, 45, 0, 1, 0, 12, 0, 48, 0, 12, 0, 47, 0, 48, 0, 12, 0, 13, 0, 47, 0, 13, 0, 46, 0, 47, 0, 13, 0, 20, 0, 46, 0, 20, 0, 44, 0, 46, 0, 20, 0, 2, 0, 44, 0, 105, 0, 75, 0, 56, 0, 105, 0, 107, 0, 75, 0, 107, 0, 67, 0, 75, 0, 107, 0, 109, 0, 67, 0, 108, 0, 65, 0, 66, 0, 108, 0, 106, 0, 65, 0, 106, 0, 55, 0, 65, 0, 106, 0, 104, 0, 55, 0, 56, 0, 14, 0, 1, 0, 56, 0, 72, 0, 14, 0, 72, 0, 5, 0, 14, 0, 72, 0, 74, 0, 5, 0, 73, 0, 3, 0, 4, 0, 73, 0, 59, 0, 3, 0, 59, 0, 0, 0, 3, 0, 59, 0, 54, 0, 0, 0, 50, 0, 29, 0, 23, 0, 50, 0, 52, 0, 29, 0, 52, 0, 40, 0, 29, 0, 52, 0, 53, 0, 40, 0, 53, 0, 39, 0, 40, 0, 53, 0, 51, 0, 39, 0, 51, 0, 25, 0, 39, 0, 51, 0, 49, 0, 25, 0, 80, 0, 0, 0, 54, 0, 80, 0, 24, 0, 0, 0),
"lods": [0.0150424, PackedByteArray(81, 0, 50, 0, 23, 0, 81, 0, 110, 0, 50, 0, 81, 0, 112, 0, 110, 0, 50, 0, 52, 0, 23, 0, 81, 0, 114, 0, 112, 0, 81, 0, 98, 0, 114, 0, 81, 0, 102, 0, 98, 0, 81, 0, 87, 0, 102, 0, 81, 0, 95, 0, 87, 0, 81, 0, 91, 0, 95, 0, 27, 0, 91, 0, 81, 0, 23, 0, 27, 0, 81, 0, 27, 0, 33, 0, 31, 0, 23, 0, 140, 0, 27, 0, 52, 0, 139, 0, 23, 0, 23, 0, 139, 0, 140, 0, 52, 0, 53, 0, 139, 0, 53, 0, 51, 0, 139, 0, 51, 0, 49, 0, 139, 0, 139, 0, 49, 0, 44, 0, 139, 0, 44, 0, 136, 0, 139, 0, 138, 0, 140, 0, 136, 0, 138, 0, 139, 0, 138, 0, 32, 0, 140, 0, 138, 0, 28, 0, 32, 0, 28, 0, 138, 0, 92, 0, 136, 0, 44, 0, 46, 0, 138, 0, 80, 0, 92, 0, 80, 0, 96, 0, 92, 0, 136, 0, 0, 0, 138, 0, 80, 0, 138, 0, 0, 0, 1, 0, 136, 0, 46, 0, 1, 0, 46, 0, 47, 0, 0, 0, 136, 0, 137, 0, 136, 0, 1, 0, 137, 0, 0, 0, 137, 0, 4, 0, 1, 0, 47, 0, 48, 0, 1, 0, 9, 0, 137, 0, 1, 0, 48, 0, 45, 0, 1, 0, 5, 0, 9, 0, 1, 0, 45, 0, 105, 0, 74, 0, 5, 0, 1, 0, 1, 0, 105, 0, 56, 0, 56, 0, 74, 0, 1, 0, 105, 0, 107, 0, 56, 0, 56, 0, 67, 0, 74, 0, 107, 0, 67, 0, 56, 0, 107, 0, 109, 0, 67, 0, 74, 0, 67, 0, 78, 0, 67, 0, 64, 0, 78, 0, 4, 0, 10, 0, 8, 0, 141, 0, 0, 0, 4, 0, 73, 0, 141, 0, 4, 0, 80, 0, 0, 0, 141, 0, 141, 0, 73, 0, 77, 0, 141, 0, 77, 0, 61, 0, 77, 0, 62, 0, 61, 0, 55, 0, 141, 0, 61, 0, 55, 0, 61, 0, 63, 0, 55, 0, 63, 0, 66, 0, 108, 0, 55, 0, 66, 0, 108, 0, 106, 0, 55, 0, 106, 0, 104, 0, 55, 0, 111, 0, 55, 0, 104, 0, 141, 0, 55, 0, 142, 0, 111, 0, 142, 0, 55, 0, 141, 0, 142, 0, 80, 0, 142, 0, 111, 0, 113, 0, 99, 0, 142, 0, 113, 0, 99, 0, 113, 0, 115, 0, 142, 0, 99, 0, 103, 0, 80, 0, 142, 0, 86, 0, 142, 0, 103, 0, 86, 0, 103, 0, 89, 0, 86, 0, 80, 0, 86, 0, 96, 0, 96, 0, 86, 0, 88, 0), 0.238994, PackedByteArray(135, 0, 50, 0, 121, 0, 135, 0, 121, 0, 133, 0, 50, 0, 52, 0, 121, 0, 52, 0, 125, 0, 121, 0, 52, 0, 53, 0, 125, 0, 135, 0, 110, 0, 50, 0, 135, 0, 112, 0, 110, 0, 135, 0, 114, 0, 112, 0, 53, 0, 51, 0, 123, 0, 51, 0, 49, 0, 123, 0, 123, 0, 49, 0, 44, 0, 123, 0, 44, 0, 118, 0, 118, 0, 44, 0, 46, 0, 117, 0, 120, 0, 46, 0, 117, 0, 46, 0, 47, 0, 117, 0, 47, 0, 48, 0, 117, 0, 48, 0, 45, 0, 117, 0, 45, 0, 105, 0, 117, 0, 105, 0, 130, 0, 130, 0, 129, 0, 117, 0, 105, 0, 107, 0, 130, 0, 107, 0, 109, 0, 130, 0, 111, 0, 128, 0, 104, 0, 106, 0, 104, 0, 128, 0, 108, 0, 106, 0, 128, 0, 111, 0, 131, 0, 128, 0, 108, 0, 63, 0, 66, 0, 61, 0, 63, 0, 86, 0, 131, 0, 111, 0, 113, 0, 99, 0, 131, 0, 113, 0, 99, 0, 89, 0, 131, 0, 99, 0, 113, 0, 115, 0, 116, 0, 127, 0, 126, 0, 122, 0, 116, 0, 126, 0, 119, 0, 116, 0, 122, 0, 119, 0, 122, 0, 124, 0, 126, 0, 132, 0, 122, 0, 122, 0, 132, 0, 134, 0)],
"material": SubResource("StandardMaterial3D_d4sqk"),
"name": "Material.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 143,
"vertex_data": PackedByteArray(118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 63, 35, 252, 118, 63, 108, 227, 126, 191, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 191, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 191, 35, 252, 118, 63, 118, 101, 113, 63, 108, 227, 126, 191, 254, 31, 119, 63, 254, 31, 119, 63, 248, 139, 125, 191, 149, 213, 118, 63, 229, 250, 122, 63, 229, 250, 122, 191, 149, 213, 118, 63, 229, 250, 122, 63, 229, 250, 122, 191, 232, 211, 121, 63, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 63, 232, 211, 121, 191, 35, 252, 118, 63, 108, 227, 126, 63, 118, 101, 113, 191, 2, 185, 123, 63, 2, 185, 123, 63, 118, 101, 113, 191, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 191, 254, 31, 119, 63, 248, 139, 125, 63, 254, 31, 119, 191, 229, 250, 122, 63, 229, 250, 122, 63, 149, 213, 118, 191, 229, 250, 122, 63, 229, 250, 122, 63, 149, 213, 118, 191, 108, 227, 126, 63, 118, 101, 113, 63, 35, 252, 118, 191, 2, 185, 123, 63, 118, 101, 113, 63, 2, 185, 123, 191, 108, 227, 126, 63, 35, 252, 118, 63, 118, 101, 113, 191, 248, 139, 125, 63, 254, 31, 119, 63, 254, 31, 119, 191, 229, 250, 122, 63, 149, 213, 118, 63, 229, 250, 122, 191, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 191, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 191, 0, 0, 128, 63, 118, 101, 113, 191, 118, 101, 113, 191, 118, 101, 113, 63, 108, 227, 126, 191, 35, 252, 118, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 35, 252, 118, 63, 108, 227, 126, 191, 118, 101, 113, 191, 254, 31, 119, 63, 248, 139, 125, 191, 254, 31, 119, 191, 149, 213, 118, 63, 229, 250, 122, 191, 229, 250, 122, 191, 149, 213, 118, 63, 229, 250, 122, 191, 229, 250, 122, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 35, 252, 118, 63, 118, 101, 113, 191, 108, 227, 126, 191, 2, 185, 123, 63, 118, 101, 113, 191, 2, 185, 123, 191, 118, 101, 113, 63, 35, 252, 118, 191, 108, 227, 126, 191, 254, 31, 119, 63, 254, 31, 119, 191, 248, 139, 125, 191, 229, 250, 122, 63, 149, 213, 118, 191, 229, 250, 122, 191, 108, 227, 126, 63, 35, 252, 118, 191, 118, 101, 113, 191, 2, 185, 123, 63, 2, 185, 123, 191, 118, 101, 113, 191, 108, 227, 126, 63, 118, 101, 113, 191, 35, 252, 118, 191, 248, 139, 125, 63, 254, 31, 119, 191, 254, 31, 119, 191, 229, 250, 122, 63, 229, 250, 122, 191, 149, 213, 118, 191, 0, 0, 128, 63, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 63, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 63, 118, 101, 113, 63, 35, 252, 118, 63, 108, 227, 126, 63, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 191, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 191, 118, 101, 113, 63, 35, 252, 118, 63, 108, 227, 126, 191, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 191, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 63, 0, 0, 128, 191, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 191, 35, 252, 118, 191, 118, 101, 113, 63, 108, 227, 126, 191, 2, 185, 123, 191, 118, 101, 113, 63, 2, 185, 123, 191, 118, 101, 113, 191, 35, 252, 118, 63, 108, 227, 126, 191, 254, 31, 119, 191, 254, 31, 119, 63, 248, 139, 125, 191, 229, 250, 122, 191, 149, 213, 118, 63, 229, 250, 122, 191, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 191, 108, 227, 126, 191, 35, 252, 118, 63, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 191, 108, 227, 126, 191, 118, 101, 113, 63, 35, 252, 118, 191, 248, 139, 125, 191, 254, 31, 119, 63, 254, 31, 119, 191, 229, 250, 122, 191, 229, 250, 122, 63, 149, 213, 118, 191, 229, 250, 122, 191, 229, 250, 122, 63, 149, 213, 118, 191, 118, 101, 113, 191, 108, 227, 126, 63, 35, 252, 118, 191, 118, 101, 113, 191, 2, 185, 123, 63, 2, 185, 123, 191, 118, 101, 113, 191, 2, 185, 123, 63, 2, 185, 123, 191, 35, 252, 118, 191, 108, 227, 126, 63, 118, 101, 113, 191, 254, 31, 119, 191, 248, 139, 125, 63, 254, 31, 119, 191, 149, 213, 118, 191, 229, 250, 122, 63, 229, 250, 122, 191, 149, 213, 118, 191, 229, 250, 122, 63, 229, 250, 122, 191, 0, 0, 128, 191, 118, 101, 113, 191, 118, 101, 113, 191, 118, 101, 113, 191, 118, 101, 113, 191, 0, 0, 128, 191, 118, 101, 113, 191, 0, 0, 128, 191, 118, 101, 113, 191, 108, 227, 126, 191, 118, 101, 113, 191, 35, 252, 118, 191, 2, 185, 123, 191, 118, 101, 113, 191, 2, 185, 123, 191, 108, 227, 126, 191, 35, 252, 118, 191, 118, 101, 113, 191, 248, 139, 125, 191, 254, 31, 119, 191, 254, 31, 119, 191, 229, 250, 122, 191, 149, 213, 118, 191, 229, 250, 122, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 118, 101, 113, 191, 35, 252, 118, 191, 108, 227, 126, 191, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 191, 35, 252, 118, 191, 118, 101, 113, 191, 108, 227, 126, 191, 254, 31, 119, 191, 254, 31, 119, 191, 248, 139, 125, 191, 149, 213, 118, 191, 229, 250, 122, 191, 229, 250, 122, 191, 149, 213, 118, 191, 229, 250, 122, 191, 229, 250, 122, 191, 35, 252, 118, 191, 108, 227, 126, 191, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 191, 118, 101, 113, 191, 108, 227, 126, 191, 35, 252, 118, 191, 254, 31, 119, 191, 248, 139, 125, 191, 254, 31, 119, 191, 229, 250, 122, 191, 229, 250, 122, 191, 149, 213, 118, 191, 229, 250, 122, 191, 229, 250, 122, 191, 149, 213, 118, 191, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 63, 108, 227, 126, 191, 35, 252, 118, 63, 118, 101, 113, 63, 35, 252, 118, 191, 108, 227, 126, 63, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 191, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 191, 118, 101, 113, 63, 35, 252, 118, 191, 108, 227, 126, 191, 118, 101, 113, 63, 108, 227, 126, 191, 35, 252, 118, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 63, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 191, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 191, 232, 211, 121, 63, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 63, 232, 211, 121, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 229, 250, 122, 191, 149, 213, 118, 63, 229, 250, 122, 191, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 191, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 191, 229, 250, 122, 191, 149, 213, 118, 191, 229, 250, 122, 191, 229, 250, 122, 191, 149, 213, 118, 191, 229, 250, 122, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 191, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 63, 118, 101, 113, 191, 232, 211, 121, 63, 232, 211, 121, 63, 232, 211, 121, 191, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 191, 0, 0, 128, 63, 118, 101, 113, 191, 118, 101, 113, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 118, 101, 113, 191, 118, 101, 113, 63, 0, 0, 128, 191, 0, 0, 128, 191, 118, 101, 113, 191, 118, 101, 113, 191, 52, 245, 52, 245, 255, 127, 26, 250, 149, 149, 52, 245, 201, 11, 227, 197, 52, 245, 149, 149, 54, 116, 26, 250, 67, 221, 2, 247, 245, 127, 77, 237, 235, 195, 39, 248, 94, 126, 164, 223, 235, 195, 39, 248, 99, 7, 99, 199, 2, 247, 67, 221, 40, 126, 86, 249, 106, 226, 106, 226, 232, 123, 241, 235, 216, 204, 76, 230, 78, 121, 42, 223, 216, 204, 76, 230, 100, 24, 101, 213, 84, 213, 84, 213, 255, 95, 255, 223, 84, 213, 84, 213, 169, 42, 255, 223, 184, 171, 67, 221, 87, 37, 176, 210, 235, 195, 235, 195, 255, 63, 255, 223, 184, 171, 1, 247, 121, 11, 168, 198, 45, 187, 104, 226, 5, 36, 14, 212, 216, 204, 216, 204, 7, 69, 42, 223, 216, 204, 216, 204, 248, 58, 212, 224, 2, 247, 184, 171, 134, 116, 86, 249, 38, 248, 235, 195, 154, 120, 154, 248, 67, 221, 184, 171, 168, 90, 78, 237, 99, 226, 54, 187, 244, 91, 235, 235, 76, 230, 216, 204, 152, 106, 152, 234, 149, 149, 202, 10, 53, 244, 227, 197, 52, 245, 202, 10, 200, 139, 254, 255, 52, 245, 105, 106, 200, 139, 26, 250, 184, 171, 252, 8, 174, 242, 187, 197, 235, 195, 215, 7, 55, 241, 177, 195, 234, 195, 215, 7, 180, 192, 46, 255, 184, 171, 187, 34, 157, 218, 171, 210, 41, 187, 148, 29, 32, 217, 239, 210, 216, 204, 178, 25, 50, 213, 231, 208, 216, 204, 178, 25, 179, 193, 147, 252, 84, 213, 170, 42, 255, 191, 35, 230, 2, 247, 187, 34, 79, 141, 19, 255, 38, 248, 19, 60, 200, 142, 76, 252, 67, 221, 252, 8, 97, 165, 249, 255, 106, 226, 148, 29, 27, 169, 176, 252, 76, 230, 38, 51, 204, 170, 202, 240, 67, 221, 70, 84, 97, 165, 83, 237, 235, 195, 19, 60, 255, 191, 255, 223, 2, 247, 70, 84, 79, 141, 66, 250, 106, 226, 213, 68, 222, 166, 15, 237, 216, 204, 38, 51, 255, 191, 255, 223, 133, 244, 121, 139, 133, 116, 66, 250, 121, 139, 133, 244, 121, 11, 188, 197, 123, 218, 252, 165, 88, 90, 43, 237, 24, 192, 23, 192, 255, 63, 255, 223, 170, 165, 206, 218, 84, 37, 169, 210, 133, 244, 133, 116, 121, 139, 66, 250, 121, 139, 121, 11, 133, 244, 188, 197, 205, 218, 84, 90, 84, 165, 84, 237, 252, 165, 131, 37, 87, 218, 210, 210, 41, 192, 6, 64, 230, 191, 11, 224, 202, 10, 52, 245, 255, 127, 26, 250, 202, 10, 149, 149, 200, 139, 26, 250, 105, 106, 52, 245, 201, 11, 26, 186, 252, 8, 67, 221, 214, 129, 86, 249, 216, 7, 235, 195, 100, 135, 154, 248, 187, 34, 2, 247, 9, 128, 78, 237, 148, 29, 106, 226, 23, 132, 241, 235, 178, 25, 216, 204, 255, 160, 152, 234, 170, 42, 84, 213, 85, 149, 254, 223, 170, 42, 84, 213, 145, 199, 254, 223, 170, 42, 84, 213, 0, 64, 170, 170, 187, 34, 184, 171, 87, 165, 78, 237, 20, 60, 235, 195, 20, 191, 164, 223, 19, 60, 234, 195, 181, 64, 116, 160, 252, 8, 184, 171, 120, 139, 86, 249, 148, 29, 41, 187, 201, 170, 194, 236, 38, 51, 216, 204, 68, 188, 97, 223, 38, 51, 216, 204, 169, 65, 130, 162, 70, 84, 2, 247, 80, 13, 66, 186, 20, 60, 39, 248, 160, 129, 164, 223, 19, 60, 39, 248, 199, 14, 77, 188, 70, 84, 67, 221, 97, 37, 83, 173, 213, 68, 106, 226, 26, 40, 253, 173, 38, 51, 76, 230, 176, 134, 42, 223, 38, 51, 76, 230, 204, 42, 204, 179, 202, 10, 105, 106, 54, 116, 26, 250, 202, 10, 202, 10, 200, 139, 254, 255, 105, 106, 202, 10, 53, 244, 26, 186, 252, 8, 70, 84, 175, 114, 66, 250, 216, 7, 19, 60, 54, 113, 76, 252, 187, 34, 70, 84, 158, 90, 83, 237, 155, 29, 200, 68, 216, 87, 249, 237, 178, 25, 38, 51, 50, 85, 24, 251, 170, 42, 170, 42, 109, 184, 255, 159, 170, 42, 170, 42, 145, 199, 54, 252, 170, 42, 170, 42, 255, 63, 170, 234, 187, 34, 252, 8, 157, 90, 250, 255, 19, 60, 215, 7, 155, 248, 155, 184, 19, 60, 215, 7, 74, 63, 46, 255, 252, 8, 188, 34, 175, 114, 19, 255, 148, 29, 148, 29, 201, 170, 214, 253, 38, 51, 178, 25, 125, 204, 131, 169, 38, 51, 178, 25, 195, 62, 131, 253, 70, 84, 187, 34, 167, 218, 78, 173, 19, 60, 19, 60, 234, 192, 164, 159, 20, 60, 19, 60, 74, 63, 116, 224, 70, 84, 253, 8, 133, 244, 86, 185, 209, 68, 150, 29, 51, 213, 193, 172, 38, 51, 38, 51, 185, 195, 97, 159, 38, 51, 38, 51, 85, 62, 130, 226, 121, 11, 121, 139, 121, 139, 66, 250, 133, 116, 133, 244, 121, 11, 66, 186, 49, 37, 170, 165, 84, 165, 84, 237, 2, 90, 123, 218, 167, 37, 43, 173, 214, 65, 2, 183, 199, 188, 99, 226, 248, 72, 25, 190, 63, 59, 149, 161, 133, 116, 121, 11, 133, 244, 66, 186, 121, 11, 133, 116, 133, 116, 66, 250, 84, 90, 48, 37, 170, 218, 85, 173, 131, 37, 2, 90, 88, 90, 43, 237, 252, 72, 214, 65, 55, 195, 99, 162, 229, 65, 248, 72, 192, 68, 149, 225, 35, 232, 88, 244, 94, 126, 164, 223, 2, 166, 155, 230, 99, 7, 99, 199, 70, 233, 29, 157, 255, 95, 255, 223, 78, 254, 1, 216, 255, 95, 255, 223, 62, 183, 196, 207, 255, 95, 255, 223, 64, 160, 41, 14, 55, 241, 177, 195, 187, 240, 226, 13, 180, 192, 46, 255, 227, 229, 241, 96, 255, 191, 35, 230, 189, 251, 247, 42, 255, 191, 35, 230, 171, 180, 8, 47, 255, 191, 35, 230, 35, 23, 176, 252, 255, 160, 152, 234, 17, 55, 83, 252, 85, 149, 254, 223, 230, 26, 126, 158, 145, 199, 254, 223, 242, 77, 195, 251, 0, 64, 170, 170, 11, 88, 81, 229, 181, 64, 116, 160, 82, 31, 146, 81, 50, 85, 24, 251, 213, 30, 143, 5, 50, 85, 24, 251, 176, 77, 76, 4, 109, 184, 255, 159, 107, 55, 30, 4, 145, 199, 54, 252, 15, 93, 220, 23, 234, 192, 164, 159, 152, 234, 172, 175, 54, 116, 26, 250, 242, 212, 13, 216, 255, 95, 255, 223, 205, 235, 62, 26, 200, 139, 254, 255, 195, 226, 187, 83, 200, 139, 26, 250, 146, 212, 14, 40, 255, 191, 35, 230, 34, 18, 249, 228, 255, 127, 26, 250, 255, 16, 40, 83, 54, 116, 26, 250)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_18hhm")

[sub_resource type="CompressedTexture2D" id="CompressedTexture2D_jdfgm"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_uu2bt"]
resource_local_to_scene = true
shading_mode = 0
vertex_color_use_as_albedo = true
albedo_texture = SubResource("CompressedTexture2D_jdfgm")
heightmap_enabled = true
heightmap_scale = -0.655

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_po1bx"]
resource_name = "Material.002"
cull_mode = 2
vertex_color_use_as_albedo = true
albedo_texture = ExtResource("1_fqpum")
roughness = 0.5

[sub_resource type="ArrayMesh" id="ArrayMesh_dnys1"]
_surfaces = [{
"aabb": AABB(-1, -1, 0.942954, 2.00001, 2, 0.0570456),
"format": 34359742465,
"index_count": 390,
"index_data": PackedByteArray(20, 0, 39, 0, 2, 0, 20, 0, 58, 0, 39, 0, 0, 0, 6, 0, 3, 0, 0, 0, 5, 0, 6, 0, 3, 0, 7, 0, 4, 0, 3, 0, 6, 0, 7, 0, 5, 0, 18, 0, 6, 0, 5, 0, 15, 0, 18, 0, 6, 0, 8, 0, 7, 0, 6, 0, 18, 0, 8, 0, 1, 0, 12, 0, 9, 0, 1, 0, 11, 0, 12, 0, 9, 0, 13, 0, 10, 0, 9, 0, 12, 0, 13, 0, 11, 0, 7, 0, 12, 0, 11, 0, 4, 0, 7, 0, 12, 0, 8, 0, 13, 0, 12, 0, 7, 0, 8, 0, 2, 0, 17, 0, 14, 0, 2, 0, 16, 0, 17, 0, 14, 0, 18, 0, 15, 0, 14, 0, 17, 0, 18, 0, 16, 0, 13, 0, 17, 0, 16, 0, 10, 0, 13, 0, 17, 0, 8, 0, 18, 0, 17, 0, 13, 0, 8, 0, 19, 0, 25, 0, 22, 0, 19, 0, 24, 0, 25, 0, 22, 0, 26, 0, 23, 0, 22, 0, 25, 0, 26, 0, 24, 0, 37, 0, 25, 0, 24, 0, 34, 0, 37, 0, 25, 0, 27, 0, 26, 0, 25, 0, 37, 0, 27, 0, 20, 0, 31, 0, 28, 0, 20, 0, 30, 0, 31, 0, 28, 0, 32, 0, 29, 0, 28, 0, 31, 0, 32, 0, 30, 0, 26, 0, 31, 0, 30, 0, 23, 0, 26, 0, 31, 0, 27, 0, 32, 0, 31, 0, 26, 0, 27, 0, 21, 0, 36, 0, 33, 0, 21, 0, 35, 0, 36, 0, 33, 0, 37, 0, 34, 0, 33, 0, 36, 0, 37, 0, 35, 0, 32, 0, 36, 0, 35, 0, 29, 0, 32, 0, 36, 0, 27, 0, 37, 0, 36, 0, 32, 0, 27, 0, 38, 0, 44, 0, 41, 0, 38, 0, 43, 0, 44, 0, 41, 0, 45, 0, 42, 0, 41, 0, 44, 0, 45, 0, 43, 0, 56, 0, 44, 0, 43, 0, 53, 0, 56, 0, 44, 0, 46, 0, 45, 0, 44, 0, 56, 0, 46, 0, 39, 0, 50, 0, 47, 0, 39, 0, 49, 0, 50, 0, 47, 0, 51, 0, 48, 0, 47, 0, 50, 0, 51, 0, 49, 0, 45, 0, 50, 0, 49, 0, 42, 0, 45, 0, 50, 0, 46, 0, 51, 0, 50, 0, 45, 0, 46, 0, 40, 0, 55, 0, 52, 0, 40, 0, 54, 0, 55, 0, 52, 0, 56, 0, 53, 0, 52, 0, 55, 0, 56, 0, 54, 0, 51, 0, 55, 0, 54, 0, 48, 0, 51, 0, 55, 0, 46, 0, 56, 0, 55, 0, 51, 0, 46, 0, 57, 0, 63, 0, 60, 0, 57, 0, 62, 0, 63, 0, 60, 0, 64, 0, 61, 0, 60, 0, 63, 0, 64, 0, 62, 0, 75, 0, 63, 0, 62, 0, 72, 0, 75, 0, 63, 0, 65, 0, 64, 0, 63, 0, 75, 0, 65, 0, 58, 0, 69, 0, 66, 0, 58, 0, 68, 0, 69, 0, 66, 0, 70, 0, 67, 0, 66, 0, 69, 0, 70, 0, 68, 0, 64, 0, 69, 0, 68, 0, 61, 0, 64, 0, 69, 0, 65, 0, 70, 0, 69, 0, 64, 0, 65, 0, 59, 0, 74, 0, 71, 0, 59, 0, 73, 0, 74, 0, 71, 0, 75, 0, 72, 0, 71, 0, 74, 0, 75, 0, 73, 0, 70, 0, 74, 0, 73, 0, 67, 0, 70, 0, 74, 0, 65, 0, 75, 0, 74, 0, 70, 0, 65, 0, 58, 0, 49, 0, 39, 0, 58, 0, 66, 0, 49, 0, 66, 0, 42, 0, 49, 0, 66, 0, 67, 0, 42, 0, 67, 0, 41, 0, 42, 0, 67, 0, 73, 0, 41, 0, 73, 0, 38, 0, 41, 0, 73, 0, 59, 0, 38, 0, 2, 0, 30, 0, 20, 0, 2, 0, 14, 0, 30, 0, 14, 0, 23, 0, 30, 0, 14, 0, 15, 0, 23, 0, 15, 0, 22, 0, 23, 0, 15, 0, 5, 0, 22, 0, 5, 0, 19, 0, 22, 0, 5, 0, 0, 0, 19, 0, 1, 0, 54, 0, 40, 0, 1, 0, 9, 0, 54, 0, 9, 0, 48, 0, 54, 0, 9, 0, 10, 0, 48, 0, 10, 0, 47, 0, 48, 0, 10, 0, 16, 0, 47, 0, 16, 0, 39, 0, 47, 0, 16, 0, 2, 0, 39, 0, 57, 0, 35, 0, 21, 0, 57, 0, 60, 0, 35, 0, 60, 0, 29, 0, 35, 0, 60, 0, 61, 0, 29, 0, 61, 0, 28, 0, 29, 0, 61, 0, 68, 0, 28, 0, 68, 0, 20, 0, 28, 0, 68, 0, 58, 0, 20, 0),
"lods": [0.00694598, PackedByteArray(20, 0, 39, 0, 2, 0, 20, 0, 58, 0, 39, 0, 9, 0, 2, 0, 39, 0, 2, 0, 9, 0, 12, 0, 1, 0, 12, 0, 9, 0, 1, 0, 11, 0, 12, 0, 1, 0, 9, 0, 54, 0, 9, 0, 39, 0, 54, 0, 1, 0, 54, 0, 40, 0, 39, 0, 50, 0, 54, 0, 40, 0, 54, 0, 55, 0, 54, 0, 50, 0, 55, 0, 40, 0, 55, 0, 52, 0, 11, 0, 6, 0, 12, 0, 11, 0, 4, 0, 6, 0, 3, 0, 6, 0, 4, 0, 0, 0, 6, 0, 3, 0, 17, 0, 12, 0, 6, 0, 2, 0, 12, 0, 17, 0, 2, 0, 17, 0, 6, 0, 0, 0, 5, 0, 6, 0, 2, 0, 6, 0, 5, 0, 5, 0, 0, 0, 19, 0, 2, 0, 5, 0, 22, 0, 5, 0, 19, 0, 22, 0, 2, 0, 22, 0, 20, 0, 19, 0, 25, 0, 22, 0, 20, 0, 22, 0, 25, 0, 19, 0, 24, 0, 25, 0, 24, 0, 34, 0, 25, 0, 33, 0, 25, 0, 34, 0, 20, 0, 25, 0, 31, 0, 33, 0, 35, 0, 25, 0, 35, 0, 31, 0, 25, 0, 20, 0, 31, 0, 35, 0, 21, 0, 35, 0, 33, 0, 57, 0, 35, 0, 21, 0, 61, 0, 20, 0, 35, 0, 57, 0, 60, 0, 35, 0, 60, 0, 61, 0, 35, 0, 57, 0, 63, 0, 60, 0, 60, 0, 63, 0, 61, 0, 61, 0, 58, 0, 20, 0, 58, 0, 61, 0, 63, 0, 57, 0, 62, 0, 63, 0, 58, 0, 63, 0, 69, 0, 62, 0, 75, 0, 63, 0, 62, 0, 72, 0, 75, 0, 71, 0, 75, 0, 72, 0, 74, 0, 63, 0, 75, 0, 71, 0, 74, 0, 75, 0, 74, 0, 69, 0, 63, 0, 59, 0, 74, 0, 71, 0, 73, 0, 69, 0, 74, 0, 59, 0, 73, 0, 74, 0, 58, 0, 69, 0, 73, 0, 73, 0, 59, 0, 38, 0, 58, 0, 73, 0, 42, 0, 58, 0, 42, 0, 39, 0, 73, 0, 38, 0, 41, 0, 73, 0, 41, 0, 42, 0, 38, 0, 44, 0, 41, 0, 41, 0, 44, 0, 42, 0, 39, 0, 42, 0, 44, 0, 39, 0, 44, 0, 50, 0, 38, 0, 43, 0, 44, 0, 55, 0, 50, 0, 44, 0, 43, 0, 56, 0, 44, 0, 55, 0, 44, 0, 56, 0, 43, 0, 53, 0, 56, 0, 52, 0, 55, 0, 56, 0, 52, 0, 56, 0, 53, 0), 0.0150099, PackedByteArray(20, 0, 39, 0, 2, 0, 1, 0, 2, 0, 39, 0, 1, 0, 39, 0, 40, 0, 39, 0, 50, 0, 40, 0, 40, 0, 50, 0, 53, 0, 52, 0, 40, 0, 53, 0, 38, 0, 53, 0, 50, 0, 39, 0, 38, 0, 50, 0, 43, 0, 53, 0, 38, 0, 58, 0, 38, 0, 39, 0, 20, 0, 58, 0, 39, 0, 58, 0, 59, 0, 38, 0, 58, 0, 69, 0, 59, 0, 59, 0, 69, 0, 72, 0, 71, 0, 59, 0, 72, 0, 58, 0, 57, 0, 69, 0, 57, 0, 72, 0, 69, 0, 57, 0, 58, 0, 20, 0, 62, 0, 72, 0, 57, 0, 57, 0, 20, 0, 21, 0, 20, 0, 31, 0, 21, 0, 21, 0, 31, 0, 22, 0, 20, 0, 22, 0, 31, 0, 33, 0, 21, 0, 22, 0, 33, 0, 22, 0, 34, 0, 24, 0, 34, 0, 22, 0, 19, 0, 24, 0, 22, 0, 2, 0, 22, 0, 20, 0, 0, 0, 19, 0, 22, 0, 2, 0, 0, 0, 22, 0, 2, 0, 6, 0, 0, 0, 0, 0, 6, 0, 3, 0, 2, 0, 1, 0, 6, 0, 3, 0, 6, 0, 4, 0, 11, 0, 6, 0, 1, 0, 11, 0, 4, 0, 6, 0), 0.0530864, PackedByteArray(24, 0, 38, 0, 3, 0, 1, 0, 3, 0, 38, 0, 24, 0, 57, 0, 38, 0, 57, 0, 59, 0, 38, 0, 1, 0, 38, 0, 40, 0, 71, 0, 59, 0, 72, 0, 59, 0, 57, 0, 72, 0, 62, 0, 72, 0, 57, 0, 57, 0, 24, 0, 21, 0, 11, 0, 3, 0, 1, 0, 11, 0, 4, 0, 3, 0, 3, 0, 0, 0, 19, 0, 3, 0, 19, 0, 24, 0, 33, 0, 21, 0, 24, 0, 33, 0, 24, 0, 34, 0, 43, 0, 53, 0, 38, 0, 40, 0, 38, 0, 53, 0, 52, 0, 40, 0, 53, 0)],
"name": "Material.002",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 76,
"vertex_data": PackedByteArray(0, 0, 128, 63, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 63, 108, 227, 126, 63, 35, 252, 118, 63, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 63, 118, 101, 113, 63, 108, 227, 126, 63, 118, 101, 113, 63, 35, 252, 118, 63, 248, 139, 125, 63, 254, 31, 119, 63, 254, 31, 119, 63, 229, 250, 122, 63, 229, 250, 122, 63, 149, 213, 118, 63, 232, 211, 121, 63, 232, 211, 121, 63, 232, 211, 121, 63, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 63, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 63, 35, 252, 118, 63, 108, 227, 126, 63, 118, 101, 113, 63, 254, 31, 119, 63, 248, 139, 125, 63, 254, 31, 119, 63, 149, 213, 118, 63, 229, 250, 122, 63, 229, 250, 122, 63, 35, 252, 118, 63, 118, 101, 113, 63, 108, 227, 126, 63, 2, 185, 123, 63, 118, 101, 113, 63, 2, 185, 123, 63, 118, 101, 113, 63, 35, 252, 118, 63, 108, 227, 126, 63, 254, 31, 119, 63, 254, 31, 119, 63, 248, 139, 125, 63, 229, 250, 122, 63, 149, 213, 118, 63, 229, 250, 122, 63, 0, 0, 128, 63, 118, 101, 113, 191, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 63, 108, 227, 126, 63, 118, 101, 113, 191, 35, 252, 118, 63, 2, 185, 123, 63, 118, 101, 113, 191, 2, 185, 123, 63, 108, 227, 126, 63, 35, 252, 118, 191, 118, 101, 113, 63, 248, 139, 125, 63, 254, 31, 119, 191, 254, 31, 119, 63, 229, 250, 122, 63, 149, 213, 118, 191, 229, 250, 122, 63, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 63, 118, 101, 113, 63, 35, 252, 118, 191, 108, 227, 126, 63, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 63, 35, 252, 118, 63, 118, 101, 113, 191, 108, 227, 126, 63, 254, 31, 119, 63, 254, 31, 119, 191, 248, 139, 125, 63, 149, 213, 118, 63, 229, 250, 122, 191, 229, 250, 122, 63, 35, 252, 118, 63, 108, 227, 126, 191, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 191, 118, 101, 113, 63, 118, 101, 113, 63, 108, 227, 126, 191, 35, 252, 118, 63, 254, 31, 119, 63, 248, 139, 125, 191, 254, 31, 119, 63, 229, 250, 122, 63, 229, 250, 122, 191, 149, 213, 118, 63, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 63, 108, 227, 126, 191, 118, 101, 113, 63, 35, 252, 118, 63, 2, 185, 123, 191, 118, 101, 113, 63, 2, 185, 123, 63, 108, 227, 126, 191, 35, 252, 118, 63, 118, 101, 113, 63, 248, 139, 125, 191, 254, 31, 119, 63, 254, 31, 119, 63, 229, 250, 122, 191, 149, 213, 118, 63, 229, 250, 122, 63, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 63, 118, 101, 113, 191, 35, 252, 118, 63, 108, 227, 126, 63, 118, 101, 113, 191, 2, 185, 123, 63, 2, 185, 123, 63, 35, 252, 118, 191, 118, 101, 113, 63, 108, 227, 126, 63, 254, 31, 119, 191, 254, 31, 119, 63, 248, 139, 125, 63, 149, 213, 118, 191, 229, 250, 122, 63, 229, 250, 122, 63, 35, 252, 118, 191, 108, 227, 126, 63, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 63, 118, 101, 113, 191, 108, 227, 126, 63, 35, 252, 118, 63, 254, 31, 119, 191, 248, 139, 125, 63, 254, 31, 119, 63, 229, 250, 122, 191, 229, 250, 122, 63, 149, 213, 118, 63, 118, 101, 113, 191, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 191, 0, 0, 128, 63, 0, 0, 128, 191, 118, 101, 113, 191, 118, 101, 113, 63, 118, 101, 113, 191, 108, 227, 126, 191, 35, 252, 118, 63, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 63, 35, 252, 118, 191, 108, 227, 126, 191, 118, 101, 113, 63, 254, 31, 119, 191, 248, 139, 125, 191, 254, 31, 119, 63, 149, 213, 118, 191, 229, 250, 122, 191, 229, 250, 122, 63, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 63, 35, 252, 118, 191, 118, 101, 113, 191, 108, 227, 126, 63, 2, 185, 123, 191, 118, 101, 113, 191, 2, 185, 123, 63, 118, 101, 113, 191, 35, 252, 118, 191, 108, 227, 126, 63, 254, 31, 119, 191, 254, 31, 119, 191, 248, 139, 125, 63, 229, 250, 122, 191, 149, 213, 118, 191, 229, 250, 122, 63, 108, 227, 126, 191, 35, 252, 118, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 63, 108, 227, 126, 191, 118, 101, 113, 191, 35, 252, 118, 63, 248, 139, 125, 191, 254, 31, 119, 191, 254, 31, 119, 63, 229, 250, 122, 191, 229, 250, 122, 191, 149, 213, 118, 63)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_tgt8w"]
resource_name = "card-v2_Cube_002"
_surfaces = [{
"aabb": AABB(-1, -1, 0.942954, 2.00001, 2, 0.0570456),
"attribute_data": PackedByteArray(0, 0, 128, 63, 0, 169, 233, 60, 178, 178, 120, 63, 0, 0, 128, 51, 178, 178, 120, 63, 0, 169, 233, 60, 175, 113, 127, 63, 192, 62, 144, 60, 113, 220, 125, 63, 64, 225, 8, 60, 175, 113, 127, 63, 0, 169, 233, 60, 244, 197, 126, 63, 192, 0, 142, 60, 102, 125, 125, 63, 128, 164, 32, 60, 231, 233, 124, 63, 128, 132, 69, 60, 178, 178, 120, 63, 0, 80, 14, 59, 178, 178, 120, 63, 64, 225, 8, 60, 178, 178, 120, 63, 64, 225, 8, 60, 13, 126, 123, 63, 0, 80, 14, 59, 243, 143, 123, 63, 128, 2, 157, 59, 201, 106, 123, 63, 128, 164, 32, 60, 201, 106, 123, 63, 128, 164, 32, 60, 13, 126, 123, 63, 0, 169, 233, 60, 113, 220, 125, 63, 0, 169, 233, 60, 178, 178, 120, 63, 192, 62, 144, 60, 243, 143, 123, 63, 192, 0, 142, 60, 102, 125, 125, 63, 96, 167, 146, 60, 0, 0, 128, 63, 190, 178, 120, 63, 178, 178, 120, 63, 190, 178, 120, 63, 178, 178, 120, 63, 0, 0, 128, 63, 175, 113, 127, 63, 190, 178, 120, 63, 113, 220, 125, 63, 190, 178, 120, 63, 175, 113, 127, 63, 21, 126, 123, 63, 244, 197, 126, 63, 0, 144, 123, 63, 102, 125, 125, 63, 203, 106, 123, 63, 231, 233, 124, 63, 249, 233, 124, 63, 178, 178, 120, 63, 21, 126, 123, 63, 178, 178, 120, 63, 129, 220, 125, 63, 178, 178, 120, 63, 129, 220, 125, 63, 13, 126, 123, 63, 190, 178, 120, 63, 243, 143, 123, 63, 0, 144, 123, 63, 201, 106, 123, 63, 116, 125, 125, 63, 201, 106, 123, 63, 116, 125, 125, 63, 13, 126, 123, 63, 182, 113, 127, 63, 113, 220, 125, 63, 129, 220, 125, 63, 178, 178, 120, 63, 182, 113, 127, 63, 243, 143, 123, 63, 252, 197, 126, 63, 102, 125, 125, 63, 116, 125, 125, 63, 0, 0, 0, 0, 0, 169, 233, 60, 144, 168, 233, 60, 0, 169, 233, 60, 144, 168, 233, 60, 0, 0, 128, 51, 243, 75, 14, 59, 0, 169, 233, 60, 212, 223, 8, 60, 0, 169, 233, 60, 212, 223, 8, 60, 0, 169, 233, 60, 243, 75, 14, 59, 192, 62, 144, 60, 64, 1, 157, 59, 192, 0, 142, 60, 165, 162, 32, 60, 96, 167, 146, 60, 165, 162, 32, 60, 96, 167, 146, 60, 188, 131, 69, 60, 128, 132, 69, 60, 188, 131, 69, 60, 128, 132, 69, 60, 188, 131, 69, 60, 128, 132, 69, 60, 144, 168, 233, 60, 192, 62, 144, 60, 144, 168, 233, 60, 64, 225, 8, 60, 144, 168, 233, 60, 64, 225, 8, 60, 203, 61, 144, 60, 0, 169, 233, 60, 97, 0, 142, 60, 192, 0, 142, 60, 75, 166, 146, 60, 128, 164, 32, 60, 75, 166, 146, 60, 128, 164, 32, 60, 203, 61, 144, 60, 0, 80, 14, 59, 212, 223, 8, 60, 64, 225, 8, 60, 212, 223, 8, 60, 64, 225, 8, 60, 144, 168, 233, 60, 0, 80, 14, 59, 97, 0, 142, 60, 128, 2, 157, 59, 165, 162, 32, 60, 128, 164, 32, 60, 165, 162, 32, 60, 128, 164, 32, 60, 144, 168, 233, 60, 0, 0, 128, 63, 144, 168, 233, 60, 190, 178, 120, 63, 0, 0, 0, 0, 190, 178, 120, 63, 144, 168, 233, 60, 182, 113, 127, 63, 144, 168, 233, 60, 129, 220, 125, 63, 144, 168, 233, 60, 129, 220, 125, 63, 203, 61, 144, 60, 182, 113, 127, 63, 97, 0, 142, 60, 252, 197, 126, 63, 75, 166, 146, 60, 116, 125, 125, 63, 75, 166, 146, 60, 116, 125, 125, 63, 188, 131, 69, 60, 249, 233, 124, 63, 188, 131, 69, 60, 249, 233, 124, 63, 188, 131, 69, 60, 249, 233, 124, 63, 203, 61, 144, 60, 190, 178, 120, 63, 212, 223, 8, 60, 190, 178, 120, 63, 212, 223, 8, 60, 190, 178, 120, 63, 144, 168, 233, 60, 21, 126, 123, 63, 97, 0, 142, 60, 0, 144, 123, 63, 165, 162, 32, 60, 203, 106, 123, 63, 165, 162, 32, 60, 203, 106, 123, 63, 243, 75, 14, 59, 21, 126, 123, 63, 212, 223, 8, 60, 129, 220, 125, 63, 212, 223, 8, 60, 129, 220, 125, 63, 243, 75, 14, 59, 190, 178, 120, 63, 64, 1, 157, 59, 0, 144, 123, 63, 165, 162, 32, 60, 116, 125, 125, 63, 165, 162, 32, 60, 116, 125, 125, 63, 178, 178, 120, 63, 0, 0, 128, 51, 175, 113, 127, 63, 192, 62, 144, 60, 175, 113, 127, 63, 192, 62, 144, 60, 175, 113, 127, 63, 21, 126, 123, 63, 175, 113, 127, 63, 21, 126, 123, 63, 0, 0, 0, 0, 0, 169, 233, 60, 0, 0, 0, 0, 0, 169, 233, 60, 144, 168, 233, 60, 0, 0, 128, 51, 212, 223, 8, 60, 64, 225, 8, 60, 144, 168, 233, 60, 0, 0, 128, 63, 144, 168, 233, 60, 0, 0, 128, 63, 0, 0, 0, 0, 190, 178, 120, 63, 212, 223, 8, 60, 129, 220, 125, 63, 178, 178, 120, 63, 0, 0, 128, 51, 178, 178, 120, 63, 0, 169, 233, 60, 244, 197, 126, 63, 192, 0, 142, 60, 178, 178, 120, 63, 190, 178, 120, 63, 178, 178, 120, 63, 0, 0, 128, 63, 175, 113, 127, 63, 190, 178, 120, 63, 243, 143, 123, 63, 0, 144, 123, 63, 0, 0, 0, 0, 0, 169, 233, 60, 144, 168, 233, 60, 0, 169, 233, 60, 97, 0, 142, 60, 192, 0, 142, 60, 212, 223, 8, 60, 64, 225, 8, 60, 144, 168, 233, 60, 0, 0, 128, 63, 144, 168, 233, 60, 190, 178, 120, 63, 0, 0, 0, 0, 190, 178, 120, 63, 97, 0, 142, 60, 0, 144, 123, 63, 212, 223, 8, 60, 129, 220, 125, 63, 178, 178, 120, 63, 0, 169, 233, 60, 244, 197, 126, 63, 0, 144, 123, 63),
"format": 34359742487,
"index_count": 390,
"index_data": PackedByteArray(22, 0, 43, 0, 2, 0, 22, 0, 70, 0, 43, 0, 0, 0, 6, 0, 3, 0, 0, 0, 5, 0, 6, 0, 3, 0, 7, 0, 4, 0, 3, 0, 6, 0, 7, 0, 5, 0, 20, 0, 6, 0, 5, 0, 17, 0, 20, 0, 6, 0, 8, 0, 7, 0, 6, 0, 20, 0, 8, 0, 1, 0, 13, 0, 9, 0, 1, 0, 12, 0, 13, 0, 9, 0, 15, 0, 11, 0, 9, 0, 13, 0, 15, 0, 12, 0, 7, 0, 13, 0, 12, 0, 4, 0, 7, 0, 13, 0, 8, 0, 15, 0, 13, 0, 7, 0, 8, 0, 2, 0, 19, 0, 16, 0, 2, 0, 18, 0, 19, 0, 16, 0, 20, 0, 17, 0, 16, 0, 19, 0, 20, 0, 18, 0, 14, 0, 19, 0, 18, 0, 10, 0, 14, 0, 19, 0, 8, 0, 20, 0, 19, 0, 14, 0, 8, 0, 21, 0, 27, 0, 24, 0, 21, 0, 26, 0, 27, 0, 24, 0, 28, 0, 25, 0, 24, 0, 27, 0, 28, 0, 26, 0, 41, 0, 27, 0, 26, 0, 38, 0, 41, 0, 27, 0, 29, 0, 28, 0, 27, 0, 41, 0, 29, 0, 22, 0, 34, 0, 30, 0, 22, 0, 33, 0, 34, 0, 30, 0, 36, 0, 32, 0, 30, 0, 34, 0, 36, 0, 33, 0, 28, 0, 34, 0, 33, 0, 25, 0, 28, 0, 34, 0, 29, 0, 36, 0, 34, 0, 28, 0, 29, 0, 23, 0, 40, 0, 37, 0, 23, 0, 39, 0, 40, 0, 37, 0, 41, 0, 38, 0, 37, 0, 40, 0, 41, 0, 39, 0, 35, 0, 40, 0, 39, 0, 31, 0, 35, 0, 40, 0, 29, 0, 41, 0, 40, 0, 35, 0, 29, 0, 42, 0, 49, 0, 45, 0, 42, 0, 48, 0, 49, 0, 45, 0, 51, 0, 47, 0, 45, 0, 49, 0, 51, 0, 48, 0, 67, 0, 49, 0, 48, 0, 63, 0, 67, 0, 49, 0, 52, 0, 51, 0, 49, 0, 67, 0, 52, 0, 43, 0, 59, 0, 55, 0, 43, 0, 58, 0, 59, 0, 55, 0, 60, 0, 56, 0, 55, 0, 59, 0, 60, 0, 58, 0, 50, 0, 59, 0, 58, 0, 46, 0, 50, 0, 59, 0, 53, 0, 60, 0, 59, 0, 50, 0, 53, 0, 44, 0, 66, 0, 62, 0, 44, 0, 65, 0, 66, 0, 62, 0, 68, 0, 64, 0, 62, 0, 66, 0, 68, 0, 65, 0, 61, 0, 66, 0, 65, 0, 57, 0, 61, 0, 66, 0, 54, 0, 68, 0, 66, 0, 61, 0, 54, 0, 69, 0, 76, 0, 72, 0, 69, 0, 75, 0, 76, 0, 72, 0, 77, 0, 73, 0, 72, 0, 76, 0, 77, 0, 75, 0, 94, 0, 76, 0, 75, 0, 90, 0, 94, 0, 76, 0, 79, 0, 77, 0, 76, 0, 94, 0, 79, 0, 70, 0, 86, 0, 82, 0, 70, 0, 85, 0, 86, 0, 82, 0, 88, 0, 83, 0, 82, 0, 86, 0, 88, 0, 85, 0, 78, 0, 86, 0, 85, 0, 74, 0, 78, 0, 86, 0, 81, 0, 88, 0, 86, 0, 78, 0, 81, 0, 71, 0, 93, 0, 89, 0, 71, 0, 92, 0, 93, 0, 89, 0, 95, 0, 91, 0, 89, 0, 93, 0, 95, 0, 92, 0, 87, 0, 93, 0, 92, 0, 84, 0, 87, 0, 93, 0, 80, 0, 95, 0, 93, 0, 87, 0, 80, 0, 70, 0, 58, 0, 43, 0, 70, 0, 82, 0, 58, 0, 82, 0, 46, 0, 58, 0, 82, 0, 83, 0, 46, 0, 84, 0, 45, 0, 47, 0, 84, 0, 92, 0, 45, 0, 92, 0, 42, 0, 45, 0, 92, 0, 71, 0, 42, 0, 2, 0, 33, 0, 22, 0, 2, 0, 16, 0, 33, 0, 16, 0, 25, 0, 33, 0, 16, 0, 17, 0, 25, 0, 17, 0, 24, 0, 25, 0, 17, 0, 5, 0, 24, 0, 5, 0, 21, 0, 24, 0, 5, 0, 0, 0, 21, 0, 1, 0, 65, 0, 44, 0, 1, 0, 9, 0, 65, 0, 9, 0, 57, 0, 65, 0, 9, 0, 11, 0, 57, 0, 10, 0, 55, 0, 56, 0, 10, 0, 18, 0, 55, 0, 18, 0, 43, 0, 55, 0, 18, 0, 2, 0, 43, 0, 69, 0, 39, 0, 23, 0, 69, 0, 72, 0, 39, 0, 72, 0, 31, 0, 39, 0, 72, 0, 73, 0, 31, 0, 74, 0, 30, 0, 32, 0, 74, 0, 85, 0, 30, 0, 85, 0, 22, 0, 30, 0, 85, 0, 70, 0, 22, 0),
"lods": [0.00694598, PackedByteArray(22, 0, 43, 0, 125, 0, 22, 0, 70, 0, 43, 0, 9, 0, 125, 0, 43, 0, 125, 0, 9, 0, 13, 0, 1, 0, 13, 0, 9, 0, 1, 0, 12, 0, 13, 0, 1, 0, 9, 0, 65, 0, 9, 0, 43, 0, 65, 0, 1, 0, 65, 0, 44, 0, 43, 0, 59, 0, 65, 0, 44, 0, 65, 0, 66, 0, 65, 0, 59, 0, 66, 0, 44, 0, 66, 0, 62, 0, 12, 0, 6, 0, 13, 0, 12, 0, 4, 0, 6, 0, 3, 0, 6, 0, 4, 0, 0, 0, 6, 0, 3, 0, 19, 0, 13, 0, 6, 0, 125, 0, 13, 0, 19, 0, 125, 0, 19, 0, 6, 0, 0, 0, 5, 0, 6, 0, 125, 0, 6, 0, 5, 0, 5, 0, 0, 0, 21, 0, 125, 0, 5, 0, 24, 0, 5, 0, 21, 0, 24, 0, 125, 0, 24, 0, 22, 0, 21, 0, 126, 0, 24, 0, 22, 0, 24, 0, 126, 0, 21, 0, 26, 0, 126, 0, 26, 0, 38, 0, 126, 0, 37, 0, 126, 0, 38, 0, 22, 0, 126, 0, 34, 0, 37, 0, 39, 0, 126, 0, 39, 0, 34, 0, 126, 0, 22, 0, 34, 0, 39, 0, 23, 0, 39, 0, 37, 0, 69, 0, 39, 0, 23, 0, 73, 0, 22, 0, 39, 0, 69, 0, 72, 0, 39, 0, 72, 0, 73, 0, 39, 0, 69, 0, 76, 0, 72, 0, 72, 0, 76, 0, 73, 0, 73, 0, 70, 0, 22, 0, 70, 0, 73, 0, 76, 0, 69, 0, 75, 0, 76, 0, 70, 0, 76, 0, 86, 0, 75, 0, 94, 0, 76, 0, 75, 0, 90, 0, 94, 0, 89, 0, 94, 0, 90, 0, 93, 0, 76, 0, 94, 0, 89, 0, 93, 0, 94, 0, 93, 0, 86, 0, 76, 0, 71, 0, 93, 0, 89, 0, 92, 0, 86, 0, 93, 0, 71, 0, 92, 0, 93, 0, 70, 0, 86, 0, 92, 0, 92, 0, 71, 0, 42, 0, 70, 0, 92, 0, 46, 0, 70, 0, 46, 0, 43, 0, 92, 0, 42, 0, 45, 0, 92, 0, 45, 0, 46, 0, 42, 0, 49, 0, 45, 0, 45, 0, 49, 0, 46, 0, 43, 0, 46, 0, 49, 0, 43, 0, 49, 0, 59, 0, 42, 0, 48, 0, 49, 0, 66, 0, 59, 0, 49, 0, 48, 0, 67, 0, 49, 0, 66, 0, 49, 0, 67, 0, 48, 0, 63, 0, 67, 0, 62, 0, 66, 0, 67, 0, 62, 0, 67, 0, 63, 0), 0.0150099, PackedByteArray(112, 0, 43, 0, 110, 0, 109, 0, 110, 0, 43, 0, 109, 0, 117, 0, 44, 0, 43, 0, 118, 0, 44, 0, 44, 0, 118, 0, 119, 0, 62, 0, 44, 0, 119, 0, 116, 0, 119, 0, 118, 0, 43, 0, 116, 0, 118, 0, 48, 0, 119, 0, 116, 0, 121, 0, 116, 0, 43, 0, 112, 0, 121, 0, 43, 0, 121, 0, 122, 0, 116, 0, 121, 0, 123, 0, 122, 0, 122, 0, 123, 0, 124, 0, 89, 0, 122, 0, 124, 0, 121, 0, 120, 0, 123, 0, 120, 0, 124, 0, 123, 0, 120, 0, 121, 0, 112, 0, 75, 0, 124, 0, 120, 0, 120, 0, 112, 0, 113, 0, 112, 0, 115, 0, 113, 0, 113, 0, 115, 0, 114, 0, 112, 0, 114, 0, 115, 0, 37, 0, 113, 0, 114, 0, 37, 0, 114, 0, 38, 0, 26, 0, 38, 0, 114, 0, 21, 0, 26, 0, 114, 0, 110, 0, 114, 0, 112, 0, 0, 0, 21, 0, 114, 0, 110, 0, 0, 0, 114, 0, 110, 0, 111, 0, 0, 0, 0, 0, 111, 0, 3, 0, 110, 0, 109, 0, 111, 0, 3, 0, 111, 0, 4, 0, 12, 0, 111, 0, 109, 0, 12, 0, 4, 0, 111, 0), 0.0530864, PackedByteArray(99, 0, 101, 0, 97, 0, 96, 0, 97, 0, 101, 0, 99, 0, 105, 0, 101, 0, 105, 0, 107, 0, 101, 0, 96, 0, 101, 0, 103, 0, 89, 0, 107, 0, 108, 0, 107, 0, 106, 0, 108, 0, 75, 0, 108, 0, 106, 0, 106, 0, 99, 0, 23, 0, 12, 0, 98, 0, 96, 0, 12, 0, 4, 0, 98, 0, 98, 0, 0, 0, 21, 0, 98, 0, 21, 0, 100, 0, 37, 0, 23, 0, 100, 0, 37, 0, 100, 0, 38, 0, 48, 0, 104, 0, 102, 0, 103, 0, 102, 0, 104, 0, 62, 0, 103, 0, 104, 0)],
"material": SubResource("StandardMaterial3D_po1bx"),
"name": "Material.002",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 127,
"vertex_data": PackedByteArray(0, 0, 128, 63, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 63, 108, 227, 126, 63, 35, 252, 118, 63, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 63, 118, 101, 113, 63, 108, 227, 126, 63, 118, 101, 113, 63, 35, 252, 118, 63, 248, 139, 125, 63, 254, 31, 119, 63, 254, 31, 119, 63, 229, 250, 122, 63, 229, 250, 122, 63, 149, 213, 118, 63, 232, 211, 121, 63, 232, 211, 121, 63, 232, 211, 121, 63, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 63, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 63, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 63, 35, 252, 118, 63, 108, 227, 126, 63, 118, 101, 113, 63, 254, 31, 119, 63, 248, 139, 125, 63, 254, 31, 119, 63, 149, 213, 118, 63, 229, 250, 122, 63, 229, 250, 122, 63, 149, 213, 118, 63, 229, 250, 122, 63, 229, 250, 122, 63, 35, 252, 118, 63, 118, 101, 113, 63, 108, 227, 126, 63, 2, 185, 123, 63, 118, 101, 113, 63, 2, 185, 123, 63, 118, 101, 113, 63, 35, 252, 118, 63, 108, 227, 126, 63, 254, 31, 119, 63, 254, 31, 119, 63, 248, 139, 125, 63, 229, 250, 122, 63, 149, 213, 118, 63, 229, 250, 122, 63, 0, 0, 128, 63, 118, 101, 113, 191, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 63, 108, 227, 126, 63, 118, 101, 113, 191, 35, 252, 118, 63, 2, 185, 123, 63, 118, 101, 113, 191, 2, 185, 123, 63, 108, 227, 126, 63, 35, 252, 118, 191, 118, 101, 113, 63, 248, 139, 125, 63, 254, 31, 119, 191, 254, 31, 119, 63, 229, 250, 122, 63, 149, 213, 118, 191, 229, 250, 122, 63, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 63, 118, 101, 113, 63, 35, 252, 118, 191, 108, 227, 126, 63, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 63, 35, 252, 118, 63, 118, 101, 113, 191, 108, 227, 126, 63, 254, 31, 119, 63, 254, 31, 119, 191, 248, 139, 125, 63, 149, 213, 118, 63, 229, 250, 122, 191, 229, 250, 122, 63, 149, 213, 118, 63, 229, 250, 122, 191, 229, 250, 122, 63, 35, 252, 118, 63, 108, 227, 126, 191, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 191, 118, 101, 113, 63, 118, 101, 113, 63, 108, 227, 126, 191, 35, 252, 118, 63, 254, 31, 119, 63, 248, 139, 125, 191, 254, 31, 119, 63, 229, 250, 122, 63, 229, 250, 122, 191, 149, 213, 118, 63, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 63, 108, 227, 126, 191, 118, 101, 113, 63, 35, 252, 118, 63, 2, 185, 123, 191, 118, 101, 113, 63, 2, 185, 123, 63, 2, 185, 123, 191, 118, 101, 113, 63, 2, 185, 123, 63, 108, 227, 126, 191, 35, 252, 118, 63, 118, 101, 113, 63, 248, 139, 125, 191, 254, 31, 119, 63, 254, 31, 119, 63, 229, 250, 122, 191, 149, 213, 118, 63, 229, 250, 122, 63, 229, 250, 122, 191, 149, 213, 118, 63, 229, 250, 122, 63, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 63, 118, 101, 113, 191, 35, 252, 118, 63, 108, 227, 126, 63, 118, 101, 113, 191, 2, 185, 123, 63, 2, 185, 123, 63, 118, 101, 113, 191, 2, 185, 123, 63, 2, 185, 123, 63, 35, 252, 118, 191, 118, 101, 113, 63, 108, 227, 126, 63, 254, 31, 119, 191, 254, 31, 119, 63, 248, 139, 125, 63, 149, 213, 118, 191, 229, 250, 122, 63, 229, 250, 122, 63, 149, 213, 118, 191, 229, 250, 122, 63, 229, 250, 122, 63, 35, 252, 118, 191, 108, 227, 126, 63, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 63, 118, 101, 113, 191, 108, 227, 126, 63, 35, 252, 118, 63, 254, 31, 119, 191, 248, 139, 125, 63, 254, 31, 119, 63, 229, 250, 122, 191, 229, 250, 122, 63, 149, 213, 118, 63, 229, 250, 122, 191, 229, 250, 122, 63, 149, 213, 118, 63, 118, 101, 113, 191, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 191, 0, 0, 128, 63, 0, 0, 128, 191, 118, 101, 113, 191, 118, 101, 113, 63, 118, 101, 113, 191, 108, 227, 126, 191, 35, 252, 118, 63, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 191, 2, 185, 123, 191, 2, 185, 123, 63, 35, 252, 118, 191, 108, 227, 126, 191, 118, 101, 113, 63, 254, 31, 119, 191, 248, 139, 125, 191, 254, 31, 119, 63, 149, 213, 118, 191, 229, 250, 122, 191, 229, 250, 122, 63, 149, 213, 118, 191, 229, 250, 122, 191, 229, 250, 122, 63, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 63, 232, 211, 121, 191, 232, 211, 121, 191, 232, 211, 121, 63, 35, 252, 118, 191, 118, 101, 113, 191, 108, 227, 126, 63, 2, 185, 123, 191, 118, 101, 113, 191, 2, 185, 123, 63, 2, 185, 123, 191, 118, 101, 113, 191, 2, 185, 123, 63, 118, 101, 113, 191, 35, 252, 118, 191, 108, 227, 126, 63, 254, 31, 119, 191, 254, 31, 119, 191, 248, 139, 125, 63, 229, 250, 122, 191, 149, 213, 118, 191, 229, 250, 122, 63, 229, 250, 122, 191, 149, 213, 118, 191, 229, 250, 122, 63, 108, 227, 126, 191, 35, 252, 118, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 63, 108, 227, 126, 191, 118, 101, 113, 191, 35, 252, 118, 63, 248, 139, 125, 191, 254, 31, 119, 191, 254, 31, 119, 63, 229, 250, 122, 191, 229, 250, 122, 191, 149, 213, 118, 63, 229, 250, 122, 191, 229, 250, 122, 191, 149, 213, 118, 63, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 63, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 63, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 191, 118, 101, 113, 63, 108, 227, 126, 63, 35, 252, 118, 191, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 191, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 191, 118, 101, 113, 63, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 63, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 63, 248, 139, 125, 63, 254, 31, 119, 63, 254, 31, 119, 63, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 63, 118, 101, 113, 63, 0, 0, 128, 191, 118, 101, 113, 63, 108, 227, 126, 63, 118, 101, 113, 191, 35, 252, 118, 63, 254, 31, 119, 63, 254, 31, 119, 191, 248, 139, 125, 63, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 63, 0, 0, 128, 63, 254, 31, 119, 191, 254, 31, 119, 63, 248, 139, 125, 63, 2, 185, 123, 191, 2, 185, 123, 63, 118, 101, 113, 63, 118, 101, 113, 191, 0, 0, 128, 191, 118, 101, 113, 63, 118, 101, 113, 191, 118, 101, 113, 191, 0, 0, 128, 63, 0, 0, 128, 191, 118, 101, 113, 191, 118, 101, 113, 63, 254, 31, 119, 191, 254, 31, 119, 191, 248, 139, 125, 63, 2, 185, 123, 191, 2, 185, 123, 191, 118, 101, 113, 63, 118, 101, 113, 63, 118, 101, 113, 63, 0, 0, 128, 63, 248, 139, 125, 63, 254, 31, 119, 191, 254, 31, 119, 63, 166, 225, 50, 138, 254, 255, 241, 138, 50, 138, 166, 225, 64, 254, 182, 172, 202, 138, 202, 138, 254, 255, 227, 197, 125, 206, 72, 159, 205, 255, 252, 243, 58, 184, 58, 184, 166, 255, 243, 141, 70, 212, 252, 136, 244, 255, 83, 237, 213, 196, 148, 157, 233, 255, 201, 236, 37, 179, 38, 179, 73, 255, 243, 234, 170, 170, 170, 170, 255, 255, 255, 159, 252, 136, 70, 212, 98, 254, 88, 207, 215, 135, 19, 188, 229, 254, 155, 184, 215, 135, 19, 188, 41, 254, 2, 201, 147, 159, 57, 206, 9, 253, 45, 153, 148, 157, 213, 196, 254, 255, 255, 159, 177, 153, 37, 179, 64, 252, 152, 170, 177, 153, 38, 179, 169, 253, 243, 214, 187, 162, 252, 136, 244, 255, 78, 173, 18, 188, 216, 135, 255, 255, 255, 159, 252, 136, 187, 162, 215, 255, 67, 198, 148, 157, 148, 157, 233, 255, 190, 172, 38, 179, 177, 153, 255, 255, 255, 159, 166, 225, 204, 117, 254, 255, 12, 245, 202, 138, 52, 117, 254, 255, 26, 186, 50, 138, 88, 30, 64, 254, 71, 211, 70, 212, 2, 119, 244, 255, 171, 146, 18, 188, 38, 120, 255, 255, 255, 223, 58, 206, 107, 96, 202, 255, 4, 140, 200, 196, 100, 98, 228, 255, 57, 147, 38, 179, 77, 102, 255, 255, 255, 223, 170, 170, 85, 85, 255, 255, 255, 159, 252, 136, 67, 93, 215, 255, 186, 185, 215, 135, 235, 67, 41, 254, 251, 182, 215, 135, 235, 67, 229, 254, 99, 199, 187, 162, 2, 119, 244, 255, 176, 210, 148, 157, 106, 98, 233, 255, 64, 211, 177, 153, 216, 76, 169, 253, 11, 169, 177, 153, 217, 76, 64, 252, 101, 213, 72, 159, 129, 49, 54, 253, 191, 230, 41, 184, 168, 71, 160, 255, 13, 242, 253, 136, 184, 43, 99, 254, 165, 176, 150, 157, 45, 59, 254, 255, 255, 223, 37, 179, 216, 76, 72, 255, 11, 149, 88, 30, 50, 138, 228, 149, 255, 191, 52, 117, 202, 138, 53, 244, 255, 191, 204, 117, 166, 225, 110, 217, 222, 192, 184, 43, 252, 136, 87, 165, 250, 191, 235, 67, 215, 135, 73, 191, 207, 192, 235, 67, 215, 135, 19, 191, 46, 191, 196, 49, 147, 159, 9, 152, 228, 191, 54, 59, 154, 157, 115, 166, 241, 191, 217, 76, 177, 153, 195, 190, 122, 194, 216, 76, 177, 153, 68, 188, 131, 189, 85, 85, 170, 170, 142, 184, 70, 188, 84, 85, 170, 170, 255, 191, 183, 195, 84, 85, 169, 170, 255, 191, 255, 191, 2, 119, 187, 162, 118, 243, 235, 191, 39, 120, 19, 188, 55, 241, 139, 192, 39, 120, 19, 188, 248, 237, 20, 191, 67, 93, 252, 136, 157, 218, 4, 192, 106, 98, 148, 157, 125, 217, 10, 192, 77, 102, 37, 179, 49, 213, 222, 193, 77, 102, 38, 179, 23, 210, 212, 190, 182, 96, 125, 206, 126, 178, 99, 193, 100, 62, 177, 185, 164, 142, 9, 196, 61, 70, 151, 193, 82, 149, 9, 198, 1, 119, 70, 212, 77, 225, 49, 191, 104, 98, 209, 196, 255, 191, 255, 191, 216, 76, 37, 179, 13, 167, 31, 190, 217, 76, 38, 179, 146, 171, 41, 193, 204, 117, 88, 30, 110, 217, 32, 191, 52, 117, 52, 117, 53, 244, 255, 191, 88, 30, 204, 117, 228, 149, 255, 191, 2, 119, 184, 43, 75, 225, 205, 192, 39, 120, 235, 67, 248, 237, 234, 192, 39, 120, 235, 67, 55, 241, 114, 191, 107, 96, 197, 49, 91, 178, 132, 190, 106, 98, 41, 59, 255, 191, 255, 191, 77, 102, 216, 76, 23, 210, 41, 193, 77, 102, 217, 76, 50, 213, 31, 190, 84, 85, 85, 85, 255, 191, 255, 191, 85, 85, 84, 85, 142, 184, 183, 195, 84, 85, 84, 85, 255, 191, 70, 188, 67, 93, 2, 119, 157, 218, 250, 191, 235, 67, 39, 120, 73, 191, 46, 191, 235, 67, 39, 120, 20, 191, 207, 192, 2, 119, 67, 93, 118, 243, 19, 192, 106, 98, 106, 98, 125, 217, 244, 191, 216, 76, 77, 102, 68, 188, 122, 194, 217, 76, 77, 102, 195, 190, 131, 189, 129, 49, 182, 96, 4, 152, 23, 192, 77, 70, 100, 62, 96, 149, 251, 185, 103, 62, 61, 70, 154, 142, 239, 187, 184, 43, 2, 119, 87, 165, 4, 192, 41, 59, 106, 98, 107, 166, 10, 192, 217, 76, 216, 76, 146, 171, 212, 190, 216, 76, 217, 76, 13, 167, 222, 193, 181, 140, 107, 193, 64, 254, 182, 172, 181, 133, 11, 155, 205, 255, 252, 243, 40, 197, 61, 158, 205, 255, 252, 243, 227, 131, 50, 99, 202, 255, 4, 140, 212, 187, 125, 87, 202, 255, 4, 140, 56, 112, 68, 149, 228, 149, 255, 191, 108, 65, 214, 161, 228, 149, 255, 191, 83, 107, 171, 195, 110, 217, 222, 192, 49, 80, 144, 181, 164, 142, 9, 196, 96, 96, 237, 122, 110, 217, 32, 191, 106, 103, 110, 60, 110, 217, 32, 191, 237, 60, 95, 106, 228, 149, 255, 191, 98, 75, 126, 79, 96, 149, 251, 185, 13, 143, 53, 195, 64, 254, 182, 172, 214, 157, 245, 146, 254, 255, 227, 197, 207, 183, 132, 168, 233, 255, 201, 236, 224, 141, 98, 101, 254, 255, 26, 186, 128, 148, 150, 57, 64, 254, 71, 211, 192, 192, 135, 104, 244, 255, 171, 146, 98, 161, 109, 90, 233, 255, 64, 211, 5, 57, 206, 142, 228, 149, 255, 191, 229, 126, 211, 203, 53, 244, 255, 191, 65, 89, 130, 165, 125, 217, 10, 192, 151, 75, 220, 179, 164, 142, 9, 196, 249, 112, 159, 57, 110, 217, 32, 191, 217, 101, 154, 108, 53, 244, 255, 191, 57, 57, 140, 110, 228, 149, 255, 191, 121, 91, 177, 89, 125, 217, 244, 191, 14, 74, 59, 77, 96, 149, 251, 185, 166, 155, 75, 149, 254, 255, 227, 197, 183, 184, 77, 92, 228, 255, 57, 147)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_dnys1")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_hy8tp"]
resource_local_to_scene = true
shading_mode = 0
vertex_color_use_as_albedo = true
albedo_color = Color(0.0313726, 0.298039, 0.392157, 1)

[sub_resource type="CylinderMesh" id="CylinderMesh_kb6oc"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_g4bpp"]
resource_local_to_scene = true
shading_mode = 0
vertex_color_use_as_albedo = true
albedo_color = Color(0.8, 0.333333, 0, 1)

[sub_resource type="ConvexPolygonShape3D" id="ConvexPolygonShape3D_aed3s"]
points = PackedVector3Array(-0.961016, -0.992092, -0.961789, 0.964394, 0.99578, 0.932916, 1, -0.624902, 0.937004, -0.995748, -0.964558, 0.932885, -0.961016, 0.992092, -0.961789, 0.992092, -0.961016, -0.961789, 0.960441, 0.960635, -0.992454, -0.995748, 0.964558, 0.932885, 0.964394, -0.99578, 0.932916, -0.992092, -0.961016, -0.961789, -0.960603, -0.960603, -0.992422, 0.992092, 0.961016, -0.961789, 0.960853, -0.992124, -0.96182, 0.960853, 0.992124, -0.96182, -0.992092, 0.961016, -0.961789, 0.960441, -0.960635, -0.992454, -0.960603, 0.960603, -0.992422, -0.964558, -0.995748, 0.932885, -0.964558, 0.995748, 0.932885, 0.995748, 0.964558, 0.932696, 0.995748, -0.964558, 0.932885)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_i8q73"]
transparency = 4
shading_mode = 0
albedo_color = Color(0.619608, 0.933333, 0.85098, 0.113725)
grow = true
grow_amount = 0.035

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_hl3uc"]
resource_name = "Material"
cull_mode = 2
albedo_color = Color(0.906332, 0.906332, 0.906332, 1)
roughness = 0.4

[sub_resource type="ArrayMesh" id="ArrayMesh_gx2cb"]
_surfaces = [{
"aabb": AABB(-1, -1, -1, 2, 2, 2),
"format": 34896613377,
"index_count": 900,
"index_data": PackedByteArray(97, 0, 59, 0, 19, 0, 97, 0, 133, 0, 59, 0, 135, 0, 77, 0, 114, 0, 135, 0, 95, 0, 77, 0, 58, 0, 115, 0, 40, 0, 58, 0, 134, 0, 115, 0, 1, 0, 116, 0, 78, 0, 1, 0, 39, 0, 116, 0, 21, 0, 38, 0, 2, 0, 21, 0, 57, 0, 38, 0, 0, 0, 6, 0, 3, 0, 0, 0, 5, 0, 6, 0, 3, 0, 7, 0, 4, 0, 3, 0, 6, 0, 7, 0, 5, 0, 18, 0, 6, 0, 5, 0, 15, 0, 18, 0, 6, 0, 8, 0, 7, 0, 6, 0, 18, 0, 8, 0, 1, 0, 12, 0, 9, 0, 1, 0, 11, 0, 12, 0, 9, 0, 13, 0, 10, 0, 9, 0, 12, 0, 13, 0, 11, 0, 7, 0, 12, 0, 11, 0, 4, 0, 7, 0, 12, 0, 8, 0, 13, 0, 12, 0, 7, 0, 8, 0, 2, 0, 17, 0, 14, 0, 2, 0, 16, 0, 17, 0, 14, 0, 18, 0, 15, 0, 14, 0, 17, 0, 18, 0, 16, 0, 13, 0, 17, 0, 16, 0, 10, 0, 13, 0, 17, 0, 8, 0, 18, 0, 17, 0, 13, 0, 8, 0, 19, 0, 25, 0, 22, 0, 19, 0, 24, 0, 25, 0, 22, 0, 26, 0, 23, 0, 22, 0, 25, 0, 26, 0, 24, 0, 37, 0, 25, 0, 24, 0, 34, 0, 37, 0, 25, 0, 27, 0, 26, 0, 25, 0, 37, 0, 27, 0, 20, 0, 31, 0, 28, 0, 20, 0, 30, 0, 31, 0, 28, 0, 32, 0, 29, 0, 28, 0, 31, 0, 32, 0, 30, 0, 26, 0, 31, 0, 30, 0, 23, 0, 26, 0, 31, 0, 27, 0, 32, 0, 31, 0, 26, 0, 27, 0, 21, 0, 36, 0, 33, 0, 21, 0, 35, 0, 36, 0, 33, 0, 37, 0, 34, 0, 33, 0, 36, 0, 37, 0, 35, 0, 32, 0, 36, 0, 35, 0, 29, 0, 32, 0, 36, 0, 27, 0, 37, 0, 36, 0, 32, 0, 27, 0, 38, 0, 44, 0, 41, 0, 38, 0, 43, 0, 44, 0, 41, 0, 45, 0, 42, 0, 41, 0, 44, 0, 45, 0, 43, 0, 56, 0, 44, 0, 43, 0, 53, 0, 56, 0, 44, 0, 46, 0, 45, 0, 44, 0, 56, 0, 46, 0, 39, 0, 50, 0, 47, 0, 39, 0, 49, 0, 50, 0, 47, 0, 51, 0, 48, 0, 47, 0, 50, 0, 51, 0, 49, 0, 45, 0, 50, 0, 49, 0, 42, 0, 45, 0, 50, 0, 46, 0, 51, 0, 50, 0, 45, 0, 46, 0, 40, 0, 55, 0, 52, 0, 40, 0, 54, 0, 55, 0, 52, 0, 56, 0, 53, 0, 52, 0, 55, 0, 56, 0, 54, 0, 51, 0, 55, 0, 54, 0, 48, 0, 51, 0, 55, 0, 46, 0, 56, 0, 55, 0, 51, 0, 46, 0, 57, 0, 63, 0, 60, 0, 57, 0, 62, 0, 63, 0, 60, 0, 64, 0, 61, 0, 60, 0, 63, 0, 64, 0, 62, 0, 75, 0, 63, 0, 62, 0, 72, 0, 75, 0, 63, 0, 65, 0, 64, 0, 63, 0, 75, 0, 65, 0, 58, 0, 69, 0, 66, 0, 58, 0, 68, 0, 69, 0, 66, 0, 70, 0, 67, 0, 66, 0, 69, 0, 70, 0, 68, 0, 64, 0, 69, 0, 68, 0, 61, 0, 64, 0, 69, 0, 65, 0, 70, 0, 69, 0, 64, 0, 65, 0, 59, 0, 74, 0, 71, 0, 59, 0, 73, 0, 74, 0, 71, 0, 75, 0, 72, 0, 71, 0, 74, 0, 75, 0, 73, 0, 70, 0, 74, 0, 73, 0, 67, 0, 70, 0, 74, 0, 65, 0, 75, 0, 74, 0, 70, 0, 65, 0, 76, 0, 82, 0, 79, 0, 76, 0, 81, 0, 82, 0, 79, 0, 83, 0, 80, 0, 79, 0, 82, 0, 83, 0, 81, 0, 94, 0, 82, 0, 81, 0, 91, 0, 94, 0, 82, 0, 84, 0, 83, 0, 82, 0, 94, 0, 84, 0, 77, 0, 88, 0, 85, 0, 77, 0, 87, 0, 88, 0, 85, 0, 89, 0, 86, 0, 85, 0, 88, 0, 89, 0, 87, 0, 83, 0, 88, 0, 87, 0, 80, 0, 83, 0, 88, 0, 84, 0, 89, 0, 88, 0, 83, 0, 84, 0, 78, 0, 93, 0, 90, 0, 78, 0, 92, 0, 93, 0, 90, 0, 94, 0, 91, 0, 90, 0, 93, 0, 94, 0, 92, 0, 89, 0, 93, 0, 92, 0, 86, 0, 89, 0, 93, 0, 84, 0, 94, 0, 93, 0, 89, 0, 84, 0, 95, 0, 101, 0, 98, 0, 95, 0, 100, 0, 101, 0, 98, 0, 102, 0, 99, 0, 98, 0, 101, 0, 102, 0, 100, 0, 113, 0, 101, 0, 100, 0, 110, 0, 113, 0, 101, 0, 103, 0, 102, 0, 101, 0, 113, 0, 103, 0, 96, 0, 107, 0, 104, 0, 96, 0, 106, 0, 107, 0, 104, 0, 108, 0, 105, 0, 104, 0, 107, 0, 108, 0, 106, 0, 102, 0, 107, 0, 106, 0, 99, 0, 102, 0, 107, 0, 103, 0, 108, 0, 107, 0, 102, 0, 103, 0, 97, 0, 112, 0, 109, 0, 97, 0, 111, 0, 112, 0, 109, 0, 113, 0, 110, 0, 109, 0, 112, 0, 113, 0, 111, 0, 108, 0, 112, 0, 111, 0, 105, 0, 108, 0, 112, 0, 103, 0, 113, 0, 112, 0, 108, 0, 103, 0, 114, 0, 120, 0, 117, 0, 114, 0, 119, 0, 120, 0, 117, 0, 121, 0, 118, 0, 117, 0, 120, 0, 121, 0, 119, 0, 132, 0, 120, 0, 119, 0, 129, 0, 132, 0, 120, 0, 122, 0, 121, 0, 120, 0, 132, 0, 122, 0, 115, 0, 126, 0, 123, 0, 115, 0, 125, 0, 126, 0, 123, 0, 127, 0, 124, 0, 123, 0, 126, 0, 127, 0, 125, 0, 121, 0, 126, 0, 125, 0, 118, 0, 121, 0, 126, 0, 122, 0, 127, 0, 126, 0, 121, 0, 122, 0, 116, 0, 131, 0, 128, 0, 116, 0, 130, 0, 131, 0, 128, 0, 132, 0, 129, 0, 128, 0, 131, 0, 132, 0, 130, 0, 127, 0, 131, 0, 130, 0, 124, 0, 127, 0, 131, 0, 122, 0, 132, 0, 131, 0, 127, 0, 122, 0, 133, 0, 139, 0, 136, 0, 133, 0, 138, 0, 139, 0, 136, 0, 140, 0, 137, 0, 136, 0, 139, 0, 140, 0, 138, 0, 151, 0, 139, 0, 138, 0, 148, 0, 151, 0, 139, 0, 141, 0, 140, 0, 139, 0, 151, 0, 141, 0, 134, 0, 145, 0, 142, 0, 134, 0, 144, 0, 145, 0, 142, 0, 146, 0, 143, 0, 142, 0, 145, 0, 146, 0, 144, 0, 140, 0, 145, 0, 144, 0, 137, 0, 140, 0, 145, 0, 141, 0, 146, 0, 145, 0, 140, 0, 141, 0, 135, 0, 150, 0, 147, 0, 135, 0, 149, 0, 150, 0, 147, 0, 151, 0, 148, 0, 147, 0, 150, 0, 151, 0, 149, 0, 146, 0, 150, 0, 149, 0, 143, 0, 146, 0, 150, 0, 141, 0, 151, 0, 150, 0, 146, 0, 141, 0, 97, 0, 138, 0, 133, 0, 97, 0, 109, 0, 138, 0, 109, 0, 148, 0, 138, 0, 109, 0, 110, 0, 148, 0, 110, 0, 147, 0, 148, 0, 110, 0, 100, 0, 147, 0, 100, 0, 135, 0, 147, 0, 100, 0, 95, 0, 135, 0, 19, 0, 111, 0, 97, 0, 19, 0, 22, 0, 111, 0, 22, 0, 105, 0, 111, 0, 22, 0, 23, 0, 105, 0, 23, 0, 104, 0, 105, 0, 23, 0, 30, 0, 104, 0, 30, 0, 96, 0, 104, 0, 30, 0, 20, 0, 96, 0, 2, 0, 35, 0, 21, 0, 2, 0, 14, 0, 35, 0, 14, 0, 29, 0, 35, 0, 14, 0, 15, 0, 29, 0, 15, 0, 28, 0, 29, 0, 15, 0, 5, 0, 28, 0, 5, 0, 20, 0, 28, 0, 5, 0, 0, 0, 20, 0, 134, 0, 125, 0, 115, 0, 134, 0, 142, 0, 125, 0, 142, 0, 118, 0, 125, 0, 142, 0, 143, 0, 118, 0, 143, 0, 117, 0, 118, 0, 143, 0, 149, 0, 117, 0, 149, 0, 114, 0, 117, 0, 149, 0, 135, 0, 114, 0, 40, 0, 68, 0, 58, 0, 40, 0, 52, 0, 68, 0, 52, 0, 61, 0, 68, 0, 52, 0, 53, 0, 61, 0, 53, 0, 60, 0, 61, 0, 53, 0, 43, 0, 60, 0, 43, 0, 57, 0, 60, 0, 43, 0, 38, 0, 57, 0, 76, 0, 106, 0, 96, 0, 76, 0, 79, 0, 106, 0, 79, 0, 99, 0, 106, 0, 79, 0, 80, 0, 99, 0, 80, 0, 98, 0, 99, 0, 80, 0, 87, 0, 98, 0, 87, 0, 95, 0, 98, 0, 87, 0, 77, 0, 95, 0, 39, 0, 130, 0, 116, 0, 39, 0, 47, 0, 130, 0, 47, 0, 124, 0, 130, 0, 47, 0, 48, 0, 124, 0, 48, 0, 123, 0, 124, 0, 48, 0, 54, 0, 123, 0, 54, 0, 115, 0, 123, 0, 54, 0, 40, 0, 115, 0, 1, 0, 49, 0, 39, 0, 1, 0, 9, 0, 49, 0, 9, 0, 42, 0, 49, 0, 9, 0, 10, 0, 42, 0, 10, 0, 41, 0, 42, 0, 10, 0, 16, 0, 41, 0, 16, 0, 38, 0, 41, 0, 16, 0, 2, 0, 38, 0, 133, 0, 73, 0, 59, 0, 133, 0, 136, 0, 73, 0, 136, 0, 67, 0, 73, 0, 136, 0, 137, 0, 67, 0, 137, 0, 66, 0, 67, 0, 137, 0, 144, 0, 66, 0, 144, 0, 58, 0, 66, 0, 144, 0, 134, 0, 58, 0, 116, 0, 92, 0, 78, 0, 116, 0, 128, 0, 92, 0, 128, 0, 86, 0, 92, 0, 128, 0, 129, 0, 86, 0, 129, 0, 85, 0, 86, 0, 129, 0, 119, 0, 85, 0, 119, 0, 77, 0, 85, 0, 119, 0, 114, 0, 77, 0, 78, 0, 11, 0, 1, 0, 78, 0, 90, 0, 11, 0, 90, 0, 4, 0, 11, 0, 90, 0, 91, 0, 4, 0, 91, 0, 3, 0, 4, 0, 91, 0, 81, 0, 3, 0, 81, 0, 0, 0, 3, 0, 81, 0, 76, 0, 0, 0, 59, 0, 24, 0, 19, 0, 59, 0, 71, 0, 24, 0, 71, 0, 34, 0, 24, 0, 71, 0, 72, 0, 34, 0, 72, 0, 33, 0, 34, 0, 72, 0, 62, 0, 33, 0, 62, 0, 21, 0, 33, 0, 62, 0, 57, 0, 21, 0, 96, 0, 0, 0, 76, 0, 96, 0, 20, 0, 0, 0),
"lods": [0.0179063, PackedByteArray(97, 0, 59, 0, 19, 0, 19, 0, 23, 0, 97, 0, 23, 0, 27, 0, 26, 0, 19, 0, 27, 0, 23, 0, 23, 0, 105, 0, 97, 0, 19, 0, 21, 0, 27, 0, 59, 0, 21, 0, 19, 0, 97, 0, 105, 0, 108, 0, 97, 0, 108, 0, 113, 0, 113, 0, 108, 0, 103, 0, 97, 0, 113, 0, 110, 0, 97, 0, 110, 0, 133, 0, 97, 0, 133, 0, 59, 0, 110, 0, 148, 0, 133, 0, 133, 0, 148, 0, 151, 0, 133, 0, 151, 0, 137, 0, 133, 0, 137, 0, 59, 0, 137, 0, 151, 0, 140, 0, 151, 0, 141, 0, 140, 0, 137, 0, 67, 0, 59, 0, 59, 0, 67, 0, 70, 0, 70, 0, 65, 0, 59, 0, 57, 0, 59, 0, 65, 0, 59, 0, 57, 0, 21, 0, 58, 0, 57, 0, 65, 0, 58, 0, 65, 0, 67, 0, 67, 0, 65, 0, 70, 0, 134, 0, 58, 0, 67, 0, 137, 0, 134, 0, 67, 0, 134, 0, 137, 0, 140, 0, 134, 0, 140, 0, 146, 0, 146, 0, 140, 0, 141, 0, 134, 0, 146, 0, 143, 0, 134, 0, 143, 0, 115, 0, 58, 0, 134, 0, 115, 0, 143, 0, 118, 0, 115, 0, 115, 0, 118, 0, 121, 0, 115, 0, 121, 0, 127, 0, 127, 0, 121, 0, 122, 0, 115, 0, 127, 0, 124, 0, 58, 0, 115, 0, 40, 0, 40, 0, 115, 0, 124, 0, 48, 0, 40, 0, 124, 0, 40, 0, 48, 0, 51, 0, 40, 0, 57, 0, 58, 0, 51, 0, 46, 0, 40, 0, 40, 0, 38, 0, 57, 0, 38, 0, 40, 0, 46, 0, 21, 0, 57, 0, 38, 0, 39, 0, 38, 0, 46, 0, 21, 0, 38, 0, 2, 0, 2, 0, 20, 0, 21, 0, 1, 0, 38, 0, 39, 0, 1, 0, 2, 0, 38, 0, 39, 0, 46, 0, 48, 0, 48, 0, 46, 0, 51, 0, 39, 0, 48, 0, 116, 0, 1, 0, 39, 0, 116, 0, 48, 0, 124, 0, 116, 0, 116, 0, 124, 0, 127, 0, 116, 0, 127, 0, 132, 0, 132, 0, 127, 0, 122, 0, 116, 0, 132, 0, 129, 0, 116, 0, 129, 0, 78, 0, 1, 0, 116, 0, 78, 0, 129, 0, 86, 0, 78, 0, 78, 0, 86, 0, 89, 0, 78, 0, 89, 0, 94, 0, 94, 0, 89, 0, 84, 0, 78, 0, 94, 0, 91, 0, 78, 0, 91, 0, 1, 0, 91, 0, 4, 0, 1, 0, 1, 0, 4, 0, 7, 0, 1, 0, 7, 0, 8, 0, 2, 0, 1, 0, 8, 0, 0, 0, 2, 0, 8, 0, 0, 0, 8, 0, 4, 0, 2, 0, 0, 0, 20, 0, 4, 0, 8, 0, 7, 0, 76, 0, 0, 0, 4, 0, 91, 0, 76, 0, 4, 0, 76, 0, 91, 0, 94, 0, 96, 0, 0, 0, 76, 0, 96, 0, 20, 0, 0, 0, 76, 0, 94, 0, 83, 0, 94, 0, 84, 0, 83, 0, 77, 0, 76, 0, 83, 0, 89, 0, 83, 0, 84, 0, 77, 0, 83, 0, 89, 0, 77, 0, 89, 0, 86, 0, 76, 0, 77, 0, 95, 0, 76, 0, 95, 0, 96, 0, 114, 0, 77, 0, 86, 0, 129, 0, 114, 0, 86, 0, 114, 0, 129, 0, 132, 0, 132, 0, 122, 0, 121, 0, 118, 0, 132, 0, 121, 0, 114, 0, 132, 0, 118, 0, 135, 0, 77, 0, 114, 0, 135, 0, 114, 0, 118, 0, 135, 0, 95, 0, 77, 0, 143, 0, 135, 0, 118, 0, 135, 0, 143, 0, 146, 0, 135, 0, 146, 0, 151, 0, 151, 0, 146, 0, 141, 0, 135, 0, 151, 0, 148, 0, 95, 0, 135, 0, 148, 0, 110, 0, 95, 0, 148, 0, 95, 0, 110, 0, 113, 0, 95, 0, 113, 0, 102, 0, 113, 0, 103, 0, 102, 0, 96, 0, 95, 0, 102, 0, 96, 0, 102, 0, 108, 0, 108, 0, 102, 0, 103, 0, 96, 0, 108, 0, 105, 0, 20, 0, 96, 0, 105, 0, 23, 0, 20, 0, 105, 0, 20, 0, 23, 0, 26, 0, 20, 0, 26, 0, 27, 0, 21, 0, 20, 0, 27, 0), 0.0210464, PackedByteArray(105, 0, 59, 0, 27, 0, 110, 0, 105, 0, 103, 0, 105, 0, 110, 0, 148, 0, 105, 0, 148, 0, 59, 0, 148, 0, 141, 0, 137, 0, 148, 0, 137, 0, 59, 0, 137, 0, 67, 0, 59, 0, 67, 0, 65, 0, 59, 0, 59, 0, 21, 0, 27, 0, 59, 0, 65, 0, 21, 0, 21, 0, 65, 0, 46, 0, 21, 0, 46, 0, 2, 0, 2, 0, 23, 0, 21, 0, 48, 0, 46, 0, 65, 0, 48, 0, 65, 0, 58, 0, 58, 0, 65, 0, 67, 0, 137, 0, 58, 0, 67, 0, 58, 0, 118, 0, 48, 0, 58, 0, 137, 0, 118, 0, 48, 0, 118, 0, 124, 0, 124, 0, 118, 0, 122, 0, 137, 0, 143, 0, 118, 0, 143, 0, 137, 0, 141, 0, 2, 0, 4, 0, 23, 0, 96, 0, 23, 0, 4, 0, 96, 0, 4, 0, 83, 0, 23, 0, 96, 0, 105, 0, 96, 0, 102, 0, 105, 0, 83, 0, 102, 0, 96, 0, 105, 0, 102, 0, 103, 0, 91, 0, 84, 0, 83, 0, 91, 0, 83, 0, 4, 0, 4, 0, 2, 0, 8, 0, 86, 0, 83, 0, 84, 0, 77, 0, 83, 0, 86, 0, 83, 0, 77, 0, 102, 0, 129, 0, 77, 0, 86, 0, 110, 0, 103, 0, 102, 0, 110, 0, 102, 0, 148, 0, 143, 0, 102, 0, 77, 0, 102, 0, 143, 0, 148, 0, 143, 0, 77, 0, 129, 0, 148, 0, 143, 0, 141, 0, 143, 0, 129, 0, 118, 0, 129, 0, 122, 0, 118, 0, 1, 0, 124, 0, 86, 0, 124, 0, 129, 0, 86, 0, 129, 0, 124, 0, 122, 0, 86, 0, 91, 0, 1, 0, 91, 0, 86, 0, 84, 0, 91, 0, 4, 0, 1, 0, 1, 0, 4, 0, 8, 0, 2, 0, 1, 0, 8, 0, 1, 0, 2, 0, 46, 0, 1, 0, 39, 0, 124, 0, 1, 0, 46, 0, 39, 0, 39, 0, 48, 0, 124, 0, 39, 0, 46, 0, 48, 0, 21, 0, 23, 0, 27, 0, 27, 0, 23, 0, 105, 0), 0.215511, PackedByteArray(23, 0, 67, 0, 27, 0, 67, 0, 65, 0, 27, 0, 23, 0, 148, 0, 67, 0, 148, 0, 141, 0, 67, 0, 148, 0, 23, 0, 103, 0, 143, 0, 102, 0, 86, 0, 83, 0, 86, 0, 102, 0, 86, 0, 122, 0, 143, 0, 102, 0, 143, 0, 148, 0, 148, 0, 143, 0, 141, 0, 148, 0, 103, 0, 102, 0, 86, 0, 83, 0, 84, 0, 4, 0, 84, 0, 83, 0, 83, 0, 8, 0, 4, 0, 83, 0, 102, 0, 23, 0, 23, 0, 102, 0, 103, 0, 23, 0, 8, 0, 83, 0, 8, 0, 23, 0, 27, 0, 67, 0, 143, 0, 48, 0, 143, 0, 67, 0, 141, 0, 48, 0, 143, 0, 122, 0, 48, 0, 65, 0, 67, 0, 48, 0, 46, 0, 65, 0, 4, 0, 48, 0, 86, 0, 86, 0, 48, 0, 122, 0, 4, 0, 86, 0, 84, 0, 4, 0, 46, 0, 48, 0, 4, 0, 8, 0, 46, 0, 27, 0, 46, 0, 8, 0, 27, 0, 65, 0, 46, 0)],
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 152,
"vertex_data": PackedByteArray(177, 248, 177, 248, 0, 0, 0, 0, 177, 248, 255, 255, 77, 7, 0, 0, 255, 255, 177, 248, 77, 7, 0, 0, 177, 248, 125, 251, 142, 0, 0, 0, 177, 248, 219, 253, 35, 2, 0, 0, 125, 251, 177, 248, 142, 0, 0, 0, 143, 251, 143, 251, 58, 1, 0, 0, 105, 251, 124, 253, 130, 2, 0, 0, 232, 252, 232, 252, 22, 3, 0, 0, 125, 251, 112, 255, 77, 7, 0, 0, 219, 253, 219, 253, 77, 7, 0, 0, 177, 248, 112, 255, 129, 4, 0, 0, 143, 251, 196, 254, 111, 4, 0, 0, 124, 253, 124, 253, 149, 4, 0, 0, 112, 255, 177, 248, 129, 4, 0, 0, 219, 253, 177, 248, 35, 2, 0, 0, 112, 255, 125, 251, 77, 7, 0, 0, 196, 254, 143, 251, 111, 4, 0, 0, 124, 253, 105, 251, 130, 2, 0, 0, 177, 248, 0, 0, 77, 7, 0, 0, 177, 248, 77, 7, 0, 0, 0, 0, 255, 255, 77, 7, 77, 7, 0, 0, 177, 248, 142, 0, 129, 4, 0, 0, 177, 248, 35, 2, 35, 2, 0, 0, 125, 251, 142, 0, 77, 7, 0, 0, 143, 251, 58, 1, 111, 4, 0, 0, 105, 251, 130, 2, 130, 2, 0, 0, 232, 252, 22, 3, 22, 3, 0, 0, 125, 251, 77, 7, 142, 0, 0, 0, 219, 253, 77, 7, 35, 2, 0, 0, 177, 248, 129, 4, 142, 0, 0, 0, 143, 251, 111, 4, 58, 1, 0, 0, 124, 253, 149, 4, 130, 2, 0, 0, 112, 255, 129, 4, 77, 7, 0, 0, 219, 253, 35, 2, 77, 7, 0, 0, 112, 255, 77, 7, 129, 4, 0, 0, 196, 254, 111, 4, 111, 4, 0, 0, 124, 253, 130, 2, 149, 4, 0, 0, 255, 255, 177, 248, 177, 248, 0, 0, 177, 248, 255, 255, 177, 248, 0, 0, 177, 248, 177, 248, 255, 255, 0, 0, 112, 255, 125, 251, 177, 248, 0, 0, 219, 253, 219, 253, 177, 248, 0, 0, 112, 255, 177, 248, 125, 251, 0, 0, 196, 254, 143, 251, 143, 251, 0, 0, 124, 253, 124, 253, 105, 251, 0, 0, 232, 252, 232, 252, 232, 252, 0, 0, 177, 248, 112, 255, 125, 251, 0, 0, 177, 248, 219, 253, 219, 253, 0, 0, 125, 251, 112, 255, 177, 248, 0, 0, 143, 251, 196, 254, 143, 251, 0, 0, 105, 251, 124, 253, 124, 253, 0, 0, 125, 251, 177, 248, 112, 255, 0, 0, 219, 253, 177, 248, 219, 253, 0, 0, 177, 248, 125, 251, 112, 255, 0, 0, 143, 251, 143, 251, 196, 254, 0, 0, 124, 253, 105, 251, 124, 253, 0, 0, 255, 255, 77, 7, 177, 248, 0, 0, 177, 248, 77, 7, 255, 255, 0, 0, 177, 248, 0, 0, 177, 248, 0, 0, 112, 255, 77, 7, 125, 251, 0, 0, 219, 253, 77, 7, 219, 253, 0, 0, 112, 255, 129, 4, 177, 248, 0, 0, 196, 254, 111, 4, 143, 251, 0, 0, 124, 253, 149, 4, 124, 253, 0, 0, 232, 252, 22, 3, 232, 252, 0, 0, 177, 248, 129, 4, 112, 255, 0, 0, 177, 248, 35, 2, 219, 253, 0, 0, 125, 251, 77, 7, 112, 255, 0, 0, 143, 251, 111, 4, 196, 254, 0, 0, 105, 251, 130, 2, 124, 253, 0, 0, 125, 251, 142, 0, 177, 248, 0, 0, 219, 253, 35, 2, 177, 248, 0, 0, 177, 248, 142, 0, 125, 251, 0, 0, 143, 251, 58, 1, 143, 251, 0, 0, 124, 253, 130, 2, 105, 251, 0, 0, 77, 7, 177, 248, 0, 0, 0, 0, 0, 0, 177, 248, 77, 7, 0, 0, 77, 7, 255, 255, 77, 7, 0, 0, 129, 4, 177, 248, 142, 0, 0, 0, 35, 2, 177, 248, 35, 2, 0, 0, 77, 7, 125, 251, 142, 0, 0, 0, 111, 4, 143, 251, 58, 1, 0, 0, 130, 2, 105, 251, 130, 2, 0, 0, 22, 3, 232, 252, 22, 3, 0, 0, 142, 0, 125, 251, 77, 7, 0, 0, 35, 2, 219, 253, 77, 7, 0, 0, 142, 0, 177, 248, 129, 4, 0, 0, 58, 1, 143, 251, 111, 4, 0, 0, 130, 2, 124, 253, 149, 4, 0, 0, 77, 7, 112, 255, 129, 4, 0, 0, 77, 7, 219, 253, 35, 2, 0, 0, 129, 4, 112, 255, 77, 7, 0, 0, 111, 4, 196, 254, 111, 4, 0, 0, 149, 4, 124, 253, 130, 2, 0, 0, 0, 0, 77, 7, 77, 7, 0, 0, 77, 7, 77, 7, 0, 0, 0, 0, 77, 7, 0, 0, 77, 7, 0, 0, 142, 0, 77, 7, 129, 4, 0, 0, 35, 2, 77, 7, 35, 2, 0, 0, 142, 0, 129, 4, 77, 7, 0, 0, 58, 1, 111, 4, 111, 4, 0, 0, 130, 2, 149, 4, 130, 2, 0, 0, 22, 3, 22, 3, 22, 3, 0, 0, 77, 7, 129, 4, 142, 0, 0, 0, 77, 7, 35, 2, 35, 2, 0, 0, 129, 4, 77, 7, 142, 0, 0, 0, 111, 4, 111, 4, 58, 1, 0, 0, 149, 4, 130, 2, 130, 2, 0, 0, 129, 4, 142, 0, 77, 7, 0, 0, 35, 2, 35, 2, 77, 7, 0, 0, 77, 7, 142, 0, 129, 4, 0, 0, 111, 4, 58, 1, 111, 4, 0, 0, 130, 2, 130, 2, 149, 4, 0, 0, 0, 0, 177, 248, 177, 248, 0, 0, 77, 7, 177, 248, 255, 255, 0, 0, 77, 7, 255, 255, 177, 248, 0, 0, 142, 0, 177, 248, 125, 251, 0, 0, 35, 2, 177, 248, 219, 253, 0, 0, 142, 0, 125, 251, 177, 248, 0, 0, 58, 1, 143, 251, 143, 251, 0, 0, 130, 2, 105, 251, 124, 253, 0, 0, 22, 3, 232, 252, 232, 252, 0, 0, 77, 7, 125, 251, 112, 255, 0, 0, 77, 7, 219, 253, 219, 253, 0, 0, 129, 4, 177, 248, 112, 255, 0, 0, 111, 4, 143, 251, 196, 254, 0, 0, 149, 4, 124, 253, 124, 253, 0, 0, 129, 4, 112, 255, 177, 248, 0, 0, 35, 2, 219, 253, 177, 248, 0, 0, 77, 7, 112, 255, 125, 251, 0, 0, 111, 4, 196, 254, 143, 251, 0, 0, 130, 2, 124, 253, 105, 251, 0, 0, 77, 7, 0, 0, 177, 248, 0, 0, 77, 7, 77, 7, 255, 255, 0, 0, 0, 0, 77, 7, 177, 248, 0, 0, 77, 7, 142, 0, 125, 251, 0, 0, 77, 7, 35, 2, 219, 253, 0, 0, 129, 4, 142, 0, 177, 248, 0, 0, 111, 4, 58, 1, 143, 251, 0, 0, 149, 4, 130, 2, 124, 253, 0, 0, 22, 3, 22, 3, 232, 252, 0, 0, 129, 4, 77, 7, 112, 255, 0, 0, 35, 2, 77, 7, 219, 253, 0, 0, 77, 7, 129, 4, 112, 255, 0, 0, 111, 4, 111, 4, 196, 254, 0, 0, 130, 2, 149, 4, 124, 253, 0, 0, 142, 0, 129, 4, 177, 248, 0, 0, 35, 2, 35, 2, 177, 248, 0, 0, 142, 0, 77, 7, 125, 251, 0, 0, 58, 1, 111, 4, 143, 251, 0, 0, 130, 2, 130, 2, 105, 251, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_208t2"]
resource_name = "card_Cube"
_surfaces = [{
"aabb": AABB(-1, -1, -1, 2, 2, 2),
"attribute_data": PackedByteArray(44, 158, 210, 129, 210, 161, 44, 126, 44, 158, 44, 126, 222, 158, 210, 129, 255, 159, 210, 129, 210, 161, 255, 127, 44, 158, 31, 129, 227, 158, 27, 129, 255, 159, 36, 129, 36, 161, 255, 127, 189, 159, 255, 127, 31, 161, 44, 126, 255, 159, 44, 126, 210, 161, 223, 126, 27, 161, 227, 126, 255, 159, 218, 126, 255, 159, 218, 126, 44, 158, 223, 126, 44, 158, 255, 127, 222, 158, 44, 126, 227, 158, 227, 126, 218, 158, 255, 127, 44, 94, 44, 126, 210, 97, 210, 129, 210, 97, 44, 126, 44, 94, 223, 126, 44, 94, 255, 127, 255, 95, 210, 129, 223, 94, 44, 126, 227, 94, 227, 126, 218, 94, 255, 127, 255, 95, 36, 129, 255, 95, 189, 127, 210, 97, 31, 129, 210, 97, 255, 127, 32, 97, 210, 129, 27, 97, 27, 129, 36, 97, 255, 127, 32, 97, 44, 126, 255, 95, 44, 126, 210, 97, 223, 126, 27, 97, 227, 126, 255, 95, 218, 126, 44, 158, 211, 65, 210, 161, 211, 65, 44, 158, 44, 62, 222, 158, 211, 65, 255, 159, 211, 65, 44, 158, 32, 65, 227, 158, 27, 65, 255, 159, 37, 65, 255, 159, 65, 64, 210, 161, 32, 65, 255, 159, 44, 62, 210, 161, 255, 63, 31, 161, 211, 65, 27, 161, 27, 65, 255, 159, 218, 62, 36, 161, 255, 63, 44, 158, 223, 62, 44, 158, 255, 63, 222, 158, 44, 62, 227, 158, 227, 62, 218, 158, 255, 63, 210, 97, 211, 65, 210, 97, 44, 62, 44, 94, 211, 65, 210, 97, 32, 65, 210, 97, 255, 63, 32, 97, 211, 65, 27, 97, 27, 65, 36, 97, 255, 63, 65, 96, 255, 63, 32, 97, 44, 62, 44, 94, 255, 63, 255, 95, 44, 62, 210, 97, 223, 62, 27, 97, 227, 62, 218, 94, 255, 63, 255, 95, 218, 62, 223, 94, 211, 65, 255, 95, 211, 65, 44, 94, 32, 65, 227, 94, 27, 65, 255, 95, 37, 65, 44, 158, 43, 190, 44, 158, 210, 193, 43, 222, 44, 126, 44, 158, 222, 190, 44, 158, 255, 191, 222, 158, 43, 190, 227, 158, 227, 190, 218, 158, 255, 191, 57, 159, 255, 191, 255, 159, 196, 192, 57, 223, 255, 127, 222, 158, 210, 193, 255, 159, 210, 193, 255, 223, 44, 126, 44, 158, 31, 193, 227, 158, 27, 193, 255, 159, 36, 193, 255, 223, 218, 126, 43, 222, 223, 126, 255, 159, 43, 190, 43, 222, 255, 127, 222, 222, 44, 126, 227, 222, 227, 126, 255, 159, 217, 190, 217, 222, 255, 127, 210, 97, 210, 193, 210, 97, 43, 190, 211, 33, 44, 126, 210, 97, 31, 193, 210, 97, 255, 191, 32, 97, 210, 193, 27, 97, 27, 193, 36, 97, 255, 191, 255, 31, 57, 127, 255, 95, 57, 191, 197, 96, 255, 191, 32, 97, 43, 190, 211, 33, 255, 127, 255, 95, 43, 190, 210, 97, 222, 190, 27, 97, 227, 190, 37, 33, 255, 127, 255, 95, 217, 190, 32, 33, 44, 126, 255, 31, 44, 126, 255, 95, 210, 193, 211, 33, 223, 126, 27, 33, 227, 126, 255, 31, 218, 126, 255, 95, 36, 193, 44, 158, 43, 254, 44, 158, 211, 1, 43, 222, 211, 65, 44, 158, 222, 254, 44, 158, 0, 0, 44, 158, 255, 255, 222, 158, 43, 254, 227, 158, 227, 254, 218, 158, 0, 0, 218, 158, 255, 255, 57, 159, 255, 255, 255, 159, 197, 0, 255, 223, 197, 64, 222, 158, 211, 1, 255, 159, 211, 1, 43, 222, 255, 63, 44, 158, 32, 1, 227, 158, 27, 1, 255, 159, 37, 1, 217, 222, 255, 63, 222, 222, 211, 65, 255, 159, 43, 254, 255, 223, 211, 65, 43, 222, 32, 65, 227, 222, 27, 65, 255, 159, 217, 254, 255, 223, 37, 65, 211, 33, 211, 65, 210, 97, 211, 1, 210, 97, 43, 254, 211, 33, 32, 65, 211, 33, 255, 63, 255, 95, 211, 1, 32, 33, 211, 65, 27, 33, 27, 65, 37, 33, 255, 63, 255, 95, 37, 1, 197, 32, 255, 63, 255, 95, 57, 255, 197, 96, 0, 0, 210, 97, 32, 1, 210, 97, 0, 0, 210, 97, 255, 255, 32, 97, 211, 1, 27, 97, 27, 1, 36, 97, 255, 255, 36, 97, 0, 0, 32, 97, 43, 254, 255, 31, 211, 65, 255, 95, 43, 254, 210, 97, 222, 254, 27, 97, 227, 254, 255, 31, 37, 65, 255, 95, 217, 254, 210, 161, 255, 127, 189, 159, 255, 127, 189, 159, 255, 127, 189, 159, 255, 127, 44, 94, 255, 127, 255, 95, 210, 129, 255, 95, 189, 127, 255, 95, 189, 127, 255, 95, 189, 127, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 44, 62, 210, 161, 255, 63, 65, 96, 255, 63, 65, 96, 255, 63, 65, 96, 255, 63, 44, 94, 255, 63, 255, 95, 44, 62, 218, 158, 255, 191, 218, 158, 255, 191, 57, 159, 255, 191, 57, 223, 255, 127, 255, 159, 210, 193, 255, 223, 44, 126, 36, 97, 255, 191, 36, 97, 255, 191, 255, 31, 57, 127, 255, 95, 57, 191, 197, 96, 255, 191, 57, 159, 255, 255, 255, 159, 197, 0, 255, 223, 197, 64, 197, 32, 255, 63, 197, 96, 0, 0, 210, 97, 0, 0, 210, 97, 255, 255, 255, 31, 211, 65, 44, 158, 44, 126, 255, 159, 210, 129, 189, 159, 255, 127, 210, 97, 44, 126, 255, 95, 210, 129, 255, 95, 210, 129, 255, 95, 189, 127, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 65, 64, 255, 159, 44, 62, 65, 96, 255, 63, 65, 96, 255, 63, 65, 96, 255, 63, 218, 158, 255, 191, 218, 158, 255, 191, 255, 223, 44, 126, 36, 97, 255, 191, 36, 97, 255, 191, 211, 33, 255, 127, 44, 158, 0, 0, 43, 222, 255, 63, 255, 159, 43, 254, 255, 95, 211, 1, 210, 97, 255, 255, 255, 31, 211, 65, 210, 161, 44, 126, 44, 158, 44, 126, 189, 159, 255, 127, 210, 97, 210, 129, 210, 97, 44, 126, 255, 95, 189, 127, 44, 158, 211, 65, 44, 158, 44, 62, 255, 159, 65, 64, 255, 159, 65, 64, 210, 97, 211, 65, 44, 94, 211, 65, 65, 96, 255, 63, 44, 158, 43, 190, 210, 97, 210, 193),
"format": 34896613399,
"index_count": 900,
"index_data": PackedByteArray(112, 0, 66, 0, 22, 0, 112, 0, 162, 0, 66, 0, 164, 0, 86, 0, 135, 0, 164, 0, 110, 0, 86, 0, 65, 0, 136, 0, 45, 0, 65, 0, 163, 0, 136, 0, 1, 0, 137, 0, 87, 0, 1, 0, 44, 0, 137, 0, 24, 0, 43, 0, 2, 0, 24, 0, 64, 0, 43, 0, 0, 0, 7, 0, 3, 0, 0, 0, 6, 0, 7, 0, 3, 0, 8, 0, 4, 0, 3, 0, 7, 0, 8, 0, 6, 0, 21, 0, 7, 0, 6, 0, 18, 0, 21, 0, 7, 0, 10, 0, 8, 0, 7, 0, 21, 0, 10, 0, 1, 0, 14, 0, 11, 0, 1, 0, 13, 0, 14, 0, 11, 0, 16, 0, 12, 0, 11, 0, 14, 0, 16, 0, 13, 0, 9, 0, 14, 0, 13, 0, 5, 0, 9, 0, 14, 0, 10, 0, 16, 0, 14, 0, 9, 0, 10, 0, 2, 0, 20, 0, 17, 0, 2, 0, 19, 0, 20, 0, 17, 0, 21, 0, 18, 0, 17, 0, 20, 0, 21, 0, 19, 0, 15, 0, 20, 0, 19, 0, 12, 0, 15, 0, 20, 0, 10, 0, 21, 0, 20, 0, 15, 0, 10, 0, 22, 0, 29, 0, 25, 0, 22, 0, 28, 0, 29, 0, 25, 0, 30, 0, 26, 0, 25, 0, 29, 0, 30, 0, 28, 0, 42, 0, 29, 0, 28, 0, 39, 0, 42, 0, 29, 0, 32, 0, 30, 0, 29, 0, 42, 0, 32, 0, 23, 0, 36, 0, 33, 0, 23, 0, 35, 0, 36, 0, 33, 0, 37, 0, 34, 0, 33, 0, 36, 0, 37, 0, 35, 0, 31, 0, 36, 0, 35, 0, 27, 0, 31, 0, 36, 0, 32, 0, 37, 0, 36, 0, 31, 0, 32, 0, 24, 0, 41, 0, 38, 0, 24, 0, 40, 0, 41, 0, 38, 0, 42, 0, 39, 0, 38, 0, 41, 0, 42, 0, 40, 0, 37, 0, 41, 0, 40, 0, 34, 0, 37, 0, 41, 0, 32, 0, 42, 0, 41, 0, 37, 0, 32, 0, 43, 0, 49, 0, 46, 0, 43, 0, 48, 0, 49, 0, 46, 0, 50, 0, 47, 0, 46, 0, 49, 0, 50, 0, 48, 0, 63, 0, 49, 0, 48, 0, 60, 0, 63, 0, 49, 0, 51, 0, 50, 0, 49, 0, 63, 0, 51, 0, 44, 0, 56, 0, 52, 0, 44, 0, 55, 0, 56, 0, 52, 0, 58, 0, 54, 0, 52, 0, 56, 0, 58, 0, 55, 0, 50, 0, 56, 0, 55, 0, 47, 0, 50, 0, 56, 0, 51, 0, 58, 0, 56, 0, 50, 0, 51, 0, 45, 0, 62, 0, 59, 0, 45, 0, 61, 0, 62, 0, 59, 0, 63, 0, 60, 0, 59, 0, 62, 0, 63, 0, 61, 0, 57, 0, 62, 0, 61, 0, 53, 0, 57, 0, 62, 0, 51, 0, 63, 0, 62, 0, 57, 0, 51, 0, 64, 0, 70, 0, 67, 0, 64, 0, 69, 0, 70, 0, 67, 0, 71, 0, 68, 0, 67, 0, 70, 0, 71, 0, 69, 0, 84, 0, 70, 0, 69, 0, 81, 0, 84, 0, 70, 0, 72, 0, 71, 0, 70, 0, 84, 0, 72, 0, 65, 0, 77, 0, 73, 0, 65, 0, 76, 0, 77, 0, 73, 0, 79, 0, 75, 0, 73, 0, 77, 0, 79, 0, 76, 0, 71, 0, 77, 0, 76, 0, 68, 0, 71, 0, 77, 0, 72, 0, 79, 0, 77, 0, 71, 0, 72, 0, 66, 0, 83, 0, 80, 0, 66, 0, 82, 0, 83, 0, 80, 0, 84, 0, 81, 0, 80, 0, 83, 0, 84, 0, 82, 0, 78, 0, 83, 0, 82, 0, 74, 0, 78, 0, 83, 0, 72, 0, 84, 0, 83, 0, 78, 0, 72, 0, 85, 0, 91, 0, 88, 0, 85, 0, 90, 0, 91, 0, 88, 0, 92, 0, 89, 0, 88, 0, 91, 0, 92, 0, 90, 0, 108, 0, 91, 0, 90, 0, 104, 0, 108, 0, 91, 0, 93, 0, 92, 0, 91, 0, 108, 0, 93, 0, 86, 0, 100, 0, 96, 0, 86, 0, 99, 0, 100, 0, 96, 0, 101, 0, 97, 0, 96, 0, 100, 0, 101, 0, 99, 0, 92, 0, 100, 0, 99, 0, 89, 0, 92, 0, 100, 0, 94, 0, 101, 0, 100, 0, 92, 0, 94, 0, 87, 0, 107, 0, 103, 0, 87, 0, 106, 0, 107, 0, 103, 0, 109, 0, 105, 0, 103, 0, 107, 0, 109, 0, 106, 0, 102, 0, 107, 0, 106, 0, 98, 0, 102, 0, 107, 0, 95, 0, 109, 0, 107, 0, 102, 0, 95, 0, 110, 0, 116, 0, 113, 0, 110, 0, 115, 0, 116, 0, 113, 0, 117, 0, 114, 0, 113, 0, 116, 0, 117, 0, 115, 0, 134, 0, 116, 0, 115, 0, 130, 0, 134, 0, 116, 0, 120, 0, 117, 0, 116, 0, 134, 0, 120, 0, 111, 0, 125, 0, 121, 0, 111, 0, 124, 0, 125, 0, 121, 0, 127, 0, 123, 0, 121, 0, 125, 0, 127, 0, 124, 0, 117, 0, 125, 0, 124, 0, 114, 0, 117, 0, 125, 0, 119, 0, 127, 0, 125, 0, 117, 0, 119, 0, 112, 0, 132, 0, 128, 0, 112, 0, 131, 0, 132, 0, 128, 0, 133, 0, 129, 0, 128, 0, 132, 0, 133, 0, 131, 0, 126, 0, 132, 0, 131, 0, 122, 0, 126, 0, 132, 0, 118, 0, 133, 0, 132, 0, 126, 0, 118, 0, 135, 0, 142, 0, 138, 0, 135, 0, 141, 0, 142, 0, 138, 0, 144, 0, 140, 0, 138, 0, 142, 0, 144, 0, 141, 0, 160, 0, 142, 0, 141, 0, 156, 0, 160, 0, 142, 0, 145, 0, 144, 0, 142, 0, 160, 0, 145, 0, 136, 0, 152, 0, 148, 0, 136, 0, 151, 0, 152, 0, 148, 0, 153, 0, 149, 0, 148, 0, 152, 0, 153, 0, 151, 0, 143, 0, 152, 0, 151, 0, 139, 0, 143, 0, 152, 0, 146, 0, 153, 0, 152, 0, 143, 0, 146, 0, 137, 0, 159, 0, 155, 0, 137, 0, 158, 0, 159, 0, 155, 0, 161, 0, 157, 0, 155, 0, 159, 0, 161, 0, 158, 0, 154, 0, 159, 0, 158, 0, 150, 0, 154, 0, 159, 0, 147, 0, 161, 0, 159, 0, 154, 0, 147, 0, 162, 0, 169, 0, 165, 0, 162, 0, 168, 0, 169, 0, 165, 0, 170, 0, 166, 0, 165, 0, 169, 0, 170, 0, 168, 0, 187, 0, 169, 0, 168, 0, 183, 0, 187, 0, 169, 0, 172, 0, 170, 0, 169, 0, 187, 0, 172, 0, 163, 0, 179, 0, 175, 0, 163, 0, 178, 0, 179, 0, 175, 0, 181, 0, 176, 0, 175, 0, 179, 0, 181, 0, 178, 0, 171, 0, 179, 0, 178, 0, 167, 0, 171, 0, 179, 0, 174, 0, 181, 0, 179, 0, 171, 0, 174, 0, 164, 0, 186, 0, 182, 0, 164, 0, 185, 0, 186, 0, 182, 0, 188, 0, 184, 0, 182, 0, 186, 0, 188, 0, 185, 0, 180, 0, 186, 0, 185, 0, 177, 0, 180, 0, 186, 0, 173, 0, 188, 0, 186, 0, 180, 0, 173, 0, 112, 0, 168, 0, 162, 0, 112, 0, 128, 0, 168, 0, 128, 0, 183, 0, 168, 0, 128, 0, 129, 0, 183, 0, 130, 0, 182, 0, 184, 0, 130, 0, 115, 0, 182, 0, 115, 0, 164, 0, 182, 0, 115, 0, 110, 0, 164, 0, 22, 0, 131, 0, 112, 0, 22, 0, 25, 0, 131, 0, 25, 0, 122, 0, 131, 0, 25, 0, 26, 0, 122, 0, 27, 0, 121, 0, 123, 0, 27, 0, 35, 0, 121, 0, 35, 0, 111, 0, 121, 0, 35, 0, 23, 0, 111, 0, 2, 0, 40, 0, 24, 0, 2, 0, 17, 0, 40, 0, 17, 0, 34, 0, 40, 0, 17, 0, 18, 0, 34, 0, 18, 0, 33, 0, 34, 0, 18, 0, 6, 0, 33, 0, 6, 0, 23, 0, 33, 0, 6, 0, 0, 0, 23, 0, 163, 0, 151, 0, 136, 0, 163, 0, 175, 0, 151, 0, 175, 0, 139, 0, 151, 0, 175, 0, 176, 0, 139, 0, 177, 0, 138, 0, 140, 0, 177, 0, 185, 0, 138, 0, 185, 0, 135, 0, 138, 0, 185, 0, 164, 0, 135, 0, 45, 0, 76, 0, 65, 0, 45, 0, 59, 0, 76, 0, 59, 0, 68, 0, 76, 0, 59, 0, 60, 0, 68, 0, 60, 0, 67, 0, 68, 0, 60, 0, 48, 0, 67, 0, 48, 0, 64, 0, 67, 0, 48, 0, 43, 0, 64, 0, 85, 0, 124, 0, 111, 0, 85, 0, 88, 0, 124, 0, 88, 0, 114, 0, 124, 0, 88, 0, 89, 0, 114, 0, 89, 0, 113, 0, 114, 0, 89, 0, 99, 0, 113, 0, 99, 0, 110, 0, 113, 0, 99, 0, 86, 0, 110, 0, 44, 0, 158, 0, 137, 0, 44, 0, 52, 0, 158, 0, 52, 0, 150, 0, 158, 0, 52, 0, 54, 0, 150, 0, 53, 0, 148, 0, 149, 0, 53, 0, 61, 0, 148, 0, 61, 0, 136, 0, 148, 0, 61, 0, 45, 0, 136, 0, 1, 0, 55, 0, 44, 0, 1, 0, 11, 0, 55, 0, 11, 0, 47, 0, 55, 0, 11, 0, 12, 0, 47, 0, 12, 0, 46, 0, 47, 0, 12, 0, 19, 0, 46, 0, 19, 0, 43, 0, 46, 0, 19, 0, 2, 0, 43, 0, 162, 0, 82, 0, 66, 0, 162, 0, 165, 0, 82, 0, 165, 0, 74, 0, 82, 0, 165, 0, 166, 0, 74, 0, 167, 0, 73, 0, 75, 0, 167, 0, 178, 0, 73, 0, 178, 0, 65, 0, 73, 0, 178, 0, 163, 0, 65, 0, 137, 0, 106, 0, 87, 0, 137, 0, 155, 0, 106, 0, 155, 0, 98, 0, 106, 0, 155, 0, 157, 0, 98, 0, 156, 0, 96, 0, 97, 0, 156, 0, 141, 0, 96, 0, 141, 0, 86, 0, 96, 0, 141, 0, 135, 0, 86, 0, 87, 0, 13, 0, 1, 0, 87, 0, 103, 0, 13, 0, 103, 0, 5, 0, 13, 0, 103, 0, 105, 0, 5, 0, 104, 0, 3, 0, 4, 0, 104, 0, 90, 0, 3, 0, 90, 0, 0, 0, 3, 0, 90, 0, 85, 0, 0, 0, 66, 0, 28, 0, 22, 0, 66, 0, 80, 0, 28, 0, 80, 0, 39, 0, 28, 0, 80, 0, 81, 0, 39, 0, 81, 0, 38, 0, 39, 0, 81, 0, 69, 0, 38, 0, 69, 0, 24, 0, 38, 0, 69, 0, 64, 0, 24, 0, 111, 0, 0, 0, 85, 0, 111, 0, 23, 0, 0, 0),
"lods": [0.0179063, PackedByteArray(112, 0, 8, 1, 22, 0, 22, 0, 26, 0, 112, 0, 26, 0, 32, 0, 30, 0, 22, 0, 2, 1, 26, 0, 26, 0, 122, 0, 112, 0, 22, 0, 1, 1, 2, 1, 8, 1, 1, 1, 22, 0, 112, 0, 122, 0, 126, 0, 112, 0, 126, 0, 133, 0, 133, 0, 126, 0, 118, 0, 112, 0, 133, 0, 129, 0, 112, 0, 129, 0, 162, 0, 112, 0, 162, 0, 8, 1, 129, 0, 183, 0, 162, 0, 162, 0, 183, 0, 187, 0, 162, 0, 187, 0, 166, 0, 162, 0, 166, 0, 8, 1, 166, 0, 187, 0, 170, 0, 187, 0, 172, 0, 170, 0, 166, 0, 74, 0, 8, 1, 8, 1, 74, 0, 78, 0, 78, 0, 9, 1, 8, 1, 7, 1, 8, 1, 9, 1, 8, 1, 7, 1, 1, 1, 65, 0, 7, 1, 9, 1, 65, 0, 9, 1, 75, 0, 75, 0, 72, 0, 79, 0, 163, 0, 65, 0, 75, 0, 167, 0, 163, 0, 75, 0, 163, 0, 167, 0, 171, 0, 163, 0, 171, 0, 181, 0, 181, 0, 171, 0, 174, 0, 163, 0, 181, 0, 176, 0, 163, 0, 176, 0, 136, 0, 65, 0, 163, 0, 136, 0, 176, 0, 139, 0, 136, 0, 136, 0, 139, 0, 143, 0, 136, 0, 143, 0, 153, 0, 153, 0, 143, 0, 146, 0, 136, 0, 153, 0, 149, 0, 65, 0, 136, 0, 4, 1, 4, 1, 136, 0, 149, 0, 53, 0, 4, 1, 149, 0, 4, 1, 53, 0, 57, 0, 4, 1, 7, 1, 65, 0, 57, 0, 5, 1, 4, 1, 4, 1, 3, 1, 7, 1, 3, 1, 4, 1, 5, 1, 1, 1, 7, 1, 3, 1, 44, 0, 3, 1, 5, 1, 1, 1, 3, 1, 254, 0, 254, 0, 0, 1, 1, 1, 253, 0, 3, 1, 44, 0, 253, 0, 254, 0, 3, 1, 44, 0, 6, 1, 54, 0, 54, 0, 51, 0, 58, 0, 44, 0, 54, 0, 137, 0, 253, 0, 44, 0, 137, 0, 54, 0, 150, 0, 137, 0, 137, 0, 150, 0, 154, 0, 137, 0, 154, 0, 161, 0, 161, 0, 154, 0, 147, 0, 137, 0, 161, 0, 157, 0, 137, 0, 157, 0, 87, 0, 253, 0, 137, 0, 87, 0, 157, 0, 98, 0, 87, 0, 87, 0, 98, 0, 102, 0, 87, 0, 102, 0, 109, 0, 109, 0, 102, 0, 95, 0, 87, 0, 109, 0, 105, 0, 87, 0, 105, 0, 253, 0, 105, 0, 5, 0, 253, 0, 253, 0, 5, 0, 9, 0, 253, 0, 9, 0, 255, 0, 254, 0, 253, 0, 255, 0, 0, 0, 254, 0, 255, 0, 0, 0, 255, 0, 4, 0, 254, 0, 0, 0, 23, 0, 4, 0, 10, 0, 8, 0, 10, 1, 0, 0, 4, 0, 104, 0, 10, 1, 4, 0, 10, 1, 104, 0, 108, 0, 111, 0, 0, 0, 10, 1, 111, 0, 23, 0, 0, 0, 10, 1, 108, 0, 92, 0, 108, 0, 93, 0, 92, 0, 86, 0, 10, 1, 92, 0, 101, 0, 92, 0, 94, 0, 86, 0, 92, 0, 101, 0, 86, 0, 101, 0, 97, 0, 10, 1, 86, 0, 11, 1, 10, 1, 11, 1, 111, 0, 135, 0, 86, 0, 97, 0, 156, 0, 135, 0, 97, 0, 135, 0, 156, 0, 160, 0, 160, 0, 145, 0, 144, 0, 140, 0, 160, 0, 144, 0, 135, 0, 160, 0, 140, 0, 164, 0, 86, 0, 135, 0, 164, 0, 135, 0, 140, 0, 164, 0, 11, 1, 86, 0, 177, 0, 164, 0, 140, 0, 164, 0, 177, 0, 180, 0, 164, 0, 180, 0, 188, 0, 188, 0, 180, 0, 173, 0, 164, 0, 188, 0, 184, 0, 11, 1, 164, 0, 184, 0, 130, 0, 11, 1, 184, 0, 11, 1, 130, 0, 134, 0, 11, 1, 134, 0, 117, 0, 134, 0, 120, 0, 117, 0, 111, 0, 11, 1, 117, 0, 111, 0, 117, 0, 127, 0, 127, 0, 117, 0, 119, 0, 111, 0, 127, 0, 123, 0, 23, 0, 111, 0, 123, 0, 27, 0, 23, 0, 123, 0, 23, 0, 27, 0, 31, 0, 23, 0, 31, 0, 2, 1, 1, 1, 23, 0, 2, 1), 0.0210464, PackedByteArray(246, 0, 66, 0, 233, 0, 129, 0, 246, 0, 118, 0, 246, 0, 129, 0, 252, 0, 246, 0, 252, 0, 66, 0, 252, 0, 172, 0, 166, 0, 252, 0, 166, 0, 66, 0, 166, 0, 74, 0, 66, 0, 74, 0, 239, 0, 66, 0, 66, 0, 230, 0, 233, 0, 66, 0, 240, 0, 230, 0, 230, 0, 238, 0, 234, 0, 230, 0, 234, 0, 227, 0, 227, 0, 232, 0, 230, 0, 237, 0, 236, 0, 239, 0, 237, 0, 239, 0, 65, 0, 65, 0, 239, 0, 75, 0, 250, 0, 65, 0, 75, 0, 65, 0, 247, 0, 237, 0, 65, 0, 250, 0, 247, 0, 237, 0, 247, 0, 149, 0, 149, 0, 247, 0, 146, 0, 250, 0, 176, 0, 247, 0, 176, 0, 250, 0, 174, 0, 227, 0, 228, 0, 231, 0, 111, 0, 231, 0, 228, 0, 111, 0, 228, 0, 242, 0, 231, 0, 111, 0, 123, 0, 111, 0, 245, 0, 123, 0, 241, 0, 245, 0, 111, 0, 123, 0, 244, 0, 119, 0, 104, 0, 93, 0, 241, 0, 104, 0, 241, 0, 4, 0, 4, 0, 2, 0, 10, 0, 97, 0, 241, 0, 94, 0, 86, 0, 241, 0, 97, 0, 241, 0, 86, 0, 244, 0, 249, 0, 86, 0, 97, 0, 130, 0, 120, 0, 244, 0, 130, 0, 244, 0, 184, 0, 251, 0, 244, 0, 86, 0, 244, 0, 251, 0, 184, 0, 251, 0, 86, 0, 249, 0, 184, 0, 251, 0, 173, 0, 251, 0, 249, 0, 140, 0, 249, 0, 145, 0, 140, 0, 1, 0, 248, 0, 243, 0, 248, 0, 157, 0, 243, 0, 157, 0, 248, 0, 147, 0, 243, 0, 105, 0, 1, 0, 105, 0, 243, 0, 95, 0, 105, 0, 5, 0, 1, 0, 1, 0, 5, 0, 229, 0, 227, 0, 1, 0, 229, 0, 1, 0, 227, 0, 235, 0, 1, 0, 44, 0, 248, 0, 1, 0, 235, 0, 44, 0, 44, 0, 54, 0, 248, 0, 44, 0, 235, 0, 54, 0, 24, 0, 27, 0, 32, 0, 32, 0, 26, 0, 122, 0), 0.215511, PackedByteArray(193, 0, 206, 0, 195, 0, 206, 0, 205, 0, 195, 0, 193, 0, 226, 0, 206, 0, 226, 0, 222, 0, 206, 0, 226, 0, 193, 0, 216, 0, 225, 0, 214, 0, 212, 0, 209, 0, 212, 0, 214, 0, 212, 0, 219, 0, 225, 0, 214, 0, 225, 0, 184, 0, 184, 0, 225, 0, 173, 0, 184, 0, 218, 0, 214, 0, 212, 0, 208, 0, 94, 0, 4, 0, 210, 0, 208, 0, 92, 0, 10, 0, 4, 0, 208, 0, 215, 0, 194, 0, 194, 0, 215, 0, 217, 0, 194, 0, 191, 0, 208, 0, 191, 0, 194, 0, 197, 0, 207, 0, 224, 0, 201, 0, 224, 0, 207, 0, 223, 0, 201, 0, 224, 0, 220, 0, 201, 0, 204, 0, 207, 0, 201, 0, 199, 0, 204, 0, 189, 0, 202, 0, 213, 0, 213, 0, 202, 0, 221, 0, 189, 0, 213, 0, 211, 0, 189, 0, 200, 0, 202, 0, 189, 0, 192, 0, 200, 0, 196, 0, 198, 0, 190, 0, 196, 0, 203, 0, 198, 0)],
"material": SubResource("StandardMaterial3D_hl3uc"),
"name": "Material",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 268,
"vertex_data": PackedByteArray(177, 248, 177, 248, 0, 0, 251, 255, 177, 248, 255, 255, 77, 7, 60, 253, 255, 255, 177, 248, 77, 7, 253, 217, 177, 248, 125, 251, 142, 0, 149, 247, 177, 248, 219, 253, 35, 2, 116, 236, 177, 248, 219, 253, 35, 2, 136, 252, 125, 251, 177, 248, 142, 0, 190, 247, 143, 251, 143, 251, 58, 1, 165, 255, 105, 251, 124, 253, 130, 2, 141, 244, 105, 251, 124, 253, 130, 2, 227, 243, 232, 252, 232, 252, 22, 3, 26, 251, 125, 251, 112, 255, 77, 7, 83, 245, 219, 253, 219, 253, 77, 7, 190, 234, 177, 248, 112, 255, 129, 4, 49, 253, 143, 251, 196, 254, 111, 4, 176, 245, 124, 253, 124, 253, 149, 4, 182, 239, 124, 253, 124, 253, 149, 4, 8, 236, 112, 255, 177, 248, 129, 4, 53, 225, 219, 253, 177, 248, 35, 2, 57, 236, 112, 255, 125, 251, 77, 7, 207, 224, 196, 254, 143, 251, 111, 4, 78, 231, 124, 253, 105, 251, 130, 2, 65, 243, 177, 248, 0, 0, 77, 7, 48, 196, 177, 248, 77, 7, 0, 0, 49, 250, 255, 255, 77, 7, 77, 7, 142, 213, 177, 248, 142, 0, 129, 4, 242, 207, 177, 248, 35, 2, 35, 2, 8, 224, 177, 248, 35, 2, 35, 2, 243, 230, 125, 251, 142, 0, 77, 7, 102, 197, 143, 251, 58, 1, 111, 4, 132, 208, 105, 251, 130, 2, 130, 2, 80, 223, 105, 251, 130, 2, 130, 2, 228, 223, 232, 252, 22, 3, 22, 3, 248, 218, 125, 251, 77, 7, 142, 0, 223, 241, 219, 253, 77, 7, 35, 2, 34, 231, 177, 248, 129, 4, 142, 0, 248, 241, 143, 251, 111, 4, 58, 1, 189, 233, 124, 253, 149, 4, 130, 2, 122, 225, 112, 255, 129, 4, 77, 7, 191, 207, 219, 253, 35, 2, 77, 7, 61, 201, 112, 255, 77, 7, 129, 4, 230, 220, 196, 254, 111, 4, 111, 4, 254, 215, 124, 253, 130, 2, 149, 4, 52, 210, 255, 255, 177, 248, 177, 248, 148, 213, 177, 248, 255, 255, 177, 248, 241, 252, 177, 248, 177, 248, 255, 255, 255, 191, 112, 255, 125, 251, 177, 248, 92, 221, 219, 253, 219, 253, 177, 248, 162, 232, 112, 255, 177, 248, 125, 251, 131, 207, 196, 254, 143, 251, 143, 251, 44, 217, 124, 253, 124, 253, 105, 251, 22, 230, 232, 252, 232, 252, 232, 252, 79, 221, 177, 248, 112, 255, 125, 251, 252, 252, 177, 248, 219, 253, 219, 253, 89, 197, 177, 248, 219, 253, 219, 253, 143, 254, 125, 251, 112, 255, 177, 248, 61, 244, 143, 251, 196, 254, 143, 251, 184, 242, 105, 251, 124, 253, 124, 253, 215, 196, 105, 251, 124, 253, 124, 253, 146, 243, 125, 251, 177, 248, 112, 255, 123, 193, 219, 253, 177, 248, 219, 253, 99, 199, 177, 248, 125, 251, 112, 255, 225, 192, 143, 251, 143, 251, 196, 254, 223, 194, 124, 253, 105, 251, 124, 253, 185, 208, 255, 255, 77, 7, 177, 248, 130, 208, 177, 248, 77, 7, 255, 255, 104, 192, 177, 248, 0, 0, 177, 248, 3, 188, 112, 255, 77, 7, 125, 251, 65, 202, 219, 253, 77, 7, 219, 253, 182, 196, 112, 255, 129, 4, 177, 248, 243, 201, 196, 254, 111, 4, 143, 251, 21, 195, 124, 253, 149, 4, 124, 253, 249, 193, 232, 252, 22, 3, 232, 252, 244, 183, 177, 248, 129, 4, 112, 255, 95, 194, 177, 248, 35, 2, 219, 253, 52, 160, 177, 248, 35, 2, 219, 253, 190, 198, 125, 251, 77, 7, 112, 255, 196, 193, 143, 251, 111, 4, 196, 254, 45, 196, 105, 251, 130, 2, 124, 253, 23, 163, 105, 251, 130, 2, 124, 253, 229, 200, 125, 251, 142, 0, 177, 248, 174, 189, 219, 253, 35, 2, 177, 248, 196, 194, 177, 248, 142, 0, 125, 251, 69, 176, 143, 251, 58, 1, 143, 251, 146, 178, 124, 253, 130, 2, 105, 251, 77, 186, 77, 7, 177, 248, 0, 0, 49, 250, 0, 0, 177, 248, 77, 7, 142, 213, 77, 7, 255, 255, 77, 7, 60, 253, 129, 4, 177, 248, 142, 0, 223, 241, 35, 2, 177, 248, 35, 2, 34, 231, 77, 7, 125, 251, 142, 0, 248, 241, 111, 4, 143, 251, 58, 1, 190, 233, 130, 2, 105, 251, 130, 2, 204, 225, 22, 3, 232, 252, 22, 3, 25, 217, 22, 3, 232, 252, 22, 3, 42, 221, 22, 3, 232, 252, 22, 3, 233, 235, 142, 0, 125, 251, 77, 7, 191, 207, 35, 2, 219, 253, 77, 7, 255, 200, 35, 2, 219, 253, 77, 7, 67, 234, 142, 0, 177, 248, 129, 4, 230, 220, 58, 1, 143, 251, 111, 4, 222, 216, 130, 2, 124, 253, 149, 4, 149, 209, 130, 2, 124, 253, 149, 4, 8, 236, 77, 7, 112, 255, 129, 4, 49, 253, 77, 7, 219, 253, 35, 2, 243, 230, 77, 7, 219, 253, 35, 2, 136, 252, 129, 4, 112, 255, 77, 7, 83, 245, 111, 4, 196, 254, 111, 4, 176, 245, 149, 4, 124, 253, 130, 2, 231, 223, 149, 4, 124, 253, 130, 2, 227, 243, 0, 0, 77, 7, 77, 7, 253, 217, 77, 7, 77, 7, 0, 0, 251, 255, 77, 7, 0, 0, 77, 7, 48, 196, 142, 0, 77, 7, 129, 4, 53, 225, 35, 2, 77, 7, 35, 2, 57, 236, 142, 0, 129, 4, 77, 7, 207, 224, 58, 1, 111, 4, 111, 4, 78, 231, 130, 2, 149, 4, 130, 2, 23, 246, 22, 3, 22, 3, 22, 3, 114, 219, 22, 3, 22, 3, 22, 3, 23, 249, 22, 3, 22, 3, 22, 3, 150, 247, 77, 7, 129, 4, 142, 0, 149, 247, 77, 7, 35, 2, 35, 2, 8, 224, 77, 7, 35, 2, 35, 2, 116, 236, 129, 4, 77, 7, 142, 0, 190, 247, 111, 4, 111, 4, 58, 1, 151, 254, 149, 4, 130, 2, 130, 2, 81, 222, 149, 4, 130, 2, 130, 2, 51, 244, 129, 4, 142, 0, 77, 7, 102, 197, 35, 2, 35, 2, 77, 7, 124, 201, 35, 2, 35, 2, 77, 7, 57, 235, 77, 7, 142, 0, 129, 4, 242, 207, 111, 4, 58, 1, 111, 4, 50, 208, 130, 2, 130, 2, 149, 4, 222, 210, 130, 2, 130, 2, 149, 4, 182, 239, 0, 0, 177, 248, 177, 248, 130, 208, 77, 7, 177, 248, 255, 255, 104, 192, 77, 7, 255, 255, 177, 248, 241, 252, 142, 0, 177, 248, 125, 251, 65, 202, 35, 2, 177, 248, 219, 253, 182, 196, 35, 2, 177, 248, 219, 253, 182, 196, 142, 0, 125, 251, 177, 248, 243, 201, 58, 1, 143, 251, 143, 251, 21, 195, 130, 2, 105, 251, 124, 253, 155, 208, 130, 2, 105, 251, 124, 253, 104, 192, 22, 3, 232, 252, 232, 252, 143, 186, 22, 3, 232, 252, 232, 252, 44, 213, 22, 3, 232, 252, 232, 252, 229, 223, 77, 7, 125, 251, 112, 255, 95, 194, 77, 7, 219, 253, 219, 253, 190, 198, 77, 7, 219, 253, 219, 253, 143, 254, 129, 4, 177, 248, 112, 255, 197, 193, 111, 4, 143, 251, 196, 254, 211, 199, 149, 4, 124, 253, 124, 253, 170, 201, 149, 4, 124, 253, 124, 253, 14, 235, 129, 4, 112, 255, 177, 248, 61, 244, 35, 2, 219, 253, 177, 248, 15, 195, 35, 2, 219, 253, 177, 248, 42, 233, 77, 7, 112, 255, 125, 251, 252, 252, 111, 4, 196, 254, 143, 251, 176, 240, 130, 2, 124, 253, 105, 251, 247, 187, 130, 2, 124, 253, 105, 251, 12, 232, 77, 7, 0, 0, 177, 248, 3, 188, 77, 7, 77, 7, 255, 255, 255, 191, 0, 0, 77, 7, 177, 248, 148, 213, 77, 7, 142, 0, 125, 251, 69, 176, 77, 7, 35, 2, 219, 253, 52, 160, 77, 7, 35, 2, 219, 253, 89, 197, 129, 4, 142, 0, 177, 248, 174, 189, 111, 4, 58, 1, 143, 251, 146, 178, 149, 4, 130, 2, 124, 253, 23, 163, 149, 4, 130, 2, 124, 253, 197, 196, 22, 3, 22, 3, 232, 252, 191, 170, 22, 3, 22, 3, 232, 252, 154, 231, 22, 3, 22, 3, 232, 252, 11, 203, 129, 4, 77, 7, 112, 255, 123, 193, 35, 2, 77, 7, 219, 253, 99, 199, 35, 2, 77, 7, 219, 253, 99, 199, 77, 7, 129, 4, 112, 255, 225, 192, 111, 4, 111, 4, 196, 254, 193, 193, 130, 2, 149, 4, 124, 253, 218, 218, 130, 2, 149, 4, 124, 253, 36, 202, 142, 0, 129, 4, 177, 248, 92, 221, 35, 2, 35, 2, 177, 248, 123, 194, 35, 2, 35, 2, 177, 248, 27, 232, 142, 0, 77, 7, 125, 251, 131, 207, 58, 1, 111, 4, 143, 251, 24, 219, 130, 2, 130, 2, 105, 251, 195, 184, 130, 2, 130, 2, 105, 251, 34, 228, 177, 248, 219, 253, 35, 2, 28, 251, 232, 252, 232, 252, 22, 3, 223, 241, 232, 252, 232, 252, 22, 3, 255, 255, 232, 252, 232, 252, 22, 3, 31, 232, 177, 248, 35, 2, 35, 2, 151, 193, 177, 248, 35, 2, 35, 2, 49, 242, 232, 252, 22, 3, 22, 3, 50, 199, 232, 252, 22, 3, 22, 3, 216, 196, 232, 252, 22, 3, 22, 3, 160, 235, 232, 252, 232, 252, 232, 252, 137, 224, 232, 252, 232, 252, 232, 252, 218, 219, 232, 252, 232, 252, 232, 252, 19, 228, 177, 248, 219, 253, 219, 253, 157, 189, 177, 248, 219, 253, 219, 253, 255, 255, 232, 252, 22, 3, 232, 252, 199, 203, 232, 252, 22, 3, 232, 252, 253, 170, 232, 252, 22, 3, 232, 252, 62, 200, 177, 248, 35, 2, 219, 253, 119, 185, 177, 248, 35, 2, 219, 253, 211, 193, 130, 2, 105, 251, 130, 2, 131, 241, 130, 2, 105, 251, 130, 2, 182, 219, 22, 3, 232, 252, 22, 3, 91, 233, 22, 3, 232, 252, 22, 3, 233, 237, 35, 2, 219, 253, 77, 7, 144, 205, 35, 2, 219, 253, 77, 7, 227, 233, 130, 2, 149, 4, 130, 2, 157, 229, 130, 2, 149, 4, 130, 2, 167, 248, 22, 3, 22, 3, 22, 3, 164, 200, 22, 3, 22, 3, 22, 3, 232, 236, 22, 3, 22, 3, 22, 3, 12, 236, 22, 3, 232, 252, 232, 252, 156, 206, 22, 3, 232, 252, 232, 252, 89, 197, 22, 3, 232, 252, 232, 252, 108, 231, 22, 3, 22, 3, 232, 252, 214, 176, 22, 3, 22, 3, 232, 252, 193, 204, 35, 2, 77, 7, 219, 253, 223, 194, 35, 2, 77, 7, 219, 253, 193, 209, 35, 2, 35, 2, 177, 248, 168, 191, 255, 255, 177, 248, 77, 7, 221, 222, 177, 248, 219, 253, 35, 2, 255, 255, 232, 252, 232, 252, 22, 3, 13, 240, 255, 255, 77, 7, 77, 7, 16, 213, 177, 248, 35, 2, 35, 2, 150, 237, 177, 248, 35, 2, 35, 2, 127, 211, 232, 252, 22, 3, 22, 3, 6, 200, 232, 252, 232, 252, 232, 252, 113, 224, 232, 252, 232, 252, 232, 252, 87, 226, 232, 252, 232, 252, 232, 252, 219, 219, 177, 248, 219, 253, 219, 253, 2, 190, 232, 252, 22, 3, 232, 252, 161, 203, 232, 252, 22, 3, 232, 252, 69, 174, 232, 252, 22, 3, 232, 252, 149, 200, 130, 2, 105, 251, 130, 2, 141, 226, 130, 2, 105, 251, 130, 2, 169, 252, 35, 2, 219, 253, 77, 7, 168, 234, 130, 2, 149, 4, 130, 2, 175, 233, 130, 2, 149, 4, 130, 2, 255, 255, 77, 7, 35, 2, 35, 2, 183, 198, 35, 2, 177, 248, 219, 253, 150, 190, 77, 7, 219, 253, 219, 253, 255, 255, 35, 2, 219, 253, 177, 248, 67, 190, 77, 7, 35, 2, 219, 253, 148, 190, 35, 2, 77, 7, 219, 253, 58, 208, 35, 2, 35, 2, 177, 248, 19, 192, 177, 248, 255, 255, 77, 7, 109, 252, 255, 255, 177, 248, 77, 7, 145, 223, 232, 252, 232, 252, 22, 3, 4, 250, 177, 248, 77, 7, 0, 0, 144, 225, 255, 255, 77, 7, 77, 7, 152, 214, 232, 252, 22, 3, 22, 3, 15, 218, 255, 255, 177, 248, 177, 248, 35, 211, 177, 248, 177, 248, 255, 255, 205, 192, 232, 252, 232, 252, 232, 252, 210, 220, 232, 252, 232, 252, 232, 252, 226, 224, 255, 255, 77, 7, 177, 248, 238, 202, 177, 248, 0, 0, 177, 248, 76, 184, 232, 252, 22, 3, 232, 252, 148, 182, 77, 7, 177, 248, 0, 0, 195, 242, 0, 0, 77, 7, 77, 7, 180, 222, 20, 189, 196, 188, 122, 65, 79, 3, 149, 45, 50, 41, 127, 185, 105, 184, 120, 180, 112, 179, 77, 89, 180, 1, 207, 56, 5, 57, 69, 182, 143, 180, 206, 178, 217, 175, 140, 84, 6, 8, 4, 53, 174, 40, 214, 60, 8, 12, 6, 55, 202, 22, 164, 74, 82, 3, 251, 68, 138, 12, 86, 58, 9, 27, 121, 62, 231, 22, 77, 49, 25, 45, 4, 53, 8, 51, 175, 49, 70, 33, 121, 53, 5, 37, 4, 53, 244, 45, 231, 5, 192, 116, 43, 64, 216, 63, 131, 42, 11, 47, 63, 5, 221, 117, 74, 2, 43, 120, 99, 74, 80, 73, 97, 18, 237, 92, 14, 19, 66, 95, 149, 16, 76, 98, 235, 71, 146, 68, 145, 39, 86, 75, 193, 59, 137, 59, 4, 53, 20, 55, 255, 68, 220, 67, 22, 64, 240, 63, 16, 48, 255, 63, 168, 37, 58, 56, 150, 29, 109, 71, 133, 46, 219, 50, 100, 40, 151, 60, 16, 31, 255, 74, 135, 42, 111, 38, 90, 59, 0, 3, 61, 244, 7, 0, 43, 46, 229, 30, 250, 50, 29, 21, 50, 38, 191, 33, 40, 41, 199, 26, 55, 45, 186, 18, 65, 41, 215, 12, 31, 50, 25, 3, 89, 223, 160, 25, 78, 36, 9, 4, 155, 55, 5, 11, 171, 47, 22, 10, 68, 213, 138, 15, 109, 35, 229, 10, 222, 22, 104, 13, 227, 31, 129, 25, 44, 233, 187, 13, 168, 219, 11, 0, 167, 36, 53, 18, 216, 39, 22, 44, 7, 0, 75, 244, 217, 5, 173, 115, 14, 35, 173, 39, 11, 26, 143, 32, 145, 35, 39, 53, 168, 30, 251, 48, 158, 17, 186, 42, 187, 15, 136, 56, 140, 13, 123, 233, 76, 12, 155, 110, 89, 25, 179, 223, 95, 13, 207, 22, 146, 0, 63, 220, 207, 30, 59, 79, 20, 15, 168, 214, 232, 17, 93, 90, 96, 28, 129, 68, 3, 7, 114, 113, 163, 19, 85, 85, 127, 26, 248, 63, 211, 191, 38, 192, 123, 213, 243, 208, 175, 124, 122, 193, 61, 196, 117, 196, 250, 202, 234, 200, 255, 186, 34, 188, 198, 190, 208, 192, 80, 209, 53, 191, 23, 195, 143, 190, 164, 227, 116, 175, 124, 110, 47, 204, 86, 218, 196, 199, 180, 225, 183, 184, 159, 105, 113, 183, 121, 209, 35, 205, 163, 217, 124, 194, 151, 222, 199, 181, 24, 105, 120, 190, 172, 124, 164, 202, 155, 181, 174, 182, 75, 126, 77, 217, 247, 115, 213, 188, 116, 115, 250, 196, 0, 184, 118, 187, 248, 119, 140, 212, 105, 210, 204, 214, 235, 66, 58, 67, 62, 11, 231, 133, 177, 206, 229, 210, 250, 202, 246, 204, 79, 206, 184, 222, 133, 202, 249, 218, 194, 207, 240, 205, 88, 51, 188, 164, 95, 72, 235, 89, 96, 198, 234, 219, 126, 70, 149, 71, 211, 7, 74, 130, 134, 75, 142, 76, 47, 199, 249, 198, 41, 71, 0, 78, 229, 34, 252, 154, 153, 76, 167, 80, 17, 35, 96, 146, 106, 56, 226, 156, 98, 201, 201, 232, 34, 10, 62, 133, 147, 33, 226, 149, 56, 52, 188, 156, 168, 197, 245, 228, 39, 216, 232, 211, 247, 255, 179, 11, 255, 124, 89, 187, 240, 220, 81, 216, 243, 229, 111, 223, 243, 229, 111, 223, 109, 220, 215, 202, 86, 225, 3, 207, 54, 248, 70, 213, 49, 237, 67, 213, 26, 243, 231, 199, 118, 247, 144, 45, 233, 113, 2, 168, 114, 242, 131, 22, 165, 230, 74, 32, 246, 123, 78, 164, 159, 242, 47, 233, 207, 253, 34, 34, 149, 240, 214, 40, 243, 120, 255, 165, 249, 116, 155, 183, 80, 228, 102, 187, 127, 106, 151, 178, 230, 124, 30, 178, 63, 119, 145, 176, 139, 232, 7, 192, 38, 108, 24, 172, 82, 12, 217, 133, 193, 11, 247, 255, 119, 213, 143, 217, 141, 14, 3, 135, 100, 17, 75, 140, 165, 32, 94, 230, 162, 37, 232, 145, 169, 42, 162, 147, 196, 48, 207, 158, 186, 42, 104, 240, 24, 68, 130, 165, 108, 219, 120, 238, 197, 203, 31, 254, 32, 233, 150, 242, 27, 224, 125, 230, 27, 224, 125, 230, 210, 22, 67, 242, 146, 36, 105, 255, 124, 224, 88, 234, 117, 215, 97, 240, 211, 209, 25, 225, 148, 59, 18, 157, 161, 204, 68, 235, 204, 217, 63, 222, 42, 216, 83, 228, 6, 64, 165, 157, 161, 209, 105, 238, 87, 81, 119, 3, 42, 34, 50, 50, 27, 61, 62, 164, 230, 233, 5, 3, 37, 1, 57, 119, 45, 101, 9, 53, 101, 22, 117, 80, 178, 50, 143, 44, 133, 69, 198, 54, 170, 50, 225, 42, 168, 32, 95, 20, 133, 45, 229, 5, 197, 41, 77, 7, 124, 113, 123, 9, 61, 38, 166, 49, 92, 13, 57, 23, 65, 9, 99, 76, 49, 13, 32, 114, 231, 28, 167, 240, 49, 214, 206, 173, 234, 201, 120, 205, 139, 195, 97, 175, 253, 127, 67, 180, 0, 209, 102, 200, 88, 129, 87, 137, 148, 216, 186, 212, 94, 77, 63, 64, 43, 34, 234, 134, 55, 78, 254, 74, 177, 210, 224, 225, 1, 220, 80, 206, 31, 219, 216, 4, 149, 129, 155, 145, 91, 42, 226, 143, 16, 28, 210, 239, 56, 5, 196, 248, 36, 212, 151, 221, 190, 39, 111, 130, 180, 51, 27, 37, 214, 103, 131, 196, 29, 211, 152, 8, 164, 38, 85, 52, 140, 96, 160, 53, 241, 79, 254, 34, 211, 21, 165, 81, 101, 50, 200, 42, 103, 44, 66, 6, 142, 32, 87, 20, 219, 44, 55, 0, 68, 38, 112, 49, 167, 10, 96, 43, 91, 15, 3, 72, 147, 210, 29, 189, 221, 174, 55, 206, 73, 123, 106, 167, 189, 211, 204, 214, 130, 230, 202, 161, 136, 14, 168, 135, 125, 247, 26, 235, 126, 118, 111, 226, 99, 218, 152, 202, 252, 43, 160, 250, 129, 213, 96, 222, 57, 46, 240, 139, 121, 89, 35, 13, 24, 51, 1, 40, 55, 53, 204, 38, 107, 53, 70, 49, 232, 38, 206, 53, 122, 37, 17, 77, 29, 42, 229, 30, 146, 20, 30, 3, 128, 41, 98, 16, 174, 227, 62, 4, 4, 34, 59, 44, 242, 14, 102, 106, 76, 14, 2, 56, 119, 202, 79, 187, 220, 205, 144, 215)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_gx2cb")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_2yew7"]
transparency = 4
shading_mode = 0
albedo_color = Color(0.772549, 1, 0.388235, 0.670588)
grow = true
grow_amount = 0.04

[node name="Card" type="CharacterBody3D" groups=["clickableObject"]]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, 0, 0)
top_level = true
collision_mask = 2

[node name="CardBack" type="MeshInstance3D" parent="."]
transform = Transform3D(0.926858, 0, 0, 0, 1.29466, 0, 0, 0, 0.0185, -0.000188898, 0.1162, 0)
layers = 524288
mesh = SubResource("ArrayMesh_3v6u2")
skeleton = NodePath("")

[node name="CardFront" type="MeshInstance3D" parent="."]
transform = Transform3D(0.926858, 0, 0, 0, 1.29466, 0, 0, 0, 0.0185, -0.000188898, 0.1162, 0)
layers = 524288
material_override = SubResource("StandardMaterial3D_uu2bt")
mesh = SubResource("ArrayMesh_tgt8w")
skeleton = NodePath("")

[node name="CardName" type="Label3D" parent="."]
transform = Transform3D(0.934, 0, 0, 0, 1.229, 0, 0, 0, 1.545, 0.0373941, -1.0711, 0.040714)
layers = 524288
pixel_size = 0.007
alpha_cut = 1
modulate = Color(0, 0, 0, 1)
outline_modulate = Color(0.937255, 0.196078, 0.32549, 1)
text = "Card Name2"
font = ExtResource("2_rfw4o")
font_size = 92
uppercase = true

[node name="Description" type="Label3D" parent="."]
transform = Transform3D(0.934, 0, 0, 0, 1.229, 0, 0, 0, 1.545, 0.0373941, -1.44804, 0.004)
visible = false
layers = 524288
text = "Description : dsfsdfsdfsdfsdfsdfsdfsdfsd"
font = ExtResource("2_rfw4o")
font_size = 72
vertical_alignment = 0
autowrap_mode = 3

[node name="CardCost" type="Label3D" parent="."]
transform = Transform3D(1.19287, 0, 0, 0, 1.19287, 0, 0, 0, 1.5, -0.842354, 1.32467, 0.0359745)
layers = 524288
pixel_size = 0.01
text = "1"
font = ExtResource("2_rfw4o")

[node name="CostBG" type="MeshInstance3D" parent="."]
transform = Transform3D(0.637541, -7.54979e-10, -4.19905e-15, 0, 2.18557e-10, -0.636196, -9.62663e-08, -0.005, -2.7809e-08, -0.849702, 1.305, 0.0180465)
layers = 524288
material_override = SubResource("StandardMaterial3D_hy8tp")
mesh = SubResource("CylinderMesh_kb6oc")

[node name="CardPower" type="Label3D" parent="."]
transform = Transform3D(1.19287, 0, 0, 0, 1.19287, 0, 0, 0, 1.5, 0.825435, 1.32628, 0.0359745)
layers = 524288
pixel_size = 0.01
text = "2"
font = ExtResource("2_rfw4o")

[node name="PowerBG" type="MeshInstance3D" parent="."]
transform = Transform3D(0.572501, -7.54979e-10, -4.19905e-15, 0, 2.18557e-10, -0.636196, -8.64455e-08, -0.005, -2.7809e-08, 0.822413, 1.30661, 0.0180465)
layers = 524288
material_override = SubResource("StandardMaterial3D_g4bpp")
mesh = SubResource("CylinderMesh_kb6oc")

[node name="Draggable" type="Node" parent="."]
script = ExtResource("3_wkr8g")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(0.942, 0, 0, 0, 1.326, 0, 0, 0, 0.02, -0.000188898, 0.0958171, 0)
visible = false
shape = SubResource("ConvexPolygonShape3D_aed3s")

[node name="CollisionHand" type="CollisionShape3D" parent="."]
transform = Transform3D(0.942, 0, 0, 0, 1.731, 0, 0, 0, 0.02, -0.000188898, -0.277, 0)
visible = false
shape = SubResource("ConvexPolygonShape3D_aed3s")
disabled = true

[node name="Current" type="MeshInstance3D" parent="."]
transform = Transform3D(1.008, 0, 0, 0, 1.364, 0, 0, 0, 0.011, 0, 0.11823, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_i8q73")
mesh = SubResource("ArrayMesh_208t2")
skeleton = NodePath("")

[node name="MostRecent" type="MeshInstance3D" parent="."]
transform = Transform3D(1.008, 0, 0, 0, 1.364, 0, 0, 0, 0.011, 0, 0.11823, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_2yew7")
mesh = SubResource("ArrayMesh_208t2")
skeleton = NodePath("")

[node name="ClickTimer" type="Timer" parent="."]

[connection signal="input_event" from="." to="." method="_on_input_event"]
[connection signal="mouse_entered" from="." to="." method="_on_mouse_entered"]
[connection signal="mouse_exited" from="." to="." method="_on_mouse_exited"]
