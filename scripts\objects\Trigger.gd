class_name Trigger
extends Object

enum {
	NONE,
	ON_HAND, #Passive
	ON_ARENA, #Passive
	ON_ENTER,
	ON_TURN_START_ARENA,
	ON_TURN_START_HAND,
	ON_TURN_END_ARENA,
	ON_TURN_END_HAND,
	ON_DEAD,
	ON_DISCARD,
	ON_DEBUFFED,
	ON_BUFFED,
	ON_HOST,
	ON_LANE_ALLY_ENTER,
	ON_BOUNCE
}

static var TriggerInfo = {
	NONE: {
		"label": "-",
		"type": KeywordType.NONE
	},
	ON_HAND: {
		"label": "🖐️",
		"type": KeywordType.PASSIVE
	},
	ON_ARENA: {
		"label": "⚔️",
		"type": KeywordType.PASSIVE
	},
	ON_ENTER: {
		"label": "🔽",
		"type": KeywordType.ACTIVE
	},
	ON_TURN_START_ARENA: {
		"label": "🕒",
		"type": KeywordType.ACTIVE
	},
	ON_TURN_START_HAND: {
		"label": "🖐️🕒",
		"type": KeywordType.ACTIVE
	},
	ON_TURN_END_ARENA: {
		"label": "⌚",
		"type": KeywordType.ACTIVE
	},
	ON_TURN_END_HAND: {
		"label": "🖐️⌚",
		"type": KeywordType.ACTIVE
	},
	ON_DEAD: {
		"label": "💀",
		"type": KeywordType.ACTIVE
	},
	ON_DISCARD: {
		"label": "🗑️",
		"type": KeywordType.ACTIVE
	},
	ON_DEBUFFED: {
		"label": "➖",
		"type": KeywordType.ACTIVE
	},
	ON_BUFFED: {
		"label": "➕",
		"type": KeywordType.ACTIVE
	},
	ON_HOST: {
		"label": "🌿",
		"type": KeywordType.ACTIVE
	},
	ON_LANE_ALLY_ENTER: {
		"label": "📗🔽",
		"type": KeywordType.ACTIVE
	},
	ON_BOUNCE: {
		"label": "🖐️",
		"type": KeywordType.ACTIVE
	},
}

var name:int
var condition:int
var conditionTier:int

func _init(p_name:int = NONE, p_condition:int = TriggerCondition.NONE, p_conditionTier:int = 0):
	self.name = p_name
	self.condition = p_condition
	self.conditionTier = p_conditionTier

func getName() -> int:
	return self.name

func getCondition() -> int:
	return self.condition

func getConditionTier() -> int:
	return self.conditionTier
