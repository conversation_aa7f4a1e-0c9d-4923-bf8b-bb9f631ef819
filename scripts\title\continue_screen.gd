extends Control

@onready var option_screen: Control = $OptionScreen
@onready var confirmation_screen: Control = $ConfirmationScreen

var save_files = []
# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	option_screen.find_child("ReturnButton").visible = false
	if FileAccess.file_exists("user://savegame.save"):
		var save_file = FileAccess.open("user://savegame.save", FileAccess.READ)
		while save_file.get_position() < save_file.get_length():
			var json_string = save_file.get_line()
			
	pass # Replace with function body.

# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass

func _on_start_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/title/starter_deck_selection_screen.tscn")

func _on_quit_button_pressed() -> void:
	get_tree().quit()


func _on_settings_button_pressed() -> void:
	option_screen.visible = true


func _on_back_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/title/title_screen.tscn")


func _on_savefile_01_button_pressed() -> void:
	confirmation_screen.visible = true
	confirmation_screen.popup_label.text = "Load game?"
	pass # Replace with function body.


func _on_savefile_02_button_pressed() -> void:
	confirmation_screen.visible = true
	confirmation_screen.popup_label.text = "Load game?"
	pass # Replace with function body.
