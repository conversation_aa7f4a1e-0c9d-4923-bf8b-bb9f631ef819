class_name PlayCardData
extends Object

var name: String
var ownedPlayer: int  # Player ID
var color: int
var rarity: int
var cost: int
var baseCost: int
var power: int
var basePower: int
var castCondition: int

var abilities: Array[Ability]
var givingAbilities: Array[Ability]
var enableAbilities: bool

var enterArenaOnTurn: int
var drawnOnTurn: int
var dieOnTurn: int

var effectPowerModifier: int
var permanentPowerModifier: int
var effectCostModifier: int
var permanentCostModifier: int
var is_ignited = false
var is_silenced = false

func _init(p_owner: int, p_color: int = CardColor.NONE, p_name: String = "", p_rarity: int = Rarity.COMMON, p_cost: int = 0, p_power: int = 0, p_castCondition: int = CastCondition.NORMAL, p_abilities: Array[Ability] = [], p_givingAbilities: Array[Ability] = []):
	if p_name == "":
		self.name = str(randi() % 999)
	else:
		self.name = p_name
	self.ownedPlayer = p_owner
	self.color = p_color
	self.rarity = p_rarity
	self.cost = p_cost
	self.baseCost = p_cost
	self.power = p_power
	self.basePower = p_power
	self.castCondition = p_castCondition
	self.abilities = p_abilities
	self.givingAbilities = p_givingAbilities
	self.enableAbilities = true











