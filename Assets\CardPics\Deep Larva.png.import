[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://4qp2hfmuro67"
path="res://.godot/imported/Deep Larva.png-e98d60fea2885cfb81f8818b29fdd0b2.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://Assets/CardPics/Deep Larva.png"
dest_files=["res://.godot/imported/Deep Larva.png-e98d60fea2885cfb81f8818b29fdd0b2.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
