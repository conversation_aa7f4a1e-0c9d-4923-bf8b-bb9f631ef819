class_name KeywordNoteService

static var keywordDict:Dictionary = {
	"Indestructible": "This card can't be destroyed",
	"On enter": "The ability that trigger when the card enters the arena",
	"On dead": "The ability that trigger when the card is destroyed",
	"On reveal": "The ability trigger once when the event reveals",
	"Permanent mana": "Your maximum mana that replenish each turn",
	"Temp mana": "Temporary mana that carry on indefinitely until you use it",
	"Heal": "The target regain its original power if being weakened",
	"Ignite": "Draw a card, give +X Power to that card, discard that card at the end of your next turn",
	"Winning player": "A player who's having highest total power in this lane",
	"Doubled": "Only an effect with numerical value is doubled",
	"Mimic": "This card has abilities of target card",
	"Unsummon": "Return target card to its owner hand"
}

static func generateKeywordNotes(abilityText:String, castCondition:int) -> String:
	var keywordNotes = ""
	match castCondition:
		CastCondition.SYMBIONT:
			keywordNotes += "Symbiont 🌳\n"
			keywordNotes += "Symbiont card must be cast on your ally. That ally become its host\n\n"
		CastCondition.SACRIFICE:
			keywordNotes += "Sacrifice 🩸\n"
			keywordNotes += "This card enters the arena by destroying an ally and replace it\n\n"
		CastCondition.BOUNCE:
			keywordNotes += "Bounce 🔃\n"
			keywordNotes += "This card enters the arena by returning an ally to your hand and replace it\n\n"
	
	for keyword in keywordDict:
		if abilityText.to_lower().contains(keyword.to_lower()):
			keywordNotes += keyword + "\n"
			keywordNotes += keywordDict[keyword] + "\n\n"
	
	return keywordNotes


static func extractReproduceCardName(sourceCard:Card) -> String:
	if not sourceCard.hasAbilityKeyword(Keyword.REPRODUCE): return ""
	
	var abilityText = sourceCard.getAbility().getText()
	var word = "Create a"
	var word_position = abilityText.find(word)
	if word_position == -1:
		return ""
	
	var start_position = word_position + word.length()
	var remaining_string = abilityText.substr(start_position, abilityText.length() - start_position).strip_edges()
	
	var end_position = remaining_string.find(" ")
	if end_position == -1:
		return remaining_string
	
	return remaining_string.substr(0, end_position)
