class_name Lane<PERSON>ard<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
extends Card<PERSON>ollectionUIController

enum {
	IDLE,
	IDLE_PLAYER_SIDE,
	IDLE_ENEMY_SIDE,
	DISABLED,
	CASTABLE,
	CASTABLE_PLAYER_SIDE,
	READY_TO_CAST,
	READY_TO_CAST_PLAYER_SIDE,
	SA<PERSON>IF<PERSON>BLE,
	READY_TO_SAC,
	SYMBIONTABLE,
	READY_TO_SYM,
	#OCCUPIED
}

var state:int = IDLE
signal slotStateChange(state:int)

# Called when the node enters the scene tree for the first time.w
func _ready():
	super()

func add(p_cards:Array[Card]) -> void:
	super(p_cards)
	for card in get_children():
		if card.get_index() > 0:
			card.visible = false
		#card.cardBorder.visible = false
		card.playerParticle.scale_amount_max = 1
		card.playerParticle.scale_amount_min = 1
		card.enemyParticle.scale_amount_max = 1
		card.enemyParticle.scale_amount_min = 1
		#if card.cardOwner.name == "Player":
			#card.playerParticle.visible = true
			#card.enemyParticle.visible = false
		#else:
			#card.enemyParticle.visible = true
			#card.playerParticle.visible = false
		card.powerParticle.visible = true
		card.brightness_up(false)
		card.name_visible(false)
		card.lightBorder(true)
		card.cost_visible(false)
		card.cardSelected.connect(battleNode.inspect_card)
		battleNode.inspecting.connect(card.change_collision_mode)
		card.set_layer(20, false)

func getState() -> int:
	return self.state

func setState(_state:int) -> void:
	self.state = _state
	slotStateChange.emit(_state)
