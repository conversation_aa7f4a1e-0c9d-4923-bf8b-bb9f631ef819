extends Node
class_name player_profile

#varibles
var player_name : String = "PlayerX"
var decks : Array = [] #deck[0] = all cards
#{"name": "deck1", "cards": {CardData.Card.Elderbloom: 2},"active": 1}
var resources : Dictionary = {} 

# {"green_ink": 100, "red_ink": 50}
#green_ink, red_ink, blue_ink, 
#rare_shard, epic_shard, legendary_shard
#powder
#combine_crystal

var progress : Dictionary = {} #progress[0] = a, progress[1] = b, progress[2] = c, progress[3] = d
var achievements : Array = [] #list of achievement ids

var time : int  = 0 #unix timestamp of last save
var version : int = 1 #version of the game when the profile was created/last updated


