[remap]

importer="wavefront_obj"
importer_version=1
type="Mesh"
uid="uid://cvbjmblmqmf76"
path="res://.godot/imported/explosion_ring.obj-5192d5b39b5d26d2c4bbdb1c6c6859a9.mesh"

[deps]

files=["res://.godot/imported/explosion_ring.obj-5192d5b39b5d26d2c4bbdb1c6c6859a9.mesh"]

source_file="res://effects/halloween_explosion/explosion_ring.obj"
dest_files=["res://.godot/imported/explosion_ring.obj-5192d5b39b5d26d2c4bbdb1c6c6859a9.mesh", "res://.godot/imported/explosion_ring.obj-5192d5b39b5d26d2c4bbdb1c6c6859a9.mesh"]

[params]

generate_tangents=true
scale_mesh=Vector3(1, 1, 1)
offset_mesh=Vector3(0, 0, 0)
optimize_mesh=true
force_disable_mesh_compression=false
