extends Control
@onready var option_screen: Control = $OptionScreen

#varibles
var decks # deck[0] = all cards

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	pass

# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass

#logic functions

#add aquire card to all cards deck
func aquire_card(card_id: int, amount: int) -> void:
	var deck = PlayerProfile.decks[0]
	if deck.has("cards"):
		deck["cards"] += amount
	pass

#add to deck index 
func add_card(card_id: int, amount: int, index: int) -> void:
	if amount > 2: pass # max 2 copies of a card in a deck
	var deck = PlayerProfile.decks[index]
	if deck.has("cards"):
		if deck["cards"] + amount > 2:
			pass # max 2 copies of a card in a deck
		deck["cards"] += amount
	else:
		deck["cards"] = amount
	pass

func remove_card(card_id: int, amount: int, index: int) -> void:
	var deck = PlayerProfile.decks[index]
	if deck.has("cards"):
		deck["cards"] -= amount
		if deck["cards"] <= 0:
			deck.erase("cards")
	else:
		print("Card ID not found in deck.")
	pass

func set_active_deck(index: int) -> void:
	for i in range(PlayerProfile.decks.size()):
		if PlayerProfile.decks[i].has("active"):
			PlayerProfile.decks[i]["active"] = 0
	PlayerProfile.decks[index]["active"] = 1
	pass

func sort(index: int, direction: int, criteria: String) -> Dictionary:
	var deck = PlayerProfile.decks[index]
	print("Sorting cards...")
	return {}

func filter(index: int, filter_criteria: Dictionary) -> Dictionary:
	var deck = PlayerProfile.decks[index]
	for card in deck:
		print("Filtering cards...")
	return {}

#ui functions
func _on_start_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/map/world_map_screen.tscn")

func _on_done_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/my_lib/my_library_screen.tscn")


func _on_settings_button_pressed() -> void:
	option_screen.visible = true
	pass # Replace with function body.
