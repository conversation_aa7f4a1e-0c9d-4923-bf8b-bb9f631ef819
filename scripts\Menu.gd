extends Control

@onready var yourName = $YourDeck/YourName
@onready var yourDeckGroup = $YourDeck/YourDeckGroup
@onready var yourDeckPic = $YourDeck/YourDeckPic
@onready var yourGStarter = $YourDeck/YourDeckGroup/YourGStarter
@onready var yourRStarter = $YourDeck/YourDeckGroup/YourRStarter
@onready var yourGreen = $YourDeck/YourDeckGroup/YourGreen
@onready var yourRed = $YourDeck/YourDeckGroup/yourRed
@onready var yourDragon = $YourDeck/YourDeckGroup/YourDragon
@onready var oppoGStarter = $OpponentButtons/GStarter
@onready var oppoRStarter = $OpponentButtons/RStarter
@onready var oppoGreen = $OpponentButtons/Green
@onready var oppoRed = $OpponentButtons/Red
@onready var oppoDragon = $OpponentButtons/Dragon
@onready var gStarterGlow = $OpponentButtons/Glow/GStarterGlow
@onready var rStarterGlow = $OpponentButtons/Glow/RStarterGlow
@onready var bStarterGlow = $OpponentButtons/Glow/BStarterGlow
@onready var rbGlow = $OpponentButtons/Glow/RBGlow
@onready var gbGlow = $OpponentButtons/Glow/GBGlow
@onready var grGlow = $OpponentButtons/Glow/GRGlow
@onready var dragonGlow = $OpponentButtons/Glow/DragonGlow
@onready var pickedGlow = $YourDeck/DeckGlow/PickedGlow
@onready var deckGStarterGlow = $YourDeck/DeckGlow/DeckGstarterGlow
@onready var deckRStarterGlow = $YourDeck/DeckGlow/DeckRstarterGlow
@onready var deckBStarterGlow = $YourDeck/DeckGlow/DeckBstarterGlow
@onready var deckRedGlow = $YourDeck/DeckGlow/DeckRstarterGlow
@onready var deckGreenGlow = $YourDeck/DeckGlow/DeckGstarterGlow
@onready var deckBlueGlow = $YourDeck/DeckGlow/DeckBstarterGlow
@onready var deckRBGlow = $YourDeck/DeckGlow/DeckRBGlow
@onready var deckGBGlow = $YourDeck/DeckGlow/DeckGBGlow
@onready var deckGRGlow = $YourDeck/DeckGlow/DeckGRGlow
@onready var deckDragonGlow = $YourDeck/DeckGlow/DeckDragonGlow
@onready var classicMode = $GameModeSelect/ModeButtons/ClassicMode
@onready var dominationMode = $GameModeSelect/ModeButtons/DominationMode

var rStarterDeckPic = load("res://Assets/Menu2/Deck Red Starter.png")
var gStarterDeckPic = load("res://Assets/Menu2/Deck Green Starter.png")
var bStarterDeckPic = load("res://Assets/Menu2/Deck Blue Starter.png")
var rbDeckPic = load("res://Assets/Menu2/Deck Red.png")
var gbDeckPic = load("res://Assets/Menu2/Deck Green.png")
var grDeckPic = load("res://Assets/Menu2/Deck Dragon.png")

# Add this variable to track current game mode
var currentMode = "classic"

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	#get_viewport().size = DisplayServer.screen_get_size()
	updateYourDeck()
	self.yourName.text = MiscService.PlayerName
	
	# Connect mode buttons
	classicMode.pressed.connect(_on_classic_mode_pressed)
	dominationMode.pressed.connect(_on_domination_mode_pressed)

func updateYourDeck() -> void:
	match DeckResource.MainDeck:
		DeckResource.RED:
			self.yourDeckPic.icon = rStarterDeckPic
		DeckResource.GREEN:
			self.yourDeckPic.icon = gStarterDeckPic
		DeckResource.BLUE:
			self.yourDeckPic.icon = bStarterDeckPic
		DeckResource.RED_BLUE:
			self.yourDeckPic.icon = rbDeckPic
		DeckResource.GREEN_BLUE:
			self.yourDeckPic.icon = gbDeckPic
		DeckResource.GREEN_RED:
			self.yourDeckPic.icon = grDeckPic

func enterArena() -> void:
	if self.yourName.text != MiscService.PlayerName:
		ScoreService.dragonWin = 0
		ScoreService.dragonLose = 0
		
	if self.yourName.text == "":
		MiscService.PlayerName = "Player"
	else:
		MiscService.PlayerName = self.yourName.text
	
	# You might want to store the selected mode in a global service/autoload
	# For example: GameModeService.currentMode = currentMode
	
	get_tree().change_scene_to_file("res://main3D.tscn")

func _on_quit_button_pressed() -> void:
	get_tree().quit()

func _on_red_click():
	DeckResource.OppoDeck = DeckResource.RED
	LocationData.DifficultySet = LocationData.difficulty.STANDARD
	enterArena()

func _on_green_click():
	DeckResource.OppoDeck = DeckResource.GREEN
	LocationData.DifficultySet = LocationData.difficulty.STANDARD
	enterArena()

func _on_blue_click() -> void:
	DeckResource.OppoDeck = DeckResource.BLUE
	LocationData.DifficultySet = LocationData.difficulty.STANDARD
	enterArena()

func _on_rb_click():
	DeckResource.OppoDeck = DeckResource.RED_BLUE
	LocationData.DifficultySet = LocationData.difficulty.SIMPLE
	enterArena()

func _on_gb_click():
	DeckResource.OppoDeck = DeckResource.GREEN_BLUE
	LocationData.DifficultySet = LocationData.difficulty.SIMPLE
	enterArena()

func _on_gr_click():
	DeckResource.OppoDeck = DeckResource.GREEN_RED
	LocationData.DifficultySet = LocationData.difficulty.CHALLENGE
	enterArena()

func _on_change_your_deck():
	pickedGlow.visible = true

func _on_your_rstarter_click():
	self.yourDeckGroup.visible = false
	DeckResource.MainDeck = DeckResource.RED
	updateYourDeck()

func _on_your_gstarter_click():
	self.yourDeckGroup.visible = false
	DeckResource.MainDeck = DeckResource.GREEN
	updateYourDeck()

func _on_your_b_starter_click() -> void:
	self.yourDeckGroup.visible = false
	DeckResource.MainDeck = DeckResource.BLUE
	updateYourDeck()


func _on_your_rb_click():
	self.yourDeckGroup.visible = false
	DeckResource.MainDeck = DeckResource.RED_BLUE
	updateYourDeck()

func _on_your_gb_click():
	self.yourDeckGroup.visible = false
	DeckResource.MainDeck = DeckResource.GREEN_BLUE
	updateYourDeck()

func _on_your_gr_click():
	self.yourDeckGroup.visible = false
	DeckResource.MainDeck = DeckResource.GREEN_RED
	updateYourDeck()

#func _on_your_dragon_click():
	#self.yourDeckGroup.visible = false
	#DeckResource.MainDeck = DeckResource.GREEN_RED
	#updateYourDeck()

func _on_r_starter_mouse_entered() -> void:
	rStarterGlow.visible = true

func _on_g_starter_mouse_entered() -> void:
	gStarterGlow.visible = true

func _on_b_starter_mouse_entered() -> void:
	bStarterGlow.visible = true

func _on_rb_mouse_entered() -> void:
	rbGlow.visible = true

func _on_gb_mouse_entered() -> void:
	gbGlow.visible = true

func _on_gr_mouse_entered() -> void:
	grGlow.visible = true

#func _on_dragon_mouse_entered() -> void:
	#dragonGlow.visible = true


func _on_r_starter_mouse_exited() -> void:
	rStarterGlow.visible = false

func _on_g_starter_mouse_exited() -> void:
	gStarterGlow.visible = false

func _on_b_starter_mouse_exited() -> void:
	bStarterGlow.visible = false

func _on_rb_mouse_exited() -> void:
	rbGlow.visible = false

func _on_gb_mouse_exited() -> void:
	gbGlow.visible = false

func _on_gr_mouse_exited() -> void:
	grGlow.visible = false

#func _on_dragon_mouse_exited() -> void:
	#dragonGlow.visible = false

func _on_your_r_starter_mouse_entered() -> void:
	deckRStarterGlow.visible = true

func _on_your_g_starter_mouse_entered() -> void:
	deckGStarterGlow.visible = true

func _on_your_b_starter_mouse_entered() -> void:
	deckBStarterGlow.visible = true

func _on_your_rb_mouse_entered() -> void:
	deckRBGlow.visible = true

func _on_your_gb_mouse_entered() -> void:
	deckGBGlow.visible = true

func _on_your_gr_mouse_entered() -> void:
	deckGRGlow.visible = true

func _on_your_dragon_mouse_entered():
	deckDragonGlow.visible = true

func _on_your_g_starter_mouse_exited() -> void:
	deckGStarterGlow.visible = false

func _on_your_r_starter_mouse_exited() -> void:
	deckRStarterGlow.visible = false

func _on_your_b_starter_mouse_exited() -> void:
	deckBlueGlow.visible = false

func _on_your_rb_mouse_exited() -> void:
	deckRBGlow.visible = false

func _on_your_gb_mouse_exited() -> void:
	deckGBGlow.visible = false

func _on_your_gr_mouse_exited() -> void:
	deckGRGlow.visible = false




func _on_your_blue_pressed() -> void:
	pass # Replace with function body.

func _on_your_deck_pic_pressed() -> void:
	#self.yourDragon.visible = ScoreService.usedMorco
	self.yourDeckGroup.visible = !self.yourDeckGroup.visible


func _on_your_deck_pic_mouse_exited() -> void:
	pickedGlow.visible = false
	#self.yourDeckGroup.visible = false


func _on_your_name_text_changed():
	if self.yourName.text.length() > 15:
		self.yourName.text = self.yourName.text.substr(0, 15)
		self.yourName.set_caret_column(15)

func _on_classic_mode_pressed() -> void:
	if !classicMode.button_pressed:
		classicMode.button_pressed = true
		return
	dominationMode.button_pressed = false
	currentMode = "classic"

func _on_domination_mode_pressed() -> void:
	if !dominationMode.button_pressed:
		dominationMode.button_pressed = true
		return
	classicMode.button_pressed = false
	currentMode = "domination"

















