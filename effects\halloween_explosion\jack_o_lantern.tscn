[gd_scene load_steps=30 format=3 uid="uid://ldem8o2w0156"]

[ext_resource type="Script" path="res://effects/halloween_explosion/jack_o_lantern.gd" id="1_acisk"]
[ext_resource type="PackedScene" uid="uid://dnnls4fwxqvxy" path="res://effects/halloween_explosion/mesh/jack_o_lantern.glb" id="1_lr0q6"]
[ext_resource type="Material" uid="uid://d3pfxi4udjfxb" path="res://effects/halloween_explosion/mesh/jack_o_lantern_mat.tres" id="3_3nbxj"]
[ext_resource type="AnimationLibrary" uid="uid://dcm2qc86snx3a" path="res://effects/halloween_explosion/mesh/animations/custom.res" id="4_1mtwm"]

[sub_resource type="Animation" id="Animation_f7bho"]
resource_name = "Explode"
tracks/0/type = "position_3d"
tracks/0/imported = true
tracks/0/enabled = true
tracks/0/path = NodePath("rig/Skeleton3D:DEF-lid1.B.L")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = PackedFloat32Array(0, 1, 0.30289, 0.438881, 0.358675, 0.0333333, 1, 0.311734, 0.414588, 0.369148, 0.0666667, 1, 0.331538, 0.368431, 0.392599, 0.1, 1, 0.359043, 0.314986, 0.425171, 0.133333, 1, 0.387323, 0.268828, 0.458659, 0.166667, 1, 0.405777, 0.244535, 0.480512, 0.2, 1, 0.391127, 0.263463, 0.463164, 0.233333, 1, 0.362571, 0.309104, 0.429348, 0.266667, 1, 0.328374, 0.378448, 0.388852, 0.3, 1, 0.294634, 0.467669, 0.348899, 0.333333, 1, 0.265286, 0.57212, 0.314145, 0.366667, 1, 0.243035, 0.685613, 0.287796, 0.4, 1, 0.231925, 0.822412, 0.27464, 0.433333, 1, 0.235819, 0.994135, 0.279251, 0.466667, 1, 0.25377, 1.18934, 0.300508, 0.5, 1, 0.280023, 1.37352, 0.331597, 0.533333, 1, 0.300136, 1.49747, 0.355414, 0.566667, 1, 0.31231, 1.579, 0.36983, 0.6, 1, 0.31817, 1.62304, 0.376769, 0.633333, 1, 0.319302, 1.63329, 0.378109, 0.666667, 1, 0.317256, 1.61224, 0.375687, 0.7, 1, 0.308963, 1.54133, 0.365867, 0.733333, 1, 0.293902, 1.42238, 0.348032, 0.766667, 1, 0.273775, 1.25137, 0.324198, 0.8, 1, 0.253878, 1.03746, 0.300636, 0.833333, 1, 0.243097, 0.803019, 0.28787, 0.866667, 1, 0.260623, 0.619275, 0.308623, 0.9, 1, 0.295363, 0.471878, 0.349762, 0.933333, 1, 0.337707, 0.359639, 0.399905, 0.966667, 1, 0.376041, 0.285353, 0.445299, 1, 1, 0.396749, 0.25579, 0.469821)
tracks/1/type = "rotation_3d"
tracks/1/imported = true
tracks/1/enabled = true
tracks/1/path = NodePath("rig/Skeleton3D:DEF-lid1.B.L")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = PackedFloat32Array(0, 1, 0.683922, -0.235471, -0.688633, 0.0508833, 0.0333333, 1, 0.680699, -0.252797, -0.685389, 0.0546276, 0.0666667, 1, 0.672504, -0.291966, -0.677137, 0.0630916, 0.133333, 1, 0.644117, -0.396424, -0.648555, 0.0856643, 0.166667, 1, 0.633824, -0.427153, -0.638191, 0.092304, 0.2, 1, 0.64199, -0.403009, -0.646412, 0.0870867, 0.233333, 1, 0.657608, -0.351233, -0.662138, 0.0758985, 0.266667, 1, 0.673852, -0.285921, -0.678495, 0.0617854, 0.3, 1, 0.686552, -0.220253, -0.691282, 0.0475953, 0.333333, 1, 0.694427, -0.166095, -0.699211, 0.0358922, 0.366667, 1, 0.698358, -0.130592, -0.703169, 0.02822, 0.4, 1, 0.699882, -0.113808, -0.704704, 0.0245931, 0.433333, 1, 0.69937, -0.119711, -0.704189, 0.0258686, 0.466667, 1, 0.696591, -0.147628, -0.701391, 0.0319013, 0.5, 1, 0.69091, -0.192248, -0.695671, 0.041544, 0.533333, 1, 0.684816, -0.230412, -0.689534, 0.0497902, 0.566667, 1, 0.680476, -0.25395, -0.685164, 0.0548768, 0.6, 1, 0.67819, -0.265454, -0.682863, 0.057363, 0.633333, 1, 0.677733, -0.267692, -0.682402, 0.057846, 0.666667, 1, 0.678557, -0.263647, -0.683231, 0.056971, 0.7, 1, 0.681739, -0.247351, -0.686435, 0.0534501, 0.733333, 1, 0.686846, -0.218478, -0.691578, 0.0472114, 0.766667, 1, 0.692385, -0.181755, -0.697155, 0.0392759, 0.8, 1, 0.696642, -0.147171, -0.701441, 0.0318027, 0.833333, 1, 0.698406, -0.130089, -0.703218, 0.0281113, 0.866667, 1, 0.695346, -0.158522, -0.700137, 0.0342557, 0.9, 1, 0.686136, -0.22273, -0.690863, 0.0481305, 0.933333, 1, 0.669798, -0.303698, -0.674412, 0.0656268, 0.966667, 1, 0.650291, -0.376531, -0.654771, 0.0813651, 1, 1, 0.638824, -0.412575, -0.643224, 0.0891533)
tracks/2/type = "scale_3d"
tracks/2/imported = true
tracks/2/enabled = true
tracks/2/path = NodePath("rig/Skeleton3D:DEF-lid1.B.L")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 1.01867, 0.963935, 1.01867, 0.0666667, 1, 1.05311, 0.903138, 1.05311, 0.1, 1, 1.0883, 0.84506, 1.0883, 0.133333, 1, 1.11308, 0.807156, 1.11308, 0.166667, 1, 1.12022, 0.796883, 1.12022, 0.2, 1, 1.11515, 0.804162, 1.11515, 0.233333, 1, 1.09153, 0.840024, 1.09153, 0.266667, 1, 1.04516, 0.919273, 1.04516, 0.3, 1, 0.979219, 1.04786, 0.979219, 0.333333, 1, 0.904282, 1.2229, 0.904282, 0.366667, 1, 0.837565, 1.43178, 0.837564, 0.4, 1, 0.803254, 1.55135, 0.803253, 0.433333, 1, 0.815306, 1.51006, 0.815305, 0.466667, 1, 0.869718, 1.32881, 0.869718, 0.5, 1, 0.944529, 1.12091, 0.944528, 0.533333, 1, 0.992903, 1.01625, 0.992902, 0.566667, 1, 1.01969, 0.962153, 1.01969, 0.6, 1, 1.03158, 0.93973, 1.03158, 0.633333, 1, 1.03374, 0.935796, 1.03374, 0.666667, 1, 1.02984, 0.942887, 1.02984, 0.7, 1, 1.01294, 0.974806, 1.01294, 0.733333, 1, 0.978279, 1.0469, 0.978279, 0.766667, 1, 0.926428, 1.17138, 0.926428, 0.8, 1, 0.870955, 1.31972, 0.870955, 0.833333, 1, 0.838407, 1.42262, 0.838407, 0.866667, 1, 0.890467, 1.26458, 0.890467, 0.9, 1, 0.977363, 1.05932, 0.977363, 0.933333, 1, 1.0572, 0.898035, 1.0572, 0.966667, 1, 1.106, 0.817566, 1.106, 1, 1, 1.11777, 0.800372, 1.11777)
tracks/3/type = "position_3d"
tracks/3/imported = true
tracks/3/enabled = true
tracks/3/path = NodePath("rig/Skeleton3D:DEF-lid1.B.R")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = PackedFloat32Array(0, 1, -0.30289, 0.438881, 0.358675, 0.0333333, 1, -0.311734, 0.414588, 0.369148, 0.0666667, 1, -0.331538, 0.368431, 0.392599, 0.1, 1, -0.359043, 0.314986, 0.425171, 0.133333, 1, -0.387323, 0.268828, 0.458659, 0.166667, 1, -0.405777, 0.244535, 0.480512, 0.2, 1, -0.391127, 0.263463, 0.463164, 0.233333, 1, -0.362571, 0.309104, 0.429348, 0.266667, 1, -0.328374, 0.378448, 0.388852, 0.3, 1, -0.294634, 0.467669, 0.348899, 0.333333, 1, -0.265286, 0.57212, 0.314145, 0.366667, 1, -0.243035, 0.685613, 0.287796, 0.4, 1, -0.231925, 0.822412, 0.27464, 0.433333, 1, -0.235819, 0.994135, 0.279251, 0.466667, 1, -0.25377, 1.18934, 0.300508, 0.5, 1, -0.280023, 1.37352, 0.331597, 0.533333, 1, -0.300136, 1.49747, 0.355414, 0.566667, 1, -0.31231, 1.579, 0.36983, 0.6, 1, -0.31817, 1.62304, 0.376769, 0.633333, 1, -0.319302, 1.63329, 0.378109, 0.666667, 1, -0.317256, 1.61224, 0.375687, 0.7, 1, -0.308963, 1.54133, 0.365867, 0.733333, 1, -0.293902, 1.42238, 0.348032, 0.766667, 1, -0.273775, 1.25137, 0.324198, 0.8, 1, -0.253878, 1.03746, 0.300636, 0.833333, 1, -0.243098, 0.803019, 0.28787, 0.866667, 1, -0.260623, 0.619275, 0.308623, 0.9, 1, -0.295363, 0.471878, 0.349762, 0.933333, 1, -0.337707, 0.359639, 0.399905, 0.966667, 1, -0.376041, 0.285353, 0.445299, 1, 1, -0.396749, 0.25579, 0.469821)
tracks/4/type = "rotation_3d"
tracks/4/imported = true
tracks/4/enabled = true
tracks/4/path = NodePath("rig/Skeleton3D:DEF-lid1.B.R")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = PackedFloat32Array(0, 1, 0.683921, 0.235472, 0.688633, 0.0508841, 0.0333333, 1, 0.680699, 0.252797, 0.685389, 0.0546278, 0.0666667, 1, 0.672504, 0.291966, 0.677137, 0.0630918, 0.133333, 1, 0.644117, 0.396424, 0.648554, 0.0856643, 0.166667, 1, 0.633824, 0.427154, 0.63819, 0.0923047, 0.2, 1, 0.641989, 0.40301, 0.646412, 0.0870873, 0.233333, 1, 0.657608, 0.351233, 0.662138, 0.0758988, 0.266667, 1, 0.673852, 0.285921, 0.678495, 0.0617855, 0.3, 1, 0.686552, 0.220253, 0.691281, 0.0475952, 0.333333, 1, 0.694427, 0.166095, 0.699211, 0.035892, 0.366667, 1, 0.698358, 0.130591, 0.703169, 0.0282196, 0.4, 1, 0.699882, 0.113807, 0.704704, 0.0245929, 0.433333, 1, 0.69937, 0.119711, 0.704188, 0.0258689, 0.466667, 1, 0.696591, 0.147628, 0.70139, 0.0319016, 0.5, 1, 0.690911, 0.192247, 0.69567, 0.0415428, 0.533333, 1, 0.684817, 0.23041, 0.689535, 0.0497897, 0.566667, 1, 0.680476, 0.25395, 0.685164, 0.0548766, 0.6, 1, 0.67819, 0.265455, 0.682862, 0.057363, 0.633333, 1, 0.677733, 0.267691, 0.682402, 0.0578466, 0.666667, 1, 0.678557, 0.263648, 0.683231, 0.0569726, 0.7, 1, 0.681739, 0.247349, 0.686435, 0.0534502, 0.733333, 1, 0.686847, 0.218478, 0.691578, 0.0472113, 0.766667, 1, 0.692385, 0.181755, 0.697155, 0.0392759, 0.8, 1, 0.696642, 0.147171, 0.701441, 0.0318025, 0.833333, 1, 0.698406, 0.130089, 0.703218, 0.0281115, 0.866667, 1, 0.695346, 0.158522, 0.700136, 0.0342554, 0.9, 1, 0.686136, 0.22273, 0.690863, 0.0481307, 0.933333, 1, 0.669798, 0.303699, 0.674412, 0.0656274, 0.966667, 1, 0.650291, 0.376532, 0.654771, 0.0813659, 1, 1, 0.638823, 0.412575, 0.643224, 0.0891547)
tracks/5/type = "scale_3d"
tracks/5/imported = true
tracks/5/enabled = true
tracks/5/path = NodePath("rig/Skeleton3D:DEF-lid1.B.R")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 1.01866, 0.963937, 1.01867, 0.0666667, 1, 1.05311, 0.90314, 1.05311, 0.1, 1, 1.0883, 0.845061, 1.0883, 0.133333, 1, 1.11308, 0.807157, 1.11308, 0.166667, 1, 1.12022, 0.796885, 1.12022, 0.2, 1, 1.11514, 0.804165, 1.11514, 0.233333, 1, 1.09153, 0.840025, 1.09153, 0.266667, 1, 1.04516, 0.919273, 1.04516, 0.3, 1, 0.979219, 1.04786, 0.97922, 0.333333, 1, 0.904282, 1.2229, 0.904283, 0.366667, 1, 0.837565, 1.43178, 0.837565, 0.4, 1, 0.803254, 1.55135, 0.803254, 0.433333, 1, 0.815306, 1.51006, 0.815306, 0.466667, 1, 0.869718, 1.32881, 0.869718, 0.5, 1, 0.944526, 1.12091, 0.944527, 0.533333, 1, 0.992902, 1.01625, 0.992902, 0.566667, 1, 1.01969, 0.962153, 1.01969, 0.6, 1, 1.03158, 0.939732, 1.03158, 0.633333, 1, 1.03374, 0.935802, 1.03374, 0.666667, 1, 1.02984, 0.942889, 1.02984, 0.7, 1, 1.01294, 0.97481, 1.01294, 0.733333, 1, 0.97828, 1.0469, 0.97828, 0.766667, 1, 0.926429, 1.17138, 0.92643, 0.8, 1, 0.870955, 1.31972, 0.870955, 0.833333, 1, 0.838407, 1.42262, 0.838407, 0.866667, 1, 0.890467, 1.26458, 0.890467, 0.9, 1, 0.977363, 1.05932, 0.977363, 0.933333, 1, 1.0572, 0.898036, 1.0572, 0.966667, 1, 1.106, 0.817566, 1.106, 1, 1, 1.11777, 0.800374, 1.11777)
tracks/6/type = "position_3d"
tracks/6/imported = true
tracks/6/enabled = true
tracks/6/path = NodePath("rig/Skeleton3D:DEF-lid1.T.L")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = PackedFloat32Array(0, 1, 0.30289, 0.438881, 0.358675, 0.0333333, 1, 0.311734, 0.414588, 0.369148, 0.0666667, 1, 0.331538, 0.368431, 0.392599, 0.1, 1, 0.359043, 0.314986, 0.425171, 0.133333, 1, 0.387323, 0.268828, 0.458659, 0.166667, 1, 0.405777, 0.244535, 0.480512, 0.2, 1, 0.391127, 0.263463, 0.463164, 0.233333, 1, 0.362571, 0.309104, 0.429348, 0.266667, 1, 0.328374, 0.378448, 0.388852, 0.3, 1, 0.294634, 0.467669, 0.348899, 0.333333, 1, 0.265286, 0.57212, 0.314145, 0.366667, 1, 0.243035, 0.685613, 0.287796, 0.4, 1, 0.231925, 0.822412, 0.27464, 0.433333, 1, 0.235819, 0.994135, 0.279251, 0.466667, 1, 0.25377, 1.18934, 0.300508, 0.5, 1, 0.280023, 1.37352, 0.331597, 0.533333, 1, 0.300136, 1.49747, 0.355414, 0.566667, 1, 0.31231, 1.579, 0.36983, 0.6, 1, 0.31817, 1.62304, 0.376769, 0.633333, 1, 0.319302, 1.63329, 0.378109, 0.666667, 1, 0.317256, 1.61224, 0.375687, 0.7, 1, 0.308963, 1.54133, 0.365867, 0.733333, 1, 0.293902, 1.42238, 0.348032, 0.766667, 1, 0.273775, 1.25137, 0.324198, 0.8, 1, 0.253878, 1.03746, 0.300636, 0.833333, 1, 0.243097, 0.803019, 0.28787, 0.866667, 1, 0.260623, 0.619275, 0.308623, 0.9, 1, 0.295363, 0.471878, 0.349762, 0.933333, 1, 0.337707, 0.359639, 0.399905, 0.966667, 1, 0.376041, 0.285353, 0.445299, 1, 1, 0.396749, 0.25579, 0.469821)
tracks/7/type = "rotation_3d"
tracks/7/imported = true
tracks/7/enabled = true
tracks/7/path = NodePath("rig/Skeleton3D:DEF-lid1.T.L")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = PackedFloat32Array(0, 1, 0.272322, -0.820261, 0.076848, 0.497099, 0.0333333, 1, 0.303885, -0.811005, 0.079515, 0.493561, 0.0666667, 1, 0.37817, -0.784908, 0.0817303, 0.48397, 0.1, 1, 0.474743, -0.741317, 0.0772874, 0.468076, 0.133333, 1, 0.562865, -0.690586, 0.0659007, 0.449368, 0.166667, 1, 0.607373, -0.65993, 0.0538258, 0.438968, 0.2, 1, 0.567061, -0.687995, 0.0665825, 0.447964, 0.233333, 1, 0.479937, -0.738746, 0.0782727, 0.466677, 0.266667, 1, 0.371386, -0.787404, 0.0798004, 0.485488, 0.3, 1, 0.268303, -0.820829, 0.0699353, 0.499362, 0.333333, 1, 0.195752, -0.837334, 0.0552404, 0.507446, 0.366667, 1, 0.154983, -0.844049, 0.0437357, 0.511516, 0.4, 1, 0.135547, -0.846687, 0.0382509, 0.513114, 0.433333, 1, 0.142392, -0.845799, 0.0401827, 0.512576, 0.466667, 1, 0.174541, -0.841029, 0.0492549, 0.509686, 0.5, 1, 0.2251, -0.83149, 0.0635222, 0.503904, 0.533333, 1, 0.266734, -0.821709, 0.0752712, 0.497977, 0.566667, 1, 0.291938, -0.814916, 0.082384, 0.49386, 0.6, 1, 0.304077, -0.811403, 0.0858094, 0.491731, 0.633333, 1, 0.306414, -0.810708, 0.0864687, 0.49131, 0.666667, 1, 0.30219, -0.811959, 0.0852774, 0.492068, 0.7, 1, 0.284976, -0.816859, 0.0804191, 0.495038, 0.733333, 1, 0.253786, -0.824941, 0.0716174, 0.499936, 0.766667, 1, 0.21317, -0.833969, 0.0601557, 0.505407, 0.8, 1, 0.174163, -0.841091, 0.0491481, 0.509723, 0.833333, 1, 0.154499, -0.844119, 0.0435987, 0.511558, 0.866667, 1, 0.187088, -0.838896, 0.0527954, 0.508393, 0.9, 1, 0.257715, -0.823979, 0.0727261, 0.499352, 0.933333, 1, 0.342162, -0.799337, 0.0965566, 0.484419, 0.966667, 1, 0.414237, -0.77194, 0.116896, 0.467816, 1, 1, 0.447768, -0.757012, 0.126359, 0.458769)
tracks/8/type = "scale_3d"
tracks/8/imported = true
tracks/8/enabled = true
tracks/8/path = NodePath("rig/Skeleton3D:DEF-lid1.T.L")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 1.05493, 0.900494, 1.05493, 0.0666667, 1, 1.16342, 0.749049, 1.16342, 0.1, 1, 1.27842, 0.616283, 1.27842, 0.133333, 1, 1.36318, 0.538303, 1.36318, 0.166667, 1, 1.39116, 0.516708, 1.39116, 0.2, 1, 1.35934, 0.541403, 1.35934, 0.233333, 1, 1.27702, 0.617274, 1.27702, 0.266667, 1, 1.15598, 0.761891, 1.15598, 0.3, 1, 1.0221, 0.966018, 1.0221, 0.333333, 1, 0.915313, 1.1936, 0.915314, 0.366667, 1, 0.851807, 1.38357, 0.851807, 0.4, 1, 0.81869, 1.49329, 0.81869, 0.433333, 1, 0.830329, 1.45542, 0.830329, 0.466667, 1, 0.882426, 1.2898, 0.882426, 0.5, 1, 0.952014, 1.10335, 0.952014, 0.533333, 1, 0.993708, 1.0141, 0.993708, 0.566667, 1, 1.01593, 0.969153, 1.01593, 0.6, 1, 1.02541, 0.951063, 1.02541, 0.633333, 1, 1.02708, 0.947961, 1.02708, 0.666667, 1, 1.02406, 0.953555, 1.02406, 0.7, 1, 1.01056, 0.979341, 1.01056, 0.733333, 1, 0.981256, 1.04, 0.981256, 0.766667, 1, 0.93508, 1.1487, 0.93508, 0.8, 1, 0.883975, 1.28096, 0.883975, 0.833333, 1, 0.852902, 1.37468, 0.852902, 0.866667, 1, 0.902237, 1.23137, 0.902237, 0.9, 1, 0.978753, 1.05318, 0.978753, 0.933333, 1, 1.04196, 0.922647, 1.04196, 0.966667, 1, 1.07334, 0.868009, 1.07334, 1, 1, 1.07633, 0.863196, 1.07633)
tracks/9/type = "position_3d"
tracks/9/imported = true
tracks/9/enabled = true
tracks/9/path = NodePath("rig/Skeleton3D:DEF-lid1.T.R")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = PackedFloat32Array(0, 1, -0.30289, 0.438881, 0.358675, 0.0333333, 1, -0.311734, 0.414588, 0.369148, 0.0666667, 1, -0.331538, 0.368431, 0.392599, 0.1, 1, -0.359043, 0.314986, 0.425171, 0.133333, 1, -0.387323, 0.268828, 0.458659, 0.166667, 1, -0.405777, 0.244535, 0.480512, 0.2, 1, -0.391127, 0.263463, 0.463164, 0.233333, 1, -0.362571, 0.309104, 0.429348, 0.266667, 1, -0.328374, 0.378448, 0.388852, 0.3, 1, -0.294634, 0.467669, 0.348899, 0.333333, 1, -0.265286, 0.57212, 0.314145, 0.366667, 1, -0.243035, 0.685613, 0.287796, 0.4, 1, -0.231925, 0.822412, 0.27464, 0.433333, 1, -0.235819, 0.994135, 0.279251, 0.466667, 1, -0.25377, 1.18934, 0.300508, 0.5, 1, -0.280023, 1.37352, 0.331597, 0.533333, 1, -0.300136, 1.49747, 0.355414, 0.566667, 1, -0.31231, 1.579, 0.36983, 0.6, 1, -0.31817, 1.62304, 0.376769, 0.633333, 1, -0.319302, 1.63329, 0.378109, 0.666667, 1, -0.317256, 1.61224, 0.375687, 0.7, 1, -0.308963, 1.54133, 0.365867, 0.733333, 1, -0.293902, 1.42238, 0.348032, 0.766667, 1, -0.273775, 1.25137, 0.324198, 0.8, 1, -0.253878, 1.03746, 0.300636, 0.833333, 1, -0.243098, 0.803019, 0.28787, 0.866667, 1, -0.260623, 0.619275, 0.308623, 0.9, 1, -0.295363, 0.471878, 0.349762, 0.933333, 1, -0.337707, 0.359639, 0.399905, 0.966667, 1, -0.376041, 0.285353, 0.445299, 1, 1, -0.396749, 0.25579, 0.469821)
tracks/10/type = "rotation_3d"
tracks/10/imported = true
tracks/10/enabled = true
tracks/10/path = NodePath("rig/Skeleton3D:DEF-lid1.T.R")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = PackedFloat32Array(0, 1, 0.272322, 0.820261, -0.076848, 0.497099, 0.0333333, 1, 0.303885, 0.811005, -0.0795152, 0.493561, 0.0666667, 1, 0.37817, 0.784908, -0.0817306, 0.48397, 0.1, 1, 0.474742, 0.741317, -0.0772875, 0.468076, 0.133333, 1, 0.562864, 0.690587, -0.0659008, 0.449368, 0.166667, 1, 0.607373, 0.65993, -0.0538267, 0.438968, 0.2, 1, 0.567061, 0.687995, -0.0665827, 0.447964, 0.233333, 1, 0.479938, 0.738746, -0.0782726, 0.466677, 0.266667, 1, 0.371386, 0.787404, -0.0798, 0.485488, 0.3, 1, 0.268303, 0.820829, -0.069935, 0.499362, 0.333333, 1, 0.195751, 0.837334, -0.0552403, 0.507446, 0.366667, 1, 0.154983, 0.844049, -0.0437358, 0.511516, 0.4, 1, 0.135547, 0.846686, -0.0382507, 0.513114, 0.433333, 1, 0.142392, 0.845799, -0.0401824, 0.512576, 0.466667, 1, 0.174541, 0.841029, -0.0492548, 0.509685, 0.5, 1, 0.225099, 0.83149, -0.0635222, 0.503904, 0.533333, 1, 0.266733, 0.82171, -0.0752708, 0.497977, 0.566667, 1, 0.291938, 0.814916, -0.0823838, 0.49386, 0.6, 1, 0.304077, 0.811403, -0.085809, 0.491731, 0.633333, 1, 0.306414, 0.810708, -0.0864678, 0.49131, 0.666667, 1, 0.302189, 0.81196, -0.0852774, 0.492068, 0.7, 1, 0.284976, 0.816859, -0.0804198, 0.495038, 0.733333, 1, 0.253787, 0.824941, -0.071618, 0.499935, 0.766667, 1, 0.21317, 0.833969, -0.060156, 0.505407, 0.8, 1, 0.174163, 0.841091, -0.0491482, 0.509723, 0.833333, 1, 0.154499, 0.844119, -0.0435991, 0.511558, 0.866667, 1, 0.187088, 0.838897, -0.0527957, 0.508393, 0.9, 1, 0.257715, 0.823979, -0.0727262, 0.499352, 0.933333, 1, 0.342161, 0.799338, -0.0965564, 0.484419, 0.966667, 1, 0.414237, 0.77194, -0.116896, 0.467816, 1, 1, 0.447769, 0.757012, -0.126358, 0.458769)
tracks/11/type = "scale_3d"
tracks/11/imported = true
tracks/11/enabled = true
tracks/11/path = NodePath("rig/Skeleton3D:DEF-lid1.T.R")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 1.05493, 0.900494, 1.05493, 0.0666667, 1, 1.16342, 0.749049, 1.16342, 0.1, 1, 1.27842, 0.616283, 1.27842, 0.133333, 1, 1.36318, 0.538304, 1.36318, 0.166667, 1, 1.39116, 0.516708, 1.39116, 0.2, 1, 1.35934, 0.541403, 1.35934, 0.233333, 1, 1.27702, 0.617274, 1.27702, 0.266667, 1, 1.15598, 0.761892, 1.15598, 0.3, 1, 1.0221, 0.966018, 1.0221, 0.333333, 1, 0.915313, 1.1936, 0.915313, 0.366667, 1, 0.851807, 1.38357, 0.851807, 0.4, 1, 0.818689, 1.49329, 0.818689, 0.433333, 1, 0.830329, 1.45542, 0.830329, 0.466667, 1, 0.882425, 1.2898, 0.882425, 0.5, 1, 0.952013, 1.10335, 0.952013, 0.533333, 1, 0.993707, 1.0141, 0.993707, 0.566667, 1, 1.01593, 0.969154, 1.01593, 0.6, 1, 1.02541, 0.951063, 1.02541, 0.633333, 1, 1.02708, 0.94796, 1.02709, 0.666667, 1, 1.02406, 0.953553, 1.02406, 0.7, 1, 1.01056, 0.979337, 1.01056, 0.733333, 1, 0.981256, 1.04, 0.981256, 0.766667, 1, 0.935079, 1.14871, 0.935079, 0.8, 1, 0.883974, 1.28097, 0.883974, 0.833333, 1, 0.852902, 1.37468, 0.852902, 0.866667, 1, 0.902237, 1.23137, 0.902237, 0.9, 1, 0.978753, 1.05318, 0.978753, 0.933333, 1, 1.04196, 0.922646, 1.04196, 0.966667, 1, 1.07335, 0.868008, 1.07335, 1, 1, 1.07633, 0.863196, 1.07633)
tracks/12/type = "position_3d"
tracks/12/imported = true
tracks/12/enabled = true
tracks/12/path = NodePath("rig/Skeleton3D:DEF-lip.B.L")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = PackedFloat32Array(0, 1, 2.27374e-13, 0.0911501, 0.425459, 0.0333333, 1, 4.54747e-14, 0.0861047, 0.437882, 0.0666667, 1, 0, 0.0765184, 0.465699, 0.1, 1, 0, 0.0654185, 0.504336, 0.133333, 1, 0, 0.0558322, 0.544059, 0.166667, 1, 0, 0.0507868, 0.569981, 0.2, 1, 0, 0.054718, 0.549403, 0.233333, 1, 0, 0.064197, 0.509291, 0.266667, 1, -5.29945e-13, 0.0782001, 0.461114, 0.3, 1, -1.90655e-12, 0.0953918, 0.413356, 0.333333, 1, -4.2333e-12, 0.114125, 0.371515, 0.366667, 1, -6.80281e-12, 0.131957, 0.33953, 0.4, 1, -9.83421e-12, 0.211721, 0.323109, 0.433333, 1, -1.36682e-11, 0.396195, 0.327561, 0.466667, 1, -1.84042e-11, 0.666862, 0.35152, 0.5, 1, -2.39003e-11, 0.943915, 0.386939, 0.533333, 1, -2.88157e-11, 1.1201, 0.413983, 0.566667, 1, -3.13096e-11, 1.23026, 0.430374, 0.6, 1, -3.15407e-11, 1.28745, 0.438496, 0.633333, 1, -2.96935e-11, 1.30138, 0.440588, 0.666667, 1, -2.59788e-11, 1.27844, 0.438749, 0.7, 1, -2.05815e-11, 1.19269, 0.428579, 0.733333, 1, -1.44355e-11, 1.04005, 0.408993, 0.766667, 1, -8.82057e-12, 0.813582, 0.382187, 0.8, 1, -4.38534e-12, 0.536311, 0.355438, 0.833333, 1, -1.14793e-12, 0.261212, 0.341106, 0.866667, 1, -4.76871e-14, 0.147503, 0.366015, 0.9, 1, -4.54743e-14, 0.098003, 0.414886, 0.933333, 1, -1.36424e-13, 0.0746925, 0.474365, 0.966667, 1, 0, 0.0592641, 0.528212, 1, 1, 0, 0.0531243, 0.5573)
tracks/13/type = "rotation_3d"
tracks/13/imported = true
tracks/13/enabled = true
tracks/13/path = NodePath("rig/Skeleton3D:DEF-lip.B.L")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = PackedFloat32Array(0, 1, -0.417605, -0.397508, -0.562312, 0.592789, 0.0333333, 1, -0.417932, -0.397209, -0.562752, 0.592342, 0.0666667, 1, -0.418519, -0.396668, -0.563543, 0.591536, 0.1, 1, -0.419162, -0.396076, -0.564409, 0.590652, 0.133333, 1, -0.419683, -0.395594, -0.565111, 0.589933, 0.2, 1, -0.419741, -0.395541, -0.565188, 0.589854, 0.233333, 1, -0.419229, -0.396014, -0.564499, 0.59056, 0.266667, 1, -0.418239, -0.396931, -0.563261, 0.591827, 0.3, 1, -0.416563, -0.398479, -0.56127, 0.593856, 0.333333, 1, -0.413943, -0.400883, -0.558243, 0.596916, 0.366667, 1, -0.409716, -0.40473, -0.553651, 0.601496, 0.4, 1, -0.405836, -0.408252, -0.550083, 0.605009, 0.433333, 1, -0.404137, -0.40987, -0.549846, 0.605268, 0.466667, 1, -0.404824, -0.409414, -0.553056, 0.602185, 0.5, 1, -0.40646, -0.408101, -0.557619, 0.59775, 0.533333, 1, -0.406992, -0.407737, -0.560472, 0.594962, 0.566667, 1, -0.407065, -0.407755, -0.56231, 0.593162, 0.6, 1, -0.407068, -0.407807, -0.563498, 0.591996, 0.633333, 1, -0.407273, -0.407645, -0.564283, 0.591218, 0.666667, 1, -0.407834, -0.407126, -0.564798, 0.590697, 0.7, 1, -0.408716, -0.406267, -0.564654, 0.590817, 0.733333, 1, -0.409559, -0.405415, -0.563812, 0.591622, 0.766667, 1, -0.410211, -0.404714, -0.562297, 0.59309, 0.8, 1, -0.410825, -0.404033, -0.560538, 0.594792, 0.833333, 1, -0.411856, -0.402974, -0.559379, 0.595887, 0.866667, 1, -0.41428, -0.400677, -0.560621, 0.594588, 0.9, 1, -0.416649, -0.398446, -0.562319, 0.592826, 0.933333, 1, -0.418463, -0.396738, -0.563851, 0.591236, 0.966667, 1, -0.419504, -0.39576, -0.564869, 0.590181, 1, 1, -0.419822, -0.395465, -0.565297, 0.589742)
tracks/14/type = "scale_3d"
tracks/14/imported = true
tracks/14/enabled = true
tracks/14/path = NodePath("rig/Skeleton3D:DEF-lip.B.L")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.985801, 1.02917, 0.985801, 0.0666667, 1, 0.956536, 1.0945, 0.956536, 0.133333, 1, 0.884555, 1.27858, 0.884555, 0.166667, 1, 0.864033, 1.33949, 0.864033, 0.2, 1, 0.880175, 1.29114, 0.880175, 0.233333, 1, 0.91511, 1.1969, 0.91511, 0.266667, 1, 0.962032, 1.08407, 0.962032, 0.3, 1, 1.01488, 0.972807, 1.01488, 0.333333, 1, 1.06835, 0.876143, 1.06835, 0.366667, 1, 1.11621, 0.80377, 1.11621, 0.4, 1, 1.14061, 0.768822, 1.14061, 0.433333, 1, 1.13033, 0.783657, 1.13033, 0.466667, 1, 1.08884, 0.845134, 1.08884, 0.5, 1, 1.03439, 0.934618, 1.03439, 0.533333, 1, 0.998242, 1.00448, 0.998242, 0.566667, 1, 0.977043, 1.04787, 0.977043, 0.6, 1, 0.966838, 1.0698, 0.966838, 0.633333, 1, 0.964388, 1.07522, 0.964388, 0.666667, 1, 0.967168, 1.06904, 0.967168, 0.7, 1, 0.980573, 1.04016, 0.980573, 0.733333, 1, 1.00678, 0.987603, 1.00678, 0.766667, 1, 1.04507, 0.917584, 1.04507, 0.8, 1, 1.08603, 0.848194, 1.08603, 0.833333, 1, 1.11153, 0.80939, 1.11153, 0.866667, 1, 1.07578, 0.864768, 1.07578, 0.9, 1, 1.01421, 0.977307, 1.01421, 0.933333, 1, 0.948984, 1.11557, 0.948984, 0.966667, 1, 0.897784, 1.24135, 0.897784, 1, 1, 0.873806, 1.30969, 0.873806)
tracks/15/type = "position_3d"
tracks/15/imported = true
tracks/15/enabled = true
tracks/15/path = NodePath("rig/Skeleton3D:DEF-lip.B.R")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = PackedFloat32Array(0, 1, 2.27374e-13, 0.0911501, 0.425459, 0.0333333, 1, 4.54747e-14, 0.0861047, 0.437882, 0.0666667, 1, 0, 0.0765184, 0.465699, 0.1, 1, 0, 0.0654185, 0.504336, 0.133333, 1, 0, 0.0558322, 0.544059, 0.166667, 1, 0, 0.0507868, 0.569981, 0.2, 1, 0, 0.054718, 0.549403, 0.233333, 1, 0, 0.064197, 0.509291, 0.266667, 1, -5.29945e-13, 0.0782001, 0.461114, 0.3, 1, -1.90655e-12, 0.0953918, 0.413356, 0.333333, 1, -4.2333e-12, 0.114125, 0.371515, 0.366667, 1, -6.80281e-12, 0.131957, 0.33953, 0.4, 1, -9.83421e-12, 0.211721, 0.323109, 0.433333, 1, -1.36682e-11, 0.396195, 0.327561, 0.466667, 1, -1.84042e-11, 0.666862, 0.35152, 0.5, 1, -2.39003e-11, 0.943915, 0.386939, 0.533333, 1, -2.88157e-11, 1.1201, 0.413983, 0.566667, 1, -3.13096e-11, 1.23026, 0.430374, 0.6, 1, -3.15407e-11, 1.28745, 0.438496, 0.633333, 1, -2.96935e-11, 1.30138, 0.440588, 0.666667, 1, -2.59788e-11, 1.27844, 0.438749, 0.7, 1, -2.05815e-11, 1.19269, 0.428579, 0.733333, 1, -1.44355e-11, 1.04005, 0.408993, 0.766667, 1, -8.82057e-12, 0.813582, 0.382187, 0.8, 1, -4.38534e-12, 0.536311, 0.355438, 0.833333, 1, -1.14793e-12, 0.261212, 0.341106, 0.866667, 1, -4.76871e-14, 0.147503, 0.366015, 0.9, 1, -4.54743e-14, 0.098003, 0.414886, 0.933333, 1, -1.36424e-13, 0.0746925, 0.474365, 0.966667, 1, 0, 0.0592641, 0.528212, 1, 1, 0, 0.0531243, 0.5573)
tracks/16/type = "rotation_3d"
tracks/16/imported = true
tracks/16/enabled = true
tracks/16/path = NodePath("rig/Skeleton3D:DEF-lip.B.R")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = PackedFloat32Array(0, 1, -0.417605, 0.397508, 0.562312, 0.592788, 0.0333333, 1, -0.417932, 0.397209, 0.562752, 0.592342, 0.0666667, 1, -0.418519, 0.396668, 0.563543, 0.591536, 0.1, 1, -0.419162, 0.396076, 0.564409, 0.590652, 0.133333, 1, -0.419683, 0.395594, 0.565111, 0.589933, 0.2, 1, -0.419741, 0.395541, 0.565188, 0.589854, 0.233333, 1, -0.419229, 0.396014, 0.564499, 0.59056, 0.266667, 1, -0.418239, 0.396931, 0.563261, 0.591827, 0.3, 1, -0.416563, 0.398479, 0.561271, 0.593856, 0.333333, 1, -0.413943, 0.400883, 0.558243, 0.596916, 0.366667, 1, -0.409716, 0.40473, 0.553651, 0.601496, 0.4, 1, -0.405836, 0.408252, 0.550083, 0.605009, 0.433333, 1, -0.404137, 0.40987, 0.549846, 0.605268, 0.466667, 1, -0.404824, 0.409413, 0.553056, 0.602185, 0.5, 1, -0.40646, 0.408101, 0.557619, 0.59775, 0.533333, 1, -0.406992, 0.407737, 0.560472, 0.594962, 0.566667, 1, -0.407065, 0.407755, 0.56231, 0.593162, 0.6, 1, -0.407068, 0.407807, 0.563498, 0.591996, 0.633333, 1, -0.407273, 0.407645, 0.564283, 0.591218, 0.666667, 1, -0.407833, 0.407127, 0.564798, 0.590697, 0.7, 1, -0.408716, 0.406267, 0.564654, 0.590816, 0.733333, 1, -0.409559, 0.405415, 0.563812, 0.591621, 0.766667, 1, -0.410211, 0.404714, 0.562297, 0.59309, 0.8, 1, -0.410825, 0.404033, 0.560538, 0.594792, 0.833333, 1, -0.411856, 0.402974, 0.559379, 0.595888, 0.866667, 1, -0.41428, 0.400677, 0.56062, 0.594588, 0.9, 1, -0.416648, 0.398446, 0.562319, 0.592826, 0.933333, 1, -0.418463, 0.396738, 0.563851, 0.591236, 0.966667, 1, -0.419504, 0.39576, 0.564869, 0.590181, 1, 1, -0.419822, 0.395465, 0.565297, 0.589742)
tracks/17/type = "scale_3d"
tracks/17/imported = true
tracks/17/enabled = true
tracks/17/path = NodePath("rig/Skeleton3D:DEF-lip.B.R")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.985801, 1.02917, 0.985801, 0.0666667, 1, 0.956536, 1.0945, 0.956536, 0.133333, 1, 0.884555, 1.27858, 0.884555, 0.166667, 1, 0.864033, 1.33949, 0.864033, 0.2, 1, 0.880175, 1.29114, 0.880175, 0.233333, 1, 0.91511, 1.1969, 0.91511, 0.266667, 1, 0.962032, 1.08407, 0.962032, 0.3, 1, 1.01488, 0.972807, 1.01488, 0.333333, 1, 1.06835, 0.876143, 1.06835, 0.366667, 1, 1.11621, 0.80377, 1.11621, 0.4, 1, 1.14061, 0.768822, 1.14061, 0.433333, 1, 1.13033, 0.783657, 1.13033, 0.466667, 1, 1.08885, 0.845134, 1.08884, 0.5, 1, 1.03439, 0.934619, 1.03439, 0.533333, 1, 0.998242, 1.00448, 0.998242, 0.566667, 1, 0.977043, 1.04787, 0.977043, 0.6, 1, 0.966838, 1.0698, 0.966838, 0.633333, 1, 0.964388, 1.07522, 0.964388, 0.666667, 1, 0.967168, 1.06904, 0.967168, 0.7, 1, 0.980573, 1.04016, 0.980573, 0.733333, 1, 1.00678, 0.987603, 1.00678, 0.766667, 1, 1.04507, 0.917584, 1.04507, 0.8, 1, 1.08603, 0.848193, 1.08603, 0.833333, 1, 1.11153, 0.80939, 1.11153, 0.866667, 1, 1.07578, 0.864768, 1.07578, 0.9, 1, 1.01421, 0.977307, 1.01421, 0.933333, 1, 0.948984, 1.11557, 0.948984, 0.966667, 1, 0.897784, 1.24135, 0.897784, 1, 1, 0.873806, 1.30969, 0.873806)
tracks/18/type = "position_3d"
tracks/18/imported = true
tracks/18/enabled = true
tracks/18/path = NodePath("rig/Skeleton3D:DEF-lip.T.L")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = PackedFloat32Array(0, 1, -2.27374e-13, 0.257975, 0.479661, 0.0333333, 1, -4.54747e-14, 0.243695, 0.493667, 0.0666667, 1, 0, 0.216564, 0.525028, 0.1, 1, -3.63798e-13, 0.185149, 0.568586, 0.133333, 1, -8.18545e-13, 0.158018, 0.613371, 0.166667, 1, -4.54747e-13, 0.143738, 0.642595, 0.2, 1, -4.54747e-13, 0.154864, 0.619395, 0.233333, 1, -4.54747e-13, 0.181692, 0.574173, 0.266667, 1, -4.61989e-13, 0.22374, 0.520018, 0.3, 1, -4.35322e-13, 0.280506, 0.466589, 0.333333, 1, -2.85205e-13, 0.351463, 0.420115, 0.366667, 1, -1.40908e-13, 0.436708, 0.384881, 0.4, 1, -2.73886e-13, 0.5682, 0.36729, 0.433333, 1, -3.26261e-13, 0.764249, 0.373459, 0.466667, 1, -2.54543e-13, 1.00559, 0.401891, 0.5, 1, -3.29621e-13, 1.23539, 0.443471, 0.533333, 1, -9.37602e-13, 1.38344, 0.475327, 0.566667, 1, -7.01253e-13, 1.47753, 0.494608, 0.6, 1, -4.33999e-13, 1.52511, 0.503887, 0.633333, 1, -4.53619e-13, 1.53187, 0.505678, 0.666667, 1, -5.82245e-13, 1.50178, 0.502436, 0.7, 1, -5.06066e-13, 1.41346, 0.489297, 0.733333, 1, -4.2521e-13, 1.26693, 0.465441, 0.766667, 1, -2.58789e-13, 1.05619, 0.433563, 0.8, 1, -6.05313e-14, 0.797338, 0.402049, 0.833333, 1, -1.87875e-14, 0.528581, 0.384974, 0.866667, 1, -1.85657e-13, 0.375324, 0.412726, 0.9, 1, -3.63798e-13, 0.27737, 0.467741, 0.933333, 1, -4.54747e-13, 0.211396, 0.534798, 0.966667, 1, -4.54747e-13, 0.167731, 0.595504, 1, 1, -4.54747e-13, 0.150354, 0.628298)
tracks/19/type = "rotation_3d"
tracks/19/imported = true
tracks/19/enabled = true
tracks/19/path = NodePath("rig/Skeleton3D:DEF-lip.T.L")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = PackedFloat32Array(0, 1, -0.527783, -0.471217, -0.497076, 0.502309, 0.0333333, 1, -0.526735, -0.472253, -0.496088, 0.503413, 0.0666667, 1, -0.524838, -0.474114, -0.494301, 0.505398, 0.1, 1, -0.522752, -0.476145, -0.492337, 0.507563, 0.133333, 1, -0.521051, -0.47779, -0.490734, 0.509316, 0.166667, 1, -0.520217, -0.478592, -0.489949, 0.510171, 0.2, 1, -0.520862, -0.477971, -0.490557, 0.509509, 0.233333, 1, -0.522534, -0.476357, -0.492131, 0.507789, 0.266667, 1, -0.525616, -0.473352, -0.495034, 0.504586, 0.3, 1, -0.530645, -0.46837, -0.49977, 0.499275, 0.333333, 1, -0.538282, -0.460608, -0.506961, 0.491003, 0.366667, 1, -0.550005, -0.448213, -0.518282, 0.477476, 0.4, 1, -0.559805, -0.437354, -0.528133, 0.465205, 0.433333, 1, -0.562458, -0.434318, -0.531519, 0.460974, 0.466667, 1, -0.557484, -0.439962, -0.527916, 0.465779, 0.5, 1, -0.548868, -0.449502, -0.521005, 0.474601, 0.533333, 1, -0.54362, -0.455176, -0.517258, 0.47931, 0.566667, 1, -0.540456, -0.458557, -0.515353, 0.481709, 0.6, 1, -0.538454, -0.460686, -0.51435, 0.482991, 0.633333, 1, -0.536947, -0.46228, -0.513548, 0.483998, 0.666667, 1, -0.535547, -0.463745, -0.512509, 0.485247, 0.7, 1, -0.535023, -0.464276, -0.511666, 0.486205, 0.733333, 1, -0.535839, -0.463405, -0.511659, 0.486145, 0.766667, 1, -0.537991, -0.461131, -0.512595, 0.484943, 0.8, 1, -0.540329, -0.458644, -0.513504, 0.483739, 0.833333, 1, -0.540581, -0.458337, -0.512361, 0.48496, 0.9, 1, -0.529087, -0.469982, -0.499234, 0.499949, 0.933333, 1, -0.524428, -0.474534, -0.494191, 0.505536, 0.966667, 1, -0.521637, -0.477224, -0.491287, 0.508713, 1, 1, -0.520596, -0.478228, -0.490306, 0.509782)
tracks/20/type = "scale_3d"
tracks/20/imported = true
tracks/20/enabled = true
tracks/20/path = NodePath("rig/Skeleton3D:DEF-lip.T.L")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.985887, 1.02899, 0.985887, 0.0666667, 1, 0.956749, 1.094, 0.956749, 0.133333, 1, 0.884942, 1.27746, 0.884942, 0.166667, 1, 0.86444, 1.33823, 0.86444, 0.2, 1, 0.880567, 1.28999, 0.880567, 0.233333, 1, 0.915441, 1.19603, 0.915441, 0.266667, 1, 0.962169, 1.08372, 0.962169, 0.3, 1, 1.01451, 0.973441, 1.01451, 0.333333, 1, 1.06688, 0.878548, 1.06688, 0.366667, 1, 1.11168, 0.810196, 1.11168, 0.4, 1, 1.13249, 0.779817, 1.13249, 0.433333, 1, 1.1212, 0.796388, 1.1212, 0.466667, 1, 1.08183, 0.855963, 1.08183, 0.5, 1, 1.0305, 0.941678, 1.0305, 0.533333, 1, 0.995794, 1.00935, 0.995794, 0.566667, 1, 0.975388, 1.0514, 0.975388, 0.6, 1, 0.965641, 1.07245, 0.965642, 0.633333, 1, 0.963513, 1.07718, 0.963513, 0.666667, 1, 0.966567, 1.07038, 0.966567, 0.7, 1, 0.980049, 1.04128, 0.980049, 0.733333, 1, 1.00607, 0.98899, 1.00607, 0.766667, 1, 1.04386, 0.91966, 1.04386, 0.8, 1, 1.08425, 0.850972, 1.08425, 0.833333, 1, 1.10964, 0.812142, 1.10964, 0.866667, 1, 1.07495, 0.86607, 1.07495, 0.9, 1, 1.01409, 0.977455, 1.01409, 0.933333, 1, 0.949235, 1.11495, 0.949235, 0.966667, 1, 0.898153, 1.24033, 0.898153, 1, 1, 0.874205, 1.3085, 0.874205)
tracks/21/type = "position_3d"
tracks/21/imported = true
tracks/21/enabled = true
tracks/21/path = NodePath("rig/Skeleton3D:DEF-lip.T.R")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = PackedFloat32Array(0, 1, -2.27374e-13, 0.257975, 0.479661, 0.0333333, 1, -4.54747e-14, 0.243695, 0.493667, 0.0666667, 1, 0, 0.216564, 0.525028, 0.1, 1, -3.63798e-13, 0.185149, 0.568586, 0.133333, 1, -8.18545e-13, 0.158018, 0.613371, 0.166667, 1, -4.54747e-13, 0.143738, 0.642595, 0.2, 1, -4.54747e-13, 0.154864, 0.619395, 0.233333, 1, -4.54747e-13, 0.181692, 0.574173, 0.266667, 1, -4.61989e-13, 0.22374, 0.520018, 0.3, 1, -4.35322e-13, 0.280506, 0.466589, 0.333333, 1, -2.85205e-13, 0.351463, 0.420115, 0.366667, 1, -1.40908e-13, 0.436708, 0.384881, 0.4, 1, -2.73886e-13, 0.5682, 0.36729, 0.433333, 1, -3.26261e-13, 0.764249, 0.373459, 0.466667, 1, -2.54543e-13, 1.00559, 0.401891, 0.5, 1, -3.29621e-13, 1.23539, 0.443471, 0.533333, 1, -9.37602e-13, 1.38344, 0.475327, 0.566667, 1, -7.01253e-13, 1.47753, 0.494608, 0.6, 1, -4.33999e-13, 1.52511, 0.503887, 0.633333, 1, -4.53619e-13, 1.53187, 0.505678, 0.666667, 1, -5.82245e-13, 1.50178, 0.502436, 0.7, 1, -5.06066e-13, 1.41346, 0.489297, 0.733333, 1, -4.2521e-13, 1.26693, 0.465441, 0.766667, 1, -2.58789e-13, 1.05619, 0.433563, 0.8, 1, -6.05313e-14, 0.797338, 0.402049, 0.833333, 1, -1.87875e-14, 0.528581, 0.384974, 0.866667, 1, -1.85657e-13, 0.375324, 0.412726, 0.9, 1, -3.63798e-13, 0.27737, 0.467741, 0.933333, 1, -4.54747e-13, 0.211396, 0.534798, 0.966667, 1, -4.54747e-13, 0.167731, 0.595504, 1, 1, -4.54747e-13, 0.150354, 0.628298)
tracks/22/type = "rotation_3d"
tracks/22/imported = true
tracks/22/enabled = true
tracks/22/path = NodePath("rig/Skeleton3D:DEF-lip.T.R")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = PackedFloat32Array(0, 1, -0.527783, 0.471217, 0.497076, 0.502309, 0.0333333, 1, -0.526735, 0.472253, 0.496088, 0.503413, 0.0666667, 1, -0.524838, 0.474114, 0.494301, 0.505398, 0.1, 1, -0.522752, 0.476146, 0.492337, 0.507563, 0.133333, 1, -0.52105, 0.47779, 0.490734, 0.509316, 0.166667, 1, -0.520217, 0.478592, 0.489949, 0.510171, 0.2, 1, -0.520862, 0.477971, 0.490557, 0.50951, 0.233333, 1, -0.522534, 0.476357, 0.492131, 0.507789, 0.266667, 1, -0.525616, 0.473353, 0.495034, 0.504586, 0.3, 1, -0.530645, 0.46837, 0.49977, 0.499275, 0.333333, 1, -0.538282, 0.460607, 0.506961, 0.491003, 0.366667, 1, -0.550005, 0.448213, 0.518283, 0.477475, 0.4, 1, -0.559805, 0.437354, 0.528133, 0.465205, 0.433333, 1, -0.562458, 0.434318, 0.531519, 0.460974, 0.466667, 1, -0.557484, 0.439962, 0.527916, 0.465779, 0.5, 1, -0.548868, 0.449502, 0.521005, 0.474601, 0.533333, 1, -0.543619, 0.455176, 0.517257, 0.47931, 0.566667, 1, -0.540456, 0.458557, 0.515353, 0.48171, 0.6, 1, -0.538454, 0.460686, 0.51435, 0.482991, 0.633333, 1, -0.536947, 0.46228, 0.513548, 0.483999, 0.666667, 1, -0.535547, 0.463745, 0.512509, 0.485247, 0.7, 1, -0.535024, 0.464275, 0.511666, 0.486205, 0.733333, 1, -0.53584, 0.463405, 0.511659, 0.486145, 0.766667, 1, -0.537991, 0.461132, 0.512595, 0.484943, 0.8, 1, -0.540329, 0.458644, 0.513504, 0.483739, 0.833333, 1, -0.540581, 0.458337, 0.512361, 0.48496, 0.9, 1, -0.529087, 0.469982, 0.499234, 0.499949, 0.933333, 1, -0.524428, 0.474534, 0.494191, 0.505537, 0.966667, 1, -0.521637, 0.477224, 0.491287, 0.508713, 1, 1, -0.520596, 0.478228, 0.490306, 0.509782)
tracks/23/type = "scale_3d"
tracks/23/imported = true
tracks/23/enabled = true
tracks/23/path = NodePath("rig/Skeleton3D:DEF-lip.T.R")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.985888, 1.02899, 0.985888, 0.0666667, 1, 0.956749, 1.094, 0.956749, 0.133333, 1, 0.884942, 1.27746, 0.884942, 0.166667, 1, 0.86444, 1.33823, 0.86444, 0.2, 1, 0.880568, 1.28999, 0.880568, 0.233333, 1, 0.915441, 1.19602, 0.915441, 0.266667, 1, 0.96217, 1.08372, 0.96217, 0.3, 1, 1.01451, 0.973441, 1.01451, 0.333333, 1, 1.06688, 0.878548, 1.06688, 0.366667, 1, 1.11168, 0.810196, 1.11168, 0.4, 1, 1.13249, 0.779818, 1.13249, 0.433333, 1, 1.1212, 0.796388, 1.1212, 0.466667, 1, 1.08183, 0.855963, 1.08183, 0.5, 1, 1.0305, 0.941678, 1.0305, 0.533333, 1, 0.995794, 1.00935, 0.995794, 0.566667, 1, 0.975389, 1.0514, 0.975389, 0.6, 1, 0.965642, 1.07245, 0.965642, 0.633333, 1, 0.963514, 1.07718, 0.963514, 0.666667, 1, 0.966567, 1.07038, 0.966567, 0.7, 1, 0.980049, 1.04128, 0.980049, 0.733333, 1, 1.00607, 0.98899, 1.00607, 0.766667, 1, 1.04386, 0.91966, 1.04386, 0.8, 1, 1.08425, 0.850972, 1.08425, 0.833333, 1, 1.10965, 0.812142, 1.10965, 0.866667, 1, 1.07495, 0.86607, 1.07495, 0.9, 1, 1.01409, 0.977455, 1.01409, 0.933333, 1, 0.949236, 1.11495, 0.949236, 0.966667, 1, 0.898154, 1.24033, 0.898154, 1, 1, 0.874205, 1.3085, 0.874205)
tracks/24/type = "position_3d"
tracks/24/imported = true
tracks/24/enabled = true
tracks/24/path = NodePath("rig/Skeleton3D:DEF-bone.01")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = PackedFloat32Array(0, 1, 0, 0, 0, 0.366667, 1, 0, 0, 0, 0.4, 1, -7.33699e-16, 0.0730151, 0, 0.433333, 1, -2.68082e-15, 0.266786, 0, 0.466667, 1, -5.62268e-15, 0.559549, 0, 0.5, 1, -8.64215e-15, 0.860037, 0, 0.533333, 1, -1.05397e-14, 1.04888, 0, 0.566667, 1, -1.17142e-14, 1.16576, 0, 0.6, 1, -1.23123e-14, 1.22528, 0, 0.633333, 1, -1.24437e-14, 1.23835, 0, 0.666667, 1, -1.2181e-14, 1.21221, 0, 0.7, 1, -1.12475e-14, 1.11931, 0, 0.733333, 1, -9.59095e-15, 0.954457, 0, 0.766667, 1, -7.13242e-15, 0.709793, 0, 0.8, 1, -4.13881e-15, 0.41188, 0, 0.833333, 1, -1.22283e-15, 0.121692, 0, 0.866667, 1, -2.44566e-16, 0.0243383, 0, 0.9, 1, 0, 0, 0, 1, 1, 0, 0, 0)
tracks/25/type = "rotation_3d"
tracks/25/imported = true
tracks/25/enabled = true
tracks/25/path = NodePath("rig/Skeleton3D:DEF-bone.01")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = PackedFloat32Array(0, 1, -2.98023e-08, 1.19209e-07, 0, 1)
tracks/26/type = "scale_3d"
tracks/26/imported = true
tracks/26/enabled = true
tracks/26/path = NodePath("rig/Skeleton3D:DEF-bone.01")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 1.0292, 0.944647, 1.0292, 0.0666667, 1, 1.09458, 0.839477, 1.09458, 0.1, 1, 1.18539, 0.717701, 1.18539, 0.133333, 1, 1.27876, 0.612531, 1.27876, 0.166667, 1, 1.33969, 0.557178, 1.33969, 0.2, 1, 1.29132, 0.600307, 1.29132, 0.233333, 1, 1.19704, 0.7043, 1.19704, 0.266667, 1, 1.08414, 0.862302, 1.08414, 0.3, 1, 0.972743, 1.06559, 0.972743, 0.333333, 1, 0.87585, 1.30359, 0.87585, 0.366667, 1, 0.802388, 1.56218, 0.802388, 0.4, 1, 0.765708, 1.70752, 0.765708, 0.433333, 1, 0.778563, 1.65728, 0.778563, 0.466667, 1, 0.837828, 1.43499, 0.837828, 0.5, 1, 0.924505, 1.16999, 0.924505, 0.533333, 1, 0.990908, 1.02212, 0.990908, 0.566667, 1, 1.0311, 0.94158, 1.0311, 0.6, 1, 1.05045, 0.906308, 1.05045, 0.633333, 1, 1.05418, 0.899873, 1.05418, 0.666667, 1, 1.04743, 0.911485, 1.04743, 0.7, 1, 1.02005, 0.961579, 1.02005, 0.733333, 1, 0.970327, 1.06617, 0.970326, 0.766667, 1, 0.903877, 1.23399, 0.903877, 0.8, 1, 0.838185, 1.42539, 0.838185, 0.833333, 1, 0.802594, 1.55242, 0.802594, 0.866667, 1, 0.860454, 1.35558, 0.860454, 0.933333, 1, 1.11495, 0.819445, 1.11495, 0.966667, 1, 1.24151, 0.650182, 1.24151, 1, 1, 1.30988, 0.582823, 1.30988)
tracks/27/type = "position_3d"
tracks/27/imported = true
tracks/27/enabled = true
tracks/27/path = NodePath("rig/Skeleton3D:DEF-lid2.B.L")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = PackedFloat32Array(0, 1, 0.279235, 0.35591, 0.395649, 0.0333333, 1, 0.287389, 0.33621, 0.407203, 0.0666667, 1, 0.305645, 0.298779, 0.433071, 0.1, 1, 0.331003, 0.255437, 0.469, 0.133333, 1, 0.357074, 0.218006, 0.50594, 0.166667, 1, 0.374087, 0.198305, 0.530046, 0.2, 1, 0.360581, 0.213655, 0.51091, 0.233333, 1, 0.334255, 0.250668, 0.473608, 0.266667, 1, 0.302728, 0.306902, 0.428938, 0.3, 1, 0.271624, 0.379255, 0.384866, 0.333333, 1, 0.244568, 0.46396, 0.34653, 0.366667, 1, 0.224055, 0.555997, 0.317465, 0.4, 1, 0.213812, 0.680738, 0.302952, 0.433333, 1, 0.217402, 0.856629, 0.308038, 0.466667, 1, 0.233951, 1.07028, 0.331486, 0.5, 1, 0.258154, 1.27645, 0.36578, 0.533333, 1, 0.276696, 1.41266, 0.392053, 0.566667, 1, 0.287919, 1.50088, 0.407955, 0.6, 1, 0.293321, 1.54785, 0.415609, 0.633333, 1, 0.294365, 1.55863, 0.417087, 0.666667, 1, 0.292479, 1.53661, 0.414415, 0.7, 1, 0.284834, 1.46155, 0.403583, 0.733333, 1, 0.270949, 1.33392, 0.383909, 0.766667, 1, 0.252394, 1.14898, 0.357619, 0.8, 1, 0.23405, 0.91919, 0.331628, 0.833333, 1, 0.224112, 0.674213, 0.317546, 0.866667, 1, 0.240269, 0.506801, 0.340438, 0.9, 1, 0.272296, 0.382669, 0.385818, 0.933333, 1, 0.311333, 0.291649, 0.44113, 0.966667, 1, 0.346673, 0.231406, 0.491203, 1, 1, 0.365764, 0.207433, 0.518253)
tracks/28/type = "rotation_3d"
tracks/28/imported = true
tracks/28/enabled = true
tracks/28/path = NodePath("rig/Skeleton3D:DEF-lid2.B.L")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = PackedFloat32Array(0, 1, 0.651517, -0.593384, -0.445735, 0.157294, 0.0333333, 1, 0.647637, -0.600424, -0.440685, 0.160791, 0.0666667, 1, 0.640824, -0.612903, -0.430667, 0.167988, 0.1, 1, 0.633609, -0.626138, -0.418518, 0.177013, 0.133333, 1, 0.628014, -0.636478, -0.407565, 0.185432, 0.166667, 1, 0.625526, -0.641288, -0.401587, 0.19025, 0.2, 1, 0.627435, -0.637581, -0.406257, 0.186473, 0.233333, 1, 0.63289, -0.627484, -0.417137, 0.178075, 0.266667, 1, 0.642598, -0.609942, -0.432742, 0.166653, 0.3, 1, 0.656618, -0.584388, -0.45138, 0.153623, 0.333333, 1, 0.674435, -0.550851, -0.471203, 0.140245, 0.366667, 1, 0.694344, -0.511349, -0.490004, 0.127692, 0.4, 1, 0.704911, -0.488674, -0.499685, 0.120883, 0.433333, 1, 0.701301, -0.496604, -0.496353, 0.123269, 0.466667, 1, 0.68477, -0.530885, -0.480976, 0.133843, 0.5, 1, 0.664147, -0.570252, -0.460431, 0.147393, 0.533333, 1, 0.653257, -0.59035, -0.447645, 0.156064, 0.566667, 1, 0.647442, -0.600791, -0.440395, 0.161002, 0.6, 1, 0.644991, -0.605186, -0.437128, 0.163268, 0.633333, 1, 0.644557, -0.605969, -0.436524, 0.163693, 0.666667, 1, 0.645336, -0.604557, -0.437615, 0.162925, 0.7, 1, 0.648813, -0.598294, -0.442242, 0.159708, 0.733333, 1, 0.656489, -0.584472, -0.451522, 0.153438, 0.766667, 1, 0.669323, -0.56083, -0.4655, 0.144173, 0.8, 1, 0.683876, -0.532478, -0.480405, 0.134136, 0.833333, 1, 0.69352, -0.512884, -0.489495, 0.127967, 0.866667, 1, 0.678546, -0.543024, -0.47512, 0.137698, 0.9, 1, 0.657865, -0.582565, -0.451957, 0.153513, 0.933333, 1, 0.640003, -0.614755, -0.42855, 0.169757, 0.966667, 1, 0.629848, -0.632994, -0.411565, 0.182275, 1, 1, 0.626627, -0.639127, -0.404375, 0.187978)
tracks/29/type = "scale_3d"
tracks/29/imported = true
tracks/29/enabled = true
tracks/29/path = NodePath("rig/Skeleton3D:DEF-lid2.B.L")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = PackedFloat32Array(0, 1, 0.999999, 1, 1, 0.0333333, 1, 0.988111, 1.02432, 0.988111, 0.0666667, 1, 0.962271, 1.08116, 0.962271, 0.1, 1, 0.928257, 1.16271, 0.928257, 0.133333, 1, 0.895116, 1.24855, 0.895116, 0.166667, 1, 0.87516, 1.30564, 0.87516, 0.2, 1, 0.890883, 1.26027, 0.890883, 0.233333, 1, 0.924105, 1.17339, 0.924105, 0.266667, 1, 0.966676, 1.07283, 0.966676, 0.3, 1, 1.01093, 0.97953, 1.01093, 0.333333, 1, 1.04969, 0.907561, 1.04969, 0.366667, 1, 1.07308, 0.86875, 1.07308, 0.4, 1, 1.08258, 0.853272, 1.08258, 0.433333, 1, 1.07908, 0.858923, 1.07908, 0.466667, 1, 1.06228, 0.886783, 1.06228, 0.5, 1, 1.03075, 0.941225, 1.03075, 0.533333, 1, 1.00369, 0.993178, 1.0037, 0.566667, 1, 0.987354, 1.02597, 0.987354, 0.6, 1, 0.979515, 1.04227, 0.979515, 0.633333, 1, 0.978012, 1.04548, 0.978012, 0.666667, 1, 0.980726, 1.03969, 0.980726, 0.7, 1, 0.99183, 1.01664, 0.99183, 0.733333, 1, 1.01212, 0.976776, 1.01212, 0.766667, 1, 1.03831, 0.928368, 1.03831, 0.8, 1, 1.06289, 0.885239, 1.06289, 0.833333, 1, 1.07411, 0.866768, 1.07411, 0.866667, 1, 1.05485, 0.898922, 1.05485, 0.9, 1, 1.00977, 0.983807, 1.00977, 0.933333, 1, 0.955207, 1.10023, 0.955207, 0.966667, 1, 0.907843, 1.21394, 0.907843, 1, 1, 0.884708, 1.27762, 0.884708)
tracks/30/type = "position_3d"
tracks/30/imported = true
tracks/30/enabled = true
tracks/30/path = NodePath("rig/Skeleton3D:DEF-lid3.B.L")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = PackedFloat32Array(0, 1, 0.21764, 0.331942, 0.467069, 0.0333333, 1, 0.223996, 0.313568, 0.480707, 0.0666667, 1, 0.238225, 0.278658, 0.511245, 0.1, 1, 0.257989, 0.238235, 0.55366, 0.133333, 1, 0.27831, 0.203325, 0.597268, 0.166667, 1, 0.29157, 0.184951, 0.625725, 0.2, 1, 0.281043, 0.199267, 0.603135, 0.233333, 1, 0.260524, 0.233787, 0.5591, 0.266667, 1, 0.235952, 0.286235, 0.506366, 0.3, 1, 0.211708, 0.353715, 0.454338, 0.366667, 1, 0.174632, 0.518555, 0.37477, 0.4, 1, 0.166649, 0.639812, 0.357638, 0.433333, 1, 0.169447, 0.816907, 0.363642, 0.466667, 1, 0.182345, 1.03588, 0.391323, 0.5, 1, 0.20121, 1.2484, 0.431807, 0.533333, 1, 0.215662, 1.38816, 0.462822, 0.566667, 1, 0.224409, 1.47831, 0.481595, 0.6, 1, 0.22862, 1.52612, 0.490631, 0.633333, 1, 0.229433, 1.53706, 0.492376, 0.666667, 1, 0.227963, 1.51477, 0.489222, 0.7, 1, 0.222004, 1.4385, 0.476434, 0.733333, 1, 0.211182, 1.30837, 0.453209, 0.766667, 1, 0.19672, 1.1194, 0.422173, 0.8, 1, 0.182423, 0.885027, 0.39149, 0.833333, 1, 0.174677, 0.637004, 0.374866, 0.866667, 1, 0.18727, 0.474311, 0.401891, 0.9, 1, 0.212232, 0.356899, 0.455462, 0.933333, 1, 0.242658, 0.272008, 0.520758, 0.966667, 1, 0.270203, 0.215823, 0.579871, 1, 1, 0.285083, 0.193464, 0.611804)
tracks/31/type = "rotation_3d"
tracks/31/imported = true
tracks/31/enabled = true
tracks/31/path = NodePath("rig/Skeleton3D:DEF-lid3.B.L")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = PackedFloat32Array(0, 1, -0.630992, 0.724555, 0.263042, 0.0876211, 0.0333333, 1, -0.634718, 0.723375, 0.259093, 0.0820521, 0.0666667, 1, -0.641673, 0.721209, 0.251083, 0.0712189, 0.1, 1, -0.649514, 0.718722, 0.24118, 0.0583397, 0.133333, 1, -0.656084, 0.716584, 0.232077, 0.0469207, 0.166667, 1, -0.659445, 0.715504, 0.226991, 0.0407514, 0.2, 1, -0.656833, 0.716343, 0.230971, 0.0455672, 0.233333, 1, -0.65036, 0.718456, 0.240034, 0.0568917, 0.266667, 1, -0.64017, 0.721767, 0.252656, 0.0734822, 0.3, 1, -0.626502, 0.726099, 0.267269, 0.0940419, 0.333333, 1, -0.609776, 0.731259, 0.282297, 0.117225, 0.366667, 1, -0.590757, 0.737215, 0.295751, 0.141604, 0.4, 1, -0.579766, 0.740427, 0.302618, 0.155115, 0.433333, 1, -0.583594, 0.739362, 0.300215, 0.150441, 0.466667, 1, -0.6002, 0.734348, 0.289283, 0.129651, 0.5, 1, -0.61927, 0.728278, 0.274275, 0.104351, 0.533333, 1, -0.629486, 0.725081, 0.264469, 0.089784, 0.566667, 1, -0.634925, 0.723313, 0.258858, 0.0817371, 0.6, 1, -0.637275, 0.722553, 0.256296, 0.0781674, 0.633333, 1, -0.637701, 0.722418, 0.255818, 0.0775105, 0.666667, 1, -0.636931, 0.722662, 0.256682, 0.0786948, 0.7, 1, -0.633583, 0.723736, 0.260314, 0.0837617, 0.733333, 1, -0.626472, 0.726043, 0.267453, 0.0941455, 0.766667, 1, -0.614735, 0.729832, 0.277927, 0.110466, 0.8, 1, -0.600916, 0.734031, 0.288981, 0.128793, 0.833333, 1, -0.591492, 0.736881, 0.295523, 0.140744, 0.866667, 1, -0.606011, 0.732496, 0.285094, 0.122152, 0.9, 1, -0.625819, 0.726496, 0.267508, 0.0948311, 0.933333, 1, -0.642971, 0.720902, 0.249259, 0.0689856, 0.966667, 1, -0.653758, 0.717327, 0.235445, 0.0510702, 1, 1, -0.657899, 0.715998, 0.229375, 0.043624)
tracks/32/type = "scale_3d"
tracks/32/imported = true
tracks/32/enabled = true
tracks/32/path = NodePath("rig/Skeleton3D:DEF-lid3.B.L")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.985949, 1.02886, 0.985949, 0.0666667, 1, 0.956902, 1.09365, 0.956902, 0.133333, 1, 0.885221, 1.27666, 0.885221, 0.166667, 1, 0.864733, 1.33732, 0.864733, 0.2, 1, 0.88085, 1.28916, 0.88085, 0.233333, 1, 0.91568, 1.19539, 0.91568, 0.266667, 1, 0.962328, 1.08335, 0.962328, 0.3, 1, 1.01463, 0.973228, 1.01463, 0.333333, 1, 1.06714, 0.878119, 1.06714, 0.366667, 1, 1.11377, 0.807249, 1.11377, 0.4, 1, 1.13813, 0.772197, 1.13813, 0.433333, 1, 1.1296, 0.784503, 1.1296, 0.466667, 1, 1.09116, 0.841377, 1.09116, 0.5, 1, 1.03936, 0.925691, 1.03936, 0.533333, 1, 1.00492, 0.991069, 1.00492, 0.566667, 1, 0.985102, 1.03074, 0.985102, 0.6, 1, 0.975965, 1.04987, 0.975965, 0.633333, 1, 0.974247, 1.05357, 0.974247, 0.666667, 1, 0.97735, 1.04689, 0.97735, 0.7, 1, 0.990304, 1.01981, 0.990304, 0.733333, 1, 1.01541, 0.970783, 1.01541, 0.766667, 1, 1.05181, 0.905632, 1.05181, 0.8, 1, 1.09024, 0.841584, 1.09024, 0.833333, 1, 1.11299, 0.807274, 1.11299, 0.866667, 1, 1.07675, 0.863223, 1.07675, 0.9, 1, 1.01501, 0.975776, 1.01501, 0.933333, 1, 0.949689, 1.11392, 0.949689, 0.966667, 1, 0.89842, 1.23959, 0.89842, 1, 1, 0.874493, 1.30764, 0.874493)
tracks/33/type = "position_3d"
tracks/33/imported = true
tracks/33/enabled = true
tracks/33/path = NodePath("rig/Skeleton3D:DEF-lid4.B.L")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/keys = PackedFloat32Array(0, 1, 0.127494, 0.338073, 0.492466, 0.0333333, 1, 0.131217, 0.319359, 0.506847, 0.0666667, 1, 0.139552, 0.283804, 0.539045, 0.1, 1, 0.15113, 0.242635, 0.583766, 0.133333, 1, 0.163034, 0.20708, 0.629746, 0.166667, 1, 0.170802, 0.188367, 0.65975, 0.2, 1, 0.164635, 0.202947, 0.635931, 0.233333, 1, 0.152615, 0.238105, 0.589502, 0.266667, 1, 0.138221, 0.291521, 0.5339, 0.3, 1, 0.124019, 0.360248, 0.479043, 0.366667, 1, 0.1023, 0.528131, 0.395149, 0.4, 1, 0.0976231, 0.65028, 0.377085, 0.433333, 1, 0.099262, 0.827067, 0.383416, 0.466667, 1, 0.106818, 1.04468, 0.412602, 0.5, 1, 0.117869, 1.25558, 0.455288, 0.533333, 1, 0.126335, 1.39443, 0.487989, 0.566667, 1, 0.131459, 1.48408, 0.507782, 0.6, 1, 0.133925, 1.53168, 0.517309, 0.633333, 1, 0.134402, 1.54258, 0.51915, 0.666667, 1, 0.133541, 1.52036, 0.515824, 0.7, 1, 0.13005, 1.4444, 0.502341, 0.733333, 1, 0.123711, 1.3149, 0.477853, 0.766667, 1, 0.115239, 1.12697, 0.445129, 0.8, 1, 0.106863, 0.893765, 0.412778, 0.833333, 1, 0.102326, 0.646521, 0.39525, 0.866667, 1, 0.109703, 0.482621, 0.423744, 0.9, 1, 0.124326, 0.36349, 0.480229, 0.933333, 1, 0.142149, 0.277032, 0.549076, 0.966667, 1, 0.158285, 0.219809, 0.611402, 1, 1, 0.167002, 0.197036, 0.645072)
tracks/34/type = "rotation_3d"
tracks/34/imported = true
tracks/34/enabled = true
tracks/34/path = NodePath("rig/Skeleton3D:DEF-lid4.B.L")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/keys = PackedFloat32Array(0, 1, -0.579754, 0.769356, 0.095577, 0.250681, 0.0333333, 1, -0.589916, 0.763885, 0.0939001, 0.244258, 0.0666667, 1, -0.60851, 0.753575, 0.089879, 0.231868, 0.1, 1, -0.628922, 0.741724, 0.0842144, 0.217281, 0.133333, 1, -0.645533, 0.731651, 0.0784416, 0.204503, 0.166667, 1, -0.653694, 0.726617, 0.0749146, 0.197738, 0.2, 1, -0.647374, 0.730519, 0.0776919, 0.20301, 0.233333, 1, -0.63107, 0.740452, 0.0834987, 0.215661, 0.266667, 1, -0.604344, 0.756025, 0.0904652, 0.234544, 0.3, 1, -0.567408, 0.775912, 0.096798, 0.258146, 0.333333, 1, -0.521393, 0.798101, 0.10081, 0.284643, 0.366667, 1, -0.471093, 0.819096, 0.100907, 0.311402, 0.4, 1, -0.443021, 0.829262, 0.100182, 0.325608, 0.433333, 1, -0.452898, 0.825804, 0.100421, 0.320698, 0.466667, 1, -0.495814, 0.80925, 0.101013, 0.298461, 0.5, 1, -0.547468, 0.78584, 0.0992385, 0.269975, 0.533333, 1, -0.575587, 0.771603, 0.0959907, 0.253208, 0.566667, 1, -0.590463, 0.763591, 0.0937837, 0.243899, 0.6, 1, -0.596851, 0.760062, 0.0926414, 0.239776, 0.633333, 1, -0.598004, 0.759421, 0.0924143, 0.23902, 0.666667, 1, -0.595924, 0.760575, 0.0928238, 0.240383, 0.7, 1, -0.586827, 0.765563, 0.0944353, 0.246229, 0.733333, 1, -0.567298, 0.775926, 0.097056, 0.258249, 0.766667, 1, -0.535127, 0.791827, 0.0996793, 0.27697, 0.8, 1, -0.497528, 0.808498, 0.101311, 0.297546, 0.833333, 1, -0.472611, 0.818496, 0.101256, 0.310566, 0.866667, 1, -0.511336, 0.802596, 0.100944, 0.290147, 0.9, 1, -0.565444, 0.777068, 0.0963247, 0.259153, 0.933333, 1, -0.611663, 0.751909, 0.088611, 0.229456, 0.966667, 1, -0.639778, 0.735155, 0.0806876, 0.209095, 1, 1, -0.649977, 0.728912, 0.0765979, 0.200871)
tracks/35/type = "scale_3d"
tracks/35/imported = true
tracks/35/enabled = true
tracks/35/path = NodePath("rig/Skeleton3D:DEF-lid4.B.L")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.989472, 1.02148, 0.989472, 0.0666667, 1, 0.965707, 1.0733, 0.965707, 0.1, 1, 0.933555, 1.14937, 0.933556, 0.133333, 1, 0.901566, 1.23073, 0.901567, 0.166667, 1, 0.881982, 1.28552, 0.881982, 0.2, 1, 0.89743, 1.24193, 0.89743, 0.233333, 1, 0.929559, 1.15947, 0.929559, 0.266667, 1, 0.969474, 1.0662, 0.969474, 0.3, 1, 1.00877, 0.983361, 1.00877, 0.333333, 1, 1.03951, 0.925425, 1.03951, 0.366667, 1, 1.05135, 0.904786, 1.05135, 0.4, 1, 1.05408, 0.900029, 1.05408, 0.433333, 1, 1.05293, 0.901995, 1.05293, 0.466667, 1, 1.04627, 0.913786, 1.04627, 0.5, 1, 1.02556, 0.950768, 1.02556, 0.533333, 1, 1.00297, 0.994464, 1.00297, 0.566667, 1, 0.988774, 1.02299, 0.988774, 0.6, 1, 0.981759, 1.03751, 0.981759, 0.633333, 1, 0.980396, 1.0404, 0.980396, 0.666667, 1, 0.98286, 1.03518, 0.98286, 0.7, 1, 0.992789, 1.01466, 0.992789, 0.733333, 1, 1.01013, 0.980468, 1.01013, 0.766667, 1, 1.03048, 0.942141, 1.03048, 0.8, 1, 1.04753, 0.911333, 1.04753, 0.833333, 1, 1.05302, 0.901836, 1.05302, 0.866667, 1, 1.0424, 0.92037, 1.0424, 0.9, 1, 1.00681, 0.988603, 1.00681, 0.966667, 1, 0.91397, 1.19768, 0.91397, 1, 1, 0.891381, 1.25856, 0.891381)
tracks/36/type = "position_3d"
tracks/36/imported = true
tracks/36/enabled = true
tracks/36/path = NodePath("rig/Skeleton3D:DEF-lid2.B.R")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/keys = PackedFloat32Array(0, 1, -0.279235, 0.35591, 0.39565, 0.0333333, 1, -0.287389, 0.33621, 0.407203, 0.0666667, 1, -0.305645, 0.298778, 0.433071, 0.1, 1, -0.331003, 0.255437, 0.469, 0.133333, 1, -0.357074, 0.218006, 0.505941, 0.166667, 1, -0.374087, 0.198305, 0.530046, 0.2, 1, -0.360581, 0.213655, 0.51091, 0.233333, 1, -0.334255, 0.250668, 0.473608, 0.266667, 1, -0.302728, 0.306902, 0.428938, 0.3, 1, -0.271624, 0.379255, 0.384866, 0.333333, 1, -0.244568, 0.46396, 0.34653, 0.366667, 1, -0.224055, 0.555997, 0.317464, 0.4, 1, -0.213812, 0.680738, 0.302952, 0.433333, 1, -0.217402, 0.856629, 0.308038, 0.466667, 1, -0.233951, 1.07028, 0.331487, 0.5, 1, -0.258154, 1.27645, 0.36578, 0.533333, 1, -0.276696, 1.41266, 0.392052, 0.566667, 1, -0.287919, 1.50088, 0.407955, 0.6, 1, -0.293321, 1.54785, 0.415609, 0.633333, 1, -0.294365, 1.55863, 0.417088, 0.666667, 1, -0.292479, 1.53661, 0.414416, 0.7, 1, -0.284834, 1.46155, 0.403583, 0.733333, 1, -0.270949, 1.33392, 0.383909, 0.766667, 1, -0.252394, 1.14898, 0.357619, 0.8, 1, -0.23405, 0.91919, 0.331628, 0.833333, 1, -0.224112, 0.674213, 0.317546, 0.866667, 1, -0.240269, 0.506801, 0.340438, 0.9, 1, -0.272296, 0.382668, 0.385818, 0.933333, 1, -0.311333, 0.291649, 0.44113, 0.966667, 1, -0.346673, 0.231406, 0.491203, 1, 1, -0.365764, 0.207433, 0.518254)
tracks/37/type = "rotation_3d"
tracks/37/imported = true
tracks/37/enabled = true
tracks/37/path = NodePath("rig/Skeleton3D:DEF-lid2.B.R")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/keys = PackedFloat32Array(0, 1, 0.651518, 0.593384, 0.445734, 0.157294, 0.0333333, 1, 0.647637, 0.600424, 0.440685, 0.160791, 0.0666667, 1, 0.640824, 0.612903, 0.430667, 0.167988, 0.1, 1, 0.633609, 0.626138, 0.418519, 0.177013, 0.133333, 1, 0.628014, 0.636478, 0.407565, 0.185432, 0.166667, 1, 0.625526, 0.641287, 0.401587, 0.19025, 0.2, 1, 0.627435, 0.637581, 0.406256, 0.186473, 0.233333, 1, 0.63289, 0.627484, 0.417137, 0.178075, 0.266667, 1, 0.642598, 0.609942, 0.432742, 0.166654, 0.3, 1, 0.656618, 0.584388, 0.451381, 0.153623, 0.333333, 1, 0.674435, 0.550851, 0.471203, 0.140245, 0.366667, 1, 0.694344, 0.511349, 0.490004, 0.127692, 0.4, 1, 0.704911, 0.488675, 0.499685, 0.120884, 0.433333, 1, 0.701301, 0.496604, 0.496353, 0.123269, 0.466667, 1, 0.68477, 0.530884, 0.480976, 0.133843, 0.5, 1, 0.664145, 0.570253, 0.460431, 0.147394, 0.533333, 1, 0.653257, 0.59035, 0.447646, 0.156064, 0.566667, 1, 0.647442, 0.60079, 0.440395, 0.161002, 0.6, 1, 0.644991, 0.605186, 0.437128, 0.163267, 0.633333, 1, 0.644556, 0.60597, 0.436523, 0.163693, 0.666667, 1, 0.645338, 0.604557, 0.437614, 0.162925, 0.7, 1, 0.648813, 0.598295, 0.442242, 0.159709, 0.733333, 1, 0.656489, 0.584472, 0.451522, 0.153438, 0.766667, 1, 0.669324, 0.560829, 0.465501, 0.144172, 0.8, 1, 0.683876, 0.532478, 0.480405, 0.134136, 0.833333, 1, 0.693519, 0.512885, 0.489494, 0.127967, 0.866667, 1, 0.678545, 0.543025, 0.47512, 0.137699, 0.9, 1, 0.657865, 0.582565, 0.451957, 0.153513, 0.933333, 1, 0.640003, 0.614755, 0.428549, 0.169757, 0.966667, 1, 0.629848, 0.632994, 0.411564, 0.182275, 1, 1, 0.626627, 0.639127, 0.404374, 0.187979)
tracks/38/type = "scale_3d"
tracks/38/imported = true
tracks/38/enabled = true
tracks/38/path = NodePath("rig/Skeleton3D:DEF-lid2.B.R")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.988111, 1.02432, 0.988111, 0.0666667, 1, 0.962272, 1.08116, 0.962272, 0.1, 1, 0.928258, 1.16271, 0.928258, 0.133333, 1, 0.895116, 1.24855, 0.895116, 0.166667, 1, 0.875161, 1.30564, 0.875161, 0.2, 1, 0.890884, 1.26026, 0.890884, 0.233333, 1, 0.924105, 1.17339, 0.924105, 0.266667, 1, 0.966676, 1.07283, 0.966675, 0.3, 1, 1.01093, 0.979531, 1.01093, 0.333333, 1, 1.04969, 0.907563, 1.04969, 0.366667, 1, 1.07308, 0.868751, 1.07308, 0.4, 1, 1.08258, 0.853273, 1.08258, 0.433333, 1, 1.07908, 0.858924, 1.07908, 0.466667, 1, 1.06228, 0.886785, 1.06228, 0.5, 1, 1.03075, 0.941224, 1.03075, 0.533333, 1, 1.00369, 0.99318, 1.00369, 0.566667, 1, 0.987354, 1.02597, 0.987354, 0.6, 1, 0.979516, 1.04227, 0.979516, 0.633333, 1, 0.978013, 1.04547, 0.978013, 0.666667, 1, 0.980728, 1.03969, 0.980728, 0.7, 1, 0.99183, 1.01664, 0.99183, 0.733333, 1, 1.01212, 0.976778, 1.01212, 0.766667, 1, 1.03831, 0.928371, 1.03831, 0.8, 1, 1.06289, 0.885241, 1.06289, 0.833333, 1, 1.07411, 0.866768, 1.07411, 0.866667, 1, 1.05485, 0.898923, 1.05485, 0.9, 1, 1.00977, 0.983808, 1.00977, 0.933333, 1, 0.955207, 1.10023, 0.955207, 0.966667, 1, 0.907843, 1.21394, 0.907843, 1, 1, 0.884708, 1.27762, 0.884708)
tracks/39/type = "position_3d"
tracks/39/imported = true
tracks/39/enabled = true
tracks/39/path = NodePath("rig/Skeleton3D:DEF-lid3.B.R")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/keys = PackedFloat32Array(0, 1, -0.21764, 0.331942, 0.467069, 0.0333333, 1, -0.223996, 0.313568, 0.480708, 0.0666667, 1, -0.238225, 0.278658, 0.511245, 0.1, 1, -0.257989, 0.238235, 0.55366, 0.133333, 1, -0.27831, 0.203325, 0.597269, 0.166667, 1, -0.29157, 0.184951, 0.625725, 0.2, 1, -0.281043, 0.199267, 0.603135, 0.233333, 1, -0.260524, 0.233787, 0.5591, 0.266667, 1, -0.235952, 0.286235, 0.506366, 0.3, 1, -0.211708, 0.353715, 0.454338, 0.366667, 1, -0.174632, 0.518555, 0.37477, 0.4, 1, -0.166649, 0.639812, 0.357638, 0.433333, 1, -0.169447, 0.816907, 0.363642, 0.466667, 1, -0.182345, 1.03588, 0.391324, 0.5, 1, -0.20121, 1.2484, 0.431807, 0.533333, 1, -0.215662, 1.38816, 0.462822, 0.566667, 1, -0.224409, 1.47831, 0.481595, 0.6, 1, -0.22862, 1.52612, 0.490631, 0.633333, 1, -0.229433, 1.53706, 0.492376, 0.666667, 1, -0.227963, 1.51477, 0.489222, 0.7, 1, -0.222004, 1.4385, 0.476434, 0.733333, 1, -0.211182, 1.30837, 0.453209, 0.766667, 1, -0.19672, 1.1194, 0.422173, 0.8, 1, -0.182423, 0.885027, 0.39149, 0.833333, 1, -0.174677, 0.637005, 0.374867, 0.866667, 1, -0.18727, 0.474311, 0.401891, 0.9, 1, -0.212232, 0.356898, 0.455462, 0.933333, 1, -0.242658, 0.272008, 0.520759, 0.966667, 1, -0.270203, 0.215823, 0.579871, 1, 1, -0.285083, 0.193463, 0.611804)
tracks/40/type = "rotation_3d"
tracks/40/imported = true
tracks/40/enabled = true
tracks/40/path = NodePath("rig/Skeleton3D:DEF-lid3.B.R")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/keys = PackedFloat32Array(0, 1, -0.630991, -0.724556, -0.263042, 0.0876213, 0.0333333, 1, -0.634718, -0.723375, -0.259092, 0.0820528, 0.0666667, 1, -0.641672, -0.721209, -0.251082, 0.0712189, 0.1, 1, -0.649513, -0.718722, -0.241181, 0.0583397, 0.133333, 1, -0.656084, -0.716584, -0.232077, 0.0469212, 0.166667, 1, -0.659445, -0.715504, -0.226991, 0.0407516, 0.2, 1, -0.656833, -0.716343, -0.230971, 0.0455673, 0.233333, 1, -0.650361, -0.718455, -0.240034, 0.0568918, 0.266667, 1, -0.64017, -0.721767, -0.252655, 0.0734823, 0.3, 1, -0.626501, -0.726099, -0.267268, 0.094042, 0.333333, 1, -0.609775, -0.73126, -0.282296, 0.117225, 0.366667, 1, -0.590757, -0.737215, -0.29575, 0.141604, 0.4, 1, -0.579767, -0.740427, -0.302617, 0.155115, 0.433333, 1, -0.583593, -0.739363, -0.300215, 0.150441, 0.466667, 1, -0.600198, -0.734349, -0.289283, 0.129651, 0.5, 1, -0.619271, -0.728277, -0.274275, 0.10435, 0.533333, 1, -0.629484, -0.725082, -0.264469, 0.0897841, 0.566667, 1, -0.634924, -0.723314, -0.258859, 0.0817376, 0.6, 1, -0.637275, -0.722553, -0.256296, 0.0781676, 0.633333, 1, -0.637701, -0.722417, -0.255817, 0.0775102, 0.666667, 1, -0.636931, -0.722663, -0.256681, 0.0786954, 0.7, 1, -0.633582, -0.723736, -0.260313, 0.0837613, 0.733333, 1, -0.626472, -0.726043, -0.267453, 0.0941453, 0.766667, 1, -0.614734, -0.729833, -0.277927, 0.110467, 0.8, 1, -0.600915, -0.734032, -0.288981, 0.128793, 0.833333, 1, -0.591493, -0.736881, -0.295522, 0.140744, 0.866667, 1, -0.606011, -0.732496, -0.285094, 0.122152, 0.9, 1, -0.62582, -0.726496, -0.267507, 0.0948313, 0.933333, 1, -0.642971, -0.720902, -0.249258, 0.0689857, 0.966667, 1, -0.653758, -0.717327, -0.235444, 0.0510701, 1, 1, -0.657899, -0.715998, -0.229375, 0.0436236)
tracks/41/type = "scale_3d"
tracks/41/imported = true
tracks/41/enabled = true
tracks/41/path = NodePath("rig/Skeleton3D:DEF-lid3.B.R")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.98595, 1.02886, 0.98595, 0.0666667, 1, 0.956902, 1.09364, 0.956903, 0.133333, 1, 0.885222, 1.27666, 0.885222, 0.166667, 1, 0.864734, 1.33732, 0.864734, 0.2, 1, 0.880851, 1.28916, 0.880851, 0.233333, 1, 0.91568, 1.19539, 0.91568, 0.266667, 1, 0.962328, 1.08335, 0.962328, 0.3, 1, 1.01463, 0.973229, 1.01463, 0.333333, 1, 1.06715, 0.878118, 1.06715, 0.366667, 1, 1.11377, 0.807249, 1.11377, 0.4, 1, 1.13813, 0.772197, 1.13813, 0.433333, 1, 1.1296, 0.784504, 1.1296, 0.466667, 1, 1.09116, 0.841377, 1.09116, 0.5, 1, 1.03936, 0.925691, 1.03936, 0.533333, 1, 1.00492, 0.991069, 1.00492, 0.566667, 1, 0.985102, 1.03074, 0.985103, 0.6, 1, 0.975966, 1.04987, 0.975966, 0.633333, 1, 0.974247, 1.05357, 0.974247, 0.666667, 1, 0.97735, 1.04689, 0.97735, 0.7, 1, 0.990303, 1.01981, 0.990303, 0.733333, 1, 1.01541, 0.970784, 1.01541, 0.766667, 1, 1.05182, 0.905631, 1.05182, 0.8, 1, 1.09024, 0.841584, 1.09024, 0.833333, 1, 1.11299, 0.807273, 1.11299, 0.866667, 1, 1.07676, 0.863223, 1.07676, 0.9, 1, 1.01501, 0.975775, 1.01501, 0.933333, 1, 0.949689, 1.11392, 0.949689, 0.966667, 1, 0.89842, 1.23959, 0.89842, 1, 1, 0.874493, 1.30764, 0.874493)
tracks/42/type = "position_3d"
tracks/42/imported = true
tracks/42/enabled = true
tracks/42/path = NodePath("rig/Skeleton3D:DEF-lid4.B.R")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/keys = PackedFloat32Array(0, 1, -0.127494, 0.338073, 0.492466, 0.0333333, 1, -0.131217, 0.319359, 0.506847, 0.0666667, 1, -0.139553, 0.283804, 0.539045, 0.1, 1, -0.15113, 0.242635, 0.583766, 0.133333, 1, -0.163034, 0.20708, 0.629746, 0.166667, 1, -0.170802, 0.188367, 0.65975, 0.2, 1, -0.164635, 0.202947, 0.635931, 0.233333, 1, -0.152615, 0.238105, 0.589502, 0.266667, 1, -0.138221, 0.291521, 0.5339, 0.3, 1, -0.124019, 0.360248, 0.479043, 0.366667, 1, -0.1023, 0.528131, 0.395149, 0.4, 1, -0.0976231, 0.65028, 0.377085, 0.433333, 1, -0.099262, 0.827067, 0.383416, 0.466667, 1, -0.106818, 1.04468, 0.412602, 0.5, 1, -0.117869, 1.25558, 0.455288, 0.533333, 1, -0.126335, 1.39443, 0.487989, 0.566667, 1, -0.131459, 1.48408, 0.507782, 0.6, 1, -0.133926, 1.53168, 0.517309, 0.633333, 1, -0.134402, 1.54258, 0.51915, 0.666667, 1, -0.133541, 1.52036, 0.515824, 0.7, 1, -0.13005, 1.4444, 0.502341, 0.733333, 1, -0.123711, 1.3149, 0.477853, 0.766667, 1, -0.115239, 1.12697, 0.445129, 0.8, 1, -0.106863, 0.893765, 0.412778, 0.833333, 1, -0.102326, 0.646522, 0.39525, 0.866667, 1, -0.109703, 0.482621, 0.423744, 0.9, 1, -0.124326, 0.36349, 0.480229, 0.933333, 1, -0.142149, 0.277032, 0.549076, 0.966667, 1, -0.158285, 0.219809, 0.611402, 1, 1, -0.167002, 0.197036, 0.645072)
tracks/43/type = "rotation_3d"
tracks/43/imported = true
tracks/43/enabled = true
tracks/43/path = NodePath("rig/Skeleton3D:DEF-lid4.B.R")
tracks/43/interp = 1
tracks/43/loop_wrap = true
tracks/43/keys = PackedFloat32Array(0, 1, -0.579755, -0.769356, -0.095577, 0.250681, 0.0333333, 1, -0.589916, -0.763885, -0.0939007, 0.244257, 0.0666667, 1, -0.60851, -0.753575, -0.0898791, 0.231868, 0.1, 1, -0.628922, -0.741724, -0.0842144, 0.217281, 0.133333, 1, -0.645533, -0.731651, -0.0784419, 0.204503, 0.166667, 1, -0.653694, -0.726617, -0.0749148, 0.197738, 0.2, 1, -0.647374, -0.730519, -0.0776922, 0.203009, 0.233333, 1, -0.63107, -0.740452, -0.0834986, 0.215661, 0.266667, 1, -0.604344, -0.756025, -0.090465, 0.234544, 0.3, 1, -0.567408, -0.775912, -0.0967979, 0.258146, 0.333333, 1, -0.521394, -0.798101, -0.10081, 0.284642, 0.366667, 1, -0.471093, -0.819096, -0.100908, 0.311402, 0.4, 1, -0.443021, -0.829262, -0.100182, 0.325608, 0.433333, 1, -0.4529, -0.825803, -0.100421, 0.320698, 0.466667, 1, -0.495816, -0.809249, -0.101013, 0.298461, 0.5, 1, -0.547467, -0.78584, -0.0992384, 0.269975, 0.533333, 1, -0.575587, -0.771603, -0.0959903, 0.253208, 0.566667, 1, -0.590464, -0.763591, -0.0937839, 0.243899, 0.6, 1, -0.596851, -0.760062, -0.0926416, 0.239776, 0.633333, 1, -0.598003, -0.759422, -0.092414, 0.23902, 0.666667, 1, -0.595925, -0.760575, -0.0928242, 0.240382, 0.7, 1, -0.586827, -0.765563, -0.0944343, 0.246229, 0.733333, 1, -0.567299, -0.775925, -0.0970555, 0.258249, 0.766667, 1, -0.535128, -0.791827, -0.0996793, 0.27697, 0.8, 1, -0.497529, -0.808497, -0.101311, 0.297545, 0.833333, 1, -0.472612, -0.818495, -0.101256, 0.310566, 0.866667, 1, -0.511336, -0.802596, -0.100944, 0.290148, 0.9, 1, -0.565444, -0.777068, -0.0963249, 0.259153, 0.933333, 1, -0.611663, -0.751909, -0.0886113, 0.229456, 0.966667, 1, -0.639778, -0.735155, -0.0806878, 0.209094, 1, 1, -0.649977, -0.728912, -0.0765976, 0.200871)
tracks/44/type = "scale_3d"
tracks/44/imported = true
tracks/44/enabled = true
tracks/44/path = NodePath("rig/Skeleton3D:DEF-lid4.B.R")
tracks/44/interp = 1
tracks/44/loop_wrap = true
tracks/44/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.989472, 1.02148, 0.989472, 0.0666667, 1, 0.965706, 1.0733, 0.965706, 0.1, 1, 0.933555, 1.14937, 0.933555, 0.133333, 1, 0.901566, 1.23073, 0.901566, 0.166667, 1, 0.881982, 1.28553, 0.881982, 0.2, 1, 0.89743, 1.24193, 0.89743, 0.233333, 1, 0.929559, 1.15947, 0.929559, 0.266667, 1, 0.969474, 1.0662, 0.969474, 0.3, 1, 1.00877, 0.983362, 1.00877, 0.333333, 1, 1.03951, 0.925426, 1.03951, 0.366667, 1, 1.05135, 0.904787, 1.05135, 0.4, 1, 1.05408, 0.900029, 1.05408, 0.433333, 1, 1.05293, 0.901995, 1.05293, 0.466667, 1, 1.04627, 0.913787, 1.04627, 0.5, 1, 1.02556, 0.95077, 1.02556, 0.533333, 1, 1.00297, 0.994466, 1.00297, 0.566667, 1, 0.988774, 1.02299, 0.988774, 0.6, 1, 0.981759, 1.03751, 0.981759, 0.633333, 1, 0.980395, 1.0404, 0.980396, 0.666667, 1, 0.98286, 1.03518, 0.98286, 0.7, 1, 0.992789, 1.01466, 0.992789, 0.733333, 1, 1.01013, 0.980469, 1.01013, 0.766667, 1, 1.03049, 0.942142, 1.03049, 0.8, 1, 1.04753, 0.911333, 1.04753, 0.833333, 1, 1.05302, 0.901836, 1.05302, 0.866667, 1, 1.0424, 0.920371, 1.0424, 0.9, 1, 1.00681, 0.988605, 1.00681, 0.966667, 1, 0.913969, 1.19768, 0.913969, 1, 1, 0.891381, 1.25856, 0.891381)
tracks/45/type = "position_3d"
tracks/45/imported = true
tracks/45/enabled = true
tracks/45/path = NodePath("rig/Skeleton3D:DEF-lid2.T.L")
tracks/45/interp = 1
tracks/45/loop_wrap = true
tracks/45/keys = PackedFloat32Array(0, 1, 0.249452, 0.524671, 0.373452, 0.0333333, 1, 0.259326, 0.488473, 0.384802, 0.0666667, 1, 0.281138, 0.422506, 0.410163, 0.1, 1, 0.311022, 0.34919, 0.445318, 0.133333, 1, 0.341398, 0.288521, 0.481402, 0.166667, 1, 0.360972, 0.258068, 0.504906, 0.2, 1, 0.344743, 0.282742, 0.486129, 0.233333, 1, 0.314087, 0.3427, 0.449695, 0.266667, 1, 0.278537, 0.434417, 0.406263, 0.3, 1, 0.245144, 0.551351, 0.363701, 0.366667, 1, 0.200157, 0.819633, 0.299654, 0.4, 1, 0.191007, 0.9689, 0.285956, 0.433333, 1, 0.194214, 1.13631, 0.290756, 0.466667, 1, 0.208998, 1.31245, 0.312889, 0.5, 1, 0.230619, 1.47389, 0.345259, 0.533333, 1, 0.247184, 1.58515, 0.370057, 0.566667, 1, 0.25721, 1.65978, 0.385067, 0.6, 1, 0.262036, 1.70079, 0.392292, 0.633333, 1, 0.262968, 1.71049, 0.393688, 0.666667, 1, 0.261283, 1.69044, 0.391165, 0.7, 1, 0.254453, 1.62383, 0.38094, 0.733333, 1, 0.24205, 1.51385, 0.362371, 0.766667, 1, 0.225474, 1.35723, 0.337555, 0.8, 1, 0.209087, 1.15974, 0.313022, 0.833333, 1, 0.200208, 0.9362, 0.299731, 0.866667, 1, 0.214642, 0.73557, 0.321339, 0.9, 1, 0.243253, 0.564117, 0.364172, 0.933333, 1, 0.278126, 0.429939, 0.416381, 0.966667, 1, 0.309697, 0.341132, 0.463645, 1, 1, 0.326752, 0.30579, 0.489178)
tracks/46/type = "rotation_3d"
tracks/46/imported = true
tracks/46/enabled = true
tracks/46/path = NodePath("rig/Skeleton3D:DEF-lid2.T.L")
tracks/46/interp = 1
tracks/46/loop_wrap = true
tracks/46/keys = PackedFloat32Array(0, 1, 0.718897, -0.596817, -0.129892, 0.33185, 0.0333333, 1, 0.745523, -0.563131, -0.136494, 0.329315, 0.0666667, 1, 0.78011, -0.510613, -0.143639, 0.331769, 0.1, 1, 0.804573, -0.462059, -0.149042, 0.341981, 0.133333, 1, 0.814069, -0.431505, -0.152796, 0.357418, 0.166667, 1, 0.813415, -0.421706, -0.156709, 0.368731, 0.2, 1, 0.811564, -0.434052, -0.150726, 0.360894, 0.233333, 1, 0.802282, -0.464295, -0.146764, 0.345302, 0.266667, 1, 0.78095, -0.510206, -0.146165, 0.329305, 0.3, 1, 0.750696, -0.560517, -0.149287, 0.316211, 0.333333, 1, 0.725025, -0.596777, -0.156, 0.306366, 0.366667, 1, 0.73226, -0.591602, -0.171182, 0.290687, 0.4, 1, 0.736602, -0.587853, -0.178723, 0.282673, 0.433333, 1, 0.735135, -0.589129, -0.176089, 0.285475, 0.466667, 1, 0.728677, -0.594304, -0.163865, 0.298296, 0.5, 1, 0.722016, -0.597722, -0.145809, 0.316482, 0.533333, 1, 0.719368, -0.596856, -0.131817, 0.329995, 0.566667, 1, 0.71804, -0.595767, -0.12337, 0.338022, 0.6, 1, 0.717525, -0.59504, -0.11932, 0.34183, 0.633333, 1, 0.71744, -0.59488, -0.118542, 0.342558, 0.666667, 1, 0.717595, -0.595167, -0.119948, 0.341243, 0.7, 1, 0.71832, -0.59618, -0.125681, 0.335843, 0.733333, 1, 0.720134, -0.597246, -0.136161, 0.325829, 0.8, 1, 0.728203, -0.594841, -0.163814, 0.29841, 0.833333, 1, 0.731751, -0.59219, -0.171151, 0.29079, 0.866667, 1, 0.726455, -0.595859, -0.159192, 0.303106, 0.9, 1, 0.720884, -0.596107, -0.135169, 0.326668, 0.933333, 1, 0.716831, -0.591437, -0.106682, 0.353517, 0.966667, 1, 0.714826, -0.583939, -0.0820426, 0.37591, 1, 1, 0.714203, -0.579357, -0.0702047, 0.386433)
tracks/47/type = "scale_3d"
tracks/47/imported = true
tracks/47/enabled = true
tracks/47/path = NodePath("rig/Skeleton3D:DEF-lid2.T.L")
tracks/47/interp = 1
tracks/47/loop_wrap = true
tracks/47/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.991031, 1.01824, 0.991031, 0.0666667, 1, 0.967661, 1.06897, 0.967661, 0.1, 1, 0.938836, 1.13582, 0.938836, 0.133333, 1, 0.9134, 1.1988, 0.9134, 0.166667, 1, 0.900098, 1.2343, 0.900098, 0.2, 1, 0.910697, 1.20586, 0.910697, 0.233333, 1, 0.935896, 1.14302, 0.935896, 0.266667, 1, 0.973085, 1.05851, 0.973085, 0.3, 1, 1.01847, 0.965639, 1.01847, 0.333333, 1, 1.06706, 0.878263, 1.06706, 0.366667, 1, 1.11355, 0.807558, 1.11355, 0.4, 1, 1.13782, 0.772609, 1.13782, 0.433333, 1, 1.12933, 0.784881, 1.12933, 0.466667, 1, 1.09101, 0.841602, 1.09101, 0.5, 1, 1.03932, 0.925764, 1.03932, 0.533333, 1, 1.00492, 0.991081, 1.00492, 0.566667, 1, 0.985112, 1.03072, 0.985112, 0.6, 1, 0.975982, 1.04984, 0.975982, 0.633333, 1, 0.974265, 1.05353, 0.974265, 0.666667, 1, 0.977365, 1.04685, 0.977365, 0.7, 1, 0.990311, 1.0198, 0.990311, 0.733333, 1, 1.0154, 0.970813, 1.0154, 0.766667, 1, 1.05175, 0.905744, 1.05175, 0.8, 1, 1.0901, 0.8418, 1.0901, 0.833333, 1, 1.11278, 0.807571, 1.11278, 0.866667, 1, 1.07664, 0.8634, 1.07664, 0.9, 1, 1.01498, 0.975816, 1.01498, 0.933333, 1, 0.949715, 1.11386, 0.949715, 0.966667, 1, 0.898464, 1.23947, 0.898464, 1, 1, 0.874541, 1.3075, 0.874541)
tracks/48/type = "position_3d"
tracks/48/imported = true
tracks/48/enabled = true
tracks/48/path = NodePath("rig/Skeleton3D:DEF-lid3.T.L")
tracks/48/interp = 1
tracks/48/loop_wrap = true
tracks/48/keys = PackedFloat32Array(0, 1, 0.172225, 0.517931, 0.436701, 0.0333333, 1, 0.183037, 0.473287, 0.450445, 0.0666667, 1, 0.206581, 0.394734, 0.481106, 0.1, 1, 0.238364, 0.310625, 0.523533, 0.133333, 1, 0.270262, 0.243899, 0.567022, 0.166667, 1, 0.290524, 0.21208, 0.595305, 0.2, 1, 0.272898, 0.238984, 0.572588, 0.233333, 1, 0.240726, 0.304892, 0.528681, 0.266667, 1, 0.204792, 0.40641, 0.476546, 0.3, 1, 0.173093, 0.534636, 0.425753, 0.333333, 1, 0.150843, 0.675168, 0.382485, 0.366667, 1, 0.138191, 0.809103, 0.350404, 0.4, 1, 0.131874, 0.957391, 0.334386, 0.433333, 1, 0.134088, 1.12514, 0.339999, 0.466667, 1, 0.144295, 1.30277, 0.36588, 0.5, 1, 0.159223, 1.46601, 0.403732, 0.533333, 1, 0.170659, 1.57826, 0.432731, 0.566667, 1, 0.177581, 1.65343, 0.450283, 0.6, 1, 0.180913, 1.69469, 0.458731, 0.633333, 1, 0.181557, 1.70443, 0.460363, 0.666667, 1, 0.180394, 1.68429, 0.457414, 0.7, 1, 0.175678, 1.61734, 0.445457, 0.733333, 1, 0.167115, 1.50666, 0.423743, 0.766667, 1, 0.15567, 1.34891, 0.394724, 0.8, 1, 0.144356, 1.15013, 0.366036, 0.833333, 1, 0.138227, 0.925736, 0.350494, 0.866667, 1, 0.148192, 0.726433, 0.375761, 0.9, 1, 0.167945, 0.55687, 0.425849, 0.933333, 1, 0.192022, 0.424416, 0.4869, 0.966667, 1, 0.213819, 0.336749, 0.542169, 1, 1, 0.225594, 0.301862, 0.572026)
tracks/49/type = "rotation_3d"
tracks/49/imported = true
tracks/49/enabled = true
tracks/49/path = NodePath("rig/Skeleton3D:DEF-lid3.T.L")
tracks/49/interp = 1
tracks/49/loop_wrap = true
tracks/49/keys = PackedFloat32Array(0, 1, 0.87894, -0.440964, 0.0367312, 0.177951, 0.0333333, 1, 0.856425, -0.481487, 0.0292127, 0.183991, 0.0666667, 1, 0.813824, -0.547153, 0.0241958, 0.194237, 0.1, 1, 0.767977, -0.606391, 0.0254822, 0.204576, 0.133333, 1, 0.734446, -0.643812, 0.0315638, 0.212364, 0.166667, 1, 0.721701, -0.656946, 0.035476, 0.215199, 0.2, 1, 0.733423, -0.643803, 0.0346929, 0.21542, 0.233333, 1, 0.766395, -0.607177, 0.028651, 0.207733, 0.266667, 1, 0.818509, -0.541504, 0.0215499, 0.19066, 0.3, 1, 0.876039, -0.452964, 0.0154418, 0.164745, 0.333333, 1, 0.921329, -0.364667, 0.0104189, 0.134399, 0.366667, 1, 0.944843, -0.309017, -0.00492105, 0.108428, 0.4, 1, 0.955095, -0.280173, -0.0125513, 0.0955943, 0.433333, 1, 0.951601, -0.290399, -0.00988563, 0.100132, 0.466667, 1, 0.934093, -0.335913, 0.00248174, 0.120936, 0.5, 1, 0.904755, -0.397449, 0.0207214, 0.151735, 0.533333, 1, 0.882388, -0.435462, 0.0348055, 0.174796, 0.566667, 1, 0.868764, -0.455973, 0.0432472, 0.188327, 0.6, 1, 0.862374, -0.464939, 0.0472776, 0.194698, 0.633333, 1, 0.861178, -0.466566, 0.0480498, 0.19591, 0.666667, 1, 0.863333, -0.463629, 0.0466539, 0.193719, 0.7, 1, 0.872327, -0.450849, 0.0409413, 0.184676, 0.733333, 1, 0.88942, -0.42412, 0.0304444, 0.167712, 0.766667, 1, 0.912355, -0.382719, 0.0163899, 0.144449, 0.8, 1, 0.933712, -0.33696, 0.00251706, 0.120974, 0.833333, 1, 0.944663, -0.309599, -0.00490423, 0.108337, 0.866667, 1, 0.926572, -0.353245, 0.00719844, 0.128963, 0.9, 1, 0.889562, -0.423184, 0.0314623, 0.169131, 0.933333, 1, 0.845424, -0.485777, 0.0597403, 0.213799, 0.966667, 1, 0.809558, -0.524444, 0.0837149, 0.250134, 1, 1, 0.794238, -0.537509, 0.0950362, 0.266904)
tracks/50/type = "scale_3d"
tracks/50/imported = true
tracks/50/enabled = true
tracks/50/path = NodePath("rig/Skeleton3D:DEF-lid3.T.L")
tracks/50/interp = 1
tracks/50/loop_wrap = true
tracks/50/keys = PackedFloat32Array(0, 1, 1, 0.999998, 1, 0.0333333, 1, 1.00269, 0.994645, 1.00269, 0.0666667, 1, 0.986218, 1.02876, 0.986218, 0.1, 1, 0.949898, 1.11176, 0.949898, 0.133333, 1, 0.905662, 1.22006, 0.905662, 0.166667, 1, 0.878055, 1.29705, 0.878055, 0.2, 1, 0.901273, 1.23171, 0.901273, 0.233333, 1, 0.945798, 1.12172, 0.945798, 0.266667, 1, 0.986964, 1.0277, 0.986965, 0.3, 1, 1.00572, 0.988878, 1.00572, 0.333333, 1, 0.988623, 1.02315, 0.988623, 0.366667, 1, 0.958248, 1.08984, 0.958248, 0.4, 1, 0.939162, 1.13409, 0.939161, 0.433333, 1, 0.945799, 1.11904, 0.945799, 0.466667, 1, 0.973353, 1.05599, 0.973353, 0.5, 1, 0.997767, 1.00448, 0.997767, 0.533333, 1, 0.999212, 1.00158, 0.999212, 0.566667, 1, 0.997321, 1.00539, 0.997321, 0.6, 1, 0.995345, 1.00938, 0.995345, 0.633333, 1, 0.994854, 1.01037, 0.994854, 0.666667, 1, 0.995744, 1.00857, 0.995744, 0.7, 1, 0.998496, 1.00302, 0.998496, 0.733333, 1, 0.999337, 1.00133, 0.999337, 0.766667, 1, 0.991587, 1.01733, 0.991587, 0.8, 1, 0.975985, 1.05001, 0.975985, 0.833333, 1, 0.96069, 1.08351, 0.96069, 0.866667, 1, 0.982842, 1.03562, 0.982842, 0.9, 1, 0.993198, 1.01379, 0.993198, 0.933333, 1, 0.9814, 1.03937, 0.9814, 0.966667, 1, 0.954307, 1.09836, 0.954307, 1, 1, 0.935995, 1.14144, 0.935995)
tracks/51/type = "position_3d"
tracks/51/imported = true
tracks/51/enabled = true
tracks/51/path = NodePath("rig/Skeleton3D:DEF-lid4.T.L")
tracks/51/interp = 1
tracks/51/loop_wrap = true
tracks/51/keys = PackedFloat32Array(0, 1, 0.086621, 0.458442, 0.467156, 0.0333333, 1, 0.0928986, 0.422712, 0.48144, 0.0666667, 1, 0.106524, 0.358888, 0.51335, 0.1, 1, 0.124853, 0.289426, 0.557568, 0.133333, 1, 0.143193, 0.233268, 0.602945, 0.166667, 1, 0.154802, 0.205851, 0.632494, 0.2, 1, 0.144587, 0.228582, 0.608864, 0.233333, 1, 0.126093, 0.284066, 0.563049, 0.266667, 1, 0.105625, 0.369258, 0.508471, 0.3, 1, 0.0878653, 0.477321, 0.455041, 0.333333, 1, 0.0758669, 0.597619, 0.409158, 0.366667, 1, 0.0695036, 0.71617, 0.37484, 0.4, 1, 0.0663263, 0.855812, 0.357705, 0.433333, 1, 0.0674398, 1.02655, 0.36371, 0.466667, 1, 0.0725735, 1.21741, 0.391396, 0.5, 1, 0.0800814, 1.39641, 0.431888, 0.533333, 1, 0.0858334, 1.51746, 0.462908, 0.566667, 1, 0.0893151, 1.59742, 0.481685, 0.6, 1, 0.0909907, 1.64077, 0.490722, 0.633333, 1, 0.0913142, 1.65089, 0.492468, 0.666667, 1, 0.0907294, 1.63007, 0.489313, 0.7, 1, 0.0883578, 1.56014, 0.476522, 0.733333, 1, 0.0840505, 1.44324, 0.453294, 0.766667, 1, 0.0782946, 1.2755, 0.422252, 0.8, 1, 0.0726044, 1.06534, 0.391563, 0.833333, 1, 0.0695215, 0.833385, 0.374936, 0.866667, 1, 0.0745333, 0.645791, 0.401966, 0.9, 1, 0.0844685, 0.492909, 0.455547, 0.933333, 1, 0.0965781, 0.375668, 0.520855, 0.966667, 1, 0.107541, 0.298071, 0.579979, 1, 1, 0.113463, 0.26719, 0.611918)
tracks/52/type = "rotation_3d"
tracks/52/imported = true
tracks/52/enabled = true
tracks/52/path = NodePath("rig/Skeleton3D:DEF-lid4.T.L")
tracks/52/interp = 1
tracks/52/loop_wrap = true
tracks/52/keys = PackedFloat32Array(0, 1, 0.95909, -0.237543, 0.125869, 0.0887552, 0.0333333, 1, 0.940842, -0.299308, 0.121705, 0.102076, 0.0666667, 1, 0.883197, -0.433458, 0.121596, 0.131497, 0.1, 1, 0.799138, -0.565321, 0.125072, 0.161703, 0.133333, 1, 0.724385, -0.651687, 0.13077, 0.182948, 0.166667, 1, 0.697206, -0.678113, 0.134289, 0.189826, 0.2, 1, 0.722803, -0.651913, 0.133862, 0.186136, 0.233333, 1, 0.796531, -0.56731, 0.128249, 0.165076, 0.266667, 1, 0.887364, -0.426535, 0.118844, 0.128567, 0.3, 1, 0.953921, -0.266889, 0.106792, 0.0860331, 0.333333, 1, 0.978924, -0.170379, 0.0967923, 0.0575303, 0.366667, 1, 0.98669, -0.13495, 0.0798374, 0.043087, 0.4, 1, 0.989764, -0.118084, 0.0713802, 0.0364344, 0.433333, 1, 0.988731, -0.124022, 0.0743411, 0.0387687, 0.466667, 1, 0.983188, -0.151951, 0.0880353, 0.0500251, 0.5, 1, 0.972178, -0.196014, 0.108184, 0.068877, 0.533333, 1, 0.96077, -0.232645, 0.123759, 0.0864908, 0.566667, 1, 0.952765, -0.254917, 0.13306, 0.0977269, 0.6, 1, 0.948591, -0.26568, 0.137493, 0.103365, 0.633333, 1, 0.947761, -0.267757, 0.138343, 0.104481, 0.666667, 1, 0.949257, -0.264002, 0.136807, 0.102463, 0.7, 1, 0.955074, -0.248741, 0.130516, 0.0944868, 0.733333, 1, 0.964549, -0.22124, 0.118946, 0.080927, 0.766667, 1, 0.975031, -0.185631, 0.103423, 0.0644947, 0.8, 1, 0.983259, -0.151592, 0.0880538, 0.0496734, 0.833333, 1, 0.986764, -0.134512, 0.0798364, 0.0427637, 0.866667, 1, 0.980727, -0.162845, 0.0932401, 0.05442, 0.9, 1, 0.963337, -0.224867, 0.120143, 0.083558, 0.933333, 1, 0.933707, -0.299883, 0.151259, 0.124022, 0.966667, 1, 0.899478, -0.364432, 0.177096, 0.1636, 1, 1, 0.879943, -0.394591, 0.189013, 0.185125)
tracks/53/type = "scale_3d"
tracks/53/imported = true
tracks/53/enabled = true
tracks/53/path = NodePath("rig/Skeleton3D:DEF-lid4.T.L")
tracks/53/interp = 1
tracks/53/loop_wrap = true
tracks/53/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 1.05266, 0.904237, 1.05266, 0.0666667, 1, 1.10305, 0.823779, 1.10305, 0.1, 1, 1.09607, 0.835613, 1.09607, 0.133333, 1, 1.03702, 0.931268, 1.03702, 0.166667, 1, 0.991698, 1.01681, 0.991698, 0.2, 1, 1.03202, 0.940036, 1.03202, 0.233333, 1, 1.09241, 0.841605, 1.09241, 0.266667, 1, 1.09445, 0.838023, 1.09445, 0.3, 1, 1.01924, 0.972408, 1.01924, 0.333333, 1, 0.90741, 1.21449, 0.90741, 0.366667, 1, 0.841566, 1.41799, 0.841566, 0.4, 1, 0.807576, 1.53475, 0.807576, 0.433333, 1, 0.819517, 1.49444, 0.819517, 0.466667, 1, 0.8733, 1.31763, 0.8733, 0.5, 1, 0.946665, 1.11585, 0.946665, 0.533333, 1, 0.993129, 1.01563, 0.993129, 0.566667, 1, 1.01859, 0.96419, 1.01859, 0.6, 1, 1.02977, 0.94303, 1.02977, 0.633333, 1, 1.03179, 0.939339, 1.03179, 0.666667, 1, 1.02815, 0.945991, 1.02815, 0.7, 1, 1.01225, 0.976123, 1.01225, 0.733333, 1, 0.97913, 1.04491, 0.97913, 0.766667, 1, 0.928887, 1.16486, 0.928887, 0.8, 1, 0.874629, 1.30861, 0.874629, 0.833333, 1, 0.84248, 1.4089, 0.84248, 0.866667, 1, 0.893796, 1.25505, 0.893796, 0.9, 1, 0.97773, 1.05758, 0.97773, 0.933333, 1, 1.0526, 0.905283, 1.0526, 0.966667, 1, 1.09602, 0.832501, 1.09602, 1, 1, 1.10494, 0.819072, 1.10494)
tracks/54/type = "position_3d"
tracks/54/imported = true
tracks/54/enabled = true
tracks/54/path = NodePath("rig/Skeleton3D:DEF-lid2.T.R")
tracks/54/interp = 1
tracks/54/loop_wrap = true
tracks/54/keys = PackedFloat32Array(0, 1, -0.249452, 0.524671, 0.373452, 0.0333333, 1, -0.259326, 0.488473, 0.384802, 0.0666667, 1, -0.281138, 0.422506, 0.410163, 0.1, 1, -0.311022, 0.34919, 0.445318, 0.133333, 1, -0.341398, 0.288521, 0.481402, 0.166667, 1, -0.360972, 0.258068, 0.504906, 0.2, 1, -0.344743, 0.282742, 0.486129, 0.233333, 1, -0.314087, 0.3427, 0.449695, 0.266667, 1, -0.278537, 0.434417, 0.406263, 0.3, 1, -0.245144, 0.551351, 0.363701, 0.366667, 1, -0.200157, 0.819633, 0.299654, 0.4, 1, -0.191007, 0.9689, 0.285956, 0.433333, 1, -0.194214, 1.13631, 0.290756, 0.466667, 1, -0.208998, 1.31245, 0.312889, 0.5, 1, -0.230619, 1.4739, 0.345259, 0.533333, 1, -0.247184, 1.58515, 0.370057, 0.566667, 1, -0.25721, 1.65978, 0.385067, 0.6, 1, -0.262036, 1.70079, 0.392292, 0.633333, 1, -0.262968, 1.71049, 0.393688, 0.666667, 1, -0.261284, 1.69044, 0.391165, 0.7, 1, -0.254453, 1.62383, 0.38094, 0.733333, 1, -0.24205, 1.51385, 0.362371, 0.766667, 1, -0.225474, 1.35723, 0.337555, 0.8, 1, -0.209087, 1.15974, 0.313022, 0.833333, 1, -0.200208, 0.9362, 0.299731, 0.866667, 1, -0.214642, 0.73557, 0.321339, 0.9, 1, -0.243253, 0.564117, 0.364172, 0.933333, 1, -0.278126, 0.429939, 0.416381, 0.966667, 1, -0.309697, 0.341132, 0.463645, 1, 1, -0.326752, 0.30579, 0.489178)
tracks/55/type = "rotation_3d"
tracks/55/imported = true
tracks/55/enabled = true
tracks/55/path = NodePath("rig/Skeleton3D:DEF-lid2.T.R")
tracks/55/interp = 1
tracks/55/loop_wrap = true
tracks/55/keys = PackedFloat32Array(0, 1, 0.718897, 0.596817, 0.129892, 0.331851, 0.0333333, 1, 0.745523, 0.563131, 0.136494, 0.329315, 0.0666667, 1, 0.78011, 0.510613, 0.143638, 0.331769, 0.1, 1, 0.804573, 0.462059, 0.149042, 0.341981, 0.133333, 1, 0.81407, 0.431505, 0.152796, 0.357418, 0.166667, 1, 0.813415, 0.421706, 0.156709, 0.368731, 0.2, 1, 0.811564, 0.434052, 0.150725, 0.360894, 0.233333, 1, 0.802282, 0.464296, 0.146764, 0.345301, 0.266667, 1, 0.78095, 0.510207, 0.146165, 0.329305, 0.3, 1, 0.750696, 0.560517, 0.149287, 0.316211, 0.333333, 1, 0.725025, 0.596777, 0.156, 0.306366, 0.366667, 1, 0.73226, 0.591602, 0.171182, 0.290688, 0.4, 1, 0.736602, 0.587854, 0.178723, 0.282673, 0.433333, 1, 0.735135, 0.589129, 0.176089, 0.285474, 0.466667, 1, 0.728678, 0.594304, 0.163865, 0.298296, 0.5, 1, 0.722017, 0.597721, 0.145809, 0.316481, 0.533333, 1, 0.719368, 0.596856, 0.131817, 0.329994, 0.566667, 1, 0.718041, 0.595767, 0.12337, 0.338022, 0.6, 1, 0.717526, 0.59504, 0.11932, 0.34183, 0.633333, 1, 0.71744, 0.59488, 0.118543, 0.342557, 0.666667, 1, 0.717594, 0.595168, 0.119947, 0.341243, 0.7, 1, 0.718319, 0.59618, 0.125681, 0.335844, 0.733333, 1, 0.720134, 0.597246, 0.136161, 0.325829, 0.8, 1, 0.728204, 0.59484, 0.163815, 0.29841, 0.833333, 1, 0.731751, 0.592189, 0.171151, 0.29079, 0.866667, 1, 0.726455, 0.595858, 0.159193, 0.303108, 0.9, 1, 0.720884, 0.596107, 0.13517, 0.326668, 0.933333, 1, 0.716831, 0.591437, 0.106683, 0.353517, 0.966667, 1, 0.714826, 0.583939, 0.0820429, 0.375909, 1, 1, 0.714203, 0.579357, 0.070205, 0.386432)
tracks/56/type = "scale_3d"
tracks/56/imported = true
tracks/56/enabled = true
tracks/56/path = NodePath("rig/Skeleton3D:DEF-lid2.T.R")
tracks/56/interp = 1
tracks/56/loop_wrap = true
tracks/56/keys = PackedFloat32Array(0, 1, 1, 0.999999, 1, 0.0333333, 1, 0.99103, 1.01825, 0.99103, 0.0666667, 1, 0.967661, 1.06897, 0.967661, 0.1, 1, 0.938836, 1.13582, 0.938836, 0.133333, 1, 0.913399, 1.1988, 0.9134, 0.166667, 1, 0.900098, 1.2343, 0.900098, 0.2, 1, 0.910697, 1.20586, 0.910697, 0.233333, 1, 0.935897, 1.14302, 0.935897, 0.266667, 1, 0.973085, 1.05851, 0.973086, 0.3, 1, 1.01847, 0.965638, 1.01847, 0.333333, 1, 1.06706, 0.878263, 1.06706, 0.366667, 1, 1.11355, 0.807557, 1.11355, 0.4, 1, 1.13782, 0.772608, 1.13782, 0.433333, 1, 1.12933, 0.78488, 1.12933, 0.466667, 1, 1.09101, 0.841602, 1.09101, 0.5, 1, 1.03932, 0.925764, 1.03932, 0.533333, 1, 1.00492, 0.99108, 1.00492, 0.566667, 1, 0.985112, 1.03072, 0.985112, 0.6, 1, 0.975982, 1.04984, 0.975982, 0.633333, 1, 0.974265, 1.05353, 0.974265, 0.666667, 1, 0.977364, 1.04686, 0.977364, 0.7, 1, 0.99031, 1.0198, 0.99031, 0.733333, 1, 1.01539, 0.970814, 1.0154, 0.766667, 1, 1.05175, 0.905744, 1.05175, 0.8, 1, 1.0901, 0.841801, 1.0901, 0.833333, 1, 1.11278, 0.807571, 1.11278, 0.866667, 1, 1.07664, 0.863399, 1.07664, 0.9, 1, 1.01498, 0.975815, 1.01498, 0.933333, 1, 0.949715, 1.11386, 0.949715, 0.966667, 1, 0.898464, 1.23947, 0.898464, 1, 1, 0.874541, 1.30749, 0.874541)
tracks/57/type = "position_3d"
tracks/57/imported = true
tracks/57/enabled = true
tracks/57/path = NodePath("rig/Skeleton3D:DEF-lid3.T.R")
tracks/57/interp = 1
tracks/57/loop_wrap = true
tracks/57/keys = PackedFloat32Array(0, 1, -0.172225, 0.517931, 0.436701, 0.0333333, 1, -0.183037, 0.473287, 0.450445, 0.0666667, 1, -0.206581, 0.394734, 0.481106, 0.1, 1, -0.238364, 0.310625, 0.523533, 0.133333, 1, -0.270262, 0.243899, 0.567022, 0.166667, 1, -0.290524, 0.21208, 0.595305, 0.2, 1, -0.272898, 0.238984, 0.572587, 0.233333, 1, -0.240726, 0.304893, 0.528681, 0.266667, 1, -0.204792, 0.40641, 0.476545, 0.3, 1, -0.173093, 0.534636, 0.425753, 0.333333, 1, -0.150843, 0.675168, 0.382485, 0.366667, 1, -0.138191, 0.809103, 0.350404, 0.4, 1, -0.131874, 0.957391, 0.334386, 0.433333, 1, -0.134088, 1.12514, 0.339999, 0.466667, 1, -0.144295, 1.30277, 0.365881, 0.5, 1, -0.159223, 1.46601, 0.403732, 0.533333, 1, -0.170659, 1.57826, 0.432731, 0.566667, 1, -0.177581, 1.65343, 0.450283, 0.6, 1, -0.180913, 1.69469, 0.458731, 0.633333, 1, -0.181557, 1.70443, 0.460363, 0.666667, 1, -0.180394, 1.68429, 0.457414, 0.7, 1, -0.175678, 1.61734, 0.445457, 0.733333, 1, -0.167114, 1.50666, 0.423743, 0.766667, 1, -0.15567, 1.34891, 0.394724, 0.8, 1, -0.144356, 1.15013, 0.366036, 0.833333, 1, -0.138227, 0.925736, 0.350494, 0.866667, 1, -0.148192, 0.726433, 0.375761, 0.9, 1, -0.167945, 0.55687, 0.425849, 0.933333, 1, -0.192022, 0.424416, 0.4869, 0.966667, 1, -0.213819, 0.336749, 0.542169, 1, 1, -0.225594, 0.301862, 0.572026)
tracks/58/type = "rotation_3d"
tracks/58/imported = true
tracks/58/enabled = true
tracks/58/path = NodePath("rig/Skeleton3D:DEF-lid3.T.R")
tracks/58/interp = 1
tracks/58/loop_wrap = true
tracks/58/keys = PackedFloat32Array(0, 1, 0.87894, 0.440964, -0.0367313, 0.177951, 0.0333333, 1, 0.856425, 0.481487, -0.0292128, 0.183991, 0.0666667, 1, 0.813824, 0.547153, -0.024196, 0.194238, 0.1, 1, 0.767978, 0.606391, -0.0254822, 0.204576, 0.133333, 1, 0.734446, 0.643812, -0.0315637, 0.212364, 0.166667, 1, 0.721701, 0.656946, -0.0354764, 0.2152, 0.2, 1, 0.733423, 0.643803, -0.0346928, 0.215421, 0.233333, 1, 0.766395, 0.607177, -0.0286505, 0.207734, 0.266667, 1, 0.818509, 0.541504, -0.0215496, 0.190661, 0.3, 1, 0.876039, 0.452965, -0.0154419, 0.164745, 0.333333, 1, 0.921329, 0.364667, -0.0104188, 0.134398, 0.366667, 1, 0.944843, 0.309017, 0.00492107, 0.108428, 0.4, 1, 0.955096, 0.280173, 0.012551, 0.0955943, 0.433333, 1, 0.951601, 0.290398, 0.00988536, 0.100132, 0.466667, 1, 0.934094, 0.335913, -0.0024815, 0.120936, 0.5, 1, 0.904755, 0.397448, -0.0207207, 0.151735, 0.533333, 1, 0.882388, 0.435462, -0.0348053, 0.174795, 0.566667, 1, 0.868764, 0.455973, -0.0432472, 0.188327, 0.6, 1, 0.862374, 0.464939, -0.0472772, 0.194698, 0.633333, 1, 0.861178, 0.466566, -0.048049, 0.19591, 0.666667, 1, 0.863334, 0.463628, -0.0466541, 0.19372, 0.7, 1, 0.872326, 0.45085, -0.0409421, 0.184676, 0.733333, 1, 0.88942, 0.42412, -0.0304447, 0.167712, 0.766667, 1, 0.912356, 0.382718, -0.0163895, 0.144449, 0.8, 1, 0.933711, 0.33696, -0.00251647, 0.120974, 0.833333, 1, 0.944663, 0.3096, 0.0049044, 0.108336, 0.866667, 1, 0.926572, 0.353245, -0.00719814, 0.128963, 0.9, 1, 0.889562, 0.423184, -0.031462, 0.169131, 0.933333, 1, 0.845424, 0.485776, -0.0597399, 0.213799, 0.966667, 1, 0.809558, 0.524443, -0.0837142, 0.250134, 1, 1, 0.794238, 0.537509, -0.0950356, 0.266903)
tracks/59/type = "scale_3d"
tracks/59/imported = true
tracks/59/enabled = true
tracks/59/path = NodePath("rig/Skeleton3D:DEF-lid3.T.R")
tracks/59/interp = 1
tracks/59/loop_wrap = true
tracks/59/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 1.00269, 0.994645, 1.00269, 0.0666667, 1, 0.986218, 1.02876, 0.986218, 0.1, 1, 0.949898, 1.11176, 0.949898, 0.133333, 1, 0.905661, 1.22006, 0.905661, 0.166667, 1, 0.878055, 1.29705, 0.878055, 0.2, 1, 0.901272, 1.23171, 0.901272, 0.233333, 1, 0.945798, 1.12173, 0.945798, 0.266667, 1, 0.986964, 1.0277, 0.986964, 0.3, 1, 1.00572, 0.988878, 1.00572, 0.333333, 1, 0.988623, 1.02315, 0.988623, 0.366667, 1, 0.958248, 1.08984, 0.958248, 0.4, 1, 0.939161, 1.13409, 0.939161, 0.433333, 1, 0.945799, 1.11904, 0.945799, 0.466667, 1, 0.973352, 1.05599, 0.973352, 0.5, 1, 0.997765, 1.00449, 0.997765, 0.533333, 1, 0.999212, 1.00158, 0.999212, 0.566667, 1, 0.99732, 1.00539, 0.99732, 0.6, 1, 0.995343, 1.00938, 0.995343, 0.633333, 1, 0.994855, 1.01037, 0.994855, 0.666667, 1, 0.995742, 1.00857, 0.995742, 0.7, 1, 0.998496, 1.00302, 0.998496, 0.733333, 1, 0.999337, 1.00133, 0.999337, 0.766667, 1, 0.991587, 1.01733, 0.991588, 0.8, 1, 0.975985, 1.05001, 0.975985, 0.833333, 1, 0.960689, 1.08351, 0.96069, 0.866667, 1, 0.982841, 1.03563, 0.982841, 0.9, 1, 0.993198, 1.01379, 0.993198, 0.933333, 1, 0.9814, 1.03937, 0.981399, 0.966667, 1, 0.954306, 1.09837, 0.954306, 1, 1, 0.935994, 1.14144, 0.935994)
tracks/60/type = "position_3d"
tracks/60/imported = true
tracks/60/enabled = true
tracks/60/path = NodePath("rig/Skeleton3D:DEF-lid4.T.R")
tracks/60/interp = 1
tracks/60/loop_wrap = true
tracks/60/keys = PackedFloat32Array(0, 1, -0.0866209, 0.458442, 0.467156, 0.0333333, 1, -0.0928986, 0.422712, 0.48144, 0.0666667, 1, -0.106524, 0.358888, 0.51335, 0.1, 1, -0.124853, 0.289426, 0.557568, 0.133333, 1, -0.143193, 0.233268, 0.602945, 0.166667, 1, -0.154802, 0.205851, 0.632493, 0.2, 1, -0.144587, 0.228582, 0.608865, 0.233333, 1, -0.126092, 0.284066, 0.563049, 0.266667, 1, -0.105625, 0.369258, 0.508471, 0.3, 1, -0.0878652, 0.477321, 0.455041, 0.333333, 1, -0.075867, 0.597619, 0.409158, 0.366667, 1, -0.0695036, 0.71617, 0.37484, 0.4, 1, -0.0663264, 0.855812, 0.357705, 0.433333, 1, -0.0674399, 1.02655, 0.36371, 0.466667, 1, -0.0725734, 1.21741, 0.391396, 0.5, 1, -0.0800812, 1.39641, 0.431888, 0.533333, 1, -0.0858334, 1.51746, 0.462908, 0.566667, 1, -0.0893148, 1.59742, 0.481685, 0.6, 1, -0.0909905, 1.64077, 0.490722, 0.633333, 1, -0.0913144, 1.65089, 0.492468, 0.666667, 1, -0.0907291, 1.63007, 0.489313, 0.7, 1, -0.0883576, 1.56014, 0.476522, 0.733333, 1, -0.0840506, 1.44324, 0.453294, 0.766667, 1, -0.0782948, 1.2755, 0.422252, 0.8, 1, -0.0726043, 1.06534, 0.391563, 0.833333, 1, -0.0695214, 0.833385, 0.374936, 0.866667, 1, -0.0745332, 0.645791, 0.401966, 0.9, 1, -0.0844684, 0.492909, 0.455547, 0.933333, 1, -0.0965781, 0.375668, 0.520855, 0.966667, 1, -0.107541, 0.29807, 0.579979, 1, 1, -0.113463, 0.26719, 0.611918)
tracks/61/type = "rotation_3d"
tracks/61/imported = true
tracks/61/enabled = true
tracks/61/path = NodePath("rig/Skeleton3D:DEF-lid4.T.R")
tracks/61/interp = 1
tracks/61/loop_wrap = true
tracks/61/keys = PackedFloat32Array(0, 1, 0.95909, 0.237543, -0.125869, 0.0887554, 0.0333333, 1, 0.940842, 0.299308, -0.121705, 0.102076, 0.0666667, 1, 0.883197, 0.433458, -0.121596, 0.131497, 0.1, 1, 0.799138, 0.565321, -0.125072, 0.161703, 0.133333, 1, 0.724385, 0.651687, -0.13077, 0.182948, 0.166667, 1, 0.697206, 0.678112, -0.134289, 0.189827, 0.2, 1, 0.722803, 0.651913, -0.133862, 0.186136, 0.233333, 1, 0.796531, 0.56731, -0.12825, 0.165075, 0.266667, 1, 0.887364, 0.426534, -0.118844, 0.128567, 0.3, 1, 0.953921, 0.266888, -0.106791, 0.0860337, 0.333333, 1, 0.978924, 0.170379, -0.0967921, 0.0575306, 0.366667, 1, 0.98669, 0.13495, -0.0798373, 0.043087, 0.4, 1, 0.989764, 0.118084, -0.0713804, 0.0364345, 0.433333, 1, 0.988731, 0.124022, -0.0743413, 0.0387688, 0.466667, 1, 0.983188, 0.151951, -0.088035, 0.050025, 0.5, 1, 0.972178, 0.196014, -0.108184, 0.0688767, 0.533333, 1, 0.96077, 0.232645, -0.123758, 0.0864915, 0.566667, 1, 0.952766, 0.254916, -0.13306, 0.0977273, 0.6, 1, 0.948591, 0.26568, -0.137493, 0.103365, 0.633333, 1, 0.947761, 0.267758, -0.138342, 0.10448, 0.666667, 1, 0.949257, 0.264001, -0.136807, 0.102463, 0.7, 1, 0.955074, 0.24874, -0.130516, 0.0944873, 0.733333, 1, 0.964549, 0.221241, -0.118947, 0.0809273, 0.766667, 1, 0.975031, 0.185632, -0.103422, 0.0644946, 0.8, 1, 0.983259, 0.151592, -0.0880534, 0.0496731, 0.833333, 1, 0.986764, 0.134512, -0.0798363, 0.0427639, 0.866667, 1, 0.980727, 0.162845, -0.0932399, 0.0544198, 0.9, 1, 0.963337, 0.224867, -0.120143, 0.0835577, 0.933333, 1, 0.933707, 0.299883, -0.151259, 0.124021, 0.966667, 1, 0.899478, 0.364433, -0.177096, 0.163599, 1, 1, 0.879943, 0.394592, -0.189012, 0.185125)
tracks/62/type = "scale_3d"
tracks/62/imported = true
tracks/62/enabled = true
tracks/62/path = NodePath("rig/Skeleton3D:DEF-lid4.T.R")
tracks/62/interp = 1
tracks/62/loop_wrap = true
tracks/62/keys = PackedFloat32Array(0, 1, 1, 0.999999, 1, 0.0333333, 1, 1.05266, 0.904236, 1.05266, 0.0666667, 1, 1.10306, 0.823778, 1.10306, 0.1, 1, 1.09607, 0.835612, 1.09607, 0.133333, 1, 1.03702, 0.931267, 1.03702, 0.166667, 1, 0.991699, 1.01681, 0.991699, 0.2, 1, 1.03202, 0.940034, 1.03202, 0.233333, 1, 1.09241, 0.841603, 1.09241, 0.266667, 1, 1.09446, 0.838022, 1.09446, 0.3, 1, 1.01924, 0.972407, 1.01924, 0.333333, 1, 0.907411, 1.21449, 0.907411, 0.366667, 1, 0.841566, 1.41799, 0.841566, 0.4, 1, 0.807576, 1.53475, 0.807576, 0.433333, 1, 0.819518, 1.49444, 0.819518, 0.466667, 1, 0.873301, 1.31763, 0.873301, 0.5, 1, 0.946667, 1.11585, 0.946667, 0.533333, 1, 0.99313, 1.01563, 0.99313, 0.566667, 1, 1.01859, 0.964187, 1.01859, 0.6, 1, 1.02977, 0.943026, 1.02977, 0.633333, 1, 1.03179, 0.939338, 1.03179, 0.666667, 1, 1.02815, 0.945987, 1.02815, 0.7, 1, 1.01225, 0.976121, 1.01225, 0.733333, 1, 0.979131, 1.04491, 0.979131, 0.766667, 1, 0.928887, 1.16486, 0.928887, 0.8, 1, 0.874628, 1.30861, 0.874628, 0.833333, 1, 0.842481, 1.4089, 0.842481, 0.866667, 1, 0.893797, 1.25505, 0.893797, 0.9, 1, 0.977731, 1.05757, 0.977731, 0.933333, 1, 1.0526, 0.905282, 1.0526, 0.966667, 1, 1.09602, 0.832499, 1.09602, 1, 1, 1.10494, 0.81907, 1.10494)
tracks/63/type = "position_3d"
tracks/63/imported = true
tracks/63/enabled = true
tracks/63/path = NodePath("rig/Skeleton3D:DEF-lip1.B.L")
tracks/63/interp = 1
tracks/63/loop_wrap = true
tracks/63/keys = PackedFloat32Array(0, 1, 0.129234, 0.0935858, 0.41924, 0.0333333, 1, 0.133007, 0.0884056, 0.431482, 0.0666667, 1, 0.141457, 0.0785632, 0.458892, 0.1, 1, 0.153193, 0.0671666, 0.496964, 0.133333, 1, 0.165259, 0.0573242, 0.536107, 0.166667, 1, 0.173132, 0.052144, 0.56165, 0.2, 1, 0.166882, 0.0561802, 0.541373, 0.233333, 1, 0.154698, 0.0659125, 0.501847, 0.266667, 1, 0.140107, 0.0803584, 0.454393, 0.3, 1, 0.125711, 0.09824, 0.407381, 0.333333, 1, 0.113189, 0.117983, 0.366232, 0.366667, 1, 0.10377, 0.137243, 0.334874, 0.4, 1, 0.0991869, 0.218215, 0.318934, 0.433333, 1, 0.101096, 0.403143, 0.323667, 0.466667, 1, 0.109099, 0.67344, 0.347736, 0.5, 1, 0.12075, 0.949684, 0.383215, 0.533333, 1, 0.129831, 1.12534, 0.41045, 0.566667, 1, 0.135471, 1.23517, 0.427078, 0.6, 1, 0.138326, 1.29209, 0.4354, 0.633333, 1, 0.139039, 1.30575, 0.437594, 0.666667, 1, 0.138248, 1.2825, 0.435716, 0.7, 1, 0.134511, 1.19649, 0.42533, 0.733333, 1, 0.127701, 1.04372, 0.405496, 0.766667, 1, 0.118625, 0.81726, 0.37847, 0.8, 1, 0.109628, 0.540046, 0.351519, 0.833333, 1, 0.104592, 0.264871, 0.336915, 0.866667, 1, 0.111756, 0.150641, 0.361152, 0.9, 1, 0.126309, 0.100532, 0.409068, 0.933333, 1, 0.144184, 0.0766679, 0.467514, 0.966667, 1, 0.160445, 0.0608478, 0.520491, 1, 1, 0.169281, 0.0545439, 0.549154)
tracks/64/type = "rotation_3d"
tracks/64/imported = true
tracks/64/enabled = true
tracks/64/path = NodePath("rig/Skeleton3D:DEF-lip1.B.L")
tracks/64/interp = 1
tracks/64/loop_wrap = true
tracks/64/keys = PackedFloat32Array(0, 1, -0.458978, -0.21784, -0.415744, 0.754348, 0.0333333, 1, -0.464212, -0.215963, -0.420793, 0.748866, 0.0666667, 1, -0.473653, -0.212505, -0.429924, 0.738688, 0.1, 1, -0.483918, -0.208626, -0.43988, 0.72719, 0.133333, 1, -0.492199, -0.205402, -0.447936, 0.717568, 0.166667, 1, -0.496226, -0.203803, -0.451865, 0.71277, 0.2, 1, -0.49311, -0.205042, -0.448824, 0.71649, 0.233333, 1, -0.484988, -0.208214, -0.44092, 0.725963, 0.266667, 1, -0.471171, -0.213582, -0.427595, 0.741312, 0.3, 1, -0.451061, -0.221295, -0.408333, 0.762127, 0.333333, 1, -0.42419, -0.231382, -0.382689, 0.787448, 0.366667, 1, -0.390831, -0.244287, -0.351715, 0.814783, 0.4, 1, -0.369029, -0.253885, -0.332872, 0.829793, 0.433333, 1, -0.370324, -0.256595, -0.33735, 0.826568, 0.466667, 1, -0.392464, -0.252493, -0.363075, 0.806471, 0.5, 1, -0.419908, -0.245637, -0.395155, 0.779226, 0.533333, 1, -0.433541, -0.242835, -0.41385, 0.762759, 0.566667, 1, -0.440152, -0.242062, -0.424854, 0.753108, 0.6, 1, -0.442819, -0.242158, -0.430848, 0.748092, 0.633333, 1, -0.44365, -0.242224, -0.433636, 0.745964, 0.666667, 1, -0.443904, -0.24169, -0.434262, 0.745622, 0.7, 1, -0.441919, -0.240806, -0.43007, 0.749507, 0.733333, 1, -0.435262, -0.240839, -0.419701, 0.759206, 0.766667, 1, -0.422263, -0.242359, -0.402189, 0.775371, 0.8, 1, -0.406321, -0.244198, -0.381481, 0.793564, 0.833333, 1, -0.396503, -0.243798, -0.367007, 0.80539, 0.866667, 1, -0.420212, -0.233987, -0.385366, 0.787506, 0.9, 1, -0.450146, -0.222351, -0.410565, 0.76116, 0.933333, 1, -0.474811, -0.212485, -0.43205, 0.736707, 0.966667, 1, -0.489349, -0.206521, -0.445159, 0.720916, 1, 1, -0.494397, -0.204532, -0.450079, 0.71496)
tracks/65/type = "scale_3d"
tracks/65/imported = true
tracks/65/enabled = true
tracks/65/path = NodePath("rig/Skeleton3D:DEF-lip1.B.L")
tracks/65/interp = 1
tracks/65/loop_wrap = true
tracks/65/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.987865, 1.02483, 0.987865, 0.0666667, 1, 0.961655, 1.08258, 0.961655, 0.1, 1, 0.927311, 1.16512, 0.927311, 0.133333, 1, 0.893968, 1.25177, 0.893968, 0.166667, 1, 0.873948, 1.30927, 0.873948, 0.2, 1, 0.889719, 1.26357, 0.889719, 0.233333, 1, 0.923131, 1.1759, 0.923131, 0.266667, 1, 0.966148, 1.07408, 0.966148, 0.3, 1, 1.01099, 0.979431, 1.01099, 0.333333, 1, 1.05015, 0.906767, 1.05015, 0.366667, 1, 1.07154, 0.871198, 1.07154, 0.4, 1, 1.07723, 0.861759, 1.07723, 0.433333, 1, 1.07084, 0.872211, 1.07084, 0.466667, 1, 1.05336, 0.901834, 1.05336, 0.5, 1, 1.02306, 0.955428, 1.02306, 0.533333, 1, 0.996293, 1.00799, 0.996293, 0.566667, 1, 0.979627, 1.04224, 0.979626, 0.6, 1, 0.971198, 1.06021, 0.971198, 0.633333, 1, 0.969134, 1.06472, 0.969134, 0.666667, 1, 0.971551, 1.05942, 0.971551, 0.7, 1, 0.983016, 1.03496, 0.983016, 0.733333, 1, 1.0044, 0.991946, 1.0044, 0.766667, 1, 1.03283, 0.938455, 1.03283, 0.8, 1, 1.06069, 0.88896, 1.0607, 0.833333, 1, 1.07562, 0.86433, 1.07562, 0.866667, 1, 1.05575, 0.897402, 1.05575, 0.9, 1, 1.00961, 0.98427, 1.00961, 0.933333, 1, 0.954316, 1.10233, 0.954316, 0.966667, 1, 0.906751, 1.21687, 0.906752, 1, 1, 0.883522, 1.28105, 0.883522)
tracks/66/type = "position_3d"
tracks/66/imported = true
tracks/66/enabled = true
tracks/66/path = NodePath("rig/Skeleton3D:DEF-lip2.B.L")
tracks/66/interp = 1
tracks/66/loop_wrap = true
tracks/66/keys = PackedFloat32Array(0, 1, 0.244186, 0.125964, 0.348183, 0.0333333, 1, 0.251316, 0.118991, 0.35835, 0.0666667, 1, 0.267282, 0.105744, 0.381115, 0.133333, 1, 0.312255, 0.0771568, 0.445242, 0.166667, 1, 0.327132, 0.0701844, 0.466456, 0.2, 1, 0.315322, 0.0756169, 0.449615, 0.233333, 1, 0.2923, 0.0887162, 0.416789, 0.266667, 1, 0.264731, 0.108409, 0.377404, 0.3, 1, 0.23753, 0.133313, 0.338427, 0.333333, 1, 0.21387, 0.161737, 0.304366, 0.366667, 1, 0.196174, 0.191173, 0.278613, 0.4, 1, 0.187731, 0.278781, 0.265854, 0.433333, 1, 0.191677, 0.46339, 0.270489, 0.466667, 1, 0.207273, 0.726878, 0.29143, 0.5, 1, 0.229904, 0.994171, 0.322103, 0.533333, 1, 0.247747, 1.16466, 0.345987, 0.566667, 1, 0.259017, 1.27156, 0.36086, 0.6, 1, 0.264897, 1.32694, 0.368532, 0.633333, 1, 0.266562, 1.33982, 0.370743, 0.666667, 1, 0.265179, 1.31617, 0.369163, 0.7, 1, 0.257843, 1.23093, 0.359849, 0.733333, 1, 0.24445, 1.08067, 0.342286, 0.766667, 1, 0.226631, 0.858737, 0.31855, 0.833333, 1, 0.19882, 0.31486, 0.281696, 0.866667, 1, 0.211925, 0.194162, 0.301116, 0.9, 1, 0.239052, 0.13514, 0.340334, 0.933333, 1, 0.272566, 0.103153, 0.388474, 0.966667, 1, 0.30316, 0.0818993, 0.432273, 1, 1, 0.319854, 0.0734145, 0.456078)
tracks/67/type = "rotation_3d"
tracks/67/imported = true
tracks/67/enabled = true
tracks/67/path = NodePath("rig/Skeleton3D:DEF-lip2.B.L")
tracks/67/interp = 1
tracks/67/loop_wrap = true
tracks/67/keys = PackedFloat32Array(0, 1, -0.358415, -0.169806, -0.310324, 0.863947, 0.0333333, 1, -0.370822, -0.166984, -0.321854, 0.854996, 0.0666667, 1, -0.394502, -0.161451, -0.344018, 0.836632, 0.1, 1, -0.421258, -0.154866, -0.369258, 0.813761, 0.133333, 1, -0.443581, -0.14907, -0.390493, 0.792798, 0.166667, 1, -0.454749, -0.146069, -0.40121, 0.7816, 0.2, 1, -0.446091, -0.148401, -0.392897, 0.790324, 0.233333, 1, -0.424135, -0.154137, -0.371991, 0.811156, 0.266667, 1, -0.388963, -0.163051, -0.339074, 0.840923, 0.3, 1, -0.342277, -0.174443, -0.296131, 0.874484, 0.333333, 1, -0.288086, -0.187372, -0.24714, 0.905991, 0.366667, 1, -0.237916, -0.201172, -0.203842, 0.928102, 0.4, 1, -0.209397, -0.211292, -0.181631, 0.937293, 0.433333, 1, -0.211111, -0.215622, -0.188857, 0.934491, 0.466667, 1, -0.240224, -0.214396, -0.223631, 0.919954, 0.5, 1, -0.282827, -0.210484, -0.274067, 0.894759, 0.533333, 1, -0.309558, -0.209781, -0.310642, 0.873881, 0.566667, 1, -0.32346, -0.210766, -0.333315, 0.860146, 0.6, 1, -0.329571, -0.212207, -0.346049, 0.852409, 0.633333, 1, -0.331578, -0.213038, -0.351767, 0.849077, 0.666667, 1, -0.33201, -0.212503, -0.352529, 0.848725, 0.7, 1, -0.327049, -0.21052, -0.342394, 0.85527, 0.733333, 1, -0.312394, -0.208915, -0.319389, 0.869917, 0.766667, 1, -0.287098, -0.208249, -0.28437, 0.890697, 0.8, 1, -0.258502, -0.20742, -0.246182, 0.910795, 0.833333, 1, -0.243178, -0.204386, -0.222561, 0.921714, 0.866667, 1, -0.281621, -0.192488, -0.251248, 0.905821, 0.9, 1, -0.342055, -0.176641, -0.301273, 0.872371, 0.933333, 1, -0.398292, -0.161517, -0.349621, 0.832491, 0.966667, 1, -0.435753, -0.151127, -0.383015, 0.800362, 1, 1, -0.449648, -0.147448, -0.396306, 0.786777)
tracks/68/type = "scale_3d"
tracks/68/imported = true
tracks/68/enabled = true
tracks/68/path = NodePath("rig/Skeleton3D:DEF-lip2.B.L")
tracks/68/interp = 1
tracks/68/loop_wrap = true
tracks/68/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.99775, 1.00452, 0.99775, 0.0666667, 1, 0.987595, 1.02547, 0.987595, 0.1, 1, 0.968421, 1.06712, 0.968421, 0.133333, 1, 0.945118, 1.11977, 0.945118, 0.166667, 1, 0.928584, 1.15973, 0.928584, 0.2, 1, 0.94175, 1.1277, 0.94175, 0.233333, 1, 0.965582, 1.07354, 0.965582, 0.266667, 1, 0.987291, 1.02621, 0.987291, 0.3, 1, 0.995865, 1.00847, 0.995865, 0.333333, 1, 0.982073, 1.03684, 0.982073, 0.366667, 1, 0.943185, 1.12548, 0.943185, 0.4, 1, 0.91582, 1.19316, 0.91582, 0.433333, 1, 0.916789, 1.19079, 0.916789, 0.466667, 1, 0.942915, 1.12548, 0.942915, 0.5, 1, 0.971026, 1.06057, 0.971026, 0.533333, 1, 0.976154, 1.04948, 0.976154, 0.566667, 1, 0.975963, 1.04987, 0.975963, 0.6, 1, 0.974649, 1.0527, 0.974649, 0.633333, 1, 0.974611, 1.05278, 0.974611, 0.666667, 1, 0.976447, 1.04882, 0.976447, 0.7, 1, 0.981019, 1.03909, 0.981019, 0.733333, 1, 0.98449, 1.03177, 0.98449, 0.766667, 1, 0.980518, 1.04028, 0.980518, 0.8, 1, 0.969597, 1.06379, 0.969597, 0.833333, 1, 0.959053, 1.08721, 0.959053, 0.866667, 1, 0.982306, 1.0368, 0.982305, 0.9, 1, 0.993028, 1.01414, 0.993028, 0.933333, 1, 0.981589, 1.03892, 0.981589, 0.966667, 1, 0.954962, 1.09685, 0.954962, 1, 1, 0.93673, 1.13965, 0.93673)
tracks/69/type = "position_3d"
tracks/69/imported = true
tracks/69/enabled = true
tracks/69/path = NodePath("rig/Skeleton3D:DEF-lip1.B.R")
tracks/69/interp = 1
tracks/69/loop_wrap = true
tracks/69/keys = PackedFloat32Array(0, 1, -0.129234, 0.0935858, 0.41924, 0.0333333, 1, -0.133007, 0.0884056, 0.431482, 0.0666667, 1, -0.141457, 0.0785632, 0.458893, 0.1, 1, -0.153193, 0.0671666, 0.496964, 0.133333, 1, -0.165259, 0.0573242, 0.536107, 0.166667, 1, -0.173132, 0.052144, 0.56165, 0.2, 1, -0.166882, 0.0561802, 0.541373, 0.233333, 1, -0.154698, 0.0659125, 0.501847, 0.266667, 1, -0.140107, 0.0803585, 0.454393, 0.3, 1, -0.125711, 0.09824, 0.407381, 0.333333, 1, -0.113189, 0.117983, 0.366232, 0.366667, 1, -0.10377, 0.137243, 0.334874, 0.4, 1, -0.0991869, 0.218215, 0.318934, 0.433333, 1, -0.101096, 0.403143, 0.323667, 0.466667, 1, -0.109099, 0.67344, 0.347736, 0.5, 1, -0.12075, 0.949684, 0.383215, 0.533333, 1, -0.129831, 1.12534, 0.41045, 0.566667, 1, -0.135471, 1.23517, 0.427078, 0.6, 1, -0.138326, 1.29209, 0.4354, 0.633333, 1, -0.139039, 1.30575, 0.437594, 0.666667, 1, -0.138248, 1.2825, 0.435716, 0.7, 1, -0.134511, 1.19649, 0.42533, 0.733333, 1, -0.127701, 1.04372, 0.405496, 0.766667, 1, -0.118625, 0.81726, 0.37847, 0.8, 1, -0.109627, 0.540046, 0.351519, 0.833333, 1, -0.104592, 0.264871, 0.336915, 0.866667, 1, -0.111756, 0.150641, 0.361152, 0.9, 1, -0.126309, 0.100532, 0.409068, 0.933333, 1, -0.144184, 0.0766679, 0.467514, 0.966667, 1, -0.160445, 0.0608478, 0.520491, 1, 1, -0.169281, 0.0545439, 0.549154)
tracks/70/type = "rotation_3d"
tracks/70/imported = true
tracks/70/enabled = true
tracks/70/path = NodePath("rig/Skeleton3D:DEF-lip1.B.R")
tracks/70/interp = 1
tracks/70/loop_wrap = true
tracks/70/keys = PackedFloat32Array(0, 1, -0.458978, 0.21784, 0.415744, 0.754348, 0.0333333, 1, -0.464212, 0.215964, 0.420793, 0.748866, 0.0666667, 1, -0.473653, 0.212505, 0.429924, 0.738688, 0.1, 1, -0.483918, 0.208626, 0.43988, 0.727189, 0.133333, 1, -0.492199, 0.205402, 0.447936, 0.717568, 0.166667, 1, -0.496226, 0.203804, 0.451866, 0.71277, 0.2, 1, -0.49311, 0.205042, 0.448824, 0.71649, 0.233333, 1, -0.484988, 0.208214, 0.44092, 0.725963, 0.266667, 1, -0.471171, 0.213582, 0.427595, 0.741312, 0.3, 1, -0.451061, 0.221295, 0.408333, 0.762126, 0.333333, 1, -0.424191, 0.231382, 0.38269, 0.787447, 0.366667, 1, -0.390831, 0.244287, 0.351715, 0.814783, 0.4, 1, -0.369029, 0.253885, 0.332872, 0.829793, 0.433333, 1, -0.370324, 0.256595, 0.33735, 0.826568, 0.466667, 1, -0.392465, 0.252493, 0.363076, 0.806471, 0.5, 1, -0.419909, 0.245636, 0.395155, 0.779225, 0.533333, 1, -0.433542, 0.242835, 0.413851, 0.762758, 0.566667, 1, -0.440152, 0.242062, 0.424855, 0.753108, 0.6, 1, -0.442818, 0.242158, 0.430848, 0.748092, 0.633333, 1, -0.44365, 0.242225, 0.433637, 0.745963, 0.666667, 1, -0.443904, 0.24169, 0.434263, 0.745621, 0.7, 1, -0.441919, 0.240806, 0.43007, 0.749506, 0.733333, 1, -0.435262, 0.240839, 0.419701, 0.759207, 0.766667, 1, -0.422263, 0.242359, 0.402189, 0.775371, 0.8, 1, -0.406321, 0.244198, 0.381482, 0.793563, 0.833333, 1, -0.396503, 0.243798, 0.367008, 0.80539, 0.866667, 1, -0.420212, 0.233987, 0.385366, 0.787506, 0.9, 1, -0.450146, 0.222351, 0.410565, 0.76116, 0.933333, 1, -0.474811, 0.212485, 0.43205, 0.736707, 0.966667, 1, -0.489349, 0.206521, 0.445159, 0.720916, 1, 1, -0.494397, 0.204532, 0.450079, 0.71496)
tracks/71/type = "scale_3d"
tracks/71/imported = true
tracks/71/enabled = true
tracks/71/path = NodePath("rig/Skeleton3D:DEF-lip1.B.R")
tracks/71/interp = 1
tracks/71/loop_wrap = true
tracks/71/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.987865, 1.02483, 0.987865, 0.0666667, 1, 0.961655, 1.08258, 0.961655, 0.1, 1, 0.927311, 1.16512, 0.927311, 0.133333, 1, 0.893968, 1.25177, 0.893968, 0.166667, 1, 0.873948, 1.30927, 0.873948, 0.2, 1, 0.889719, 1.26357, 0.889719, 0.233333, 1, 0.923131, 1.1759, 0.923131, 0.266667, 1, 0.966148, 1.07408, 0.966148, 0.3, 1, 1.01099, 0.979432, 1.01099, 0.333333, 1, 1.05015, 0.906768, 1.05015, 0.366667, 1, 1.07154, 0.871197, 1.07154, 0.4, 1, 1.07723, 0.861759, 1.07723, 0.433333, 1, 1.07084, 0.872211, 1.07085, 0.466667, 1, 1.05336, 0.901834, 1.05336, 0.5, 1, 1.02306, 0.955428, 1.02306, 0.533333, 1, 0.996293, 1.00799, 0.996293, 0.566667, 1, 0.979626, 1.04224, 0.979626, 0.6, 1, 0.971198, 1.06021, 0.971198, 0.633333, 1, 0.969134, 1.06472, 0.969134, 0.666667, 1, 0.971551, 1.05942, 0.971551, 0.7, 1, 0.983016, 1.03496, 0.983016, 0.733333, 1, 1.0044, 0.991946, 1.0044, 0.766667, 1, 1.03283, 0.938455, 1.03283, 0.8, 1, 1.06069, 0.88896, 1.0607, 0.833333, 1, 1.07562, 0.86433, 1.07562, 0.866667, 1, 1.05575, 0.897403, 1.05575, 0.9, 1, 1.00961, 0.98427, 1.00961, 0.933333, 1, 0.954316, 1.10233, 0.954316, 0.966667, 1, 0.906751, 1.21687, 0.906751, 1, 1, 0.883522, 1.28105, 0.883522)
tracks/72/type = "position_3d"
tracks/72/imported = true
tracks/72/enabled = true
tracks/72/path = NodePath("rig/Skeleton3D:DEF-lip2.B.R")
tracks/72/interp = 1
tracks/72/loop_wrap = true
tracks/72/keys = PackedFloat32Array(0, 1, -0.244186, 0.125964, 0.348183, 0.0333333, 1, -0.251316, 0.118991, 0.35835, 0.0666667, 1, -0.267281, 0.105744, 0.381115, 0.133333, 1, -0.312255, 0.0771567, 0.445242, 0.166667, 1, -0.327132, 0.0701842, 0.466456, 0.2, 1, -0.315322, 0.0756169, 0.449615, 0.233333, 1, -0.2923, 0.0887163, 0.416789, 0.266667, 1, -0.264731, 0.108409, 0.377404, 0.3, 1, -0.23753, 0.133313, 0.338427, 0.333333, 1, -0.21387, 0.161737, 0.304366, 0.366667, 1, -0.196174, 0.191173, 0.278613, 0.4, 1, -0.187731, 0.278781, 0.265854, 0.433333, 1, -0.191677, 0.46339, 0.270489, 0.466667, 1, -0.207273, 0.726878, 0.29143, 0.5, 1, -0.229904, 0.994171, 0.322103, 0.533333, 1, -0.247747, 1.16466, 0.345987, 0.566667, 1, -0.259017, 1.27156, 0.36086, 0.6, 1, -0.264897, 1.32694, 0.368532, 0.633333, 1, -0.266562, 1.33982, 0.370743, 0.666667, 1, -0.265179, 1.31617, 0.369164, 0.7, 1, -0.257843, 1.23093, 0.359849, 0.733333, 1, -0.24445, 1.08067, 0.342286, 0.766667, 1, -0.226631, 0.858737, 0.31855, 0.833333, 1, -0.19882, 0.31486, 0.281696, 0.866667, 1, -0.211925, 0.194162, 0.301116, 0.9, 1, -0.239052, 0.13514, 0.340334, 0.933333, 1, -0.272566, 0.103153, 0.388474, 0.966667, 1, -0.30316, 0.0818993, 0.432273, 1, 1, -0.319854, 0.0734145, 0.456078)
tracks/73/type = "rotation_3d"
tracks/73/imported = true
tracks/73/enabled = true
tracks/73/path = NodePath("rig/Skeleton3D:DEF-lip2.B.R")
tracks/73/interp = 1
tracks/73/loop_wrap = true
tracks/73/keys = PackedFloat32Array(0, 1, -0.358414, 0.169807, 0.310324, 0.863947, 0.0333333, 1, -0.370822, 0.166984, 0.321854, 0.854995, 0.0666667, 1, -0.394502, 0.161451, 0.344018, 0.836632, 0.1, 1, -0.421258, 0.154866, 0.369258, 0.813761, 0.133333, 1, -0.443581, 0.14907, 0.390493, 0.792798, 0.166667, 1, -0.454749, 0.146069, 0.40121, 0.7816, 0.2, 1, -0.446091, 0.148401, 0.392896, 0.790324, 0.233333, 1, -0.424135, 0.154137, 0.371991, 0.811156, 0.266667, 1, -0.388962, 0.163051, 0.339074, 0.840923, 0.3, 1, -0.342277, 0.174443, 0.296131, 0.874484, 0.333333, 1, -0.288086, 0.187372, 0.24714, 0.905991, 0.366667, 1, -0.237916, 0.201172, 0.203842, 0.928102, 0.4, 1, -0.209396, 0.211292, 0.18163, 0.937294, 0.433333, 1, -0.21111, 0.215622, 0.188857, 0.934491, 0.466667, 1, -0.240224, 0.214396, 0.223631, 0.919954, 0.5, 1, -0.282826, 0.210485, 0.274067, 0.894759, 0.533333, 1, -0.309558, 0.209781, 0.310642, 0.873881, 0.566667, 1, -0.323461, 0.210766, 0.333315, 0.860147, 0.6, 1, -0.329571, 0.212207, 0.346049, 0.852409, 0.633333, 1, -0.331578, 0.213038, 0.351767, 0.849077, 0.666667, 1, -0.33201, 0.212504, 0.352529, 0.848725, 0.7, 1, -0.327049, 0.21052, 0.342394, 0.85527, 0.733333, 1, -0.312394, 0.208915, 0.319389, 0.869917, 0.766667, 1, -0.287098, 0.208249, 0.284371, 0.890697, 0.8, 1, -0.258502, 0.207421, 0.246182, 0.910795, 0.833333, 1, -0.243177, 0.204387, 0.222561, 0.921714, 0.866667, 1, -0.281621, 0.192489, 0.251248, 0.905821, 0.9, 1, -0.342055, 0.176641, 0.301273, 0.872371, 0.933333, 1, -0.398292, 0.161517, 0.349621, 0.832491, 0.966667, 1, -0.435753, 0.151128, 0.383015, 0.800362, 1, 1, -0.449648, 0.147448, 0.396306, 0.786777)
tracks/74/type = "scale_3d"
tracks/74/imported = true
tracks/74/enabled = true
tracks/74/path = NodePath("rig/Skeleton3D:DEF-lip2.B.R")
tracks/74/interp = 1
tracks/74/loop_wrap = true
tracks/74/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.99775, 1.00452, 0.99775, 0.0666667, 1, 0.987595, 1.02547, 0.987595, 0.1, 1, 0.968421, 1.06712, 0.968421, 0.133333, 1, 0.945118, 1.11977, 0.945119, 0.166667, 1, 0.928584, 1.15973, 0.928584, 0.2, 1, 0.94175, 1.1277, 0.94175, 0.233333, 1, 0.965583, 1.07354, 0.965583, 0.266667, 1, 0.987291, 1.02621, 0.987291, 0.3, 1, 0.995866, 1.00847, 0.995866, 0.333333, 1, 0.982073, 1.03684, 0.982073, 0.366667, 1, 0.943185, 1.12548, 0.943185, 0.4, 1, 0.91582, 1.19316, 0.91582, 0.433333, 1, 0.916789, 1.19079, 0.916789, 0.466667, 1, 0.942915, 1.12548, 0.942915, 0.5, 1, 0.971027, 1.06057, 0.971026, 0.533333, 1, 0.976154, 1.04948, 0.976154, 0.566667, 1, 0.975963, 1.04987, 0.975963, 0.6, 1, 0.974649, 1.0527, 0.97465, 0.633333, 1, 0.974611, 1.05278, 0.974611, 0.666667, 1, 0.976446, 1.04883, 0.976446, 0.7, 1, 0.981019, 1.03909, 0.981019, 0.733333, 1, 0.98449, 1.03177, 0.98449, 0.766667, 1, 0.980518, 1.04028, 0.980518, 0.8, 1, 0.969597, 1.06379, 0.969597, 0.833333, 1, 0.959053, 1.08721, 0.959053, 0.866667, 1, 0.982306, 1.0368, 0.982306, 0.9, 1, 0.993029, 1.01414, 0.993029, 0.933333, 1, 0.981589, 1.03892, 0.981589, 0.966667, 1, 0.954963, 1.09685, 0.954963, 1, 1, 0.936731, 1.13965, 0.936731)
tracks/75/type = "position_3d"
tracks/75/imported = true
tracks/75/enabled = true
tracks/75/path = NodePath("rig/Skeleton3D:DEF-lip1.T.L")
tracks/75/interp = 1
tracks/75/loop_wrap = true
tracks/75/keys = PackedFloat32Array(0, 1, 0.126628, 0.251461, 0.471815, 0.0333333, 1, 0.130326, 0.237542, 0.485592, 0.0666667, 1, 0.138605, 0.211095, 0.51644, 0.1, 1, 0.150104, 0.180474, 0.559286, 0.133333, 1, 0.161927, 0.154027, 0.603338, 0.166667, 1, 0.169642, 0.140108, 0.632084, 0.2, 1, 0.163518, 0.150953, 0.609264, 0.233333, 1, 0.151579, 0.177104, 0.564781, 0.266667, 1, 0.137282, 0.217958, 0.511512, 0.3, 1, 0.123177, 0.272845, 0.458956, 0.333333, 1, 0.110907, 0.341026, 0.413243, 0.366667, 1, 0.10167, 0.422177, 0.37864, 0.4, 1, 0.0971645, 0.54995, 0.361456, 0.433333, 1, 0.0990103, 0.744277, 0.367712, 0.466667, 1, 0.106819, 0.986223, 0.395939, 0.5, 1, 0.11819, 1.21799, 0.437179, 0.533333, 1, 0.127039, 1.3673, 0.46889, 0.566667, 1, 0.132521, 1.46213, 0.488194, 0.6, 1, 0.135283, 1.51026, 0.49759, 0.633333, 1, 0.13596, 1.51762, 0.499526, 0.666667, 1, 0.135177, 1.48832, 0.496398, 0.7, 1, 0.131534, 1.40074, 0.483325, 0.733333, 1, 0.124899, 1.25465, 0.459569, 0.766667, 1, 0.116055, 1.04404, 0.427843, 0.8, 1, 0.107289, 0.785305, 0.396461, 0.833333, 1, 0.102398, 0.517286, 0.379336, 0.866667, 1, 0.109448, 0.366134, 0.406396, 0.9, 1, 0.123734, 0.270287, 0.460307, 0.933333, 1, 0.141268, 0.20604, 0.526123, 0.966667, 1, 0.15721, 0.163495, 0.585764, 1, 1, 0.165868, 0.146557, 0.618021)
tracks/76/type = "rotation_3d"
tracks/76/imported = true
tracks/76/enabled = true
tracks/76/path = NodePath("rig/Skeleton3D:DEF-lip1.T.L")
tracks/76/interp = 1
tracks/76/loop_wrap = true
tracks/76/keys = PackedFloat32Array(0, 1, -0.707902, -0.316721, -0.286142, 0.562749, 0.0333333, 1, -0.704214, -0.319894, -0.283682, 0.566812, 0.0666667, 1, -0.697429, -0.325642, -0.279219, 0.574097, 0.1, 1, -0.689832, -0.331934, -0.274291, 0.581993, 0.133333, 1, -0.683528, -0.337047, -0.270258, 0.588345, 0.166667, 1, -0.680402, -0.339551, -0.268281, 0.591426, 0.2, 1, -0.682824, -0.337613, -0.269812, 0.589042, 0.233333, 1, -0.689026, -0.332593, -0.273774, 0.582814, 0.266667, 1, -0.699779, -0.323582, -0.280891, 0.571581, 0.3, 1, -0.715797, -0.30938, -0.292077, 0.553723, 0.333333, 1, -0.737532, -0.288435, -0.308352, 0.527039, 0.366667, 1, -0.764652, -0.259231, -0.332533, 0.487368, 0.4, 1, -0.782641, -0.237009, -0.352363, 0.455128, 0.433333, 1, -0.785464, -0.232971, -0.35893, 0.447146, 0.466667, 1, -0.774019, -0.247739, -0.351689, 0.464579, 0.5, 1, -0.754964, -0.271056, -0.337909, 0.492317, 0.533333, 1, -0.742521, -0.285927, -0.33077, 0.507444, 0.566667, 1, -0.734971, -0.295087, -0.327528, 0.515235, 0.6, 1, -0.730414, -0.300888, -0.326186, 0.519196, 0.633333, 1, -0.727324, -0.304948, -0.325205, 0.521774, 0.666667, 1, -0.724726, -0.308206, -0.323582, 0.524476, 0.7, 1, -0.724616, -0.308346, -0.322034, 0.525499, 0.733333, 1, -0.728397, -0.30423, -0.322167, 0.522581, 0.766667, 1, -0.736252, -0.295783, -0.324403, 0.514984, 0.8, 1, -0.744961, -0.286214, -0.32662, 0.506394, 0.833333, 1, -0.748303, -0.282178, -0.324327, 0.505203, 0.866667, 1, -0.732354, -0.29694, -0.308542, 0.529421, 0.9, 1, -0.712935, -0.313584, -0.292108, 0.555034, 0.933333, 1, -0.696166, -0.327089, -0.279136, 0.574847, 0.966667, 1, -0.685715, -0.335282, -0.27165, 0.586163, 1, 1, -0.681827, -0.338412, -0.26918, 0.590027)
tracks/77/type = "scale_3d"
tracks/77/imported = true
tracks/77/enabled = true
tracks/77/path = NodePath("rig/Skeleton3D:DEF-lip1.T.L")
tracks/77/interp = 1
tracks/77/loop_wrap = true
tracks/77/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.986839, 1.02699, 0.986839, 0.0666667, 1, 0.959098, 1.08851, 0.959098, 0.1, 1, 0.923403, 1.17515, 0.923403, 0.133333, 1, 0.889241, 1.26513, 0.889241, 0.166667, 1, 0.868964, 1.32433, 0.868964, 0.2, 1, 0.884925, 1.27731, 0.884925, 0.233333, 1, 0.919111, 1.18636, 0.919111, 0.266667, 1, 0.963857, 1.07951, 0.963857, 0.3, 1, 1.01154, 0.978586, 1.01154, 0.333333, 1, 1.05458, 0.899165, 1.05458, 0.366667, 1, 1.07793, 0.860949, 1.07793, 0.4, 1, 1.07865, 0.859517, 1.07865, 0.433333, 1, 1.06402, 0.883735, 1.06402, 0.466667, 1, 1.03914, 0.926777, 1.03914, 0.5, 1, 1.00701, 0.986126, 1.00701, 0.533333, 1, 0.980969, 1.03971, 0.980969, 0.566667, 1, 0.965099, 1.07384, 0.965099, 0.6, 1, 0.957688, 1.09032, 0.957688, 0.633333, 1, 0.95698, 1.09194, 0.95698, 0.666667, 1, 0.961165, 1.08244, 0.961165, 0.7, 1, 0.974646, 1.05286, 0.974647, 0.733333, 1, 0.998732, 1.00341, 0.998732, 0.8, 1, 1.06677, 0.879034, 1.06677, 0.833333, 1, 1.08969, 0.842156, 1.08969, 0.866667, 1, 1.06496, 0.882067, 1.06496, 0.9, 1, 1.012, 0.980515, 1.012, 0.933333, 1, 0.951752, 1.1087, 0.951752, 0.966667, 1, 0.902251, 1.22906, 0.902251, 1, 1, 0.878639, 1.29532, 0.878639)
tracks/78/type = "position_3d"
tracks/78/imported = true
tracks/78/enabled = true
tracks/78/path = NodePath("rig/Skeleton3D:DEF-lip2.T.L")
tracks/78/interp = 1
tracks/78/loop_wrap = true
tracks/78/keys = PackedFloat32Array(0, 1, 0.242882, 0.226413, 0.378946, 0.0333333, 1, 0.249974, 0.21388, 0.390011, 0.0666667, 1, 0.265854, 0.190068, 0.414787, 0.1, 1, 0.287911, 0.162497, 0.449199, 0.133333, 1, 0.310588, 0.138685, 0.48458, 0.166667, 1, 0.325386, 0.126152, 0.507668, 0.2, 1, 0.313638, 0.135917, 0.48934, 0.233333, 1, 0.290739, 0.159462, 0.453613, 0.266667, 1, 0.263317, 0.195928, 0.410829, 0.3, 1, 0.236262, 0.244278, 0.368618, 0.333333, 1, 0.212728, 0.303299, 0.331902, 0.366667, 1, 0.195122, 0.371687, 0.304268, 0.4, 1, 0.186714, 0.48859, 0.2908, 0.433333, 1, 0.190622, 0.678827, 0.296349, 0.466667, 1, 0.206113, 0.924141, 0.319751, 0.5, 1, 0.228594, 1.16319, 0.353827, 0.533333, 1, 0.24631, 1.31698, 0.380356, 0.566667, 1, 0.257491, 1.41438, 0.396808, 0.6, 1, 0.263317, 1.46423, 0.405107, 0.633333, 1, 0.264958, 1.47321, 0.407153, 0.666667, 1, 0.263576, 1.44588, 0.404814, 0.7, 1, 0.256293, 1.35982, 0.393892, 0.733333, 1, 0.242996, 1.21402, 0.373998, 0.766667, 1, 0.225303, 1.00238, 0.34748, 0.8, 1, 0.207733, 0.742435, 0.321196, 0.833333, 1, 0.197702, 0.475315, 0.306517, 0.866667, 1, 0.210758, 0.331243, 0.327585, 0.9, 1, 0.237757, 0.243148, 0.370312, 0.933333, 1, 0.271105, 0.185467, 0.422766, 0.966667, 1, 0.301541, 0.147209, 0.470465, 1, 1, 0.318146, 0.131958, 0.496373)
tracks/79/type = "rotation_3d"
tracks/79/imported = true
tracks/79/enabled = true
tracks/79/path = NodePath("rig/Skeleton3D:DEF-lip2.T.L")
tracks/79/interp = 1
tracks/79/loop_wrap = true
tracks/79/keys = PackedFloat32Array(0, 1, -0.756911, -0.235661, -0.247054, 0.557237, 0.0333333, 1, -0.751338, -0.240255, -0.243305, 0.564421, 0.0666667, 1, -0.740857, -0.248695, -0.236469, 0.577377, 0.1, 1, -0.728862, -0.258029, -0.228887, 0.591432, 0.133333, 1, -0.718705, -0.26569, -0.222655, 0.602741, 0.166667, 1, -0.713596, -0.269476, -0.219596, 0.608228, 0.2, 1, -0.717559, -0.266543, -0.221965, 0.603983, 0.233333, 1, -0.727572, -0.259015, -0.228089, 0.592896, 0.266667, 1, -0.744689, -0.245665, -0.239062, 0.572658, 0.3, 1, -0.76933, -0.225106, -0.256102, 0.540251, 0.333333, 1, -0.800798, -0.195732, -0.280361, 0.49174, 0.366667, 1, -0.833548, -0.160105, -0.314113, 0.425321, 0.4, 1, -0.851589, -0.136017, -0.340284, 0.374837, 0.433333, 1, -0.854641, -0.132293, -0.349491, 0.360476, 0.466667, 1, -0.845214, -0.148806, -0.341759, 0.382976, 0.5, 1, -0.826881, -0.176334, -0.325716, 0.423182, 0.533333, 1, -0.812483, -0.196514, -0.317599, 0.447644, 0.566667, 1, -0.803062, -0.209794, -0.314414, 0.460676, 0.6, 1, -0.796884, -0.218774, -0.313508, 0.467789, 0.633333, 1, -0.792308, -0.225286, -0.312764, 0.47294, 0.666667, 1, -0.788142, -0.230463, -0.310733, 0.47871, 0.7, 1, -0.787168, -0.230674, -0.308066, 0.481925, 0.733333, 1, -0.791256, -0.224577, -0.307226, 0.478635, 0.766667, 1, -0.800198, -0.212527, -0.308899, 0.468079, 0.8, 1, -0.809734, -0.199013, -0.310321, 0.456536, 0.833333, 1, -0.81233, -0.192857, -0.305579, 0.457765, 0.866667, 1, -0.791592, -0.210801, -0.282019, 0.49941, 0.9, 1, -0.764165, -0.232672, -0.256832, 0.544016, 0.933333, 1, -0.738881, -0.251222, -0.236598, 0.578761, 0.966667, 1, -0.722256, -0.26303, -0.224806, 0.598852, 1, 1, -0.715932, -0.26775, -0.220988, 0.605736)
tracks/80/type = "scale_3d"
tracks/80/imported = true
tracks/80/enabled = true
tracks/80/path = NodePath("rig/Skeleton3D:DEF-lip2.T.L")
tracks/80/interp = 1
tracks/80/loop_wrap = true
tracks/80/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.98854, 1.02342, 0.98854, 0.0666667, 1, 0.963349, 1.07868, 0.963349, 0.1, 1, 0.929915, 1.15851, 0.929915, 0.133333, 1, 0.89713, 1.24295, 0.89713, 0.166667, 1, 0.877288, 1.29932, 0.877288, 0.2, 1, 0.892927, 1.2545, 0.892927, 0.233333, 1, 0.925811, 1.16901, 0.925811, 0.266667, 1, 0.966801, 1.07225, 0.966801, 0.3, 1, 1.00578, 0.98902, 1.00578, 0.333333, 1, 1.03165, 0.939576, 1.03165, 0.366667, 1, 1.02115, 0.959089, 1.02115, 0.4, 1, 0.99389, 1.01356, 0.99389, 0.433333, 1, 0.97211, 1.05835, 0.97211, 0.466667, 1, 0.96237, 1.07977, 0.96237, 0.5, 1, 0.955579, 1.09513, 0.955579, 0.533333, 1, 0.943699, 1.12301, 0.943699, 0.566667, 1, 0.93551, 1.14269, 0.93551, 0.6, 1, 0.932315, 1.15047, 0.932315, 0.633333, 1, 0.934467, 1.14522, 0.934467, 0.666667, 1, 0.941365, 1.12845, 0.941365, 0.7, 1, 0.955861, 1.09468, 0.955861, 0.733333, 1, 0.97778, 1.04671, 0.97778, 0.766667, 1, 1.00505, 0.990969, 1.00505, 0.8, 1, 1.0332, 0.937089, 1.0332, 0.833333, 1, 1.05534, 0.897865, 1.05534, 0.866667, 1, 1.04744, 0.911514, 1.04744, 0.9, 1, 1.00822, 0.986403, 1.00822, 0.933333, 1, 0.956237, 1.09768, 0.956237, 0.966667, 1, 0.909757, 1.20882, 0.909757, 1, 1, 0.88679, 1.27162, 0.88679)
tracks/81/type = "position_3d"
tracks/81/imported = true
tracks/81/enabled = true
tracks/81/path = NodePath("rig/Skeleton3D:DEF-lip1.T.R")
tracks/81/interp = 1
tracks/81/loop_wrap = true
tracks/81/keys = PackedFloat32Array(0, 1, -0.126628, 0.251461, 0.471815, 0.0333333, 1, -0.130326, 0.237542, 0.485592, 0.0666667, 1, -0.138605, 0.211095, 0.51644, 0.1, 1, -0.150104, 0.180474, 0.559286, 0.133333, 1, -0.161927, 0.154027, 0.603338, 0.166667, 1, -0.169642, 0.140108, 0.632084, 0.2, 1, -0.163518, 0.150954, 0.609263, 0.233333, 1, -0.151579, 0.177104, 0.564781, 0.266667, 1, -0.137282, 0.217958, 0.511512, 0.3, 1, -0.123177, 0.272845, 0.458956, 0.333333, 1, -0.110907, 0.341026, 0.413243, 0.366667, 1, -0.10167, 0.422177, 0.37864, 0.4, 1, -0.0971645, 0.549951, 0.361456, 0.433333, 1, -0.0990103, 0.744277, 0.367712, 0.466667, 1, -0.106819, 0.986223, 0.395939, 0.5, 1, -0.11819, 1.21799, 0.437179, 0.533333, 1, -0.127039, 1.3673, 0.46889, 0.566667, 1, -0.132521, 1.46213, 0.488194, 0.6, 1, -0.135283, 1.51026, 0.49759, 0.633333, 1, -0.13596, 1.51762, 0.499526, 0.666667, 1, -0.135177, 1.48832, 0.496398, 0.7, 1, -0.131534, 1.40074, 0.483325, 0.733333, 1, -0.124899, 1.25465, 0.459569, 0.766667, 1, -0.116055, 1.04404, 0.427843, 0.8, 1, -0.107289, 0.785305, 0.396461, 0.833333, 1, -0.102398, 0.517286, 0.379336, 0.866667, 1, -0.109448, 0.366134, 0.406396, 0.9, 1, -0.123734, 0.270287, 0.460307, 0.933333, 1, -0.141268, 0.20604, 0.526123, 0.966667, 1, -0.15721, 0.163495, 0.585764, 1, 1, -0.165868, 0.146557, 0.618021)
tracks/82/type = "rotation_3d"
tracks/82/imported = true
tracks/82/enabled = true
tracks/82/path = NodePath("rig/Skeleton3D:DEF-lip1.T.R")
tracks/82/interp = 1
tracks/82/loop_wrap = true
tracks/82/keys = PackedFloat32Array(0, 1, -0.707902, 0.316721, 0.286142, 0.562749, 0.0333333, 1, -0.704214, 0.319894, 0.283682, 0.566811, 0.0666667, 1, -0.697429, 0.325642, 0.279219, 0.574097, 0.1, 1, -0.689832, 0.331934, 0.274292, 0.581993, 0.133333, 1, -0.683528, 0.337048, 0.270258, 0.588345, 0.166667, 1, -0.680402, 0.339551, 0.268281, 0.591425, 0.2, 1, -0.682824, 0.337613, 0.269812, 0.589042, 0.233333, 1, -0.689026, 0.332593, 0.273774, 0.582814, 0.266667, 1, -0.699779, 0.323581, 0.280891, 0.571581, 0.3, 1, -0.715797, 0.30938, 0.292077, 0.553723, 0.333333, 1, -0.737532, 0.288435, 0.308352, 0.527039, 0.366667, 1, -0.764652, 0.259231, 0.332533, 0.487368, 0.4, 1, -0.782641, 0.237009, 0.352363, 0.455127, 0.433333, 1, -0.785465, 0.232971, 0.35893, 0.447146, 0.466667, 1, -0.77402, 0.247739, 0.35169, 0.464578, 0.5, 1, -0.754964, 0.271056, 0.33791, 0.492316, 0.533333, 1, -0.742521, 0.285927, 0.330771, 0.507443, 0.566667, 1, -0.734971, 0.295087, 0.327528, 0.515234, 0.6, 1, -0.730414, 0.300889, 0.326186, 0.519195, 0.633333, 1, -0.727324, 0.304948, 0.325205, 0.521773, 0.666667, 1, -0.724726, 0.308206, 0.323582, 0.524476, 0.7, 1, -0.724615, 0.308346, 0.322034, 0.525499, 0.733333, 1, -0.728397, 0.30423, 0.322167, 0.522581, 0.766667, 1, -0.736252, 0.295783, 0.324403, 0.514983, 0.8, 1, -0.744961, 0.286214, 0.32662, 0.506393, 0.833333, 1, -0.748303, 0.282178, 0.324328, 0.505203, 0.866667, 1, -0.732354, 0.29694, 0.308542, 0.529421, 0.9, 1, -0.712935, 0.313584, 0.292108, 0.555033, 0.933333, 1, -0.696166, 0.32709, 0.279136, 0.574847, 0.966667, 1, -0.685715, 0.335282, 0.27165, 0.586163, 1, 1, -0.681827, 0.338412, 0.269181, 0.590026)
tracks/83/type = "scale_3d"
tracks/83/imported = true
tracks/83/enabled = true
tracks/83/path = NodePath("rig/Skeleton3D:DEF-lip1.T.R")
tracks/83/interp = 1
tracks/83/loop_wrap = true
tracks/83/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.986839, 1.02699, 0.986839, 0.0666667, 1, 0.959098, 1.08851, 0.959098, 0.1, 1, 0.923403, 1.17515, 0.923403, 0.133333, 1, 0.889241, 1.26513, 0.889241, 0.166667, 1, 0.868964, 1.32433, 0.868964, 0.2, 1, 0.884925, 1.27731, 0.884925, 0.233333, 1, 0.919111, 1.18636, 0.919111, 0.266667, 1, 0.963857, 1.07951, 0.963857, 0.3, 1, 1.01154, 0.978586, 1.01154, 0.333333, 1, 1.05458, 0.899166, 1.05458, 0.366667, 1, 1.07793, 0.86095, 1.07793, 0.4, 1, 1.07865, 0.859518, 1.07865, 0.433333, 1, 1.06402, 0.883736, 1.06402, 0.466667, 1, 1.03914, 0.926778, 1.03914, 0.5, 1, 1.00701, 0.986126, 1.00701, 0.533333, 1, 0.980969, 1.03971, 0.980969, 0.566667, 1, 0.965099, 1.07384, 0.965099, 0.6, 1, 0.957688, 1.09032, 0.957688, 0.633333, 1, 0.95698, 1.09195, 0.956979, 0.666667, 1, 0.961165, 1.08244, 0.961165, 0.7, 1, 0.974646, 1.05286, 0.974647, 0.733333, 1, 0.998732, 1.00341, 0.998732, 0.8, 1, 1.06677, 0.879035, 1.06677, 0.833333, 1, 1.08969, 0.842157, 1.08969, 0.866667, 1, 1.06496, 0.882067, 1.06496, 0.9, 1, 1.012, 0.980516, 1.012, 0.933333, 1, 0.951752, 1.1087, 0.951752, 0.966667, 1, 0.902251, 1.22906, 0.902251, 1, 1, 0.87864, 1.29532, 0.87864)
tracks/84/type = "position_3d"
tracks/84/imported = true
tracks/84/enabled = true
tracks/84/path = NodePath("rig/Skeleton3D:DEF-lip2.T.R")
tracks/84/interp = 1
tracks/84/loop_wrap = true
tracks/84/keys = PackedFloat32Array(0, 1, -0.242882, 0.226413, 0.378946, 0.0333333, 1, -0.249974, 0.21388, 0.390011, 0.0666667, 1, -0.265854, 0.190068, 0.414787, 0.1, 1, -0.287911, 0.162497, 0.449199, 0.133333, 1, -0.310588, 0.138685, 0.48458, 0.166667, 1, -0.325386, 0.126152, 0.507668, 0.2, 1, -0.313638, 0.135917, 0.48934, 0.233333, 1, -0.29074, 0.159462, 0.453613, 0.266667, 1, -0.263317, 0.195928, 0.410829, 0.3, 1, -0.236262, 0.244278, 0.368618, 0.333333, 1, -0.212728, 0.303299, 0.331902, 0.366667, 1, -0.195122, 0.371687, 0.304268, 0.4, 1, -0.186714, 0.48859, 0.2908, 0.433333, 1, -0.190623, 0.678827, 0.296349, 0.466667, 1, -0.206113, 0.924141, 0.319751, 0.5, 1, -0.228595, 1.16319, 0.353827, 0.533333, 1, -0.24631, 1.31698, 0.380356, 0.566667, 1, -0.257491, 1.41438, 0.396808, 0.6, 1, -0.263317, 1.46423, 0.405107, 0.633333, 1, -0.264958, 1.47321, 0.407153, 0.666667, 1, -0.263576, 1.44588, 0.404814, 0.7, 1, -0.256293, 1.35982, 0.393892, 0.733333, 1, -0.242996, 1.21402, 0.373998, 0.766667, 1, -0.225303, 1.00238, 0.34748, 0.8, 1, -0.207733, 0.742435, 0.321197, 0.833333, 1, -0.197703, 0.475315, 0.306517, 0.866667, 1, -0.210758, 0.331243, 0.327585, 0.9, 1, -0.237758, 0.243148, 0.370312, 0.933333, 1, -0.271105, 0.185467, 0.422766, 0.966667, 1, -0.301541, 0.147209, 0.470465, 1, 1, -0.318147, 0.131958, 0.496373)
tracks/85/type = "rotation_3d"
tracks/85/imported = true
tracks/85/enabled = true
tracks/85/path = NodePath("rig/Skeleton3D:DEF-lip2.T.R")
tracks/85/interp = 1
tracks/85/loop_wrap = true
tracks/85/keys = PackedFloat32Array(0, 1, -0.756911, 0.235661, 0.247054, 0.557237, 0.0333333, 1, -0.751338, 0.240255, 0.243305, 0.564422, 0.0666667, 1, -0.740857, 0.248695, 0.236469, 0.577377, 0.1, 1, -0.728862, 0.258029, 0.228887, 0.591432, 0.133333, 1, -0.718705, 0.26569, 0.222655, 0.602741, 0.166667, 1, -0.713596, 0.269476, 0.219596, 0.608228, 0.2, 1, -0.717559, 0.266543, 0.221965, 0.603983, 0.233333, 1, -0.727572, 0.259016, 0.228089, 0.592896, 0.266667, 1, -0.744689, 0.245665, 0.239062, 0.572658, 0.3, 1, -0.76933, 0.225106, 0.256102, 0.540251, 0.333333, 1, -0.800798, 0.195733, 0.280362, 0.49174, 0.366667, 1, -0.833548, 0.160105, 0.314113, 0.42532, 0.4, 1, -0.851589, 0.136017, 0.340284, 0.374837, 0.433333, 1, -0.854641, 0.132293, 0.349491, 0.360476, 0.466667, 1, -0.845214, 0.148806, 0.341759, 0.382977, 0.5, 1, -0.826881, 0.176334, 0.325716, 0.423183, 0.533333, 1, -0.812483, 0.196514, 0.317599, 0.447643, 0.566667, 1, -0.803062, 0.209793, 0.314413, 0.460676, 0.6, 1, -0.796884, 0.218774, 0.313508, 0.46779, 0.633333, 1, -0.792308, 0.225286, 0.312764, 0.47294, 0.666667, 1, -0.788142, 0.230463, 0.310734, 0.47871, 0.7, 1, -0.787168, 0.230674, 0.308066, 0.481925, 0.733333, 1, -0.791256, 0.224577, 0.307226, 0.478635, 0.766667, 1, -0.800197, 0.212527, 0.308899, 0.468079, 0.8, 1, -0.809734, 0.199013, 0.310321, 0.456537, 0.833333, 1, -0.812329, 0.192857, 0.305578, 0.457766, 0.866667, 1, -0.791592, 0.210801, 0.282019, 0.499411, 0.9, 1, -0.764165, 0.232672, 0.256832, 0.544016, 0.933333, 1, -0.738881, 0.251222, 0.236598, 0.578761, 0.966667, 1, -0.722256, 0.263031, 0.224806, 0.598852, 1, 1, -0.715932, 0.26775, 0.220988, 0.605736)
tracks/86/type = "scale_3d"
tracks/86/imported = true
tracks/86/enabled = true
tracks/86/path = NodePath("rig/Skeleton3D:DEF-lip2.T.R")
tracks/86/interp = 1
tracks/86/loop_wrap = true
tracks/86/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 0.98854, 1.02342, 0.98854, 0.0666667, 1, 0.96335, 1.07868, 0.96335, 0.1, 1, 0.929916, 1.15851, 0.929916, 0.133333, 1, 0.89713, 1.24295, 0.89713, 0.166667, 1, 0.877289, 1.29932, 0.877288, 0.2, 1, 0.892927, 1.2545, 0.892927, 0.233333, 1, 0.925812, 1.16901, 0.925812, 0.266667, 1, 0.966801, 1.07224, 0.966801, 0.3, 1, 1.00578, 0.989019, 1.00578, 0.333333, 1, 1.03165, 0.939575, 1.03165, 0.366667, 1, 1.02115, 0.959088, 1.02115, 0.4, 1, 0.993891, 1.01356, 0.993891, 0.433333, 1, 0.972111, 1.05835, 0.972111, 0.466667, 1, 0.962371, 1.07977, 0.962371, 0.5, 1, 0.95558, 1.09513, 0.95558, 0.533333, 1, 0.9437, 1.12301, 0.9437, 0.566667, 1, 0.93551, 1.14269, 0.93551, 0.6, 1, 0.932316, 1.15047, 0.932316, 0.633333, 1, 0.934467, 1.14522, 0.934468, 0.666667, 1, 0.941366, 1.12845, 0.941366, 0.7, 1, 0.955861, 1.09468, 0.955861, 0.733333, 1, 0.97778, 1.0467, 0.97778, 0.766667, 1, 1.00505, 0.990969, 1.00505, 0.8, 1, 1.0332, 0.937088, 1.0332, 0.833333, 1, 1.05535, 0.897865, 1.05535, 0.866667, 1, 1.04744, 0.911514, 1.04744, 0.9, 1, 1.00822, 0.986403, 1.00822, 0.933333, 1, 0.956237, 1.09768, 0.956237, 0.966667, 1, 0.909758, 1.20882, 0.909758, 1, 1, 0.886791, 1.27162, 0.886791)
tracks/87/type = "position_3d"
tracks/87/imported = true
tracks/87/enabled = true
tracks/87/path = NodePath("rig/Skeleton3D:DEF-bone.02")
tracks/87/interp = 1
tracks/87/loop_wrap = true
tracks/87/keys = PackedFloat32Array(0, 1, 0, 0.362402, 0, 0.0333333, 1, 0, 0.342342, 0, 0.0666667, 1, 0, 0.304228, 0, 0.1, 1, 0, 0.260097, 0, 0.133333, 1, 0, 0.221983, 0, 0.166667, 1, 0, 0.201923, 0, 0.2, 1, 0, 0.217553, 0, 0.233333, 1, 0, 0.25524, 0, 0.266667, 1, 0, 0.3125, 0, 0.3, 1, 0, 0.386173, 0, 0.333333, 1, 0, 0.472423, 0, 0.366667, 1, 0, 0.566139, 0, 0.4, 1, -7.33699e-16, 0.691823, 0, 0.433333, 1, -2.68082e-15, 0.867388, 0, 0.466667, 1, -5.62268e-15, 1.07959, 0, 0.5, 1, -8.64215e-15, 1.28404, 0, 0.533333, 1, -1.05397e-14, 1.4193, 0, 0.566667, 1, -1.17142e-14, 1.50699, 0, 0.6, 1, -1.23123e-14, 1.55373, 0, 0.633333, 1, -1.24437e-14, 1.56447, 0, 0.666667, 1, -1.2181e-14, 1.54253, 0, 0.7, 1, -1.12475e-14, 1.46779, 0, 0.733333, 1, -9.59095e-15, 1.34084, 0, 0.766667, 1, -7.13242e-15, 1.15699, 0, 0.8, 1, -4.13881e-15, 0.928444, 0, 0.833333, 1, -1.22283e-15, 0.684291, 0, 0.866667, 1, -2.44566e-16, 0.515602, 0, 0.9, 1, 0, 0.389649, 0, 0.933333, 1, 0, 0.296969, 0, 0.966667, 1, 0, 0.235627, 0, 1, 1, 0, 0.211216, 0)
tracks/88/type = "rotation_3d"
tracks/88/imported = true
tracks/88/enabled = true
tracks/88/path = NodePath("rig/Skeleton3D:DEF-bone.02")
tracks/88/interp = 1
tracks/88/loop_wrap = true
tracks/88/keys = PackedFloat32Array(0, 1, -2.10557e-13, -1, 2.98023e-08, 3.65178e-06)
tracks/89/type = "scale_3d"
tracks/89/imported = true
tracks/89/enabled = true
tracks/89/path = NodePath("rig/Skeleton3D:DEF-bone.02")
tracks/89/interp = 1
tracks/89/loop_wrap = true
tracks/89/keys = PackedFloat32Array(0, 1, 1, 1, 1, 0.0333333, 1, 1.0292, 0.944647, 1.0292, 0.0666667, 1, 1.09458, 0.839477, 1.09458, 0.1, 1, 1.18539, 0.717701, 1.18539, 0.133333, 1, 1.27876, 0.612531, 1.27876, 0.166667, 1, 1.33969, 0.557178, 1.33969, 0.2, 1, 1.29132, 0.600307, 1.29132, 0.233333, 1, 1.19704, 0.7043, 1.19704, 0.266667, 1, 1.08414, 0.862302, 1.08414, 0.3, 1, 0.972743, 1.06559, 0.972743, 0.333333, 1, 0.87585, 1.30359, 0.87585, 0.366667, 1, 0.802388, 1.56218, 0.802388, 0.4, 1, 0.765708, 1.70752, 0.765708, 0.433333, 1, 0.778563, 1.65728, 0.778563, 0.466667, 1, 0.837828, 1.43499, 0.837828, 0.5, 1, 0.924505, 1.16999, 0.924505, 0.533333, 1, 0.990908, 1.02212, 0.990908, 0.566667, 1, 1.0311, 0.941579, 1.0311, 0.6, 1, 1.05045, 0.906307, 1.05045, 0.633333, 1, 1.05418, 0.899872, 1.05418, 0.666667, 1, 1.04743, 0.911484, 1.04743, 0.7, 1, 1.02005, 0.961579, 1.02005, 0.733333, 1, 0.970327, 1.06617, 0.970327, 0.766667, 1, 0.903878, 1.23398, 0.903878, 0.8, 1, 0.838185, 1.42539, 0.838185, 0.833333, 1, 0.802594, 1.55242, 0.802594, 0.866667, 1, 0.860454, 1.35558, 0.860454, 0.933333, 1, 1.11495, 0.819445, 1.11495, 0.966667, 1, 1.24151, 0.650182, 1.24151, 1, 1, 1.30988, 0.582823, 1.30988)

[sub_resource type="Animation" id="Animation_k6dxj"]
resource_name = "Fall"
length = 0.583333
tracks/0/type = "position_3d"
tracks/0/imported = true
tracks/0/enabled = true
tracks/0/path = NodePath("rig/Skeleton3D:DEF-lid1.B.L")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = PackedFloat32Array(0, 1, 0.0284704, 0.160885, 0.033714, 0.0333333, 1, 0.0363235, 0.208847, 0.0430134, 0.0666667, 1, 0.0523729, 0.31204, 0.0620187, 0.1, 1, 0.0732438, 0.452811, 0.0867336, 0.133333, 1, 0.0968461, 0.608844, 0.114683, 0.166667, 1, 0.122374, 0.753157, 0.144912, 0.2, 1, 0.151253, 0.821265, 0.17911, 0.233333, 1, 0.181837, 0.818341, 0.215327, 0.266667, 1, 0.213721, 0.753616, 0.253083, 0.3, 1, 0.24658, 0.643773, 0.291994, 0.333333, 1, 0.28017, 0.512949, 0.33177, 0.366667, 1, 0.314615, 0.410904, 0.372559, 0.4, 1, 0.337013, 0.355716, 0.399083, 0.433333, 1, 0.343003, 0.342366, 0.406176, 0.466667, 1, 0.335038, 0.359096, 0.396744, 0.5, 1, 0.322385, 0.387407, 0.38176, 0.533333, 1, 0.311355, 0.415718, 0.368699, 0.566667, 1, 0.305173, 0.432447, 0.361379, 0.583333, 1, 0.30289, 0.438881, 0.358675)
tracks/1/type = "rotation_3d"
tracks/1/imported = true
tracks/1/enabled = true
tracks/1/path = NodePath("rig/Skeleton3D:DEF-lid1.B.L")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = PackedFloat32Array(0, 1, 0.689635, -0.200869, -0.694386, 0.043406, 0.0333333, 1, 0.691478, -0.188279, -0.696242, 0.0406863, 0.0666667, 1, 0.694557, -0.165042, -0.699342, 0.0356645, 0.1, 1, 0.697587, -0.138289, -0.702393, 0.029883, 0.133333, 1, 0.6998, -0.114777, -0.704621, 0.0248023, 0.166667, 1, 0.700915, -0.100842, -0.705744, 0.0217914, 0.2, 1, 0.700988, -0.0998654, -0.705817, 0.02158, 0.233333, 1, 0.700502, -0.106217, -0.705328, 0.0229524, 0.266667, 1, 0.699229, -0.121294, -0.704046, 0.0262106, 0.3, 1, 0.696509, -0.148377, -0.701307, 0.0320631, 0.333333, 1, 0.690872, -0.192515, -0.695632, 0.0416014, 0.366667, 1, 0.679483, -0.259017, -0.684164, 0.0559711, 0.4, 1, 0.670034, -0.302694, -0.67465, 0.0654096, 0.433333, 1, 0.667213, -0.314456, -0.671809, 0.0679515, 0.466667, 1, 0.670936, -0.298823, -0.675559, 0.0645732, 0.5, 1, 0.676466, -0.273791, -0.681126, 0.0591643, 0.533333, 1, 0.680841, -0.252061, -0.685532, 0.0544686, 0.566667, 1, 0.683116, -0.23993, -0.687822, 0.0518469, 0.583333, 1, 0.683922, -0.235471, -0.688633, 0.0508833)
tracks/2/type = "scale_3d"
tracks/2/imported = true
tracks/2/enabled = true
tracks/2/path = NodePath("rig/Skeleton3D:DEF-lid1.B.L")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = PackedFloat32Array(0, 1, 0.0956618, 0.109275, 0.0956618, 0.0333333, 1, 0.122761, 0.148993, 0.122761, 0.0666667, 1, 0.178779, 0.250255, 0.178779, 0.1, 1, 0.252161, 0.419273, 0.252161, 0.133333, 1, 0.335419, 0.649282, 0.335419, 0.166667, 1, 0.425132, 0.920536, 0.425132, 0.2, 1, 0.525573, 1.14934, 0.525573, 0.233333, 1, 0.630856, 1.29582, 0.630856, 0.266667, 1, 0.738477, 1.34205, 0.738477, 0.3, 1, 0.844772, 1.28168, 0.844772, 0.333333, 1, 0.944911, 1.12, 0.944911, 0.366667, 1, 1.02179, 0.962216, 1.02179, 0.4, 1, 1.06256, 0.886449, 1.06256, 0.433333, 1, 1.07244, 0.869542, 1.07244, 0.466667, 1, 1.06046, 0.889499, 1.06045, 0.5, 1, 1.03948, 0.925485, 1.03948, 0.533333, 1, 1.01777, 0.965699, 1.01777, 0.566667, 1, 1.00494, 0.990301, 1.00494, 0.583333, 1, 1, 1, 1)
tracks/3/type = "position_3d"
tracks/3/imported = true
tracks/3/enabled = true
tracks/3/path = NodePath("rig/Skeleton3D:DEF-lid1.B.R")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = PackedFloat32Array(0, 1, -0.0284704, 0.160885, 0.033714, 0.0333333, 1, -0.0363235, 0.208847, 0.0430134, 0.0666667, 1, -0.0523729, 0.31204, 0.0620187, 0.1, 1, -0.0732438, 0.452811, 0.0867336, 0.133333, 1, -0.0968461, 0.608844, 0.114683, 0.166667, 1, -0.122374, 0.753157, 0.144912, 0.2, 1, -0.151253, 0.821265, 0.17911, 0.233333, 1, -0.181837, 0.818341, 0.215327, 0.266667, 1, -0.213721, 0.753616, 0.253083, 0.3, 1, -0.24658, 0.643773, 0.291994, 0.333333, 1, -0.28017, 0.512949, 0.33177, 0.366667, 1, -0.314615, 0.410904, 0.372559, 0.4, 1, -0.337013, 0.355716, 0.399083, 0.433333, 1, -0.343003, 0.342366, 0.406176, 0.466667, 1, -0.335038, 0.359096, 0.396744, 0.5, 1, -0.322385, 0.387407, 0.38176, 0.533333, 1, -0.311355, 0.415718, 0.3687, 0.566667, 1, -0.305173, 0.432447, 0.361379, 0.583333, 1, -0.30289, 0.438881, 0.358675)
tracks/4/type = "rotation_3d"
tracks/4/imported = true
tracks/4/enabled = true
tracks/4/path = NodePath("rig/Skeleton3D:DEF-lid1.B.R")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = PackedFloat32Array(0, 1, 0.689635, 0.200867, 0.694386, 0.0434052, 0.0333333, 1, 0.691479, 0.188279, 0.696242, 0.0406852, 0.0666667, 1, 0.694557, 0.165041, 0.699342, 0.0356638, 0.1, 1, 0.697587, 0.138288, 0.702393, 0.0298828, 0.133333, 1, 0.6998, 0.114776, 0.704621, 0.0248022, 0.166667, 1, 0.700915, 0.100842, 0.705744, 0.0217914, 0.2, 1, 0.700988, 0.0998647, 0.705817, 0.0215799, 0.233333, 1, 0.700502, 0.106216, 0.705328, 0.0229526, 0.266667, 1, 0.699229, 0.121294, 0.704046, 0.0262109, 0.3, 1, 0.696509, 0.148377, 0.701307, 0.0320633, 0.333333, 1, 0.690872, 0.192515, 0.695631, 0.0416006, 0.366667, 1, 0.679483, 0.259016, 0.684164, 0.0559714, 0.4, 1, 0.670034, 0.302693, 0.67465, 0.0654093, 0.433333, 1, 0.667213, 0.314455, 0.671809, 0.067951, 0.466667, 1, 0.670937, 0.298823, 0.675559, 0.0645732, 0.5, 1, 0.676466, 0.273791, 0.681126, 0.0591641, 0.533333, 1, 0.680841, 0.252061, 0.685532, 0.0544684, 0.566667, 1, 0.683116, 0.23993, 0.687822, 0.0518473, 0.583333, 1, 0.683921, 0.235472, 0.688633, 0.0508841)
tracks/5/type = "scale_3d"
tracks/5/imported = true
tracks/5/enabled = true
tracks/5/path = NodePath("rig/Skeleton3D:DEF-lid1.B.R")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = PackedFloat32Array(0, 1, 0.0956618, 0.109276, 0.0956618, 0.0333333, 1, 0.122761, 0.148993, 0.122761, 0.0666667, 1, 0.178779, 0.250255, 0.178779, 0.1, 1, 0.252161, 0.419274, 0.252161, 0.133333, 1, 0.335419, 0.649282, 0.335419, 0.166667, 1, 0.425132, 0.920536, 0.425132, 0.2, 1, 0.525573, 1.14934, 0.525573, 0.233333, 1, 0.630856, 1.29582, 0.630856, 0.266667, 1, 0.738477, 1.34205, 0.738477, 0.3, 1, 0.844771, 1.28169, 0.844772, 0.333333, 1, 0.944911, 1.12, 0.944911, 0.366667, 1, 1.02179, 0.962218, 1.02179, 0.4, 1, 1.06256, 0.88645, 1.06256, 0.433333, 1, 1.07244, 0.869542, 1.07244, 0.466667, 1, 1.06046, 0.889499, 1.06046, 0.5, 1, 1.03948, 0.925486, 1.03948, 0.533333, 1, 1.01777, 0.9657, 1.01777, 0.566667, 1, 1.00494, 0.990301, 1.00494, 0.583333, 1, 1, 1, 1)
tracks/6/type = "position_3d"
tracks/6/imported = true
tracks/6/enabled = true
tracks/6/path = NodePath("rig/Skeleton3D:DEF-lid1.T.L")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = PackedFloat32Array(0, 1, 0.0284704, 0.160885, 0.033714, 0.0333333, 1, 0.0363235, 0.208847, 0.0430134, 0.0666667, 1, 0.0523729, 0.31204, 0.0620187, 0.1, 1, 0.0732438, 0.452811, 0.0867336, 0.133333, 1, 0.0968461, 0.608844, 0.114683, 0.166667, 1, 0.122374, 0.753157, 0.144912, 0.2, 1, 0.151253, 0.821265, 0.17911, 0.233333, 1, 0.181837, 0.818341, 0.215327, 0.266667, 1, 0.213721, 0.753616, 0.253083, 0.3, 1, 0.24658, 0.643773, 0.291994, 0.333333, 1, 0.28017, 0.512949, 0.33177, 0.366667, 1, 0.314615, 0.410904, 0.372559, 0.4, 1, 0.337013, 0.355716, 0.399083, 0.433333, 1, 0.343003, 0.342366, 0.406176, 0.466667, 1, 0.335038, 0.359096, 0.396744, 0.5, 1, 0.322385, 0.387407, 0.38176, 0.533333, 1, 0.311355, 0.415718, 0.368699, 0.566667, 1, 0.305173, 0.432447, 0.361379, 0.583333, 1, 0.30289, 0.438881, 0.358675)
tracks/7/type = "rotation_3d"
tracks/7/imported = true
tracks/7/enabled = true
tracks/7/path = NodePath("rig/Skeleton3D:DEF-lid1.T.L")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = PackedFloat32Array(0, 1, 0.234648, -0.829403, 0.0662164, 0.50264, 0.0333333, 1, 0.220666, -0.832428, 0.0622706, 0.504473, 0.0666667, 1, 0.194465, -0.837571, 0.0548771, 0.50759, 0.1, 1, 0.163893, -0.842719, 0.0462502, 0.51071, 0.133333, 1, 0.136677, -0.846543, 0.0385698, 0.513027, 0.166667, 1, 0.120364, -0.848496, 0.0339659, 0.514211, 0.2, 1, 0.119215, -0.848625, 0.033642, 0.514288, 0.233333, 1, 0.126666, -0.847772, 0.0357446, 0.513772, 0.266667, 1, 0.144239, -0.845552, 0.0407038, 0.512426, 0.3, 1, 0.175403, -0.840888, 0.0494981, 0.5096, 0.333333, 1, 0.225395, -0.831427, 0.0636055, 0.503866, 0.366667, 1, 0.296876, -0.813506, 0.0837775, 0.493005, 0.4, 1, 0.342131, -0.799348, 0.0965482, 0.484425, 0.433333, 1, 0.354132, -0.79521, 0.0999347, 0.481918, 0.466667, 1, 0.338362, -0.800614, 0.0954842, 0.485193, 0.5, 1, 0.31276, -0.808792, 0.0882597, 0.490149, 0.533333, 1, 0.289947, -0.815477, 0.0818218, 0.4942, 0.566667, 1, 0.277085, -0.819, 0.078192, 0.496335, 0.583333, 1, 0.272322, -0.820261, 0.076848, 0.497099)
tracks/8/type = "scale_3d"
tracks/8/imported = true
tracks/8/enabled = true
tracks/8/path = NodePath("rig/Skeleton3D:DEF-lid1.T.L")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = PackedFloat32Array(0, 1, 0.0962761, 0.107885, 0.0962761, 0.0333333, 1, 0.12383, 0.146424, 0.12383, 0.0666667, 1, 0.181076, 0.24386, 0.181076, 0.1, 1, 0.25633, 0.405547, 0.25633, 0.133333, 1, 0.341865, 0.624958, 0.341865, 0.166667, 1, 0.433907, 0.883677, 0.433907, 0.2, 1, 0.536476, 1.1031, 0.536476, 0.233333, 1, 0.64348, 1.24541, 0.64348, 0.266667, 1, 0.751888, 1.2942, 0.751888, 0.3, 1, 0.856983, 1.24444, 0.856983, 0.333333, 1, 0.952355, 1.10256, 0.952355, 0.366667, 1, 1.01658, 0.970736, 1.01658, 0.4, 1, 1.04784, 0.911156, 1.04784, 0.433333, 1, 1.05499, 0.898505, 1.05499, 0.466667, 1, 1.0467, 0.912899, 1.0467, 0.5, 1, 1.03148, 0.939886, 1.03148, 0.533333, 1, 1.01441, 0.971997, 1.01441, 0.566667, 1, 1.00406, 0.99201, 1.00406, 0.583333, 1, 1, 1, 1)
tracks/9/type = "position_3d"
tracks/9/imported = true
tracks/9/enabled = true
tracks/9/path = NodePath("rig/Skeleton3D:DEF-lid1.T.R")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = PackedFloat32Array(0, 1, -0.0284704, 0.160885, 0.033714, 0.0333333, 1, -0.0363235, 0.208847, 0.0430134, 0.0666667, 1, -0.0523729, 0.31204, 0.0620187, 0.1, 1, -0.0732438, 0.452811, 0.0867336, 0.133333, 1, -0.0968461, 0.608844, 0.114683, 0.166667, 1, -0.122374, 0.753157, 0.144912, 0.2, 1, -0.151253, 0.821265, 0.17911, 0.233333, 1, -0.181837, 0.818341, 0.215327, 0.266667, 1, -0.213721, 0.753616, 0.253083, 0.3, 1, -0.24658, 0.643773, 0.291994, 0.333333, 1, -0.28017, 0.512949, 0.33177, 0.366667, 1, -0.314615, 0.410904, 0.372559, 0.4, 1, -0.337013, 0.355716, 0.399083, 0.433333, 1, -0.343003, 0.342366, 0.406176, 0.466667, 1, -0.335038, 0.359096, 0.396744, 0.5, 1, -0.322385, 0.387407, 0.38176, 0.533333, 1, -0.311355, 0.415718, 0.3687, 0.566667, 1, -0.305173, 0.432447, 0.361379, 0.583333, 1, -0.30289, 0.438881, 0.358675)
tracks/10/type = "rotation_3d"
tracks/10/imported = true
tracks/10/enabled = true
tracks/10/path = NodePath("rig/Skeleton3D:DEF-lid1.T.R")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = PackedFloat32Array(0, 1, 0.234648, 0.829403, -0.0662172, 0.502639, 0.0333333, 1, 0.220666, 0.832428, -0.0622704, 0.504473, 0.0666667, 1, 0.194465, 0.837571, -0.054877, 0.50759, 0.1, 1, 0.163893, 0.842719, -0.0462501, 0.51071, 0.133333, 1, 0.136677, 0.846543, -0.0385696, 0.513027, 0.166667, 1, 0.120364, 0.848496, -0.0339666, 0.514211, 0.2, 1, 0.119215, 0.848625, -0.0336421, 0.514288, 0.233333, 1, 0.126666, 0.847772, -0.0357445, 0.513772, 0.266667, 1, 0.144239, 0.845552, -0.0407036, 0.512426, 0.3, 1, 0.175402, 0.840888, -0.0494979, 0.5096, 0.333333, 1, 0.225395, 0.831427, -0.0636057, 0.503866, 0.366667, 1, 0.296877, 0.813506, -0.0837778, 0.493005, 0.4, 1, 0.342131, 0.799348, -0.0965482, 0.484425, 0.433333, 1, 0.354132, 0.79521, -0.0999347, 0.481918, 0.466667, 1, 0.338361, 0.800614, -0.0954845, 0.485193, 0.5, 1, 0.31276, 0.808792, -0.0882593, 0.490149, 0.533333, 1, 0.289947, 0.815477, -0.081822, 0.4942, 0.566667, 1, 0.277085, 0.819, -0.0781922, 0.496335, 0.583333, 1, 0.272322, 0.820261, -0.076848, 0.497099)
tracks/11/type = "scale_3d"
tracks/11/imported = true
tracks/11/enabled = true
tracks/11/path = NodePath("rig/Skeleton3D:DEF-lid1.T.R")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = PackedFloat32Array(0, 1, 0.0962762, 0.107885, 0.0962762, 0.0333333, 1, 0.12383, 0.146425, 0.12383, 0.0666667, 1, 0.181076, 0.243859, 0.181076, 0.1, 1, 0.25633, 0.405547, 0.25633, 0.133333, 1, 0.341865, 0.624958, 0.341865, 0.166667, 1, 0.433907, 0.883677, 0.433907, 0.2, 1, 0.536476, 1.10309, 0.536476, 0.233333, 1, 0.64348, 1.24541, 0.64348, 0.266667, 1, 0.751888, 1.2942, 0.751888, 0.3, 1, 0.856983, 1.24444, 0.856983, 0.333333, 1, 0.952355, 1.10256, 0.952355, 0.366667, 1, 1.01658, 0.970735, 1.01658, 0.4, 1, 1.04784, 0.911156, 1.04784, 0.433333, 1, 1.05499, 0.898505, 1.05499, 0.466667, 1, 1.0467, 0.912898, 1.0467, 0.5, 1, 1.03148, 0.939885, 1.03148, 0.533333, 1, 1.01441, 0.971997, 1.01441, 0.566667, 1, 1.00406, 0.99201, 1.00406, 0.583333, 1, 1, 1, 1)
tracks/12/type = "position_3d"
tracks/12/imported = true
tracks/12/enabled = true
tracks/12/path = NodePath("rig/Skeleton3D:DEF-lip.B.L")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = PackedFloat32Array(0, 1, 2.84217e-14, 0.121528, 0.0399914, 0.0333333, 1, -1.7053e-14, 0.154549, 0.0510223, 0.0666667, 1, -1.13687e-14, 0.218919, 0.0735664, 0.1, 1, 2.27374e-14, 0.294084, 0.102883, 0.133333, 1, 4.54747e-14, 0.36032, 0.136036, 0.166667, 1, 0, 0.398734, 0.171895, 0.2, 1, 0, 0.378553, 0.212459, 0.233333, 1, 0, 0.320711, 0.25542, 0.266667, 1, -9.09496e-14, 0.242066, 0.300206, 0.3, 1, -2.27374e-13, 0.162821, 0.346362, 0.333333, 1, -2.27374e-13, 0.106533, 0.393544, 0.366667, 1, -2.27374e-13, 0.0853395, 0.441928, 0.4, 1, -2.27374e-13, 0.0738778, 0.47339, 0.433333, 1, -4.54747e-14, 0.0711052, 0.481805, 0.466667, 1, 1.81899e-13, 0.0745796, 0.470617, 0.5, 1, 0, 0.0804595, 0.452843, 0.533333, 1, 1.81899e-13, 0.0863393, 0.43735, 0.566667, 1, 2.27374e-13, 0.0898138, 0.428666, 0.583333, 1, 2.27374e-13, 0.0911501, 0.425459)
tracks/13/type = "rotation_3d"
tracks/13/imported = true
tracks/13/enabled = true
tracks/13/path = NodePath("rig/Skeleton3D:DEF-lip.B.L")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = PackedFloat32Array(0, 1, -0.416787, -0.398257, -0.561211, 0.593904, 0.0333333, 1, -0.416414, -0.398597, -0.560709, 0.594412, 0.0666667, 1, -0.415543, -0.39939, -0.559535, 0.595594, 0.1, 1, -0.414226, -0.400582, -0.557762, 0.597372, 0.133333, 1, -0.412716, -0.401939, -0.555729, 0.599396, 0.166667, 1, -0.41147, -0.403053, -0.554052, 0.601056, 0.2, 1, -0.411367, -0.403145, -0.553912, 0.601193, 0.233333, 1, -0.411979, -0.402599, -0.554736, 0.60038, 0.266667, 1, -0.413156, -0.401545, -0.556321, 0.598808, 0.3, 1, -0.414737, -0.40012, -0.55845, 0.596684, 0.333333, 1, -0.416549, -0.398474, -0.56089, 0.594229, 0.366667, 1, -0.417961, -0.397182, -0.562792, 0.592301, 0.4, 1, -0.418681, -0.39652, -0.563761, 0.591314, 0.533333, 1, -0.417916, -0.397223, -0.562731, 0.592363, 0.583333, 1, -0.417605, -0.397508, -0.562312, 0.592789)
tracks/14/type = "scale_3d"
tracks/14/imported = true
tracks/14/enabled = true
tracks/14/path = NodePath("rig/Skeleton3D:DEF-lip.B.L")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = PackedFloat32Array(0, 1, 0.10314, 0.0940035, 0.10314, 0.0333333, 1, 0.136816, 0.119938, 0.136816, 0.0666667, 1, 0.214904, 0.172954, 0.214904, 0.1, 1, 0.331441, 0.241929, 0.331441, 0.133333, 1, 0.477544, 0.319968, 0.477544, 0.166667, 1, 0.64141, 0.404405, 0.64141, 0.2, 1, 0.796958, 0.499852, 0.796958, 0.233333, 1, 0.926194, 0.60084, 0.926194, 0.266667, 1, 1.01693, 0.706031, 1.01693, 0.3, 1, 1.05809, 0.81437, 1.05809, 0.333333, 1, 1.0397, 0.925086, 1.0397, 0.366667, 1, 0.982387, 1.03868, 0.982387, 0.4, 1, 0.948463, 1.11257, 0.948463, 0.433333, 1, 0.939798, 1.13233, 0.939798, 0.466667, 1, 0.950981, 1.10605, 0.950981, 0.5, 1, 0.969319, 1.0643, 0.969319, 0.533333, 1, 0.986438, 1.02792, 0.986438, 0.566667, 1, 0.996287, 1.00753, 0.996287, 0.583333, 1, 1, 1, 1)
tracks/15/type = "position_3d"
tracks/15/imported = true
tracks/15/enabled = true
tracks/15/path = NodePath("rig/Skeleton3D:DEF-lip.B.R")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = PackedFloat32Array(0, 1, 2.84217e-14, 0.121528, 0.0399914, 0.0333333, 1, -1.7053e-14, 0.154549, 0.0510223, 0.0666667, 1, -1.13687e-14, 0.218919, 0.0735664, 0.1, 1, 2.27374e-14, 0.294084, 0.102883, 0.133333, 1, 4.54747e-14, 0.36032, 0.136036, 0.166667, 1, 0, 0.398734, 0.171895, 0.2, 1, 0, 0.378553, 0.212459, 0.233333, 1, 0, 0.320711, 0.25542, 0.266667, 1, -9.09496e-14, 0.242066, 0.300206, 0.3, 1, -2.27374e-13, 0.162821, 0.346362, 0.333333, 1, -2.27374e-13, 0.106533, 0.393544, 0.366667, 1, -2.27374e-13, 0.0853395, 0.441928, 0.4, 1, -2.27374e-13, 0.0738778, 0.47339, 0.433333, 1, -4.54747e-14, 0.0711052, 0.481805, 0.466667, 1, 1.81899e-13, 0.0745796, 0.470617, 0.5, 1, 0, 0.0804595, 0.452843, 0.533333, 1, 1.81899e-13, 0.0863393, 0.43735, 0.566667, 1, 2.27374e-13, 0.0898138, 0.428666, 0.583333, 1, 2.27374e-13, 0.0911501, 0.425459)
tracks/16/type = "rotation_3d"
tracks/16/imported = true
tracks/16/enabled = true
tracks/16/path = NodePath("rig/Skeleton3D:DEF-lip.B.R")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = PackedFloat32Array(0, 1, -0.416787, 0.398257, 0.561211, 0.593904, 0.0333333, 1, -0.416414, 0.398597, 0.560709, 0.594412, 0.0666667, 1, -0.415543, 0.39939, 0.559535, 0.595594, 0.1, 1, -0.414225, 0.400582, 0.557762, 0.597372, 0.133333, 1, -0.412716, 0.40194, 0.555729, 0.599396, 0.166667, 1, -0.41147, 0.403053, 0.554051, 0.601056, 0.2, 1, -0.411366, 0.403145, 0.553912, 0.601193, 0.233333, 1, -0.411979, 0.402599, 0.554736, 0.60038, 0.266667, 1, -0.413156, 0.401545, 0.556321, 0.598808, 0.3, 1, -0.414737, 0.40012, 0.558449, 0.596684, 0.333333, 1, -0.416549, 0.398474, 0.560889, 0.594229, 0.366667, 1, -0.417961, 0.397182, 0.562792, 0.592301, 0.4, 1, -0.418681, 0.39652, 0.563761, 0.591314, 0.533333, 1, -0.417916, 0.397223, 0.562731, 0.592363, 0.583333, 1, -0.417605, 0.397508, 0.562312, 0.592788)
tracks/17/type = "scale_3d"
tracks/17/imported = true
tracks/17/enabled = true
tracks/17/path = NodePath("rig/Skeleton3D:DEF-lip.B.R")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = PackedFloat32Array(0, 1, 0.10314, 0.0940034, 0.10314, 0.0333333, 1, 0.136816, 0.119938, 0.136816, 0.0666667, 1, 0.214904, 0.172954, 0.214904, 0.1, 1, 0.331441, 0.241929, 0.331441, 0.133333, 1, 0.477544, 0.319968, 0.477544, 0.166667, 1, 0.64141, 0.404406, 0.64141, 0.2, 1, 0.796958, 0.499852, 0.796958, 0.233333, 1, 0.926194, 0.60084, 0.926194, 0.266667, 1, 1.01693, 0.706031, 1.01693, 0.3, 1, 1.05809, 0.81437, 1.05809, 0.333333, 1, 1.0397, 0.925086, 1.0397, 0.366667, 1, 0.982387, 1.03868, 0.982387, 0.4, 1, 0.948463, 1.11257, 0.948463, 0.433333, 1, 0.939798, 1.13233, 0.939798, 0.466667, 1, 0.950981, 1.10605, 0.950981, 0.5, 1, 0.96932, 1.0643, 0.96932, 0.533333, 1, 0.986438, 1.02792, 0.986438, 0.566667, 1, 0.996287, 1.00753, 0.996287, 0.583333, 1, 1, 1, 1)
tracks/18/type = "position_3d"
tracks/18/imported = true
tracks/18/enabled = true
tracks/18/path = NodePath("rig/Skeleton3D:DEF-lip.T.L")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = PackedFloat32Array(0, 1, 0, 0.140409, 0.0450862, 0.0333333, 1, -4.54747e-14, 0.180598, 0.0575224, 0.0666667, 1, -5.68434e-14, 0.263594, 0.0829385, 0.1, 1, -3.41061e-14, 0.370234, 0.11599, 0.133333, 1, -4.54748e-14, 0.479549, 0.153367, 0.166667, 1, -2.27374e-13, 0.56877, 0.193793, 0.2, 1, -1.36424e-13, 0.590945, 0.239526, 0.233333, 1, -1.81899e-13, 0.55945, 0.28796, 0.266667, 1, -3.18323e-13, 0.487483, 0.338452, 0.333333, 1, -4.54747e-13, 0.301512, 0.443681, 0.366667, 1, -4.54747e-13, 0.24153, 0.498229, 0.4, 1, -4.54747e-13, 0.20909, 0.533699, 0.433333, 1, -4.54747e-13, 0.201243, 0.543185, 0.466667, 1, -4.09273e-13, 0.211077, 0.530572, 0.5, 1, -2.27374e-13, 0.227718, 0.510534, 0.533333, 1, -4.54747e-14, 0.244359, 0.493067, 0.566667, 1, -1.36424e-13, 0.254193, 0.483277, 0.583333, 1, -2.27374e-13, 0.257975, 0.479661)
tracks/19/type = "rotation_3d"
tracks/19/imported = true
tracks/19/enabled = true
tracks/19/path = NodePath("rig/Skeleton3D:DEF-lip.T.L")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = PackedFloat32Array(0, 1, -0.530395, -0.46862, -0.499535, 0.499541, 0.0333333, 1, -0.531578, -0.467434, -0.50065, 0.498276, 0.0666667, 1, -0.534325, -0.46466, -0.503237, 0.495319, 0.1, 1, -0.538425, -0.46046, -0.507098, 0.490843, 0.133333, 1, -0.543055, -0.455632, -0.511458, 0.485697, 0.166667, 1, -0.546815, -0.451643, -0.515, 0.481443, 0.2, 1, -0.547125, -0.451311, -0.515291, 0.48109, 0.233333, 1, -0.545287, -0.453272, -0.51356, 0.48318, 0.266667, 1, -0.541713, -0.457041, -0.510195, 0.487198, 0.3, 1, -0.536842, -0.46209, -0.505607, 0.49258, 0.333333, 1, -0.531153, -0.467862, -0.500248, 0.498733, 0.366667, 1, -0.526638, -0.472348, -0.495997, 0.503514, 0.4, 1, -0.524315, -0.474625, -0.493808, 0.505943, 0.433333, 1, -0.523773, -0.475153, -0.493298, 0.506506, 0.466667, 1, -0.524439, -0.474505, -0.493925, 0.505814, 0.5, 1, -0.525587, -0.473381, -0.495006, 0.504617, 0.533333, 1, -0.526785, -0.472204, -0.496134, 0.503361, 0.566667, 1, -0.527504, -0.471494, -0.496812, 0.502604, 0.583333, 1, -0.527783, -0.471217, -0.497076, 0.502309)
tracks/20/type = "scale_3d"
tracks/20/imported = true
tracks/20/enabled = true
tracks/20/path = NodePath("rig/Skeleton3D:DEF-lip.T.L")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = PackedFloat32Array(0, 1, 0.103114, 0.0940515, 0.103114, 0.0333333, 1, 0.136762, 0.120033, 0.136762, 0.0666667, 1, 0.214728, 0.173233, 0.214728, 0.1, 1, 0.330937, 0.24265, 0.330937, 0.133333, 1, 0.476451, 0.321428, 0.476451, 0.166667, 1, 0.639471, 0.406861, 0.639471, 0.2, 1, 0.794488, 0.502964, 0.794489, 0.233333, 1, 0.923731, 0.604037, 0.923731, 0.266667, 1, 1.01495, 0.708753, 1.01495, 0.3, 1, 1.05689, 0.816165, 1.05689, 0.333333, 1, 1.03935, 0.925713, 1.03935, 0.366667, 1, 0.982458, 1.03849, 0.982459, 0.4, 1, 0.94871, 1.11198, 0.94871, 0.433333, 1, 0.940079, 1.13165, 0.940079, 0.466667, 1, 0.951223, 1.10548, 0.951224, 0.5, 1, 0.969491, 1.06393, 0.969492, 0.533333, 1, 0.98652, 1.02775, 0.98652, 0.566667, 1, 0.996311, 1.00748, 0.996311, 0.583333, 1, 1, 1, 1)
tracks/21/type = "position_3d"
tracks/21/imported = true
tracks/21/enabled = true
tracks/21/path = NodePath("rig/Skeleton3D:DEF-lip.T.R")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = PackedFloat32Array(0, 1, 0, 0.140409, 0.0450862, 0.0333333, 1, -4.54747e-14, 0.180598, 0.0575224, 0.0666667, 1, -5.68434e-14, 0.263594, 0.0829385, 0.1, 1, -3.41061e-14, 0.370234, 0.11599, 0.133333, 1, -4.54748e-14, 0.479549, 0.153367, 0.166667, 1, -2.27374e-13, 0.56877, 0.193793, 0.2, 1, -1.36424e-13, 0.590945, 0.239526, 0.233333, 1, -1.81899e-13, 0.55945, 0.28796, 0.266667, 1, -3.18323e-13, 0.487483, 0.338452, 0.333333, 1, -4.54747e-13, 0.301512, 0.443681, 0.366667, 1, -4.54747e-13, 0.24153, 0.498229, 0.4, 1, -4.54747e-13, 0.20909, 0.533699, 0.433333, 1, -4.54747e-13, 0.201243, 0.543185, 0.466667, 1, -4.09273e-13, 0.211077, 0.530572, 0.5, 1, -2.27374e-13, 0.227718, 0.510534, 0.533333, 1, -4.54747e-14, 0.244359, 0.493067, 0.566667, 1, -1.36424e-13, 0.254193, 0.483277, 0.583333, 1, -2.27374e-13, 0.257975, 0.479661)
tracks/22/type = "rotation_3d"
tracks/22/imported = true
tracks/22/enabled = true
tracks/22/path = NodePath("rig/Skeleton3D:DEF-lip.T.R")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = PackedFloat32Array(0, 1, -0.530395, 0.46862, 0.499535, 0.499541, 0.0333333, 1, -0.531579, 0.467434, 0.50065, 0.498276, 0.0666667, 1, -0.534325, 0.46466, 0.503236, 0.495319, 0.1, 1, -0.538425, 0.46046, 0.507098, 0.490842, 0.133333, 1, -0.543055, 0.455632, 0.511458, 0.485696, 0.166667, 1, -0.546815, 0.451642, 0.514999, 0.481443, 0.2, 1, -0.547125, 0.451311, 0.515291, 0.48109, 0.233333, 1, -0.545287, 0.453271, 0.51356, 0.48318, 0.266667, 1, -0.541714, 0.457041, 0.510195, 0.487198, 0.3, 1, -0.536842, 0.46209, 0.505607, 0.49258, 0.333333, 1, -0.531153, 0.467862, 0.500248, 0.498733, 0.366667, 1, -0.526638, 0.472348, 0.495997, 0.503514, 0.4, 1, -0.524315, 0.474626, 0.493808, 0.505943, 0.433333, 1, -0.523773, 0.475154, 0.493298, 0.506506, 0.466667, 1, -0.524439, 0.474504, 0.493925, 0.505814, 0.5, 1, -0.525586, 0.473381, 0.495006, 0.504616, 0.533333, 1, -0.526785, 0.472204, 0.496135, 0.503361, 0.566667, 1, -0.527504, 0.471494, 0.496812, 0.502604, 0.583333, 1, -0.527783, 0.471217, 0.497076, 0.502309)
tracks/23/type = "scale_3d"
tracks/23/imported = true
tracks/23/enabled = true
tracks/23/path = NodePath("rig/Skeleton3D:DEF-lip.T.R")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = PackedFloat32Array(0, 1, 0.103114, 0.0940515, 0.103114, 0.0333333, 1, 0.136762, 0.120033, 0.136762, 0.0666667, 1, 0.214728, 0.173233, 0.214728, 0.1, 1, 0.330937, 0.24265, 0.330937, 0.133333, 1, 0.476451, 0.321428, 0.476451, 0.166667, 1, 0.639471, 0.406861, 0.639471, 0.2, 1, 0.794488, 0.502964, 0.794488, 0.233333, 1, 0.923731, 0.604037, 0.923731, 0.266667, 1, 1.01495, 0.708753, 1.01495, 0.3, 1, 1.05689, 0.816166, 1.05689, 0.333333, 1, 1.03935, 0.925713, 1.03935, 0.366667, 1, 0.982459, 1.03849, 0.982459, 0.4, 1, 0.948711, 1.11198, 0.948711, 0.433333, 1, 0.940079, 1.13165, 0.940079, 0.466667, 1, 0.951224, 1.10548, 0.951224, 0.5, 1, 0.969492, 1.06393, 0.969492, 0.533333, 1, 0.98652, 1.02775, 0.98652, 0.566667, 1, 0.996311, 1.00748, 0.996311, 0.583333, 1, 1, 1, 1)
tracks/24/type = "position_3d"
tracks/24/imported = true
tracks/24/enabled = true
tracks/24/path = NodePath("rig/Skeleton3D:DEF-bone.01")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = PackedFloat32Array(0, 1, -1.11751e-15, 0.111211, 0, 0.0333333, 1, -1.40997e-15, 0.140315, 0, 0.0666667, 1, -1.95455e-15, 0.19451, 0, 0.1, 1, -2.53704e-15, 0.252477, 0, 0.133333, 1, -2.96609e-15, 0.295174, 0, 0.166667, 1, -3.07316e-15, 0.30583, 0, 0.2, 1, -2.63782e-15, 0.262506, 0, 0.233333, 1, -1.91193e-15, 0.190269, 0, 0.266667, 1, -1.08498e-15, 0.107974, 0, 0.3, 1, -3.69289e-16, 0.0367503, 0, 0.333333, 1, 0, 0, 0, 0.583333, 1, 0, 0, 0)
tracks/25/type = "rotation_3d"
tracks/25/imported = true
tracks/25/enabled = true
tracks/25/path = NodePath("rig/Skeleton3D:DEF-bone.01")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = PackedFloat32Array(0, 1, -2.98023e-08, 1.19209e-07, 0, 1)
tracks/26/type = "scale_3d"
tracks/26/imported = true
tracks/26/enabled = true
tracks/26/path = NodePath("rig/Skeleton3D:DEF-bone.01")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = PackedFloat32Array(0, 1, 0.093996, 0.113183, 0.093996, 0.0333333, 1, 0.119923, 0.156151, 0.119923, 0.0666667, 1, 0.172911, 0.267795, 0.172911, 0.1, 1, 0.241817, 0.456464, 0.241817, 0.133333, 1, 0.31974, 0.714701, 0.31974, 0.166667, 1, 0.404022, 1.01925, 0.404022, 0.2, 1, 0.499366, 1.27314, 0.499366, 0.233333, 1, 0.60034, 1.43108, 0.60034, 0.266667, 1, 0.705606, 1.47111, 0.705606, 0.3, 1, 0.81409, 1.38311, 0.81409, 0.333333, 1, 0.924988, 1.16877, 0.924988, 0.366667, 1, 1.03871, 0.936253, 1.03871, 0.4, 1, 1.11266, 0.810507, 1.11266, 0.433333, 1, 1.13244, 0.780089, 1.13244, 0.466667, 1, 1.10614, 0.818207, 1.10614, 0.5, 1, 1.06436, 0.882714, 1.06436, 0.533333, 1, 1.02795, 0.947221, 1.02795, 0.566667, 1, 1.00754, 0.985339, 1.00754, 0.583333, 1, 1, 1, 1)
tracks/27/type = "position_3d"
tracks/27/imported = true
tracks/27/enabled = true
tracks/27/path = NodePath("rig/Skeleton3D:DEF-lid2.B.L")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = PackedFloat32Array(0, 1, 0.0262469, 0.151494, 0.0371895, 0.0333333, 1, 0.0334867, 0.195891, 0.0474476, 0.0666667, 1, 0.0482827, 0.289821, 0.068412, 0.1, 1, 0.0675236, 0.414937, 0.0956747, 0.133333, 1, 0.0892826, 0.549544, 0.126505, 0.166667, 1, 0.112817, 0.66859, 0.159851, 0.2, 1, 0.13944, 0.715631, 0.197574, 0.233333, 1, 0.167636, 0.699603, 0.237524, 0.266667, 1, 0.19703, 0.631557, 0.279173, 0.3, 1, 0.227322, 0.529015, 0.322094, 0.333333, 1, 0.258289, 0.415975, 0.365971, 0.366667, 1, 0.290044, 0.333222, 0.410965, 0.4, 1, 0.310693, 0.288468, 0.440223, 0.433333, 1, 0.316215, 0.277642, 0.448048, 0.466667, 1, 0.308873, 0.291208, 0.437644, 0.5, 1, 0.297207, 0.314167, 0.421115, 0.533333, 1, 0.287039, 0.337126, 0.406708, 0.566667, 1, 0.28134, 0.350692, 0.398632, 0.583333, 1, 0.279235, 0.35591, 0.395649)
tracks/28/type = "rotation_3d"
tracks/28/imported = true
tracks/28/enabled = true
tracks/28/path = NodePath("rig/Skeleton3D:DEF-lid2.B.L")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = PackedFloat32Array(0, 1, 0.661249, -0.575614, -0.457231, 0.149527, 0.0333333, 1, 0.665629, -0.567509, -0.46201, 0.146351, 0.0666667, 1, 0.675578, -0.548778, -0.472153, 0.139678, 0.1, 1, 0.689587, -0.521122, -0.485619, 0.130677, 0.133333, 1, 0.704245, -0.490138, -0.499087, 0.12131, 0.166667, 1, 0.715034, -0.465869, -0.508517, 0.11447, 0.2, 1, 0.715871, -0.463927, -0.509233, 0.113938, 0.233333, 1, 0.710754, -0.475677, -0.5048, 0.117206, 0.266667, 1, 0.700109, -0.499144, -0.495305, 0.123998, 0.3, 1, 0.684334, -0.531747, -0.480568, 0.134114, 0.333333, 1, 0.664053, -0.570426, -0.460329, 0.147461, 0.366667, 1, 0.647446, -0.601091, -0.439742, 0.161649, 0.4, 1, 0.63891, -0.6163, -0.42788, 0.169962, 0.433333, 1, 0.636945, -0.619788, -0.424915, 0.172081, 0.466667, 1, 0.639303, -0.615515, -0.428668, 0.169343, 0.5, 1, 0.643411, -0.608038, -0.434902, 0.16484, 0.533333, 1, 0.647827, -0.60009, -0.440918, 0.160634, 0.566667, 1, 0.650481, -0.59527, -0.444408, 0.158209, 0.583333, 1, 0.651517, -0.593384, -0.445735, 0.157294)
tracks/29/type = "scale_3d"
tracks/29/imported = true
tracks/29/enabled = true
tracks/29/path = NodePath("rig/Skeleton3D:DEF-lid2.B.L")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = PackedFloat32Array(0, 1, 0.102452, 0.0952703, 0.102452, 0.0333333, 1, 0.135412, 0.122432, 0.135412, 0.0666667, 1, 0.210492, 0.180192, 0.210493, 0.1, 1, 0.31936, 0.260278, 0.31936, 0.133333, 1, 0.452271, 0.356579, 0.452271, 0.2, 1, 0.741965, 0.576693, 0.741965, 0.233333, 1, 0.870223, 0.680468, 0.870223, 0.266667, 1, 0.970343, 0.77475, 0.970343, 0.3, 1, 1.0287, 0.860545, 1.0287, 0.333333, 1, 1.03056, 0.941578, 1.03056, 0.366667, 1, 0.984375, 1.03363, 0.984375, 0.4, 1, 0.95514, 1.09689, 0.95514, 0.433333, 1, 0.947381, 1.11426, 0.947381, 0.466667, 1, 0.957534, 1.0909, 0.957534, 0.5, 1, 0.973937, 1.05424, 0.973938, 0.533333, 1, 0.988633, 1.0233, 0.988633, 0.566667, 1, 0.996922, 1.00623, 0.996923, 0.583333, 1, 0.999999, 1, 1)
tracks/30/type = "position_3d"
tracks/30/imported = true
tracks/30/enabled = true
tracks/30/path = NodePath("rig/Skeleton3D:DEF-lid3.B.L")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = PackedFloat32Array(0, 1, 0.0204573, 0.148781, 0.0439026, 0.0333333, 1, 0.0261001, 0.192148, 0.0560123, 0.0666667, 1, 0.0376324, 0.283402, 0.0807612, 0.1, 1, 0.0526291, 0.403997, 0.112945, 0.133333, 1, 0.0695885, 0.532414, 0.149341, 0.166667, 1, 0.0879315, 0.64416, 0.188706, 0.2, 1, 0.108682, 0.685116, 0.233238, 0.233333, 1, 0.130658, 0.665303, 0.2804, 0.266667, 1, 0.153568, 0.596297, 0.329566, 0.3, 1, 0.177179, 0.495864, 0.380236, 0.333333, 1, 0.201315, 0.387963, 0.432033, 0.366667, 1, 0.226065, 0.310782, 0.485149, 0.4, 1, 0.24216, 0.269041, 0.519688, 0.433333, 1, 0.246464, 0.258944, 0.528925, 0.466667, 1, 0.240741, 0.271597, 0.516643, 0.5, 1, 0.231649, 0.29301, 0.497131, 0.533333, 1, 0.223723, 0.314423, 0.480123, 0.566667, 1, 0.219281, 0.327076, 0.47059, 0.583333, 1, 0.21764, 0.331942, 0.467069)
tracks/31/type = "rotation_3d"
tracks/31/imported = true
tracks/31/enabled = true
tracks/31/path = NodePath("rig/Skeleton3D:DEF-lid3.B.L")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = PackedFloat32Array(0, 1, -0.621937, 0.727434, 0.271858, 0.100634, 0.0333333, 1, -0.617922, 0.728709, 0.275455, 0.106207, 0.0666667, 1, -0.608811, 0.731621, 0.282924, 0.118463, 0.1, 1, -0.595451, 0.73579, 0.292629, 0.135718, 0.133333, 1, -0.580471, 0.740227, 0.302192, 0.154263, 0.166667, 1, -0.568572, 0.743701, 0.308651, 0.168433, 0.2, 1, -0.567605, 0.743985, 0.309128, 0.169563, 0.233333, 1, -0.573401, 0.742308, 0.306117, 0.162732, 0.266667, 1, -0.584833, 0.738979, 0.299499, 0.148933, 0.3, 1, -0.600613, 0.734217, 0.288993, 0.12912, 0.333333, 1, -0.619357, 0.72825, 0.274198, 0.104232, 0.366667, 1, -0.635266, 0.723314, 0.258222, 0.0810847, 0.4, 1, -0.643562, 0.720583, 0.248864, 0.0682308, 0.433333, 1, -0.645532, 0.719928, 0.246491, 0.0650719, 0.466667, 1, -0.643063, 0.72072, 0.249524, 0.0690647, 0.5, 1, -0.638836, 0.722055, 0.254531, 0.0757547, 0.533333, 1, -0.634546, 0.723433, 0.259272, 0.0823088, 0.566667, 1, -0.631983, 0.724243, 0.262008, 0.0861515, 0.583333, 1, -0.630992, 0.724555, 0.263042, 0.0876211)
tracks/32/type = "scale_3d"
tracks/32/imported = true
tracks/32/enabled = true
tracks/32/path = NodePath("rig/Skeleton3D:DEF-lid3.B.L")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = PackedFloat32Array(0, 1, 0.103095, 0.0940861, 0.103095, 0.0333333, 1, 0.136723, 0.120101, 0.136723, 0.0666667, 1, 0.214602, 0.173434, 0.214602, 0.1, 1, 0.330577, 0.243166, 0.330577, 0.133333, 1, 0.475673, 0.322473, 0.475673, 0.166667, 1, 0.638094, 0.408619, 0.638094, 0.2, 1, 0.792735, 0.505191, 0.792735, 0.233333, 1, 0.92198, 0.606326, 0.92198, 0.266667, 1, 1.01353, 0.710704, 1.01353, 0.3, 1, 1.05604, 0.817455, 1.05604, 0.333333, 1, 1.0391, 0.926165, 1.0391, 0.366667, 1, 0.98251, 1.03836, 0.98251, 0.4, 1, 0.948888, 1.11156, 0.948888, 0.433333, 1, 0.940281, 1.13117, 0.940281, 0.466667, 1, 0.951399, 1.10508, 0.951399, 0.5, 1, 0.969616, 1.06365, 0.969616, 0.533333, 1, 0.98658, 1.02762, 0.98658, 0.566667, 1, 0.996328, 1.00745, 0.996328, 0.583333, 1, 1, 1, 1)
tracks/33/type = "position_3d"
tracks/33/imported = true
tracks/33/enabled = true
tracks/33/path = NodePath("rig/Skeleton3D:DEF-lid4.B.L")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/keys = PackedFloat32Array(0, 1, 0.0119839, 0.149475, 0.0462899, 0.0333333, 1, 0.0152895, 0.193106, 0.0590581, 0.0666667, 1, 0.022045, 0.285044, 0.0851527, 0.1, 1, 0.0308301, 0.406795, 0.119087, 0.133333, 1, 0.0407649, 0.536795, 0.157461, 0.166667, 1, 0.0515103, 0.650409, 0.198967, 0.2, 1, 0.063666, 0.692921, 0.245921, 0.233333, 1, 0.0765397, 0.674076, 0.295647, 0.266667, 1, 0.0899604, 0.605315, 0.347487, 0.3, 1, 0.103791, 0.504343, 0.400912, 0.333333, 1, 0.11793, 0.395128, 0.455526, 0.366667, 1, 0.132429, 0.316521, 0.51153, 0.4, 1, 0.141857, 0.27401, 0.547947, 0.433333, 1, 0.144379, 0.263727, 0.557687, 0.466667, 1, 0.141026, 0.276613, 0.544736, 0.5, 1, 0.1357, 0.298421, 0.524163, 0.533333, 1, 0.131057, 0.32023, 0.50623, 0.566667, 1, 0.128455, 0.333116, 0.496179, 0.583333, 1, 0.127494, 0.338073, 0.492466)
tracks/34/type = "rotation_3d"
tracks/34/imported = true
tracks/34/enabled = true
tracks/34/path = NodePath("rig/Skeleton3D:DEF-lid4.B.L")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/keys = PackedFloat32Array(0, 1, -0.554826, 0.782209, 0.0985883, 0.265703, 0.0333333, 1, -0.54375, 0.78765, 0.0995065, 0.272106, 0.0666667, 1, -0.518866, 0.799266, 0.100703, 0.286026, 0.1, 1, -0.483358, 0.814302, 0.101088, 0.305054, 0.133333, 1, -0.444817, 0.828641, 0.100248, 0.324718, 0.166667, 1, -0.415911, 0.83808, 0.0986016, 0.338996, 0.2, 1, -0.413654, 0.838769, 0.098435, 0.3401, 0.233333, 1, -0.427504, 0.834429, 0.099331, 0.333321, 0.266667, 1, -0.455957, 0.824706, 0.100563, 0.31914, 0.3, 1, -0.496909, 0.808793, 0.101015, 0.297879, 0.333333, 1, -0.547704, 0.785725, 0.0992188, 0.269839, 0.366667, 1, -0.591267, 0.763255, 0.0931775, 0.243234, 0.4, 1, -0.613563, 0.750655, 0.0887587, 0.228429, 0.433333, 1, -0.618803, 0.747589, 0.0875115, 0.224801, 0.466667, 1, -0.612299, 0.751355, 0.0891765, 0.229354, 0.5, 1, -0.601059, 0.757716, 0.0917919, 0.237001, 0.533333, 1, -0.58944, 0.764148, 0.0939712, 0.244557, 0.566667, 1, -0.582463, 0.767912, 0.0951521, 0.248986, 0.583333, 1, -0.579754, 0.769357, 0.095577, 0.250681)
tracks/35/type = "scale_3d"
tracks/35/imported = true
tracks/35/enabled = true
tracks/35/path = NodePath("rig/Skeleton3D:DEF-lid4.B.L")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/keys = PackedFloat32Array(0, 1, 0.102062, 0.096, 0.102062, 0.0333333, 1, 0.134627, 0.123862, 0.134627, 0.0666667, 1, 0.208136, 0.184266, 0.208136, 0.1, 1, 0.313327, 0.270336, 0.313327, 0.133333, 1, 0.440301, 0.376224, 0.440301, 0.2, 1, 0.717293, 0.617046, 0.717293, 0.233333, 1, 0.844373, 0.722769, 0.844373, 0.266667, 1, 0.947795, 0.811946, 0.947795, 0.3, 1, 1.01352, 0.88619, 1.01351, 0.333333, 1, 1.02541, 0.95105, 1.02541, 0.366667, 1, 0.985613, 1.03062, 0.985613, 0.4, 1, 0.959145, 1.08764, 0.959145, 0.433333, 1, 0.951942, 1.1036, 0.951942, 0.466667, 1, 0.961459, 1.08198, 0.961459, 0.5, 1, 0.976678, 1.04833, 0.976678, 0.533333, 1, 0.989927, 1.02059, 0.989927, 0.566667, 1, 0.997295, 1.00546, 0.997295, 0.583333, 1, 1, 1, 1)
tracks/36/type = "position_3d"
tracks/36/imported = true
tracks/36/enabled = true
tracks/36/path = NodePath("rig/Skeleton3D:DEF-lid2.B.R")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/keys = PackedFloat32Array(0, 1, -0.0262469, 0.151494, 0.0371894, 0.0333333, 1, -0.0334867, 0.195891, 0.0474475, 0.0666667, 1, -0.0482827, 0.28982, 0.068412, 0.1, 1, -0.0675236, 0.414937, 0.0956747, 0.133333, 1, -0.0892826, 0.549544, 0.126505, 0.166667, 1, -0.112817, 0.66859, 0.159851, 0.2, 1, -0.13944, 0.71563, 0.197574, 0.233333, 1, -0.167636, 0.699603, 0.237524, 0.266667, 1, -0.19703, 0.631557, 0.279173, 0.3, 1, -0.227322, 0.529015, 0.322094, 0.333333, 1, -0.258289, 0.415975, 0.365971, 0.366667, 1, -0.290044, 0.333222, 0.410965, 0.4, 1, -0.310693, 0.288467, 0.440223, 0.433333, 1, -0.316215, 0.277641, 0.448048, 0.466667, 1, -0.308873, 0.291208, 0.437644, 0.5, 1, -0.297207, 0.314167, 0.421115, 0.533333, 1, -0.287039, 0.337126, 0.406708, 0.566667, 1, -0.28134, 0.350692, 0.398632, 0.583333, 1, -0.279235, 0.35591, 0.39565)
tracks/37/type = "rotation_3d"
tracks/37/imported = true
tracks/37/enabled = true
tracks/37/path = NodePath("rig/Skeleton3D:DEF-lid2.B.R")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/keys = PackedFloat32Array(0, 1, 0.661248, 0.575615, 0.457232, 0.149528, 0.0333333, 1, 0.665628, 0.567509, 0.462011, 0.146352, 0.0666667, 1, 0.675577, 0.548778, 0.472153, 0.139678, 0.1, 1, 0.689586, 0.521122, 0.485619, 0.130677, 0.133333, 1, 0.704245, 0.490138, 0.499087, 0.12131, 0.166667, 1, 0.715034, 0.465869, 0.508517, 0.11447, 0.2, 1, 0.71587, 0.463929, 0.509233, 0.113938, 0.233333, 1, 0.710754, 0.475678, 0.504799, 0.117206, 0.266667, 1, 0.700109, 0.499144, 0.495305, 0.123998, 0.3, 1, 0.684334, 0.531747, 0.480568, 0.134114, 0.333333, 1, 0.664052, 0.570427, 0.46033, 0.147462, 0.366667, 1, 0.647446, 0.601091, 0.439741, 0.161649, 0.4, 1, 0.63891, 0.6163, 0.427879, 0.169962, 0.433333, 1, 0.636945, 0.619788, 0.424915, 0.172081, 0.466667, 1, 0.639303, 0.615515, 0.428668, 0.169343, 0.5, 1, 0.64341, 0.608038, 0.434902, 0.164841, 0.533333, 1, 0.647827, 0.60009, 0.440918, 0.160635, 0.566667, 1, 0.650481, 0.59527, 0.444408, 0.158209, 0.583333, 1, 0.651518, 0.593384, 0.445734, 0.157294)
tracks/38/type = "scale_3d"
tracks/38/imported = true
tracks/38/enabled = true
tracks/38/path = NodePath("rig/Skeleton3D:DEF-lid2.B.R")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/keys = PackedFloat32Array(0, 1, 0.102452, 0.0952704, 0.102452, 0.0333333, 1, 0.135412, 0.122433, 0.135412, 0.0666667, 1, 0.210493, 0.180192, 0.210493, 0.1, 1, 0.31936, 0.260279, 0.31936, 0.133333, 1, 0.452271, 0.356579, 0.452271, 0.2, 1, 0.741966, 0.576692, 0.741965, 0.233333, 1, 0.870223, 0.680469, 0.870223, 0.266667, 1, 0.970343, 0.774751, 0.970343, 0.3, 1, 1.0287, 0.860545, 1.0287, 0.333333, 1, 1.03056, 0.941578, 1.03056, 0.366667, 1, 0.984375, 1.03363, 0.984375, 0.4, 1, 0.95514, 1.09689, 0.95514, 0.433333, 1, 0.947381, 1.11426, 0.947381, 0.466667, 1, 0.957533, 1.0909, 0.957533, 0.5, 1, 0.973937, 1.05424, 0.973937, 0.533333, 1, 0.988634, 1.0233, 0.988634, 0.566667, 1, 0.996923, 1.00623, 0.996923, 0.583333, 1, 1, 1, 1)
tracks/39/type = "position_3d"
tracks/39/imported = true
tracks/39/enabled = true
tracks/39/path = NodePath("rig/Skeleton3D:DEF-lid3.B.R")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/keys = PackedFloat32Array(0, 1, -0.0204573, 0.148781, 0.0439026, 0.0333333, 1, -0.0261001, 0.192148, 0.0560124, 0.0666667, 1, -0.0376324, 0.283402, 0.0807612, 0.1, 1, -0.0526291, 0.403997, 0.112945, 0.133333, 1, -0.0695885, 0.532414, 0.149341, 0.166667, 1, -0.0879315, 0.64416, 0.188706, 0.2, 1, -0.108682, 0.685116, 0.233238, 0.233333, 1, -0.130658, 0.665303, 0.2804, 0.266667, 1, -0.153568, 0.596297, 0.329566, 0.3, 1, -0.177179, 0.495864, 0.380236, 0.333333, 1, -0.201315, 0.387963, 0.432033, 0.366667, 1, -0.226065, 0.310782, 0.485149, 0.4, 1, -0.24216, 0.269041, 0.519688, 0.433333, 1, -0.246464, 0.258944, 0.528925, 0.466667, 1, -0.240741, 0.271597, 0.516643, 0.5, 1, -0.231649, 0.29301, 0.497131, 0.533333, 1, -0.223723, 0.314423, 0.480123, 0.566667, 1, -0.219281, 0.327076, 0.47059, 0.583333, 1, -0.21764, 0.331942, 0.467069)
tracks/40/type = "rotation_3d"
tracks/40/imported = true
tracks/40/enabled = true
tracks/40/path = NodePath("rig/Skeleton3D:DEF-lid3.B.R")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/keys = PackedFloat32Array(0, 1, -0.621938, -0.727433, -0.271858, 0.100634, 0.0333333, 1, -0.617921, -0.72871, -0.275455, 0.106207, 0.0666667, 1, -0.60881, -0.731622, -0.282925, 0.118462, 0.1, 1, -0.595451, -0.73579, -0.292629, 0.135718, 0.133333, 1, -0.580471, -0.740227, -0.302192, 0.154263, 0.166667, 1, -0.568572, -0.743701, -0.308651, 0.168432, 0.2, 1, -0.567605, -0.743984, -0.309128, 0.169562, 0.233333, 1, -0.5734, -0.742309, -0.306117, 0.162731, 0.266667, 1, -0.584832, -0.73898, -0.299499, 0.148933, 0.3, 1, -0.600614, -0.734217, -0.288994, 0.129119, 0.333333, 1, -0.619357, -0.72825, -0.274198, 0.104232, 0.366667, 1, -0.635266, -0.723314, -0.258222, 0.0810845, 0.4, 1, -0.643562, -0.720583, -0.248864, 0.0682305, 0.433333, 1, -0.645532, -0.719928, -0.246491, 0.0650717, 0.466667, 1, -0.643063, -0.720721, -0.249524, 0.0690646, 0.5, 1, -0.638836, -0.722055, -0.25453, 0.0757548, 0.533333, 1, -0.634546, -0.723433, -0.259272, 0.0823088, 0.566667, 1, -0.631983, -0.724243, -0.262007, 0.0861515, 0.583333, 1, -0.630991, -0.724556, -0.263042, 0.0876213)
tracks/41/type = "scale_3d"
tracks/41/imported = true
tracks/41/enabled = true
tracks/41/path = NodePath("rig/Skeleton3D:DEF-lid3.B.R")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/keys = PackedFloat32Array(0, 1, 0.103095, 0.0940862, 0.103095, 0.0333333, 1, 0.136723, 0.120101, 0.136723, 0.0666667, 1, 0.214602, 0.173434, 0.214602, 0.1, 1, 0.330577, 0.243166, 0.330577, 0.133333, 1, 0.475673, 0.322473, 0.475673, 0.166667, 1, 0.638095, 0.408618, 0.638095, 0.2, 1, 0.792736, 0.505191, 0.792736, 0.233333, 1, 0.92198, 0.606327, 0.92198, 0.266667, 1, 1.01353, 0.710704, 1.01353, 0.3, 1, 1.05604, 0.817455, 1.05604, 0.333333, 1, 1.0391, 0.926165, 1.0391, 0.366667, 1, 0.982511, 1.03836, 0.982511, 0.4, 1, 0.948889, 1.11156, 0.948889, 0.433333, 1, 0.940282, 1.13117, 0.940282, 0.466667, 1, 0.951399, 1.10507, 0.951399, 0.5, 1, 0.969616, 1.06365, 0.969616, 0.533333, 1, 0.98658, 1.02762, 0.98658, 0.566667, 1, 0.996328, 1.00745, 0.996328, 0.583333, 1, 1, 1, 1)
tracks/42/type = "position_3d"
tracks/42/imported = true
tracks/42/enabled = true
tracks/42/path = NodePath("rig/Skeleton3D:DEF-lid4.B.R")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/keys = PackedFloat32Array(0, 1, -0.0119839, 0.149475, 0.0462898, 0.0333333, 1, -0.0152895, 0.193106, 0.0590581, 0.0666667, 1, -0.022045, 0.285044, 0.0851527, 0.1, 1, -0.0308301, 0.406795, 0.119087, 0.133333, 1, -0.040765, 0.536795, 0.157461, 0.166667, 1, -0.0515103, 0.650409, 0.198967, 0.2, 1, -0.0636661, 0.692921, 0.245921, 0.233333, 1, -0.0765397, 0.674076, 0.295647, 0.266667, 1, -0.0899604, 0.605316, 0.347487, 0.3, 1, -0.103791, 0.504343, 0.400912, 0.333333, 1, -0.11793, 0.395127, 0.455526, 0.366667, 1, -0.132429, 0.316521, 0.51153, 0.4, 1, -0.141857, 0.27401, 0.547947, 0.433333, 1, -0.144379, 0.263727, 0.557687, 0.466667, 1, -0.141026, 0.276613, 0.544736, 0.5, 1, -0.1357, 0.298421, 0.524163, 0.533333, 1, -0.131057, 0.32023, 0.50623, 0.566667, 1, -0.128455, 0.333116, 0.496179, 0.583333, 1, -0.127494, 0.338073, 0.492466)
tracks/43/type = "rotation_3d"
tracks/43/imported = true
tracks/43/enabled = true
tracks/43/path = NodePath("rig/Skeleton3D:DEF-lid4.B.R")
tracks/43/interp = 1
tracks/43/loop_wrap = true
tracks/43/keys = PackedFloat32Array(0, 1, -0.554826, -0.782209, -0.0985886, 0.265703, 0.0333333, 1, -0.543751, -0.787649, -0.0995057, 0.272106, 0.0666667, 1, -0.518867, -0.799266, -0.100702, 0.286026, 0.1, 1, -0.483358, -0.814303, -0.101087, 0.305053, 0.133333, 1, -0.444817, -0.828641, -0.100248, 0.324718, 0.166667, 1, -0.415912, -0.838079, -0.0986015, 0.338996, 0.2, 1, -0.413653, -0.838769, -0.0984349, 0.3401, 0.233333, 1, -0.427505, -0.834429, -0.0993306, 0.33332, 0.266667, 1, -0.455958, -0.824705, -0.100563, 0.31914, 0.3, 1, -0.496909, -0.808793, -0.101016, 0.297879, 0.333333, 1, -0.547704, -0.785725, -0.0992189, 0.269839, 0.366667, 1, -0.591267, -0.763256, -0.0931775, 0.243234, 0.4, 1, -0.613563, -0.750655, -0.0887589, 0.228429, 0.433333, 1, -0.618803, -0.747589, -0.0875114, 0.224801, 0.466667, 1, -0.612299, -0.751355, -0.0891761, 0.229353, 0.5, 1, -0.601059, -0.757716, -0.091792, 0.237001, 0.533333, 1, -0.58944, -0.764148, -0.0939712, 0.244557, 0.566667, 1, -0.582464, -0.767912, -0.0951521, 0.248986, 0.583333, 1, -0.579755, -0.769356, -0.095577, 0.250681)
tracks/44/type = "scale_3d"
tracks/44/imported = true
tracks/44/enabled = true
tracks/44/path = NodePath("rig/Skeleton3D:DEF-lid4.B.R")
tracks/44/interp = 1
tracks/44/loop_wrap = true
tracks/44/keys = PackedFloat32Array(0, 1, 0.102062, 0.0960002, 0.102062, 0.0333333, 1, 0.134628, 0.123862, 0.134628, 0.0666667, 1, 0.208136, 0.184266, 0.208136, 0.1, 1, 0.313326, 0.270337, 0.313326, 0.133333, 1, 0.440301, 0.376224, 0.440301, 0.2, 1, 0.717293, 0.617047, 0.717293, 0.233333, 1, 0.844373, 0.722769, 0.844373, 0.266667, 1, 0.947795, 0.811947, 0.947796, 0.3, 1, 1.01351, 0.886192, 1.01351, 0.333333, 1, 1.02541, 0.951051, 1.02541, 0.366667, 1, 0.985613, 1.03062, 0.985613, 0.4, 1, 0.959145, 1.08765, 0.959145, 0.433333, 1, 0.951941, 1.1036, 0.951941, 0.466667, 1, 0.961458, 1.08198, 0.961458, 0.5, 1, 0.976678, 1.04833, 0.976678, 0.533333, 1, 0.989926, 1.0206, 0.989927, 0.566667, 1, 0.997295, 1.00547, 0.997295, 0.583333, 1, 1, 1, 1)
tracks/45/type = "position_3d"
tracks/45/imported = true
tracks/45/enabled = true
tracks/45/path = NodePath("rig/Skeleton3D:DEF-lid2.T.L")
tracks/45/interp = 1
tracks/45/loop_wrap = true
tracks/45/keys = PackedFloat32Array(0, 1, 0.0234474, 0.170595, 0.035103, 0.0333333, 1, 0.029915, 0.222243, 0.0447856, 0.0666667, 1, 0.0431329, 0.335014, 0.0645739, 0.1, 1, 0.0603216, 0.491971, 0.090307, 0.133333, 1, 0.0797598, 0.670157, 0.119408, 0.166667, 1, 0.100784, 0.840598, 0.150883, 0.2, 1, 0.124568, 0.930487, 0.186489, 0.233333, 1, 0.149756, 0.941113, 0.224199, 0.266667, 1, 0.176015, 0.879822, 0.26351, 0.3, 1, 0.203076, 0.76243, 0.304024, 0.333333, 1, 0.23074, 0.613217, 0.345439, 0.366667, 1, 0.259108, 0.491225, 0.387909, 0.4, 1, 0.277555, 0.425249, 0.415525, 0.433333, 1, 0.282488, 0.40929, 0.422911, 0.466667, 1, 0.275928, 0.429289, 0.413091, 0.5, 1, 0.265507, 0.463134, 0.397489, 0.533333, 1, 0.256424, 0.496979, 0.38389, 0.566667, 1, 0.251332, 0.516979, 0.376268, 0.583333, 1, 0.249452, 0.524671, 0.373452)
tracks/46/type = "rotation_3d"
tracks/46/imported = true
tracks/46/enabled = true
tracks/46/path = NodePath("rig/Skeleton3D:DEF-lid2.T.L")
tracks/46/interp = 1
tracks/46/loop_wrap = true
tracks/46/keys = PackedFloat32Array(0, 1, 0.721246, -0.597745, -0.142555, 0.319661, 0.0333333, 1, 0.722436, -0.597637, -0.147338, 0.314974, 0.0666667, 1, 0.725487, -0.596376, -0.156563, 0.305765, 0.133333, 1, 0.73632, -0.58811, -0.178275, 0.283156, 0.166667, 1, 0.741414, -0.583149, -0.185114, 0.275636, 0.2, 1, 0.741844, -0.582709, -0.185621, 0.275067, 0.233333, 1, 0.73933, -0.585224, -0.18243, 0.27861, 0.266667, 1, 0.734603, -0.58962, -0.175331, 0.286296, 0.3, 1, 0.728516, -0.594422, -0.163542, 0.298632, 0.333333, 1, 0.721993, -0.597723, -0.145708, 0.31658, 0.366667, 1, 0.718225, -0.595066, -0.121844, 0.339416, 0.4, 1, 0.716432, -0.591884, -0.106642, 0.35359, 0.433333, 1, 0.716041, -0.590837, -0.102598, 0.35731, 0.466667, 1, 0.71647, -0.592296, -0.107895, 0.352442, 0.5, 1, 0.717217, -0.594426, -0.11643, 0.344531, 0.533333, 1, 0.718118, -0.595889, -0.12403, 0.3374, 0.566667, 1, 0.718673, -0.59659, -0.128307, 0.333356, 0.583333, 1, 0.718897, -0.596817, -0.129892, 0.33185)
tracks/47/type = "scale_3d"
tracks/47/imported = true
tracks/47/enabled = true
tracks/47/path = NodePath("rig/Skeleton3D:DEF-lid2.T.L")
tracks/47/interp = 1
tracks/47/loop_wrap = true
tracks/47/keys = PackedFloat32Array(0, 1, 0.103092, 0.0940918, 0.103092, 0.0333333, 1, 0.136716, 0.120113, 0.136716, 0.0666667, 1, 0.214581, 0.173467, 0.214581, 0.1, 1, 0.330518, 0.243252, 0.330518, 0.133333, 1, 0.475544, 0.322647, 0.475544, 0.166667, 1, 0.637867, 0.40891, 0.637867, 0.2, 1, 0.792446, 0.505561, 0.792446, 0.233333, 1, 0.921691, 0.606706, 0.921691, 0.266667, 1, 1.0133, 0.711027, 1.0133, 0.3, 1, 1.0559, 0.817668, 1.0559, 0.333333, 1, 1.03905, 0.92624, 1.03905, 0.366667, 1, 0.982519, 1.03834, 0.982519, 0.4, 1, 0.948918, 1.11149, 0.948918, 0.433333, 1, 0.940315, 1.13109, 0.940315, 0.466667, 1, 0.951428, 1.10501, 0.951428, 0.5, 1, 0.969636, 1.06361, 0.969636, 0.533333, 1, 0.986589, 1.0276, 0.986589, 0.566667, 1, 0.996331, 1.00744, 0.996331, 0.583333, 1, 1, 1, 1)
tracks/48/type = "position_3d"
tracks/48/imported = true
tracks/48/enabled = true
tracks/48/path = NodePath("rig/Skeleton3D:DEF-lid3.T.L")
tracks/48/interp = 1
tracks/48/loop_wrap = true
tracks/48/keys = PackedFloat32Array(0, 1, 0.0161884, 0.169832, 0.0410481, 0.0333333, 1, 0.0206538, 0.221191, 0.0523706, 0.0666667, 1, 0.0297795, 0.333209, 0.0755103, 0.1, 1, 0.0416469, 0.488894, 0.105602, 0.133333, 1, 0.0550673, 0.66534, 0.139631, 0.166667, 1, 0.0695827, 0.833728, 0.176437, 0.2, 1, 0.0860032, 0.921906, 0.218073, 0.233333, 1, 0.103394, 0.931467, 0.262169, 0.266667, 1, 0.121523, 0.869907, 0.308139, 0.3, 1, 0.140207, 0.753108, 0.355514, 0.333333, 1, 0.159306, 0.60534, 0.403943, 0.366667, 1, 0.178892, 0.484914, 0.453606, 0.4, 1, 0.191628, 0.419786, 0.485899, 0.433333, 1, 0.195034, 0.404032, 0.494536, 0.466667, 1, 0.190505, 0.423774, 0.483052, 0.5, 1, 0.18331, 0.457185, 0.464809, 0.533333, 1, 0.177039, 0.490595, 0.448906, 0.566667, 1, 0.173523, 0.510337, 0.439993, 0.583333, 1, 0.172225, 0.517931, 0.436701)
tracks/49/type = "rotation_3d"
tracks/49/imported = true
tracks/49/enabled = true
tracks/49/path = NodePath("rig/Skeleton3D:DEF-lid3.T.L")
tracks/49/interp = 1
tracks/49/loop_wrap = true
tracks/49/keys = PackedFloat32Array(0, 1, 0.899435, -0.407103, 0.0240036, 0.157189, 0.0333333, 1, 0.907295, -0.392685, 0.0191768, 0.149153, 0.0666667, 1, 0.922458, -0.362168, 0.009859, 0.133449, 0.1, 1, 0.939807, -0.32203, -0.00148115, 0.114267, 0.133333, 1, 0.954499, -0.281956, -0.0120983, 0.0963596, 0.166667, 1, 0.962994, -0.255028, -0.0190114, 0.0850965, 0.2, 1, 0.963579, -0.253041, -0.0195245, 0.0842879, 0.233333, 1, 0.959778, -0.265618, -0.0162981, 0.0894829, 0.266667, 1, 0.950572, -0.293343, -0.00911948, 0.101387, 0.3, 1, 0.93359, -0.337109, 0.00280856, 0.121488, 0.333333, 1, 0.904589, -0.397757, 0.0208228, 0.151905, 0.366667, 1, 0.867351, -0.457569, 0.0447706, 0.1906, 0.4, 1, 0.843754, -0.488546, 0.0598021, 0.21407, 0.433333, 1, 0.837576, -0.495906, 0.0637735, 0.220174, 0.466667, 1, 0.845331, -0.486774, 0.0585734, 0.212215, 0.5, 1, 0.857949, -0.470885, 0.050145, 0.199187, 0.533333, 1, 0.86978, -0.454525, 0.0425885, 0.187286, 0.566667, 1, 0.876445, -0.444745, 0.0383176, 0.180492, 0.583333, 1, 0.87894, -0.440964, 0.0367312, 0.177951)
tracks/50/type = "scale_3d"
tracks/50/imported = true
tracks/50/enabled = true
tracks/50/path = NodePath("rig/Skeleton3D:DEF-lid3.T.L")
tracks/50/interp = 1
tracks/50/loop_wrap = true
tracks/50/keys = PackedFloat32Array(0, 1, 0.0999299, 0.10014, 0.0999299, 0.0333333, 1, 0.130479, 0.13186, 0.130479, 0.0666667, 1, 0.196724, 0.206264, 0.196724, 0.1, 1, 0.287086, 0.322296, 0.287086, 0.133333, 1, 0.392195, 0.474428, 0.392195, 0.166667, 1, 0.504893, 0.652663, 0.504893, 0.2, 1, 0.624918, 0.812955, 0.624918, 0.233333, 1, 0.743981, 0.931259, 0.743981, 0.266667, 1, 0.854271, 1.00035, 0.854271, 0.3, 1, 0.944085, 1.02147, 0.944085, 0.333333, 1, 0.997824, 1.00437, 0.997824, 0.366667, 1, 0.993971, 1.01218, 0.993971, 0.4, 1, 0.984381, 1.03214, 0.984381, 0.433333, 1, 0.980865, 1.03942, 0.980865, 0.466667, 1, 0.986059, 1.02852, 0.986059, 0.5, 1, 0.993423, 1.01328, 0.993423, 0.533333, 1, 0.99768, 1.00467, 0.99768, 0.566667, 1, 0.999498, 1.00101, 0.999498, 0.583333, 1, 1, 0.999998, 1)
tracks/51/type = "position_3d"
tracks/51/imported = true
tracks/51/enabled = true
tracks/51/path = NodePath("rig/Skeleton3D:DEF-lid4.T.L")
tracks/51/interp = 1
tracks/51/loop_wrap = true
tracks/51/keys = PackedFloat32Array(0, 1, 0.008142, 0.163099, 0.0439107, 0.0333333, 1, 0.0103879, 0.211901, 0.0560228, 0.0666667, 1, 0.0149777, 0.317278, 0.0807762, 0.1, 1, 0.0209464, 0.461739, 0.112966, 0.133333, 1, 0.0276962, 0.622823, 0.149368, 0.166667, 1, 0.0349967, 0.773094, 0.188741, 0.2, 1, 0.0432555, 0.846168, 0.233282, 0.233333, 1, 0.052002, 0.846334, 0.280452, 0.266667, 1, 0.0611203, 0.782392, 0.329628, 0.3, 1, 0.0705172, 0.670828, 0.380307, 0.333333, 1, 0.0801233, 0.535811, 0.432113, 0.366667, 1, 0.0899741, 0.429218, 0.485239, 0.4, 1, 0.0963795, 0.37157, 0.519785, 0.433333, 1, 0.0980927, 0.357625, 0.529024, 0.466667, 1, 0.0958149, 0.3751, 0.516739, 0.5, 1, 0.0921962, 0.404673, 0.497223, 0.533333, 1, 0.0890419, 0.434246, 0.480212, 0.566667, 1, 0.0872739, 0.451721, 0.470677, 0.583333, 1, 0.086621, 0.458442, 0.467156)
tracks/52/type = "rotation_3d"
tracks/52/imported = true
tracks/52/enabled = true
tracks/52/path = NodePath("rig/Skeleton3D:DEF-lid4.T.L")
tracks/52/interp = 1
tracks/52/loop_wrap = true
tracks/52/keys = PackedFloat32Array(0, 1, 0.969761, -0.204383, 0.111812, 0.0727294, 0.0333333, 1, 0.973261, -0.192139, 0.106479, 0.0671363, 0.0666667, 1, 0.979195, -0.169278, 0.0961854, 0.0571873, 0.1, 1, 0.985148, -0.14268, 0.0836425, 0.0461433, 0.133333, 1, 0.989597, -0.119063, 0.0718827, 0.0368093, 0.166667, 1, 0.991902, -0.104934, 0.0642373, 0.0315143, 0.2, 1, 0.992055, -0.103938, 0.0636711, 0.0311497, 0.233333, 1, 0.991044, -0.110391, 0.0672381, 0.0335417, 0.266667, 1, 0.988441, -0.125623, 0.0751867, 0.0393774, 0.3, 1, 0.983024, -0.1527, 0.0883962, 0.0503279, 0.333333, 1, 0.972105, -0.196275, 0.108297, 0.0689959, 0.366667, 1, 0.951017, -0.259394, 0.134777, 0.100578, 0.4, 1, 0.933962, -0.299665, 0.151257, 0.12263, 0.433333, 1, 0.928921, -0.310375, 0.155579, 0.128715, 0.466667, 1, 0.935545, -0.296263, 0.149901, 0.120472, 0.5, 1, 0.945464, -0.273404, 0.140647, 0.107543, 0.533333, 1, 0.953432, -0.25315, 0.132333, 0.0967986, 0.566667, 1, 0.957604, -0.241754, 0.12762, 0.0908959, 0.583333, 1, 0.95909, -0.237543, 0.125869, 0.0887552)
tracks/53/type = "scale_3d"
tracks/53/imported = true
tracks/53/enabled = true
tracks/53/path = NodePath("rig/Skeleton3D:DEF-lid4.T.L")
tracks/53/interp = 1
tracks/53/loop_wrap = true
tracks/53/keys = PackedFloat32Array(0, 1, 0.0958378, 0.108874, 0.0958378, 0.0333333, 1, 0.123066, 0.148254, 0.123066, 0.0666667, 1, 0.179429, 0.24842, 0.179429, 0.1, 1, 0.253333, 0.415344, 0.253333, 0.133333, 1, 0.337224, 0.64233, 0.337224, 0.166667, 1, 0.427584, 0.910009, 0.427584, 0.2, 1, 0.528618, 1.13614, 0.528618, 0.233333, 1, 0.634386, 1.28142, 0.634386, 0.266667, 1, 0.742238, 1.32837, 0.742238, 0.3, 1, 0.848214, 1.27101, 0.848214, 0.333333, 1, 0.947037, 1.11498, 0.947037, 0.366667, 1, 1.02025, 0.964711, 1.02025, 0.4, 1, 1.05818, 0.893687, 1.05818, 0.433333, 1, 1.06723, 0.878033, 1.06723, 0.466667, 1, 1.05637, 0.896346, 1.05637, 0.5, 1, 1.03713, 0.929682, 1.03713, 0.533333, 1, 1.01679, 0.967532, 1.01679, 0.566667, 1, 1.00468, 0.990798, 1.00468, 0.583333, 1, 1, 1, 1)
tracks/54/type = "position_3d"
tracks/54/imported = true
tracks/54/enabled = true
tracks/54/path = NodePath("rig/Skeleton3D:DEF-lid2.T.R")
tracks/54/interp = 1
tracks/54/loop_wrap = true
tracks/54/keys = PackedFloat32Array(0, 1, -0.0234474, 0.170595, 0.035103, 0.0333333, 1, -0.029915, 0.222243, 0.0447856, 0.0666667, 1, -0.0431329, 0.335014, 0.0645739, 0.1, 1, -0.0603216, 0.491971, 0.0903071, 0.133333, 1, -0.0797598, 0.670157, 0.119408, 0.166667, 1, -0.100784, 0.840598, 0.150883, 0.2, 1, -0.124568, 0.930487, 0.186489, 0.233333, 1, -0.149756, 0.941113, 0.224199, 0.266667, 1, -0.176015, 0.879822, 0.26351, 0.3, 1, -0.203076, 0.76243, 0.304024, 0.333333, 1, -0.23074, 0.613217, 0.345439, 0.366667, 1, -0.259108, 0.491225, 0.387909, 0.4, 1, -0.277555, 0.425249, 0.415525, 0.433333, 1, -0.282488, 0.40929, 0.422911, 0.466667, 1, -0.275928, 0.429289, 0.41309, 0.5, 1, -0.265507, 0.463134, 0.397489, 0.533333, 1, -0.256424, 0.49698, 0.38389, 0.566667, 1, -0.251332, 0.516979, 0.376268, 0.583333, 1, -0.249452, 0.524671, 0.373452)
tracks/55/type = "rotation_3d"
tracks/55/imported = true
tracks/55/enabled = true
tracks/55/path = NodePath("rig/Skeleton3D:DEF-lid2.T.R")
tracks/55/interp = 1
tracks/55/loop_wrap = true
tracks/55/keys = PackedFloat32Array(0, 1, 0.721246, 0.597745, 0.142555, 0.319662, 0.0333333, 1, 0.722436, 0.597636, 0.147339, 0.314974, 0.0666667, 1, 0.725488, 0.596376, 0.156563, 0.305764, 0.133333, 1, 0.736321, 0.588109, 0.178274, 0.283155, 0.166667, 1, 0.741413, 0.58315, 0.185114, 0.275637, 0.2, 1, 0.741844, 0.58271, 0.185621, 0.275067, 0.233333, 1, 0.739329, 0.585225, 0.18243, 0.27861, 0.266667, 1, 0.734604, 0.58962, 0.175331, 0.286295, 0.3, 1, 0.728516, 0.594422, 0.163542, 0.298631, 0.333333, 1, 0.721993, 0.597723, 0.145708, 0.31658, 0.366667, 1, 0.718225, 0.595066, 0.121843, 0.339416, 0.4, 1, 0.716432, 0.591884, 0.106642, 0.35359, 0.433333, 1, 0.716041, 0.590837, 0.102598, 0.35731, 0.466667, 1, 0.71647, 0.592296, 0.107894, 0.352442, 0.5, 1, 0.717217, 0.594427, 0.11643, 0.34453, 0.533333, 1, 0.718118, 0.595889, 0.12403, 0.3374, 0.566667, 1, 0.718673, 0.59659, 0.128307, 0.333356, 0.583333, 1, 0.718897, 0.596817, 0.129892, 0.331851)
tracks/56/type = "scale_3d"
tracks/56/imported = true
tracks/56/enabled = true
tracks/56/path = NodePath("rig/Skeleton3D:DEF-lid2.T.R")
tracks/56/interp = 1
tracks/56/loop_wrap = true
tracks/56/keys = PackedFloat32Array(0, 1, 0.103092, 0.0940919, 0.103092, 0.0333333, 1, 0.136717, 0.120112, 0.136717, 0.0666667, 1, 0.214581, 0.173467, 0.214581, 0.1, 1, 0.330518, 0.243252, 0.330518, 0.133333, 1, 0.475544, 0.322646, 0.475545, 0.166667, 1, 0.637867, 0.40891, 0.637867, 0.2, 1, 0.792446, 0.50556, 0.792446, 0.233333, 1, 0.921691, 0.606706, 0.921691, 0.266667, 1, 1.0133, 0.711028, 1.0133, 0.3, 1, 1.0559, 0.81767, 1.0559, 0.333333, 1, 1.03905, 0.92624, 1.03905, 0.366667, 1, 0.982519, 1.03833, 0.982519, 0.4, 1, 0.948919, 1.11149, 0.948919, 0.433333, 1, 0.940315, 1.13109, 0.940315, 0.466667, 1, 0.951428, 1.10501, 0.951428, 0.5, 1, 0.969636, 1.06361, 0.969636, 0.533333, 1, 0.986589, 1.0276, 0.986589, 0.566667, 1, 0.996331, 1.00744, 0.996331, 0.583333, 1, 1, 0.999999, 1)
tracks/57/type = "position_3d"
tracks/57/imported = true
tracks/57/enabled = true
tracks/57/path = NodePath("rig/Skeleton3D:DEF-lid3.T.R")
tracks/57/interp = 1
tracks/57/loop_wrap = true
tracks/57/keys = PackedFloat32Array(0, 1, -0.0161885, 0.169832, 0.0410481, 0.0333333, 1, -0.0206538, 0.221191, 0.0523706, 0.0666667, 1, -0.0297795, 0.333209, 0.0755103, 0.1, 1, -0.0416469, 0.488894, 0.105602, 0.133333, 1, -0.0550673, 0.66534, 0.139631, 0.166667, 1, -0.0695826, 0.833728, 0.176437, 0.2, 1, -0.0860033, 0.921906, 0.218073, 0.233333, 1, -0.103394, 0.931467, 0.262169, 0.266667, 1, -0.121523, 0.869907, 0.308139, 0.3, 1, -0.140207, 0.753108, 0.355514, 0.333333, 1, -0.159306, 0.60534, 0.403943, 0.366667, 1, -0.178892, 0.484914, 0.453606, 0.4, 1, -0.191628, 0.419786, 0.485899, 0.433333, 1, -0.195034, 0.404032, 0.494536, 0.466667, 1, -0.190505, 0.423774, 0.483052, 0.5, 1, -0.18331, 0.457185, 0.464809, 0.533333, 1, -0.177039, 0.490595, 0.448906, 0.566667, 1, -0.173523, 0.510338, 0.439993, 0.583333, 1, -0.172225, 0.517931, 0.436701)
tracks/58/type = "rotation_3d"
tracks/58/imported = true
tracks/58/enabled = true
tracks/58/path = NodePath("rig/Skeleton3D:DEF-lid3.T.R")
tracks/58/interp = 1
tracks/58/loop_wrap = true
tracks/58/keys = PackedFloat32Array(0, 1, 0.899434, 0.407105, -0.0240041, 0.157189, 0.0333333, 1, 0.907295, 0.392684, -0.0191763, 0.149153, 0.0666667, 1, 0.922458, 0.362168, -0.00985936, 0.133449, 0.1, 1, 0.939807, 0.322031, 0.00148072, 0.114268, 0.133333, 1, 0.954499, 0.281956, 0.0120986, 0.0963607, 0.166667, 1, 0.962994, 0.255029, 0.0190111, 0.0850969, 0.2, 1, 0.963579, 0.253041, 0.0195242, 0.084288, 0.233333, 1, 0.959778, 0.265618, 0.0162978, 0.0894826, 0.266667, 1, 0.950572, 0.293343, 0.00911926, 0.101387, 0.3, 1, 0.93359, 0.337109, -0.00280854, 0.121488, 0.333333, 1, 0.904589, 0.397756, -0.020823, 0.151906, 0.366667, 1, 0.867351, 0.457569, -0.044771, 0.190601, 0.4, 1, 0.843754, 0.488546, -0.0598021, 0.21407, 0.433333, 1, 0.837576, 0.495905, -0.0637734, 0.220174, 0.466667, 1, 0.845331, 0.486774, -0.0585737, 0.212215, 0.5, 1, 0.857949, 0.470885, -0.0501453, 0.199186, 0.533333, 1, 0.86978, 0.454525, -0.0425881, 0.187287, 0.566667, 1, 0.876445, 0.444745, -0.0383175, 0.180492, 0.583333, 1, 0.87894, 0.440964, -0.0367313, 0.177951)
tracks/59/type = "scale_3d"
tracks/59/imported = true
tracks/59/enabled = true
tracks/59/path = NodePath("rig/Skeleton3D:DEF-lid3.T.R")
tracks/59/interp = 1
tracks/59/loop_wrap = true
tracks/59/keys = PackedFloat32Array(0, 1, 0.0999302, 0.10014, 0.0999302, 0.0333333, 1, 0.130479, 0.13186, 0.130479, 0.0666667, 1, 0.196724, 0.206265, 0.196724, 0.1, 1, 0.287086, 0.322296, 0.287086, 0.133333, 1, 0.392195, 0.474428, 0.392195, 0.166667, 1, 0.504894, 0.652662, 0.504894, 0.2, 1, 0.624918, 0.812955, 0.624918, 0.233333, 1, 0.74398, 0.931261, 0.74398, 0.266667, 1, 0.854271, 1.00035, 0.854271, 0.3, 1, 0.944084, 1.02148, 0.944085, 0.333333, 1, 0.997823, 1.00437, 0.997823, 0.366667, 1, 0.99397, 1.01218, 0.99397, 0.4, 1, 0.984381, 1.03214, 0.984381, 0.433333, 1, 0.980865, 1.03942, 0.980865, 0.466667, 1, 0.986059, 1.02852, 0.986058, 0.5, 1, 0.993422, 1.01329, 0.993422, 0.533333, 1, 0.99768, 1.00467, 0.99768, 0.566667, 1, 0.999497, 1.00101, 0.999497, 0.583333, 1, 1, 1, 1)
tracks/60/type = "position_3d"
tracks/60/imported = true
tracks/60/enabled = true
tracks/60/path = NodePath("rig/Skeleton3D:DEF-lid4.T.R")
tracks/60/interp = 1
tracks/60/loop_wrap = true
tracks/60/keys = PackedFloat32Array(0, 1, -0.00814202, 0.163099, 0.0439107, 0.0333333, 1, -0.0103879, 0.211901, 0.0560228, 0.0666667, 1, -0.0149777, 0.317278, 0.0807762, 0.1, 1, -0.0209464, 0.461739, 0.112966, 0.133333, 1, -0.0276962, 0.622823, 0.149369, 0.166667, 1, -0.0349967, 0.773095, 0.188741, 0.2, 1, -0.0432555, 0.846168, 0.233281, 0.233333, 1, -0.052002, 0.846334, 0.280452, 0.266667, 1, -0.0611202, 0.782392, 0.329628, 0.3, 1, -0.0705172, 0.670828, 0.380307, 0.333333, 1, -0.0801233, 0.535811, 0.432113, 0.366667, 1, -0.089974, 0.429218, 0.485239, 0.4, 1, -0.0963796, 0.37157, 0.519785, 0.433333, 1, -0.0980927, 0.357625, 0.529024, 0.466667, 1, -0.0958148, 0.3751, 0.516739, 0.5, 1, -0.0921961, 0.404673, 0.497223, 0.533333, 1, -0.0890419, 0.434246, 0.480212, 0.566667, 1, -0.0872739, 0.451721, 0.470677, 0.583333, 1, -0.0866209, 0.458442, 0.467156)
tracks/61/type = "rotation_3d"
tracks/61/imported = true
tracks/61/enabled = true
tracks/61/path = NodePath("rig/Skeleton3D:DEF-lid4.T.R")
tracks/61/interp = 1
tracks/61/loop_wrap = true
tracks/61/keys = PackedFloat32Array(0, 1, 0.969761, 0.204383, -0.111813, 0.0727301, 0.0333333, 1, 0.973261, 0.192139, -0.106478, 0.0671363, 0.0666667, 1, 0.979195, 0.169277, -0.0961857, 0.0571876, 0.1, 1, 0.985148, 0.14268, -0.0836429, 0.0461433, 0.133333, 1, 0.989597, 0.119063, -0.0718823, 0.0368085, 0.166667, 1, 0.991902, 0.104933, -0.0642375, 0.0315144, 0.2, 1, 0.992055, 0.103939, -0.0636713, 0.0311501, 0.233333, 1, 0.991044, 0.110391, -0.0672384, 0.0335421, 0.266667, 1, 0.988441, 0.125623, -0.0751868, 0.0393779, 0.3, 1, 0.983024, 0.1527, -0.0883962, 0.0503283, 0.333333, 1, 0.972105, 0.196275, -0.108297, 0.0689954, 0.366667, 1, 0.951017, 0.259394, -0.134777, 0.100579, 0.4, 1, 0.933961, 0.299666, -0.151257, 0.12263, 0.433333, 1, 0.928921, 0.310375, -0.15558, 0.128714, 0.466667, 1, 0.935545, 0.296263, -0.149901, 0.120472, 0.5, 1, 0.945465, 0.273403, -0.140647, 0.107544, 0.533333, 1, 0.953432, 0.25315, -0.132333, 0.096798, 0.566667, 1, 0.957604, 0.241755, -0.12762, 0.0908957, 0.583333, 1, 0.95909, 0.237543, -0.125869, 0.0887554)
tracks/62/type = "scale_3d"
tracks/62/imported = true
tracks/62/enabled = true
tracks/62/path = NodePath("rig/Skeleton3D:DEF-lid4.T.R")
tracks/62/interp = 1
tracks/62/loop_wrap = true
tracks/62/keys = PackedFloat32Array(0, 1, 0.0958376, 0.108875, 0.0958376, 0.0333333, 1, 0.123066, 0.148253, 0.123066, 0.0666667, 1, 0.179429, 0.248419, 0.179429, 0.1, 1, 0.253334, 0.415343, 0.253334, 0.133333, 1, 0.337224, 0.642329, 0.337224, 0.166667, 1, 0.427583, 0.91001, 0.427583, 0.2, 1, 0.528618, 1.13613, 0.528618, 0.233333, 1, 0.634386, 1.28142, 0.634386, 0.266667, 1, 0.742239, 1.32836, 0.742239, 0.3, 1, 0.848215, 1.27101, 0.848215, 0.333333, 1, 0.947039, 1.11497, 0.947038, 0.366667, 1, 1.02025, 0.96471, 1.02025, 0.4, 1, 1.05818, 0.893685, 1.05818, 0.433333, 1, 1.06723, 0.878031, 1.06723, 0.466667, 1, 1.05637, 0.896343, 1.05637, 0.5, 1, 1.03713, 0.929681, 1.03713, 0.533333, 1, 1.01679, 0.967531, 1.01679, 0.566667, 1, 1.00468, 0.990797, 1.00468, 0.583333, 1, 1, 0.999999, 1)
tracks/63/type = "position_3d"
tracks/63/imported = true
tracks/63/enabled = true
tracks/63/path = NodePath("rig/Skeleton3D:DEF-lip1.B.L")
tracks/63/interp = 1
tracks/63/loop_wrap = true
tracks/63/keys = PackedFloat32Array(0, 1, 0.0121474, 0.121803, 0.0394069, 0.0333333, 1, 0.0154981, 0.154929, 0.0502766, 0.0666667, 1, 0.0223459, 0.219571, 0.0724911, 0.1, 1, 0.0312508, 0.295196, 0.101379, 0.133333, 1, 0.0413212, 0.36206, 0.134048, 0.166667, 1, 0.0522132, 0.401217, 0.169382, 0.2, 1, 0.0645348, 0.381654, 0.209354, 0.233333, 1, 0.0775842, 0.324197, 0.251687, 0.266667, 1, 0.091188, 0.245649, 0.295818, 0.3, 1, 0.105208, 0.16619, 0.341299, 0.333333, 1, 0.11954, 0.10938, 0.387792, 0.366667, 1, 0.134236, 0.08762, 0.435469, 0.4, 1, 0.143793, 0.0758519, 0.466471, 0.433333, 1, 0.146349, 0.0730053, 0.474762, 0.466667, 1, 0.14295, 0.0765726, 0.463738, 0.5, 1, 0.137552, 0.0826095, 0.446224, 0.533333, 1, 0.132846, 0.0886465, 0.430957, 0.566667, 1, 0.130208, 0.0922138, 0.4224, 0.583333, 1, 0.129234, 0.0935858, 0.41924)
tracks/64/type = "rotation_3d"
tracks/64/imported = true
tracks/64/enabled = true
tracks/64/path = NodePath("rig/Skeleton3D:DEF-lip1.B.L")
tracks/64/interp = 1
tracks/64/loop_wrap = true
tracks/64/keys = PackedFloat32Array(0, 1, -0.445921, -0.2224, -0.403187, 0.76755, 0.0333333, 1, -0.440013, -0.22441, -0.397522, 0.773308, 0.0666667, 1, -0.426418, -0.228919, -0.384526, 0.786069, 0.1, 1, -0.406375, -0.23528, -0.365442, 0.803714, 0.133333, 1, -0.38391, -0.242014, -0.344134, 0.821957, 0.166667, 1, -0.366249, -0.247068, -0.327455, 0.83522, 0.2, 1, -0.364831, -0.247466, -0.326119, 0.836245, 0.233333, 1, -0.373394, -0.24505, -0.334197, 0.829964, 0.266667, 1, -0.390442, -0.2401, -0.350325, 0.81681, 0.3, 1, -0.414084, -0.232873, -0.372776, 0.797084, 0.333333, 1, -0.442136, -0.223691, -0.399556, 0.771254, 0.366667, 1, -0.464783, -0.215766, -0.421356, 0.748252, 0.4, 1, -0.476229, -0.211542, -0.432416, 0.735849, 0.433333, 1, -0.478888, -0.210539, -0.434992, 0.732885, 0.466667, 1, -0.475607, -0.211774, -0.431812, 0.736538, 0.5, 1, -0.469917, -0.213883, -0.426303, 0.742763, 0.533333, 1, -0.463966, -0.216052, -0.420555, 0.749127, 0.566667, 1, -0.460377, -0.217341, -0.417093, 0.752894, 0.583333, 1, -0.458978, -0.21784, -0.415744, 0.754348)
tracks/65/type = "scale_3d"
tracks/65/imported = true
tracks/65/enabled = true
tracks/65/path = NodePath("rig/Skeleton3D:DEF-lip1.B.L")
tracks/65/interp = 1
tracks/65/loop_wrap = true
tracks/65/keys = PackedFloat32Array(0, 1, 0.102524, 0.095137, 0.102524, 0.0333333, 1, 0.135557, 0.122171, 0.135557, 0.0666667, 1, 0.210937, 0.179441, 0.210937, 0.1, 1, 0.320527, 0.258405, 0.320527, 0.133333, 1, 0.454633, 0.35289, 0.454633, 0.2, 1, 0.746929, 0.569053, 0.746929, 0.233333, 1, 0.875371, 0.672493, 0.875371, 0.266667, 1, 0.974756, 0.767787, 0.974756, 0.3, 1, 1.0316, 0.855791, 1.0316, 0.333333, 1, 1.03151, 0.939846, 1.03151, 0.366667, 1, 0.984156, 1.03417, 0.984156, 0.4, 1, 0.954421, 1.09856, 0.954421, 0.433333, 1, 0.946564, 1.11618, 0.946564, 0.466667, 1, 0.956829, 1.09251, 0.956829, 0.5, 1, 0.973444, 1.05531, 0.973444, 0.533333, 1, 0.988399, 1.02379, 0.988399, 0.566667, 1, 0.996855, 1.00636, 0.996855, 0.583333, 1, 1, 1, 1)
tracks/66/type = "position_3d"
tracks/66/imported = true
tracks/66/enabled = true
tracks/66/path = NodePath("rig/Skeleton3D:DEF-lip2.B.L")
tracks/66/interp = 1
tracks/66/loop_wrap = true
tracks/66/keys = PackedFloat32Array(0, 1, 0.0229525, 0.125468, 0.0327278, 0.0333333, 1, 0.0292836, 0.159985, 0.0417552, 0.0666667, 1, 0.0422224, 0.228242, 0.0602046, 0.1, 1, 0.0590483, 0.309975, 0.0841965, 0.133333, 1, 0.0780761, 0.385201, 0.111328, 0.166667, 1, 0.0986564, 0.434218, 0.140674, 0.2, 1, 0.121938, 0.422876, 0.173871, 0.233333, 1, 0.146595, 0.370532, 0.209028, 0.266667, 1, 0.172299, 0.29328, 0.24568, 0.3, 1, 0.198789, 0.210973, 0.283452, 0.333333, 1, 0.225869, 0.147222, 0.322065, 0.366667, 1, 0.253639, 0.117934, 0.361661, 0.4, 1, 0.271696, 0.102094, 0.387409, 0.433333, 1, 0.276525, 0.0982628, 0.394295, 0.466667, 1, 0.270104, 0.103064, 0.385139, 0.5, 1, 0.259903, 0.11119, 0.370593, 0.533333, 1, 0.251011, 0.119315, 0.357914, 0.566667, 1, 0.246027, 0.124117, 0.350808, 0.583333, 1, 0.244186, 0.125964, 0.348183)
tracks/67/type = "rotation_3d"
tracks/67/imported = true
tracks/67/enabled = true
tracks/67/path = NodePath("rig/Skeleton3D:DEF-lip2.B.L")
tracks/67/interp = 1
tracks/67/loop_wrap = true
tracks/67/keys = PackedFloat32Array(0, 1, -0.329561, -0.176193, -0.283699, 0.883097, 0.0333333, 1, -0.317485, -0.178805, -0.272629, 0.890453, 0.0666667, 1, -0.292245, -0.184218, -0.249641, 0.904619, 0.1, 1, -0.259391, -0.191132, -0.219937, 0.920767, 0.133333, 1, -0.226865, -0.197834, -0.190741, 0.934351, 0.166667, 1, -0.205159, -0.202415, -0.17142, 0.9421, 0.2, 1, -0.203559, -0.202761, -0.170003, 0.94263, 0.233333, 1, -0.213686, -0.200613, -0.179, 0.939176, 0.266667, 1, -0.236094, -0.195964, -0.199013, 0.930727, 0.3, 1, -0.271738, -0.188573, -0.231091, 0.914984, 0.333333, 1, -0.321713, -0.17789, -0.276498, 0.887922, 0.366667, 1, -0.373224, -0.166511, -0.324166, 0.853167, 0.4, 1, -0.400925, -0.159887, -0.350036, 0.831366, 0.433333, 1, -0.407668, -0.158226, -0.356369, 0.825696, 0.466667, 1, -0.399196, -0.160295, -0.348396, 0.832808, 0.5, 1, -0.384716, -0.163744, -0.334812, 0.844442, 0.533333, 1, -0.370249, -0.167117, -0.321323, 0.855417, 0.566667, 1, -0.361696, -0.169066, -0.31337, 0.861621, 0.583333, 1, -0.358415, -0.169806, -0.310324, 0.863947)
tracks/68/type = "scale_3d"
tracks/68/imported = true
tracks/68/enabled = true
tracks/68/path = NodePath("rig/Skeleton3D:DEF-lip2.B.L")
tracks/68/interp = 1
tracks/68/loop_wrap = true
tracks/68/keys = PackedFloat32Array(0, 1, 0.0999007, 0.100199, 0.0999007, 0.0333333, 1, 0.130423, 0.131973, 0.130423, 0.0666667, 1, 0.196581, 0.206566, 0.196581, 0.1, 1, 0.286782, 0.322988, 0.286782, 0.133333, 1, 0.391669, 0.475707, 0.391669, 0.166667, 1, 0.504125, 0.654652, 0.504125, 0.2, 1, 0.623958, 0.815459, 0.623958, 0.233333, 1, 0.74291, 0.933951, 0.74291, 0.266667, 1, 0.853225, 1.00282, 0.853225, 0.3, 1, 0.943251, 1.0233, 0.943251, 0.333333, 1, 0.997449, 1.00512, 0.997449, 0.366667, 1, 0.994107, 1.0119, 0.994106, 0.4, 1, 0.984775, 1.03131, 0.984775, 0.433333, 1, 0.98132, 1.03846, 0.98132, 0.466667, 1, 0.986441, 1.02772, 0.986441, 0.5, 1, 0.993677, 1.01277, 0.993677, 0.533333, 1, 0.997796, 1.00444, 0.997796, 0.566667, 1, 0.99953, 1.00094, 0.99953, 0.583333, 1, 1, 1, 1)
tracks/69/type = "position_3d"
tracks/69/imported = true
tracks/69/enabled = true
tracks/69/path = NodePath("rig/Skeleton3D:DEF-lip1.B.R")
tracks/69/interp = 1
tracks/69/loop_wrap = true
tracks/69/keys = PackedFloat32Array(0, 1, -0.0121474, 0.121803, 0.0394069, 0.0333333, 1, -0.0154981, 0.154929, 0.0502766, 0.0666667, 1, -0.0223459, 0.219571, 0.0724911, 0.1, 1, -0.0312508, 0.295196, 0.101379, 0.133333, 1, -0.0413212, 0.36206, 0.134048, 0.166667, 1, -0.0522132, 0.401217, 0.169382, 0.2, 1, -0.0645348, 0.381654, 0.209354, 0.233333, 1, -0.0775841, 0.324197, 0.251687, 0.266667, 1, -0.091188, 0.245649, 0.295818, 0.3, 1, -0.105208, 0.16619, 0.341299, 0.333333, 1, -0.11954, 0.10938, 0.387792, 0.366667, 1, -0.134236, 0.08762, 0.435469, 0.4, 1, -0.143793, 0.0758519, 0.466471, 0.433333, 1, -0.146349, 0.0730052, 0.474762, 0.466667, 1, -0.14295, 0.0765726, 0.463738, 0.5, 1, -0.137552, 0.0826095, 0.446224, 0.533333, 1, -0.132846, 0.0886465, 0.430957, 0.566667, 1, -0.130208, 0.0922138, 0.4224, 0.583333, 1, -0.129234, 0.0935858, 0.41924)
tracks/70/type = "rotation_3d"
tracks/70/imported = true
tracks/70/enabled = true
tracks/70/path = NodePath("rig/Skeleton3D:DEF-lip1.B.R")
tracks/70/interp = 1
tracks/70/loop_wrap = true
tracks/70/keys = PackedFloat32Array(0, 1, -0.445921, 0.2224, 0.403188, 0.76755, 0.0333333, 1, -0.440013, 0.22441, 0.397522, 0.773308, 0.0666667, 1, -0.426418, 0.228919, 0.384526, 0.786068, 0.1, 1, -0.406375, 0.23528, 0.365442, 0.803713, 0.133333, 1, -0.38391, 0.242014, 0.344134, 0.821957, 0.166667, 1, -0.366249, 0.247068, 0.327455, 0.83522, 0.2, 1, -0.364832, 0.247466, 0.326119, 0.836244, 0.233333, 1, -0.373394, 0.24505, 0.334197, 0.829964, 0.266667, 1, -0.390443, 0.2401, 0.350325, 0.81681, 0.3, 1, -0.414085, 0.232873, 0.372776, 0.797084, 0.333333, 1, -0.442136, 0.223691, 0.399556, 0.771254, 0.366667, 1, -0.464783, 0.215766, 0.421356, 0.748252, 0.4, 1, -0.476229, 0.211542, 0.432416, 0.735849, 0.433333, 1, -0.478888, 0.210539, 0.434992, 0.732885, 0.466667, 1, -0.475607, 0.211774, 0.431812, 0.736538, 0.5, 1, -0.469917, 0.213883, 0.426303, 0.742763, 0.533333, 1, -0.463966, 0.216053, 0.420555, 0.749126, 0.566667, 1, -0.460377, 0.217342, 0.417093, 0.752894, 0.583333, 1, -0.458978, 0.21784, 0.415744, 0.754348)
tracks/71/type = "scale_3d"
tracks/71/imported = true
tracks/71/enabled = true
tracks/71/path = NodePath("rig/Skeleton3D:DEF-lip1.B.R")
tracks/71/interp = 1
tracks/71/loop_wrap = true
tracks/71/keys = PackedFloat32Array(0, 1, 0.102524, 0.095137, 0.102524, 0.0333333, 1, 0.135557, 0.122171, 0.135557, 0.0666667, 1, 0.210937, 0.179441, 0.210937, 0.1, 1, 0.320527, 0.258405, 0.320527, 0.133333, 1, 0.454633, 0.35289, 0.454633, 0.2, 1, 0.746929, 0.569053, 0.746929, 0.233333, 1, 0.875371, 0.672493, 0.875371, 0.266667, 1, 0.974756, 0.767787, 0.974756, 0.3, 1, 1.0316, 0.855791, 1.0316, 0.333333, 1, 1.03151, 0.939846, 1.03151, 0.366667, 1, 0.984156, 1.03417, 0.984156, 0.4, 1, 0.954421, 1.09856, 0.954421, 0.433333, 1, 0.946564, 1.11618, 0.946564, 0.466667, 1, 0.956829, 1.09252, 0.956829, 0.5, 1, 0.973444, 1.05531, 0.973444, 0.533333, 1, 0.988399, 1.02379, 0.9884, 0.566667, 1, 0.996855, 1.00636, 0.996855, 0.583333, 1, 1, 1, 1)
tracks/72/type = "position_3d"
tracks/72/imported = true
tracks/72/enabled = true
tracks/72/path = NodePath("rig/Skeleton3D:DEF-lip2.B.R")
tracks/72/interp = 1
tracks/72/loop_wrap = true
tracks/72/keys = PackedFloat32Array(0, 1, -0.0229525, 0.125468, 0.0327278, 0.0333333, 1, -0.0292835, 0.159985, 0.0417552, 0.0666667, 1, -0.0422224, 0.228242, 0.0602046, 0.1, 1, -0.0590483, 0.309975, 0.0841965, 0.133333, 1, -0.0780761, 0.385201, 0.111328, 0.166667, 1, -0.0986564, 0.434218, 0.140674, 0.2, 1, -0.121938, 0.422876, 0.173871, 0.233333, 1, -0.146595, 0.370532, 0.209028, 0.266667, 1, -0.172299, 0.29328, 0.24568, 0.3, 1, -0.198789, 0.210972, 0.283452, 0.333333, 1, -0.225869, 0.147222, 0.322065, 0.366667, 1, -0.253638, 0.117934, 0.361661, 0.4, 1, -0.271696, 0.102094, 0.387409, 0.433333, 1, -0.276525, 0.0982628, 0.394295, 0.466667, 1, -0.270104, 0.103064, 0.385139, 0.5, 1, -0.259903, 0.11119, 0.370593, 0.533333, 1, -0.251011, 0.119315, 0.357915, 0.566667, 1, -0.246027, 0.124117, 0.350808, 0.583333, 1, -0.244186, 0.125964, 0.348183)
tracks/73/type = "rotation_3d"
tracks/73/imported = true
tracks/73/enabled = true
tracks/73/path = NodePath("rig/Skeleton3D:DEF-lip2.B.R")
tracks/73/interp = 1
tracks/73/loop_wrap = true
tracks/73/keys = PackedFloat32Array(0, 1, -0.329561, 0.176193, 0.283699, 0.883097, 0.0333333, 1, -0.317485, 0.178805, 0.272629, 0.890452, 0.0666667, 1, -0.292245, 0.184218, 0.249641, 0.90462, 0.1, 1, -0.259391, 0.191132, 0.219937, 0.920767, 0.133333, 1, -0.226865, 0.197835, 0.190741, 0.934351, 0.166667, 1, -0.205158, 0.202416, 0.17142, 0.9421, 0.2, 1, -0.203559, 0.202761, 0.170003, 0.94263, 0.233333, 1, -0.213686, 0.200613, 0.179, 0.939176, 0.266667, 1, -0.236093, 0.195964, 0.199013, 0.930727, 0.3, 1, -0.271737, 0.188573, 0.231091, 0.914984, 0.333333, 1, -0.321713, 0.17789, 0.276498, 0.887921, 0.366667, 1, -0.373223, 0.166512, 0.324166, 0.853167, 0.4, 1, -0.400925, 0.159887, 0.350037, 0.831366, 0.433333, 1, -0.407667, 0.158226, 0.356369, 0.825696, 0.466667, 1, -0.399196, 0.160295, 0.348396, 0.832808, 0.5, 1, -0.384717, 0.163744, 0.334813, 0.844442, 0.533333, 1, -0.370249, 0.167117, 0.321323, 0.855417, 0.566667, 1, -0.361696, 0.169066, 0.31337, 0.861621, 0.583333, 1, -0.358414, 0.169807, 0.310324, 0.863947)
tracks/74/type = "scale_3d"
tracks/74/imported = true
tracks/74/enabled = true
tracks/74/path = NodePath("rig/Skeleton3D:DEF-lip2.B.R")
tracks/74/interp = 1
tracks/74/loop_wrap = true
tracks/74/keys = PackedFloat32Array(0, 1, 0.0999007, 0.100199, 0.0999007, 0.0333333, 1, 0.130423, 0.131973, 0.130423, 0.0666667, 1, 0.196581, 0.206566, 0.196581, 0.1, 1, 0.286782, 0.322988, 0.286782, 0.133333, 1, 0.391669, 0.475707, 0.391669, 0.166667, 1, 0.504125, 0.654652, 0.504126, 0.2, 1, 0.623958, 0.815459, 0.623958, 0.233333, 1, 0.74291, 0.933951, 0.74291, 0.266667, 1, 0.853224, 1.00282, 0.853224, 0.3, 1, 0.943252, 1.0233, 0.943252, 0.333333, 1, 0.997449, 1.00512, 0.997449, 0.366667, 1, 0.994107, 1.0119, 0.994107, 0.4, 1, 0.984776, 1.03131, 0.984776, 0.433333, 1, 0.98132, 1.03846, 0.98132, 0.466667, 1, 0.986441, 1.02772, 0.986441, 0.5, 1, 0.993678, 1.01277, 0.993678, 0.533333, 1, 0.997796, 1.00444, 0.997796, 0.566667, 1, 0.99953, 1.00094, 0.99953, 0.583333, 1, 1, 1, 1)
tracks/75/type = "position_3d"
tracks/75/imported = true
tracks/75/enabled = true
tracks/75/path = NodePath("rig/Skeleton3D:DEF-lip1.T.L")
tracks/75/interp = 1
tracks/75/loop_wrap = true
tracks/75/keys = PackedFloat32Array(0, 1, 0.0119026, 0.139672, 0.0443487, 0.0333333, 1, 0.0151857, 0.179581, 0.0565816, 0.0666667, 1, 0.0218954, 0.261849, 0.0815819, 0.1, 1, 0.0306208, 0.36726, 0.114093, 0.133333, 1, 0.0404882, 0.474894, 0.150858, 0.166667, 1, 0.0511606, 0.56213, 0.190623, 0.2, 1, 0.0632338, 0.582651, 0.235608, 0.233333, 1, 0.0760201, 0.550128, 0.28325, 0.266667, 1, 0.0893497, 0.4779, 0.332915, 0.3, 1, 0.103087, 0.384549, 0.3841, 0.333333, 1, 0.11713, 0.293898, 0.436423, 0.366667, 1, 0.13153, 0.235431, 0.490079, 0.4, 1, 0.140894, 0.20381, 0.524969, 0.433333, 1, 0.143399, 0.196162, 0.5343, 0.466667, 1, 0.140069, 0.205747, 0.521893, 0.5, 1, 0.134779, 0.221968, 0.502183, 0.533333, 1, 0.130167, 0.238189, 0.485002, 0.566667, 1, 0.127583, 0.247774, 0.475372, 0.583333, 1, 0.126628, 0.251461, 0.471815)
tracks/76/type = "rotation_3d"
tracks/76/imported = true
tracks/76/enabled = true
tracks/76/path = NodePath("rig/Skeleton3D:DEF-lip1.T.L")
tracks/76/interp = 1
tracks/76/loop_wrap = true
tracks/76/keys = PackedFloat32Array(0, 1, -0.716888, -0.308828, -0.292239, 0.552534, 0.0333333, 1, -0.72086, -0.305262, -0.294984, 0.547869, 0.0666667, 1, -0.729812, -0.297046, -0.301306, 0.536986, 0.1, 1, -0.742524, -0.284886, -0.310596, 0.520604, 0.133333, 1, -0.756062, -0.271198, -0.32088, 0.501855, 0.166667, 1, -0.766278, -0.260292, -0.329034, 0.486624, 0.2, 1, -0.767084, -0.259408, -0.329696, 0.485376, 0.233333, 1, -0.762197, -0.264715, -0.32574, 0.492825, 0.266667, 1, -0.752211, -0.275178, -0.31792, 0.507329, 0.3, 1, -0.737703, -0.289572, -0.307032, 0.526947, 0.333333, 1, -0.719438, -0.306544, -0.293996, 0.54955, 0.366667, 1, -0.70383, -0.320235, -0.283457, 0.567209, 0.4, 1, -0.695543, -0.327215, -0.277982, 0.576087, 0.433333, 1, -0.69358, -0.328843, -0.276702, 0.57814, 0.466667, 1, -0.695997, -0.326835, -0.278275, 0.575613, 0.5, 1, -0.700132, -0.323363, -0.28098, 0.571228, 0.533333, 1, -0.704389, -0.319745, -0.283799, 0.56662, 0.566667, 1, -0.706921, -0.317568, -0.285486, 0.563836, 0.583333, 1, -0.707902, -0.316721, -0.286142, 0.562749)
tracks/77/type = "scale_3d"
tracks/77/imported = true
tracks/77/enabled = true
tracks/77/path = NodePath("rig/Skeleton3D:DEF-lip1.T.L")
tracks/77/interp = 1
tracks/77/loop_wrap = true
tracks/77/keys = PackedFloat32Array(0, 1, 0.102827, 0.0945768, 0.102827, 0.0333333, 1, 0.136174, 0.12107, 0.136174, 0.0666667, 1, 0.212853, 0.176259, 0.212853, 0.1, 1, 0.325683, 0.25039, 0.325683, 0.133333, 1, 0.465264, 0.336988, 0.465264, 0.166667, 1, 0.619973, 0.432855, 0.619973, 0.2, 1, 0.769704, 0.535875, 0.769704, 0.233333, 1, 0.898748, 0.638002, 0.898748, 0.266667, 1, 0.994471, 0.737871, 0.994471, 0.3, 1, 1.04426, 0.835554, 1.04426, 0.333333, 1, 1.03553, 0.93256, 1.03553, 0.366667, 1, 0.983262, 1.03642, 0.983262, 0.4, 1, 0.951444, 1.10552, 0.951444, 0.433333, 1, 0.943181, 1.12421, 0.943181, 0.466667, 1, 0.953908, 1.09924, 0.953908, 0.5, 1, 0.971389, 1.05977, 0.971389, 0.533333, 1, 0.987424, 1.02584, 0.987424, 0.566667, 1, 0.996573, 1.00694, 0.996573, 0.583333, 1, 1, 1, 1)
tracks/78/type = "position_3d"
tracks/78/imported = true
tracks/78/enabled = true
tracks/78/path = NodePath("rig/Skeleton3D:DEF-lip2.T.L")
tracks/78/interp = 1
tracks/78/loop_wrap = true
tracks/78/keys = PackedFloat32Array(0, 1, 0.0228299, 0.136837, 0.0356194, 0.0333333, 1, 0.0291272, 0.17567, 0.0454444, 0.0666667, 1, 0.0419969, 0.255142, 0.0655237, 0.1, 1, 0.0587329, 0.355826, 0.0916354, 0.133333, 1, 0.0776592, 0.456992, 0.121164, 0.166667, 1, 0.0981296, 0.5366, 0.153102, 0.2, 1, 0.121287, 0.550762, 0.189232, 0.233333, 1, 0.145812, 0.514282, 0.227496, 0.266667, 1, 0.171379, 0.441052, 0.267386, 0.3, 1, 0.197728, 0.349905, 0.308496, 0.333333, 1, 0.224663, 0.264623, 0.35052, 0.366667, 1, 0.252284, 0.21198, 0.393615, 0.4, 1, 0.270245, 0.183509, 0.421637, 0.433333, 1, 0.275048, 0.176622, 0.429132, 0.466667, 1, 0.268661, 0.185252, 0.419167, 0.5, 1, 0.258515, 0.199858, 0.403336, 0.533333, 1, 0.24967, 0.214463, 0.389537, 0.566667, 1, 0.244713, 0.223093, 0.381802, 0.583333, 1, 0.242882, 0.226413, 0.378946)
tracks/79/type = "rotation_3d"
tracks/79/imported = true
tracks/79/enabled = true
tracks/79/path = NodePath("rig/Skeleton3D:DEF-lip2.T.L")
tracks/79/interp = 1
tracks/79/loop_wrap = true
tracks/79/keys = PackedFloat32Array(0, 1, -0.770114, -0.224442, -0.256271, 0.539329, 0.0333333, 1, -0.775769, -0.219476, -0.260382, 0.531239, 0.0666667, 1, -0.788026, -0.208346, -0.269725, 0.512695, 0.1, 1, -0.804378, -0.192519, -0.283153, 0.485528, 0.133333, 1, -0.820579, -0.175398, -0.297673, 0.455277, 0.166667, 1, -0.831722, -0.162553, -0.308833, 0.431784, 0.2, 1, -0.832556, -0.161548, -0.309722, 0.429913, 0.233333, 1, -0.827363, -0.167704, -0.304352, 0.441266, 0.266667, 1, -0.816068, -0.180339, -0.29352, 0.464065, 0.3, 1, -0.798308, -0.19855, -0.278045, 0.495957, 0.333333, 1, -0.773763, -0.221248, -0.258907, 0.534142, 0.366667, 1, -0.750654, -0.240857, -0.242946, 0.565229, 0.4, 1, -0.73792, -0.251002, -0.234571, 0.580904, 0.433333, 1, -0.734842, -0.253398, -0.232602, 0.584546, 0.466667, 1, -0.738643, -0.250427, -0.235021, 0.58005, 0.5, 1, -0.745085, -0.24531, -0.239174, 0.572248, 0.533333, 1, -0.751603, -0.24004, -0.243484, 0.564083, 0.566667, 1, -0.755437, -0.236885, -0.246056, 0.559157, 0.583333, 1, -0.756911, -0.235661, -0.247054, 0.557237)
tracks/80/type = "scale_3d"
tracks/80/imported = true
tracks/80/enabled = true
tracks/80/path = NodePath("rig/Skeleton3D:DEF-lip2.T.L")
tracks/80/interp = 1
tracks/80/loop_wrap = true
tracks/80/keys = PackedFloat32Array(0, 1, 0.102328, 0.0955015, 0.102328, 0.0333333, 1, 0.135162, 0.122886, 0.135162, 0.0666667, 1, 0.209732, 0.181491, 0.209732, 0.1, 1, 0.317384, 0.263503, 0.317384, 0.133333, 1, 0.448307, 0.362907, 0.448307, 0.2, 1, 0.733706, 0.589748, 0.733706, 0.233333, 1, 0.861619, 0.694122, 0.861619, 0.266667, 1, 0.96291, 0.786708, 0.96291, 0.3, 1, 1.02376, 0.868743, 1.02376, 0.333333, 1, 1.02892, 0.944582, 1.02892, 0.366667, 1, 0.98476, 1.03268, 0.98476, 0.4, 1, 0.956396, 1.09397, 0.956396, 0.433333, 1, 0.948811, 1.1109, 0.948811, 0.466667, 1, 0.958765, 1.08809, 0.958766, 0.5, 1, 0.974799, 1.05237, 0.9748, 0.533333, 1, 0.989041, 1.02245, 0.989041, 0.566667, 1, 0.99704, 1.00599, 0.99704, 0.583333, 1, 1, 1, 1)
tracks/81/type = "position_3d"
tracks/81/imported = true
tracks/81/enabled = true
tracks/81/path = NodePath("rig/Skeleton3D:DEF-lip1.T.R")
tracks/81/interp = 1
tracks/81/loop_wrap = true
tracks/81/keys = PackedFloat32Array(0, 1, -0.0119026, 0.139672, 0.0443487, 0.0333333, 1, -0.0151857, 0.179581, 0.0565815, 0.0666667, 1, -0.0218954, 0.261849, 0.0815819, 0.1, 1, -0.0306208, 0.36726, 0.114093, 0.133333, 1, -0.0404882, 0.474894, 0.150858, 0.166667, 1, -0.0511606, 0.56213, 0.190623, 0.2, 1, -0.0632338, 0.582651, 0.235608, 0.233333, 1, -0.0760201, 0.550128, 0.283249, 0.266667, 1, -0.0893497, 0.4779, 0.332915, 0.3, 1, -0.103087, 0.384549, 0.3841, 0.333333, 1, -0.11713, 0.293898, 0.436423, 0.366667, 1, -0.13153, 0.235431, 0.490079, 0.4, 1, -0.140894, 0.20381, 0.524969, 0.433333, 1, -0.143398, 0.196162, 0.5343, 0.466667, 1, -0.140069, 0.205747, 0.521893, 0.5, 1, -0.134779, 0.221968, 0.502183, 0.533333, 1, -0.130167, 0.238189, 0.485002, 0.566667, 1, -0.127583, 0.247774, 0.475372, 0.583333, 1, -0.126628, 0.251461, 0.471815)
tracks/82/type = "rotation_3d"
tracks/82/imported = true
tracks/82/enabled = true
tracks/82/path = NodePath("rig/Skeleton3D:DEF-lip1.T.R")
tracks/82/interp = 1
tracks/82/loop_wrap = true
tracks/82/keys = PackedFloat32Array(0, 1, -0.716888, 0.308828, 0.292239, 0.552534, 0.0333333, 1, -0.72086, 0.305262, 0.294984, 0.547869, 0.0666667, 1, -0.729812, 0.297046, 0.301306, 0.536985, 0.1, 1, -0.742524, 0.284886, 0.310596, 0.520604, 0.133333, 1, -0.756061, 0.271198, 0.320881, 0.501855, 0.166667, 1, -0.766278, 0.260292, 0.329034, 0.486624, 0.2, 1, -0.767084, 0.259408, 0.329696, 0.485376, 0.233333, 1, -0.762196, 0.264715, 0.32574, 0.492825, 0.266667, 1, -0.75221, 0.275179, 0.317921, 0.507329, 0.3, 1, -0.737703, 0.289573, 0.307032, 0.526947, 0.333333, 1, -0.719438, 0.306544, 0.293996, 0.549551, 0.366667, 1, -0.70383, 0.320235, 0.283457, 0.567208, 0.4, 1, -0.695543, 0.327215, 0.277983, 0.576087, 0.433333, 1, -0.69358, 0.328843, 0.276702, 0.57814, 0.466667, 1, -0.695997, 0.326835, 0.278275, 0.575613, 0.5, 1, -0.700132, 0.323363, 0.28098, 0.571228, 0.533333, 1, -0.704389, 0.319745, 0.283799, 0.56662, 0.566667, 1, -0.706921, 0.317568, 0.285486, 0.563835, 0.583333, 1, -0.707902, 0.316721, 0.286142, 0.562749)
tracks/83/type = "scale_3d"
tracks/83/imported = true
tracks/83/enabled = true
tracks/83/path = NodePath("rig/Skeleton3D:DEF-lip1.T.R")
tracks/83/interp = 1
tracks/83/loop_wrap = true
tracks/83/keys = PackedFloat32Array(0, 1, 0.102827, 0.0945769, 0.102827, 0.0333333, 1, 0.136174, 0.12107, 0.136174, 0.0666667, 1, 0.212853, 0.176259, 0.212853, 0.1, 1, 0.325683, 0.25039, 0.325683, 0.133333, 1, 0.465264, 0.336988, 0.465264, 0.166667, 1, 0.619973, 0.432855, 0.619973, 0.2, 1, 0.769704, 0.535875, 0.769704, 0.233333, 1, 0.898748, 0.638002, 0.898748, 0.266667, 1, 0.994471, 0.737871, 0.994471, 0.3, 1, 1.04426, 0.835554, 1.04426, 0.333333, 1, 1.03553, 0.932561, 1.03553, 0.366667, 1, 0.983262, 1.03642, 0.983262, 0.4, 1, 0.951444, 1.10552, 0.951444, 0.433333, 1, 0.943181, 1.12422, 0.943181, 0.466667, 1, 0.953908, 1.09924, 0.953908, 0.5, 1, 0.971389, 1.05978, 0.971389, 0.533333, 1, 0.987424, 1.02584, 0.987424, 0.566667, 1, 0.996573, 1.00694, 0.996573, 0.583333, 1, 1, 1, 1)
tracks/84/type = "position_3d"
tracks/84/imported = true
tracks/84/enabled = true
tracks/84/path = NodePath("rig/Skeleton3D:DEF-lip2.T.R")
tracks/84/interp = 1
tracks/84/loop_wrap = true
tracks/84/keys = PackedFloat32Array(0, 1, -0.0228299, 0.136837, 0.0356194, 0.0333333, 1, -0.0291272, 0.17567, 0.0454443, 0.0666667, 1, -0.0419969, 0.255142, 0.0655237, 0.1, 1, -0.058733, 0.355826, 0.0916354, 0.133333, 1, -0.0776592, 0.456992, 0.121164, 0.166667, 1, -0.0981297, 0.5366, 0.153102, 0.2, 1, -0.121287, 0.550762, 0.189232, 0.233333, 1, -0.145812, 0.514282, 0.227496, 0.266667, 1, -0.171379, 0.441052, 0.267386, 0.3, 1, -0.197728, 0.349905, 0.308496, 0.333333, 1, -0.224663, 0.264623, 0.35052, 0.366667, 1, -0.252284, 0.211979, 0.393615, 0.4, 1, -0.270245, 0.183509, 0.421637, 0.433333, 1, -0.275048, 0.176622, 0.429132, 0.466667, 1, -0.268661, 0.185252, 0.419167, 0.5, 1, -0.258515, 0.199858, 0.403336, 0.533333, 1, -0.24967, 0.214463, 0.389537, 0.566667, 1, -0.244713, 0.223093, 0.381802, 0.583333, 1, -0.242882, 0.226413, 0.378946)
tracks/85/type = "rotation_3d"
tracks/85/imported = true
tracks/85/enabled = true
tracks/85/path = NodePath("rig/Skeleton3D:DEF-lip2.T.R")
tracks/85/interp = 1
tracks/85/loop_wrap = true
tracks/85/keys = PackedFloat32Array(0, 1, -0.770114, 0.224442, 0.256271, 0.539329, 0.0333333, 1, -0.775769, 0.219476, 0.260382, 0.531239, 0.0666667, 1, -0.788026, 0.208346, 0.269725, 0.512695, 0.1, 1, -0.804378, 0.192519, 0.283153, 0.485528, 0.133333, 1, -0.820579, 0.175398, 0.297673, 0.455276, 0.166667, 1, -0.831722, 0.162553, 0.308833, 0.431784, 0.2, 1, -0.832556, 0.161548, 0.309723, 0.429913, 0.233333, 1, -0.827363, 0.167704, 0.304353, 0.441266, 0.266667, 1, -0.816068, 0.180339, 0.29352, 0.464065, 0.3, 1, -0.798308, 0.19855, 0.278045, 0.495957, 0.333333, 1, -0.773763, 0.221248, 0.258907, 0.534142, 0.366667, 1, -0.750654, 0.240856, 0.242946, 0.56523, 0.4, 1, -0.73792, 0.251002, 0.234571, 0.580904, 0.433333, 1, -0.734842, 0.253398, 0.232602, 0.584546, 0.466667, 1, -0.738643, 0.250427, 0.235021, 0.58005, 0.5, 1, -0.745085, 0.24531, 0.239174, 0.572247, 0.533333, 1, -0.751603, 0.24004, 0.243484, 0.564083, 0.566667, 1, -0.755437, 0.236885, 0.246056, 0.559157, 0.583333, 1, -0.756911, 0.235661, 0.247054, 0.557237)
tracks/86/type = "scale_3d"
tracks/86/imported = true
tracks/86/enabled = true
tracks/86/path = NodePath("rig/Skeleton3D:DEF-lip2.T.R")
tracks/86/interp = 1
tracks/86/loop_wrap = true
tracks/86/keys = PackedFloat32Array(0, 1, 0.102328, 0.0955015, 0.102328, 0.0333333, 1, 0.135162, 0.122886, 0.135162, 0.0666667, 1, 0.209732, 0.18149, 0.209732, 0.1, 1, 0.317384, 0.263503, 0.317384, 0.133333, 1, 0.448307, 0.362907, 0.448307, 0.2, 1, 0.733706, 0.589748, 0.733706, 0.233333, 1, 0.86162, 0.694121, 0.86162, 0.266667, 1, 0.962911, 0.786708, 0.962911, 0.3, 1, 1.02376, 0.868743, 1.02376, 0.333333, 1, 1.02892, 0.944582, 1.02892, 0.366667, 1, 0.984761, 1.03268, 0.984761, 0.4, 1, 0.956397, 1.09397, 0.956397, 0.433333, 1, 0.948812, 1.1109, 0.948812, 0.466667, 1, 0.958766, 1.08809, 0.958766, 0.5, 1, 0.974801, 1.05237, 0.974801, 0.533333, 1, 0.989041, 1.02244, 0.989041, 0.566667, 1, 0.99704, 1.00599, 0.99704, 0.583333, 1, 1, 1, 1)
tracks/87/type = "position_3d"
tracks/87/imported = true
tracks/87/enabled = true
tracks/87/path = NodePath("rig/Skeleton3D:DEF-bone.02")
tracks/87/interp = 1
tracks/87/loop_wrap = true
tracks/87/keys = PackedFloat32Array(0, 1, -1.11751e-15, 0.152229, 0, 0.0333333, 1, -1.40997e-15, 0.196905, 0, 0.0666667, 1, -1.95455e-15, 0.291559, 0, 0.1, 1, -2.53704e-15, 0.417901, 0, 0.133333, 1, -2.96609e-15, 0.554184, 0, 0.166667, 1, -3.07316e-15, 0.675207, 0, 0.2, 1, -2.63782e-15, 0.723896, 0, 0.233333, 1, -1.91193e-15, 0.708894, 0, 0.266667, 1, -1.08498e-15, 0.641107, 0, 0.3, 1, -3.69289e-16, 0.537994, 0, 0.333333, 1, 0, 0.423563, 0, 0.366667, 1, 0, 0.3393, 0, 0.4, 1, 0, 0.29373, 0, 0.433333, 1, 0, 0.282706, 0, 0.466667, 1, 0, 0.29652, 0, 0.533333, 1, 0, 0.343275, 0, 0.566667, 1, 0, 0.357089, 0, 0.583333, 1, 0, 0.362402, 0)
tracks/88/type = "rotation_3d"
tracks/88/imported = true
tracks/88/enabled = true
tracks/88/path = NodePath("rig/Skeleton3D:DEF-bone.02")
tracks/88/interp = 1
tracks/88/loop_wrap = true
tracks/88/keys = PackedFloat32Array(0, 1, -2.10557e-13, -1, 2.98023e-08, 3.65178e-06)
tracks/89/type = "scale_3d"
tracks/89/imported = true
tracks/89/enabled = true
tracks/89/path = NodePath("rig/Skeleton3D:DEF-bone.02")
tracks/89/interp = 1
tracks/89/loop_wrap = true
tracks/89/keys = PackedFloat32Array(0, 1, 0.093996, 0.113183, 0.093996, 0.0333333, 1, 0.119923, 0.156151, 0.119923, 0.0666667, 1, 0.172911, 0.267794, 0.172911, 0.1, 1, 0.241817, 0.456464, 0.241817, 0.133333, 1, 0.31974, 0.714701, 0.31974, 0.166667, 1, 0.404022, 1.01925, 0.404022, 0.2, 1, 0.499366, 1.27314, 0.499366, 0.233333, 1, 0.60034, 1.43108, 0.60034, 0.266667, 1, 0.705606, 1.47111, 0.705606, 0.3, 1, 0.81409, 1.38311, 0.81409, 0.333333, 1, 0.924988, 1.16877, 0.924988, 0.366667, 1, 1.03871, 0.936253, 1.03871, 0.4, 1, 1.11266, 0.810507, 1.11266, 0.433333, 1, 1.13244, 0.780089, 1.13244, 0.466667, 1, 1.10614, 0.818206, 1.10614, 0.5, 1, 1.06436, 0.882714, 1.06436, 0.533333, 1, 1.02795, 0.947221, 1.02795, 0.566667, 1, 1.00754, 0.985339, 1.00754, 0.583333, 1, 1, 1, 1)

[sub_resource type="Animation" id="Animation_v3btl"]
resource_name = "Idle"
length = 1.66667
loop_mode = 1
tracks/0/type = "position_3d"
tracks/0/imported = true
tracks/0/enabled = true
tracks/0/path = NodePath("rig/Skeleton3D:DEF-lid1.B.L")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = PackedFloat32Array(0, 1, 0.247766, 0.472781, 0.35834, 0.0333333, 1, 0.244439, 0.48339, 0.353452, 0.0666667, 1, 0.242339, 0.493127, 0.348966, 0.1, 1, 0.241515, 0.501563, 0.345036, 0.133333, 1, 0.242022, 0.508222, 0.341817, 0.166667, 1, 0.243921, 0.512574, 0.339469, 0.2, 1, 0.247856, 0.512547, 0.338661, 0.233333, 1, 0.25342, 0.508447, 0.339244, 0.266667, 1, 0.260418, 0.500723, 0.341061, 0.3, 1, 0.268637, 0.489911, 0.343936, 0.333333, 1, 0.277847, 0.476637, 0.347672, 0.366667, 1, 0.287954, 0.46118, 0.35222, 0.4, 1, 0.298413, 0.444849, 0.357125, 0.466667, 1, 0.319458, 0.41154, 0.367249, 0.5, 1, 0.329551, 0.395568, 0.372059, 0.533333, 1, 0.338614, 0.381513, 0.37607, 0.566667, 1, 0.346493, 0.369792, 0.379092, 0.6, 1, 0.352931, 0.361051, 0.380868, 0.633333, 1, 0.357721, 0.355868, 0.381192, 0.666667, 1, 0.360705, 0.354747, 0.379912, 0.7, 1, 0.361534, 0.358642, 0.376875, 0.733333, 1, 0.361032, 0.365643, 0.37294, 0.766667, 1, 0.359363, 0.375258, 0.368375, 0.8, 1, 0.356673, 0.386983, 0.36343, 0.833333, 1, 0.353091, 0.400295, 0.358339, 0.9, 1, 0.343515, 0.428777, 0.348965, 0.933333, 1, 0.338183, 0.442128, 0.345035, 0.966667, 1, 0.332729, 0.454016, 0.341817, 1, 1, 0.327339, 0.463766, 0.339468, 1.03333, 1, 0.322469, 0.469192, 0.33866, 1.06667, 1, 0.31828, 0.470593, 0.339244, 1.1, 1, 0.314703, 0.468566, 0.341061, 1.13333, 1, 0.311652, 0.463793, 0.343935, 1.16667, 1, 0.309023, 0.457041, 0.347672, 1.2, 1, 0.306787, 0.448949, 0.35222, 1.23333, 1, 0.304712, 0.440693, 0.357125, 1.26667, 1, 0.302696, 0.432756, 0.3622, 1.3, 1, 0.300625, 0.425584, 0.367249, 1.33333, 1, 0.298374, 0.419583, 0.372059, 1.36667, 1, 0.295599, 0.415809, 0.37607, 1.4, 1, 0.292208, 0.414099, 0.379093, 1.43333, 1, 0.288071, 0.414668, 0.380868, 1.46667, 1, 0.283107, 0.417663, 0.381192, 1.5, 1, 0.277287, 0.423161, 0.379913, 1.53333, 1, 0.270827, 0.431232, 0.376875, 1.56667, 1, 0.264364, 0.440594, 0.37294, 1.6, 1, 0.258187, 0.45088, 0.368375, 1.66667, 1, 0.247766, 0.472781, 0.35834)
tracks/1/type = "rotation_3d"
tracks/1/imported = true
tracks/1/enabled = true
tracks/1/path = NodePath("rig/Skeleton3D:DEF-lid1.B.L")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = PackedFloat32Array(0, 1, 0.696881, -0.193461, -0.684446, 0.0919925, 0.0333333, 1, 0.697371, -0.187031, -0.686158, 0.0887739, 0.0666667, 1, 0.697632, -0.181832, -0.687742, 0.08519, 0.1, 1, 0.69768, -0.177995, -0.689155, 0.0813939, 0.133333, 1, 0.697524, -0.175662, -0.690357, 0.0775264, 0.166667, 1, 0.697163, -0.174986, -0.69131, 0.073716, 0.2, 1, 0.696484, -0.17697, -0.691854, 0.0702102, 0.233333, 1, 0.695501, -0.181283, -0.692041, 0.0670361, 0.266667, 1, 0.694195, -0.187729, -0.691912, 0.0641006, 0.3, 1, 0.692544, -0.196085, -0.691502, 0.0613076, 0.333333, 1, 0.690528, -0.206101, -0.690844, 0.0585581, 0.366667, 1, 0.688033, -0.217874, -0.689954, 0.0557238, 0.4, 1, 0.685164, -0.230563, -0.688919, 0.0527423, 0.433333, 1, 0.681947, -0.243831, -0.687777, 0.0495743, 0.5, 1, 0.674715, -0.270547, -0.685385, 0.0425748, 0.533333, 1, 0.671039, -0.282502, -0.684401, 0.0386579, 0.566667, 1, 0.667661, -0.292612, -0.683675, 0.0345677, 0.6, 1, 0.66483, -0.30029, -0.683304, 0.03037, 0.633333, 1, 0.662774, -0.305042, -0.683371, 0.0261504, 0.666667, 1, 0.661693, -0.306466, -0.683927, 0.0220147, 0.7, 1, 0.661877, -0.303936, -0.684987, 0.0183013, 0.733333, 1, 0.662822, -0.29912, -0.68627, 0.0150992, 0.766667, 1, 0.664375, -0.292486, -0.687678, 0.012496, 0.8, 1, 0.666382, -0.284481, -0.689125, 0.0105628, 0.833333, 1, 0.668694, -0.275536, -0.690537, 0.00935443, 0.866667, 1, 0.67118, -0.266115, -0.691821, 0.00914712, 0.9, 1, 0.673605, -0.256894, -0.692942, 0.00961489, 0.933333, 1, 0.675885, -0.248205, -0.693873, 0.0107042, 0.966667, 1, 0.677947, -0.24037, -0.694591, 0.0123746, 1, 1, 0.679736, -0.233701, -0.695076, 0.0145984, 1.03333, 1, 0.681066, -0.229251, -0.695187, 0.017571, 1.06667, 1, 0.682005, -0.226847, -0.694955, 0.021158, 1.1, 1, 0.682635, -0.226213, -0.694403, 0.0253497, 1.13333, 1, 0.68303, -0.227049, -0.693549, 0.0301291, 1.16667, 1, 0.683265, -0.229031, -0.692413, 0.0354711, 1.2, 1, 0.683413, -0.232005, -0.690941, 0.0414918, 1.23333, 1, 0.683568, -0.235331, -0.689243, 0.047934, 1.26667, 1, 0.68378, -0.238729, -0.687359, 0.0547029, 1.3, 1, 0.684095, -0.241907, -0.68534, 0.0616829, 1.33333, 1, 0.684562, -0.244563, -0.683257, 0.0687374, 1.36667, 1, 0.68532, -0.245885, -0.681303, 0.0755223, 1.4, 1, 0.686317, -0.245733, -0.679632, 0.0817646, 1.46667, 1, 0.688992, -0.24001, -0.677708, 0.0916313, 1.5, 1, 0.690598, -0.234167, -0.677681, 0.0948119, 1.53333, 1, 0.692218, -0.226459, -0.678436, 0.0963124, 1.56667, 1, 0.693704, -0.218109, -0.679602, 0.0966702, 1.6, 1, 0.694999, -0.209561, -0.681062, 0.0959928, 1.63333, 1, 0.696066, -0.201222, -0.682709, 0.0943939, 1.66667, 1, 0.696881, -0.193461, -0.684446, 0.0919925)
tracks/2/type = "scale_3d"
tracks/2/imported = true
tracks/2/enabled = true
tracks/2/path = NodePath("rig/Skeleton3D:DEF-lid1.B.L")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = PackedFloat32Array(0, 1, 0.999371, 1.00126, 0.999371, 0.0333333, 1, 0.989989, 1.02039, 0.989989, 0.0666667, 1, 0.981131, 1.03892, 0.981131, 0.1, 1, 0.973179, 1.05595, 0.973179, 0.133333, 1, 0.966528, 1.07048, 0.966528, 0.166667, 1, 0.961589, 1.08149, 0.961589, 0.2, 1, 0.959871, 1.08536, 0.959871, 0.233333, 1, 0.961111, 1.08257, 0.961111, 0.266667, 1, 0.964937, 1.07403, 0.964937, 0.3, 1, 0.970921, 1.06085, 0.970921, 0.333333, 1, 0.978576, 1.04427, 0.978576, 0.366667, 1, 0.987592, 1.02535, 0.987592, 0.4, 1, 0.997016, 1.0061, 0.997016, 0.433333, 1, 1.00645, 0.987315, 1.00645, 0.466667, 1, 1.01551, 0.969729, 1.01551, 0.5, 1, 1.02383, 0.954, 1.02383, 0.533333, 1, 1.03043, 0.941829, 1.03043, 0.566667, 1, 1.03529, 0.933003, 1.03529, 0.6, 1, 1.03809, 0.927957, 1.03809, 0.633333, 1, 1.0386, 0.927061, 1.0386, 0.666667, 1, 1.0366, 0.930625, 1.0366, 0.7, 1, 1.03175, 0.939415, 1.03175, 0.733333, 1, 1.02525, 0.951394, 1.02525, 0.766667, 1, 1.01746, 0.966059, 1.01746, 0.8, 1, 1.00871, 0.982872, 1.00871, 0.833333, 1, 0.999372, 1.00126, 0.999372, 0.866667, 1, 0.989989, 1.02039, 0.98999, 0.9, 1, 0.981131, 1.03892, 0.981131, 0.933333, 1, 0.973179, 1.05595, 0.973179, 0.966667, 1, 0.966528, 1.07048, 0.966528, 1, 1, 0.961589, 1.08149, 0.961589, 1.03333, 1, 0.959871, 1.08536, 0.95987, 1.06667, 1, 0.96111, 1.08257, 0.96111, 1.1, 1, 0.964937, 1.07403, 0.964937, 1.13333, 1, 0.970921, 1.06085, 0.970921, 1.16667, 1, 0.978576, 1.04427, 0.978576, 1.2, 1, 0.987592, 1.02535, 0.987592, 1.23333, 1, 0.997016, 1.0061, 0.997016, 1.26667, 1, 1.00645, 0.987315, 1.00645, 1.3, 1, 1.01551, 0.969729, 1.01551, 1.33333, 1, 1.02382, 0.954001, 1.02382, 1.36667, 1, 1.03043, 0.941829, 1.03043, 1.4, 1, 1.03529, 0.933003, 1.03529, 1.43333, 1, 1.03809, 0.927956, 1.03809, 1.46667, 1, 1.0386, 0.927059, 1.0386, 1.5, 1, 1.0366, 0.930624, 1.0366, 1.53333, 1, 1.03175, 0.939415, 1.03175, 1.56667, 1, 1.02525, 0.951393, 1.02525, 1.6, 1, 1.01746, 0.966058, 1.01746, 1.63333, 1, 1.00871, 0.982872, 1.00871, 1.66667, 1, 0.999371, 1.00126, 0.999371)
tracks/3/type = "position_3d"
tracks/3/imported = true
tracks/3/enabled = true
tracks/3/path = NodePath("rig/Skeleton3D:DEF-lid1.B.R")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = PackedFloat32Array(0, 1, -0.353091, 0.400294, 0.35834, 0.0666667, 1, -0.343515, 0.428776, 0.348966, 0.1, 1, -0.338183, 0.442128, 0.345036, 0.133333, 1, -0.332729, 0.454015, 0.341817, 0.166667, 1, -0.327339, 0.463765, 0.339469, 0.2, 1, -0.322469, 0.469192, 0.338661, 0.233333, 1, -0.31828, 0.470592, 0.339244, 0.266667, 1, -0.314703, 0.468565, 0.341061, 0.3, 1, -0.311652, 0.463793, 0.343936, 0.333333, 1, -0.309023, 0.457041, 0.347672, 0.366667, 1, -0.306787, 0.448948, 0.35222, 0.4, 1, -0.304712, 0.440693, 0.357125, 0.433333, 1, -0.302696, 0.432756, 0.3622, 0.466667, 1, -0.300625, 0.425584, 0.367249, 0.5, 1, -0.298374, 0.419584, 0.372059, 0.533333, 1, -0.295599, 0.41581, 0.37607, 0.566667, 1, -0.292208, 0.4141, 0.379092, 0.6, 1, -0.288071, 0.414669, 0.380868, 0.633333, 1, -0.283107, 0.417663, 0.381192, 0.666667, 1, -0.277287, 0.423162, 0.379912, 0.7, 1, -0.270827, 0.431232, 0.376875, 0.733333, 1, -0.264364, 0.440595, 0.37294, 0.766667, 1, -0.258187, 0.45088, 0.368375, 0.833333, 1, -0.247766, 0.472781, 0.358339, 0.866667, 1, -0.244439, 0.483391, 0.353451, 0.9, 1, -0.242339, 0.493127, 0.348965, 0.933333, 1, -0.241515, 0.501564, 0.345035, 0.966667, 1, -0.242022, 0.508222, 0.341816, 1, 1, -0.243921, 0.512574, 0.339468, 1.03333, 1, -0.247856, 0.512547, 0.33866, 1.06667, 1, -0.25342, 0.508447, 0.339244, 1.1, 1, -0.260418, 0.500723, 0.341061, 1.13333, 1, -0.268637, 0.489912, 0.343935, 1.16667, 1, -0.277847, 0.476637, 0.347672, 1.2, 1, -0.287954, 0.461181, 0.35222, 1.23333, 1, -0.298413, 0.444849, 0.357125, 1.3, 1, -0.319458, 0.41154, 0.367249, 1.33333, 1, -0.329551, 0.395568, 0.372059, 1.36667, 1, -0.338614, 0.381513, 0.37607, 1.4, 1, -0.346493, 0.369791, 0.379093, 1.43333, 1, -0.352931, 0.361051, 0.380868, 1.46667, 1, -0.357721, 0.355867, 0.381192, 1.5, 1, -0.360705, 0.354746, 0.379913, 1.53333, 1, -0.361534, 0.358642, 0.376875, 1.56667, 1, -0.361032, 0.365642, 0.37294, 1.6, 1, -0.359363, 0.375257, 0.368375, 1.63333, 1, -0.356673, 0.386982, 0.36343, 1.66667, 1, -0.353091, 0.400294, 0.35834)
tracks/4/type = "rotation_3d"
tracks/4/imported = true
tracks/4/enabled = true
tracks/4/path = NodePath("rig/Skeleton3D:DEF-lid1.B.R")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = PackedFloat32Array(0, 1, 0.668694, 0.275535, 0.690537, 0.00935334, 0.0333333, 1, 0.67118, 0.266115, 0.691822, 0.00914686, 0.0666667, 1, 0.673605, 0.256894, 0.692942, 0.00961458, 0.1, 1, 0.675885, 0.248205, 0.693873, 0.0107038, 0.133333, 1, 0.677947, 0.24037, 0.694591, 0.0123741, 0.166667, 1, 0.679736, 0.2337, 0.695075, 0.014597, 0.2, 1, 0.681066, 0.22925, 0.695187, 0.0175706, 0.233333, 1, 0.682005, 0.226846, 0.694955, 0.0211577, 0.266667, 1, 0.682635, 0.226212, 0.694403, 0.0253491, 0.3, 1, 0.683031, 0.227048, 0.693549, 0.030128, 0.333333, 1, 0.683265, 0.22903, 0.692413, 0.0354701, 0.366667, 1, 0.683413, 0.232005, 0.690941, 0.0414917, 0.4, 1, 0.683568, 0.235331, 0.689243, 0.047934, 0.433333, 1, 0.68378, 0.238729, 0.687358, 0.0547031, 0.466667, 1, 0.684095, 0.241907, 0.68534, 0.0616832, 0.5, 1, 0.684562, 0.244563, 0.683256, 0.0687372, 0.533333, 1, 0.68532, 0.245885, 0.681302, 0.0755224, 0.566667, 1, 0.686317, 0.245733, 0.679631, 0.0817647, 0.633333, 1, 0.688992, 0.24001, 0.677708, 0.0916316, 0.666667, 1, 0.690598, 0.234167, 0.677681, 0.0948119, 0.7, 1, 0.692218, 0.226459, 0.678435, 0.096314, 0.733333, 1, 0.693704, 0.218109, 0.679601, 0.096671, 0.8, 1, 0.696066, 0.201222, 0.682709, 0.0943939, 0.833333, 1, 0.696881, 0.193462, 0.684446, 0.091993, 0.866667, 1, 0.697371, 0.187032, 0.686157, 0.0887743, 0.9, 1, 0.697632, 0.181833, 0.687742, 0.0851906, 0.933333, 1, 0.69768, 0.177996, 0.689155, 0.0813947, 0.966667, 1, 0.697524, 0.175663, 0.690356, 0.0775272, 1, 1, 0.697163, 0.174987, 0.691309, 0.0737165, 1.03333, 1, 0.696485, 0.176971, 0.691854, 0.0702101, 1.06667, 1, 0.695501, 0.181283, 0.692041, 0.067036, 1.1, 1, 0.694195, 0.187729, 0.691912, 0.0641007, 1.13333, 1, 0.692544, 0.196085, 0.691502, 0.0613077, 1.16667, 1, 0.690528, 0.206101, 0.690844, 0.058558, 1.2, 1, 0.688034, 0.217874, 0.689953, 0.0557236, 1.23333, 1, 0.685165, 0.230562, 0.688919, 0.0527421, 1.26667, 1, 0.681947, 0.24383, 0.687777, 0.0495744, 1.33333, 1, 0.674715, 0.270546, 0.685385, 0.0425739, 1.36667, 1, 0.671039, 0.282502, 0.684401, 0.0386576, 1.4, 1, 0.667662, 0.292612, 0.683675, 0.0345672, 1.43333, 1, 0.66483, 0.300289, 0.683305, 0.0303693, 1.46667, 1, 0.662774, 0.305041, 0.683371, 0.0261499, 1.5, 1, 0.661693, 0.306466, 0.683927, 0.0220143, 1.53333, 1, 0.661877, 0.303935, 0.684988, 0.0183013, 1.56667, 1, 0.662822, 0.29912, 0.68627, 0.0150987, 1.6, 1, 0.664375, 0.292486, 0.687678, 0.0124955, 1.63333, 1, 0.666382, 0.28448, 0.689126, 0.0105626, 1.66667, 1, 0.668694, 0.275535, 0.690537, 0.00935334)
tracks/5/type = "scale_3d"
tracks/5/imported = true
tracks/5/enabled = true
tracks/5/path = NodePath("rig/Skeleton3D:DEF-lid1.B.R")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = PackedFloat32Array(0, 1, 0.999371, 1.00126, 0.999371, 0.0333333, 1, 0.989989, 1.02039, 0.98999, 0.0666667, 1, 0.981132, 1.03892, 0.981132, 0.1, 1, 0.973179, 1.05595, 0.973179, 0.133333, 1, 0.966528, 1.07048, 0.966528, 0.166667, 1, 0.961589, 1.08149, 0.961589, 0.2, 1, 0.95987, 1.08537, 0.95987, 0.233333, 1, 0.96111, 1.08257, 0.961111, 0.266667, 1, 0.964937, 1.07403, 0.964937, 0.3, 1, 0.970921, 1.06085, 0.970921, 0.333333, 1, 0.978576, 1.04427, 0.978576, 0.366667, 1, 0.987592, 1.02535, 0.987593, 0.4, 1, 0.997015, 1.0061, 0.997016, 0.433333, 1, 1.00645, 0.987317, 1.00645, 0.466667, 1, 1.01551, 0.96973, 1.01551, 0.5, 1, 1.02382, 0.954002, 1.02382, 0.533333, 1, 1.03043, 0.94183, 1.03044, 0.566667, 1, 1.03529, 0.933003, 1.03529, 0.6, 1, 1.03809, 0.927957, 1.03809, 0.633333, 1, 1.0386, 0.927061, 1.0386, 0.666667, 1, 1.0366, 0.930625, 1.0366, 0.7, 1, 1.03175, 0.939416, 1.03175, 0.733333, 1, 1.02525, 0.951394, 1.02525, 0.766667, 1, 1.01746, 0.966058, 1.01746, 0.8, 1, 1.00871, 0.982871, 1.00871, 0.833333, 1, 0.999371, 1.00126, 0.999372, 0.866667, 1, 0.989989, 1.02039, 0.989989, 0.9, 1, 0.981131, 1.03893, 0.981131, 0.933333, 1, 0.973179, 1.05595, 0.973179, 0.966667, 1, 0.966528, 1.07048, 0.966529, 1, 1, 0.961589, 1.08149, 0.961589, 1.03333, 1, 0.959871, 1.08537, 0.959871, 1.06667, 1, 0.96111, 1.08257, 0.961111, 1.1, 1, 0.964937, 1.07403, 0.964937, 1.13333, 1, 0.970921, 1.06085, 0.970921, 1.16667, 1, 0.978576, 1.04427, 0.978576, 1.2, 1, 0.987592, 1.02535, 0.987593, 1.23333, 1, 0.997015, 1.0061, 0.997016, 1.26667, 1, 1.00645, 0.987317, 1.00645, 1.3, 1, 1.01551, 0.96973, 1.01551, 1.33333, 1, 1.02382, 0.954002, 1.02382, 1.36667, 1, 1.03043, 0.94183, 1.03043, 1.4, 1, 1.03529, 0.933003, 1.03529, 1.43333, 1, 1.03809, 0.927957, 1.03809, 1.46667, 1, 1.0386, 0.927061, 1.0386, 1.5, 1, 1.0366, 0.930626, 1.0366, 1.53333, 1, 1.03175, 0.939415, 1.03175, 1.56667, 1, 1.02525, 0.951393, 1.02525, 1.6, 1, 1.01746, 0.966058, 1.01746, 1.63333, 1, 1.00871, 0.982872, 1.00871, 1.66667, 1, 0.999371, 1.00126, 0.999371)
tracks/6/type = "position_3d"
tracks/6/imported = true
tracks/6/enabled = true
tracks/6/path = NodePath("rig/Skeleton3D:DEF-lid1.T.L")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = PackedFloat32Array(0, 1, 0.247766, 0.472781, 0.35834, 0.0333333, 1, 0.244439, 0.48339, 0.353452, 0.0666667, 1, 0.242339, 0.493127, 0.348966, 0.1, 1, 0.241515, 0.501563, 0.345036, 0.133333, 1, 0.242022, 0.508222, 0.341817, 0.166667, 1, 0.243921, 0.512574, 0.339469, 0.2, 1, 0.247856, 0.512547, 0.338661, 0.233333, 1, 0.25342, 0.508447, 0.339244, 0.266667, 1, 0.260418, 0.500723, 0.341061, 0.3, 1, 0.268637, 0.489911, 0.343936, 0.333333, 1, 0.277847, 0.476637, 0.347672, 0.366667, 1, 0.287954, 0.46118, 0.35222, 0.4, 1, 0.298413, 0.444849, 0.357125, 0.466667, 1, 0.319458, 0.41154, 0.367249, 0.5, 1, 0.329551, 0.395568, 0.372059, 0.533333, 1, 0.338614, 0.381513, 0.37607, 0.566667, 1, 0.346493, 0.369792, 0.379092, 0.6, 1, 0.352931, 0.361051, 0.380868, 0.633333, 1, 0.357721, 0.355868, 0.381192, 0.666667, 1, 0.360705, 0.354747, 0.379912, 0.7, 1, 0.361534, 0.358642, 0.376875, 0.733333, 1, 0.361032, 0.365643, 0.37294, 0.766667, 1, 0.359363, 0.375258, 0.368375, 0.8, 1, 0.356673, 0.386983, 0.36343, 0.833333, 1, 0.353091, 0.400295, 0.358339, 0.9, 1, 0.343515, 0.428777, 0.348965, 0.933333, 1, 0.338183, 0.442128, 0.345035, 0.966667, 1, 0.332729, 0.454016, 0.341817, 1, 1, 0.327339, 0.463766, 0.339468, 1.03333, 1, 0.322469, 0.469192, 0.33866, 1.06667, 1, 0.31828, 0.470593, 0.339244, 1.1, 1, 0.314703, 0.468566, 0.341061, 1.13333, 1, 0.311652, 0.463793, 0.343935, 1.16667, 1, 0.309023, 0.457041, 0.347672, 1.2, 1, 0.306787, 0.448949, 0.35222, 1.23333, 1, 0.304712, 0.440693, 0.357125, 1.26667, 1, 0.302696, 0.432756, 0.3622, 1.3, 1, 0.300625, 0.425584, 0.367249, 1.33333, 1, 0.298374, 0.419583, 0.372059, 1.36667, 1, 0.295599, 0.415809, 0.37607, 1.4, 1, 0.292208, 0.414099, 0.379093, 1.43333, 1, 0.288071, 0.414668, 0.380868, 1.46667, 1, 0.283107, 0.417663, 0.381192, 1.5, 1, 0.277287, 0.423161, 0.379913, 1.53333, 1, 0.270827, 0.431232, 0.376875, 1.56667, 1, 0.264364, 0.440594, 0.37294, 1.6, 1, 0.258187, 0.45088, 0.368375, 1.66667, 1, 0.247766, 0.472781, 0.35834)
tracks/7/type = "rotation_3d"
tracks/7/imported = true
tracks/7/enabled = true
tracks/7/path = NodePath("rig/Skeleton3D:DEF-lid1.T.L")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = PackedFloat32Array(0, 1, 0.29664, -0.812408, 0.131526, 0.484457, 0.0333333, 1, 0.285451, -0.815954, 0.128671, 0.485984, 0.0666667, 1, 0.274653, -0.819256, 0.125363, 0.487513, 0.1, 1, 0.264601, -0.822225, 0.121801, 0.488976, 0.133333, 1, 0.255636, -0.82479, 0.11817, 0.490315, 0.166667, 1, 0.248089, -0.826896, 0.114644, 0.491479, 0.2, 1, 0.243141, -0.828288, 0.111597, 0.492308, 0.233333, 1, 0.240521, -0.829085, 0.109096, 0.492814, 0.3, 1, 0.241105, -0.829264, 0.105161, 0.493084, 0.333333, 1, 0.243676, -0.828809, 0.103392, 0.492958, 0.366667, 1, 0.247603, -0.828032, 0.101414, 0.492718, 0.4, 1, 0.25217, -0.827096, 0.0990621, 0.492452, 0.433333, 1, 0.257108, -0.826062, 0.096202, 0.492202, 0.466667, 1, 0.262133, -0.824993, 0.0927108, 0.492013, 0.5, 1, 0.266951, -0.823962, 0.0884769, 0.491931, 0.533333, 1, 0.270863, -0.823143, 0.0831097, 0.492099, 0.566667, 1, 0.273613, -0.822585, 0.0768728, 0.492525, 0.6, 1, 0.274906, -0.822345, 0.0698312, 0.493254, 0.633333, 1, 0.274523, -0.822454, 0.0621022, 0.494317, 0.666667, 1, 0.272323, -0.82292, 0.0538544, 0.495724, 0.733333, 1, 0.263768, -0.824453, 0.0375445, 0.499293, 0.766667, 1, 0.258826, -0.82519, 0.0301625, 0.501159, 0.8, 1, 0.253881, -0.825825, 0.0235761, 0.502993, 0.833333, 1, 0.249222, -0.826319, 0.0179629, 0.50474, 0.866667, 1, 0.245365, -0.826603, 0.0138935, 0.506291, 0.9, 1, 0.242251, -0.826739, 0.0110518, 0.507637, 0.933333, 1, 0.239967, -0.826744, 0.00946523, 0.508744, 0.966667, 1, 0.238631, -0.82662, 0.00916102, 0.509579, 1, 1, 0.238381, -0.826356, 0.0101662, 0.510104, 1.03333, 1, 0.240105, -0.825805, 0.0129656, 0.510126, 1.06667, 1, 0.243809, -0.824896, 0.0170577, 0.509719, 1.1, 1, 0.249323, -0.823625, 0.0223721, 0.508901, 1.13333, 1, 0.256438, -0.821988, 0.0288348, 0.507685, 1.16667, 1, 0.264905, -0.819989, 0.0363675, 0.506084, 1.2, 1, 0.274752, -0.817573, 0.0451497, 0.504031, 1.23333, 1, 0.285261, -0.81485, 0.0546903, 0.501653, 1.26667, 1, 0.296121, -0.811866, 0.0648289, 0.498982, 1.33333, 1, 0.317474, -0.805451, 0.0861248, 0.492992, 1.36667, 1, 0.326551, -0.802492, 0.0966043, 0.489937, 1.4, 1, 0.333757, -0.800022, 0.106398, 0.487085, 1.43333, 1, 0.338542, -0.798299, 0.115155, 0.484611, 1.46667, 1, 0.340451, -0.797554, 0.122552, 0.482682, 1.5, 1, 0.339111, -0.79797, 0.128295, 0.481444, 1.53333, 1, 0.333976, -0.799768, 0.131753, 0.481116, 1.56667, 1, 0.326698, -0.802313, 0.133642, 0.481354, 1.6, 1, 0.317751, -0.805398, 0.13412, 0.482058, 1.63333, 1, 0.307586, -0.808824, 0.133355, 0.483126, 1.66667, 1, 0.29664, -0.812408, 0.131526, 0.484457)
tracks/8/type = "scale_3d"
tracks/8/imported = true
tracks/8/enabled = true
tracks/8/path = NodePath("rig/Skeleton3D:DEF-lid1.T.L")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = PackedFloat32Array(0, 1, 0.892549, 1.25527, 0.892549, 0.0333333, 1, 0.878272, 1.29666, 0.878272, 0.0666667, 1, 0.865734, 1.33452, 0.865734, 0.1, 1, 0.855298, 1.36716, 0.855298, 0.133333, 1, 0.847348, 1.3928, 0.847348, 0.166667, 1, 0.842283, 1.40956, 0.842283, 0.2, 1, 0.841947, 1.41069, 0.841947, 0.233333, 1, 0.845517, 1.39885, 0.845517, 0.266667, 1, 0.852627, 1.37575, 0.852627, 0.3, 1, 0.862871, 1.34332, 0.862871, 0.333333, 1, 0.875803, 1.30373, 0.875803, 0.366667, 1, 0.891519, 1.25847, 0.891519, 0.4, 1, 0.908752, 1.21142, 0.908753, 0.466667, 1, 0.946264, 1.11715, 0.946264, 0.5, 1, 0.965799, 1.07208, 0.965799, 0.533333, 1, 0.98516, 1.03066, 0.98516, 0.566667, 1, 1.0039, 0.992637, 1.0039, 0.6, 1, 1.02168, 0.958301, 1.02168, 0.633333, 1, 1.03823, 0.927853, 1.03823, 0.666667, 1, 1.05327, 0.90141, 1.05327, 0.7, 1, 1.06629, 0.879628, 1.06629, 0.733333, 1, 1.07783, 0.860904, 1.07783, 0.766667, 1, 1.08785, 0.84507, 1.08785, 0.8, 1, 1.09634, 0.831996, 1.09634, 0.833333, 1, 1.10325, 0.821586, 1.10325, 0.866667, 1, 1.10796, 0.814632, 1.10796, 0.9, 1, 1.11094, 0.810252, 1.11094, 0.933333, 1, 1.11219, 0.808424, 1.11219, 0.966667, 1, 1.11173, 0.809096, 1.11173, 1, 1, 1.10962, 0.812185, 1.10962, 1.03333, 1, 1.10581, 0.817797, 1.10581, 1.06667, 1, 1.10154, 0.824149, 1.10154, 1.1, 1, 1.09681, 0.831279, 1.09681, 1.13333, 1, 1.09156, 0.839302, 1.09156, 1.16667, 1, 1.08567, 0.848411, 1.08567, 1.2, 1, 1.07883, 0.859229, 1.07883, 1.23333, 1, 1.07121, 0.871511, 1.07121, 1.26667, 1, 1.06279, 0.885398, 1.06279, 1.3, 1, 1.05352, 0.901041, 1.05352, 1.33333, 1, 1.04336, 0.918607, 1.04336, 1.36667, 1, 1.03195, 0.939124, 1.03195, 1.4, 1, 1.01953, 0.962217, 1.01953, 1.43333, 1, 1.00605, 0.988224, 1.00605, 1.46667, 1, 0.991465, 1.01749, 0.991465, 1.5, 1, 0.975741, 1.05034, 0.975741, 1.53333, 1, 0.959015, 1.08754, 0.959015, 1.56667, 1, 0.942011, 1.12732, 0.942011, 1.6, 1, 0.925046, 1.16906, 0.925046, 1.66667, 1, 0.892549, 1.25527, 0.892549)
tracks/9/type = "position_3d"
tracks/9/imported = true
tracks/9/enabled = true
tracks/9/path = NodePath("rig/Skeleton3D:DEF-lid1.T.R")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = PackedFloat32Array(0, 1, -0.353091, 0.400294, 0.35834, 0.0666667, 1, -0.343515, 0.428776, 0.348966, 0.1, 1, -0.338183, 0.442128, 0.345036, 0.133333, 1, -0.332729, 0.454015, 0.341817, 0.166667, 1, -0.327339, 0.463765, 0.339469, 0.2, 1, -0.322469, 0.469192, 0.338661, 0.233333, 1, -0.31828, 0.470592, 0.339244, 0.266667, 1, -0.314703, 0.468565, 0.341061, 0.3, 1, -0.311652, 0.463793, 0.343936, 0.333333, 1, -0.309023, 0.457041, 0.347672, 0.366667, 1, -0.306787, 0.448948, 0.35222, 0.4, 1, -0.304712, 0.440693, 0.357125, 0.433333, 1, -0.302696, 0.432756, 0.3622, 0.466667, 1, -0.300625, 0.425584, 0.367249, 0.5, 1, -0.298374, 0.419584, 0.372059, 0.533333, 1, -0.295599, 0.41581, 0.37607, 0.566667, 1, -0.292208, 0.4141, 0.379092, 0.6, 1, -0.288071, 0.414669, 0.380868, 0.633333, 1, -0.283107, 0.417663, 0.381192, 0.666667, 1, -0.277287, 0.423162, 0.379912, 0.7, 1, -0.270827, 0.431232, 0.376875, 0.733333, 1, -0.264364, 0.440595, 0.37294, 0.766667, 1, -0.258187, 0.45088, 0.368375, 0.833333, 1, -0.247766, 0.472781, 0.358339, 0.866667, 1, -0.244439, 0.483391, 0.353451, 0.9, 1, -0.242339, 0.493127, 0.348965, 0.933333, 1, -0.241515, 0.501564, 0.345035, 0.966667, 1, -0.242022, 0.508222, 0.341816, 1, 1, -0.243921, 0.512574, 0.339468, 1.03333, 1, -0.247856, 0.512547, 0.33866, 1.06667, 1, -0.25342, 0.508447, 0.339244, 1.1, 1, -0.260418, 0.500723, 0.341061, 1.13333, 1, -0.268637, 0.489912, 0.343935, 1.16667, 1, -0.277847, 0.476637, 0.347672, 1.2, 1, -0.287954, 0.461181, 0.35222, 1.23333, 1, -0.298413, 0.444849, 0.357125, 1.3, 1, -0.319458, 0.41154, 0.367249, 1.33333, 1, -0.329551, 0.395568, 0.372059, 1.36667, 1, -0.338614, 0.381513, 0.37607, 1.4, 1, -0.346493, 0.369791, 0.379093, 1.43333, 1, -0.352931, 0.361051, 0.380868, 1.46667, 1, -0.357721, 0.355867, 0.381192, 1.5, 1, -0.360705, 0.354746, 0.379913, 1.53333, 1, -0.361534, 0.358642, 0.376875, 1.56667, 1, -0.361032, 0.365642, 0.37294, 1.6, 1, -0.359363, 0.375257, 0.368375, 1.63333, 1, -0.356673, 0.386982, 0.36343, 1.66667, 1, -0.353091, 0.400294, 0.35834)
tracks/10/type = "rotation_3d"
tracks/10/imported = true
tracks/10/enabled = true
tracks/10/path = NodePath("rig/Skeleton3D:DEF-lid1.T.R")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = PackedFloat32Array(0, 1, 0.249223, 0.826319, -0.0179627, 0.50474, 0.0333333, 1, 0.245366, 0.826603, -0.0138925, 0.506291, 0.0666667, 1, 0.242251, 0.826739, -0.0110512, 0.507636, 0.1, 1, 0.239968, 0.826744, -0.00946489, 0.508744, 0.133333, 1, 0.238632, 0.82662, -0.00916079, 0.509579, 0.166667, 1, 0.238382, 0.826356, -0.010167, 0.510104, 0.2, 1, 0.240105, 0.825805, -0.0129651, 0.510126, 0.233333, 1, 0.243809, 0.824896, -0.0170573, 0.509719, 0.266667, 1, 0.249324, 0.823625, -0.0223722, 0.508901, 0.3, 1, 0.256439, 0.821988, -0.0288351, 0.507684, 0.333333, 1, 0.264906, 0.819989, -0.0363681, 0.506084, 0.366667, 1, 0.274753, 0.817572, -0.0451494, 0.504031, 0.4, 1, 0.285261, 0.81485, -0.0546901, 0.501653, 0.433333, 1, 0.296121, 0.811866, -0.0648287, 0.498982, 0.5, 1, 0.317475, 0.805451, -0.0861252, 0.492992, 0.533333, 1, 0.326552, 0.802492, -0.0966045, 0.489938, 0.566667, 1, 0.333757, 0.800022, -0.106398, 0.487085, 0.6, 1, 0.338542, 0.798299, -0.115155, 0.484611, 0.633333, 1, 0.340451, 0.797553, -0.122553, 0.482683, 0.666667, 1, 0.339111, 0.797969, -0.128296, 0.481444, 0.7, 1, 0.333975, 0.799768, -0.131753, 0.481116, 0.733333, 1, 0.326698, 0.802313, -0.133643, 0.481354, 0.766667, 1, 0.31775, 0.805398, -0.134121, 0.482058, 0.8, 1, 0.307586, 0.808824, -0.133356, 0.483126, 0.833333, 1, 0.29664, 0.812408, -0.131527, 0.484457, 0.866667, 1, 0.28545, 0.815954, -0.128672, 0.485984, 0.9, 1, 0.274653, 0.819256, -0.125364, 0.487513, 0.933333, 1, 0.264601, 0.822225, -0.121801, 0.488976, 0.966667, 1, 0.255635, 0.82479, -0.11817, 0.490315, 1, 1, 0.248089, 0.826896, -0.114644, 0.491479, 1.03333, 1, 0.243141, 0.828288, -0.111597, 0.492308, 1.06667, 1, 0.240521, 0.829085, -0.109096, 0.492814, 1.13333, 1, 0.241105, 0.829264, -0.10516, 0.493084, 1.16667, 1, 0.243676, 0.828809, -0.103392, 0.492958, 1.2, 1, 0.247603, 0.828032, -0.101414, 0.492718, 1.23333, 1, 0.25217, 0.827096, -0.0990623, 0.492452, 1.26667, 1, 0.257107, 0.826062, -0.0962022, 0.492202, 1.3, 1, 0.262133, 0.824993, -0.092711, 0.492013, 1.33333, 1, 0.266951, 0.823962, -0.0884771, 0.491931, 1.36667, 1, 0.270863, 0.823144, -0.0831095, 0.492099, 1.4, 1, 0.273613, 0.822585, -0.0768727, 0.492524, 1.43333, 1, 0.274906, 0.822345, -0.0698311, 0.493254, 1.46667, 1, 0.274523, 0.822455, -0.0621017, 0.494316, 1.5, 1, 0.272324, 0.82292, -0.0538538, 0.495723, 1.56667, 1, 0.263768, 0.824454, -0.0375433, 0.499293, 1.6, 1, 0.258826, 0.825191, -0.0301614, 0.501159, 1.63333, 1, 0.253881, 0.825825, -0.0235756, 0.502992, 1.66667, 1, 0.249223, 0.826319, -0.0179627, 0.50474)
tracks/11/type = "scale_3d"
tracks/11/imported = true
tracks/11/enabled = true
tracks/11/path = NodePath("rig/Skeleton3D:DEF-lid1.T.R")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = PackedFloat32Array(0, 1, 1.10325, 0.821585, 1.10325, 0.0333333, 1, 1.10796, 0.814632, 1.10796, 0.0666667, 1, 1.11094, 0.810252, 1.11094, 0.1, 1, 1.11219, 0.808424, 1.11219, 0.133333, 1, 1.11173, 0.809097, 1.11173, 0.166667, 1, 1.10962, 0.812186, 1.10962, 0.2, 1, 1.10581, 0.817798, 1.10581, 0.233333, 1, 1.10154, 0.824149, 1.10154, 0.266667, 1, 1.09681, 0.831279, 1.09681, 0.3, 1, 1.09156, 0.839302, 1.09156, 0.333333, 1, 1.08567, 0.84841, 1.08567, 0.366667, 1, 1.07883, 0.859228, 1.07883, 0.4, 1, 1.07122, 0.871511, 1.07122, 0.433333, 1, 1.06279, 0.885397, 1.06279, 0.466667, 1, 1.05352, 0.901041, 1.05352, 0.5, 1, 1.04336, 0.918607, 1.04336, 0.533333, 1, 1.03195, 0.939124, 1.03195, 0.566667, 1, 1.01953, 0.962217, 1.01953, 0.6, 1, 1.00605, 0.988225, 1.00605, 0.633333, 1, 0.991465, 1.01749, 0.991465, 0.666667, 1, 0.975741, 1.05034, 0.975741, 0.7, 1, 0.959015, 1.08754, 0.959015, 0.733333, 1, 0.942011, 1.12732, 0.942011, 0.766667, 1, 0.925046, 1.16906, 0.925047, 0.833333, 1, 0.892549, 1.25527, 0.892549, 0.866667, 1, 0.878272, 1.29666, 0.878272, 0.9, 1, 0.865733, 1.33452, 0.865733, 0.933333, 1, 0.855298, 1.36716, 0.855298, 0.966667, 1, 0.847347, 1.3928, 0.847347, 1, 1, 0.842283, 1.40956, 0.842283, 1.03333, 1, 0.841947, 1.41069, 0.841947, 1.06667, 1, 0.845517, 1.39885, 0.845517, 1.1, 1, 0.852627, 1.37575, 0.852627, 1.13333, 1, 0.862871, 1.34332, 0.862871, 1.16667, 1, 0.875802, 1.30373, 0.875802, 1.2, 1, 0.891519, 1.25847, 0.891519, 1.23333, 1, 0.908753, 1.21142, 0.908753, 1.3, 1, 0.946263, 1.11715, 0.946263, 1.33333, 1, 0.965799, 1.07208, 0.965799, 1.36667, 1, 0.98516, 1.03066, 0.98516, 1.4, 1, 1.00389, 0.992638, 1.00389, 1.43333, 1, 1.02168, 0.958302, 1.02168, 1.46667, 1, 1.03823, 0.927854, 1.03823, 1.5, 1, 1.05327, 0.90141, 1.05327, 1.53333, 1, 1.06629, 0.879627, 1.06629, 1.56667, 1, 1.07783, 0.860904, 1.07783, 1.6, 1, 1.08785, 0.845071, 1.08785, 1.63333, 1, 1.09634, 0.831996, 1.09634, 1.66667, 1, 1.10325, 0.821585, 1.10325)
tracks/12/type = "position_3d"
tracks/12/imported = true
tracks/12/enabled = true
tracks/12/path = NodePath("rig/Skeleton3D:DEF-lip.B.L")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = PackedFloat32Array(0, 1, -0.0109369, 0.090663, 0.425061, 0.0333333, 1, -0.01081, 0.0932522, 0.419263, 0.0666667, 1, -0.0105062, 0.0957335, 0.413942, 0.1, 1, -0.0100383, 0.097996, 0.40928, 0.133333, 1, -0.00941933, 0.099922, 0.405462, 0.166667, 1, -0.00866246, 0.101386, 0.402676, 0.2, 1, -0.00774809, 0.101947, 0.401718, 0.233333, 1, -0.00673529, 0.101667, 0.40241, 0.266667, 1, -0.00563716, 0.100654, 0.404565, 0.3, 1, -0.00446685, 0.099036, 0.407975, 0.333333, 1, -0.00323749, 0.0969564, 0.412407, 0.366667, 1, -0.00195562, 0.0945111, 0.417802, 0.4, 1, -0.00065406, 0.0919579, 0.42362, 0.466667, 1, 0.00195562, 0.0869301, 0.435629, 0.5, 1, 0.0032375, 0.0846484, 0.441335, 0.533333, 1, 0.00446686, 0.0827971, 0.446093, 0.566667, 1, 0.00563718, 0.0814024, 0.449678, 0.6, 1, 0.0067353, 0.0805538, 0.451784, 0.633333, 1, 0.00774804, 0.0803266, 0.452168, 0.666667, 1, 0.00866214, 0.0807813, 0.450651, 0.7, 1, 0.00941905, 0.0820238, 0.447047, 0.733333, 1, 0.0100381, 0.083723, 0.44238, 0.766667, 1, 0.0105061, 0.0857896, 0.436965, 0.8, 1, 0.0108101, 0.0881335, 0.431099, 0.833333, 1, 0.0109369, 0.0906637, 0.425061, 0.866667, 1, 0.0108101, 0.0932529, 0.419263, 0.9, 1, 0.0105062, 0.0957342, 0.413942, 0.933333, 1, 0.0100383, 0.0979967, 0.40928, 0.966667, 1, 0.00941936, 0.0999226, 0.405462, 1, 1, 0.00866248, 0.101387, 0.402676, 1.03333, 1, 0.00774811, 0.101948, 0.401718, 1.06667, 1, 0.0067353, 0.101667, 0.40241, 1.1, 1, 0.00563717, 0.100655, 0.404565, 1.13333, 1, 0.00446686, 0.0990363, 0.407975, 1.16667, 1, 0.00323749, 0.0969566, 0.412407, 1.2, 1, 0.00195562, 0.0945112, 0.417802, 1.23333, 1, 0.00065406, 0.091958, 0.42362, 1.3, 1, -0.00195562, 0.08693, 0.435629, 1.33333, 1, -0.00323749, 0.0846482, 0.441335, 1.36667, 1, -0.00446686, 0.0827967, 0.446093, 1.4, 1, -0.00563716, 0.0814019, 0.449678, 1.43333, 1, -0.00673528, 0.0805533, 0.451784, 1.46667, 1, -0.00774801, 0.0803259, 0.452169, 1.5, 1, -0.0086621, 0.0807806, 0.450651, 1.53333, 1, -0.00941902, 0.0820231, 0.447048, 1.56667, 1, -0.010038, 0.0837222, 0.44238, 1.6, 1, -0.0105061, 0.0857888, 0.436965, 1.63333, 1, -0.0108101, 0.0881327, 0.431099, 1.66667, 1, -0.0109369, 0.090663, 0.425061)
tracks/13/type = "rotation_3d"
tracks/13/imported = true
tracks/13/enabled = true
tracks/13/path = NodePath("rig/Skeleton3D:DEF-lip.B.L")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = PackedFloat32Array(0, 1, -0.392993, -0.421855, -0.52572, 0.62547, 0.0333333, 1, -0.393786, -0.421094, -0.526919, 0.624475, 0.0666667, 1, -0.394891, -0.420037, -0.528581, 0.623083, 0.1, 1, -0.396251, -0.418735, -0.530618, 0.621362, 0.133333, 1, -0.397815, -0.417234, -0.532957, 0.619368, 0.166667, 1, -0.399544, -0.415566, -0.535537, 0.617146, 0.2, 1, -0.40145, -0.41372, -0.538374, 0.614676, 0.233333, 1, -0.403485, -0.411739, -0.541394, 0.612015, 0.266667, 1, -0.405665, -0.409601, -0.544625, 0.609136, 0.3, 1, -0.408005, -0.407286, -0.548089, 0.606011, 0.333333, 1, -0.410515, -0.404775, -0.551804, 0.602617, 0.366667, 1, -0.41326, -0.401994, -0.555869, 0.598855, 0.4, 1, -0.416164, -0.39901, -0.560171, 0.594817, 0.433333, 1, -0.419201, -0.39584, -0.564676, 0.590527, 0.466667, 1, -0.422339, -0.392512, -0.569336, 0.586021, 0.533333, 1, -0.428697, -0.385593, -0.578798, 0.576653, 0.566667, 1, -0.431703, -0.382237, -0.583282, 0.57211, 0.6, 1, -0.434455, -0.379112, -0.587397, 0.56788, 0.633333, 1, -0.436862, -0.376338, -0.591002, 0.564126, 0.666667, 1, -0.438834, -0.374031, -0.593964, 0.56101, 0.7, 1, -0.440152, -0.372468, -0.595954, 0.558903, 0.733333, 1, -0.440975, -0.371477, -0.597206, 0.557575, 0.8, 1, -0.441219, -0.371146, -0.597617, 0.557163, 0.833333, 1, -0.44069, -0.37175, -0.596848, 0.558002, 0.866667, 1, -0.439671, -0.372931, -0.595344, 0.559622, 0.9, 1, -0.438381, -0.374423, -0.593434, 0.561663, 0.933333, 1, -0.436882, -0.37615, -0.591208, 0.56402, 0.966667, 1, -0.435228, -0.378044, -0.58875, 0.566598, 1, 1, -0.433471, -0.380045, -0.58613, 0.569316, 1.03333, 1, -0.431653, -0.382103, -0.583414, 0.572103, 1.06667, 1, -0.429812, -0.384177, -0.580653, 0.574902, 1.1, 1, -0.427911, -0.386302, -0.5778, 0.577763, 1.13333, 1, -0.425918, -0.388515, -0.574802, 0.580735, 1.16667, 1, -0.423794, -0.39085, -0.571607, 0.583866, 1.2, 1, -0.421455, -0.393394, -0.568089, 0.587275, 1.23333, 1, -0.418933, -0.396102, -0.5643, 0.590901, 1.26667, 1, -0.416231, -0.398962, -0.560247, 0.59473, 1.3, 1, -0.413364, -0.401953, -0.555952, 0.598734, 1.33333, 1, -0.410355, -0.405043, -0.551452, 0.602868, 1.36667, 1, -0.407267, -0.408162, -0.546842, 0.607043, 1.4, 1, -0.404231, -0.411181, -0.542315, 0.611083, 1.43333, 1, -0.401346, -0.414003, -0.538023, 0.614861, 1.46667, 1, -0.398718, -0.416536, -0.53412, 0.618254, 1.5, 1, -0.396452, -0.418689, -0.530762, 0.621143, 1.53333, 1, -0.394796, -0.42024, -0.528318, 0.62323, 1.56667, 1, -0.39363, -0.421318, -0.526603, 0.624689, 1.6, 1, -0.392949, -0.421936, -0.525612, 0.625534, 1.66667, 1, -0.392993, -0.421855, -0.52572, 0.62547)
tracks/14/type = "scale_3d"
tracks/14/imported = true
tracks/14/enabled = true
tracks/14/path = NodePath("rig/Skeleton3D:DEF-lip.B.L")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = PackedFloat32Array(0, 1, 1.00047, 0.999066, 1.00047, 0.0333333, 1, 1.00737, 0.985454, 1.00737, 0.0666667, 1, 1.01382, 0.972962, 1.01382, 0.1, 1, 1.01956, 0.962018, 1.01956, 0.133333, 1, 1.02434, 0.953056, 1.02434, 0.166667, 1, 1.02786, 0.946517, 1.02786, 0.2, 1, 1.02909, 0.944268, 1.02909, 0.233333, 1, 1.0282, 0.945893, 1.0282, 0.266667, 1, 1.02547, 0.950952, 1.02547, 0.3, 1, 1.02119, 0.958955, 1.02119, 0.333333, 1, 1.01568, 0.96936, 1.01568, 0.366667, 1, 1.00913, 0.982025, 1.00913, 0.4, 1, 1.00219, 0.995683, 1.00219, 0.433333, 1, 0.995154, 1.00982, 0.995154, 0.466667, 1, 0.988285, 1.02388, 0.988285, 0.5, 1, 0.981866, 1.03728, 0.981866, 0.533333, 1, 0.97663, 1.04845, 0.97663, 0.566667, 1, 0.972729, 1.05687, 0.972729, 0.6, 1, 0.970454, 1.06182, 0.970454, 0.633333, 1, 0.970042, 1.06272, 0.970042, 0.666667, 1, 0.971672, 1.05916, 0.971672, 0.7, 1, 0.975583, 1.05069, 0.975583, 0.733333, 1, 0.980724, 1.03973, 0.980724, 0.766667, 1, 0.986783, 1.02702, 0.986783, 0.8, 1, 0.993461, 1.01324, 0.993461, 0.833333, 1, 1.00047, 0.999066, 1.00047, 0.866667, 1, 1.00737, 0.985454, 1.00737, 0.9, 1, 1.01382, 0.972962, 1.01382, 0.933333, 1, 1.01956, 0.962018, 1.01956, 0.966667, 1, 1.02434, 0.953056, 1.02434, 1, 1, 1.02786, 0.946517, 1.02786, 1.03333, 1, 1.02909, 0.944268, 1.02909, 1.06667, 1, 1.0282, 0.945893, 1.0282, 1.1, 1, 1.02547, 0.950952, 1.02547, 1.13333, 1, 1.02119, 0.958955, 1.02119, 1.16667, 1, 1.01568, 0.96936, 1.01568, 1.2, 1, 1.00913, 0.982025, 1.00913, 1.23333, 1, 1.00219, 0.995683, 1.00219, 1.26667, 1, 0.995154, 1.00982, 0.995154, 1.3, 1, 0.988285, 1.02388, 0.988285, 1.33333, 1, 0.981866, 1.03728, 0.981866, 1.36667, 1, 0.97663, 1.04845, 0.97663, 1.4, 1, 0.972729, 1.05687, 0.972729, 1.43333, 1, 0.970454, 1.06182, 0.970454, 1.46667, 1, 0.970042, 1.06272, 0.970042, 1.5, 1, 0.971672, 1.05916, 0.971672, 1.53333, 1, 0.975583, 1.05069, 0.975583, 1.56667, 1, 0.980724, 1.03973, 0.980724, 1.6, 1, 0.986783, 1.02702, 0.986783, 1.63333, 1, 0.993461, 1.01324, 0.993461, 1.66667, 1, 1.00047, 0.999066, 1.00047)
tracks/15/type = "position_3d"
tracks/15/imported = true
tracks/15/enabled = true
tracks/15/path = NodePath("rig/Skeleton3D:DEF-lip.B.R")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = PackedFloat32Array(0, 1, -0.0109369, 0.090663, 0.425061, 0.0333333, 1, -0.01081, 0.0932522, 0.419263, 0.0666667, 1, -0.0105062, 0.0957335, 0.413942, 0.1, 1, -0.0100383, 0.097996, 0.40928, 0.133333, 1, -0.00941933, 0.099922, 0.405462, 0.166667, 1, -0.00866246, 0.101386, 0.402676, 0.2, 1, -0.00774809, 0.101947, 0.401718, 0.233333, 1, -0.00673529, 0.101667, 0.40241, 0.266667, 1, -0.00563716, 0.100654, 0.404565, 0.3, 1, -0.00446685, 0.099036, 0.407975, 0.333333, 1, -0.00323749, 0.0969564, 0.412407, 0.366667, 1, -0.00195562, 0.0945111, 0.417802, 0.4, 1, -0.00065406, 0.0919579, 0.42362, 0.466667, 1, 0.00195562, 0.0869301, 0.435629, 0.5, 1, 0.0032375, 0.0846484, 0.441335, 0.533333, 1, 0.00446686, 0.0827971, 0.446093, 0.566667, 1, 0.00563718, 0.0814024, 0.449678, 0.6, 1, 0.0067353, 0.0805538, 0.451784, 0.633333, 1, 0.00774804, 0.0803266, 0.452168, 0.666667, 1, 0.00866214, 0.0807813, 0.450651, 0.7, 1, 0.00941905, 0.0820238, 0.447047, 0.733333, 1, 0.0100381, 0.083723, 0.44238, 0.766667, 1, 0.0105061, 0.0857896, 0.436965, 0.8, 1, 0.0108101, 0.0881335, 0.431099, 0.833333, 1, 0.0109369, 0.0906637, 0.425061, 0.866667, 1, 0.0108101, 0.0932529, 0.419263, 0.9, 1, 0.0105062, 0.0957342, 0.413942, 0.933333, 1, 0.0100383, 0.0979967, 0.40928, 0.966667, 1, 0.00941936, 0.0999226, 0.405462, 1, 1, 0.00866248, 0.101387, 0.402676, 1.03333, 1, 0.00774811, 0.101948, 0.401718, 1.06667, 1, 0.0067353, 0.101667, 0.40241, 1.1, 1, 0.00563717, 0.100655, 0.404565, 1.13333, 1, 0.00446686, 0.0990363, 0.407975, 1.16667, 1, 0.00323749, 0.0969566, 0.412407, 1.2, 1, 0.00195562, 0.0945112, 0.417802, 1.23333, 1, 0.00065406, 0.091958, 0.42362, 1.3, 1, -0.00195562, 0.08693, 0.435629, 1.33333, 1, -0.00323749, 0.0846482, 0.441335, 1.36667, 1, -0.00446686, 0.0827967, 0.446093, 1.4, 1, -0.00563716, 0.0814019, 0.449678, 1.43333, 1, -0.00673528, 0.0805533, 0.451784, 1.46667, 1, -0.00774801, 0.0803259, 0.452169, 1.5, 1, -0.0086621, 0.0807806, 0.450651, 1.53333, 1, -0.00941902, 0.0820231, 0.447048, 1.56667, 1, -0.010038, 0.0837222, 0.44238, 1.6, 1, -0.0105061, 0.0857888, 0.436965, 1.63333, 1, -0.0108101, 0.0881327, 0.431099, 1.66667, 1, -0.0109369, 0.090663, 0.425061)
tracks/16/type = "rotation_3d"
tracks/16/imported = true
tracks/16/enabled = true
tracks/16/path = NodePath("rig/Skeleton3D:DEF-lip.B.R")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = PackedFloat32Array(0, 1, -0.44069, 0.371749, 0.596849, 0.558002, 0.0333333, 1, -0.43967, 0.372931, 0.595345, 0.559623, 0.0666667, 1, -0.43838, 0.374423, 0.593434, 0.561664, 0.1, 1, -0.436881, 0.37615, 0.591209, 0.56402, 0.133333, 1, -0.435228, 0.378044, 0.58875, 0.566598, 0.166667, 1, -0.433471, 0.380044, 0.586131, 0.569316, 0.233333, 1, -0.429811, 0.384176, 0.580654, 0.574902, 0.266667, 1, -0.427911, 0.386302, 0.5778, 0.577763, 0.3, 1, -0.425917, 0.388515, 0.574802, 0.580736, 0.333333, 1, -0.423794, 0.39085, 0.571607, 0.583867, 0.366667, 1, -0.421455, 0.393394, 0.568089, 0.587275, 0.4, 1, -0.418933, 0.396102, 0.5643, 0.590901, 0.433333, 1, -0.416231, 0.398962, 0.560247, 0.59473, 0.466667, 1, -0.413364, 0.401953, 0.555951, 0.598734, 0.5, 1, -0.410355, 0.405043, 0.551451, 0.602868, 0.533333, 1, -0.407267, 0.408163, 0.546841, 0.607043, 0.566667, 1, -0.404231, 0.411181, 0.542315, 0.611083, 0.6, 1, -0.401347, 0.414003, 0.538023, 0.614861, 0.633333, 1, -0.398718, 0.416536, 0.53412, 0.618254, 0.666667, 1, -0.396452, 0.418689, 0.530761, 0.621142, 0.7, 1, -0.394797, 0.42024, 0.528317, 0.623229, 0.733333, 1, -0.39363, 0.421319, 0.526603, 0.624688, 0.766667, 1, -0.39295, 0.421937, 0.525612, 0.625534, 0.833333, 1, -0.392993, 0.421856, 0.52572, 0.62547, 0.866667, 1, -0.393787, 0.421094, 0.526919, 0.624474, 0.9, 1, -0.394892, 0.420037, 0.528581, 0.623083, 0.933333, 1, -0.396251, 0.418736, 0.530618, 0.621362, 0.966667, 1, -0.397815, 0.417234, 0.532957, 0.619368, 1, 1, -0.399544, 0.415566, 0.535537, 0.617146, 1.03333, 1, -0.401451, 0.41372, 0.538374, 0.614676, 1.06667, 1, -0.403485, 0.411739, 0.541394, 0.612015, 1.1, 1, -0.405665, 0.409601, 0.544625, 0.609135, 1.13333, 1, -0.408005, 0.407286, 0.548089, 0.60601, 1.16667, 1, -0.410514, 0.404775, 0.551804, 0.602617, 1.2, 1, -0.41326, 0.401994, 0.555869, 0.598855, 1.23333, 1, -0.416164, 0.39901, 0.560171, 0.594817, 1.26667, 1, -0.419201, 0.39584, 0.564676, 0.590527, 1.3, 1, -0.422339, 0.392512, 0.569336, 0.586021, 1.36667, 1, -0.428697, 0.385593, 0.578799, 0.576653, 1.4, 1, -0.431702, 0.382237, 0.583283, 0.57211, 1.43333, 1, -0.434455, 0.379112, 0.587397, 0.567881, 1.46667, 1, -0.436862, 0.376337, 0.591002, 0.564127, 1.5, 1, -0.438834, 0.374031, 0.593965, 0.56101, 1.53333, 1, -0.440152, 0.372467, 0.595954, 0.558904, 1.56667, 1, -0.440975, 0.371476, 0.597207, 0.557576, 1.63333, 1, -0.441219, 0.371146, 0.597617, 0.557163, 1.66667, 1, -0.44069, 0.371749, 0.596849, 0.558002)
tracks/17/type = "scale_3d"
tracks/17/imported = true
tracks/17/enabled = true
tracks/17/path = NodePath("rig/Skeleton3D:DEF-lip.B.R")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = PackedFloat32Array(0, 1, 1.00047, 0.999066, 1.00047, 0.0333333, 1, 1.00737, 0.985454, 1.00737, 0.0666667, 1, 1.01382, 0.972962, 1.01382, 0.1, 1, 1.01956, 0.962018, 1.01956, 0.133333, 1, 1.02434, 0.953056, 1.02434, 0.166667, 1, 1.02786, 0.946517, 1.02786, 0.2, 1, 1.02909, 0.944268, 1.02909, 0.233333, 1, 1.0282, 0.945893, 1.0282, 0.266667, 1, 1.02547, 0.950952, 1.02547, 0.3, 1, 1.02119, 0.958955, 1.02119, 0.333333, 1, 1.01568, 0.969359, 1.01568, 0.366667, 1, 1.00913, 0.982025, 1.00913, 0.4, 1, 1.00219, 0.995683, 1.00219, 0.433333, 1, 0.995154, 1.00982, 0.995154, 0.466667, 1, 0.988285, 1.02388, 0.988285, 0.5, 1, 0.981866, 1.03728, 0.981866, 0.533333, 1, 0.97663, 1.04845, 0.97663, 0.566667, 1, 0.972729, 1.05687, 0.972729, 0.6, 1, 0.970454, 1.06182, 0.970454, 0.633333, 1, 0.970043, 1.06272, 0.970043, 0.666667, 1, 0.971672, 1.05916, 0.971672, 0.7, 1, 0.975583, 1.05069, 0.975583, 0.733333, 1, 0.980724, 1.03973, 0.980724, 0.766667, 1, 0.986784, 1.02702, 0.986783, 0.8, 1, 0.993461, 1.01324, 0.993461, 0.833333, 1, 1.00047, 0.999067, 1.00047, 0.866667, 1, 1.00737, 0.985454, 1.00737, 0.9, 1, 1.01382, 0.972962, 1.01382, 0.933333, 1, 1.01956, 0.962018, 1.01956, 0.966667, 1, 1.02434, 0.953056, 1.02434, 1, 1, 1.02786, 0.946517, 1.02786, 1.03333, 1, 1.02909, 0.944268, 1.02909, 1.06667, 1, 1.0282, 0.945893, 1.0282, 1.1, 1, 1.02547, 0.950952, 1.02547, 1.13333, 1, 1.02119, 0.958955, 1.02119, 1.16667, 1, 1.01568, 0.96936, 1.01568, 1.2, 1, 1.00913, 0.982025, 1.00913, 1.23333, 1, 1.00219, 0.995683, 1.00219, 1.26667, 1, 0.995154, 1.00982, 0.995154, 1.3, 1, 0.988285, 1.02388, 0.988285, 1.33333, 1, 0.981866, 1.03728, 0.981866, 1.36667, 1, 0.97663, 1.04845, 0.97663, 1.4, 1, 0.972729, 1.05687, 0.972729, 1.43333, 1, 0.970455, 1.06182, 0.970454, 1.46667, 1, 0.970043, 1.06272, 0.970043, 1.5, 1, 0.971672, 1.05916, 0.971672, 1.53333, 1, 0.975583, 1.05069, 0.975583, 1.56667, 1, 0.980724, 1.03973, 0.980724, 1.6, 1, 0.986784, 1.02702, 0.986783, 1.63333, 1, 0.993461, 1.01324, 0.993461, 1.66667, 1, 1.00047, 0.999066, 1.00047)
tracks/18/type = "position_3d"
tracks/18/imported = true
tracks/18/enabled = true
tracks/18/path = NodePath("rig/Skeleton3D:DEF-lip.T.L")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = PackedFloat32Array(0, 1, -0.030955, 0.256597, 0.479213, 0.0333333, 1, -0.030596, 0.263925, 0.472676, 0.0666667, 1, -0.0297355, 0.270948, 0.466677, 0.1, 1, -0.0284108, 0.277351, 0.461421, 0.133333, 1, -0.0266588, 0.282802, 0.457117, 0.166667, 1, -0.0245167, 0.286946, 0.453976, 0.2, 1, -0.0219288, 0.288533, 0.452896, 0.233333, 1, -0.0190624, 0.28774, 0.453676, 0.266667, 1, -0.0159544, 0.284874, 0.456106, 0.3, 1, -0.0126422, 0.280294, 0.45995, 0.333333, 1, -0.00916281, 0.274408, 0.464947, 0.366667, 1, -0.00553483, 0.267487, 0.471029, 0.4, 1, -0.00185113, 0.260261, 0.477588, 0.433333, 1, 0.00185114, 0.253017, 0.484376, 0.466667, 1, 0.00553483, 0.246031, 0.491127, 0.5, 1, 0.00916282, 0.239574, 0.49756, 0.533333, 1, 0.0126422, 0.234334, 0.502924, 0.566667, 1, 0.0159544, 0.230386, 0.506966, 0.6, 1, 0.0190624, 0.227985, 0.50934, 0.633333, 1, 0.0219288, 0.227341, 0.509773, 0.666667, 1, 0.0245165, 0.228628, 0.508062, 0.7, 1, 0.0266586, 0.232145, 0.504, 0.733333, 1, 0.0284107, 0.236954, 0.498738, 0.766667, 1, 0.0297355, 0.242803, 0.492633, 0.8, 1, 0.030596, 0.249437, 0.48602, 0.866667, 1, 0.030596, 0.263926, 0.472676, 0.9, 1, 0.0297356, 0.270948, 0.466677, 0.933333, 1, 0.0284108, 0.277352, 0.461421, 0.966667, 1, 0.0266588, 0.282802, 0.457116, 1, 1, 0.0245167, 0.286947, 0.453976, 1.03333, 1, 0.0219288, 0.288534, 0.452895, 1.06667, 1, 0.0190624, 0.28774, 0.453676, 1.1, 1, 0.0159544, 0.284875, 0.456106, 1.13333, 1, 0.0126422, 0.280295, 0.45995, 1.16667, 1, 0.00916281, 0.274408, 0.464947, 1.2, 1, 0.00553483, 0.267487, 0.471029, 1.23333, 1, 0.00185113, 0.260261, 0.477588, 1.26667, 1, -0.00185114, 0.253017, 0.484376, 1.3, 1, -0.00553483, 0.246031, 0.491127, 1.33333, 1, -0.00916281, 0.239573, 0.49756, 1.36667, 1, -0.0126422, 0.234333, 0.502924, 1.4, 1, -0.0159544, 0.230386, 0.506966, 1.43333, 1, -0.0190624, 0.227984, 0.50934, 1.46667, 1, -0.0219288, 0.227341, 0.509774, 1.5, 1, -0.0245164, 0.228627, 0.508063, 1.53333, 1, -0.0266586, 0.232144, 0.504, 1.56667, 1, -0.0284106, 0.236953, 0.498738, 1.6, 1, -0.0297355, 0.242802, 0.492633, 1.63333, 1, -0.030596, 0.249436, 0.48602, 1.66667, 1, -0.030955, 0.256597, 0.479213)
tracks/19/type = "rotation_3d"
tracks/19/imported = true
tracks/19/enabled = true
tracks/19/path = NodePath("rig/Skeleton3D:DEF-lip.T.L")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = PackedFloat32Array(0, 1, -0.498601, -0.501998, -0.466081, 0.53119, 0.0333333, 1, -0.500333, -0.500339, -0.467851, 0.529569, 0.0666667, 1, -0.502402, -0.498327, -0.46998, 0.527619, 0.1, 1, -0.504702, -0.496056, -0.472361, 0.525432, 0.133333, 1, -0.507138, -0.493616, -0.474894, 0.523096, 0.166667, 1, -0.509622, -0.491091, -0.47749, 0.520691, 0.2, 1, -0.512028, -0.488595, -0.480029, 0.518337, 0.233333, 1, -0.514321, -0.486171, -0.482471, 0.516075, 0.266667, 1, -0.516559, -0.483762, -0.484877, 0.513844, 0.3, 1, -0.518805, -0.481305, -0.487307, 0.511584, 0.333333, 1, -0.521123, -0.478735, -0.489828, 0.509227, 0.366667, 1, -0.52363, -0.475922, -0.492561, 0.506649, 0.4, 1, -0.526314, -0.472881, -0.495488, 0.503855, 0.433333, 1, -0.529176, -0.469605, -0.498606, 0.500835, 0.466667, 1, -0.532207, -0.4661, -0.501903, 0.497594, 0.5, 1, -0.535381, -0.46239, -0.505352, 0.494149, 0.533333, 1, -0.538653, -0.458525, -0.508896, 0.490543, 0.566667, 1, -0.541874, -0.454678, -0.512378, 0.486939, 0.6, 1, -0.544946, -0.450971, -0.51569, 0.483449, 0.633333, 1, -0.547773, -0.447529, -0.518725, 0.480195, 0.666667, 1, -0.550258, -0.444485, -0.52138, 0.477297, 0.7, 1, -0.552167, -0.442148, -0.523395, 0.475052, 0.733333, 1, -0.553605, -0.440397, -0.524892, 0.473351, 0.766667, 1, -0.554573, -0.439237, -0.525876, 0.472201, 0.8, 1, -0.55508, -0.438665, -0.526356, 0.471602, 0.833333, 1, -0.555137, -0.438667, -0.526347, 0.471543, 0.866667, 1, -0.554636, -0.439376, -0.525737, 0.472152, 0.9, 1, -0.553794, -0.440511, -0.524763, 0.473166, 0.933333, 1, -0.552652, -0.44201, -0.523473, 0.47453, 0.966667, 1, -0.55124, -0.443826, -0.521905, 0.476202, 1, 1, -0.549579, -0.445924, -0.520083, 0.478152, 1.03333, 1, -0.547588, -0.448381, -0.517935, 0.480463, 1.06667, 1, -0.545324, -0.451122, -0.515519, 0.483064, 1.1, 1, -0.542774, -0.454153, -0.512821, 0.485958, 1.13333, 1, -0.539936, -0.457474, -0.509833, 0.489139, 1.16667, 1, -0.536809, -0.461077, -0.50655, 0.492597, 1.2, 1, -0.533324, -0.465032, -0.502896, 0.496393, 1.23333, 1, -0.529594, -0.469204, -0.498985, 0.500392, 1.26667, 1, -0.525652, -0.473547, -0.494849, 0.504548, 1.3, 1, -0.521541, -0.478005, -0.490534, 0.508806, 1.33333, 1, -0.517317, -0.482513, -0.486097, 0.513102, 1.36667, 1, -0.513127, -0.486921, -0.481688, 0.517286, 1.4, 1, -0.509127, -0.491068, -0.477472, 0.521212, 1.43333, 1, -0.505464, -0.49482, -0.473603, 0.524748, 1.46667, 1, -0.502283, -0.498045, -0.470234, 0.527772, 1.5, 1, -0.499729, -0.500621, -0.467516, 0.530168, 1.53333, 1, -0.498133, -0.502241, -0.465796, 0.53165, 1.56667, 1, -0.497257, -0.503152, -0.464827, 0.532456, 1.63333, 1, -0.497535, -0.502989, -0.465015, 0.532185, 1.66667, 1, -0.498601, -0.501998, -0.466081, 0.53119)
tracks/20/type = "scale_3d"
tracks/20/imported = true
tracks/20/enabled = true
tracks/20/path = NodePath("rig/Skeleton3D:DEF-lip.T.L")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = PackedFloat32Array(0, 1, 1.00046, 0.999072, 1.00046, 0.0333333, 1, 1.00732, 0.985557, 1.00732, 0.0666667, 1, 1.01372, 0.973161, 1.01372, 0.1, 1, 1.01941, 0.962305, 1.01941, 0.133333, 1, 1.02414, 0.953419, 1.02414, 0.166667, 1, 1.02764, 0.946939, 1.02764, 0.2, 1, 1.02885, 0.94471, 1.02885, 0.233333, 1, 1.02797, 0.94632, 1.02797, 0.266667, 1, 1.02527, 0.951334, 1.02527, 0.3, 1, 1.02102, 0.959268, 1.02102, 0.333333, 1, 1.01556, 0.969586, 1.01556, 0.366667, 1, 1.00906, 0.982154, 1.00906, 0.4, 1, 1.00218, 0.995714, 1.00218, 0.433333, 1, 0.995185, 1.00975, 0.995185, 0.466667, 1, 0.988358, 1.02373, 0.988358, 0.5, 1, 0.981976, 1.03705, 0.981976, 0.533333, 1, 0.976767, 1.04816, 0.976767, 0.566667, 1, 0.972885, 1.05653, 0.972885, 0.6, 1, 0.970621, 1.06145, 0.970621, 0.633333, 1, 0.970211, 1.06235, 0.970211, 0.666667, 1, 0.971833, 1.05881, 0.971833, 0.7, 1, 0.975726, 1.05039, 0.975726, 0.733333, 1, 0.98084, 1.03949, 0.98084, 0.766667, 1, 0.986866, 1.02685, 0.986866, 0.8, 1, 0.993503, 1.01316, 0.993504, 0.833333, 1, 1.00046, 0.999073, 1.00046, 0.866667, 1, 1.00732, 0.985558, 1.00732, 0.9, 1, 1.01372, 0.973161, 1.01372, 0.933333, 1, 1.01941, 0.962305, 1.01941, 0.966667, 1, 1.02414, 0.953419, 1.02414, 1, 1, 1.02764, 0.946938, 1.02764, 1.03333, 1, 1.02885, 0.94471, 1.02885, 1.06667, 1, 1.02797, 0.94632, 1.02797, 1.1, 1, 1.02527, 0.951334, 1.02527, 1.13333, 1, 1.02102, 0.959268, 1.02102, 1.16667, 1, 1.01556, 0.969586, 1.01556, 1.2, 1, 1.00906, 0.982154, 1.00906, 1.23333, 1, 1.00218, 0.995714, 1.00218, 1.26667, 1, 0.995185, 1.00975, 0.995185, 1.3, 1, 0.988358, 1.02373, 0.988358, 1.33333, 1, 0.981976, 1.03705, 0.981976, 1.36667, 1, 0.976767, 1.04816, 0.976767, 1.4, 1, 0.972885, 1.05653, 0.972885, 1.43333, 1, 0.970621, 1.06145, 0.970621, 1.46667, 1, 0.970211, 1.06235, 0.970211, 1.5, 1, 0.971833, 1.05881, 0.971834, 1.53333, 1, 0.975726, 1.05039, 0.975726, 1.56667, 1, 0.98084, 1.03949, 0.98084, 1.6, 1, 0.986865, 1.02685, 0.986866, 1.63333, 1, 0.993503, 1.01316, 0.993504, 1.66667, 1, 1.00046, 0.999072, 1.00046)
tracks/21/type = "position_3d"
tracks/21/imported = true
tracks/21/enabled = true
tracks/21/path = NodePath("rig/Skeleton3D:DEF-lip.T.R")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = PackedFloat32Array(0, 1, -0.030955, 0.256597, 0.479213, 0.0333333, 1, -0.030596, 0.263925, 0.472676, 0.0666667, 1, -0.0297355, 0.270948, 0.466677, 0.1, 1, -0.0284108, 0.277351, 0.461421, 0.133333, 1, -0.0266588, 0.282802, 0.457117, 0.166667, 1, -0.0245167, 0.286946, 0.453976, 0.2, 1, -0.0219288, 0.288533, 0.452896, 0.233333, 1, -0.0190624, 0.28774, 0.453676, 0.266667, 1, -0.0159544, 0.284874, 0.456106, 0.3, 1, -0.0126422, 0.280294, 0.45995, 0.333333, 1, -0.00916281, 0.274408, 0.464947, 0.366667, 1, -0.00553483, 0.267487, 0.471029, 0.4, 1, -0.00185113, 0.260261, 0.477588, 0.433333, 1, 0.00185114, 0.253017, 0.484376, 0.466667, 1, 0.00553483, 0.246031, 0.491127, 0.5, 1, 0.00916282, 0.239574, 0.49756, 0.533333, 1, 0.0126422, 0.234334, 0.502924, 0.566667, 1, 0.0159544, 0.230386, 0.506966, 0.6, 1, 0.0190624, 0.227985, 0.50934, 0.633333, 1, 0.0219288, 0.227341, 0.509773, 0.666667, 1, 0.0245165, 0.228628, 0.508062, 0.7, 1, 0.0266586, 0.232145, 0.504, 0.733333, 1, 0.0284107, 0.236954, 0.498738, 0.766667, 1, 0.0297355, 0.242803, 0.492633, 0.8, 1, 0.030596, 0.249437, 0.48602, 0.866667, 1, 0.030596, 0.263926, 0.472676, 0.9, 1, 0.0297356, 0.270948, 0.466677, 0.933333, 1, 0.0284108, 0.277352, 0.461421, 0.966667, 1, 0.0266588, 0.282802, 0.457116, 1, 1, 0.0245167, 0.286947, 0.453976, 1.03333, 1, 0.0219288, 0.288534, 0.452895, 1.06667, 1, 0.0190624, 0.28774, 0.453676, 1.1, 1, 0.0159544, 0.284875, 0.456106, 1.13333, 1, 0.0126422, 0.280295, 0.45995, 1.16667, 1, 0.00916281, 0.274408, 0.464947, 1.2, 1, 0.00553483, 0.267487, 0.471029, 1.23333, 1, 0.00185113, 0.260261, 0.477588, 1.26667, 1, -0.00185114, 0.253017, 0.484376, 1.3, 1, -0.00553483, 0.246031, 0.491127, 1.33333, 1, -0.00916281, 0.239573, 0.49756, 1.36667, 1, -0.0126422, 0.234333, 0.502924, 1.4, 1, -0.0159544, 0.230386, 0.506966, 1.43333, 1, -0.0190624, 0.227984, 0.50934, 1.46667, 1, -0.0219288, 0.227341, 0.509774, 1.5, 1, -0.0245164, 0.228627, 0.508063, 1.53333, 1, -0.0266586, 0.232144, 0.504, 1.56667, 1, -0.0284106, 0.236953, 0.498738, 1.6, 1, -0.0297355, 0.242802, 0.492633, 1.63333, 1, -0.030596, 0.249436, 0.48602, 1.66667, 1, -0.030955, 0.256597, 0.479213)
tracks/22/type = "rotation_3d"
tracks/22/imported = true
tracks/22/enabled = true
tracks/22/path = NodePath("rig/Skeleton3D:DEF-lip.T.R")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = PackedFloat32Array(0, 1, -0.555136, 0.438667, 0.526347, 0.471543, 0.0333333, 1, -0.554636, 0.439376, 0.525737, 0.472153, 0.0666667, 1, -0.553794, 0.44051, 0.524763, 0.473166, 0.1, 1, -0.552651, 0.44201, 0.523474, 0.474531, 0.133333, 1, -0.55124, 0.443826, 0.521905, 0.476203, 0.166667, 1, -0.549579, 0.445924, 0.520083, 0.478152, 0.2, 1, -0.547588, 0.448381, 0.517935, 0.480464, 0.233333, 1, -0.545323, 0.451122, 0.515519, 0.483065, 0.266667, 1, -0.542774, 0.454153, 0.512821, 0.485958, 0.3, 1, -0.539936, 0.457474, 0.509833, 0.489139, 0.333333, 1, -0.536809, 0.461077, 0.506549, 0.492597, 0.366667, 1, -0.533325, 0.465032, 0.502896, 0.496393, 0.4, 1, -0.529594, 0.469204, 0.498985, 0.500392, 0.433333, 1, -0.525652, 0.473547, 0.494849, 0.504548, 0.466667, 1, -0.521541, 0.478005, 0.490534, 0.508806, 0.5, 1, -0.517318, 0.482513, 0.486097, 0.513102, 0.533333, 1, -0.513128, 0.486921, 0.481687, 0.517286, 0.566667, 1, -0.509128, 0.491068, 0.477472, 0.521212, 0.6, 1, -0.505464, 0.49482, 0.473603, 0.524747, 0.633333, 1, -0.502283, 0.498046, 0.470234, 0.527772, 0.666667, 1, -0.499729, 0.500621, 0.467516, 0.530168, 0.7, 1, -0.498133, 0.502241, 0.465796, 0.531649, 0.733333, 1, -0.497257, 0.503153, 0.464826, 0.532456, 0.8, 1, -0.497536, 0.50299, 0.465015, 0.532185, 0.833333, 1, -0.498601, 0.501999, 0.466081, 0.53119, 0.866667, 1, -0.500334, 0.500339, 0.46785, 0.529569, 0.9, 1, -0.502402, 0.498327, 0.46998, 0.527619, 0.933333, 1, -0.504703, 0.496057, 0.47236, 0.525432, 0.966667, 1, -0.507139, 0.493617, 0.474893, 0.523096, 1, 1, -0.509622, 0.491091, 0.47749, 0.52069, 1.03333, 1, -0.512029, 0.488595, 0.480029, 0.518337, 1.06667, 1, -0.514321, 0.486171, 0.482471, 0.516075, 1.1, 1, -0.516559, 0.483762, 0.484876, 0.513844, 1.13333, 1, -0.518805, 0.481305, 0.487307, 0.511584, 1.16667, 1, -0.521123, 0.478735, 0.489828, 0.509227, 1.2, 1, -0.52363, 0.475922, 0.492561, 0.506649, 1.23333, 1, -0.526314, 0.472881, 0.495488, 0.503855, 1.26667, 1, -0.529176, 0.469605, 0.498606, 0.500835, 1.3, 1, -0.532207, 0.4661, 0.501903, 0.497594, 1.33333, 1, -0.53538, 0.46239, 0.505352, 0.494149, 1.36667, 1, -0.538652, 0.458525, 0.508897, 0.490543, 1.4, 1, -0.541873, 0.454678, 0.512379, 0.486939, 1.43333, 1, -0.544946, 0.45097, 0.51569, 0.48345, 1.46667, 1, -0.547773, 0.447529, 0.518726, 0.480195, 1.5, 1, -0.550258, 0.444484, 0.52138, 0.477298, 1.53333, 1, -0.552167, 0.442147, 0.523395, 0.475053, 1.56667, 1, -0.553604, 0.440396, 0.524893, 0.473351, 1.6, 1, -0.554573, 0.439236, 0.525877, 0.472202, 1.63333, 1, -0.55508, 0.438664, 0.526356, 0.471603, 1.66667, 1, -0.555136, 0.438667, 0.526347, 0.471543)
tracks/23/type = "scale_3d"
tracks/23/imported = true
tracks/23/enabled = true
tracks/23/path = NodePath("rig/Skeleton3D:DEF-lip.T.R")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = PackedFloat32Array(0, 1, 1.00046, 0.999073, 1.00046, 0.0333333, 1, 1.00732, 0.985558, 1.00732, 0.0666667, 1, 1.01372, 0.973161, 1.01372, 0.1, 1, 1.01941, 0.962305, 1.01941, 0.133333, 1, 1.02414, 0.953419, 1.02414, 0.166667, 1, 1.02764, 0.946938, 1.02764, 0.2, 1, 1.02885, 0.94471, 1.02885, 0.233333, 1, 1.02797, 0.94632, 1.02797, 0.266667, 1, 1.02527, 0.951334, 1.02527, 0.3, 1, 1.02102, 0.959268, 1.02102, 0.333333, 1, 1.01556, 0.969586, 1.01556, 0.366667, 1, 1.00906, 0.982155, 1.00906, 0.4, 1, 1.00218, 0.995715, 1.00218, 0.433333, 1, 0.995185, 1.00976, 0.995185, 0.466667, 1, 0.988359, 1.02373, 0.988359, 0.5, 1, 0.981976, 1.03705, 0.981976, 0.533333, 1, 0.976768, 1.04816, 0.976768, 0.566667, 1, 0.972885, 1.05653, 0.972885, 0.6, 1, 0.970621, 1.06145, 0.970621, 0.633333, 1, 0.970212, 1.06235, 0.970211, 0.666667, 1, 0.971834, 1.05881, 0.971834, 0.7, 1, 0.975726, 1.05039, 0.975726, 0.733333, 1, 0.98084, 1.03949, 0.98084, 0.766667, 1, 0.986866, 1.02685, 0.986866, 0.8, 1, 0.993504, 1.01316, 0.993504, 0.833333, 1, 1.00046, 0.999073, 1.00046, 0.866667, 1, 1.00732, 0.985558, 1.00732, 0.9, 1, 1.01372, 0.973161, 1.01372, 0.933333, 1, 1.01941, 0.962305, 1.01941, 0.966667, 1, 1.02414, 0.953419, 1.02414, 1, 1, 1.02764, 0.946939, 1.02764, 1.03333, 1, 1.02885, 0.94471, 1.02885, 1.06667, 1, 1.02797, 0.94632, 1.02797, 1.1, 1, 1.02527, 0.951334, 1.02527, 1.13333, 1, 1.02102, 0.959268, 1.02102, 1.16667, 1, 1.01556, 0.969586, 1.01556, 1.2, 1, 1.00906, 0.982154, 1.00906, 1.23333, 1, 1.00218, 0.995714, 1.00218, 1.26667, 1, 0.995185, 1.00976, 0.995185, 1.3, 1, 0.988359, 1.02373, 0.988359, 1.33333, 1, 0.981976, 1.03705, 0.981976, 1.36667, 1, 0.976768, 1.04816, 0.976768, 1.4, 1, 0.972885, 1.05653, 0.972885, 1.43333, 1, 0.970621, 1.06145, 0.970621, 1.46667, 1, 0.970211, 1.06235, 0.970211, 1.5, 1, 0.971834, 1.05881, 0.971834, 1.53333, 1, 0.975726, 1.05039, 0.975726, 1.56667, 1, 0.98084, 1.03949, 0.98084, 1.6, 1, 0.986866, 1.02685, 0.986866, 1.63333, 1, 0.993504, 1.01316, 0.993504, 1.66667, 1, 1.00046, 0.999073, 1.00046)
tracks/24/type = "position_3d"
tracks/24/imported = true
tracks/24/enabled = true
tracks/24/path = NodePath("rig/Skeleton3D:DEF-bone.01")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = PackedFloat32Array(0, 1, 0, 0, 0)
tracks/25/type = "rotation_3d"
tracks/25/imported = true
tracks/25/enabled = true
tracks/25/path = NodePath("rig/Skeleton3D:DEF-bone.01")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = PackedFloat32Array(0, 1, 4.77254e-07, 1.18538e-07, 0.0599926, 0.998199, 0.0333333, 1, 4.12098e-07, 1.20645e-07, 0.0576898, 0.998335, 0.0666667, 1, 3.78324e-07, 1.16431e-07, 0.054658, 0.998505, 0.1, 1, 3.96027e-07, 1.15377e-07, 0.0510482, 0.998696, 0.133333, 1, 4.02602e-07, 1.18538e-07, 0.0469924, 0.998895, 0.166667, 1, 2.52747e-07, 1.18538e-07, 0.0426036, 0.999092, 0.2, 1, 2.74542e-07, 1.20646e-07, 0.0379241, 0.999281, 0.233333, 1, 2.76852e-07, 1.20382e-07, 0.0330585, 0.999453, 0.266667, 1, 2.86975e-07, 1.20383e-07, 0.0279396, 0.99961, 0.3, 1, 2.76754e-07, 1.20514e-07, 0.0225032, 0.999747, 0.333333, 1, 1.62577e-07, 1.17879e-07, 0.0166886, 0.999861, 0.366667, 1, 5.8239e-08, 1.18934e-07, 0.0103025, 0.999947, 0.4, 1, 5.31697e-08, 1.19205e-07, 0.0034824, 0.999994, 0.433333, 1, 2.44433e-08, 1.19337e-07, -0.00373703, 0.999993, 0.466667, 1, -5.19941e-08, 1.19461e-07, -0.0112966, 0.999936, 0.533333, 1, -1.3059e-07, 1.17617e-07, -0.0269879, 0.999636, 0.566667, 1, -2.13448e-07, 1.19593e-07, -0.0346036, 0.999401, 0.6, 1, -2.75895e-07, 1.2012e-07, -0.0417085, 0.99913, 0.633333, 1, -2.98386e-07, 1.19065e-07, -0.0480526, 0.998845, 0.666667, 1, -3.15839e-07, 1.21172e-07, -0.0533873, 0.998574, 0.7, 1, -3.41002e-07, 1.19065e-07, -0.0571219, 0.998367, 0.733333, 1, -3.871e-07, 1.20119e-07, -0.0596123, 0.998222, 0.766667, 1, -3.99083e-07, 1.20119e-07, -0.0608884, 0.998145, 0.8, 1, -3.74938e-07, 1.18538e-07, -0.0609951, 0.998138, 0.833333, 1, -3.65683e-07, 1.18538e-07, -0.0599926, 0.998199, 0.866667, 1, -4.3084e-07, 1.20645e-07, -0.0576898, 0.998335, 0.9, 1, -4.14122e-07, 1.18011e-07, -0.054658, 0.998505, 0.933333, 1, -3.62753e-07, 1.18012e-07, -0.0510482, 0.998696, 0.966667, 1, -3.05636e-07, 1.20646e-07, -0.0469924, 0.998895, 1, 1, -2.53352e-07, 1.18538e-07, -0.0426036, 0.999092, 1.03333, 1, -2.3125e-07, 1.19592e-07, -0.0379241, 0.999281, 1.06667, 1, -2.79494e-07, 1.19065e-07, -0.0330585, 0.999453, 1.1, 1, -2.69453e-07, 1.19065e-07, -0.0279396, 0.99961, 1.13333, 1, -1.78568e-07, 1.19724e-07, -0.0225032, 0.999747, 1.16667, 1, -9.03481e-08, 1.19197e-07, -0.0166886, 0.999861, 1.2, 1, -5.98735e-08, 1.19197e-07, -0.0103025, 0.999947, 1.23333, 1, 1.9406e-08, 1.19205e-07, -0.0034824, 0.999994, 1.26667, 1, 5.81612e-08, 1.19337e-07, 0.00373702, 0.999993, 1.3, 1, 6.60206e-08, 1.19329e-07, 0.0112966, 0.999936, 1.36667, 1, 1.90311e-07, 1.18539e-07, 0.0269879, 0.999636, 1.4, 1, 2.25568e-07, 1.1933e-07, 0.0346036, 0.999401, 1.43333, 1, 2.97997e-07, 1.19329e-07, 0.0417085, 0.99913, 1.46667, 1, 3.93416e-07, 1.19065e-07, 0.0480526, 0.998845, 1.5, 1, 4.42805e-07, 1.21172e-07, 0.0533873, 0.998574, 1.53333, 1, 4.85077e-07, 1.19065e-07, 0.0571219, 0.998367, 1.56667, 1, 4.55741e-07, 1.21699e-07, 0.0596123, 0.998222, 1.6, 1, 4.43628e-07, 1.21699e-07, 0.0608884, 0.998145, 1.63333, 1, 4.67741e-07, 1.18538e-07, 0.0609951, 0.998138, 1.66667, 1, 4.77254e-07, 1.18538e-07, 0.0599926, 0.998199)
tracks/26/type = "scale_3d"
tracks/26/imported = true
tracks/26/enabled = true
tracks/26/path = NodePath("rig/Skeleton3D:DEF-bone.01")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = PackedFloat32Array(0, 1, 0.999065, 1.00187, 0.999065, 0.0333333, 1, 0.985437, 1.02992, 0.985437, 0.0666667, 1, 0.972931, 1.0566, 0.972931, 0.1, 1, 0.961973, 1.08075, 0.961973, 0.133333, 1, 0.952999, 1.10111, 0.952999, 0.166667, 1, 0.946452, 1.11636, 0.946451, 0.2, 1, 0.944199, 1.12169, 0.944199, 0.233333, 1, 0.945826, 1.11785, 0.945826, 0.266667, 1, 0.950893, 1.10603, 0.950892, 0.3, 1, 0.958907, 1.08764, 0.958906, 0.333333, 1, 0.969324, 1.06429, 0.969324, 0.366667, 1, 0.982005, 1.03712, 0.982005, 0.433333, 1, 1.00983, 0.980847, 1.00983, 0.466667, 1, 1.02391, 0.953972, 1.02391, 0.5, 1, 1.03732, 0.929348, 1.03732, 0.533333, 1, 1.0485, 0.909707, 1.0485, 0.566667, 1, 1.05693, 0.895233, 1.05693, 0.6, 1, 1.06188, 0.88686, 1.06188, 0.633333, 1, 1.06278, 0.885356, 1.06278, 0.666667, 1, 1.05921, 0.891321, 1.05921, 0.7, 1, 1.05074, 0.905792, 1.05074, 0.733333, 1, 1.03977, 0.925095, 1.03977, 0.766667, 1, 1.02704, 0.948218, 1.02704, 0.8, 1, 1.01326, 0.974148, 1.01326, 0.866667, 1, 0.985437, 1.02992, 0.985437, 0.9, 1, 0.972931, 1.0566, 0.972931, 0.933333, 1, 0.961973, 1.08075, 0.961973, 0.966667, 1, 0.952999, 1.10111, 0.952999, 1, 1, 0.946452, 1.11636, 0.946451, 1.03333, 1, 0.944199, 1.12169, 0.944199, 1.06667, 1, 0.945826, 1.11785, 0.945826, 1.1, 1, 0.950893, 1.10603, 0.950892, 1.13333, 1, 0.958907, 1.08764, 0.958906, 1.16667, 1, 0.969324, 1.06429, 0.969324, 1.2, 1, 0.982005, 1.03712, 0.982005, 1.26667, 1, 1.00983, 0.980847, 1.00983, 1.3, 1, 1.02391, 0.953972, 1.02391, 1.33333, 1, 1.03732, 0.929348, 1.03732, 1.36667, 1, 1.0485, 0.909707, 1.0485, 1.4, 1, 1.05693, 0.895233, 1.05693, 1.43333, 1, 1.06188, 0.88686, 1.06188, 1.46667, 1, 1.06278, 0.885356, 1.06278, 1.5, 1, 1.05921, 0.891321, 1.05921, 1.53333, 1, 1.05074, 0.905792, 1.05074, 1.56667, 1, 1.03977, 0.925095, 1.03977, 1.6, 1, 1.02704, 0.948218, 1.02704, 1.63333, 1, 1.01326, 0.974148, 1.01326, 1.66667, 1, 0.999065, 1.00187, 0.999065)
tracks/27/type = "position_3d"
tracks/27/imported = true
tracks/27/enabled = true
tracks/27/path = NodePath("rig/Skeleton3D:DEF-lid2.B.L")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = PackedFloat32Array(0, 1, 0.234259, 0.387422, 0.39528, 0.0333333, 1, 0.231124, 0.39582, 0.389888, 0.0666667, 1, 0.229026, 0.40347, 0.38494, 0.1, 1, 0.228016, 0.410039, 0.380604, 0.133333, 1, 0.228153, 0.415149, 0.377054, 0.166667, 1, 0.229499, 0.418379, 0.374463, 0.2, 1, 0.232638, 0.418054, 0.373572, 0.233333, 1, 0.237227, 0.414424, 0.374216, 0.266667, 1, 0.243091, 0.407844, 0.37622, 0.3, 1, 0.250043, 0.398742, 0.379391, 0.333333, 1, 0.257877, 0.387615, 0.383513, 0.366667, 1, 0.26651, 0.374672, 0.38853, 0.4, 1, 0.275457, 0.36098, 0.39394, 0.466667, 1, 0.293464, 0.332959, 0.405107, 0.5, 1, 0.302084, 0.319453, 0.410413, 0.533333, 1, 0.309783, 0.307485, 0.414838, 0.566667, 1, 0.316421, 0.297424, 0.418172, 0.6, 1, 0.32177, 0.289819, 0.42013, 0.633333, 1, 0.325644, 0.285162, 0.420488, 0.666667, 1, 0.327907, 0.283886, 0.419077, 0.7, 1, 0.328267, 0.286813, 0.415725, 0.733333, 1, 0.327473, 0.292359, 0.411385, 0.766667, 1, 0.325684, 0.30012, 0.406349, 0.8, 1, 0.323043, 0.309676, 0.400895, 0.833333, 1, 0.319672, 0.320597, 0.395279, 0.866667, 1, 0.315547, 0.332419, 0.389887, 0.9, 1, 0.311074, 0.344146, 0.384939, 0.933333, 1, 0.306409, 0.355246, 0.380604, 0.966667, 1, 0.301712, 0.365176, 0.377053, 1, 1, 0.297147, 0.373383, 0.374463, 1.03333, 1, 0.293146, 0.378086, 0.373572, 1.06667, 1, 0.289825, 0.379526, 0.374216, 1.1, 1, 0.287114, 0.378199, 0.37622, 1.13333, 1, 0.284926, 0.374664, 0.379391, 1.16667, 1, 0.28316, 0.36955, 0.383513, 1.2, 1, 0.281782, 0.363396, 0.38853, 1.23333, 1, 0.280565, 0.357149, 0.39394, 1.26667, 1, 0.279406, 0.351199, 0.399539, 1.3, 1, 0.278192, 0.345906, 0.405108, 1.33333, 1, 0.276802, 0.341593, 0.410413, 1.36667, 1, 0.2749, 0.339103, 0.414838, 1.4, 1, 0.272399, 0.338271, 0.418172, 1.43333, 1, 0.269172, 0.339249, 0.420131, 1.46667, 1, 0.265137, 0.342131, 0.420488, 1.5, 1, 0.260259, 0.346958, 0.419077, 1.53333, 1, 0.254708, 0.353734, 0.415726, 1.56667, 1, 0.24908, 0.361457, 0.411386, 1.6, 1, 0.243636, 0.369835, 0.40635, 1.63333, 1, 0.23862, 0.378583, 0.400895, 1.66667, 1, 0.234259, 0.387422, 0.39528)
tracks/28/type = "rotation_3d"
tracks/28/imported = true
tracks/28/enabled = true
tracks/28/path = NodePath("rig/Skeleton3D:DEF-lid2.B.L")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = PackedFloat32Array(0, 1, 0.686062, -0.552977, -0.43567, 0.183647, 0.0333333, 1, 0.686603, -0.550764, -0.438685, 0.181085, 0.0666667, 1, 0.686686, -0.549189, -0.441665, 0.178288, 0.1, 1, 0.686311, -0.548322, -0.44448, 0.175379, 0.133333, 1, 0.685461, -0.548258, -0.447, 0.172476, 0.166667, 1, 0.684098, -0.549116, -0.449095, 0.169693, 0.2, 1, 0.681892, -0.55156, -0.45035, 0.167301, 0.233333, 1, 0.678926, -0.555415, -0.450826, 0.165317, 0.266667, 1, 0.675231, -0.560538, -0.45064, 0.163657, 0.3, 1, 0.670856, -0.56675, -0.449919, 0.162231, 0.333333, 1, 0.665869, -0.57384, -0.448805, 0.160936, 0.366667, 1, 0.660234, -0.581731, -0.447422, 0.159666, 0.4, 1, 0.654221, -0.589936, -0.44599, 0.158316, 0.466667, 1, 0.641451, -0.606555, -0.443338, 0.155186, 0.5, 1, 0.634926, -0.614603, -0.442291, 0.153332, 0.533333, 1, 0.628662, -0.621962, -0.441691, 0.151183, 0.566667, 1, 0.62287, -0.628459, -0.441503, 0.148818, 0.6, 1, 0.617786, -0.633859, -0.441781, 0.146264, 0.633333, 1, 0.61364, -0.637946, -0.442562, 0.143562, 0.666667, 1, 0.610657, -0.640521, -0.443865, 0.140766, 0.7, 1, 0.609329, -0.641103, -0.445693, 0.138067, 0.733333, 1, 0.609183, -0.640331, -0.44776, 0.135586, 0.766667, 1, 0.610133, -0.638328, -0.449978, 0.133397, 0.8, 1, 0.612076, -0.635226, -0.452262, 0.131565, 0.833333, 1, 0.614893, -0.631167, -0.454529, 0.130145, 0.866667, 1, 0.618588, -0.626186, -0.456627, 0.129351, 0.933333, 1, 0.626821, -0.615442, -0.460052, 0.129151, 0.966667, 1, 0.630961, -0.610187, -0.461221, 0.129753, 1, 1, 0.634868, -0.60536, -0.46191, 0.130846, 1.03333, 1, 0.638125, -0.601656, -0.461736, 0.132688, 1.06667, 1, 0.640741, -0.599066, -0.460762, 0.135162, 1.1, 1, 0.642872, -0.597381, -0.459071, 0.138224, 1.13333, 1, 0.644682, -0.596363, -0.456756, 0.141815, 1.16667, 1, 0.646348, -0.595742, -0.453925, 0.145869, 1.2, 1, 0.648114, -0.595213, -0.450609, 0.150405, 1.23333, 1, 0.650107, -0.594481, -0.447067, 0.15519, 1.26667, 1, 0.652389, -0.593416, -0.443399, 0.160138, 1.3, 1, 0.654997, -0.591911, -0.43971, 0.165151, 1.36667, 1, 0.661311, -0.587137, -0.432883, 0.174786, 1.4, 1, 0.664851, -0.583874, -0.430146, 0.178991, 1.43333, 1, 0.668467, -0.580165, -0.428052, 0.182572, 1.46667, 1, 0.672045, -0.576093, -0.426738, 0.185383, 1.5, 1, 0.675471, -0.571757, -0.426327, 0.18729, 1.53333, 1, 0.678474, -0.567419, -0.42704, 0.188004, 1.56667, 1, 0.681059, -0.563278, -0.428444, 0.187915, 1.6, 1, 0.683199, -0.559431, -0.430428, 0.187104, 1.63333, 1, 0.684871, -0.55597, -0.432876, 0.185653, 1.66667, 1, 0.686062, -0.552977, -0.43567, 0.183647)
tracks/29/type = "scale_3d"
tracks/29/imported = true
tracks/29/enabled = true
tracks/29/path = NodePath("rig/Skeleton3D:DEF-lid2.B.L")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = PackedFloat32Array(0, 1, 1.00038, 0.999236, 1.00038, 0.0333333, 1, 1.00596, 0.988205, 1.00596, 0.0666667, 1, 1.01108, 0.978228, 1.01108, 0.1, 1, 1.01556, 0.969606, 1.01556, 0.133333, 1, 1.01922, 0.962638, 1.01922, 0.166667, 1, 1.02189, 0.957619, 1.02189, 0.2, 1, 1.0228, 0.955907, 1.0228, 0.233333, 1, 1.02214, 0.957144, 1.02214, 0.266667, 1, 1.02008, 0.961024, 1.02008, 0.3, 1, 1.01681, 0.967214, 1.01681, 0.333333, 1, 1.01256, 0.975349, 1.01256, 0.366667, 1, 1.00737, 0.985448, 1.00737, 0.4, 1, 1.00177, 0.996506, 1.00177, 0.466667, 1, 0.990251, 1.01981, 0.990252, 0.5, 1, 0.984809, 1.03109, 0.984809, 0.533333, 1, 0.9803, 1.04061, 0.9803, 0.566667, 1, 0.976915, 1.04783, 0.976915, 0.6, 1, 0.974932, 1.05209, 0.974932, 0.633333, 1, 0.974571, 1.05287, 0.974571, 0.666667, 1, 0.975997, 1.04979, 0.975997, 0.7, 1, 0.979396, 1.04253, 0.979396, 0.733333, 1, 0.983821, 1.03319, 0.983821, 0.766667, 1, 0.988978, 1.02245, 0.988978, 0.8, 1, 0.994586, 1.01094, 0.994586, 0.833333, 1, 1.00038, 0.999236, 1.00038, 0.866667, 1, 1.00596, 0.988206, 1.00596, 0.9, 1, 1.01108, 0.978229, 1.01108, 0.933333, 1, 1.01556, 0.969606, 1.01556, 0.966667, 1, 1.01922, 0.962638, 1.01922, 1, 1, 1.02189, 0.957618, 1.02189, 1.03333, 1, 1.0228, 0.955907, 1.0228, 1.06667, 1, 1.02214, 0.957144, 1.02214, 1.1, 1, 1.02008, 0.961025, 1.02008, 1.13333, 1, 1.01681, 0.967214, 1.01681, 1.16667, 1, 1.01256, 0.975349, 1.01256, 1.2, 1, 1.00737, 0.985449, 1.00737, 1.23333, 1, 1.00177, 0.996507, 1.00177, 1.3, 1, 0.990251, 1.01981, 0.990251, 1.33333, 1, 0.984809, 1.03109, 0.984809, 1.36667, 1, 0.9803, 1.04061, 0.9803, 1.4, 1, 0.976915, 1.04783, 0.976915, 1.43333, 1, 0.974931, 1.05209, 0.974932, 1.46667, 1, 0.974571, 1.05287, 0.974571, 1.5, 1, 0.975997, 1.04979, 0.975997, 1.53333, 1, 0.979396, 1.04253, 0.979396, 1.56667, 1, 0.983821, 1.03319, 0.983821, 1.6, 1, 0.988978, 1.02245, 0.988978, 1.63333, 1, 0.994587, 1.01094, 0.994587, 1.66667, 1, 1.00038, 0.999236, 1.00038)
tracks/30/type = "position_3d"
tracks/30/imported = true
tracks/30/enabled = true
tracks/30/path = NodePath("rig/Skeleton3D:DEF-lid3.B.L")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = PackedFloat32Array(0, 1, 0.176041, 0.356212, 0.466632, 0.0333333, 1, 0.173674, 0.364306, 0.460267, 0.0666667, 1, 0.17222, 0.371754, 0.454426, 0.1, 1, 0.171713, 0.378228, 0.449308, 0.133333, 1, 0.17219, 0.383363, 0.445116, 0.166667, 1, 0.173692, 0.386756, 0.442058, 0.2, 1, 0.176686, 0.386839, 0.441006, 0.233333, 1, 0.180869, 0.383842, 0.441766, 0.266667, 1, 0.186097, 0.378108, 0.444132, 0.3, 1, 0.192215, 0.370045, 0.447875, 0.333333, 1, 0.199057, 0.360128, 0.452741, 0.366667, 1, 0.206553, 0.348577, 0.458664, 0.4, 1, 0.214305, 0.336377, 0.46505, 0.466667, 1, 0.229901, 0.311528, 0.478234, 0.5, 1, 0.237387, 0.299636, 0.484497, 0.533333, 1, 0.244123, 0.289201, 0.489721, 0.566667, 1, 0.249997, 0.280525, 0.493657, 0.6, 1, 0.254823, 0.27409, 0.495969, 0.633333, 1, 0.258449, 0.270324, 0.496391, 0.666667, 1, 0.260759, 0.269601, 0.494725, 0.7, 1, 0.261493, 0.272626, 0.490769, 0.733333, 1, 0.261245, 0.277965, 0.485645, 0.766667, 1, 0.260131, 0.285251, 0.4797, 0.8, 1, 0.258253, 0.294102, 0.473261, 0.833333, 1, 0.255703, 0.304127, 0.466632, 0.866667, 1, 0.252411, 0.314891, 0.460267, 0.9, 1, 0.248743, 0.325516, 0.454425, 0.933333, 1, 0.244827, 0.335521, 0.449307, 0.966667, 1, 0.240795, 0.344413, 0.445116, 1, 1, 0.236785, 0.351686, 0.442058, 1.03333, 1, 0.233119, 0.355687, 0.441006, 1.06667, 1, 0.229925, 0.356642, 0.441766, 1.1, 1, 0.227154, 0.355001, 0.444132, 1.13333, 1, 0.224749, 0.351278, 0.447875, 1.16667, 1, 0.222637, 0.346047, 0.452741, 1.2, 1, 0.220796, 0.339787, 0.458664, 1.26667, 1, 0.217382, 0.327222, 0.47166, 1.3, 1, 0.215657, 0.32162, 0.478234, 1.33333, 1, 0.213807, 0.316893, 0.484498, 1.36667, 1, 0.211589, 0.313844, 0.489721, 1.4, 1, 0.20894, 0.312361, 0.493657, 1.43333, 1, 0.205767, 0.312616, 0.495969, 1.46667, 1, 0.202016, 0.314726, 0.496391, 1.5, 1, 0.197667, 0.31876, 0.494725, 1.53333, 1, 0.192888, 0.324785, 0.490769, 1.56667, 1, 0.188131, 0.331821, 0.485645, 1.6, 1, 0.183608, 0.339588, 0.479701, 1.66667, 1, 0.176041, 0.356212, 0.466632)
tracks/31/type = "rotation_3d"
tracks/31/imported = true
tracks/31/enabled = true
tracks/31/path = NodePath("rig/Skeleton3D:DEF-lid3.B.L")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = PackedFloat32Array(0, 1, -0.6732, 0.685443, 0.267966, 0.0718614, 0.0333333, 1, -0.669749, 0.687712, 0.269889, 0.0751559, 0.0666667, 1, -0.665868, 0.690423, 0.271609, 0.0785168, 0.133333, 1, -0.657533, 0.696628, 0.274157, 0.0848379, 0.166667, 1, -0.653424, 0.69989, 0.274838, 0.0874917, 0.2, 1, -0.649774, 0.703074, 0.274789, 0.0892868, 0.233333, 1, -0.646607, 0.706136, 0.274091, 0.0902543, 0.266667, 1, -0.643793, 0.709156, 0.272826, 0.0905191, 0.3, 1, -0.641189, 0.712215, 0.271084, 0.0902233, 0.333333, 1, -0.638634, 0.715394, 0.268967, 0.0895275, 0.366667, 1, -0.635906, 0.718848, 0.266527, 0.0885692, 0.4, 1, -0.632904, 0.722552, 0.263969, 0.0875847, 0.433333, 1, -0.629573, 0.726504, 0.261376, 0.086673, 0.466667, 1, -0.625878, 0.730686, 0.258828, 0.0859257, 0.5, 1, -0.621806, 0.735061, 0.25641, 0.0854272, 0.566667, 1, -0.61263, 0.743965, 0.252655, 0.0858267, 0.6, 1, -0.607886, 0.748148, 0.251445, 0.0867411, 0.633333, 1, -0.603224, 0.751967, 0.250781, 0.0881786, 0.666667, 1, -0.598798, 0.755282, 0.250723, 0.0901505, 0.7, 1, -0.594995, 0.757768, 0.251376, 0.0926261, 0.733333, 1, -0.591797, 0.759583, 0.252447, 0.0952976, 0.766667, 1, -0.589253, 0.760737, 0.253861, 0.0980579, 0.8, 1, -0.587399, 0.761247, 0.255546, 0.100804, 0.833333, 1, -0.586257, 0.761139, 0.25743, 0.103438, 0.866667, 1, -0.586073, 0.760283, 0.259437, 0.105741, 0.933333, 1, -0.587582, 0.757295, 0.263297, 0.109205, 0.966667, 1, -0.589228, 0.755282, 0.264983, 0.110199, 1, 1, -0.591471, 0.752975, 0.266392, 0.11057, 1.03333, 1, -0.594615, 0.750301, 0.267232, 0.109856, 1.06667, 1, -0.598532, 0.747321, 0.267526, 0.10817, 1.1, 1, -0.603178, 0.744009, 0.267339, 0.105621, 1.13333, 1, -0.608495, 0.740344, 0.266745, 0.102334, 1.16667, 1, -0.614404, 0.736308, 0.265828, 0.098454, 1.2, 1, -0.620962, 0.731797, 0.264638, 0.0940486, 1.23333, 1, -0.627861, 0.726942, 0.263341, 0.0894325, 1.26667, 1, -0.63499, 0.721778, 0.262004, 0.084729, 1.33333, 1, -0.649444, 0.710759, 0.259487, 0.0755655, 1.36667, 1, -0.65632, 0.70517, 0.25857, 0.0715646, 1.4, 1, -0.662622, 0.699809, 0.25798, 0.0681534, 1.43333, 1, -0.668126, 0.694881, 0.257798, 0.0654854, 1.46667, 1, -0.672623, 0.690587, 0.25809, 0.0636967, 1.5, 1, -0.675916, 0.68713, 0.258906, 0.0629049, 1.53333, 1, -0.677541, 0.684958, 0.260302, 0.0633486, 1.56667, 1, -0.67799, 0.683752, 0.261997, 0.0645733, 1.6, 1, -0.677352, 0.683471, 0.263899, 0.0664732, 1.63333, 1, -0.675721, 0.684058, 0.265919, 0.0689397, 1.66667, 1, -0.6732, 0.685443, 0.267966, 0.0718614)
tracks/32/type = "scale_3d"
tracks/32/imported = true
tracks/32/enabled = true
tracks/32/path = NodePath("rig/Skeleton3D:DEF-lid3.B.L")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = PackedFloat32Array(0, 1, 1.00046, 0.999078, 1.00046, 0.0333333, 1, 1.00728, 0.985633, 1.00728, 0.0666667, 1, 1.01364, 0.973305, 1.01364, 0.1, 1, 1.0193, 0.962513, 1.0193, 0.133333, 1, 1.024, 0.953681, 1.024, 0.166667, 1, 1.02747, 0.947241, 1.02747, 0.2, 1, 1.02867, 0.945028, 1.02867, 0.233333, 1, 1.02781, 0.946627, 1.02781, 0.266667, 1, 1.02512, 0.951609, 1.02512, 0.3, 1, 1.0209, 0.959493, 1.0209, 0.333333, 1, 1.01548, 0.969749, 1.01548, 0.366667, 1, 1.00901, 0.982247, 1.00901, 0.4, 1, 1.00217, 0.995737, 1.00217, 0.433333, 1, 0.995207, 1.00971, 0.995207, 0.466667, 1, 0.988411, 1.02362, 0.988411, 0.5, 1, 0.982055, 1.03688, 0.982055, 0.533333, 1, 0.976866, 1.04795, 0.976866, 0.566667, 1, 0.972998, 1.05629, 0.972998, 0.6, 1, 0.970741, 1.06119, 0.970741, 0.633333, 1, 0.970333, 1.06209, 0.970333, 0.666667, 1, 0.97195, 1.05855, 0.971949, 0.7, 1, 0.975829, 1.05017, 0.975828, 0.733333, 1, 0.980923, 1.03931, 0.980923, 0.766667, 1, 0.986925, 1.02672, 0.986925, 0.8, 1, 0.993534, 1.0131, 0.993534, 0.833333, 1, 1.00046, 0.999077, 1.00046, 0.866667, 1, 1.00728, 0.985633, 1.00728, 0.9, 1, 1.01364, 0.973305, 1.01364, 0.933333, 1, 1.0193, 0.962512, 1.0193, 0.966667, 1, 1.024, 0.95368, 1.024, 1, 1, 1.02747, 0.947242, 1.02747, 1.03333, 1, 1.02867, 0.945028, 1.02867, 1.06667, 1, 1.02781, 0.946627, 1.02781, 1.1, 1, 1.02512, 0.951609, 1.02512, 1.13333, 1, 1.0209, 0.959494, 1.0209, 1.16667, 1, 1.01548, 0.96975, 1.01548, 1.2, 1, 1.00901, 0.982247, 1.00901, 1.23333, 1, 1.00217, 0.995737, 1.00217, 1.26667, 1, 0.995207, 1.00971, 0.995207, 1.3, 1, 0.988411, 1.02362, 0.988411, 1.33333, 1, 0.982055, 1.03688, 0.982055, 1.36667, 1, 0.976866, 1.04795, 0.976866, 1.4, 1, 0.972997, 1.05629, 0.972997, 1.43333, 1, 0.970741, 1.06119, 0.970741, 1.46667, 1, 0.970333, 1.06209, 0.970333, 1.5, 1, 0.97195, 1.05855, 0.97195, 1.53333, 1, 0.975828, 1.05017, 0.975828, 1.56667, 1, 0.980923, 1.03931, 0.980923, 1.6, 1, 0.986924, 1.02672, 0.986924, 1.63333, 1, 0.993534, 1.0131, 0.993534, 1.66667, 1, 1.00046, 0.999078, 1.00046)
tracks/33/type = "position_3d"
tracks/33/imported = true
tracks/33/enabled = true
tracks/33/path = NodePath("rig/Skeleton3D:DEF-lid4.B.L")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/keys = PackedFloat32Array(0, 1, 0.0858914, 0.351523, 0.492006, 0.0333333, 1, 0.0847046, 0.360344, 0.485295, 0.0666667, 1, 0.084332, 0.368616, 0.479136, 0.1, 1, 0.0847726, 0.375974, 0.47374, 0.133333, 1, 0.0860275, 0.382016, 0.46932, 0.166667, 1, 0.0880999, 0.386312, 0.466096, 0.2, 1, 0.0912945, 0.387244, 0.464987, 0.233333, 1, 0.0953404, 0.385046, 0.465788, 0.266667, 1, 0.100133, 0.380092, 0.468283, 0.3, 1, 0.105561, 0.372819, 0.472229, 0.333333, 1, 0.111506, 0.363733, 0.47736, 0.366667, 1, 0.117917, 0.353113, 0.483604, 0.4, 1, 0.124509, 0.341944, 0.490338, 0.433333, 1, 0.131164, 0.330602, 0.497307, 0.466667, 1, 0.137757, 0.319465, 0.504239, 0.5, 1, 0.144162, 0.308903, 0.510843, 0.533333, 1, 0.150045, 0.299873, 0.51635, 0.566667, 1, 0.155331, 0.292593, 0.5205, 0.6, 1, 0.159888, 0.287486, 0.522938, 0.633333, 1, 0.163608, 0.284922, 0.523383, 0.666667, 1, 0.166402, 0.285215, 0.521626, 0.7, 1, 0.168024, 0.288945, 0.517455, 0.733333, 1, 0.168854, 0.29475, 0.512052, 0.766667, 1, 0.168939, 0.302274, 0.505784, 0.8, 1, 0.168319, 0.311152, 0.498995, 0.833333, 1, 0.167024, 0.321012, 0.492006, 0.866667, 1, 0.164896, 0.331397, 0.485294, 0.9, 1, 0.162268, 0.34153, 0.479135, 0.933333, 1, 0.159237, 0.350957, 0.473739, 0.966667, 1, 0.1559, 0.3592, 0.46932, 1, 1, 0.152358, 0.365768, 0.466095, 1.03333, 1, 0.148769, 0.368995, 0.464986, 1.06667, 1, 0.145302, 0.369113, 0.465787, 1.1, 1, 0.141949, 0.366556, 0.468282, 1.13333, 1, 0.138696, 0.361825, 0.472229, 1.16667, 1, 0.135522, 0.355484, 0.477359, 1.2, 1, 0.132424, 0.347964, 0.483604, 1.26667, 1, 0.126312, 0.332547, 0.497307, 1.3, 1, 0.123251, 0.325376, 0.504239, 1.33333, 1, 0.120147, 0.319012, 0.510843, 1.36667, 1, 0.116911, 0.314309, 0.51635, 1.4, 1, 0.113515, 0.311243, 0.5205, 1.43333, 1, 0.109926, 0.310055, 0.522938, 1.46667, 1, 0.106133, 0.310933, 0.523383, 1.5, 1, 0.102145, 0.314012, 0.521626, 1.53333, 1, 0.0981524, 0.319499, 0.517455, 1.56667, 1, 0.0943903, 0.326298, 0.512053, 1.6, 1, 0.0910029, 0.334104, 0.505785, 1.63333, 1, 0.0881269, 0.342613, 0.498996, 1.66667, 1, 0.0858914, 0.351523, 0.492006)
tracks/34/type = "rotation_3d"
tracks/34/imported = true
tracks/34/enabled = true
tracks/34/path = NodePath("rig/Skeleton3D:DEF-lid4.B.L")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/keys = PackedFloat32Array(0, 1, -0.62453, 0.733395, 0.110509, 0.244708, 0.0333333, 1, -0.617772, 0.737898, 0.110862, 0.248142, 0.1, 1, -0.603528, 0.747444, 0.1107, 0.254611, 0.133333, 1, -0.596709, 0.752042, 0.110207, 0.257344, 0.166667, 1, -0.590549, 0.75625, 0.109455, 0.259533, 0.2, 1, -0.585998, 0.759559, 0.108362, 0.26064, 0.233333, 1, -0.583018, 0.76201, 0.107006, 0.260732, 0.266667, 1, -0.581336, 0.763785, 0.105399, 0.259947, 0.3, 1, -0.580637, 0.765077, 0.103557, 0.258444, 0.333333, 1, -0.580571, 0.766092, 0.101493, 0.256398, 0.366667, 1, -0.580757, 0.767074, 0.0991705, 0.253937, 0.4, 1, -0.580769, 0.768226, 0.0966932, 0.251368, 0.433333, 1, -0.580419, 0.769643, 0.0941016, 0.248813, 0.466667, 1, -0.579548, 0.771397, 0.0914445, 0.246391, 0.5, 1, -0.578026, 0.773539, 0.0887785, 0.244216, 0.533333, 1, -0.575502, 0.776216, 0.0862464, 0.242587, 0.566667, 1, -0.572209, 0.779229, 0.083943, 0.241521, 0.6, 1, -0.56818, 0.782507, 0.0819599, 0.241116, 0.633333, 1, -0.563473, 0.785963, 0.0803784, 0.241454, 0.666667, 1, -0.558176, 0.789497, 0.0792693, 0.242592, 0.733333, 1, -0.546994, 0.795955, 0.0786894, 0.247108, 0.766667, 1, -0.541648, 0.798692, 0.0789601, 0.249948, 0.8, 1, -0.536668, 0.801028, 0.0795456, 0.253012, 0.833333, 1, -0.532196, 0.802916, 0.0804037, 0.256182, 0.866667, 1, -0.528662, 0.804147, 0.0815437, 0.259258, 0.9, 1, -0.525948, 0.804877, 0.0828141, 0.262093, 0.933333, 1, -0.524169, 0.805088, 0.0841682, 0.264567, 0.966667, 1, -0.52347, 0.804741, 0.0855638, 0.266551, 1, 1, -0.524026, 0.803778, 0.0869627, 0.267908, 1.03333, 1, -0.526743, 0.80179, 0.0882961, 0.268102, 1.06667, 1, -0.53139, 0.79887, 0.0895392, 0.267235, 1.1, 1, -0.537767, 0.795062, 0.0907075, 0.265433, 1.13333, 1, -0.545627, 0.790426, 0.0918161, 0.262846, 1.16667, 1, -0.554681, 0.78504, 0.0928793, 0.259642, 1.2, 1, -0.564831, 0.778875, 0.0939092, 0.255933, 1.23333, 1, -0.575408, 0.772248, 0.0949259, 0.252047, 1.3, 1, -0.596829, 0.758104, 0.0969779, 0.24427, 1.33333, 1, -0.607167, 0.750883, 0.0980474, 0.240645, 1.36667, 1, -0.616546, 0.744031, 0.0992032, 0.237587, 1.4, 1, -0.624735, 0.737784, 0.100441, 0.235143, 1.43333, 1, -0.63142, 0.73243, 0.10177, 0.233449, 1.46667, 1, -0.636318, 0.728243, 0.103188, 0.232622, 1.5, 1, -0.639172, 0.725482, 0.104685, 0.232758, 1.53333, 1, -0.639382, 0.724671, 0.106167, 0.234032, 1.56667, 1, -0.637835, 0.725198, 0.107543, 0.235984, 1.6, 1, -0.634714, 0.726932, 0.108758, 0.238496, 1.63333, 1, -0.630211, 0.729719, 0.109762, 0.241445, 1.66667, 1, -0.62453, 0.733395, 0.110509, 0.244708)
tracks/35/type = "scale_3d"
tracks/35/imported = true
tracks/35/enabled = true
tracks/35/path = NodePath("rig/Skeleton3D:DEF-lid4.B.L")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/keys = PackedFloat32Array(0, 1, 1.00033, 0.999335, 1.00033, 0.0333333, 1, 1.00515, 0.989804, 1.00515, 0.0666667, 1, 1.0095, 0.98128, 1.0095, 0.1, 1, 1.01327, 0.973996, 1.01327, 0.133333, 1, 1.0163, 0.968174, 1.0163, 0.166667, 1, 1.01849, 0.964024, 1.01849, 0.2, 1, 1.01923, 0.962621, 1.01923, 0.233333, 1, 1.01869, 0.963637, 1.01869, 0.266667, 1, 1.01701, 0.966842, 1.01701, 0.3, 1, 1.01431, 0.97199, 1.01431, 0.333333, 1, 1.01076, 0.978821, 1.01076, 0.366667, 1, 1.00635, 0.987435, 1.00635, 0.4, 1, 1.00152, 0.996985, 1.00152, 0.433333, 1, 0.996475, 1.00712, 0.996475, 0.466667, 1, 0.991407, 1.01743, 0.991407, 0.5, 1, 0.986544, 1.02746, 0.986544, 0.533333, 1, 0.982469, 1.03602, 0.98247, 0.566667, 1, 0.979395, 1.04253, 0.979395, 0.6, 1, 0.977587, 1.04638, 0.977587, 0.633333, 1, 0.977257, 1.04709, 0.977257, 0.666667, 1, 0.97856, 1.0443, 0.97856, 0.7, 1, 0.981652, 1.03774, 0.981652, 0.733333, 1, 0.985648, 1.02936, 0.985648, 0.766667, 1, 0.990268, 1.01978, 0.990268, 0.8, 1, 0.995246, 1.0096, 0.995246, 0.833333, 1, 1.00033, 0.999335, 1.00033, 0.866667, 1, 1.00515, 0.989803, 1.00515, 0.9, 1, 1.0095, 0.98128, 1.0095, 0.933333, 1, 1.01327, 0.973997, 1.01327, 0.966667, 1, 1.0163, 0.968174, 1.0163, 1, 1, 1.01849, 0.964024, 1.01849, 1.03333, 1, 1.01923, 0.96262, 1.01923, 1.06667, 1, 1.01869, 0.963636, 1.01869, 1.1, 1, 1.01701, 0.966841, 1.01701, 1.13333, 1, 1.01431, 0.971989, 1.01431, 1.16667, 1, 1.01076, 0.97882, 1.01076, 1.2, 1, 1.00635, 0.987436, 1.00635, 1.23333, 1, 1.00152, 0.996985, 1.00152, 1.26667, 1, 0.996475, 1.00712, 0.996475, 1.3, 1, 0.991407, 1.01743, 0.991407, 1.33333, 1, 0.986544, 1.02746, 0.986544, 1.36667, 1, 0.982469, 1.03602, 0.982469, 1.4, 1, 0.979395, 1.04253, 0.979395, 1.43333, 1, 0.977587, 1.04638, 0.977587, 1.46667, 1, 0.977257, 1.04709, 0.977257, 1.5, 1, 0.97856, 1.0443, 0.97856, 1.53333, 1, 0.981652, 1.03774, 0.981652, 1.56667, 1, 0.985648, 1.02936, 0.985648, 1.6, 1, 0.990267, 1.01978, 0.990268, 1.63333, 1, 0.995246, 1.0096, 0.995246, 1.66667, 1, 1.00033, 0.999335, 1.00033)
tracks/36/type = "position_3d"
tracks/36/imported = true
tracks/36/enabled = true
tracks/36/path = NodePath("rig/Skeleton3D:DEF-lid2.B.R")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/keys = PackedFloat32Array(0, 1, -0.319672, 0.320596, 0.39528, 0.0333333, 1, -0.315547, 0.332418, 0.389888, 0.0666667, 1, -0.311074, 0.344145, 0.38494, 0.1, 1, -0.306409, 0.355245, 0.380605, 0.133333, 1, -0.301712, 0.365176, 0.377054, 0.166667, 1, -0.297147, 0.373382, 0.374463, 0.2, 1, -0.293146, 0.378085, 0.373572, 0.233333, 1, -0.289825, 0.379526, 0.374216, 0.266667, 1, -0.287114, 0.378198, 0.37622, 0.3, 1, -0.284926, 0.374663, 0.379391, 0.333333, 1, -0.28316, 0.369549, 0.383513, 0.366667, 1, -0.281782, 0.363395, 0.38853, 0.4, 1, -0.280565, 0.357148, 0.39394, 0.433333, 1, -0.279406, 0.351199, 0.399538, 0.466667, 1, -0.278192, 0.345906, 0.405108, 0.5, 1, -0.276802, 0.341593, 0.410413, 0.533333, 1, -0.2749, 0.339103, 0.414838, 0.566667, 1, -0.272399, 0.338272, 0.418172, 0.6, 1, -0.269172, 0.33925, 0.42013, 0.633333, 1, -0.265137, 0.342132, 0.420488, 0.666667, 1, -0.260259, 0.346958, 0.419077, 0.7, 1, -0.254708, 0.353734, 0.415726, 0.733333, 1, -0.24908, 0.361458, 0.411385, 0.766667, 1, -0.243636, 0.369836, 0.406349, 0.8, 1, -0.23862, 0.378584, 0.400895, 0.833333, 1, -0.234259, 0.387423, 0.395279, 0.866667, 1, -0.231124, 0.39582, 0.389887, 0.9, 1, -0.229025, 0.403471, 0.384939, 0.933333, 1, -0.228016, 0.41004, 0.380604, 0.966667, 1, -0.228153, 0.415149, 0.377053, 1, 1, -0.229499, 0.418379, 0.374463, 1.03333, 1, -0.232638, 0.418054, 0.373572, 1.06667, 1, -0.237227, 0.414424, 0.374216, 1.1, 1, -0.243091, 0.407845, 0.37622, 1.13333, 1, -0.250043, 0.398742, 0.379391, 1.16667, 1, -0.257877, 0.387615, 0.383512, 1.2, 1, -0.26651, 0.374672, 0.38853, 1.23333, 1, -0.275457, 0.36098, 0.39394, 1.3, 1, -0.293464, 0.332959, 0.405108, 1.33333, 1, -0.302084, 0.319452, 0.410413, 1.36667, 1, -0.309783, 0.307485, 0.414838, 1.4, 1, -0.316421, 0.297424, 0.418172, 1.43333, 1, -0.32177, 0.289819, 0.420131, 1.46667, 1, -0.325644, 0.285162, 0.420488, 1.5, 1, -0.327907, 0.283885, 0.419077, 1.53333, 1, -0.328267, 0.286813, 0.415726, 1.56667, 1, -0.327473, 0.292359, 0.411386, 1.6, 1, -0.325684, 0.300119, 0.40635, 1.63333, 1, -0.323043, 0.309676, 0.400895, 1.66667, 1, -0.319672, 0.320596, 0.39528)
tracks/37/type = "rotation_3d"
tracks/37/imported = true
tracks/37/enabled = true
tracks/37/path = NodePath("rig/Skeleton3D:DEF-lid2.B.R")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/keys = PackedFloat32Array(0, 1, 0.614893, 0.631167, 0.45453, 0.130145, 0.0333333, 1, 0.618589, 0.626185, 0.456627, 0.129351, 0.1, 1, 0.626821, 0.615441, 0.460052, 0.12915, 0.133333, 1, 0.630961, 0.610187, 0.461221, 0.129752, 0.166667, 1, 0.634868, 0.60536, 0.461911, 0.130846, 0.2, 1, 0.638125, 0.601656, 0.461736, 0.132687, 0.233333, 1, 0.640741, 0.599066, 0.460763, 0.135162, 0.266667, 1, 0.642871, 0.597381, 0.459072, 0.138224, 0.3, 1, 0.644681, 0.596363, 0.456757, 0.141816, 0.333333, 1, 0.646348, 0.595742, 0.453926, 0.145868, 0.366667, 1, 0.648114, 0.595213, 0.450609, 0.150404, 0.4, 1, 0.650107, 0.594482, 0.447067, 0.15519, 0.433333, 1, 0.652388, 0.593416, 0.443399, 0.160138, 0.466667, 1, 0.654998, 0.591911, 0.439709, 0.165151, 0.533333, 1, 0.66131, 0.587137, 0.432883, 0.174786, 0.566667, 1, 0.664851, 0.583874, 0.430146, 0.178991, 0.6, 1, 0.668466, 0.580165, 0.428052, 0.182573, 0.633333, 1, 0.672045, 0.576093, 0.426738, 0.185384, 0.666667, 1, 0.675471, 0.571758, 0.426326, 0.18729, 0.7, 1, 0.678475, 0.567419, 0.427039, 0.188004, 0.733333, 1, 0.681059, 0.563278, 0.428444, 0.187915, 0.766667, 1, 0.683198, 0.559432, 0.430428, 0.187105, 0.8, 1, 0.684871, 0.55597, 0.432876, 0.185654, 0.833333, 1, 0.686062, 0.552978, 0.43567, 0.183647, 0.866667, 1, 0.686602, 0.550765, 0.438684, 0.181085, 0.9, 1, 0.686686, 0.54919, 0.441664, 0.178288, 0.933333, 1, 0.686312, 0.548322, 0.444479, 0.175379, 0.966667, 1, 0.685462, 0.548257, 0.446999, 0.172476, 1, 1, 0.684097, 0.549116, 0.449095, 0.169693, 1.03333, 1, 0.681892, 0.55156, 0.45035, 0.167302, 1.06667, 1, 0.678926, 0.555415, 0.450826, 0.165317, 1.1, 1, 0.675231, 0.560538, 0.45064, 0.163657, 1.13333, 1, 0.670856, 0.566751, 0.449919, 0.162231, 1.16667, 1, 0.665868, 0.573841, 0.448805, 0.160937, 1.2, 1, 0.660233, 0.581732, 0.447423, 0.159666, 1.23333, 1, 0.65422, 0.589937, 0.44599, 0.158316, 1.3, 1, 0.641451, 0.606555, 0.443338, 0.155185, 1.33333, 1, 0.634926, 0.614603, 0.442292, 0.153332, 1.36667, 1, 0.628662, 0.621962, 0.441692, 0.151183, 1.4, 1, 0.62287, 0.628459, 0.441504, 0.148818, 1.43333, 1, 0.617785, 0.633859, 0.441781, 0.146264, 1.46667, 1, 0.61364, 0.637946, 0.442562, 0.143561, 1.5, 1, 0.610657, 0.64052, 0.443866, 0.140766, 1.53333, 1, 0.60933, 0.641102, 0.445693, 0.138066, 1.56667, 1, 0.609183, 0.64033, 0.447761, 0.135585, 1.6, 1, 0.610133, 0.638327, 0.449979, 0.133396, 1.63333, 1, 0.612076, 0.635226, 0.452263, 0.131564, 1.66667, 1, 0.614893, 0.631167, 0.45453, 0.130145)
tracks/38/type = "scale_3d"
tracks/38/imported = true
tracks/38/enabled = true
tracks/38/path = NodePath("rig/Skeleton3D:DEF-lid2.B.R")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/keys = PackedFloat32Array(0, 1, 1.00038, 0.999237, 1.00038, 0.0333333, 1, 1.00596, 0.988207, 1.00596, 0.0666667, 1, 1.01108, 0.978229, 1.01108, 0.1, 1, 1.01556, 0.969607, 1.01556, 0.133333, 1, 1.01922, 0.962639, 1.01922, 0.166667, 1, 1.02189, 0.957619, 1.02189, 0.2, 1, 1.0228, 0.955908, 1.0228, 0.233333, 1, 1.02214, 0.957146, 1.02214, 0.266667, 1, 1.02008, 0.961027, 1.02008, 0.3, 1, 1.01681, 0.967215, 1.01681, 0.333333, 1, 1.01256, 0.97535, 1.01256, 0.366667, 1, 1.00737, 0.98545, 1.00737, 0.4, 1, 1.00177, 0.996508, 1.00177, 0.466667, 1, 0.990251, 1.01981, 0.990251, 0.5, 1, 0.984809, 1.03109, 0.984809, 0.533333, 1, 0.9803, 1.04061, 0.9803, 0.566667, 1, 0.976915, 1.04783, 0.976915, 0.6, 1, 0.974931, 1.05209, 0.974931, 0.633333, 1, 0.974571, 1.05287, 0.97457, 0.666667, 1, 0.975997, 1.04979, 0.975997, 0.7, 1, 0.979397, 1.04253, 0.979397, 0.733333, 1, 0.983821, 1.03319, 0.983821, 0.766667, 1, 0.988978, 1.02245, 0.988977, 0.8, 1, 0.994586, 1.01094, 0.994586, 0.833333, 1, 1.00038, 0.999238, 1.00038, 0.866667, 1, 1.00596, 0.988207, 1.00596, 0.9, 1, 1.01108, 0.978228, 1.01108, 0.933333, 1, 1.01556, 0.969606, 1.01556, 0.966667, 1, 1.01922, 0.962639, 1.01922, 1, 1, 1.02189, 0.957619, 1.02189, 1.03333, 1, 1.0228, 0.955909, 1.0228, 1.06667, 1, 1.02214, 0.957146, 1.02214, 1.1, 1, 1.02008, 0.961025, 1.02008, 1.13333, 1, 1.01681, 0.967214, 1.01681, 1.16667, 1, 1.01256, 0.97535, 1.01256, 1.2, 1, 1.00737, 0.98545, 1.00737, 1.23333, 1, 1.00177, 0.996508, 1.00177, 1.3, 1, 0.990252, 1.01981, 0.990252, 1.33333, 1, 0.984809, 1.03109, 0.984809, 1.36667, 1, 0.9803, 1.04061, 0.980299, 1.4, 1, 0.976915, 1.04783, 0.976914, 1.43333, 1, 0.974931, 1.05209, 0.974931, 1.46667, 1, 0.974571, 1.05287, 0.97457, 1.5, 1, 0.975997, 1.04979, 0.975997, 1.53333, 1, 0.979396, 1.04253, 0.979396, 1.56667, 1, 0.983821, 1.03319, 0.983821, 1.6, 1, 0.988978, 1.02245, 0.988977, 1.63333, 1, 0.994587, 1.01094, 0.994586, 1.66667, 1, 1.00038, 0.999237, 1.00038)
tracks/39/type = "position_3d"
tracks/39/imported = true
tracks/39/enabled = true
tracks/39/path = NodePath("rig/Skeleton3D:DEF-lid3.B.R")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/keys = PackedFloat32Array(0, 1, -0.255703, 0.304127, 0.466632, 0.0333333, 1, -0.252411, 0.31489, 0.460267, 0.0666667, 1, -0.248743, 0.325515, 0.454426, 0.1, 1, -0.244827, 0.33552, 0.449308, 0.133333, 1, -0.240795, 0.344413, 0.445116, 0.166667, 1, -0.236785, 0.351685, 0.442058, 0.2, 1, -0.233119, 0.355686, 0.441006, 0.233333, 1, -0.229924, 0.356641, 0.441766, 0.266667, 1, -0.227154, 0.355001, 0.444132, 0.3, 1, -0.224749, 0.351277, 0.447875, 0.333333, 1, -0.222637, 0.346047, 0.452741, 0.366667, 1, -0.220796, 0.339787, 0.458664, 0.433333, 1, -0.217382, 0.327222, 0.47166, 0.466667, 1, -0.215657, 0.32162, 0.478234, 0.5, 1, -0.213807, 0.316893, 0.484497, 0.533333, 1, -0.211589, 0.313844, 0.489721, 0.566667, 1, -0.208939, 0.312362, 0.493657, 0.6, 1, -0.205767, 0.312616, 0.495969, 0.633333, 1, -0.202016, 0.314727, 0.496391, 0.666667, 1, -0.197667, 0.318761, 0.494725, 0.7, 1, -0.192888, 0.324786, 0.490769, 0.733333, 1, -0.188131, 0.331822, 0.485645, 0.766667, 1, -0.183608, 0.339589, 0.4797, 0.833333, 1, -0.176041, 0.356213, 0.466632, 0.866667, 1, -0.173674, 0.364307, 0.460267, 0.9, 1, -0.17222, 0.371755, 0.454425, 0.933333, 1, -0.171713, 0.378228, 0.449307, 0.966667, 1, -0.17219, 0.383363, 0.445116, 1, 1, -0.173692, 0.386757, 0.442058, 1.03333, 1, -0.176686, 0.386839, 0.441006, 1.06667, 1, -0.180869, 0.383842, 0.441766, 1.1, 1, -0.186097, 0.378108, 0.444132, 1.13333, 1, -0.192215, 0.370045, 0.447875, 1.16667, 1, -0.199057, 0.360128, 0.452741, 1.2, 1, -0.206553, 0.348577, 0.458664, 1.23333, 1, -0.214305, 0.336377, 0.46505, 1.3, 1, -0.229901, 0.311528, 0.478234, 1.33333, 1, -0.237387, 0.299636, 0.484498, 1.36667, 1, -0.244123, 0.2892, 0.489721, 1.4, 1, -0.249997, 0.280524, 0.493657, 1.43333, 1, -0.254823, 0.274089, 0.495969, 1.46667, 1, -0.258449, 0.270323, 0.496391, 1.5, 1, -0.260759, 0.2696, 0.494725, 1.53333, 1, -0.261493, 0.272625, 0.490769, 1.56667, 1, -0.261245, 0.277965, 0.485645, 1.6, 1, -0.260131, 0.28525, 0.479701, 1.63333, 1, -0.258253, 0.294101, 0.473261, 1.66667, 1, -0.255703, 0.304127, 0.466632)
tracks/40/type = "rotation_3d"
tracks/40/imported = true
tracks/40/enabled = true
tracks/40/path = NodePath("rig/Skeleton3D:DEF-lid3.B.R")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/keys = PackedFloat32Array(0, 1, -0.586258, -0.761139, -0.257431, 0.103439, 0.0333333, 1, -0.586073, -0.760283, -0.259438, 0.105742, 0.0666667, 1, -0.586526, -0.758979, -0.261421, 0.10769, 0.1, 1, -0.587581, -0.757295, -0.263298, 0.109206, 0.133333, 1, -0.589227, -0.755282, -0.264984, 0.110199, 0.166667, 1, -0.591471, -0.752975, -0.266392, 0.110571, 0.2, 1, -0.594615, -0.750301, -0.267232, 0.109856, 0.233333, 1, -0.598532, -0.747321, -0.267526, 0.108171, 0.266667, 1, -0.603178, -0.744009, -0.267339, 0.105622, 0.3, 1, -0.608495, -0.740344, -0.266745, 0.102334, 0.333333, 1, -0.614404, -0.736308, -0.265829, 0.0984545, 0.366667, 1, -0.620962, -0.731797, -0.264638, 0.0940482, 0.4, 1, -0.627861, -0.726942, -0.263341, 0.0894322, 0.433333, 1, -0.63499, -0.721777, -0.262003, 0.0847291, 0.5, 1, -0.649444, -0.710759, -0.259487, 0.0755657, 0.533333, 1, -0.656319, -0.70517, -0.25857, 0.071564, 0.566667, 1, -0.662622, -0.69981, -0.25798, 0.0681528, 0.6, 1, -0.668126, -0.694881, -0.257797, 0.0654851, 0.633333, 1, -0.672623, -0.690588, -0.258089, 0.0636963, 0.666667, 1, -0.675915, -0.687131, -0.258906, 0.0629036, 0.7, 1, -0.677541, -0.684959, -0.260302, 0.0633473, 0.733333, 1, -0.67799, -0.683752, -0.261997, 0.0645727, 0.766667, 1, -0.677352, -0.683472, -0.263899, 0.0664729, 0.8, 1, -0.675721, -0.684059, -0.265918, 0.0689393, 0.833333, 1, -0.6732, -0.685443, -0.267966, 0.0718611, 0.866667, 1, -0.669749, -0.687712, -0.269888, 0.0751552, 0.9, 1, -0.665868, -0.690423, -0.271608, 0.0785159, 0.966667, 1, -0.657533, -0.696629, -0.274156, 0.084838, 1, 1, -0.653424, -0.69989, -0.274838, 0.0874912, 1.03333, 1, -0.649773, -0.703074, -0.274789, 0.0892866, 1.06667, 1, -0.646606, -0.706136, -0.274091, 0.090254, 1.1, 1, -0.643793, -0.709157, -0.272826, 0.0905183, 1.13333, 1, -0.641188, -0.712216, -0.271084, 0.0902226, 1.16667, 1, -0.638636, -0.715393, -0.268965, 0.0895279, 1.2, 1, -0.635906, -0.718848, -0.266526, 0.0885695, 1.23333, 1, -0.632904, -0.722552, -0.263969, 0.0875847, 1.26667, 1, -0.629573, -0.726504, -0.261375, 0.0866729, 1.3, 1, -0.625878, -0.730686, -0.258828, 0.0859259, 1.33333, 1, -0.621805, -0.735061, -0.25641, 0.0854279, 1.4, 1, -0.61263, -0.743965, -0.252655, 0.0858274, 1.43333, 1, -0.607887, -0.748148, -0.251445, 0.0867416, 1.46667, 1, -0.603225, -0.751966, -0.250782, 0.0881789, 1.5, 1, -0.598798, -0.755282, -0.250723, 0.090151, 1.53333, 1, -0.594994, -0.757768, -0.251377, 0.0926266, 1.56667, 1, -0.591796, -0.759583, -0.252448, 0.0952983, 1.6, 1, -0.589252, -0.760737, -0.253862, 0.0980587, 1.63333, 1, -0.587398, -0.761248, -0.255546, 0.100805, 1.66667, 1, -0.586258, -0.761139, -0.257431, 0.103439)
tracks/41/type = "scale_3d"
tracks/41/imported = true
tracks/41/enabled = true
tracks/41/path = NodePath("rig/Skeleton3D:DEF-lid3.B.R")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/keys = PackedFloat32Array(0, 1, 1.00046, 0.999078, 1.00046, 0.0333333, 1, 1.00728, 0.985633, 1.00728, 0.0666667, 1, 1.01364, 0.973305, 1.01364, 0.1, 1, 1.0193, 0.962512, 1.0193, 0.133333, 1, 1.024, 0.95368, 1.024, 0.166667, 1, 1.02747, 0.947241, 1.02747, 0.2, 1, 1.02867, 0.945028, 1.02867, 0.233333, 1, 1.02781, 0.946626, 1.02781, 0.266667, 1, 1.02512, 0.951608, 1.02512, 0.3, 1, 1.0209, 0.959493, 1.0209, 0.333333, 1, 1.01548, 0.969749, 1.01548, 0.366667, 1, 1.00901, 0.982248, 1.00901, 0.4, 1, 1.00217, 0.995737, 1.00217, 0.433333, 1, 0.995208, 1.00971, 0.995208, 0.466667, 1, 0.988412, 1.02362, 0.988412, 0.5, 1, 0.982056, 1.03688, 0.982056, 0.533333, 1, 0.976867, 1.04795, 0.976867, 0.566667, 1, 0.972998, 1.05629, 0.972998, 0.6, 1, 0.970742, 1.06119, 0.970742, 0.633333, 1, 0.970333, 1.06209, 0.970333, 0.666667, 1, 0.97195, 1.05855, 0.97195, 0.7, 1, 0.975829, 1.05017, 0.975829, 0.733333, 1, 0.980924, 1.03931, 0.980924, 0.766667, 1, 0.986925, 1.02672, 0.986925, 0.8, 1, 0.993534, 1.01309, 0.993534, 0.833333, 1, 1.00046, 0.999076, 1.00046, 0.866667, 1, 1.00728, 0.985632, 1.00728, 0.9, 1, 1.01364, 0.973304, 1.01364, 0.933333, 1, 1.0193, 0.962512, 1.0193, 0.966667, 1, 1.024, 0.95368, 1.024, 1, 1, 1.02747, 0.947241, 1.02747, 1.03333, 1, 1.02868, 0.945028, 1.02867, 1.06667, 1, 1.02781, 0.946626, 1.02781, 1.1, 1, 1.02512, 0.951608, 1.02512, 1.13333, 1, 1.0209, 0.959493, 1.0209, 1.16667, 1, 1.01548, 0.96975, 1.01548, 1.2, 1, 1.00901, 0.982247, 1.00901, 1.23333, 1, 1.00217, 0.995737, 1.00217, 1.26667, 1, 0.995208, 1.00971, 0.995208, 1.3, 1, 0.988412, 1.02362, 0.988412, 1.33333, 1, 0.982056, 1.03688, 0.982056, 1.36667, 1, 0.976866, 1.04795, 0.976867, 1.4, 1, 0.972998, 1.05629, 0.972998, 1.43333, 1, 0.970742, 1.06119, 0.970742, 1.46667, 1, 0.970333, 1.06209, 0.970333, 1.5, 1, 0.97195, 1.05855, 0.97195, 1.53333, 1, 0.975829, 1.05017, 0.975829, 1.56667, 1, 0.980923, 1.03931, 0.980924, 1.6, 1, 0.986925, 1.02672, 0.986925, 1.63333, 1, 0.993534, 1.0131, 0.993534, 1.66667, 1, 1.00046, 0.999078, 1.00046)
tracks/42/type = "position_3d"
tracks/42/imported = true
tracks/42/enabled = true
tracks/42/path = NodePath("rig/Skeleton3D:DEF-lid4.B.R")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/keys = PackedFloat32Array(0, 1, -0.167024, 0.321011, 0.492006, 0.0333333, 1, -0.164896, 0.331396, 0.485295, 0.0666667, 1, -0.162268, 0.34153, 0.479136, 0.1, 1, -0.159237, 0.350956, 0.47374, 0.133333, 1, -0.1559, 0.3592, 0.46932, 0.166667, 1, -0.152357, 0.365767, 0.466096, 0.2, 1, -0.148769, 0.368995, 0.464987, 0.233333, 1, -0.145302, 0.369112, 0.465788, 0.266667, 1, -0.141949, 0.366556, 0.468283, 0.3, 1, -0.138696, 0.361825, 0.472229, 0.333333, 1, -0.135522, 0.355484, 0.47736, 0.366667, 1, -0.132424, 0.347964, 0.483604, 0.433333, 1, -0.126312, 0.332547, 0.497307, 0.466667, 1, -0.123251, 0.325376, 0.504239, 0.5, 1, -0.120147, 0.319012, 0.510843, 0.533333, 1, -0.116911, 0.314309, 0.51635, 0.566667, 1, -0.113515, 0.311243, 0.5205, 0.6, 1, -0.109926, 0.310055, 0.522938, 0.633333, 1, -0.106133, 0.310933, 0.523383, 0.666667, 1, -0.102145, 0.314013, 0.521626, 0.7, 1, -0.0981524, 0.3195, 0.517455, 0.733333, 1, -0.0943904, 0.326299, 0.512052, 0.766667, 1, -0.0910029, 0.334105, 0.505784, 0.8, 1, -0.0881269, 0.342614, 0.498995, 0.833333, 1, -0.0858915, 0.351523, 0.492006, 0.866667, 1, -0.0847045, 0.360345, 0.485294, 0.9, 1, -0.084332, 0.368617, 0.479135, 0.933333, 1, -0.0847726, 0.375975, 0.473739, 0.966667, 1, -0.0860275, 0.382017, 0.46932, 1, 1, -0.0880999, 0.386312, 0.466095, 1.03333, 1, -0.0912946, 0.387244, 0.464986, 1.06667, 1, -0.0953404, 0.385047, 0.465787, 1.1, 1, -0.100133, 0.380092, 0.468282, 1.13333, 1, -0.105562, 0.372819, 0.472229, 1.16667, 1, -0.111506, 0.363733, 0.477359, 1.2, 1, -0.117917, 0.353113, 0.483604, 1.23333, 1, -0.124509, 0.341944, 0.490338, 1.26667, 1, -0.131164, 0.330602, 0.497307, 1.3, 1, -0.137757, 0.319465, 0.504239, 1.33333, 1, -0.144162, 0.308903, 0.510843, 1.36667, 1, -0.150045, 0.299873, 0.51635, 1.4, 1, -0.155331, 0.292592, 0.5205, 1.43333, 1, -0.159888, 0.287486, 0.522938, 1.46667, 1, -0.163608, 0.284921, 0.523383, 1.5, 1, -0.166402, 0.285214, 0.521627, 1.53333, 1, -0.168024, 0.288944, 0.517456, 1.56667, 1, -0.168854, 0.294749, 0.512053, 1.6, 1, -0.168939, 0.302273, 0.505785, 1.63333, 1, -0.168319, 0.311151, 0.498996, 1.66667, 1, -0.167024, 0.321011, 0.492006)
tracks/43/type = "rotation_3d"
tracks/43/imported = true
tracks/43/enabled = true
tracks/43/path = NodePath("rig/Skeleton3D:DEF-lid4.B.R")
tracks/43/interp = 1
tracks/43/loop_wrap = true
tracks/43/keys = PackedFloat32Array(0, 1, -0.532196, -0.802916, -0.0804045, 0.256183, 0.0333333, 1, -0.528662, -0.804147, -0.0815442, 0.259259, 0.0666667, 1, -0.525948, -0.804877, -0.0828149, 0.262094, 0.1, 1, -0.524169, -0.805088, -0.0841689, 0.264567, 0.133333, 1, -0.52347, -0.804741, -0.0855641, 0.266551, 0.166667, 1, -0.524026, -0.803778, -0.0869634, 0.267908, 0.2, 1, -0.526743, -0.80179, -0.0882963, 0.268102, 0.233333, 1, -0.53139, -0.79887, -0.0895398, 0.267235, 0.266667, 1, -0.537767, -0.795062, -0.0907084, 0.265433, 0.3, 1, -0.545627, -0.790426, -0.0918166, 0.262846, 0.333333, 1, -0.554681, -0.78504, -0.0928803, 0.259642, 0.366667, 1, -0.564831, -0.778874, -0.0939087, 0.255933, 0.4, 1, -0.575408, -0.772248, -0.0949254, 0.252047, 0.466667, 1, -0.596829, -0.758105, -0.0969787, 0.24427, 0.5, 1, -0.607167, -0.750883, -0.0980475, 0.240644, 0.533333, 1, -0.616546, -0.744031, -0.0992023, 0.237587, 0.566667, 1, -0.624736, -0.737784, -0.10044, 0.235143, 0.6, 1, -0.631421, -0.732429, -0.101769, 0.233448, 0.633333, 1, -0.636319, -0.728243, -0.103187, 0.232621, 0.666667, 1, -0.639173, -0.725482, -0.104684, 0.232757, 0.7, 1, -0.639383, -0.724671, -0.106166, 0.234031, 0.733333, 1, -0.637835, -0.725198, -0.107542, 0.235984, 0.766667, 1, -0.634714, -0.726932, -0.108757, 0.238496, 0.8, 1, -0.630212, -0.729719, -0.109761, 0.241445, 0.833333, 1, -0.624531, -0.733395, -0.110508, 0.244708, 0.866667, 1, -0.617773, -0.737897, -0.110862, 0.248142, 0.933333, 1, -0.603528, -0.747444, -0.1107, 0.254611, 0.966667, 1, -0.596709, -0.752042, -0.110207, 0.257344, 1, 1, -0.590549, -0.75625, -0.109454, 0.259532, 1.03333, 1, -0.585998, -0.759559, -0.108362, 0.26064, 1.06667, 1, -0.583018, -0.76201, -0.107005, 0.260732, 1.1, 1, -0.581336, -0.763785, -0.105398, 0.259947, 1.13333, 1, -0.580638, -0.765077, -0.103556, 0.258443, 1.16667, 1, -0.58057, -0.766092, -0.101493, 0.256398, 1.2, 1, -0.580756, -0.767074, -0.0991708, 0.253937, 1.23333, 1, -0.580769, -0.768227, -0.0966932, 0.251368, 1.26667, 1, -0.580419, -0.769643, -0.0941016, 0.248813, 1.3, 1, -0.579548, -0.771397, -0.0914448, 0.246391, 1.33333, 1, -0.578026, -0.773539, -0.0887789, 0.244216, 1.36667, 1, -0.575501, -0.776216, -0.0862471, 0.242587, 1.4, 1, -0.572209, -0.779229, -0.0839436, 0.241521, 1.43333, 1, -0.56818, -0.782507, -0.0819606, 0.241117, 1.46667, 1, -0.563473, -0.785963, -0.0803793, 0.241454, 1.5, 1, -0.558176, -0.789497, -0.0792697, 0.242593, 1.56667, 1, -0.546994, -0.795954, -0.0786897, 0.247109, 1.6, 1, -0.541648, -0.798692, -0.0789606, 0.249948, 1.63333, 1, -0.536668, -0.801027, -0.0795463, 0.253012, 1.66667, 1, -0.532196, -0.802916, -0.0804045, 0.256183)
tracks/44/type = "scale_3d"
tracks/44/imported = true
tracks/44/enabled = true
tracks/44/path = NodePath("rig/Skeleton3D:DEF-lid4.B.R")
tracks/44/interp = 1
tracks/44/loop_wrap = true
tracks/44/keys = PackedFloat32Array(0, 1, 1.00033, 0.999335, 1.00033, 0.0333333, 1, 1.00515, 0.989805, 1.00515, 0.0666667, 1, 1.0095, 0.981281, 1.0095, 0.1, 1, 1.01327, 0.973997, 1.01327, 0.133333, 1, 1.0163, 0.968175, 1.01631, 0.166667, 1, 1.01849, 0.964025, 1.01849, 0.2, 1, 1.01923, 0.962621, 1.01923, 0.233333, 1, 1.01869, 0.963638, 1.01869, 0.266667, 1, 1.01701, 0.966843, 1.01701, 0.3, 1, 1.01431, 0.971991, 1.01431, 0.333333, 1, 1.01076, 0.978821, 1.01076, 0.366667, 1, 1.00635, 0.987436, 1.00635, 0.4, 1, 1.00152, 0.996985, 1.00152, 0.433333, 1, 0.996475, 1.00712, 0.996475, 0.466667, 1, 0.991406, 1.01743, 0.991406, 0.5, 1, 0.986544, 1.02747, 0.986545, 0.533333, 1, 0.982469, 1.03602, 0.982469, 0.566667, 1, 0.979394, 1.04253, 0.979395, 0.6, 1, 0.977587, 1.04638, 0.977587, 0.633333, 1, 0.977257, 1.04709, 0.977257, 0.666667, 1, 0.97856, 1.0443, 0.97856, 0.7, 1, 0.981651, 1.03774, 0.981651, 0.733333, 1, 0.985647, 1.02936, 0.985647, 0.766667, 1, 0.990267, 1.01978, 0.990267, 0.8, 1, 0.995246, 1.0096, 0.995246, 0.833333, 1, 1.00033, 0.999336, 1.00033, 0.866667, 1, 1.00515, 0.989805, 1.00515, 0.9, 1, 1.0095, 0.981281, 1.0095, 0.933333, 1, 1.01327, 0.973997, 1.01327, 0.966667, 1, 1.01631, 0.968175, 1.01631, 1, 1, 1.01849, 0.964026, 1.01849, 1.03333, 1, 1.01923, 0.962622, 1.01923, 1.06667, 1, 1.01869, 0.963638, 1.01869, 1.1, 1, 1.01701, 0.966843, 1.01701, 1.13333, 1, 1.01431, 0.971991, 1.01431, 1.16667, 1, 1.01076, 0.978821, 1.01076, 1.2, 1, 1.00635, 0.987436, 1.00635, 1.23333, 1, 1.00152, 0.996985, 1.00152, 1.26667, 1, 0.996475, 1.00712, 0.996475, 1.3, 1, 0.991407, 1.01743, 0.991407, 1.33333, 1, 0.986544, 1.02747, 0.986544, 1.36667, 1, 0.982469, 1.03602, 0.982469, 1.4, 1, 0.979394, 1.04253, 0.979395, 1.43333, 1, 0.977587, 1.04638, 0.977587, 1.46667, 1, 0.977257, 1.04709, 0.977257, 1.5, 1, 0.97856, 1.0443, 0.97856, 1.53333, 1, 0.981652, 1.03774, 0.981652, 1.56667, 1, 0.985648, 1.02936, 0.985648, 1.6, 1, 0.990268, 1.01978, 0.990268, 1.63333, 1, 0.995246, 1.0096, 0.995246, 1.66667, 1, 1.00033, 0.999335, 1.00033)
tracks/45/type = "position_3d"
tracks/45/imported = true
tracks/45/enabled = true
tracks/45/path = NodePath("rig/Skeleton3D:DEF-lid2.T.L")
tracks/45/interp = 1
tracks/45/loop_wrap = true
tracks/45/keys = PackedFloat32Array(0, 1, 0.169625, 0.574, 0.367792, 0.0333333, 1, 0.166201, 0.589876, 0.362381, 0.0666667, 1, 0.164365, 0.6046, 0.357463, 0.1, 1, 0.164141, 0.617518, 0.353197, 0.133333, 1, 0.165551, 0.627923, 0.349748, 0.166667, 1, 0.168622, 0.635047, 0.347282, 0.2, 1, 0.173985, 0.636015, 0.346519, 0.233333, 1, 0.181066, 0.631398, 0.347268, 0.266667, 1, 0.189657, 0.621851, 0.349365, 0.3, 1, 0.19954, 0.60814, 0.352624, 0.333333, 1, 0.210481, 0.591146, 0.356842, 0.366667, 1, 0.222402, 0.571324, 0.361988, 0.4, 1, 0.234733, 0.55043, 0.367564, 0.466667, 1, 0.259696, 0.508011, 0.379214, 0.5, 1, 0.271844, 0.487754, 0.384854, 0.533333, 1, 0.283059, 0.46989, 0.389717, 0.566667, 1, 0.293171, 0.454804, 0.393578, 0.6, 1, 0.301923, 0.443182, 0.396156, 0.633333, 1, 0.309098, 0.435622, 0.397224, 0.666667, 1, 0.31452, 0.432632, 0.396611, 0.7, 1, 0.317734, 0.43517, 0.394123, 0.733333, 1, 0.31948, 0.441087, 0.390651, 0.766667, 1, 0.319875, 0.449849, 0.386467, 0.8, 1, 0.319019, 0.460914, 0.381824, 0.833333, 1, 0.317004, 0.473737, 0.376961, 0.9, 1, 0.309434, 0.501805, 0.367808, 0.933333, 1, 0.304623, 0.51518, 0.363905, 0.966667, 1, 0.299352, 0.527236, 0.360664, 1, 1, 0.293794, 0.537282, 0.35825, 1.03333, 1, 0.288237, 0.543067, 0.357334, 1.06667, 1, 0.282953, 0.544716, 0.357798, 1.1, 1, 0.277894, 0.542829, 0.359478, 1.13333, 1, 0.272997, 0.5381, 0.362193, 1.16667, 1, 0.268184, 0.531311, 0.36574, 1.2, 1, 0.263359, 0.523097, 0.370047, 1.26667, 1, 0.253353, 0.506552, 0.379401, 1.3, 1, 0.248039, 0.499212, 0.384056, 1.33333, 1, 0.242419, 0.493109, 0.388413, 1.36667, 1, 0.236236, 0.489472, 0.391886, 1.4, 1, 0.229523, 0.488244, 0.394307, 1.43333, 1, 0.222235, 0.489783, 0.395419, 1.46667, 1, 0.214369, 0.494378, 0.395024, 1.5, 1, 0.205968, 0.502243, 0.392978, 1.53333, 1, 0.197404, 0.513685, 0.389153, 1.56667, 1, 0.189201, 0.527062, 0.384447, 1.6, 1, 0.181654, 0.541898, 0.379143, 1.66667, 1, 0.169625, 0.574, 0.367792)
tracks/46/type = "rotation_3d"
tracks/46/imported = true
tracks/46/enabled = true
tracks/46/path = NodePath("rig/Skeleton3D:DEF-lid2.T.L")
tracks/46/interp = 1
tracks/46/loop_wrap = true
tracks/46/keys = PackedFloat32Array(0, 1, 0.668321, -0.655592, -0.0346575, 0.349779, 0.0333333, 1, 0.656862, -0.667555, -0.0326323, 0.349052, 0.0666667, 1, 0.645812, -0.678622, -0.0315164, 0.348433, 0.1, 1, 0.635695, -0.688386, -0.0312838, 0.347904, 0.133333, 1, 0.62706, -0.696463, -0.031924, 0.347443, 0.166667, 1, 0.620467, -0.702481, -0.0334422, 0.347021, 0.2, 1, 0.617595, -0.70508, -0.0361152, 0.346604, 0.233333, 1, 0.617729, -0.704976, -0.0395175, 0.346206, 0.266667, 1, 0.620444, -0.702527, -0.0436431, 0.345833, 0.3, 1, 0.625248, -0.698105, -0.0484855, 0.345489, 0.333333, 1, 0.631593, -0.692113, -0.054038, 0.345182, 0.366667, 1, 0.639044, -0.684803, -0.0605019, 0.344973, 0.4, 1, 0.646698, -0.676983, -0.0676263, 0.344821, 0.433333, 1, 0.65426, -0.668918, -0.0754109, 0.344683, 0.5, 1, 0.668286, -0.652906, -0.0929995, 0.344179, 0.533333, 1, 0.674473, -0.645366, -0.103103, 0.343451, 0.566667, 1, 0.680384, -0.637986, -0.113958, 0.342148, 0.6, 1, 0.686268, -0.630565, -0.125559, 0.340087, 0.633333, 1, 0.6924, -0.622846, -0.137871, 0.3371, 0.666667, 1, 0.699078, -0.614513, -0.150827, 0.333039, 0.7, 1, 0.706938, -0.604891, -0.164203, 0.327692, 0.733333, 1, 0.715647, -0.594083, -0.177473, 0.321588, 0.766667, 1, 0.725054, -0.582135, -0.19036, 0.314926, 0.833333, 1, 0.745114, -0.555471, -0.213876, 0.300855, 0.866667, 1, 0.755082, -0.541603, -0.223428, 0.294274, 0.9, 1, 0.76426, -0.528398, -0.231309, 0.28844, 0.933333, 1, 0.772308, -0.516503, -0.237301, 0.283643, 0.966667, 1, 0.778918, -0.506574, -0.241186, 0.280176, 1, 1, 0.783799, -0.499267, -0.242743, 0.278331, 1.03333, 1, 0.785831, -0.496547, -0.240769, 0.279181, 1.06667, 1, 0.785625, -0.497422, -0.236086, 0.282185, 1.1, 1, 0.783474, -0.501385, -0.228896, 0.287032, 1.13333, 1, 0.779674, -0.507843, -0.219418, 0.293358, 1.16667, 1, 0.774551, -0.516136, -0.2079, 0.300754, 1.2, 1, 0.768441, -0.525716, -0.19423, 0.308862, 1.23333, 1, 0.762012, -0.535434, -0.179387, 0.316967, 1.3, 1, 0.74917, -0.553849, -0.147499, 0.332025, 1.33333, 1, 0.743153, -0.562088, -0.131145, 0.3385, 1.36667, 1, 0.73773, -0.569512, -0.115295, 0.343681, 1.4, 1, 0.732507, -0.576587, -0.100321, 0.347729, 1.43333, 1, 0.727247, -0.58368, -0.0865428, 0.350628, 1.46667, 1, 0.721674, -0.591173, -0.0742292, 0.352409, 1.53333, 1, 0.708019, -0.609173, -0.0551528, 0.352952, 1.56667, 1, 0.699493, -0.61986, -0.0481548, 0.35237, 1.6, 1, 0.689946, -0.631326, -0.0424809, 0.351563, 1.63333, 1, 0.679495, -0.643332, -0.0380163, 0.350665, 1.66667, 1, 0.668321, -0.655592, -0.0346575, 0.349779)
tracks/47/type = "scale_3d"
tracks/47/imported = true
tracks/47/enabled = true
tracks/47/path = NodePath("rig/Skeleton3D:DEF-lid2.T.L")
tracks/47/interp = 1
tracks/47/loop_wrap = true
tracks/47/keys = PackedFloat32Array(0, 1, 0.951879, 1.10366, 0.951879, 0.0333333, 1, 0.952361, 1.10255, 0.952361, 0.0666667, 1, 0.952971, 1.10114, 0.952971, 0.1, 1, 0.953777, 1.09928, 0.953777, 0.133333, 1, 0.954853, 1.0968, 0.954853, 0.166667, 1, 0.956285, 1.09352, 0.956285, 0.2, 1, 0.958146, 1.08928, 0.958146, 0.233333, 1, 0.959961, 1.08516, 0.959961, 0.266667, 1, 0.961653, 1.08135, 0.961653, 0.3, 1, 0.963144, 1.078, 0.963143, 0.333333, 1, 0.964352, 1.0753, 0.964352, 0.366667, 1, 0.965036, 1.07377, 0.965036, 0.533333, 1, 0.965198, 1.07341, 0.965198, 0.566667, 1, 0.966306, 1.07096, 0.966306, 0.6, 1, 0.968473, 1.06618, 0.968473, 0.633333, 1, 0.971887, 1.05871, 0.971887, 0.666667, 1, 0.976661, 1.04836, 0.976661, 0.7, 1, 0.9826, 1.03576, 0.9826, 0.733333, 1, 0.988761, 1.02291, 0.988761, 0.766667, 1, 0.99476, 1.0106, 0.99476, 0.8, 1, 1.00027, 0.999477, 1.00027, 0.833333, 1, 1.00502, 0.99004, 1.00502, 0.866667, 1, 1.00852, 0.983177, 1.00852, 0.9, 1, 1.01119, 0.978003, 1.01119, 0.933333, 1, 1.01319, 0.974132, 1.01319, 0.966667, 1, 1.01474, 0.971153, 1.01474, 1, 1, 1.01607, 0.968627, 1.01607, 1.03333, 1, 1.01727, 0.966328, 1.01727, 1.06667, 1, 1.01812, 0.964732, 1.01812, 1.1, 1, 1.01838, 0.96422, 1.01838, 1.13333, 1, 1.01785, 0.96523, 1.01785, 1.16667, 1, 1.01626, 0.968248, 1.01626, 1.2, 1, 1.01285, 0.974796, 1.01285, 1.23333, 1, 1.00799, 0.984246, 1.00799, 1.26667, 1, 1.00182, 0.996426, 1.00182, 1.3, 1, 0.994573, 1.01099, 0.994573, 1.33333, 1, 0.986562, 1.02743, 0.986562, 1.36667, 1, 0.978369, 1.04476, 0.978369, 1.4, 1, 0.970724, 1.0613, 0.970724, 1.43333, 1, 0.964055, 1.07601, 0.964055, 1.46667, 1, 0.958711, 1.088, 0.958711, 1.5, 1, 0.954958, 1.09656, 0.954958, 1.53333, 1, 0.952983, 1.10111, 0.952983, 1.56667, 1, 0.951921, 1.10357, 0.95192, 1.6, 1, 0.95152, 1.1045, 0.95152, 1.66667, 1, 0.951879, 1.10366, 0.951879)
tracks/48/type = "position_3d"
tracks/48/imported = true
tracks/48/enabled = true
tracks/48/path = NodePath("rig/Skeleton3D:DEF-lid3.T.L")
tracks/48/interp = 1
tracks/48/loop_wrap = true
tracks/48/keys = PackedFloat32Array(0, 1, 0.0755417, 0.585516, 0.424434, 0.0333333, 1, 0.0719894, 0.604759, 0.417765, 0.0666667, 1, 0.0702386, 0.622649, 0.411751, 0.1, 1, 0.0702907, 0.638392, 0.406579, 0.133333, 1, 0.0721429, 0.651135, 0.402442, 0.166667, 1, 0.0757891, 0.65997, 0.399535, 0.2, 1, 0.0818017, 0.661575, 0.398726, 0.233333, 1, 0.0894767, 0.656771, 0.399754, 0.266667, 1, 0.0986129, 0.646333, 0.402423, 0.3, 1, 0.109004, 0.631158, 0.406517, 0.333333, 1, 0.120439, 0.612269, 0.411799, 0.366667, 1, 0.132866, 0.590223, 0.418251, 0.4, 1, 0.145739, 0.567006, 0.425271, 0.466667, 1, 0.171994, 0.519905, 0.440072, 0.5, 1, 0.184945, 0.497371, 0.447339, 0.533333, 1, 0.197178, 0.477288, 0.453756, 0.566667, 1, 0.208514, 0.459964, 0.45903, 0.6, 1, 0.218719, 0.446014, 0.46282, 0.633333, 1, 0.227587, 0.435959, 0.464847, 0.666667, 1, 0.234939, 0.430228, 0.464894, 0.7, 1, 0.240263, 0.429638, 0.462702, 0.733333, 1, 0.244149, 0.432168, 0.459307, 0.766667, 1, 0.246655, 0.437341, 0.455018, 0.8, 1, 0.247841, 0.444686, 0.450127, 0.833333, 1, 0.247758, 0.453741, 0.444906, 0.866667, 1, 0.246107, 0.464165, 0.439716, 0.9, 1, 0.243474, 0.47489, 0.434848, 0.933333, 1, 0.239997, 0.48541, 0.430485, 0.966667, 1, 0.235809, 0.495197, 0.426813, 1, 1, 0.231042, 0.5037, 0.424023, 1.03333, 1, 0.225785, 0.509133, 0.422872, 1.06667, 1, 0.220375, 0.51133, 0.42326, 1.1, 1, 0.214781, 0.510759, 0.425, 1.13333, 1, 0.208956, 0.507966, 0.427879, 1.16667, 1, 0.202844, 0.503576, 0.431662, 1.2, 1, 0.196264, 0.498091, 0.436243, 1.23333, 1, 0.189282, 0.492462, 0.441122, 1.3, 1, 0.174006, 0.482427, 0.450882, 1.33333, 1, 0.165668, 0.478826, 0.455284, 1.36667, 1, 0.156685, 0.477449, 0.458599, 1.4, 1, 0.147239, 0.478348, 0.460657, 1.43333, 1, 0.137378, 0.481956, 0.461175, 1.46667, 1, 0.127187, 0.48865, 0.459934, 1.5, 1, 0.116789, 0.498745, 0.456783, 1.53333, 1, 0.106672, 0.512732, 0.451605, 1.56667, 1, 0.0972191, 0.528917, 0.445454, 1.6, 1, 0.0887084, 0.546798, 0.438668, 1.66667, 1, 0.0755417, 0.585516, 0.424434)
tracks/49/type = "rotation_3d"
tracks/49/imported = true
tracks/49/enabled = true
tracks/49/path = NodePath("rig/Skeleton3D:DEF-lid3.T.L")
tracks/49/interp = 1
tracks/49/loop_wrap = true
tracks/49/keys = PackedFloat32Array(0, 1, 0.930844, -0.285944, 0.113598, 0.197133, 0.0333333, 1, 0.935264, -0.272779, 0.116887, 0.192901, 0.0666667, 1, 0.938757, -0.262215, 0.119568, 0.188895, 0.1, 1, 0.941329, -0.254521, 0.121524, 0.185341, 0.133333, 1, 0.942967, -0.249976, 0.122612, 0.18246, 0.166667, 1, 0.943637, -0.248868, 0.122669, 0.180463, 0.2, 1, 0.942814, -0.253015, 0.121067, 0.180076, 0.233333, 1, 0.940686, -0.261384, 0.118242, 0.181128, 0.266667, 1, 0.937237, -0.273652, 0.11432, 0.183394, 0.3, 1, 0.932426, -0.289443, 0.109444, 0.186617, 0.333333, 1, 0.926214, -0.308328, 0.103781, 0.1905, 0.366667, 1, 0.918323, -0.330543, 0.0974287, 0.194762, 0.4, 1, 0.909149, -0.354484, 0.0907534, 0.198877, 0.433333, 1, 0.898811, -0.379577, 0.0838366, 0.202564, 0.5, 1, 0.875485, -0.430832, 0.0694626, 0.207568, 0.533333, 1, 0.86351, -0.455312, 0.0619169, 0.207863, 0.566667, 1, 0.852014, -0.478034, 0.054014, 0.206493, 0.6, 1, 0.841469, -0.49852, 0.0456319, 0.203287, 0.633333, 1, 0.832309, -0.516391, 0.0366552, 0.198138, 0.666667, 1, 0.824903, -0.531344, 0.0269733, 0.191, 0.7, 1, 0.819876, -0.542673, 0.0164487, 0.181767, 0.733333, 1, 0.816325, -0.551518, 0.00549044, 0.171497, 0.766667, 1, 0.8141, -0.558053, -0.00571166, 0.160579, 0.8, 1, 0.813057, -0.562433, -0.0169382, 0.149402, 0.833333, 1, 0.813065, -0.564802, -0.0279404, 0.138359, 0.866667, 1, 0.81432, -0.564782, -0.0380417, 0.128283, 0.9, 1, 0.816297, -0.563187, -0.0469619, 0.119472, 0.933333, 1, 0.818925, -0.560184, -0.0543292, 0.112269, 0.966667, 1, 0.822137, -0.55594, -0.0597598, 0.107003, 1, 1, 0.825865, -0.550621, -0.0628579, 0.103983, 1.03333, 1, 0.83001, -0.544348, -0.0620989, 0.104469, 1.06667, 1, 0.834096, -0.537872, -0.058264, 0.107623, 1.1, 1, 0.838149, -0.531077, -0.0516813, 0.113099, 1.13333, 1, 0.842187, -0.523805, -0.0427259, 0.120517, 1.16667, 1, 0.84623, -0.515867, -0.031821, 0.12947, 1.2, 1, 0.850408, -0.506871, -0.0191447, 0.139721, 1.23333, 1, 0.85464, -0.496929, -0.00572678, 0.150395, 1.26667, 1, 0.858947, -0.485979, 0.00807421, 0.161153, 1.3, 1, 0.863364, -0.473974, 0.0219209, 0.171668, 1.46667, 1, 0.890917, -0.396129, 0.0798089, 0.207312, 1.53333, 1, 0.905223, -0.357718, 0.0943746, 0.209052, 1.56667, 1, 0.912387, -0.338375, 0.100114, 0.207437, 1.6, 1, 0.919155, -0.319656, 0.105166, 0.204728, 1.63333, 1, 0.925354, -0.302024, 0.109636, 0.2012, 1.66667, 1, 0.930844, -0.285944, 0.113598, 0.197133)
tracks/50/type = "scale_3d"
tracks/50/imported = true
tracks/50/enabled = true
tracks/50/path = NodePath("rig/Skeleton3D:DEF-lid3.T.L")
tracks/50/interp = 1
tracks/50/loop_wrap = true
tracks/50/keys = PackedFloat32Array(0, 1, 0.973682, 1.05479, 0.973682, 0.0666667, 1, 0.962123, 1.08033, 0.962123, 0.1, 1, 0.95683, 1.0923, 0.95683, 0.133333, 1, 0.952464, 1.10232, 0.952464, 0.166667, 1, 0.949492, 1.10922, 0.949492, 0.2, 1, 0.949232, 1.10983, 0.949232, 0.233333, 1, 0.951251, 1.10513, 0.951251, 0.266667, 1, 0.955163, 1.09612, 0.955163, 0.3, 1, 0.96051, 1.08395, 0.96051, 0.366667, 1, 0.973183, 1.05591, 0.973183, 0.4, 1, 0.979069, 1.04325, 0.979069, 0.433333, 1, 0.984106, 1.03259, 0.984106, 0.466667, 1, 0.988079, 1.02428, 0.988079, 0.5, 1, 0.990865, 1.01852, 0.990865, 0.533333, 1, 0.992238, 1.01571, 0.992238, 0.566667, 1, 0.992946, 1.01426, 0.992946, 0.6, 1, 0.993362, 1.01341, 0.993362, 0.633333, 1, 0.993854, 1.01241, 0.993854, 0.666667, 1, 0.994781, 1.01052, 0.994781, 0.7, 1, 0.996552, 1.00693, 0.996552, 0.733333, 1, 0.998918, 1.00218, 0.998917, 0.766667, 1, 1.00178, 0.996468, 1.00178, 0.8, 1, 1.00501, 0.990064, 1.00501, 0.866667, 1, 1.01197, 0.976499, 1.01197, 0.9, 1, 1.01522, 0.970246, 1.01522, 0.933333, 1, 1.01809, 0.964789, 1.01809, 0.966667, 1, 1.0204, 0.960425, 1.0204, 1, 1, 1.02198, 0.957444, 1.02198, 1.03333, 1, 1.02229, 0.956863, 1.02229, 1.06667, 1, 1.02155, 0.95826, 1.02155, 1.1, 1, 1.0199, 0.961353, 1.0199, 1.13333, 1, 1.01753, 0.96584, 1.01753, 1.16667, 1, 1.01462, 0.971392, 1.01462, 1.2, 1, 1.0113, 0.977787, 1.0113, 1.26667, 1, 1.00453, 0.991016, 1.00453, 1.3, 1, 1.00133, 0.997358, 1.00133, 1.33333, 1, 0.9984, 1.00321, 0.9984, 1.36667, 1, 0.996008, 1.00804, 0.996008, 1.4, 1, 0.994077, 1.01196, 0.994077, 1.43333, 1, 0.992564, 1.01504, 0.992564, 1.46667, 1, 0.991352, 1.01752, 0.991352, 1.5, 1, 0.990257, 1.01977, 0.990257, 1.53333, 1, 0.988665, 1.02306, 0.988665, 1.56667, 1, 0.98629, 1.028, 0.98629, 1.6, 1, 0.983006, 1.03489, 0.983006, 1.63333, 1, 0.978782, 1.04385, 0.978782, 1.66667, 1, 0.973682, 1.05479, 0.973682)
tracks/51/type = "position_3d"
tracks/51/imported = true
tracks/51/enabled = true
tracks/51/path = NodePath("rig/Skeleton3D:DEF-lid4.T.L")
tracks/51/interp = 1
tracks/51/loop_wrap = true
tracks/51/keys = PackedFloat32Array(0, 1, 0.0094304, 0.498599, 0.459033, 0.0333333, 1, 0.00762389, 0.514712, 0.452201, 0.0666667, 1, 0.00719282, 0.529831, 0.446001, 0.1, 1, 0.00811099, 0.543284, 0.440631, 0.133333, 1, 0.0103493, 0.554353, 0.436298, 0.166667, 1, 0.013876, 0.562274, 0.433208, 0.2, 1, 0.0190429, 0.564295, 0.432269, 0.233333, 1, 0.0253246, 0.561031, 0.433234, 0.266667, 1, 0.0325838, 0.553137, 0.435897, 0.3, 1, 0.0406815, 0.541372, 0.440026, 0.333333, 1, 0.0494767, 0.526597, 0.445368, 0.366667, 1, 0.0589327, 0.509319, 0.451884, 0.4, 1, 0.0686832, 0.491166, 0.458951, 0.433333, 1, 0.0785906, 0.472741, 0.466325, 0.466667, 1, 0.0885145, 0.454619, 0.473742, 0.5, 1, 0.0983109, 0.437352, 0.480921, 0.533333, 1, 0.107636, 0.422266, 0.487139, 0.566667, 1, 0.116374, 0.409573, 0.492108, 0.6, 1, 0.124372, 0.399773, 0.495476, 0.633333, 1, 0.131491, 0.393287, 0.496956, 0.666667, 1, 0.137605, 0.390458, 0.49633, 0.7, 1, 0.142307, 0.391921, 0.49335, 0.733333, 1, 0.145946, 0.395947, 0.48913, 0.766667, 1, 0.148525, 0.40211, 0.484007, 0.8, 1, 0.150046, 0.409991, 0.478298, 0.833333, 1, 0.15051, 0.419174, 0.472301, 0.9, 1, 0.147772, 0.439374, 0.460971, 0.933333, 1, 0.145142, 0.449036, 0.456125, 0.966667, 1, 0.141798, 0.457758, 0.452093, 1, 1, 0.137833, 0.465018, 0.44908, 1.03333, 1, 0.133233, 0.469069, 0.447919, 1.06667, 1, 0.128306, 0.469889, 0.44847, 1.1, 1, 0.12306, 0.46792, 0.45053, 1.13333, 1, 0.117496, 0.463681, 0.453872, 1.16667, 1, 0.111607, 0.457766, 0.458242, 1.2, 1, 0.105291, 0.450614, 0.463546, 1.23333, 1, 0.0986665, 0.443194, 0.469225, 1.26667, 1, 0.0917477, 0.435912, 0.475044, 1.3, 1, 0.0845513, 0.429168, 0.480748, 1.33333, 1, 0.0770974, 0.423349, 0.48607, 1.36667, 1, 0.0693596, 0.419539, 0.490278, 1.4, 1, 0.0614758, 0.41783, 0.493163, 1.43333, 1, 0.0535108, 0.418642, 0.49441, 1.46667, 1, 0.0455492, 0.422337, 0.493772, 1.5, 1, 0.0376954, 0.429219, 0.491073, 1.53333, 1, 0.0303509, 0.439785, 0.486158, 1.56667, 1, 0.0236782, 0.452466, 0.480151, 1.6, 1, 0.0178556, 0.466811, 0.47341, 1.63333, 1, 0.0130526, 0.482351, 0.466266, 1.66667, 1, 0.0094304, 0.498599, 0.459033)
tracks/52/type = "rotation_3d"
tracks/52/imported = true
tracks/52/enabled = true
tracks/52/path = NodePath("rig/Skeleton3D:DEF-lid4.T.L")
tracks/52/interp = 1
tracks/52/loop_wrap = true
tracks/52/keys = PackedFloat32Array(0, 1, 0.978943, -0.0507373, 0.178958, 0.0840796, 0.0333333, 1, 0.979192, -0.0416488, 0.181526, 0.0805988, 0.0666667, 1, 0.979243, -0.0355027, 0.183829, 0.077648, 0.1, 1, 0.979188, -0.0321146, 0.185706, 0.0753146, 0.133333, 1, 0.979095, -0.0313388, 0.186982, 0.0736816, 0.166667, 1, 0.97901, -0.0330686, 0.187464, 0.072828, 0.2, 1, 0.978967, -0.0381385, 0.186573, 0.0732043, 0.233333, 1, 0.978898, -0.0457598, 0.184652, 0.0746167, 0.266667, 1, 0.978713, -0.0559473, 0.181847, 0.0769538, 0.3, 1, 0.978295, -0.0687409, 0.178323, 0.0800939, 0.333333, 1, 0.977496, -0.0842041, 0.174271, 0.0839042, 0.366667, 1, 0.976025, -0.103408, 0.169915, 0.0883804, 0.4, 1, 0.973745, -0.125465, 0.165538, 0.0931491, 0.433333, 1, 0.970474, -0.150268, 0.161211, 0.0980349, 0.466667, 1, 0.966049, -0.177592, 0.156958, 0.102831, 0.5, 1, 0.960344, -0.207098, 0.152759, 0.107304, 0.566667, 1, 0.945448, -0.269571, 0.143812, 0.113042, 0.6, 1, 0.937075, -0.29962, 0.138539, 0.113692, 0.633333, 1, 0.928733, -0.327503, 0.132447, 0.112494, 0.666667, 1, 0.921005, -0.352281, 0.125341, 0.109262, 0.7, 1, 0.914794, -0.372463, 0.116927, 0.103694, 0.733333, 1, 0.909687, -0.389219, 0.107707, 0.0968388, 0.766667, 1, 0.905714, -0.4027, 0.0978931, 0.0890556, 0.8, 1, 0.902871, -0.413055, 0.0877269, 0.0807068, 0.833333, 1, 0.901126, -0.420429, 0.077482, 0.0721595, 0.866667, 1, 0.900799, -0.424099, 0.0677933, 0.0640714, 0.9, 1, 0.901389, -0.42518, 0.0590605, 0.0568464, 0.933333, 1, 0.902855, -0.423794, 0.0516796, 0.0508022, 0.966667, 1, 0.905145, -0.420065, 0.0460548, 0.0462361, 1, 1, 0.908193, -0.414107, 0.0425977, 0.0434249, 1.03333, 1, 0.911961, -0.405727, 0.0428125, 0.0433629, 1.06667, 1, 0.91583, -0.396336, 0.0459992, 0.0453546, 1.1, 1, 0.919789, -0.38587, 0.0518079, 0.0490839, 1.13333, 1, 0.923819, -0.374218, 0.0598356, 0.0542133, 1.16667, 1, 0.927911, -0.361231, 0.0696264, 0.0603849, 1.2, 1, 0.932175, -0.346374, 0.0808977, 0.0673055, 1.23333, 1, 0.93647, -0.330021, 0.0926496, 0.0743398, 1.26667, 1, 0.940773, -0.312136, 0.104506, 0.0812201, 1.3, 1, 0.945069, -0.292696, 0.116117, 0.0876918, 1.33333, 1, 0.949351, -0.271693, 0.127163, 0.0935134, 1.36667, 1, 0.953807, -0.24874, 0.136987, 0.0980564, 1.4, 1, 0.958195, -0.224476, 0.145614, 0.101336, 1.5, 1, 0.970092, -0.147117, 0.163749, 0.102295, 1.53333, 1, 0.973164, -0.122584, 0.167402, 0.0995047, 1.56667, 1, 0.975512, -0.100289, 0.170588, 0.0960081, 1.6, 1, 0.977193, -0.0806862, 0.173496, 0.0920981, 1.63333, 1, 0.978301, -0.0641035, 0.176259, 0.0880422, 1.66667, 1, 0.978943, -0.0507373, 0.178958, 0.0840796)
tracks/53/type = "scale_3d"
tracks/53/imported = true
tracks/53/enabled = true
tracks/53/path = NodePath("rig/Skeleton3D:DEF-lid4.T.L")
tracks/53/interp = 1
tracks/53/loop_wrap = true
tracks/53/keys = PackedFloat32Array(0, 1, 0.89142, 1.25845, 0.891419, 0.0333333, 1, 0.873022, 1.31248, 0.873022, 0.0666667, 1, 0.856882, 1.36243, 0.856882, 0.1, 1, 0.843474, 1.40588, 0.843474, 0.133333, 1, 0.83327, 1.44029, 0.83327, 0.166667, 1, 0.826742, 1.46305, 0.826742, 0.2, 1, 0.826141, 1.46518, 0.826141, 0.233333, 1, 0.830424, 1.45019, 0.830424, 0.266667, 1, 0.839142, 1.42043, 0.839142, 0.3, 1, 0.851797, 1.37861, 0.851797, 0.333333, 1, 0.867847, 1.32774, 0.867847, 0.366667, 1, 0.887387, 1.27038, 0.887387, 0.433333, 1, 0.93109, 1.15428, 0.93109, 0.466667, 1, 0.953866, 1.09951, 0.953867, 0.5, 1, 0.976261, 1.04922, 0.976261, 0.533333, 1, 0.996597, 1.00716, 0.996597, 0.566667, 1, 1.01442, 0.972084, 1.01442, 0.6, 1, 1.02935, 0.943948, 1.02935, 0.633333, 1, 1.04124, 0.922405, 1.04124, 0.666667, 1, 1.05014, 0.906794, 1.05014, 0.7, 1, 1.0562, 0.896435, 1.0562, 0.733333, 1, 1.06096, 0.888406, 1.06096, 0.766667, 1, 1.06477, 0.882047, 1.06477, 0.8, 1, 1.06791, 0.876861, 1.06791, 0.833333, 1, 1.07057, 0.872516, 1.07057, 0.866667, 1, 1.07265, 0.869125, 1.07265, 0.9, 1, 1.07422, 0.866591, 1.07422, 0.933333, 1, 1.07514, 0.865101, 1.07514, 0.966667, 1, 1.0753, 0.864852, 1.0753, 1, 1, 1.07456, 0.866044, 1.07456, 1.03333, 1, 1.0726, 0.869211, 1.0726, 1.06667, 1, 1.0701, 0.873285, 1.0701, 1.1, 1, 1.06715, 0.878114, 1.06715, 1.13333, 1, 1.06386, 0.883563, 1.06386, 1.16667, 1, 1.06029, 0.889509, 1.06029, 1.2, 1, 1.05654, 0.895852, 1.05654, 1.23333, 1, 1.05269, 0.902411, 1.05269, 1.26667, 1, 1.04871, 0.90928, 1.04871, 1.3, 1, 1.04448, 0.916657, 1.04448, 1.33333, 1, 1.03984, 0.924842, 1.03984, 1.36667, 1, 1.03406, 0.935228, 1.03406, 1.4, 1, 1.02679, 0.948555, 1.02679, 1.43333, 1, 1.01751, 0.966007, 1.01751, 1.46667, 1, 1.00573, 0.988791, 1.00573, 1.5, 1, 0.991053, 1.01814, 0.991053, 1.53333, 1, 0.972979, 1.05658, 0.972979, 1.56667, 1, 0.95326, 1.10102, 0.95326, 1.6, 1, 0.932616, 1.15039, 0.932616, 1.63333, 1, 0.911764, 1.20338, 0.911764, 1.66667, 1, 0.89142, 1.25845, 0.891419)
tracks/54/type = "position_3d"
tracks/54/imported = true
tracks/54/enabled = true
tracks/54/path = NodePath("rig/Skeleton3D:DEF-lid2.T.R")
tracks/54/interp = 1
tracks/54/loop_wrap = true
tracks/54/keys = PackedFloat32Array(0, 1, -0.317004, 0.473737, 0.376962, 0.0666667, 1, -0.309434, 0.501805, 0.367809, 0.1, 1, -0.304623, 0.51518, 0.363906, 0.133333, 1, -0.299352, 0.527235, 0.360665, 0.166667, 1, -0.293793, 0.537281, 0.358251, 0.2, 1, -0.288237, 0.543067, 0.357335, 0.233333, 1, -0.282953, 0.544715, 0.357798, 0.266667, 1, -0.277894, 0.542829, 0.359478, 0.3, 1, -0.272997, 0.538099, 0.362193, 0.333333, 1, -0.268184, 0.53131, 0.36574, 0.366667, 1, -0.263359, 0.523096, 0.370047, 0.433333, 1, -0.253354, 0.506552, 0.379401, 0.466667, 1, -0.248039, 0.499212, 0.384056, 0.5, 1, -0.242419, 0.493109, 0.388412, 0.533333, 1, -0.236236, 0.489472, 0.391886, 0.566667, 1, -0.229523, 0.488244, 0.394306, 0.6, 1, -0.222235, 0.489784, 0.395419, 0.633333, 1, -0.214369, 0.494379, 0.395023, 0.666667, 1, -0.205968, 0.502243, 0.392977, 0.7, 1, -0.197404, 0.513686, 0.389152, 0.733333, 1, -0.189201, 0.527063, 0.384446, 0.766667, 1, -0.181654, 0.541899, 0.379142, 0.833333, 1, -0.169625, 0.574, 0.367791, 0.866667, 1, -0.166201, 0.589877, 0.36238, 0.9, 1, -0.164365, 0.6046, 0.357462, 0.933333, 1, -0.164141, 0.617519, 0.353197, 0.966667, 1, -0.165551, 0.627923, 0.349748, 1, 1, -0.168622, 0.635047, 0.347281, 1.03333, 1, -0.173985, 0.636015, 0.346518, 1.06667, 1, -0.181066, 0.631399, 0.347268, 1.1, 1, -0.189657, 0.621851, 0.349364, 1.13333, 1, -0.19954, 0.608141, 0.352624, 1.16667, 1, -0.210481, 0.591146, 0.356842, 1.2, 1, -0.222402, 0.571325, 0.361988, 1.23333, 1, -0.234733, 0.55043, 0.367564, 1.3, 1, -0.259696, 0.508011, 0.379214, 1.33333, 1, -0.271844, 0.487754, 0.384854, 1.36667, 1, -0.283059, 0.469889, 0.389717, 1.4, 1, -0.293171, 0.454803, 0.393578, 1.43333, 1, -0.301923, 0.443182, 0.396156, 1.46667, 1, -0.309097, 0.435622, 0.397225, 1.5, 1, -0.31452, 0.432631, 0.396612, 1.53333, 1, -0.317734, 0.435169, 0.394124, 1.56667, 1, -0.31948, 0.441086, 0.390652, 1.6, 1, -0.319875, 0.449848, 0.386468, 1.63333, 1, -0.319019, 0.460914, 0.381825, 1.66667, 1, -0.317004, 0.473737, 0.376962)
tracks/55/type = "rotation_3d"
tracks/55/imported = true
tracks/55/enabled = true
tracks/55/path = NodePath("rig/Skeleton3D:DEF-lid2.T.R")
tracks/55/interp = 1
tracks/55/loop_wrap = true
tracks/55/keys = PackedFloat32Array(0, 1, 0.745114, 0.555471, 0.213877, 0.300855, 0.0333333, 1, 0.755083, 0.541602, 0.223429, 0.294273, 0.0666667, 1, 0.76426, 0.528398, 0.23131, 0.28844, 0.1, 1, 0.772308, 0.516503, 0.237301, 0.283643, 0.133333, 1, 0.778919, 0.506573, 0.241186, 0.280175, 0.166667, 1, 0.783799, 0.499266, 0.242743, 0.278331, 0.2, 1, 0.785831, 0.496547, 0.240769, 0.27918, 0.233333, 1, 0.785625, 0.497422, 0.236086, 0.282185, 0.266667, 1, 0.783474, 0.501385, 0.228896, 0.287032, 0.3, 1, 0.779674, 0.507843, 0.219418, 0.293358, 0.333333, 1, 0.77455, 0.516136, 0.207899, 0.300755, 0.366667, 1, 0.768441, 0.525715, 0.194231, 0.308862, 0.4, 1, 0.762012, 0.535434, 0.179387, 0.316967, 0.466667, 1, 0.74917, 0.553849, 0.147499, 0.332024, 0.5, 1, 0.743152, 0.562088, 0.131145, 0.3385, 0.533333, 1, 0.73773, 0.569513, 0.115295, 0.343681, 0.566667, 1, 0.732507, 0.576588, 0.100321, 0.347729, 0.6, 1, 0.727246, 0.583681, 0.0865423, 0.350628, 0.633333, 1, 0.721673, 0.591173, 0.0742287, 0.352409, 0.7, 1, 0.708019, 0.609173, 0.0551525, 0.352951, 0.733333, 1, 0.699493, 0.61986, 0.0481545, 0.35237, 0.766667, 1, 0.689946, 0.631326, 0.0424803, 0.351564, 0.8, 1, 0.679495, 0.643332, 0.0380155, 0.350665, 0.833333, 1, 0.668321, 0.655592, 0.034657, 0.349779, 0.866667, 1, 0.656862, 0.667554, 0.0326324, 0.349053, 0.9, 1, 0.645812, 0.678622, 0.0315162, 0.348434, 0.933333, 1, 0.635695, 0.688386, 0.0312833, 0.347904, 0.966667, 1, 0.62706, 0.696463, 0.0319236, 0.347443, 1, 1, 0.620466, 0.702481, 0.0334415, 0.347021, 1.03333, 1, 0.617594, 0.70508, 0.0361146, 0.346604, 1.06667, 1, 0.617728, 0.704976, 0.039517, 0.346207, 1.1, 1, 0.620444, 0.702527, 0.0436428, 0.345834, 1.13333, 1, 0.625249, 0.698105, 0.0484854, 0.345489, 1.16667, 1, 0.631593, 0.692112, 0.0540381, 0.345182, 1.2, 1, 0.639044, 0.684803, 0.0605019, 0.344974, 1.23333, 1, 0.646698, 0.676983, 0.0676265, 0.344822, 1.26667, 1, 0.65426, 0.668917, 0.0754112, 0.344684, 1.33333, 1, 0.668286, 0.652906, 0.0929995, 0.344179, 1.36667, 1, 0.674473, 0.645366, 0.103104, 0.34345, 1.4, 1, 0.680384, 0.637986, 0.113958, 0.342148, 1.43333, 1, 0.686268, 0.630565, 0.125559, 0.340087, 1.46667, 1, 0.6924, 0.622846, 0.137872, 0.3371, 1.5, 1, 0.699078, 0.614513, 0.150828, 0.333038, 1.53333, 1, 0.706938, 0.604892, 0.164203, 0.327692, 1.56667, 1, 0.715647, 0.594083, 0.177474, 0.321587, 1.6, 1, 0.725054, 0.582135, 0.190361, 0.314925, 1.66667, 1, 0.745114, 0.555471, 0.213877, 0.300855)
tracks/56/type = "scale_3d"
tracks/56/imported = true
tracks/56/enabled = true
tracks/56/path = NodePath("rig/Skeleton3D:DEF-lid2.T.R")
tracks/56/interp = 1
tracks/56/loop_wrap = true
tracks/56/keys = PackedFloat32Array(0, 1, 1.00502, 0.99004, 1.00502, 0.0333333, 1, 1.00852, 0.983177, 1.00852, 0.0666667, 1, 1.01119, 0.978002, 1.01119, 0.1, 1, 1.01319, 0.974132, 1.01319, 0.133333, 1, 1.01474, 0.971152, 1.01474, 0.166667, 1, 1.01607, 0.968625, 1.01607, 0.2, 1, 1.01727, 0.966328, 1.01727, 0.233333, 1, 1.01812, 0.964731, 1.01812, 0.266667, 1, 1.01839, 0.964219, 1.01839, 0.3, 1, 1.01785, 0.965228, 1.01785, 0.333333, 1, 1.01627, 0.968245, 1.01627, 0.366667, 1, 1.01285, 0.974794, 1.01285, 0.4, 1, 1.00799, 0.984246, 1.00799, 0.433333, 1, 1.00182, 0.996427, 1.00182, 0.466667, 1, 0.994573, 1.01099, 0.994573, 0.5, 1, 0.986563, 1.02743, 0.986563, 0.533333, 1, 0.97837, 1.04476, 0.97837, 0.566667, 1, 0.970725, 1.0613, 0.970725, 0.6, 1, 0.964056, 1.076, 0.964056, 0.633333, 1, 0.958711, 1.088, 0.958711, 0.666667, 1, 0.954958, 1.09656, 0.954958, 0.7, 1, 0.952983, 1.10111, 0.952983, 0.733333, 1, 0.95192, 1.10357, 0.95192, 0.766667, 1, 0.95152, 1.1045, 0.95152, 0.833333, 1, 0.95188, 1.10366, 0.95188, 0.866667, 1, 0.952362, 1.10255, 0.952362, 0.9, 1, 0.952971, 1.10113, 0.952972, 0.933333, 1, 0.953777, 1.09928, 0.953777, 0.966667, 1, 0.954854, 1.0968, 0.954854, 1, 1, 0.956286, 1.09351, 0.956286, 1.03333, 1, 0.958146, 1.08928, 0.958146, 1.06667, 1, 0.959961, 1.08516, 0.959961, 1.1, 1, 0.961654, 1.08134, 0.961654, 1.13333, 1, 0.963144, 1.078, 0.963144, 1.16667, 1, 0.964353, 1.0753, 0.964353, 1.2, 1, 0.965036, 1.07377, 0.965036, 1.36667, 1, 0.965198, 1.07341, 0.965198, 1.4, 1, 0.966306, 1.07096, 0.966306, 1.43333, 1, 0.968473, 1.06618, 0.968473, 1.46667, 1, 0.971887, 1.05871, 0.971887, 1.5, 1, 0.976661, 1.04836, 0.976662, 1.53333, 1, 0.9826, 1.03576, 0.9826, 1.56667, 1, 0.988761, 1.02291, 0.988761, 1.6, 1, 0.994761, 1.0106, 0.994761, 1.63333, 1, 1.00027, 0.999477, 1.00027, 1.66667, 1, 1.00502, 0.99004, 1.00502)
tracks/57/type = "position_3d"
tracks/57/imported = true
tracks/57/enabled = true
tracks/57/path = NodePath("rig/Skeleton3D:DEF-lid3.T.R")
tracks/57/interp = 1
tracks/57/loop_wrap = true
tracks/57/keys = PackedFloat32Array(0, 1, -0.247758, 0.45374, 0.444907, 0.0333333, 1, -0.246107, 0.464165, 0.439717, 0.0666667, 1, -0.243473, 0.474889, 0.434849, 0.1, 1, -0.239996, 0.485409, 0.430486, 0.133333, 1, -0.235809, 0.495196, 0.426814, 0.166667, 1, -0.231042, 0.503699, 0.424023, 0.2, 1, -0.225785, 0.509132, 0.422873, 0.233333, 1, -0.220375, 0.51133, 0.423261, 0.266667, 1, -0.214781, 0.510759, 0.425, 0.3, 1, -0.208957, 0.507966, 0.427879, 0.333333, 1, -0.202844, 0.503576, 0.431662, 0.366667, 1, -0.196264, 0.498091, 0.436244, 0.4, 1, -0.189282, 0.492462, 0.441122, 0.466667, 1, -0.174006, 0.482427, 0.450882, 0.5, 1, -0.165668, 0.478826, 0.455284, 0.533333, 1, -0.156685, 0.47745, 0.458598, 0.566667, 1, -0.147239, 0.478348, 0.460657, 0.6, 1, -0.137378, 0.481957, 0.461174, 0.633333, 1, -0.127187, 0.488651, 0.459933, 0.666667, 1, -0.116789, 0.498746, 0.456782, 0.7, 1, -0.106672, 0.512733, 0.451605, 0.733333, 1, -0.0972191, 0.528917, 0.445453, 0.766667, 1, -0.0887084, 0.546799, 0.438667, 0.833333, 1, -0.0755417, 0.585516, 0.424433, 0.866667, 1, -0.0719894, 0.60476, 0.417764, 0.9, 1, -0.0702386, 0.62265, 0.41175, 0.933333, 1, -0.0702907, 0.638392, 0.406578, 0.966667, 1, -0.0721429, 0.651136, 0.402442, 1, 1, -0.0757892, 0.65997, 0.399534, 1.03333, 1, -0.0818017, 0.661575, 0.398725, 1.06667, 1, -0.0894767, 0.656771, 0.399753, 1.1, 1, -0.098613, 0.646333, 0.402422, 1.13333, 1, -0.109004, 0.631159, 0.406516, 1.16667, 1, -0.120439, 0.612269, 0.411798, 1.2, 1, -0.132866, 0.590223, 0.418251, 1.23333, 1, -0.145739, 0.567006, 0.425271, 1.3, 1, -0.171994, 0.519905, 0.440072, 1.33333, 1, -0.184945, 0.497371, 0.44734, 1.36667, 1, -0.197178, 0.477288, 0.453756, 1.4, 1, -0.208514, 0.459964, 0.45903, 1.43333, 1, -0.218719, 0.446013, 0.46282, 1.46667, 1, -0.227587, 0.435958, 0.464848, 1.5, 1, -0.234939, 0.430227, 0.464895, 1.53333, 1, -0.240263, 0.429637, 0.462702, 1.56667, 1, -0.244149, 0.432168, 0.459308, 1.6, 1, -0.246655, 0.43734, 0.455019, 1.63333, 1, -0.247841, 0.444685, 0.450127, 1.66667, 1, -0.247758, 0.45374, 0.444907)
tracks/58/type = "rotation_3d"
tracks/58/imported = true
tracks/58/enabled = true
tracks/58/path = NodePath("rig/Skeleton3D:DEF-lid3.T.R")
tracks/58/interp = 1
tracks/58/loop_wrap = true
tracks/58/keys = PackedFloat32Array(0, 1, 0.813064, 0.564803, 0.0279406, 0.138359, 0.0333333, 1, 0.81432, 0.564783, 0.038042, 0.128281, 0.0666667, 1, 0.816298, 0.563187, 0.0469622, 0.119471, 0.1, 1, 0.818925, 0.560183, 0.0543296, 0.112269, 0.133333, 1, 0.822137, 0.55594, 0.0597604, 0.107003, 0.166667, 1, 0.825866, 0.55062, 0.0628578, 0.103983, 0.2, 1, 0.83001, 0.544348, 0.0620994, 0.104468, 0.233333, 1, 0.834096, 0.537873, 0.0582643, 0.107623, 0.266667, 1, 0.838149, 0.531078, 0.0516813, 0.113099, 0.3, 1, 0.842187, 0.523805, 0.0427257, 0.120517, 0.333333, 1, 0.846231, 0.515867, 0.0318199, 0.12947, 0.366667, 1, 0.850407, 0.506872, 0.0191448, 0.139721, 0.4, 1, 0.85464, 0.496929, 0.00572661, 0.150395, 0.433333, 1, 0.858947, 0.485978, -0.00807442, 0.161152, 0.466667, 1, 0.863365, 0.473974, -0.0219208, 0.171668, 0.633333, 1, 0.890917, 0.396129, -0.0798091, 0.207313, 0.7, 1, 0.905223, 0.357718, -0.0943748, 0.209053, 0.733333, 1, 0.912387, 0.338375, -0.100114, 0.207437, 0.766667, 1, 0.919155, 0.319656, -0.105166, 0.20473, 0.8, 1, 0.925354, 0.302024, -0.109636, 0.201202, 0.833333, 1, 0.930844, 0.285944, -0.113598, 0.197134, 0.866667, 1, 0.935264, 0.272779, -0.116886, 0.192902, 0.9, 1, 0.938757, 0.262215, -0.119568, 0.188895, 0.933333, 1, 0.941329, 0.254521, -0.121523, 0.185342, 0.966667, 1, 0.942967, 0.249976, -0.122611, 0.182462, 1, 1, 0.943636, 0.248868, -0.122669, 0.180464, 1.03333, 1, 0.942814, 0.253015, -0.121067, 0.180077, 1.06667, 1, 0.940686, 0.261384, -0.118243, 0.181128, 1.1, 1, 0.937236, 0.273652, -0.11432, 0.183395, 1.13333, 1, 0.932426, 0.289443, -0.109443, 0.186617, 1.16667, 1, 0.926214, 0.308328, -0.103781, 0.190501, 1.2, 1, 0.918323, 0.330543, -0.097429, 0.194762, 1.23333, 1, 0.909149, 0.354484, -0.0907536, 0.198877, 1.26667, 1, 0.898811, 0.379577, -0.0838364, 0.202564, 1.33333, 1, 0.875485, 0.430832, -0.0694625, 0.207568, 1.36667, 1, 0.86351, 0.455313, -0.0619166, 0.207863, 1.4, 1, 0.852013, 0.478034, -0.0540137, 0.206493, 1.43333, 1, 0.841469, 0.498521, -0.0456317, 0.203286, 1.46667, 1, 0.832309, 0.516391, -0.036655, 0.198138, 1.5, 1, 0.824903, 0.531345, -0.026973, 0.190999, 1.53333, 1, 0.819876, 0.542673, -0.0164487, 0.181766, 1.56667, 1, 0.816325, 0.551518, -0.00548962, 0.171497, 1.6, 1, 0.8141, 0.558053, 0.00571282, 0.160579, 1.63333, 1, 0.813056, 0.562434, 0.0169389, 0.149401, 1.66667, 1, 0.813064, 0.564803, 0.0279406, 0.138359)
tracks/59/type = "scale_3d"
tracks/59/imported = true
tracks/59/enabled = true
tracks/59/path = NodePath("rig/Skeleton3D:DEF-lid3.T.R")
tracks/59/interp = 1
tracks/59/loop_wrap = true
tracks/59/keys = PackedFloat32Array(0, 1, 1.00848, 0.98325, 1.00848, 0.0333333, 1, 1.01197, 0.976499, 1.01197, 0.0666667, 1, 1.01522, 0.970247, 1.01522, 0.1, 1, 1.01809, 0.96479, 1.01809, 0.133333, 1, 1.0204, 0.960426, 1.0204, 0.166667, 1, 1.02198, 0.957446, 1.02198, 0.2, 1, 1.02229, 0.956863, 1.02229, 0.233333, 1, 1.02155, 0.95826, 1.02155, 0.266667, 1, 1.0199, 0.961354, 1.0199, 0.3, 1, 1.01753, 0.96584, 1.01753, 0.333333, 1, 1.01462, 0.971393, 1.01462, 0.366667, 1, 1.0113, 0.977788, 1.0113, 0.433333, 1, 1.00453, 0.991017, 1.00453, 0.466667, 1, 1.00133, 0.997359, 1.00133, 0.5, 1, 0.998399, 1.00321, 0.998399, 0.533333, 1, 0.996007, 1.00804, 0.996007, 0.566667, 1, 0.994076, 1.01196, 0.994077, 0.6, 1, 0.992563, 1.01504, 0.992563, 0.633333, 1, 0.991352, 1.01752, 0.991352, 0.666667, 1, 0.990256, 1.01978, 0.990256, 0.7, 1, 0.988665, 1.02306, 0.988665, 0.733333, 1, 0.986289, 1.028, 0.986289, 0.766667, 1, 0.983005, 1.03489, 0.983005, 0.8, 1, 0.978782, 1.04385, 0.978782, 0.833333, 1, 0.973682, 1.05479, 0.973682, 0.9, 1, 0.962123, 1.08033, 0.962123, 0.933333, 1, 0.95683, 1.0923, 0.95683, 0.966667, 1, 0.952464, 1.10232, 0.952464, 1, 1, 0.949492, 1.10922, 0.949492, 1.03333, 1, 0.949232, 1.10983, 0.949232, 1.06667, 1, 0.951251, 1.10513, 0.951251, 1.1, 1, 0.955163, 1.09612, 0.955163, 1.13333, 1, 0.96051, 1.08395, 0.96051, 1.2, 1, 0.973183, 1.05591, 0.973183, 1.23333, 1, 0.979068, 1.04326, 0.979068, 1.26667, 1, 0.984105, 1.03259, 0.984105, 1.3, 1, 0.988078, 1.02428, 0.988078, 1.33333, 1, 0.990865, 1.01852, 0.990865, 1.36667, 1, 0.992237, 1.01571, 0.992237, 1.4, 1, 0.992945, 1.01426, 0.992945, 1.43333, 1, 0.993362, 1.01341, 0.993362, 1.46667, 1, 0.993854, 1.01241, 0.993854, 1.5, 1, 0.994781, 1.01052, 0.994781, 1.53333, 1, 0.996552, 1.00693, 0.996552, 1.56667, 1, 0.998917, 1.00218, 0.998917, 1.6, 1, 1.00178, 0.996469, 1.00178, 1.63333, 1, 1.00501, 0.990065, 1.00501, 1.66667, 1, 1.00848, 0.98325, 1.00848)
tracks/60/type = "position_3d"
tracks/60/imported = true
tracks/60/enabled = true
tracks/60/path = NodePath("rig/Skeleton3D:DEF-lid4.T.R")
tracks/60/interp = 1
tracks/60/loop_wrap = true
tracks/60/keys = PackedFloat32Array(0, 1, -0.150509, 0.419173, 0.472302, 0.0666667, 1, -0.147771, 0.439373, 0.460972, 0.1, 1, -0.145142, 0.449036, 0.456126, 0.133333, 1, -0.141798, 0.457757, 0.452094, 0.166667, 1, -0.137833, 0.465017, 0.44908, 0.2, 1, -0.133233, 0.469069, 0.44792, 0.233333, 1, -0.128306, 0.469889, 0.448471, 0.266667, 1, -0.12306, 0.46792, 0.450531, 0.3, 1, -0.117496, 0.463681, 0.453872, 0.333333, 1, -0.111607, 0.457766, 0.458242, 0.366667, 1, -0.105291, 0.450613, 0.463546, 0.4, 1, -0.0986665, 0.443194, 0.469225, 0.433333, 1, -0.0917476, 0.435912, 0.475044, 0.466667, 1, -0.0845512, 0.429168, 0.480748, 0.5, 1, -0.0770974, 0.423349, 0.48607, 0.533333, 1, -0.0693596, 0.41954, 0.490277, 0.566667, 1, -0.0614758, 0.417831, 0.493163, 0.6, 1, -0.0535108, 0.418642, 0.49441, 0.633333, 1, -0.0455491, 0.422337, 0.493771, 0.666667, 1, -0.0376953, 0.42922, 0.491073, 0.7, 1, -0.0303509, 0.439786, 0.486157, 0.733333, 1, -0.0236782, 0.452467, 0.480151, 0.766667, 1, -0.0178555, 0.466812, 0.473409, 0.8, 1, -0.0130525, 0.482352, 0.466266, 0.833333, 1, -0.00943034, 0.4986, 0.459032, 0.866667, 1, -0.0076238, 0.514712, 0.452201, 0.9, 1, -0.00719278, 0.529832, 0.446, 0.933333, 1, -0.00811098, 0.543285, 0.440631, 0.966667, 1, -0.0103493, 0.554354, 0.436297, 1, 1, -0.0138759, 0.562275, 0.433207, 1.03333, 1, -0.0190429, 0.564295, 0.432268, 1.06667, 1, -0.0253245, 0.561031, 0.433234, 1.1, 1, -0.0325837, 0.553137, 0.435897, 1.13333, 1, -0.0406814, 0.541372, 0.440026, 1.16667, 1, -0.0494767, 0.526597, 0.445367, 1.2, 1, -0.0589326, 0.509319, 0.451884, 1.23333, 1, -0.0686831, 0.491166, 0.458951, 1.26667, 1, -0.0785906, 0.472741, 0.466325, 1.3, 1, -0.0885144, 0.454619, 0.473742, 1.33333, 1, -0.0983108, 0.437352, 0.480921, 1.36667, 1, -0.107636, 0.422266, 0.487139, 1.4, 1, -0.116374, 0.409573, 0.492109, 1.43333, 1, -0.124372, 0.399772, 0.495477, 1.46667, 1, -0.131491, 0.393287, 0.496957, 1.5, 1, -0.137605, 0.390458, 0.496331, 1.53333, 1, -0.142307, 0.391921, 0.493351, 1.56667, 1, -0.145946, 0.395946, 0.489131, 1.6, 1, -0.148525, 0.402109, 0.484008, 1.63333, 1, -0.150046, 0.40999, 0.478299, 1.66667, 1, -0.150509, 0.419173, 0.472302)
tracks/61/type = "rotation_3d"
tracks/61/imported = true
tracks/61/enabled = true
tracks/61/path = NodePath("rig/Skeleton3D:DEF-lid4.T.R")
tracks/61/interp = 1
tracks/61/loop_wrap = true
tracks/61/keys = PackedFloat32Array(0, 1, 0.901127, 0.420429, -0.0774823, 0.0721585, 0.0333333, 1, 0.900799, 0.424099, -0.0677926, 0.064071, 0.0666667, 1, 0.901389, 0.42518, -0.0590602, 0.0568456, 0.1, 1, 0.902855, 0.423794, -0.0516797, 0.0508009, 0.133333, 1, 0.905145, 0.420065, -0.0460547, 0.0462348, 0.166667, 1, 0.908193, 0.414108, -0.0425977, 0.0434246, 0.2, 1, 0.911961, 0.405727, -0.0428119, 0.0433626, 0.233333, 1, 0.91583, 0.396337, -0.0459987, 0.0453548, 0.266667, 1, 0.919788, 0.385871, -0.0518078, 0.049084, 0.3, 1, 0.923819, 0.374218, -0.0598359, 0.0542133, 0.333333, 1, 0.927911, 0.36123, -0.0696273, 0.0603855, 0.366667, 1, 0.932175, 0.346374, -0.0808977, 0.0673054, 0.4, 1, 0.93647, 0.330021, -0.0926497, 0.07434, 0.433333, 1, 0.940773, 0.312136, -0.104506, 0.0812205, 0.466667, 1, 0.945069, 0.292696, -0.116117, 0.0876921, 0.5, 1, 0.949351, 0.271694, -0.127163, 0.0935142, 0.533333, 1, 0.953807, 0.24874, -0.136987, 0.0980569, 0.566667, 1, 0.958195, 0.224476, -0.145614, 0.101336, 0.666667, 1, 0.970092, 0.147117, -0.163749, 0.102296, 0.7, 1, 0.973164, 0.122585, -0.167401, 0.0995059, 0.733333, 1, 0.975512, 0.100289, -0.170589, 0.0960088, 0.766667, 1, 0.977193, 0.0806861, -0.173496, 0.0920985, 0.8, 1, 0.978301, 0.0641035, -0.176258, 0.0880424, 0.833333, 1, 0.978943, 0.0507372, -0.178958, 0.0840806, 0.866667, 1, 0.979192, 0.0416486, -0.181526, 0.0805994, 0.9, 1, 0.979243, 0.0355026, -0.183829, 0.0776487, 0.933333, 1, 0.979188, 0.0321147, -0.185706, 0.075315, 0.966667, 1, 0.979095, 0.0313388, -0.186981, 0.0736814, 1, 1, 0.979009, 0.0330682, -0.187465, 0.0728281, 1.03333, 1, 0.978967, 0.0381385, -0.186573, 0.0732045, 1.06667, 1, 0.978898, 0.0457596, -0.184653, 0.074617, 1.1, 1, 0.978713, 0.0559471, -0.181847, 0.0769541, 1.13333, 1, 0.978295, 0.0687409, -0.178323, 0.0800941, 1.16667, 1, 0.977496, 0.0842039, -0.174271, 0.0839047, 1.2, 1, 0.976025, 0.103408, -0.169915, 0.0883806, 1.23333, 1, 0.973745, 0.125465, -0.165538, 0.0931495, 1.26667, 1, 0.970474, 0.150267, -0.161211, 0.0980348, 1.3, 1, 0.966049, 0.177592, -0.156958, 0.102831, 1.33333, 1, 0.960344, 0.207098, -0.152759, 0.107303, 1.4, 1, 0.945448, 0.26957, -0.143812, 0.113041, 1.43333, 1, 0.937075, 0.299619, -0.138539, 0.113691, 1.46667, 1, 0.928733, 0.327502, -0.132447, 0.112493, 1.5, 1, 0.921005, 0.352281, -0.125341, 0.109261, 1.53333, 1, 0.914794, 0.372462, -0.116927, 0.103693, 1.56667, 1, 0.909687, 0.389219, -0.107707, 0.096837, 1.6, 1, 0.905715, 0.4027, -0.0978926, 0.0890538, 1.63333, 1, 0.902871, 0.413055, -0.0877264, 0.0807062, 1.66667, 1, 0.901127, 0.420429, -0.0774823, 0.0721585)
tracks/62/type = "scale_3d"
tracks/62/imported = true
tracks/62/enabled = true
tracks/62/path = NodePath("rig/Skeleton3D:DEF-lid4.T.R")
tracks/62/interp = 1
tracks/62/loop_wrap = true
tracks/62/keys = PackedFloat32Array(0, 1, 1.07057, 0.872515, 1.07057, 0.0333333, 1, 1.07265, 0.869124, 1.07265, 0.0666667, 1, 1.07422, 0.866589, 1.07422, 0.1, 1, 1.07515, 0.865099, 1.07515, 0.133333, 1, 1.0753, 0.86485, 1.0753, 0.166667, 1, 1.07456, 0.866041, 1.07456, 0.2, 1, 1.0726, 0.86921, 1.0726, 0.233333, 1, 1.0701, 0.873284, 1.0701, 0.266667, 1, 1.06715, 0.878113, 1.06715, 0.3, 1, 1.06386, 0.883562, 1.06386, 0.333333, 1, 1.06029, 0.889508, 1.06029, 0.366667, 1, 1.05654, 0.89585, 1.05654, 0.4, 1, 1.05269, 0.90241, 1.05269, 0.433333, 1, 1.04871, 0.909279, 1.04871, 0.466667, 1, 1.04448, 0.916655, 1.04448, 0.5, 1, 1.03984, 0.92484, 1.03984, 0.533333, 1, 1.03406, 0.935226, 1.03406, 0.566667, 1, 1.02679, 0.948553, 1.02679, 0.6, 1, 1.01751, 0.966006, 1.01751, 0.633333, 1, 1.00573, 0.98879, 1.00573, 0.666667, 1, 0.991054, 1.01813, 0.991054, 0.7, 1, 0.97298, 1.05658, 0.97298, 0.733333, 1, 0.953261, 1.10102, 0.953261, 0.766667, 1, 0.932617, 1.15039, 0.932617, 0.8, 1, 0.911764, 1.20337, 0.911764, 0.833333, 1, 0.89142, 1.25845, 0.89142, 0.866667, 1, 0.873022, 1.31248, 0.873022, 0.9, 1, 0.856883, 1.36243, 0.856883, 0.933333, 1, 0.843475, 1.40587, 0.843475, 0.966667, 1, 0.83327, 1.44029, 0.83327, 1, 1, 0.826743, 1.46305, 0.826743, 1.03333, 1, 0.826142, 1.46518, 0.826142, 1.06667, 1, 0.830425, 1.45019, 0.830425, 1.1, 1, 0.839142, 1.42043, 0.839142, 1.13333, 1, 0.851797, 1.37861, 0.851797, 1.16667, 1, 0.867847, 1.32774, 0.867847, 1.2, 1, 0.887388, 1.27038, 0.887388, 1.26667, 1, 0.93109, 1.15428, 0.93109, 1.3, 1, 0.953867, 1.09951, 0.953867, 1.33333, 1, 0.976262, 1.04922, 0.976262, 1.36667, 1, 0.996598, 1.00716, 0.996598, 1.4, 1, 1.01442, 0.972082, 1.01442, 1.43333, 1, 1.02935, 0.943947, 1.02935, 1.46667, 1, 1.04124, 0.922405, 1.04124, 1.5, 1, 1.05014, 0.906793, 1.05014, 1.53333, 1, 1.0562, 0.896434, 1.0562, 1.56667, 1, 1.06096, 0.888406, 1.06096, 1.6, 1, 1.06477, 0.882046, 1.06477, 1.63333, 1, 1.06791, 0.876859, 1.06791, 1.66667, 1, 1.07057, 0.872515, 1.07057)
tracks/63/type = "position_3d"
tracks/63/imported = true
tracks/63/enabled = true
tracks/63/path = NodePath("rig/Skeleton3D:DEF-lip1.B.L")
tracks/63/interp = 1
tracks/63/loop_wrap = true
tracks/63/keys = PackedFloat32Array(0, 1, 0.116954, 0.10855, 0.418848, 0.0333333, 1, 0.115404, 0.110416, 0.413135, 0.0666667, 1, 0.114196, 0.11202, 0.407892, 0.1, 1, 0.113363, 0.113294, 0.403298, 0.133333, 1, 0.112943, 0.114156, 0.399535, 0.166667, 1, 0.112975, 0.114508, 0.39679, 0.2, 1, 0.113715, 0.11392, 0.395846, 0.233333, 1, 0.115048, 0.112459, 0.396528, 0.266667, 1, 0.116905, 0.110204, 0.398652, 0.3, 1, 0.119209, 0.107254, 0.402012, 0.333333, 1, 0.121876, 0.103728, 0.406379, 0.366667, 1, 0.124871, 0.0996461, 0.411696, 0.4, 1, 0.127996, 0.0953019, 0.417428, 0.466667, 1, 0.134293, 0.086257, 0.429262, 0.5, 1, 0.137282, 0.081787, 0.434884, 0.533333, 1, 0.139886, 0.0776929, 0.439573, 0.566667, 1, 0.142045, 0.0741251, 0.443105, 0.6, 1, 0.143663, 0.0712679, 0.445181, 0.633333, 1, 0.144666, 0.0692899, 0.445559, 0.666667, 1, 0.144999, 0.0683447, 0.444064, 0.7, 1, 0.144575, 0.0687296, 0.440513, 0.733333, 1, 0.143725, 0.0699703, 0.435914, 0.766667, 1, 0.142531, 0.0719492, 0.430578, 0.8, 1, 0.141071, 0.0745429, 0.424798, 0.866667, 1, 0.137602, 0.0810733, 0.413135, 0.9, 1, 0.13577, 0.0845641, 0.407892, 0.933333, 1, 0.133976, 0.0879357, 0.403298, 0.966667, 1, 0.132285, 0.0910286, 0.399535, 1, 1, 0.130763, 0.0936836, 0.39679, 1.03333, 1, 0.129625, 0.0954228, 0.395846, 1.06667, 1, 0.128879, 0.0963083, 0.396528, 1.1, 1, 0.128481, 0.096484, 0.398652, 1.13333, 1, 0.128382, 0.0961108, 0.402012, 1.16667, 1, 0.128524, 0.0953668, 0.406379, 1.2, 1, 0.128887, 0.0944272, 0.411696, 1.23333, 1, 0.129339, 0.0935286, 0.417428, 1.3, 1, 0.130277, 0.092249, 0.429262, 1.33333, 1, 0.130634, 0.0920335, 0.434884, 1.36667, 1, 0.130713, 0.0923258, 0.439573, 1.4, 1, 0.130469, 0.0930296, 0.443105, 1.43333, 1, 0.129833, 0.0941444, 0.445181, 1.46667, 1, 0.128756, 0.0956556, 0.445559, 1.5, 1, 0.127212, 0.0975345, 0.444064, 1.53333, 1, 0.125234, 0.0997009, 0.440513, 1.56667, 1, 0.123112, 0.101949, 0.435914, 1.6, 1, 0.120958, 0.104214, 0.430578, 1.63333, 1, 0.118874, 0.106433, 0.424798, 1.66667, 1, 0.116954, 0.10855, 0.418848)
tracks/64/type = "rotation_3d"
tracks/64/imported = true
tracks/64/enabled = true
tracks/64/path = NodePath("rig/Skeleton3D:DEF-lip1.B.L")
tracks/64/interp = 1
tracks/64/loop_wrap = true
tracks/64/keys = PackedFloat32Array(0, 1, -0.444899, -0.245036, -0.369555, 0.778108, 0.0333333, 1, -0.442691, -0.244816, -0.368573, 0.7799, 0.0666667, 1, -0.440767, -0.244247, -0.368279, 0.781305, 0.1, 1, -0.439214, -0.243365, -0.368678, 0.782267, 0.133333, 1, -0.438131, -0.242188, -0.3698, 0.782709, 0.166667, 1, -0.437633, -0.240724, -0.371694, 0.782542, 0.2, 1, -0.438208, -0.238838, -0.374816, 0.781309, 0.233333, 1, -0.439761, -0.236579, -0.379001, 0.779101, 0.266667, 1, -0.442171, -0.233945, -0.384164, 0.775998, 0.3, 1, -0.445296, -0.23094, -0.390192, 0.772094, 0.333333, 1, -0.448968, -0.227577, -0.396951, 0.767507, 0.366667, 1, -0.453096, -0.223793, -0.404442, 0.762265, 0.4, 1, -0.457354, -0.219737, -0.412276, 0.756685, 0.5, 1, -0.469683, -0.206503, -0.436386, 0.739136, 0.533333, 1, -0.473087, -0.202087, -0.443889, 0.733698, 0.566667, 1, -0.475918, -0.197948, -0.450694, 0.728831, 0.6, 1, -0.478061, -0.19425, -0.456566, 0.724757, 0.633333, 1, -0.479418, -0.191155, -0.461285, 0.721689, 0.666667, 1, -0.479903, -0.188818, -0.464643, 0.719825, 0.7, 1, -0.479316, -0.18758, -0.46614, 0.719572, 0.733333, 1, -0.478046, -0.187137, -0.466333, 0.720406, 0.766667, 1, -0.476184, -0.187434, -0.465317, 0.722217, 0.8, 1, -0.473819, -0.188409, -0.463198, 0.724876, 0.833333, 1, -0.471045, -0.189987, -0.460089, 0.728244, 0.866667, 1, -0.467946, -0.192191, -0.455954, 0.732253, 0.9, 1, -0.464797, -0.194673, -0.451358, 0.736439, 0.933333, 1, -0.461742, -0.197308, -0.446517, 0.740599, 0.966667, 1, -0.458935, -0.199984, -0.441647, 0.744536, 1, 1, -0.456538, -0.202595, -0.436962, 0.748059, 1.03333, 1, -0.455051, -0.204937, -0.432955, 0.750653, 1.06667, 1, -0.454432, -0.207004, -0.429658, 0.752353, 1.1, 1, -0.454546, -0.208886, -0.426912, 0.753327, 1.16667, 1, -0.456313, -0.212467, -0.422343, 0.753832, 1.2, 1, -0.457608, -0.214415, -0.420065, 0.753769, 1.23333, 1, -0.458868, -0.216556, -0.417542, 0.753792, 1.26667, 1, -0.459988, -0.218914, -0.414694, 0.754, 1.3, 1, -0.460875, -0.221498, -0.411465, 0.754473, 1.33333, 1, -0.461447, -0.224302, -0.407819, 0.755274, 1.36667, 1, -0.461491, -0.227323, -0.403658, 0.756579, 1.4, 1, -0.461084, -0.230395, -0.399208, 0.758257, 1.43333, 1, -0.460207, -0.233419, -0.394574, 0.760287, 1.46667, 1, -0.458854, -0.236293, -0.389876, 0.762637, 1.5, 1, -0.457035, -0.23891, -0.385244, 0.765264, 1.53333, 1, -0.454793, -0.241024, -0.381024, 0.768044, 1.56667, 1, -0.452377, -0.242698, -0.377279, 0.770787, 1.6, 1, -0.44987, -0.243925, -0.37408, 0.773422, 1.63333, 1, -0.447351, -0.244702, -0.371488, 0.775883, 1.66667, 1, -0.444899, -0.245036, -0.369555, 0.778108)
tracks/65/type = "scale_3d"
tracks/65/imported = true
tracks/65/enabled = true
tracks/65/path = NodePath("rig/Skeleton3D:DEF-lip1.B.L")
tracks/65/interp = 1
tracks/65/loop_wrap = true
tracks/65/keys = PackedFloat32Array(0, 1, 1.00039, 0.999218, 1.00039, 0.0333333, 1, 1.00611, 0.987915, 1.00611, 0.0666667, 1, 1.01137, 0.977672, 1.01137, 0.1, 1, 1.01598, 0.968805, 1.01598, 0.133333, 1, 1.01976, 0.961628, 1.01976, 0.166667, 1, 1.02251, 0.95645, 1.02251, 0.2, 1, 1.02346, 0.954683, 1.02346, 0.233333, 1, 1.02278, 0.95596, 1.02278, 0.266667, 1, 1.02064, 0.959964, 1.02065, 0.3, 1, 1.01727, 0.966343, 1.01727, 0.333333, 1, 1.01289, 0.974717, 1.01289, 0.366667, 1, 1.00755, 0.985086, 1.00755, 0.4, 1, 1.00181, 0.996419, 1.00181, 0.466667, 1, 0.990043, 1.02024, 0.990043, 0.5, 1, 0.984496, 1.03174, 0.984496, 0.533333, 1, 0.979908, 1.04144, 0.979908, 0.566667, 1, 0.976468, 1.04879, 0.976468, 0.6, 1, 0.974453, 1.05312, 0.974453, 0.633333, 1, 0.974087, 1.05391, 0.974087, 0.666667, 1, 0.975534, 1.05079, 0.975534, 0.7, 1, 0.978989, 1.04339, 0.978989, 0.733333, 1, 0.983491, 1.03388, 0.983491, 0.766667, 1, 0.988744, 1.02293, 0.988744, 0.8, 1, 0.994467, 1.01118, 0.994467, 0.833333, 1, 1.00039, 0.999218, 1.00039, 0.866667, 1, 1.00611, 0.987915, 1.00611, 0.9, 1, 1.01137, 0.977672, 1.01137, 0.933333, 1, 1.01598, 0.968805, 1.01598, 0.966667, 1, 1.01976, 0.961628, 1.01976, 1, 1, 1.02251, 0.95645, 1.02251, 1.03333, 1, 1.02346, 0.954682, 1.02346, 1.06667, 1, 1.02278, 0.95596, 1.02278, 1.1, 1, 1.02064, 0.959964, 1.02065, 1.13333, 1, 1.01727, 0.966343, 1.01727, 1.16667, 1, 1.01289, 0.974717, 1.01289, 1.2, 1, 1.00755, 0.985086, 1.00755, 1.23333, 1, 1.00181, 0.996419, 1.00181, 1.3, 1, 0.990043, 1.02024, 0.990043, 1.33333, 1, 0.984496, 1.03174, 0.984496, 1.36667, 1, 0.979908, 1.04144, 0.979908, 1.4, 1, 0.976467, 1.04879, 0.976468, 1.43333, 1, 0.974453, 1.05312, 0.974453, 1.46667, 1, 0.974087, 1.05391, 0.974087, 1.5, 1, 0.975535, 1.05079, 0.975535, 1.53333, 1, 0.978989, 1.04339, 0.97899, 1.56667, 1, 0.983491, 1.03388, 0.983491, 1.6, 1, 0.988744, 1.02293, 0.988744, 1.63333, 1, 0.994467, 1.01118, 0.994467, 1.66667, 1, 1.00039, 0.999218, 1.00039)
tracks/66/type = "position_3d"
tracks/66/imported = true
tracks/66/enabled = true
tracks/66/path = NodePath("rig/Skeleton3D:DEF-lip2.B.L")
tracks/66/interp = 1
tracks/66/loop_wrap = true
tracks/66/keys = PackedFloat32Array(0, 1, 0.227087, 0.15451, 0.347858, 0.0333333, 1, 0.224088, 0.15659, 0.343113, 0.0666667, 1, 0.221635, 0.158237, 0.338758, 0.1, 1, 0.2198, 0.159383, 0.334943, 0.133333, 1, 0.218662, 0.159936, 0.331818, 0.166667, 1, 0.2183, 0.159784, 0.329538, 0.2, 1, 0.219187, 0.158361, 0.328754, 0.233333, 1, 0.221141, 0.155756, 0.329321, 0.266667, 1, 0.224037, 0.15206, 0.331085, 0.3, 1, 0.227738, 0.14739, 0.333875, 0.333333, 1, 0.23209, 0.141887, 0.337502, 0.366667, 1, 0.237034, 0.135539, 0.341917, 0.4, 1, 0.242212, 0.128755, 0.346678, 0.466667, 1, 0.252654, 0.114471, 0.356506, 0.5, 1, 0.257587, 0.107298, 0.361176, 0.533333, 1, 0.26182, 0.100596, 0.365069, 0.566667, 1, 0.265247, 0.0946325, 0.368003, 0.6, 1, 0.267692, 0.0897072, 0.369727, 0.633333, 1, 0.269021, 0.0860967, 0.370042, 0.666667, 1, 0.269141, 0.0840567, 0.3688, 0.7, 1, 0.267917, 0.0840908, 0.365851, 0.733333, 1, 0.265965, 0.0854869, 0.362031, 0.766667, 1, 0.263449, 0.0880727, 0.357599, 0.8, 1, 0.260521, 0.0916655, 0.352799, 0.833333, 1, 0.257316, 0.0960723, 0.347858, 0.866667, 1, 0.253966, 0.101148, 0.343113, 0.9, 1, 0.250673, 0.106359, 0.338758, 0.933333, 1, 0.247545, 0.111467, 0.334943, 0.966667, 1, 0.244696, 0.116236, 0.331818, 1, 1, 0.242242, 0.120436, 0.329538, 1.03333, 1, 0.240602, 0.123409, 0.328754, 1.06667, 1, 0.239756, 0.125239, 0.329321, 1.1, 1, 0.239618, 0.126136, 0.331085, 1.13333, 1, 0.240084, 0.126334, 0.333875, 1.16667, 1, 0.241038, 0.126089, 0.337502, 1.2, 1, 0.242439, 0.125678, 0.341917, 1.23333, 1, 0.24402, 0.125405, 0.346678, 1.3, 1, 0.247249, 0.125793, 0.356506, 1.33333, 1, 0.248639, 0.126659, 0.361176, 1.36667, 1, 0.249474, 0.128245, 0.36507, 1.4, 1, 0.249666, 0.130353, 0.368004, 1.43333, 1, 0.249076, 0.132933, 0.369727, 1.46667, 1, 0.247606, 0.135915, 0.370042, 1.5, 1, 0.2452, 0.139211, 0.3688, 1.53333, 1, 0.241884, 0.142612, 0.365851, 1.56667, 1, 0.238221, 0.145912, 0.362031, 1.6, 1, 0.234411, 0.149038, 0.3576, 1.63333, 1, 0.230643, 0.151923, 0.352799, 1.66667, 1, 0.227087, 0.15451, 0.347858)
tracks/67/type = "rotation_3d"
tracks/67/imported = true
tracks/67/enabled = true
tracks/67/path = NodePath("rig/Skeleton3D:DEF-lip2.B.L")
tracks/67/interp = 1
tracks/67/loop_wrap = true
tracks/67/keys = PackedFloat32Array(0, 1, -0.34716, -0.191072, -0.257531, 0.881277, 0.0333333, 1, -0.341312, -0.19131, -0.253571, 0.884652, 0.0666667, 1, -0.335948, -0.191262, -0.250615, 0.887553, 0.1, 1, -0.331296, -0.190943, -0.24877, 0.889886, 0.133333, 1, -0.327596, -0.190356, -0.248166, 0.891548, 0.166667, 1, -0.325106, -0.189492, -0.248959, 0.892423, 0.2, 1, -0.324833, -0.188175, -0.252054, 0.891932, 0.233333, 1, -0.32658, -0.186435, -0.257171, 0.890197, 0.266667, 1, -0.330101, -0.184271, -0.264121, 0.887309, 0.3, 1, -0.335106, -0.181688, -0.272672, 0.88337, 0.333333, 1, -0.341266, -0.178703, -0.282557, 0.878501, 0.366667, 1, -0.348405, -0.175245, -0.293734, 0.872711, 0.4, 1, -0.355851, -0.17148, -0.305478, 0.866399, 0.466667, 1, -0.370579, -0.163281, -0.329536, 0.852887, 0.5, 1, -0.377339, -0.159009, -0.341257, 0.846094, 0.533333, 1, -0.38297, -0.154881, -0.351938, 0.839932, 0.566667, 1, -0.387365, -0.151086, -0.361277, 0.834626, 0.6, 1, -0.39029, -0.147812, -0.3689, 0.830505, 0.633333, 1, -0.391554, -0.145238, -0.374472, 0.827866, 0.666667, 1, -0.391002, -0.143529, -0.377691, 0.826962, 0.7, 1, -0.388331, -0.143015, -0.377893, 0.828217, 0.733333, 1, -0.384418, -0.143312, -0.376116, 0.830796, 0.766667, 1, -0.379494, -0.144331, -0.372589, 0.834464, 0.8, 1, -0.373792, -0.145969, -0.367544, 0.838978, 0.833333, 1, -0.367546, -0.148117, -0.361229, 0.844088, 0.9, 1, -0.354821, -0.153438, -0.345993, 0.854897, 0.933333, 1, -0.349041, -0.156156, -0.338205, 0.859885, 0.966667, 1, -0.344022, -0.158755, -0.330768, 0.864314, 1, 1, -0.340057, -0.161128, -0.324027, 0.867989, 1.03333, 1, -0.338162, -0.163011, -0.318931, 0.870263, 1.06667, 1, -0.338186, -0.164451, -0.315435, 0.871256, 1.1, 1, -0.339877, -0.165556, -0.313275, 0.871168, 1.13333, 1, -0.34294, -0.166438, -0.312148, 0.870204, 1.16667, 1, -0.34704, -0.167215, -0.311717, 0.868582, 1.2, 1, -0.351938, -0.168044, -0.311658, 0.86647, 1.26667, 1, -0.36206, -0.170206, -0.311086, 0.862072, 1.3, 1, -0.36679, -0.171628, -0.310191, 0.860111, 1.33333, 1, -0.370986, -0.173314, -0.308672, 0.858518, 1.36667, 1, -0.37405, -0.175332, -0.306111, 0.857695, 1.4, 1, -0.375982, -0.177529, -0.30269, 0.857613, 1.43333, 1, -0.376611, -0.17983, -0.2984, 0.858361, 1.46667, 1, -0.375804, -0.182151, -0.29327, 0.859991, 1.5, 1, -0.373468, -0.184401, -0.287365, 0.862519, 1.53333, 1, -0.369486, -0.186369, -0.280959, 0.865915, 1.56667, 1, -0.364631, -0.188039, -0.274565, 0.869655, 1.63333, 1, -0.353236, -0.190399, -0.262653, 0.877489, 1.66667, 1, -0.34716, -0.191072, -0.257531, 0.881277)
tracks/68/type = "scale_3d"
tracks/68/imported = true
tracks/68/enabled = true
tracks/68/path = NodePath("rig/Skeleton3D:DEF-lip2.B.L")
tracks/68/interp = 1
tracks/68/loop_wrap = true
tracks/68/keys = PackedFloat32Array(0, 1, 1.00004, 0.999917, 1.00004, 0.166667, 1, 0.999506, 1.00099, 0.999506, 0.3, 1, 1.00015, 0.999699, 1.00015, 0.4, 1, 1.00011, 0.999783, 1.00011, 0.433333, 1, 0.999398, 1.00121, 0.999398, 0.466667, 1, 0.998371, 1.00327, 0.998371, 0.5, 1, 0.997101, 1.00582, 0.997101, 0.533333, 1, 0.995785, 1.00849, 0.995784, 0.566667, 1, 0.994703, 1.01068, 0.994703, 0.6, 1, 0.994031, 1.01205, 0.994031, 0.633333, 1, 0.993902, 1.01231, 0.993902, 0.666667, 1, 0.994405, 1.01129, 0.994405, 0.7, 1, 0.995514, 1.00903, 0.995514, 0.766667, 1, 0.99807, 1.00387, 0.99807, 0.8, 1, 0.999194, 1.00161, 0.999194, 0.833333, 1, 1.00004, 0.999917, 1.00004, 1, 1, 0.999507, 1.00099, 0.999507, 1.13333, 1, 1.00015, 0.999699, 1.00015, 1.23333, 1, 1.00011, 0.999783, 1.00011, 1.26667, 1, 0.999398, 1.00121, 0.999398, 1.3, 1, 0.998371, 1.00327, 0.998371, 1.33333, 1, 0.997101, 1.00582, 0.997101, 1.36667, 1, 0.995785, 1.00849, 0.995784, 1.4, 1, 0.994703, 1.01068, 0.994703, 1.43333, 1, 0.994031, 1.01205, 0.994031, 1.46667, 1, 0.993903, 1.01231, 0.993903, 1.5, 1, 0.994404, 1.01129, 0.994405, 1.53333, 1, 0.995514, 1.00903, 0.995514, 1.6, 1, 0.99807, 1.00387, 0.99807, 1.63333, 1, 0.999194, 1.00161, 0.999194, 1.66667, 1, 1.00004, 0.999917, 1.00004)
tracks/69/type = "position_3d"
tracks/69/imported = true
tracks/69/enabled = true
tracks/69/path = NodePath("rig/Skeleton3D:DEF-lip1.B.R")
tracks/69/interp = 1
tracks/69/loop_wrap = true
tracks/69/keys = PackedFloat32Array(0, 1, -0.139413, 0.0776217, 0.418848, 0.0333333, 1, -0.137602, 0.0810726, 0.413135, 0.0666667, 1, -0.13577, 0.0845635, 0.407892, 0.1, 1, -0.133976, 0.0879351, 0.403298, 0.133333, 1, -0.132285, 0.091028, 0.399535, 0.166667, 1, -0.130763, 0.0936831, 0.39679, 0.2, 1, -0.129625, 0.0954223, 0.395846, 0.233333, 1, -0.128879, 0.0963079, 0.396528, 0.266667, 1, -0.128481, 0.0964837, 0.398652, 0.3, 1, -0.128382, 0.0961105, 0.402012, 0.333333, 1, -0.128523, 0.0953667, 0.406379, 0.366667, 1, -0.128886, 0.0944271, 0.411696, 0.4, 1, -0.129339, 0.0935285, 0.417428, 0.466667, 1, -0.130277, 0.0922492, 0.429262, 0.5, 1, -0.130634, 0.0920338, 0.434884, 0.533333, 1, -0.130713, 0.0923261, 0.439573, 0.566667, 1, -0.130469, 0.09303, 0.443105, 0.6, 1, -0.129833, 0.0941449, 0.445181, 0.633333, 1, -0.128756, 0.0956561, 0.445559, 0.666667, 1, -0.127212, 0.0975352, 0.444064, 0.7, 1, -0.125234, 0.0997016, 0.440513, 0.733333, 1, -0.123112, 0.10195, 0.435914, 0.766667, 1, -0.120958, 0.104215, 0.430578, 0.8, 1, -0.118873, 0.106434, 0.424798, 0.833333, 1, -0.116954, 0.10855, 0.418848, 0.866667, 1, -0.115404, 0.110416, 0.413135, 0.9, 1, -0.114196, 0.11202, 0.407892, 0.933333, 1, -0.113363, 0.113295, 0.403298, 0.966667, 1, -0.112943, 0.114157, 0.399535, 1, 1, -0.112975, 0.114509, 0.39679, 1.03333, 1, -0.113715, 0.113921, 0.395846, 1.06667, 1, -0.115048, 0.11246, 0.396528, 1.1, 1, -0.116905, 0.110205, 0.398652, 1.13333, 1, -0.119209, 0.107255, 0.402012, 1.16667, 1, -0.121875, 0.103728, 0.406379, 1.2, 1, -0.124871, 0.0996462, 0.411696, 1.23333, 1, -0.127996, 0.0953019, 0.417428, 1.3, 1, -0.134293, 0.0862569, 0.429262, 1.33333, 1, -0.137282, 0.0817867, 0.434884, 1.36667, 1, -0.139886, 0.0776926, 0.439573, 1.4, 1, -0.142045, 0.0741247, 0.443105, 1.43333, 1, -0.143663, 0.0712673, 0.445181, 1.46667, 1, -0.144666, 0.0692893, 0.445559, 1.5, 1, -0.144999, 0.068344, 0.444064, 1.53333, 1, -0.144575, 0.0687289, 0.440513, 1.56667, 1, -0.143725, 0.0699696, 0.435914, 1.6, 1, -0.142531, 0.0719484, 0.430578, 1.63333, 1, -0.141071, 0.0745421, 0.424798, 1.66667, 1, -0.139413, 0.0776217, 0.418848)
tracks/70/type = "rotation_3d"
tracks/70/imported = true
tracks/70/enabled = true
tracks/70/path = NodePath("rig/Skeleton3D:DEF-lip1.B.R")
tracks/70/interp = 1
tracks/70/loop_wrap = true
tracks/70/keys = PackedFloat32Array(0, 1, -0.471044, 0.189987, 0.46009, 0.728244, 0.0333333, 1, -0.467945, 0.192191, 0.455954, 0.732253, 0.0666667, 1, -0.464796, 0.194672, 0.451358, 0.736439, 0.1, 1, -0.461741, 0.197308, 0.446517, 0.740599, 0.133333, 1, -0.458934, 0.199984, 0.441647, 0.744536, 0.166667, 1, -0.456537, 0.202595, 0.436963, 0.748059, 0.2, 1, -0.455051, 0.204936, 0.432956, 0.750653, 0.233333, 1, -0.454432, 0.207004, 0.429658, 0.752353, 0.266667, 1, -0.454546, 0.208886, 0.426912, 0.753327, 0.3, 1, -0.455234, 0.210674, 0.42454, 0.753754, 0.333333, 1, -0.456313, 0.212467, 0.422344, 0.753832, 0.366667, 1, -0.457607, 0.214415, 0.420065, 0.753769, 0.4, 1, -0.458868, 0.216556, 0.417542, 0.753792, 0.433333, 1, -0.459988, 0.218914, 0.414694, 0.754, 0.466667, 1, -0.460875, 0.221498, 0.411465, 0.754473, 0.5, 1, -0.461448, 0.224303, 0.407819, 0.755274, 0.533333, 1, -0.461491, 0.227324, 0.403657, 0.756578, 0.566667, 1, -0.461084, 0.230395, 0.399207, 0.758256, 0.6, 1, -0.460207, 0.233419, 0.394574, 0.760287, 0.633333, 1, -0.458854, 0.236293, 0.389875, 0.762637, 0.666667, 1, -0.457035, 0.23891, 0.385244, 0.765263, 0.7, 1, -0.454794, 0.241024, 0.381024, 0.768043, 0.733333, 1, -0.452378, 0.242699, 0.377279, 0.770787, 0.766667, 1, -0.44987, 0.243925, 0.37408, 0.773422, 0.8, 1, -0.447351, 0.244702, 0.371488, 0.775883, 0.833333, 1, -0.444899, 0.245037, 0.369555, 0.778107, 0.866667, 1, -0.442692, 0.244816, 0.368573, 0.779899, 0.9, 1, -0.440768, 0.244247, 0.368279, 0.781305, 0.966667, 1, -0.438132, 0.242188, 0.3698, 0.782709, 1, 1, -0.437634, 0.240724, 0.371694, 0.782542, 1.03333, 1, -0.438209, 0.238838, 0.374816, 0.781308, 1.06667, 1, -0.439761, 0.236579, 0.379001, 0.779101, 1.1, 1, -0.442171, 0.233945, 0.384164, 0.775998, 1.13333, 1, -0.445296, 0.23094, 0.390192, 0.772094, 1.16667, 1, -0.448969, 0.227577, 0.396951, 0.767506, 1.2, 1, -0.453096, 0.223793, 0.404442, 0.762265, 1.23333, 1, -0.457354, 0.219737, 0.412276, 0.756685, 1.33333, 1, -0.469683, 0.206503, 0.436387, 0.739136, 1.36667, 1, -0.473086, 0.202087, 0.443888, 0.733698, 1.4, 1, -0.475917, 0.197948, 0.450694, 0.728831, 1.43333, 1, -0.47806, 0.19425, 0.456566, 0.724757, 1.46667, 1, -0.479417, 0.191155, 0.461285, 0.721689, 1.5, 1, -0.479903, 0.188818, 0.464643, 0.719825, 1.53333, 1, -0.479315, 0.18758, 0.46614, 0.719572, 1.56667, 1, -0.478046, 0.187136, 0.466333, 0.720407, 1.6, 1, -0.476183, 0.187434, 0.465318, 0.722217, 1.63333, 1, -0.473818, 0.188409, 0.463198, 0.724876, 1.66667, 1, -0.471044, 0.189987, 0.46009, 0.728245)
tracks/71/type = "scale_3d"
tracks/71/imported = true
tracks/71/enabled = true
tracks/71/path = NodePath("rig/Skeleton3D:DEF-lip1.B.R")
tracks/71/interp = 1
tracks/71/loop_wrap = true
tracks/71/keys = PackedFloat32Array(0, 1, 1.00039, 0.999218, 1.00039, 0.0333333, 1, 1.00611, 0.987915, 1.00611, 0.0666667, 1, 1.01137, 0.977672, 1.01137, 0.1, 1, 1.01598, 0.968806, 1.01598, 0.133333, 1, 1.01976, 0.961629, 1.01976, 0.166667, 1, 1.02251, 0.95645, 1.02251, 0.2, 1, 1.02346, 0.954683, 1.02346, 0.233333, 1, 1.02278, 0.955961, 1.02278, 0.266667, 1, 1.02064, 0.959964, 1.02064, 0.3, 1, 1.01727, 0.966344, 1.01727, 0.333333, 1, 1.01289, 0.974718, 1.01289, 0.366667, 1, 1.00755, 0.985087, 1.00755, 0.4, 1, 1.00181, 0.99642, 1.00181, 0.466667, 1, 0.990043, 1.02024, 0.990043, 0.5, 1, 0.984496, 1.03174, 0.984496, 0.533333, 1, 0.979908, 1.04144, 0.979908, 0.566667, 1, 0.976468, 1.04879, 0.976468, 0.6, 1, 0.974453, 1.05312, 0.974453, 0.633333, 1, 0.974087, 1.05392, 0.974087, 0.666667, 1, 0.975534, 1.05079, 0.975534, 0.7, 1, 0.978989, 1.04339, 0.978989, 0.733333, 1, 0.983491, 1.03388, 0.983491, 0.766667, 1, 0.988744, 1.02294, 0.988744, 0.8, 1, 0.994467, 1.01119, 0.994467, 0.833333, 1, 1.00039, 0.999218, 1.00039, 0.866667, 1, 1.00611, 0.987916, 1.00611, 0.9, 1, 1.01137, 0.977673, 1.01137, 0.933333, 1, 1.01598, 0.968806, 1.01598, 0.966667, 1, 1.01976, 0.961629, 1.01976, 1, 1, 1.02251, 0.95645, 1.02251, 1.03333, 1, 1.02346, 0.954683, 1.02346, 1.06667, 1, 1.02278, 0.955961, 1.02278, 1.1, 1, 1.02064, 0.959965, 1.02064, 1.13333, 1, 1.01727, 0.966344, 1.01727, 1.16667, 1, 1.01289, 0.974718, 1.01289, 1.2, 1, 1.00755, 0.985087, 1.00755, 1.23333, 1, 1.00181, 0.99642, 1.00181, 1.3, 1, 0.990042, 1.02024, 0.990043, 1.33333, 1, 0.984496, 1.03174, 0.984496, 1.36667, 1, 0.979908, 1.04145, 0.979908, 1.4, 1, 0.976467, 1.04879, 0.976467, 1.43333, 1, 0.974453, 1.05312, 0.974453, 1.46667, 1, 0.974087, 1.05391, 0.974087, 1.5, 1, 0.975534, 1.05079, 0.975534, 1.53333, 1, 0.978989, 1.04339, 0.978989, 1.56667, 1, 0.983491, 1.03388, 0.983491, 1.6, 1, 0.988744, 1.02294, 0.988744, 1.63333, 1, 0.994467, 1.01119, 0.994467, 1.66667, 1, 1.00039, 0.999218, 1.00039)
tracks/72/type = "position_3d"
tracks/72/imported = true
tracks/72/enabled = true
tracks/72/path = NodePath("rig/Skeleton3D:DEF-lip2.B.R")
tracks/72/interp = 1
tracks/72/loop_wrap = true
tracks/72/keys = PackedFloat32Array(0, 1, -0.257316, 0.0960716, 0.347858, 0.0333333, 1, -0.253966, 0.101147, 0.343113, 0.0666667, 1, -0.250673, 0.106358, 0.338758, 0.1, 1, -0.247545, 0.111466, 0.334943, 0.133333, 1, -0.244696, 0.116236, 0.331818, 0.166667, 1, -0.242242, 0.120435, 0.329538, 0.2, 1, -0.240602, 0.123409, 0.328754, 0.233333, 1, -0.239756, 0.125238, 0.329321, 0.266667, 1, -0.239618, 0.126135, 0.331085, 0.3, 1, -0.240084, 0.126333, 0.333875, 0.333333, 1, -0.241038, 0.126088, 0.337502, 0.366667, 1, -0.242439, 0.125678, 0.341917, 0.4, 1, -0.24402, 0.125405, 0.346678, 0.466667, 1, -0.247249, 0.125793, 0.356506, 0.5, 1, -0.248639, 0.126659, 0.361176, 0.533333, 1, -0.249474, 0.128245, 0.365069, 0.566667, 1, -0.249666, 0.130353, 0.368003, 0.6, 1, -0.249076, 0.132933, 0.369727, 0.633333, 1, -0.247606, 0.135915, 0.370042, 0.666667, 1, -0.2452, 0.139212, 0.3688, 0.7, 1, -0.241884, 0.142612, 0.365851, 0.733333, 1, -0.238221, 0.145912, 0.362031, 0.766667, 1, -0.234411, 0.149038, 0.357599, 0.8, 1, -0.230643, 0.151924, 0.352799, 0.833333, 1, -0.227087, 0.15451, 0.347857, 0.866667, 1, -0.224088, 0.156591, 0.343112, 0.9, 1, -0.221635, 0.158238, 0.338758, 0.933333, 1, -0.2198, 0.159383, 0.334943, 0.966667, 1, -0.218662, 0.159937, 0.331818, 1, 1, -0.2183, 0.159785, 0.329538, 1.03333, 1, -0.219187, 0.158361, 0.328754, 1.06667, 1, -0.221141, 0.155756, 0.329321, 1.1, 1, -0.224037, 0.152061, 0.331085, 1.13333, 1, -0.227738, 0.14739, 0.333875, 1.16667, 1, -0.23209, 0.141887, 0.337502, 1.2, 1, -0.237034, 0.135539, 0.341917, 1.23333, 1, -0.242212, 0.128755, 0.346678, 1.3, 1, -0.252654, 0.114471, 0.356506, 1.33333, 1, -0.257587, 0.107298, 0.361176, 1.36667, 1, -0.26182, 0.100595, 0.36507, 1.4, 1, -0.265247, 0.0946321, 0.368003, 1.43333, 1, -0.267692, 0.0897067, 0.369727, 1.46667, 1, -0.269021, 0.0860962, 0.370042, 1.5, 1, -0.269141, 0.0840562, 0.3688, 1.53333, 1, -0.267917, 0.0840902, 0.365851, 1.56667, 1, -0.265965, 0.0854862, 0.362031, 1.6, 1, -0.263449, 0.088072, 0.3576, 1.63333, 1, -0.260521, 0.0916649, 0.352799, 1.66667, 1, -0.257316, 0.0960716, 0.347858)
tracks/73/type = "rotation_3d"
tracks/73/imported = true
tracks/73/enabled = true
tracks/73/path = NodePath("rig/Skeleton3D:DEF-lip2.B.R")
tracks/73/interp = 1
tracks/73/loop_wrap = true
tracks/73/keys = PackedFloat32Array(0, 1, -0.367545, 0.148117, 0.361229, 0.844089, 0.0666667, 1, -0.35482, 0.153438, 0.345993, 0.854897, 0.1, 1, -0.34904, 0.156156, 0.338205, 0.859885, 0.133333, 1, -0.344021, 0.158755, 0.330768, 0.864314, 0.166667, 1, -0.340057, 0.161128, 0.324027, 0.867989, 0.2, 1, -0.338161, 0.163011, 0.318931, 0.870263, 0.233333, 1, -0.338185, 0.164451, 0.315435, 0.871256, 0.266667, 1, -0.339876, 0.165556, 0.313275, 0.871168, 0.3, 1, -0.342939, 0.166438, 0.312148, 0.870204, 0.333333, 1, -0.347039, 0.167215, 0.311717, 0.868582, 0.366667, 1, -0.351938, 0.168044, 0.311658, 0.86647, 0.433333, 1, -0.36206, 0.170206, 0.311086, 0.862072, 0.466667, 1, -0.36679, 0.171628, 0.310191, 0.860111, 0.5, 1, -0.370986, 0.173315, 0.308672, 0.858518, 0.533333, 1, -0.374051, 0.175332, 0.306112, 0.857695, 0.566667, 1, -0.375983, 0.177529, 0.30269, 0.857612, 0.6, 1, -0.376611, 0.17983, 0.298401, 0.85836, 0.633333, 1, -0.375805, 0.182152, 0.293271, 0.859991, 0.666667, 1, -0.373469, 0.184401, 0.287365, 0.862519, 0.7, 1, -0.369486, 0.186369, 0.280959, 0.865915, 0.733333, 1, -0.364631, 0.188039, 0.274565, 0.869655, 0.8, 1, -0.353236, 0.1904, 0.262653, 0.877488, 0.833333, 1, -0.34716, 0.191072, 0.257531, 0.881277, 0.866667, 1, -0.341313, 0.19131, 0.253571, 0.884651, 0.9, 1, -0.335949, 0.191262, 0.250615, 0.887553, 0.933333, 1, -0.331296, 0.190943, 0.24877, 0.889886, 0.966667, 1, -0.327597, 0.190357, 0.248166, 0.891548, 1, 1, -0.325106, 0.189492, 0.248959, 0.892423, 1.03333, 1, -0.324833, 0.188175, 0.252054, 0.891932, 1.06667, 1, -0.32658, 0.186435, 0.257171, 0.890197, 1.1, 1, -0.330101, 0.184271, 0.264121, 0.887309, 1.13333, 1, -0.335107, 0.181688, 0.272672, 0.88337, 1.16667, 1, -0.341266, 0.178703, 0.282557, 0.878501, 1.2, 1, -0.348405, 0.175245, 0.293734, 0.872711, 1.23333, 1, -0.355851, 0.171481, 0.305478, 0.866399, 1.3, 1, -0.370579, 0.163281, 0.329536, 0.852887, 1.33333, 1, -0.377339, 0.159009, 0.341257, 0.846094, 1.36667, 1, -0.38297, 0.154881, 0.351938, 0.839932, 1.4, 1, -0.387364, 0.151086, 0.361278, 0.834626, 1.43333, 1, -0.390289, 0.147812, 0.368901, 0.830505, 1.46667, 1, -0.391553, 0.145238, 0.374472, 0.827866, 1.5, 1, -0.391001, 0.143529, 0.377692, 0.826962, 1.53333, 1, -0.38833, 0.143015, 0.377893, 0.828217, 1.56667, 1, -0.384417, 0.143312, 0.376117, 0.830796, 1.6, 1, -0.379494, 0.14433, 0.372589, 0.834464, 1.63333, 1, -0.373791, 0.145968, 0.367545, 0.838978, 1.66667, 1, -0.367545, 0.148117, 0.361229, 0.844089)
tracks/74/type = "scale_3d"
tracks/74/imported = true
tracks/74/enabled = true
tracks/74/path = NodePath("rig/Skeleton3D:DEF-lip2.B.R")
tracks/74/interp = 1
tracks/74/loop_wrap = true
tracks/74/keys = PackedFloat32Array(0, 1, 1.00004, 0.999917, 1.00004, 0.166667, 1, 0.999507, 1.00099, 0.999507, 0.3, 1, 1.00015, 0.999698, 1.00015, 0.4, 1, 1.00011, 0.999783, 1.00011, 0.433333, 1, 0.999399, 1.0012, 0.999399, 0.466667, 1, 0.998372, 1.00327, 0.998372, 0.5, 1, 0.997101, 1.00582, 0.997101, 0.533333, 1, 0.995785, 1.00848, 0.995785, 0.566667, 1, 0.994703, 1.01068, 0.994703, 0.6, 1, 0.994032, 1.01204, 0.994032, 0.633333, 1, 0.993903, 1.01231, 0.993903, 0.666667, 1, 0.994405, 1.01128, 0.994405, 0.7, 1, 0.995514, 1.00903, 0.995514, 0.766667, 1, 0.998071, 1.00387, 0.998071, 0.8, 1, 0.999195, 1.00161, 0.999194, 0.833333, 1, 1.00004, 0.999916, 1.00004, 1, 1, 0.999507, 1.00099, 0.999507, 1.13333, 1, 1.00015, 0.999698, 1.00015, 1.23333, 1, 1.00011, 0.999783, 1.00011, 1.26667, 1, 0.999399, 1.0012, 0.999399, 1.3, 1, 0.998372, 1.00327, 0.998372, 1.33333, 1, 0.997101, 1.00582, 0.997101, 1.36667, 1, 0.995785, 1.00849, 0.995785, 1.4, 1, 0.994703, 1.01068, 0.994703, 1.43333, 1, 0.994032, 1.01204, 0.994032, 1.46667, 1, 0.993903, 1.01231, 0.993903, 1.5, 1, 0.994405, 1.01129, 0.994405, 1.53333, 1, 0.995514, 1.00903, 0.995514, 1.6, 1, 0.998071, 1.00387, 0.998071, 1.63333, 1, 0.999194, 1.00161, 0.999194, 1.66667, 1, 1.00004, 0.999917, 1.00004)
tracks/75/type = "position_3d"
tracks/75/imported = true
tracks/75/enabled = true
tracks/75/path = NodePath("rig/Skeleton3D:DEF-lip1.T.L")
tracks/75/interp = 1
tracks/75/loop_wrap = true
tracks/75/keys = PackedFloat32Array(0, 1, 0.0954259, 0.265269, 0.471374, 0.0333333, 1, 0.0941297, 0.271636, 0.464944, 0.0666667, 1, 0.0934784, 0.277557, 0.459044, 0.1, 1, 0.093483, 0.282771, 0.453874, 0.133333, 1, 0.0941567, 0.286991, 0.449639, 0.166667, 1, 0.0955149, 0.289903, 0.44655, 0.2, 1, 0.097842, 0.29031, 0.445488, 0.233333, 1, 0.100924, 0.288387, 0.446255, 0.266667, 1, 0.104668, 0.284403, 0.448645, 0.3, 1, 0.108977, 0.278676, 0.452426, 0.333333, 1, 0.113744, 0.271575, 0.457342, 0.366667, 1, 0.118926, 0.26329, 0.463325, 0.4, 1, 0.124269, 0.254558, 0.469776, 0.433333, 1, 0.129668, 0.245662, 0.476453, 0.466667, 1, 0.135013, 0.236883, 0.483094, 0.5, 1, 0.140189, 0.228504, 0.489421, 0.533333, 1, 0.144895, 0.221247, 0.494697, 0.566667, 1, 0.149062, 0.215307, 0.498673, 0.6, 1, 0.152572, 0.21102, 0.501009, 0.633333, 1, 0.15533, 0.208683, 0.501435, 0.666667, 1, 0.157259, 0.208554, 0.499752, 0.7, 1, 0.15817, 0.211109, 0.495756, 0.733333, 1, 0.158422, 0.215303, 0.49058, 0.766667, 1, 0.158073, 0.220864, 0.484575, 0.8, 1, 0.157176, 0.227514, 0.47807, 0.833333, 1, 0.155773, 0.234966, 0.471374, 0.866667, 1, 0.153777, 0.242885, 0.464944, 0.9, 1, 0.151448, 0.250655, 0.459043, 0.933333, 1, 0.14887, 0.257924, 0.453873, 0.966667, 1, 0.146128, 0.26433, 0.449639, 1, 1, 0.14331, 0.269498, 0.44655, 1.03333, 1, 0.140592, 0.272185, 0.445487, 1.06667, 1, 0.138086, 0.272562, 0.446255, 1.1, 1, 0.135771, 0.270959, 0.448645, 1.13333, 1, 0.133623, 0.267757, 0.452426, 1.16667, 1, 0.131607, 0.263383, 0.457342, 1.2, 1, 0.129716, 0.258176, 0.463324, 1.23333, 1, 0.127878, 0.25282, 0.469776, 1.26667, 1, 0.12606, 0.247593, 0.476453, 1.3, 1, 0.124223, 0.242754, 0.483094, 1.33333, 1, 0.122326, 0.238544, 0.489421, 1.36667, 1, 0.120249, 0.235585, 0.494697, 1.4, 1, 0.117959, 0.23383, 0.498673, 1.43333, 1, 0.11541, 0.233435, 0.501009, 1.46667, 1, 0.11258, 0.234517, 0.501435, 1.5, 1, 0.109464, 0.237155, 0.499752, 1.53333, 1, 0.106199, 0.241456, 0.495756, 1.56667, 1, 0.103035, 0.246637, 0.49058, 1.6, 1, 0.100104, 0.252478, 0.484575, 1.63333, 1, 0.0975289, 0.258761, 0.47807, 1.66667, 1, 0.0954259, 0.265269, 0.471374)
tracks/76/type = "rotation_3d"
tracks/76/imported = true
tracks/76/enabled = true
tracks/76/path = NodePath("rig/Skeleton3D:DEF-lip1.T.L")
tracks/76/interp = 1
tracks/76/loop_wrap = true
tracks/76/keys = PackedFloat32Array(0, 1, -0.687757, -0.358518, -0.251958, 0.578767, 0.0333333, 1, -0.690571, -0.355396, -0.254688, 0.576142, 0.0666667, 1, -0.693544, -0.351814, -0.25777, 0.573392, 0.133333, 1, -0.699386, -0.343949, -0.264391, 0.568028, 0.166667, 1, -0.701975, -0.339991, -0.267651, 0.565686, 0.2, 1, -0.703939, -0.336387, -0.270565, 0.564011, 0.233333, 1, -0.705296, -0.333183, -0.273117, 0.562987, 0.266667, 1, -0.706161, -0.330264, -0.275417, 0.562501, 0.333333, 1, -0.706936, -0.324767, -0.279729, 0.562601, 0.366667, 1, -0.707132, -0.321844, -0.282049, 0.562876, 0.4, 1, -0.7074, -0.318664, -0.284593, 0.563068, 0.433333, 1, -0.707801, -0.315187, -0.287394, 0.563098, 0.466667, 1, -0.708387, -0.31139, -0.290466, 0.562898, 0.5, 1, -0.709193, -0.307269, -0.293803, 0.562415, 0.533333, 1, -0.710328, -0.302809, -0.297409, 0.561505, 0.566667, 1, -0.711695, -0.298225, -0.301088, 0.560267, 0.633333, 1, -0.715027, -0.289193, -0.308209, 0.556876, 0.666667, 1, -0.716932, -0.28502, -0.311413, 0.554792, 0.7, 1, -0.718894, -0.28148, -0.314052, 0.552569, 0.733333, 1, -0.720798, -0.278531, -0.316187, 0.55036, 0.766667, 1, -0.722603, -0.276211, -0.317804, 0.548228, 0.8, 1, -0.724266, -0.274549, -0.318895, 0.546231, 0.833333, 1, -0.725747, -0.273564, -0.319463, 0.544423, 0.866667, 1, -0.726912, -0.273485, -0.319359, 0.542969, 0.9, 1, -0.727801, -0.274012, -0.31882, 0.541828, 0.933333, 1, -0.728376, -0.275112, -0.31788, 0.541049, 0.966667, 1, -0.728587, -0.276772, -0.316552, 0.540697, 1, 1, -0.72837, -0.278998, -0.314836, 0.540846, 1.03333, 1, -0.72741, -0.282067, -0.312535, 0.541881, 1.06667, 1, -0.725772, -0.285848, -0.309721, 0.543709, 1.1, 1, -0.723508, -0.290302, -0.306404, 0.546239, 1.13333, 1, -0.720684, -0.295375, -0.302602, 0.549363, 1.16667, 1, -0.71738, -0.300994, -0.298349, 0.552952, 1.2, 1, -0.713615, -0.307203, -0.293582, 0.556946, 1.23333, 1, -0.709605, -0.313725, -0.288501, 0.561074, 1.26667, 1, -0.705429, -0.320464, -0.283171, 0.565232, 1.33333, 1, -0.696931, -0.334149, -0.272094, 0.573233, 1.36667, 1, -0.692957, -0.340691, -0.266676, 0.576736, 1.4, 1, -0.689365, -0.346733, -0.261613, 0.579751, 1.43333, 1, -0.686315, -0.352066, -0.257101, 0.582169, 1.46667, 1, -0.683963, -0.356496, -0.25333, 0.583891, 1.5, 1, -0.682455, -0.359837, -0.250484, 0.584833, 1.53333, 1, -0.682096, -0.36164, -0.248978, 0.584783, 1.56667, 1, -0.682525, -0.362337, -0.248441, 0.584079, 1.6, 1, -0.68367, -0.361997, -0.248809, 0.582793, 1.63333, 1, -0.685446, -0.360696, -0.250011, 0.580997, 1.66667, 1, -0.687757, -0.358518, -0.251958, 0.578767)
tracks/77/type = "scale_3d"
tracks/77/imported = true
tracks/77/enabled = true
tracks/77/path = NodePath("rig/Skeleton3D:DEF-lip1.T.L")
tracks/77/interp = 1
tracks/77/loop_wrap = true
tracks/77/keys = PackedFloat32Array(0, 1, 1.00043, 0.999142, 1.00043, 0.0333333, 1, 1.00673, 0.986696, 1.00673, 0.0666667, 1, 1.01258, 0.97534, 1.01258, 0.1, 1, 1.01775, 0.965447, 1.01775, 0.133333, 1, 1.02201, 0.957389, 1.02201, 0.166667, 1, 1.02515, 0.95154, 1.02515, 0.2, 1, 1.02623, 0.949535, 1.02623, 0.233333, 1, 1.02545, 0.950984, 1.02545, 0.266667, 1, 1.02302, 0.955508, 1.02302, 0.3, 1, 1.0192, 0.962688, 1.0192, 0.333333, 1, 1.01427, 0.972065, 1.01427, 0.366667, 1, 1.00833, 0.98357, 1.00833, 0.4, 1, 1.002, 0.996055, 1.002, 0.466667, 1, 0.989169, 1.02205, 0.98917, 0.5, 1, 0.983188, 1.03449, 0.983188, 0.533333, 1, 0.978277, 1.04492, 0.978277, 0.566667, 1, 0.974606, 1.0528, 0.974606, 0.6, 1, 0.972461, 1.05744, 0.972461, 0.633333, 1, 0.972072, 1.05829, 0.972072, 0.666667, 1, 0.973611, 1.05494, 0.973611, 0.7, 1, 0.977294, 1.04702, 0.977294, 0.733333, 1, 0.982115, 1.03679, 0.982115, 0.766667, 1, 0.98777, 1.02496, 0.98777, 0.8, 1, 0.993968, 1.01221, 0.993968, 0.833333, 1, 1.00043, 0.999142, 1.00043, 0.866667, 1, 1.00673, 0.986696, 1.00673, 0.9, 1, 1.01258, 0.97534, 1.01258, 0.933333, 1, 1.01775, 0.965447, 1.01775, 0.966667, 1, 1.02201, 0.957389, 1.02201, 1, 1, 1.02515, 0.951539, 1.02515, 1.03333, 1, 1.02623, 0.949535, 1.02623, 1.06667, 1, 1.02545, 0.950983, 1.02545, 1.1, 1, 1.02302, 0.955508, 1.02302, 1.13333, 1, 1.0192, 0.962688, 1.0192, 1.16667, 1, 1.01427, 0.972065, 1.01427, 1.2, 1, 1.00833, 0.98357, 1.00833, 1.23333, 1, 1.002, 0.996054, 1.002, 1.3, 1, 0.98917, 1.02205, 0.98917, 1.33333, 1, 0.983189, 1.03449, 0.983189, 1.36667, 1, 0.978277, 1.04492, 0.978277, 1.4, 1, 0.974606, 1.0528, 0.974606, 1.43333, 1, 0.972461, 1.05744, 0.972461, 1.46667, 1, 0.972072, 1.05829, 0.972072, 1.5, 1, 0.973611, 1.05494, 0.973611, 1.53333, 1, 0.977294, 1.04702, 0.977294, 1.56667, 1, 0.982115, 1.03679, 0.982115, 1.6, 1, 0.98777, 1.02496, 0.98777, 1.63333, 1, 0.993968, 1.01221, 0.993968, 1.66667, 1, 1.00043, 0.999142, 1.00043)
tracks/78/type = "position_3d"
tracks/78/imported = true
tracks/78/enabled = true
tracks/78/path = NodePath("rig/Skeleton3D:DEF-lip2.T.L")
tracks/78/interp = 1
tracks/78/loop_wrap = true
tracks/78/keys = PackedFloat32Array(0, 1, 0.213741, 0.254266, 0.378592, 0.0333333, 1, 0.210898, 0.259208, 0.373427, 0.0666667, 1, 0.208795, 0.263599, 0.368688, 0.1, 1, 0.20749, 0.267248, 0.364536, 0.133333, 1, 0.207044, 0.269936, 0.361135, 0.166667, 1, 0.207524, 0.271409, 0.358654, 0.2, 1, 0.209421, 0.270615, 0.3578, 0.233333, 1, 0.212488, 0.267714, 0.358417, 0.266667, 1, 0.216587, 0.262914, 0.360337, 0.3, 1, 0.221566, 0.256473, 0.363373, 0.333333, 1, 0.227258, 0.248692, 0.367321, 0.366667, 1, 0.233598, 0.239666, 0.372126, 0.4, 1, 0.240193, 0.230086, 0.377308, 0.466667, 1, 0.253474, 0.210299, 0.388004, 0.5, 1, 0.259803, 0.200634, 0.393086, 0.533333, 1, 0.265377, 0.191913, 0.397324, 0.566667, 1, 0.270084, 0.184434, 0.400517, 0.6, 1, 0.273735, 0.178594, 0.402393, 0.633333, 1, 0.27618, 0.174751, 0.402735, 0.666667, 1, 0.277314, 0.173226, 0.401384, 0.7, 1, 0.276937, 0.174638, 0.398174, 0.733333, 1, 0.275681, 0.177912, 0.394017, 0.766667, 1, 0.273699, 0.182777, 0.389194, 0.8, 1, 0.271123, 0.188951, 0.383969, 0.833333, 1, 0.268076, 0.196141, 0.378591, 0.866667, 1, 0.264603, 0.204062, 0.373427, 0.9, 1, 0.26099, 0.211998, 0.368688, 0.933333, 1, 0.25736, 0.219589, 0.364535, 0.966667, 1, 0.253839, 0.226469, 0.361135, 1, 1, 0.250559, 0.232271, 0.358653, 1.03333, 1, 0.247913, 0.23585, 0.3578, 1.06667, 1, 0.245948, 0.237359, 0.358417, 1.1, 1, 0.244592, 0.237128, 0.360336, 1.13333, 1, 0.243757, 0.23553, 0.363373, 1.16667, 1, 0.243342, 0.232979, 0.367321, 1.2, 1, 0.243314, 0.229857, 0.372126, 1.26667, 1, 0.243627, 0.223913, 0.38267, 1.3, 1, 0.243759, 0.221561, 0.388004, 1.33333, 1, 0.243719, 0.219891, 0.393086, 1.36667, 1, 0.243187, 0.219414, 0.397324, 1.4, 1, 0.242079, 0.219964, 0.400517, 1.43333, 1, 0.240274, 0.221589, 0.402393, 1.46667, 1, 0.237689, 0.224303, 0.402736, 1.5, 1, 0.23428, 0.228086, 0.401384, 1.53333, 1, 0.230143, 0.232846, 0.398174, 1.56667, 1, 0.225812, 0.238014, 0.394017, 1.6, 1, 0.221504, 0.243416, 0.389194, 1.63333, 1, 0.217418, 0.248886, 0.38397, 1.66667, 1, 0.213741, 0.254266, 0.378592)
tracks/79/type = "rotation_3d"
tracks/79/imported = true
tracks/79/enabled = true
tracks/79/path = NodePath("rig/Skeleton3D:DEF-lip2.T.L")
tracks/79/interp = 1
tracks/79/loop_wrap = true
tracks/79/keys = PackedFloat32Array(0, 1, -0.741607, -0.280501, -0.213322, 0.570817, 0.0333333, 1, -0.745211, -0.276595, -0.216785, 0.566709, 0.1, 1, -0.752349, -0.267579, -0.22444, 0.558569, 0.133333, 1, -0.755541, -0.262878, -0.228269, 0.554929, 0.166667, 1, -0.75826, -0.258313, -0.231873, 0.551861, 0.2, 1, -0.76001, -0.25432, -0.23486, 0.550043, 0.233333, 1, -0.760845, -0.250937, -0.237247, 0.549418, 0.266667, 1, -0.760915, -0.24802, -0.239179, 0.549806, 0.3, 1, -0.760384, -0.245413, -0.240813, 0.550998, 0.333333, 1, -0.759429, -0.242938, -0.242317, 0.552749, 0.366667, 1, -0.758213, -0.240362, -0.243919, 0.554836, 0.4, 1, -0.756989, -0.237553, -0.245741, 0.556909, 0.433333, 1, -0.755862, -0.234442, -0.247846, 0.558822, 0.466667, 1, -0.754926, -0.230981, -0.250275, 0.560444, 0.5, 1, -0.754262, -0.227145, -0.253047, 0.561659, 0.533333, 1, -0.754092, -0.222855, -0.256227, 0.562164, 0.566667, 1, -0.754351, -0.218328, -0.259607, 0.562042, 0.6, 1, -0.755071, -0.21367, -0.263085, 0.561248, 0.633333, 1, -0.756268, -0.208997, -0.266553, 0.559758, 0.666667, 1, -0.757947, -0.204442, -0.269892, 0.557565, 0.7, 1, -0.760095, -0.20035, -0.272827, 0.55469, 0.733333, 1, -0.762443, -0.196764, -0.275349, 0.551496, 0.766667, 1, -0.764908, -0.19375, -0.277424, 0.5481, 0.8, 1, -0.767408, -0.191366, -0.279031, 0.544615, 0.833333, 1, -0.769865, -0.18966, -0.280153, 0.541157, 0.866667, 1, -0.772111, -0.188916, -0.280624, 0.537964, 0.9, 1, -0.774087, -0.188857, -0.280647, 0.535126, 0.933333, 1, -0.775709, -0.189476, -0.280229, 0.532771, 0.966667, 1, -0.776882, -0.190788, -0.279361, 0.531047, 1, 1, -0.777498, -0.192825, -0.27801, 0.530118, 1.03333, 1, -0.777099, -0.195986, -0.275875, 0.530659, 1.06667, 1, -0.775765, -0.200109, -0.273032, 0.532539, 1.1, 1, -0.773584, -0.205126, -0.26951, 0.535589, 1.13333, 1, -0.770659, -0.210946, -0.265355, 0.539605, 1.16667, 1, -0.767113, -0.217458, -0.260629, 0.544354, 1.2, 1, -0.763007, -0.224684, -0.255291, 0.549694, 1.23333, 1, -0.758631, -0.232266, -0.2496, 0.555187, 1.3, 1, -0.749518, -0.247971, -0.237541, 0.565957, 1.33333, 1, -0.74503, -0.255805, -0.231396, 0.57092, 1.36667, 1, -0.740972, -0.263214, -0.225515, 0.575172, 1.4, 1, -0.737446, -0.269976, -0.220105, 0.578654, 1.43333, 1, -0.734636, -0.275856, -0.215392, 0.581223, 1.46667, 1, -0.732712, -0.280631, -0.21159, 0.582761, 1.5, 1, -0.731827, -0.284091, -0.208904, 0.583164, 1.56667, 1, -0.733605, -0.286108, -0.207759, 0.580347, 1.6, 1, -0.735684, -0.28529, -0.208745, 0.577758, 1.63333, 1, -0.738396, -0.283384, -0.210638, 0.57454, 1.66667, 1, -0.741607, -0.280501, -0.213322, 0.570817)
tracks/80/type = "scale_3d"
tracks/80/imported = true
tracks/80/enabled = true
tracks/80/path = NodePath("rig/Skeleton3D:DEF-lip2.T.L")
tracks/80/interp = 1
tracks/80/loop_wrap = true
tracks/80/keys = PackedFloat32Array(0, 1, 1.00037, 0.999268, 1.00037, 0.0333333, 1, 1.0057, 0.988711, 1.0057, 0.0666667, 1, 1.01058, 0.979194, 1.01058, 0.1, 1, 1.01483, 0.970995, 1.01483, 0.133333, 1, 1.0183, 0.964391, 1.0183, 0.166667, 1, 1.02081, 0.959648, 1.02081, 0.2, 1, 1.02167, 0.958035, 1.02167, 0.233333, 1, 1.02105, 0.959202, 1.02105, 0.266667, 1, 1.0191, 0.962868, 1.0191, 0.3, 1, 1.01602, 0.968726, 1.01602, 0.333333, 1, 1.01199, 0.976448, 1.01199, 0.366667, 1, 1.00704, 0.986077, 1.00704, 0.4, 1, 1.00169, 0.996658, 1.00169, 0.466667, 1, 0.990616, 1.01906, 0.990616, 0.5, 1, 0.985356, 1.02994, 0.985356, 0.533333, 1, 0.980983, 1.03916, 0.980983, 0.566667, 1, 0.977695, 1.04616, 0.977695, 0.6, 1, 0.975767, 1.05029, 0.975767, 0.633333, 1, 0.975416, 1.05104, 0.975416, 0.666667, 1, 0.976803, 1.04806, 0.976803, 0.7, 1, 0.980106, 1.04102, 0.980106, 0.733333, 1, 0.984396, 1.03198, 0.984396, 0.766667, 1, 0.989384, 1.02161, 0.989384, 0.8, 1, 0.994795, 1.01052, 0.994795, 0.833333, 1, 1.00037, 0.999268, 1.00037, 0.866667, 1, 1.0057, 0.988711, 1.0057, 0.9, 1, 1.01058, 0.979194, 1.01058, 0.933333, 1, 1.01483, 0.970996, 1.01483, 0.966667, 1, 1.0183, 0.964392, 1.0183, 1, 1, 1.02081, 0.959649, 1.02081, 1.03333, 1, 1.02167, 0.958035, 1.02167, 1.06667, 1, 1.02105, 0.959202, 1.02105, 1.1, 1, 1.0191, 0.962868, 1.0191, 1.13333, 1, 1.01602, 0.968726, 1.01602, 1.16667, 1, 1.01199, 0.976448, 1.01199, 1.2, 1, 1.00704, 0.986077, 1.00704, 1.23333, 1, 1.00169, 0.996658, 1.00169, 1.3, 1, 0.990616, 1.01906, 0.990616, 1.33333, 1, 0.985356, 1.02994, 0.985356, 1.36667, 1, 0.980983, 1.03916, 0.980983, 1.4, 1, 0.977695, 1.04616, 0.977695, 1.43333, 1, 0.975767, 1.05029, 0.975767, 1.46667, 1, 0.975416, 1.05104, 0.975416, 1.5, 1, 0.976803, 1.04806, 0.976803, 1.53333, 1, 0.980106, 1.04102, 0.980106, 1.56667, 1, 0.984396, 1.03198, 0.984396, 1.6, 1, 0.989384, 1.02161, 0.989384, 1.63333, 1, 0.994795, 1.01052, 0.994795, 1.66667, 1, 1.00037, 0.999268, 1.00037)
tracks/81/type = "position_3d"
tracks/81/imported = true
tracks/81/enabled = true
tracks/81/path = NodePath("rig/Skeleton3D:DEF-lip1.T.R")
tracks/81/interp = 1
tracks/81/loop_wrap = true
tracks/81/keys = PackedFloat32Array(0, 1, -0.155773, 0.234965, 0.471374, 0.0333333, 1, -0.153776, 0.242884, 0.464944, 0.0666667, 1, -0.151448, 0.250654, 0.459044, 0.1, 1, -0.14887, 0.257923, 0.453874, 0.133333, 1, -0.146128, 0.26433, 0.449639, 0.166667, 1, -0.14331, 0.269498, 0.44655, 0.2, 1, -0.140592, 0.272185, 0.445487, 0.233333, 1, -0.138086, 0.272561, 0.446255, 0.266667, 1, -0.135771, 0.270959, 0.448645, 0.3, 1, -0.133623, 0.267757, 0.452426, 0.333333, 1, -0.131607, 0.263383, 0.457342, 0.366667, 1, -0.129716, 0.258176, 0.463324, 0.4, 1, -0.127878, 0.25282, 0.469776, 0.433333, 1, -0.12606, 0.247593, 0.476453, 0.466667, 1, -0.124223, 0.242754, 0.483094, 0.5, 1, -0.122326, 0.238544, 0.489421, 0.533333, 1, -0.120249, 0.235585, 0.494697, 0.566667, 1, -0.117959, 0.23383, 0.498673, 0.6, 1, -0.11541, 0.233436, 0.501009, 0.633333, 1, -0.11258, 0.234518, 0.501435, 0.666667, 1, -0.109464, 0.237156, 0.499752, 0.7, 1, -0.106199, 0.241456, 0.495756, 0.733333, 1, -0.103035, 0.246638, 0.49058, 0.766667, 1, -0.100104, 0.252479, 0.484575, 0.8, 1, -0.0975288, 0.258762, 0.47807, 0.833333, 1, -0.0954259, 0.26527, 0.471374, 0.866667, 1, -0.0941297, 0.271637, 0.464944, 0.9, 1, -0.0934784, 0.277558, 0.459043, 0.933333, 1, -0.093483, 0.282772, 0.453873, 0.966667, 1, -0.0941567, 0.286992, 0.449639, 1, 1, -0.0955149, 0.289904, 0.44655, 1.03333, 1, -0.097842, 0.29031, 0.445487, 1.06667, 1, -0.100924, 0.288387, 0.446255, 1.1, 1, -0.104668, 0.284403, 0.448645, 1.13333, 1, -0.108977, 0.278676, 0.452426, 1.16667, 1, -0.113744, 0.271575, 0.457341, 1.2, 1, -0.118926, 0.26329, 0.463324, 1.23333, 1, -0.124269, 0.254558, 0.469776, 1.26667, 1, -0.129668, 0.245662, 0.476453, 1.3, 1, -0.135013, 0.236883, 0.483094, 1.33333, 1, -0.140189, 0.228503, 0.489421, 1.36667, 1, -0.144895, 0.221247, 0.494697, 1.4, 1, -0.149062, 0.215306, 0.498673, 1.43333, 1, -0.152572, 0.211019, 0.501009, 1.46667, 1, -0.15533, 0.208683, 0.501435, 1.5, 1, -0.157259, 0.208553, 0.499752, 1.53333, 1, -0.15817, 0.211108, 0.495756, 1.56667, 1, -0.158422, 0.215302, 0.49058, 1.6, 1, -0.158073, 0.220863, 0.484575, 1.63333, 1, -0.157176, 0.227513, 0.47807, 1.66667, 1, -0.155773, 0.234965, 0.471374)
tracks/82/type = "rotation_3d"
tracks/82/imported = true
tracks/82/enabled = true
tracks/82/path = NodePath("rig/Skeleton3D:DEF-lip1.T.R")
tracks/82/interp = 1
tracks/82/loop_wrap = true
tracks/82/keys = PackedFloat32Array(0, 1, -0.725747, 0.273564, 0.319464, 0.544424, 0.0333333, 1, -0.726911, 0.273485, 0.319359, 0.542969, 0.0666667, 1, -0.727801, 0.274011, 0.318821, 0.541828, 0.1, 1, -0.728376, 0.275112, 0.31788, 0.54105, 0.133333, 1, -0.728586, 0.276772, 0.316552, 0.540697, 0.166667, 1, -0.72837, 0.278998, 0.314836, 0.540847, 0.2, 1, -0.727409, 0.282067, 0.312536, 0.541881, 0.233333, 1, -0.725771, 0.285848, 0.309722, 0.543709, 0.266667, 1, -0.723508, 0.290302, 0.306404, 0.54624, 0.3, 1, -0.720684, 0.295375, 0.302602, 0.549364, 0.333333, 1, -0.71738, 0.300994, 0.298349, 0.552952, 0.366667, 1, -0.713615, 0.307203, 0.293582, 0.556946, 0.4, 1, -0.709605, 0.313725, 0.288501, 0.561074, 0.433333, 1, -0.705429, 0.320464, 0.283171, 0.565232, 0.5, 1, -0.696931, 0.334149, 0.272094, 0.573233, 0.533333, 1, -0.692957, 0.340692, 0.266676, 0.576735, 0.566667, 1, -0.689365, 0.346733, 0.261613, 0.579751, 0.6, 1, -0.686315, 0.352067, 0.257101, 0.582168, 0.633333, 1, -0.683963, 0.356497, 0.253329, 0.583891, 0.666667, 1, -0.682455, 0.359837, 0.250484, 0.584833, 0.7, 1, -0.682097, 0.36164, 0.248978, 0.584783, 0.733333, 1, -0.682526, 0.362338, 0.24844, 0.584078, 0.766667, 1, -0.68367, 0.361997, 0.248809, 0.582792, 0.8, 1, -0.685446, 0.360696, 0.25001, 0.580996, 0.833333, 1, -0.687758, 0.358519, 0.251958, 0.578767, 0.866667, 1, -0.690571, 0.355396, 0.254687, 0.576141, 0.9, 1, -0.693544, 0.351814, 0.25777, 0.573391, 0.966667, 1, -0.699386, 0.343949, 0.264391, 0.568028, 1, 1, -0.701975, 0.339991, 0.267651, 0.565685, 1.03333, 1, -0.703939, 0.336388, 0.270565, 0.564011, 1.06667, 1, -0.705296, 0.333184, 0.273117, 0.562986, 1.1, 1, -0.706161, 0.330265, 0.275417, 0.5625, 1.13333, 1, -0.706662, 0.327505, 0.27758, 0.562422, 1.16667, 1, -0.706936, 0.324767, 0.279729, 0.562601, 1.2, 1, -0.707132, 0.321844, 0.282049, 0.562876, 1.23333, 1, -0.7074, 0.318665, 0.284593, 0.563068, 1.26667, 1, -0.707801, 0.315188, 0.287394, 0.563097, 1.3, 1, -0.708386, 0.31139, 0.290466, 0.562898, 1.33333, 1, -0.709192, 0.307269, 0.293804, 0.562415, 1.36667, 1, -0.710328, 0.302809, 0.29741, 0.561505, 1.4, 1, -0.711694, 0.298225, 0.301089, 0.560267, 1.46667, 1, -0.715026, 0.289193, 0.308209, 0.556877, 1.5, 1, -0.716932, 0.28502, 0.311414, 0.554792, 1.53333, 1, -0.718893, 0.28148, 0.314052, 0.552569, 1.56667, 1, -0.720798, 0.278531, 0.316187, 0.55036, 1.6, 1, -0.722602, 0.276211, 0.317804, 0.548228, 1.63333, 1, -0.724265, 0.274549, 0.318896, 0.546231, 1.66667, 1, -0.725747, 0.273564, 0.319464, 0.544424)
tracks/83/type = "scale_3d"
tracks/83/imported = true
tracks/83/enabled = true
tracks/83/path = NodePath("rig/Skeleton3D:DEF-lip1.T.R")
tracks/83/interp = 1
tracks/83/loop_wrap = true
tracks/83/keys = PackedFloat32Array(0, 1, 1.00043, 0.999143, 1.00043, 0.0333333, 1, 1.00673, 0.986696, 1.00673, 0.0666667, 1, 1.01258, 0.975341, 1.01258, 0.1, 1, 1.01775, 0.965448, 1.01775, 0.133333, 1, 1.02201, 0.95739, 1.02201, 0.166667, 1, 1.02515, 0.95154, 1.02515, 0.2, 1, 1.02623, 0.949536, 1.02623, 0.233333, 1, 1.02545, 0.950984, 1.02545, 0.266667, 1, 1.02302, 0.955509, 1.02302, 0.3, 1, 1.0192, 0.962689, 1.0192, 0.333333, 1, 1.01427, 0.972066, 1.01427, 0.366667, 1, 1.00833, 0.983571, 1.00833, 0.4, 1, 1.002, 0.996055, 1.002, 0.466667, 1, 0.989169, 1.02205, 0.989169, 0.5, 1, 0.983188, 1.03449, 0.983188, 0.533333, 1, 0.978277, 1.04492, 0.978277, 0.566667, 1, 0.974606, 1.0528, 0.974606, 0.6, 1, 0.972461, 1.05744, 0.972461, 0.633333, 1, 0.972072, 1.05829, 0.972072, 0.666667, 1, 0.973611, 1.05494, 0.973611, 0.7, 1, 0.977294, 1.04702, 0.977294, 0.733333, 1, 0.982115, 1.03679, 0.982115, 0.766667, 1, 0.98777, 1.02496, 0.98777, 0.8, 1, 0.993968, 1.01221, 0.993968, 0.833333, 1, 1.00043, 0.999143, 1.00043, 0.866667, 1, 1.00673, 0.986696, 1.00673, 0.9, 1, 1.01258, 0.975341, 1.01258, 0.933333, 1, 1.01775, 0.965448, 1.01775, 0.966667, 1, 1.02201, 0.95739, 1.02201, 1, 1, 1.02515, 0.95154, 1.02515, 1.03333, 1, 1.02623, 0.949536, 1.02623, 1.06667, 1, 1.02545, 0.950984, 1.02545, 1.1, 1, 1.02302, 0.955508, 1.02302, 1.13333, 1, 1.0192, 0.962689, 1.0192, 1.16667, 1, 1.01427, 0.972066, 1.01427, 1.2, 1, 1.00833, 0.983571, 1.00833, 1.23333, 1, 1.002, 0.996055, 1.002, 1.3, 1, 0.989169, 1.02205, 0.989169, 1.33333, 1, 0.983188, 1.03449, 0.983189, 1.36667, 1, 0.978277, 1.04492, 0.978277, 1.4, 1, 0.974606, 1.0528, 0.974606, 1.43333, 1, 0.972461, 1.05744, 0.972461, 1.46667, 1, 0.972072, 1.05829, 0.972072, 1.5, 1, 0.973611, 1.05494, 0.973611, 1.53333, 1, 0.977294, 1.04702, 0.977294, 1.56667, 1, 0.982115, 1.03679, 0.982115, 1.6, 1, 0.98777, 1.02496, 0.98777, 1.63333, 1, 0.993968, 1.01221, 0.993968, 1.66667, 1, 1.00043, 0.999143, 1.00043)
tracks/84/type = "position_3d"
tracks/84/imported = true
tracks/84/enabled = true
tracks/84/path = NodePath("rig/Skeleton3D:DEF-lip2.T.R")
tracks/84/interp = 1
tracks/84/loop_wrap = true
tracks/84/keys = PackedFloat32Array(0, 1, -0.268076, 0.19614, 0.378591, 0.0333333, 1, -0.264603, 0.204061, 0.373427, 0.0666667, 1, -0.26099, 0.211997, 0.368688, 0.1, 1, -0.25736, 0.219588, 0.364536, 0.133333, 1, -0.253839, 0.226468, 0.361135, 0.166667, 1, -0.250559, 0.23227, 0.358654, 0.2, 1, -0.247913, 0.23585, 0.3578, 0.233333, 1, -0.245948, 0.237359, 0.358417, 0.266667, 1, -0.244592, 0.237128, 0.360337, 0.3, 1, -0.243757, 0.235529, 0.363373, 0.333333, 1, -0.243342, 0.232978, 0.367321, 0.366667, 1, -0.243314, 0.229857, 0.372126, 0.433333, 1, -0.243628, 0.223914, 0.38267, 0.466667, 1, -0.243759, 0.221561, 0.388004, 0.5, 1, -0.243719, 0.219891, 0.393086, 0.533333, 1, -0.243187, 0.219415, 0.397324, 0.566667, 1, -0.242079, 0.219964, 0.400517, 0.6, 1, -0.240274, 0.221589, 0.402393, 0.633333, 1, -0.237689, 0.224303, 0.402735, 0.666667, 1, -0.23428, 0.228087, 0.401384, 0.7, 1, -0.230143, 0.232847, 0.398174, 0.733333, 1, -0.225812, 0.238015, 0.394017, 0.766667, 1, -0.221504, 0.243417, 0.389194, 0.8, 1, -0.217418, 0.248887, 0.383969, 0.833333, 1, -0.213741, 0.254267, 0.378591, 0.866667, 1, -0.210898, 0.259209, 0.373427, 0.9, 1, -0.208795, 0.263599, 0.368688, 0.933333, 1, -0.20749, 0.267249, 0.364535, 0.966667, 1, -0.207044, 0.269936, 0.361135, 1, 1, -0.207525, 0.271409, 0.358653, 1.03333, 1, -0.209421, 0.270615, 0.3578, 1.06667, 1, -0.212488, 0.267714, 0.358416, 1.1, 1, -0.216587, 0.262914, 0.360336, 1.13333, 1, -0.221566, 0.256473, 0.363373, 1.16667, 1, -0.227259, 0.248692, 0.367321, 1.2, 1, -0.233599, 0.239666, 0.372126, 1.23333, 1, -0.240193, 0.230086, 0.377308, 1.3, 1, -0.253475, 0.210299, 0.388004, 1.33333, 1, -0.259803, 0.200633, 0.393086, 1.36667, 1, -0.265378, 0.191913, 0.397324, 1.4, 1, -0.270084, 0.184434, 0.400517, 1.43333, 1, -0.273735, 0.178593, 0.402393, 1.46667, 1, -0.27618, 0.17475, 0.402735, 1.5, 1, -0.277314, 0.173225, 0.401384, 1.53333, 1, -0.276937, 0.174638, 0.398174, 1.56667, 1, -0.275681, 0.177911, 0.394017, 1.6, 1, -0.273699, 0.182776, 0.389194, 1.63333, 1, -0.271123, 0.18895, 0.38397, 1.66667, 1, -0.268076, 0.19614, 0.378591)
tracks/85/type = "rotation_3d"
tracks/85/imported = true
tracks/85/enabled = true
tracks/85/path = NodePath("rig/Skeleton3D:DEF-lip2.T.R")
tracks/85/interp = 1
tracks/85/loop_wrap = true
tracks/85/keys = PackedFloat32Array(0, 1, -0.769865, 0.189659, 0.280153, 0.541158, 0.0333333, 1, -0.772111, 0.188915, 0.280624, 0.537965, 0.0666667, 1, -0.774087, 0.188857, 0.280647, 0.535127, 0.1, 1, -0.775709, 0.189476, 0.28023, 0.532772, 0.133333, 1, -0.776882, 0.190788, 0.279361, 0.531048, 0.166667, 1, -0.777498, 0.192825, 0.278011, 0.530118, 0.2, 1, -0.777099, 0.195985, 0.275875, 0.53066, 0.233333, 1, -0.775765, 0.200109, 0.273032, 0.53254, 0.266667, 1, -0.773584, 0.205126, 0.26951, 0.535589, 0.3, 1, -0.770659, 0.210946, 0.265355, 0.539605, 0.333333, 1, -0.767113, 0.217458, 0.260629, 0.544354, 0.366667, 1, -0.763007, 0.224684, 0.255291, 0.549694, 0.4, 1, -0.758631, 0.232266, 0.2496, 0.555187, 0.466667, 1, -0.749518, 0.247971, 0.237541, 0.565957, 0.5, 1, -0.74503, 0.255806, 0.231396, 0.57092, 0.533333, 1, -0.740972, 0.263213, 0.225514, 0.575172, 0.566667, 1, -0.737446, 0.269976, 0.220105, 0.578653, 0.6, 1, -0.734636, 0.275856, 0.215391, 0.581223, 0.633333, 1, -0.732712, 0.280631, 0.21159, 0.58276, 0.666667, 1, -0.731828, 0.284092, 0.208904, 0.583163, 0.733333, 1, -0.733605, 0.286108, 0.207759, 0.580346, 0.766667, 1, -0.735685, 0.28529, 0.208745, 0.577757, 0.8, 1, -0.738396, 0.283385, 0.210638, 0.574539, 0.833333, 1, -0.741608, 0.280501, 0.213321, 0.570816, 0.866667, 1, -0.745212, 0.276595, 0.216785, 0.566709, 0.933333, 1, -0.752349, 0.267579, 0.224439, 0.558569, 0.966667, 1, -0.755541, 0.262878, 0.228269, 0.554929, 1, 1, -0.758261, 0.258313, 0.231872, 0.551861, 1.03333, 1, -0.76001, 0.25432, 0.23486, 0.550042, 1.06667, 1, -0.760845, 0.250937, 0.237247, 0.549417, 1.1, 1, -0.760916, 0.24802, 0.239179, 0.549806, 1.13333, 1, -0.760384, 0.245412, 0.240813, 0.550997, 1.16667, 1, -0.759429, 0.242938, 0.242317, 0.552749, 1.2, 1, -0.758213, 0.240362, 0.243919, 0.554836, 1.23333, 1, -0.756989, 0.237553, 0.245741, 0.556909, 1.26667, 1, -0.755862, 0.234441, 0.247846, 0.558822, 1.3, 1, -0.754926, 0.230981, 0.250275, 0.560444, 1.33333, 1, -0.754262, 0.227145, 0.253047, 0.56166, 1.36667, 1, -0.754092, 0.222855, 0.256227, 0.562165, 1.4, 1, -0.754351, 0.218328, 0.259607, 0.562043, 1.43333, 1, -0.75507, 0.213669, 0.263086, 0.561249, 1.46667, 1, -0.756267, 0.208997, 0.266553, 0.559758, 1.5, 1, -0.757946, 0.204442, 0.269893, 0.557565, 1.53333, 1, -0.760095, 0.20035, 0.272827, 0.55469, 1.56667, 1, -0.762443, 0.196763, 0.275349, 0.551497, 1.6, 1, -0.764907, 0.19375, 0.277424, 0.5481, 1.63333, 1, -0.767407, 0.191366, 0.279031, 0.544616, 1.66667, 1, -0.769865, 0.189659, 0.280153, 0.541158)
tracks/86/type = "scale_3d"
tracks/86/imported = true
tracks/86/enabled = true
tracks/86/path = NodePath("rig/Skeleton3D:DEF-lip2.T.R")
tracks/86/interp = 1
tracks/86/loop_wrap = true
tracks/86/keys = PackedFloat32Array(0, 1, 1.00037, 0.999266, 1.00037, 0.0333333, 1, 1.0057, 0.988711, 1.0057, 0.0666667, 1, 1.01058, 0.979194, 1.01058, 0.1, 1, 1.01483, 0.970995, 1.01483, 0.133333, 1, 1.0183, 0.964391, 1.0183, 0.166667, 1, 1.02081, 0.959648, 1.02081, 0.2, 1, 1.02167, 0.958034, 1.02167, 0.233333, 1, 1.02105, 0.959202, 1.02105, 0.266667, 1, 1.0191, 0.962867, 1.0191, 0.3, 1, 1.01602, 0.968725, 1.01602, 0.333333, 1, 1.01199, 0.976448, 1.01199, 0.366667, 1, 1.00704, 0.986076, 1.00704, 0.4, 1, 1.00169, 0.996657, 1.00169, 0.466667, 1, 0.990617, 1.01906, 0.990617, 0.5, 1, 0.985357, 1.02994, 0.985357, 0.533333, 1, 0.980983, 1.03916, 0.980983, 0.566667, 1, 0.977696, 1.04616, 0.977696, 0.6, 1, 0.975768, 1.05029, 0.975768, 0.633333, 1, 0.975416, 1.05104, 0.975416, 0.666667, 1, 0.976804, 1.04806, 0.976804, 0.7, 1, 0.980107, 1.04102, 0.980107, 0.733333, 1, 0.984397, 1.03198, 0.984397, 0.766667, 1, 0.989385, 1.02161, 0.989385, 0.8, 1, 0.994795, 1.01051, 0.994795, 0.833333, 1, 1.00037, 0.999267, 1.00037, 0.866667, 1, 1.0057, 0.988711, 1.0057, 0.9, 1, 1.01058, 0.979193, 1.01058, 0.933333, 1, 1.01483, 0.970995, 1.01483, 0.966667, 1, 1.0183, 0.964391, 1.0183, 1, 1, 1.02081, 0.959648, 1.02081, 1.03333, 1, 1.02167, 0.958034, 1.02167, 1.06667, 1, 1.02105, 0.959201, 1.02105, 1.1, 1, 1.0191, 0.962867, 1.0191, 1.13333, 1, 1.01602, 0.968726, 1.01602, 1.16667, 1, 1.01199, 0.976448, 1.01199, 1.2, 1, 1.00704, 0.986077, 1.00704, 1.23333, 1, 1.00169, 0.996657, 1.00169, 1.3, 1, 0.990616, 1.01906, 0.990617, 1.33333, 1, 0.985357, 1.02994, 0.985357, 1.36667, 1, 0.980983, 1.03916, 0.980983, 1.4, 1, 0.977696, 1.04616, 0.977696, 1.43333, 1, 0.975768, 1.05029, 0.975768, 1.46667, 1, 0.975417, 1.05104, 0.975417, 1.5, 1, 0.976804, 1.04806, 0.976804, 1.53333, 1, 0.980107, 1.04102, 0.980107, 1.56667, 1, 0.984397, 1.03198, 0.984397, 1.6, 1, 0.989385, 1.02161, 0.989385, 1.63333, 1, 0.994795, 1.01051, 0.994795, 1.66667, 1, 1.00037, 0.999266, 1.00037)
tracks/87/type = "position_3d"
tracks/87/imported = true
tracks/87/enabled = true
tracks/87/path = NodePath("rig/Skeleton3D:DEF-bone.02")
tracks/87/interp = 1
tracks/87/loop_wrap = true
tracks/87/keys = PackedFloat32Array(0, 1, -0.0434858, 0.360467, 3.22568e-07, 0.0333333, 1, -0.0429814, 0.370761, 3.18884e-07, 0.0666667, 1, -0.0417726, 0.380627, 3.10055e-07, 0.1, 1, -0.0399115, 0.389622, 2.96463e-07, 0.133333, 1, -0.0374502, 0.397279, 2.78487e-07, 0.166667, 1, -0.034441, 0.403102, 2.56509e-07, 0.2, 1, -0.0308056, 0.405331, 2.29957e-07, 0.233333, 1, -0.0267787, 0.404216, 2.00547e-07, 0.266667, 1, -0.0224127, 0.400191, 1.6866e-07, 0.3, 1, -0.0177597, 0.393757, 1.34676e-07, 0.333333, 1, -0.0128719, 0.385488, 9.89777e-08, 0.366667, 1, -0.00777532, 0.375765, 6.17546e-08, 0.433333, 1, 0.00260047, 0.355437, -1.70058e-08, 0.466667, 1, 0.00777532, 0.345624, -5.18202e-08, 0.5, 1, 0.0128719, 0.336552, -8.90433e-08, 0.533333, 1, 0.0177597, 0.329191, -1.24742e-07, 0.566667, 1, 0.0224127, 0.323646, -1.58725e-07, 0.6, 1, 0.0267787, 0.320272, -1.90613e-07, 0.633333, 1, 0.0308055, 0.319368, -2.20023e-07, 0.666667, 1, 0.0344408, 0.321176, -2.46573e-07, 0.7, 1, 0.0374501, 0.326116, -2.68551e-07, 0.733333, 1, 0.0399114, 0.332871, -2.86528e-07, 0.766667, 1, 0.0417725, 0.341088, -3.00121e-07, 0.8, 1, 0.0429814, 0.350407, -3.0895e-07, 0.833333, 1, 0.0434858, 0.360467, -3.12634e-07, 0.866667, 1, 0.0429814, 0.370761, -3.0895e-07, 0.9, 1, 0.0417726, 0.380627, -3.00121e-07, 0.933333, 1, 0.0399115, 0.389622, -2.86528e-07, 0.966667, 1, 0.0374502, 0.397279, -2.68553e-07, 1, 1, 0.034441, 0.403102, -2.46574e-07, 1.03333, 1, 0.0308056, 0.405331, -2.20023e-07, 1.06667, 1, 0.0267787, 0.404216, -1.90613e-07, 1.1, 1, 0.0224127, 0.400191, -1.58725e-07, 1.13333, 1, 0.0177597, 0.393757, -1.24742e-07, 1.16667, 1, 0.0128719, 0.385488, -8.90433e-08, 1.2, 1, 0.00777531, 0.375765, -5.18202e-08, 1.26667, 1, -0.00260047, 0.355437, 2.09795e-08, 1.3, 1, -0.00777531, 0.345624, 6.17546e-08, 1.33333, 1, -0.0128719, 0.336552, 9.89777e-08, 1.36667, 1, -0.0177597, 0.329191, 1.34676e-07, 1.4, 1, -0.0224128, 0.323646, 1.6866e-07, 1.43333, 1, -0.0267788, 0.320272, 2.00547e-07, 1.46667, 1, -0.0308055, 0.319368, 2.29957e-07, 1.5, 1, -0.0344408, 0.321176, 2.56507e-07, 1.53333, 1, -0.0374501, 0.326116, 2.78486e-07, 1.56667, 1, -0.0399114, 0.332871, 2.96462e-07, 1.6, 1, -0.0417725, 0.341088, 3.10055e-07, 1.63333, 1, -0.0429814, 0.350407, 3.18884e-07, 1.66667, 1, -0.0434858, 0.360467, 3.22568e-07)
tracks/88/type = "rotation_3d"
tracks/88/imported = true
tracks/88/enabled = true
tracks/88/path = NodePath("rig/Skeleton3D:DEF-bone.02")
tracks/88/interp = 1
tracks/88/loop_wrap = true
tracks/88/keys = PackedFloat32Array(0, 1, 0.0599935, -0.998199, -2.10734e-07, 3.64834e-06, 0.0333333, 1, 0.0576906, -0.998335, -1.43299e-07, 3.64623e-06, 0.0666667, 1, 0.0546585, -0.998505, -1.77017e-07, 3.64571e-06, 0.1, 1, 0.0510484, -0.998696, -2.10735e-07, 3.64571e-06, 0.133333, 1, 0.0469924, -0.998895, -2.02305e-07, 3.6465e-06, 0.166667, 1, 0.0426036, -0.999092, -1.68587e-07, 3.64965e-06, 0.2, 1, 0.0379241, -0.999281, -1.3487e-07, 3.65177e-06, 0.233333, 1, 0.0330585, -0.999453, -1.26441e-07, 3.65151e-06, 0.266667, 1, 0.0279396, -0.99961, -1.09582e-07, 3.65046e-06, 0.3, 1, 0.0225032, -0.999747, -7.58646e-08, 3.64993e-06, 0.333333, 1, 0.0166886, -0.999861, -4.21469e-08, 3.65097e-06, 0.366667, 1, 0.0103025, -0.999947, -4.21471e-08, 3.65178e-06, 0.4, 1, 0.0034824, -0.999994, -1.68589e-08, 3.65188e-06, 0.433333, 1, -0.00373702, -0.999993, 1.6859e-08, 3.65149e-06, 0.466667, 1, -0.0112966, -0.999936, 4.21472e-08, 3.65087e-06, 0.533333, 1, -0.0269879, -0.999636, 1.09583e-07, 3.64981e-06, 0.566667, 1, -0.0346036, -0.999401, 1.5173e-07, 3.64653e-06, 0.6, 1, -0.0417085, -0.99913, 1.5173e-07, 3.646e-06, 0.633333, 1, -0.0480528, -0.998845, 1.433e-07, 3.64888e-06, 0.666667, 1, -0.053388, -0.998574, 2.10734e-07, 3.65097e-06, 0.7, 1, -0.0571225, -0.998367, 2.10735e-07, 3.64887e-06, 0.733333, 1, -0.0596129, -0.998222, 2.61311e-07, 3.64992e-06, 0.766667, 1, -0.0608891, -0.998145, 2.95028e-07, 3.64781e-06, 0.8, 1, -0.0609959, -0.998138, 2.78169e-07, 3.64254e-06, 0.833333, 1, -0.0599935, -0.998199, 2.10734e-07, 3.64043e-06, 0.866667, 1, -0.0576906, -0.998335, 2.78169e-07, 3.64465e-06, 0.9, 1, -0.0546585, -0.998505, 2.44452e-07, 3.64729e-06, 0.933333, 1, -0.0510484, -0.998696, 1.77017e-07, 3.64835e-06, 0.966667, 1, -0.0469924, -0.998895, 1.3487e-07, 3.64808e-06, 1, 1, -0.0426036, -0.999092, 1.68587e-07, 3.64702e-06, 1.03333, 1, -0.0379241, -0.999281, 1.3487e-07, 3.64808e-06, 1.06667, 1, -0.0330585, -0.999453, 1.26441e-07, 3.64914e-06, 1.1, 1, -0.0279396, -0.99961, 1.09582e-07, 3.64967e-06, 1.13333, 1, -0.0225032, -0.999747, 7.58646e-08, 3.6498e-06, 1.16667, 1, -0.0166886, -0.999861, 4.21468e-08, 3.65031e-06, 1.2, 1, -0.0103025, -0.999947, 4.21471e-08, 3.65112e-06, 1.23333, 1, -0.00348239, -0.999994, 1.68589e-08, 3.65162e-06, 1.26667, 1, 0.00373702, -0.999993, -1.68589e-08, 3.65189e-06, 1.3, 1, 0.0112966, -0.999936, -4.21472e-08, 3.65179e-06, 1.36667, 1, 0.0269879, -0.999636, -1.433e-07, 3.651e-06, 1.4, 1, 0.0346036, -0.999401, -1.68589e-07, 3.6518e-06, 1.43333, 1, 0.0417085, -0.99913, -1.85448e-07, 3.65074e-06, 1.46667, 1, 0.0480528, -0.998845, -2.10735e-07, 3.6473e-06, 1.5, 1, 0.053388, -0.998574, -2.10734e-07, 3.64307e-06, 1.53333, 1, 0.0571225, -0.998367, -2.10735e-07, 3.64308e-06, 1.56667, 1, 0.0596129, -0.998222, -2.10734e-07, 3.64307e-06, 1.6, 1, 0.0608891, -0.998145, -2.10734e-07, 3.64518e-06, 1.63333, 1, 0.0609959, -0.998138, -2.10734e-07, 3.64834e-06, 1.66667, 1, 0.0599935, -0.998199, -2.10734e-07, 3.64834e-06)
tracks/89/type = "scale_3d"
tracks/89/imported = true
tracks/89/enabled = true
tracks/89/path = NodePath("rig/Skeleton3D:DEF-bone.02")
tracks/89/interp = 1
tracks/89/loop_wrap = true
tracks/89/keys = PackedFloat32Array(0, 1, 0.999065, 1.00187, 0.999065, 0.0333333, 1, 0.985437, 1.02992, 0.985437, 0.0666667, 1, 0.972931, 1.0566, 0.972931, 0.1, 1, 0.961973, 1.08075, 0.961973, 0.133333, 1, 0.952999, 1.10111, 0.952999, 0.166667, 1, 0.946452, 1.11636, 0.946451, 0.2, 1, 0.944199, 1.12169, 0.944199, 0.233333, 1, 0.945826, 1.11785, 0.945826, 0.266667, 1, 0.950893, 1.10603, 0.950893, 0.3, 1, 0.958906, 1.08764, 0.958906, 0.333333, 1, 0.969324, 1.06429, 0.969324, 0.366667, 1, 0.982005, 1.03712, 0.982005, 0.433333, 1, 1.00983, 0.980847, 1.00983, 0.466667, 1, 1.02391, 0.953972, 1.02391, 0.5, 1, 1.03732, 0.929348, 1.03732, 0.533333, 1, 1.0485, 0.909707, 1.0485, 0.566667, 1, 1.05693, 0.895233, 1.05693, 0.6, 1, 1.06188, 0.88686, 1.06188, 0.633333, 1, 1.06278, 0.885356, 1.06278, 0.666667, 1, 1.05921, 0.891321, 1.05921, 0.7, 1, 1.05074, 0.905792, 1.05074, 0.733333, 1, 1.03977, 0.925095, 1.03977, 0.766667, 1, 1.02704, 0.948218, 1.02704, 0.8, 1, 1.01326, 0.974148, 1.01326, 0.866667, 1, 0.985437, 1.02992, 0.985437, 0.9, 1, 0.972931, 1.0566, 0.972931, 0.933333, 1, 0.961973, 1.08075, 0.961973, 0.966667, 1, 0.952999, 1.10111, 0.952999, 1, 1, 0.946452, 1.11636, 0.946451, 1.03333, 1, 0.944199, 1.12169, 0.944199, 1.06667, 1, 0.945826, 1.11785, 0.945826, 1.1, 1, 0.950893, 1.10603, 0.950893, 1.13333, 1, 0.958906, 1.08764, 0.958906, 1.16667, 1, 0.969324, 1.06429, 0.969324, 1.2, 1, 0.982005, 1.03712, 0.982005, 1.26667, 1, 1.00983, 0.980847, 1.00983, 1.3, 1, 1.02391, 0.953972, 1.02391, 1.33333, 1, 1.03732, 0.929348, 1.03732, 1.36667, 1, 1.0485, 0.909707, 1.0485, 1.4, 1, 1.05693, 0.895233, 1.05693, 1.43333, 1, 1.06188, 0.88686, 1.06188, 1.46667, 1, 1.06278, 0.885356, 1.06278, 1.5, 1, 1.05921, 0.891321, 1.05921, 1.53333, 1, 1.05074, 0.905792, 1.05074, 1.56667, 1, 1.03977, 0.925095, 1.03977, 1.6, 1, 1.02704, 0.948218, 1.02704, 1.63333, 1, 1.01326, 0.974148, 1.01326, 1.66667, 1, 0.999065, 1.00187, 0.999065)

[sub_resource type="Animation" id="Animation_c5shk"]
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("rig/Skeleton3D/Mesh:material_override:shader_parameter/emission_strength")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.4]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("rig/Skeleton3D/Mesh:material_override:shader_parameter/emission_color")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 0.780392, 0.509804, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_tipbt"]
_data = {
"Explode": SubResource("Animation_f7bho"),
"Fall": SubResource("Animation_k6dxj"),
"Idle": SubResource("Animation_v3btl"),
"RESET": SubResource("Animation_c5shk")
}

[sub_resource type="AnimationNodeAdd2" id="AnimationNodeAdd2_r5hwi"]

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_3l5v7"]
animation = &"Explode"

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_g5gyk"]
animation = &"custom/Explode_addition"

[sub_resource type="AnimationNodeOneShot" id="AnimationNodeOneShot_2utig"]

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_blp63"]
animation = &"Fall"

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_xmgdv"]
animation = &"Idle"

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_n7xqm"]
xfade_time = 0.15
switch_mode = 2
advance_mode = 2

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_p8cpc"]
advance_mode = 2

[sub_resource type="AnimationNodeStateMachineTransition" id="AnimationNodeStateMachineTransition_vb0qy"]

[sub_resource type="AnimationNodeStateMachine" id="AnimationNodeStateMachine_nkngq"]
states/Fall/node = SubResource("AnimationNodeAnimation_blp63")
states/Fall/position = Vector2(456, 100)
states/Idle/node = SubResource("AnimationNodeAnimation_xmgdv")
states/Idle/position = Vector2(597, 100)
states/Start/position = Vector2(255, 100)
transitions = ["Fall", "Idle", SubResource("AnimationNodeStateMachineTransition_n7xqm"), "Start", "Fall", SubResource("AnimationNodeStateMachineTransition_p8cpc"), "Idle", "End", SubResource("AnimationNodeStateMachineTransition_vb0qy")]

[sub_resource type="AnimationNodeBlendTree" id="AnimationNodeBlendTree_imla3"]
graph_offset = Vector2(-568, -58)
nodes/Add2/node = SubResource("AnimationNodeAdd2_r5hwi")
nodes/Add2/position = Vector2(-140, 100)
nodes/Animation/node = SubResource("AnimationNodeAnimation_g5gyk")
nodes/Animation/position = Vector2(-420, 260)
"nodes/Animation 2/node" = SubResource("AnimationNodeAnimation_3l5v7")
"nodes/Animation 2/position" = Vector2(-400, 100)
nodes/OneShot/node = SubResource("AnimationNodeOneShot_2utig")
nodes/OneShot/position = Vector2(40, 0)
nodes/StateMachine/node = SubResource("AnimationNodeStateMachine_nkngq")
nodes/StateMachine/position = Vector2(-160, -40)
nodes/output/position = Vector2(200, 0)
node_connections = [&"Add2", 0, &"Animation 2", &"Add2", 1, &"Animation", &"OneShot", 0, &"StateMachine", &"OneShot", 1, &"Add2", &"output", 0, &"OneShot"]

[sub_resource type="Shader" id="Shader_afw7j"]
code = "shader_type spatial;
render_mode cull_disabled, particle_trails;

uniform vec3 albedo_color : source_color;

void fragment() {
	ALBEDO = albedo_color;
	EMISSION = albedo_color * 2.5 * COLOR.a;
	ALPHA = step(0.2, sin(UV.x * PI) * sin(UV.y * PI)) * COLOR.a;
}
"

[sub_resource type="ShaderMaterial" id="ShaderMaterial_66q75"]
render_priority = 0
shader = SubResource("Shader_afw7j")
shader_parameter/albedo_color = Color(1, 0.717647, 0.266667, 1)

[sub_resource type="Gradient" id="Gradient_r3ipb"]
colors = PackedColorArray(1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_6jkop"]
gradient = SubResource("Gradient_r3ipb")

[sub_resource type="Curve" id="Curve_w7jca"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.5, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_uosbs"]
curve = SubResource("Curve_w7jca")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_j0qeb"]
lifetime_randomness = 0.1
direction = Vector3(0, 1, 0)
spread = 75.0
initial_velocity_min = 4.0
initial_velocity_max = 8.0
scale_min = 0.8
scale_max = 1.2
scale_curve = SubResource("CurveTexture_uosbs")
color_ramp = SubResource("GradientTexture1D_6jkop")

[sub_resource type="Curve" id="Curve_raggy"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="RibbonTrailMesh" id="RibbonTrailMesh_h0gks"]
shape = 0
size = 0.04
curve = SubResource("Curve_raggy")

[node name="JackOLantern" type="Node3D"]
script = ExtResource("1_acisk")

[node name="jack_o_lantern" parent="." instance=ExtResource("1_lr0q6")]
unique_name_in_owner = true

[node name="Skeleton3D" parent="jack_o_lantern/rig" index="0"]
bones/0/position = Vector3(0.299328, 0.422126, 0.370021)
bones/0/rotation = Quaternion(-0.684369, 0.243439, 0.684144, -0.0657489)
bones/0/scale = Vector3(1.0203, 0.960665, 1.0203)
bones/1/position = Vector3(-0.325275, 0.402335, 0.370021)
bones/1/rotation = Quaternion(0.676293, 0.264904, 0.685939, 0.04406)
bones/1/scale = Vector3(1.0203, 0.960666, 1.0203)
bones/2/position = Vector3(0.299328, 0.422126, 0.370021)
bones/2/rotation = Quaternion(-0.312976, 0.806877, -0.0816198, -0.494302)
bones/2/scale = Vector3(1.04766, 0.911164, 1.04766)
bones/3/position = Vector3(-0.325275, 0.402335, 0.370021)
bones/3/rotation = Quaternion(0.264911, 0.824403, -0.0902714, 0.491968)
bones/3/scale = Vector3(0.957522, 1.09118, 0.957522)
bones/4/position = Vector3(-0.00269438, 0.085615, 0.438917)
bones/4/rotation = Quaternion(-0.411633, -0.403736, -0.553362, 0.60112)
bones/4/scale = Vector3(0.984586, 1.0316, 0.984586)
bones/5/position = Vector3(-0.00269438, 0.085615, 0.438917)
bones/5/rotation = Quaternion(-0.424182, 0.390529, 0.572078, 0.583338)
bones/5/scale = Vector3(0.984586, 1.0316, 0.984586)
bones/6/position = Vector3(-0.00762568, 0.242309, 0.494834)
bones/6/rotation = Quaternion(-0.519111, -0.480607, -0.487981, 0.511287)
bones/6/scale = Vector3(0.98468, 1.03141, 0.98468)
bones/7/position = Vector3(-0.00762568, 0.242309, 0.494834)
bones/7/rotation = Quaternion(-0.534039, 0.463965, 0.503894, 0.495611)
bones/7/scale = Vector3(0.98468, 1.03141, 0.98468)
bones/8/rotation = Quaternion(1.01833e-07, 1.19104e-07, 0.0158179, 0.999875)
bones/8/scale = Vector3(1.03164, 0.939781, 1.03164)
bones/9/position = Vector3(0.277391, 0.34342, 0.408165)
bones/9/rotation = Quaternion(0.65683, -0.590548, -0.437752, 0.16793)
bones/9/scale = Vector3(0.987115, 1.02631, 0.987115)
bones/10/position = Vector3(0.214591, 0.318896, 0.481844)
bones/10/rotation = Quaternion(-0.646405, 0.713119, 0.260029, 0.0775095)
bones/10/scale = Vector3(0.984748, 1.03126, 0.984748)
bones/11/position = Vector3(0.121462, 0.321708, 0.508045)
bones/11/rotation = Quaternion(-0.6028, 0.753959, 0.0975963, 0.242186)
bones/11/scale = Vector3(0.988604, 1.02321, 0.988604)
bones/12/position = Vector3(-0.298432, 0.325175, 0.408165)
bones/12/rotation = Quaternion(0.637699, 0.611201, 0.442741, 0.154119)
bones/12/scale = Vector3(0.987115, 1.02631, 0.987115)
bones/13/position = Vector3(-0.234215, 0.304675, 0.481844)
bones/13/rotation = Quaternion(0.623534, 0.733211, 0.257436, -0.0856393)
bones/13/scale = Vector3(0.984749, 1.03126, 0.984749)
bones/14/position = Vector3(-0.141448, 0.313378, 0.508045)
bones/14/rotation = Quaternion(0.578672, 0.772633, 0.0899086, -0.245138)
bones/14/scale = Vector3(0.988604, 1.02322, 0.988604)
bones/15/position = Vector3(0.2448, 0.495695, 0.386567)
bones/15/rotation = Quaternion(0.74574, -0.558625, -0.138081, 0.335773)
bones/15/scale = Vector3(0.989956, 1.02046, 0.989956)
bones/16/position = Vector3(0.169201, 0.480352, 0.453419)
bones/16/rotation = Quaternion(0.86705, -0.465263, 0.0286178, 0.175884)
bones/16/scale = Vector3(0.999641, 1.00073, 0.999641)
bones/17/position = Vector3(0.0802555, 0.425814, 0.483815)
bones/17/rotation = Quaternion(0.947608, -0.280613, 0.122492, 0.0910537)
bones/17/scale = Vector3(1.04181, 0.921374, 1.04181)
bones/18/position = Vector3(-0.266697, 0.496337, 0.382464)
bones/18/rotation = Quaternion(0.665357, 0.65634, 0.089279, 0.344308)
bones/18/scale = Vector3(0.965152, 1.07351, 0.965152)
bones/19/position = Vector3(-0.179458, 0.506918, 0.444261)
bones/19/rotation = Quaternion(0.880678, 0.420091, -0.0725289, 0.206566)
bones/19/scale = Vector3(0.989684, 1.02096, 0.989684)
bones/20/position = Vector3(-0.0941602, 0.444668, 0.477879)
bones/20/rotation = Quaternion(0.962872, 0.194619, -0.154556, 0.10542)
bones/20/scale = Vector3(0.966774, 1.07053, 0.966774)
bones/21/position = Vector3(0.130483, 0.0921248, 0.432502)
bones/21/rotation = Quaternion(-0.461206, -0.223115, -0.409365, 0.754937)
bones/21/scale = Vector3(0.986846, 1.02687, 0.986846)
bones/22/position = Vector3(0.24805, 0.126292, 0.359197)
bones/22/rotation = Quaternion(-0.369209, -0.1726, -0.309317, 0.859196)
bones/22/scale = Vector3(0.997639, 1.00474, 0.997639)
bones/23/position = Vector3(-0.136016, 0.0836807, 0.432502)
bones/23/rotation = Quaternion(-0.467976, 0.208387, 0.433013, 0.741669)
bones/23/scale = Vector3(0.986846, 1.02687, 0.986846)
bones/24/position = Vector3(-0.255497, 0.110337, 0.359197)
bones/24/rotation = Quaternion(-0.374486, 0.160824, 0.336301, 0.848998)
bones/24/scale = Vector3(0.99764, 1.00474, 0.99764)
bones/25/position = Vector3(0.12313, 0.240328, 0.48674)
bones/25/rotation = Quaternion(-0.698757, -0.331262, -0.274451, 0.571559)
bones/25/scale = Vector3(0.985723, 1.02922, 0.985723)
bones/26/position = Vector3(0.243736, 0.220599, 0.390933)
bones/26/rotation = Quaternion(-0.746945, -0.25249, -0.234004, 0.568827)
bones/26/scale = Vector3(0.987585, 1.02533, 0.987585)
bones/27/position = Vector3(-0.137996, 0.232054, 0.48674)
bones/27/rotation = Quaternion(-0.708853, 0.309016, 0.292391, 0.562622)
bones/27/scale = Vector3(0.985722, 1.02922, 0.985723)
bones/28/position = Vector3(-0.257122, 0.204728, 0.390933)
bones/28/rotation = Quaternion(-0.754545, 0.228771, 0.251873, 0.561146)
bones/28/scale = Vector3(0.987585, 1.02533, 0.987586)
bones/29/position = Vector3(-0.0107125, 0.340396, 8.32067e-08)
bones/29/rotation = Quaternion(-0.0158179, 0.999875, 7.12928e-08, -3.65166e-06)
bones/29/scale = Vector3(1.03164, 0.939781, 1.03164)

[node name="Mesh" parent="jack_o_lantern/rig/Skeleton3D" index="0"]
material_override = ExtResource("3_3nbxj")

[node name="AnimationPlayer" parent="jack_o_lantern" index="1"]
libraries = {
"": SubResource("AnimationLibrary_tipbt"),
"custom": ExtResource("4_1mtwm")
}

[node name="AnimationTree" type="AnimationTree" parent="."]
unique_name_in_owner = true
tree_root = SubResource("AnimationNodeBlendTree_imla3")
anim_player = NodePath("../jack_o_lantern/AnimationPlayer")
active = true
parameters/Add2/add_amount = 1.0
parameters/OneShot/active = false
parameters/OneShot/internal_active = false
parameters/OneShot/request = 0

[node name="BoneAttachment3D" type="BoneAttachment3D" parent="."]
transform = Transform3D(-1.03112, -0.029727, -7.53576e-06, -0.0326327, 0.939311, 2.79002e-08, 7.53111e-06, 2.42549e-07, -1.03164, -0.0107125, 0.340396, 8.32067e-08)
bone_name = "DEF-bone.02"
bone_idx = 29
use_external_skeleton = true
external_skeleton = NodePath("../jack_o_lantern/rig/Skeleton3D")

[node name="Sparkes" type="GPUParticles3D" parent="BoneAttachment3D"]
material_override = SubResource("ShaderMaterial_66q75")
emitting = false
amount = 16
lifetime = 0.4
transform_align = 3
trail_enabled = true
trail_lifetime = 0.1
process_material = SubResource("ParticleProcessMaterial_j0qeb")
draw_pass_1 = SubResource("RibbonTrailMesh_h0gks")

[editable path="jack_o_lantern"]
