[gd_scene load_steps=14 format=3 uid="uid://c3cpjdmtyyu4m"]

[ext_resource type="Script" path="res://effects/fireball/fireball_spawn/fireball_spawn.gd" id="1_ecndc"]
[ext_resource type="Shader" path="res://effects/fireball/materials/fireball_particles.gdshader" id="1_rce85"]

[sub_resource type="Gradient" id="Gradient_s1un6"]
offsets = PackedFloat32Array(0.2, 0.730077)
colors = PackedColorArray(3.23439e-05, 0.283027, 0.806296, 1, 0.27451, 0.615686, 1, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_yyo8l"]
gradient = SubResource("Gradient_s1un6")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_jipsp"]
noise_type = 2
fractal_octaves = 2

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_v41l4"]
seamless = true
noise = SubResource("FastNoiseLite_jipsp")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_tg5hu"]
render_priority = 0
shader = ExtResource("1_rce85")
shader_parameter/uv_scale = 0.5
shader_parameter/voronoi_sampler = SubResource("NoiseTexture2D_v41l4")
shader_parameter/gradient_sampler = SubResource("GradientTexture1D_yyo8l")

[sub_resource type="Gradient" id="Gradient_6rwyn"]
offsets = PackedFloat32Array(0, 0.2, 1)
colors = PackedColorArray(1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_1k4ld"]
gradient = SubResource("Gradient_6rwyn")

[sub_resource type="Curve" id="Curve_nkheg"]
_data = [Vector2(0, 0.5), 0.0, 0.0, 0, 0, Vector2(0.4, 1), 0.0, 0.0, 0, 0, Vector2(1, 0.2), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_f848e"]
curve = SubResource("Curve_nkheg")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_4siie"]
lifetime_randomness = 0.4
emission_shape = 1
emission_sphere_radius = 0.15
direction = Vector3(0, 1, 0)
initial_velocity_min = 0.4
initial_velocity_max = 2.0
gravity = Vector3(0, 1, 0)
damping_min = 0.5
damping_max = 1.0
scale_min = 0.8
scale_max = 1.2
scale_curve = SubResource("CurveTexture_f848e")
color_ramp = SubResource("GradientTexture1D_1k4ld")
anim_speed_min = 0.1
anim_speed_max = 0.1
anim_offset_max = 1.0

[sub_resource type="QuadMesh" id="QuadMesh_cbqbq"]
size = Vector2(0.5, 0.5)

[node name="fireball_spawn" type="Node3D"]
script = ExtResource("1_ecndc")

[node name="Anchor" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0, 0)

[node name="FireAmber" type="GPUParticles3D" parent="Anchor"]
unique_name_in_owner = true
material_override = SubResource("ShaderMaterial_tg5hu")
emitting = false
amount = 4
lifetime = 0.6
one_shot = true
explosiveness = 0.8
process_material = SubResource("ParticleProcessMaterial_4siie")
draw_pass_1 = SubResource("QuadMesh_cbqbq")
