; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="VFX notebook"
run/main_scene="res://viewer/viewer.tscn"
config/features=PackedStringArray("4.2", "Forward Plus")
run/max_fps=60
config/icon="res://sketchbook_icon.png"

[display]

window/size/viewport_width=1920
window/size/viewport_height=1080
window/size/window_width_override=1280
window/size/window_height_override=720
window/stretch/mode="canvas_items"
window/stretch/aspect="expand"

[importer_defaults]

texture={
"compress/channel_pack": 0,
"compress/hdr_compression": 1,
"compress/high_quality": false,
"compress/lossy_quality": 0.7,
"compress/mode": 0,
"compress/normal_map": 0,
"detect_3d/compress_to": 0,
"mipmaps/generate": false,
"mipmaps/limit": -1,
"process/fix_alpha_border": true,
"process/hdr_as_srgb": false,
"process/hdr_clamp_exposure": false,
"process/normal_map_invert_y": false,
"process/premult_alpha": false,
"process/size_limit": 0,
"roughness/mode": 0,
"roughness/src_normal": ""
}

[rendering]

anti_aliasing/quality/msaa_3d=2
