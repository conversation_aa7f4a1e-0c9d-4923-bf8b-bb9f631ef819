class_name Card
extends Object

var name: String
var ownedPlayer: Player
var color: int
var rarity: int
var cost: int
var baseCost: int
var power: int
var basePower: int
var castCondition: int

var abilities: Array[Ability]
var givingAbilities: Array[Ability]
var enableAbilities: bool

var enterArenaOnTurn: int
var drawnOnTurn: int
var dieOnTurn: int

var effectPowerModifier: int
var permanentPowerModifier: int
var effectCostModifier: int
var permanentCostModifier: int
var is_ignited = false
var is_silenced = false

var card3dui: Card_3DUI

func _init(p_owner: Player, p_color: int = CardColor.NONE, p_name: String = "", p_rarity: int = Rarity.COMMON, p_cost: int = 0, p_power: int = 0, p_castCondition: int = CastCondition.NORMAL, p_abilities: Array[Ability] = [], p_givingAbilities: Array[Ability] = []):
	if p_name == "":
		self.name = str(randi() % 999)
	else:
		self.name = p_name
	self.ownedPlayer = p_owner
	self.color = p_color
	self.rarity = p_rarity
	self.cost = p_cost
	self.baseCost = p_cost
	self.power = p_power
	self.basePower = p_power
	self.castCondition = p_castCondition
	self.abilities = p_abilities
	self.givingAbilities = p_givingAbilities
	self.enableAbilities = true

#UI
func getCard3DUI() -> Card_3DUI:
	return self.card3dui

#UI
func setCard3DUI(_card3dui: Card_3DUI):
	self.card3dui = _card3dui
	self.card3dui.playerParticle.visible = getOwner().isOnMainSide()
	self.card3dui.enemyParticle.visible = !getOwner().isOnMainSide()

#UI
func setOwner3DUI(_owner: Player) -> void:
	setOwner(_owner)
	self.card3dui.playerParticle.visible = _owner.isOnMainSide()
	self.card3dui.enemyParticle.visible = !_owner.isOnMainSide()

func setOwner(_owner: Player) -> void:
	self.ownedPlayer = _owner

#UI
func setCost3DUI(p_cost: int) -> void:
	setCost(p_cost)
	self.card3dui.displayCard(self)

func setCost(p_cost: int) -> void:
	self.cost = p_cost

#UI
func setPower3DUI(p_power: int) -> void:
	self.card3dui.displayCard(self)
	setPower(p_power)


func setPower(p_power: int) -> void:
	self.power = p_power

#UI
func addPermanentPowerModifier3DUI(modifier: int) -> void:
	addPermanentPowerModifier(modifier)
	self.card3dui.animatePowerChange(modifier)
	
func addPermanentPowerModifier(modifier: int) -> void:
	print("addPermanentPowerModifier")
	self.permanentPowerModifier += modifier
	setPower(self.basePower + getEffectPowerModifier() + getPermanentPowerModifier())
	print("add success")

#UI
func updateEffectCostModifier3DUI(modifier: int) -> void:
	updateEffectCostModifier(modifier)
	setCost3DUI(self.baseCost + getEffectCostModifier() + getPermanentCostModifier())

func updateEffectCostModifier(modifier: int) -> void:
	self.effectCostModifier = modifier
	setCost(self.baseCost + getEffectCostModifier() + getPermanentCostModifier())

#UI
func updateEffectPowerModifier3DUI(modifier: int) -> void:
	self.effectPowerModifier = modifier
	setPower3DUI(self.basePower + getEffectPowerModifier() + getPermanentPowerModifier())

func updateEffectPowerModifier(modifier: int) -> void:
	self.effectPowerModifier = modifier
	setPower(self.basePower + getEffectPowerModifier() + getPermanentPowerModifier())

#UI
func setSilent3DUI(p_bool: bool) -> void:
	setSilent(p_bool)
	card3dui.siliencedVfx.visible = p_bool
	# TODO do silent animation

func setSilent(p_bool: bool) -> void:
	self.is_silenced = p_bool

func getName() -> String:
	return self.name

func getOwner() -> Player:
	return self.ownedPlayer

func getColor() -> int:
	return self.color

func getRarity() -> int:
	return self.rarity

func getCost() -> int:
	return self.cost

func getBaseCost() -> int:
	return self.baseCost

func getDiffCost() -> int:
	return self.cost - self.baseCost

func getCastCondition() -> int:
	return self.castCondition

func getPower() -> int:
	return self.power

func getBasePower() -> int:
	return self.basePower

func getDiffPower() -> int:
	return self.power - self.basePower

func getEffectPowerModifier() -> int:
	return self.effectPowerModifier

func getPermanentPowerModifier() -> int:
	return self.permanentPowerModifier

func getEffectCostModifier() -> int:
	return self.effectCostModifier

func getPermanentCostModifier() -> int:
	return self.permanentCostModifier

func getAbilities() -> Array[Ability]:
	return self.abilities

func getAbility(abilityIndex: int = 0) -> Ability:
	if self.abilities.size() > 0:
		return self.abilities[abilityIndex]
	else:
		return null

func getGivingAbilities() -> Array[Ability]:
	return self.givingAbilities

func getEnterArenaOnTurn() -> int:
	return self.enterArenaOnTurn

func getDrawnOnTurn() -> int:
	return self.drawnOnTurn

func getDieOnTurn() -> int:
	return self.dieOnTurn

func isAbilitiesEnabled() -> bool:
	return self.enableAbilities

func setEnterArenaTurn() -> void:
	self.enterArenaOnTurn = TurnService.TurnCount

func setDrawnTurn() -> void:
	self.drawnOnTurn = TurnService.TurnCount

func setDieTurn() -> void:
	self.dieOnTurn = TurnService.TurnCount

func setEnableAbilities(_enable: bool) -> void:
	self.enableAbilities = _enable

func hasCastCondition(_castCondition: int) -> bool:
	return self.castCondition == _castCondition

func hasAbilityKeyword(keyword: int) -> bool:
	for ability in getAbilities():
		if ability.getKeyword().getName() == keyword:
			return true
	return false

func cloneAbilitiesFrom(_card: Card) -> void:
	var clonedAbilities: Array[Ability] = []
	for ability in _card.getAbilities():
		clonedAbilities.append(Ability.new(self, ability.getText(), ability.getKeyword(), ability.getTrigger(), ability.getTarget(), ability.getTargetSlot()))
	addAbilities(clonedAbilities)

func cloneGivingAbilitiesFrom(_card: Card) -> void:
	var clonedAbilities: Array[Ability] = []
	for ability in _card.getGivingAbilities():
		clonedAbilities.append(Ability.new(self, ability.getText(), ability.getKeyword(), ability.getTrigger(), ability.getTarget(), ability.getTargetSlot()))
	addGivingAbilities(clonedAbilities)

func addAbilities(_abilities: Array[Ability]) -> void:
	var addingAbilities: Array[Ability] = []
	for ability in _abilities:
		addingAbilities.append(Ability.new(self, ability.getText(), ability.getKeyword(), ability.getTrigger(), ability.getTarget(), ability.getTargetSlot()))
	self.abilities.append_array(addingAbilities)

func addGivingAbilities(p_abilities: Array[Ability]) -> void:
	self.givingAbilities.append_array(p_abilities)

func removeAbilitiesByTrigger(trigger: int) -> void:
	for ability in self.abilities:
		if ability.getTrigger().getName() == trigger:
			self.abilities.pop_at(self.abilities.find(ability))

func removeAbilityByKeyword(keywordName: int) -> Ability:
	for ability in self.abilities:
		if ability.getKeyword().getName() == keywordName:
			return self.abilities.pop_at(self.abilities.find(ability))
	return null

func syncPower() -> void:
	if self.power < 0:
		self.power = 0

func syncCost() -> void:
	if self.cost < 0:
		self.cost = 0
