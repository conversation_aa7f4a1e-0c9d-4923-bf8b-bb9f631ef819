extends Control
@onready var option_screen: Control = $Control/OptionScreen
@onready var confirmation_screen: Control = $ConfirmationScreen

var save_profiles: Array = [player_profile]
# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	if not FileAccess.file_exists("user://savegame.save"):
		print("No save file found, creating new save.")
		initSave()
	else:
		print("Save file found, loading save.")
		load_profiles()
		# delete_save()
	#if Const.CURRENT_SCREEN == "title_screen":
		#option_screen.find_child("ReturnButton").visible = false
	Const.CURRENT_SCREEN = "title_screen"
	pass

# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass

#logic functions

func initSave() -> void:
	var save_file = FileAccess.open("user://savegame.save", FileAccess.WRITE)
	# var data = ["0", "0", "0"] # example data
	var data = {"name": "player1", "decks": [ {"name": "all", "cards": {CardData.Card.Fairy: 4, CardData.Card.Sunshroom: 4, CardData.Card.Lightshroom: 4, CardData.Card.Elderbloom: 2}}, {"name": "deck1", "cards": {CardData.Card.Elderbloom: 2, "active": 1}}], "resources": {"green_ink": 100, "red_ink": 50}, "time": 1753981200}
	# for d in data:
	# 	print("data: ", d)
	# 	var json_string = JSON.stringify(d)
	# 	save_file.store_line(json_string)
	var json_string = JSON.stringify(data)
	print("json_string: ", json_string)
	save_file.store_line(json_string)
	save_file.close()

func load_profiles() -> void:
	var profile_management = profile_management.new()
	var save_file = FileAccess.open("user://savegame.save", FileAccess.READ)
	print(" save_file.get_length: ", OS.get_data_dir())
	while save_file.get_position() < save_file.get_length():
		var json_string = save_file.get_line()
		var json = JSON.new()
		var result = json.parse(json_string)
		if result != OK:
			print("Error parsing JSON: ", json.get_error_message())
			pass
		var profile_data = json.get_data()
		print("Parsed JSON: ", profile_data)
		if str(profile_data) != "0": # data available
			print("Valid profile data found.")
			profile_management.load(profile_data)
		else: # 0 = no save data
			print("No valid profile data found.")

	save_file.close()

#delete save file
func delete_save() -> void:
	if FileAccess.file_exists("user://savegame.save"):
		DirAccess.remove_absolute("user://savegame.save")
		print("Save file deleted.")

#ui functions
func _on_start_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/title/starter_deck_selection_screen.tscn")

func _on_quit_button_pressed() -> void:
	confirmation_screen.visible = true
	confirmation_screen.popup_label.text = "Return to Desktop?"

func _on_test_mode_button_pressed() -> void:
	get_tree().change_scene_to_file("res://main_test.tscn")

func _on_continue_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/title/continue_screen.tscn")

func _on_settings_button_pressed() -> void:
	option_screen.visible = true
	pass # Replace with function body.
