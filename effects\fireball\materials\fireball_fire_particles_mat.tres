[gd_resource type="ShaderMaterial" load_steps=5 format=3 uid="uid://osm40gk0a5v0"]

[ext_resource type="Shader" path="res://effects/fireball/materials/fireball_particles.gdshader" id="1_xvey3"]
[ext_resource type="Texture2D" uid="uid://dblv3xi87exyb" path="res://effects/fireball/materials/fire_particles_gradient.tres" id="2_wntwn"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_dvc8t"]
noise_type = 2
fractal_octaves = 2

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_dsewx"]
seamless = true
noise = SubResource("FastNoiseLite_dvc8t")

[resource]
render_priority = 0
shader = ExtResource("1_xvey3")
shader_parameter/uv_scale = 0.3
shader_parameter/voronoi_sampler = SubResource("NoiseTexture2D_dsewx")
shader_parameter/gradient_sampler = ExtResource("2_wntwn")
