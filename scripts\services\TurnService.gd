class_name TurnService

static var ActivePlayer:Player
static var InactivePlayer:Player
static var TurnCount:int

# Run once when the battle starts
static func initBattleEvent(firstPlayer:Player, secondPlayer:Player) -> void:
	TurnCount = 0
	ActivePlayer = firstPlayer
	InactivePlayer = secondPlayer


# When new turn starts
static func initTurnEvent() -> void:
	TurnCount += 1


# When a turn ends (player click end turn)
static func endTurnEvent() -> void:
	setActivePlayer(InactivePlayer)


# set active and inactive player objects
static func setActivePlayer(player:Player) -> void:
	InactivePlayer = ActivePlayer
	ActivePlayer = player


# return an opposing player
static func getOpponent(player:Player) -> Player:
	print("getOpponent")
	if player == ActivePlayer:
		print(InactivePlayer.getName())
		return InactivePlayer
	else:
		print(ActivePlayer.getName())
		return ActivePlayer


# return number of turns passed until now
static func turnCountfromPresent(turn:int) -> int:
	return TurnCount - turn
