extends VBoxContainer
@onready var siliencedVfx: TextureRect = $AbilityTextOnCard/SiliencedVFX


# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	#siliencedVfx.position.y = 0
	#siliencedVfx.size.x = get_child(1).size.x/10
	#siliencedVfx.size.y = get_child(1).size.y
	pass


# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass
