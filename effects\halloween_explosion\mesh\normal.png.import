[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://b74vpcud1k23i"
path="res://.godot/imported/normal.png-6206f39f9c5cf7be1052a067055391dd.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://effects/halloween_explosion/mesh/normal.png"
dest_files=["res://.godot/imported/normal.png-6206f39f9c5cf7be1052a067055391dd.ctex"]

[params]

compress/mode=0
compress/high_quality=true
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
