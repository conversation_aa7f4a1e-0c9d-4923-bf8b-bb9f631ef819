[gd_scene load_steps=27 format=3 uid="uid://bli2bf40bd7lh"]

[ext_resource type="Script" path="res://scripts/ui/Location3DUI.gd" id="1_6twg1"]
[ext_resource type="FontFile" uid="uid://ciearu3o71xp8" path="res://Assets/Fonts/FAV/BarberChop.otf" id="2_77ff6"]
[ext_resource type="Texture2D" uid="uid://b0q8cgxmkycxm" path="res://Location Pics/Location1.png" id="2_dsesp"]
[ext_resource type="Texture2D" uid="uid://ckf3u21l6ojqr" path="res://Assets/EventPics2/Unrevealed.png" id="3_dhn74"]
[ext_resource type="Texture2D" uid="uid://6xflbkhvbqi" path="res://Assets/Particle/InlaneVfx3.png" id="3_qu0xa"]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_50d83"]
expanded_output_ports = [0]
input_name = "color"

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_tdwro"]
expanded_output_ports = [0]
texture = ExtResource("3_qu0xa")

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_xtqgs"]
operator = 2

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_t3ebn"]
operator = 2

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_wky8u"]
expanded_output_ports = [0]
texture = ExtResource("3_qu0xa")

[sub_resource type="VisualShader" id="VisualShader_hmd63"]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_back, diffuse_lambert, specular_schlick_ggx, unshaded;

uniform sampler2D tex_frg_6;
uniform sampler2D tex_frg_3;



void fragment() {
// Input:2
	vec4 n_out2p0 = COLOR;
	float n_out2p4 = n_out2p0.a;


// Texture2D:6
	vec4 n_out6p0 = texture(tex_frg_6, UV);


// Texture2D:3
	vec4 n_out3p0 = texture(tex_frg_3, UV);


// FloatOp:4
	float n_out4p0 = n_out3p0.x * n_out2p4;


// FloatOp:5
	float n_out5p0 = n_out6p0.x * n_out4p0;


// Output:0
	ALBEDO = vec3(n_out2p0.xyz);
	ALPHA = n_out5p0;


}
"
flags/unshaded = true
nodes/fragment/0/position = Vector2(780, 160)
nodes/fragment/2/node = SubResource("VisualShaderNodeInput_50d83")
nodes/fragment/2/position = Vector2(-100, 100)
nodes/fragment/3/node = SubResource("VisualShaderNodeTexture_tdwro")
nodes/fragment/3/position = Vector2(-120, 340)
nodes/fragment/4/node = SubResource("VisualShaderNodeFloatOp_xtqgs")
nodes/fragment/4/position = Vector2(200, 380)
nodes/fragment/5/node = SubResource("VisualShaderNodeFloatOp_t3ebn")
nodes/fragment/5/position = Vector2(540, 500)
nodes/fragment/6/node = SubResource("VisualShaderNodeTexture_wky8u")
nodes/fragment/6/position = Vector2(240, 560)
nodes/fragment/connections = PackedInt32Array(2, 0, 0, 0, 2, 4, 4, 1, 3, 0, 4, 0, 6, 0, 5, 0, 4, 0, 5, 1, 5, 0, 0, 1)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_6oie6"]
render_priority = 1
shader = SubResource("VisualShader_hmd63")

[sub_resource type="QuadMesh" id="QuadMesh_n61r8"]
size = Vector2(3.12, 3.145)

[sub_resource type="Gradient" id="Gradient_xhn1w"]
offsets = PackedFloat32Array(0, 0.504854, 1)
colors = PackedColorArray(0.694118, 0.694118, 0.694118, 0.898039, 1, 1, 1, 0.32549, 0.694118, 0.694118, 0.694118, 0.901961)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_0t3ib"]
resource_local_to_scene = true
transparency = 1
shading_mode = 0
albedo_texture = ExtResource("3_dhn74")
uv1_scale = Vector3(0.99, 0.99, 0.99)
uv1_offset = Vector3(0.5, 0, -0.495)
uv1_triplanar = true

[sub_resource type="BoxMesh" id="BoxMesh_knnp4"]

[sub_resource type="Animation" id="Animation_fp6po"]
resource_name = "Location Reveal"
tracks/0/type = "bezier"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("UnrevealTexture:material_override:albedo_color:r")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"handle_modes": PackedInt32Array(0, 0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0, 1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0, 1)
}
tracks/1/type = "bezier"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("UnrevealTexture:material_override:albedo_color:g")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"handle_modes": PackedInt32Array(0, 0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0, 1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0, 1)
}
tracks/2/type = "bezier"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("UnrevealTexture:material_override:albedo_color:b")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"handle_modes": PackedInt32Array(0, 0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0, 1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0, 1)
}
tracks/3/type = "bezier"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("UnrevealTexture:material_override:albedo_color:a")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"handle_modes": PackedInt32Array(0, 0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0, 0, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0, 1)
}

[sub_resource type="Animation" id="Animation_ptdyo"]
length = 0.001
tracks/0/type = "bezier"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("UnrevealTexture:material_override:albedo_color:r")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/1/type = "bezier"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("UnrevealTexture:material_override:albedo_color:g")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/2/type = "bezier"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("UnrevealTexture:material_override:albedo_color:b")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}
tracks/3/type = "bezier"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("UnrevealTexture:material_override:albedo_color:a")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"handle_modes": PackedInt32Array(0),
"points": PackedFloat32Array(1, -0.25, 0, 0.25, 0),
"times": PackedFloat32Array(0)
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_8qmya"]
_data = {
"Location Reveal": SubResource("Animation_fp6po"),
"RESET": SubResource("Animation_ptdyo")
}

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_7ae73"]
resource_local_to_scene = true
transparency = 1
shading_mode = 0
uv1_scale = Vector3(0.99, 0.99, 0.99)
uv1_offset = Vector3(0.5, 0, -0.495)
uv1_triplanar = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_0c504"]
resource_local_to_scene = true
shading_mode = 0
albedo_texture = ExtResource("2_dsesp")
uv1_scale = Vector3(0.99, 0.99, 0.99)
uv1_offset = Vector3(0.5, 0, -0.495)
uv1_triplanar = true

[sub_resource type="BoxShape3D" id="BoxShape3D_7gkr8"]
size = Vector3(6.495, 0.1, 5.975)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_gifsr"]
resource_name = "Material"
vertex_color_use_as_albedo = true
albedo_color = Color(0.360784, 0.360784, 0.360784, 1)
metallic_specular = 0.0
roughness = 0.4
disable_receive_shadows = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_hqrfv"]

[sub_resource type="ArrayMesh" id="ArrayMesh_e0rnb"]
_surfaces = [{
"aabb": AABB(-1.58072, -0.0189328, -0.778515, 3.16144, 0.0378657, 1.55703),
"format": 34896613377,
"index_count": 402,
"index_data": PackedByteArray(2, 0, 5, 0, 3, 0, 2, 0, 4, 0, 5, 0, 4, 0, 7, 0, 5, 0, 4, 0, 6, 0, 7, 0, 6, 0, 9, 0, 7, 0, 6, 0, 8, 0, 9, 0, 8, 0, 11, 0, 9, 0, 8, 0, 10, 0, 11, 0, 10, 0, 13, 0, 11, 0, 10, 0, 12, 0, 13, 0, 12, 0, 15, 0, 13, 0, 12, 0, 14, 0, 15, 0, 14, 0, 17, 0, 15, 0, 14, 0, 16, 0, 17, 0, 16, 0, 19, 0, 17, 0, 16, 0, 18, 0, 19, 0, 18, 0, 21, 0, 19, 0, 18, 0, 20, 0, 21, 0, 20, 0, 23, 0, 21, 0, 20, 0, 22, 0, 23, 0, 22, 0, 25, 0, 23, 0, 22, 0, 24, 0, 25, 0, 24, 0, 27, 0, 25, 0, 24, 0, 26, 0, 27, 0, 26, 0, 29, 0, 27, 0, 26, 0, 28, 0, 29, 0, 28, 0, 31, 0, 29, 0, 28, 0, 30, 0, 31, 0, 30, 0, 33, 0, 31, 0, 30, 0, 32, 0, 33, 0, 32, 0, 1, 0, 33, 0, 32, 0, 0, 0, 1, 0, 32, 0, 2, 0, 0, 0, 2, 0, 6, 0, 4, 0, 6, 0, 10, 0, 8, 0, 10, 0, 14, 0, 12, 0, 14, 0, 18, 0, 16, 0, 18, 0, 22, 0, 20, 0, 22, 0, 26, 0, 24, 0, 26, 0, 30, 0, 28, 0, 30, 0, 2, 0, 32, 0, 2, 0, 10, 0, 6, 0, 10, 0, 18, 0, 14, 0, 18, 0, 26, 0, 22, 0, 26, 0, 2, 0, 30, 0, 2, 0, 18, 0, 10, 0, 2, 0, 26, 0, 18, 0, 5, 0, 1, 0, 3, 0, 1, 0, 31, 0, 33, 0, 31, 0, 27, 0, 29, 0, 27, 0, 23, 0, 25, 0, 23, 0, 19, 0, 21, 0, 19, 0, 15, 0, 17, 0, 15, 0, 11, 0, 13, 0, 11, 0, 7, 0, 9, 0, 7, 0, 1, 0, 5, 0, 1, 0, 27, 0, 31, 0, 27, 0, 19, 0, 23, 0, 19, 0, 11, 0, 15, 0, 11, 0, 1, 0, 7, 0, 1, 0, 19, 0, 27, 0, 1, 0, 11, 0, 19, 0, 1, 0, 35, 0, 3, 0, 1, 0, 34, 0, 35, 0, 38, 0, 41, 0, 39, 0, 38, 0, 40, 0, 41, 0, 40, 0, 43, 0, 41, 0, 40, 0, 42, 0, 43, 0, 42, 0, 45, 0, 43, 0, 42, 0, 44, 0, 45, 0, 44, 0, 47, 0, 45, 0, 44, 0, 46, 0, 47, 0, 46, 0, 49, 0, 47, 0, 46, 0, 48, 0, 49, 0, 48, 0, 51, 0, 49, 0, 48, 0, 50, 0, 51, 0, 50, 0, 53, 0, 51, 0, 50, 0, 52, 0, 53, 0, 52, 0, 55, 0, 53, 0, 52, 0, 54, 0, 55, 0, 54, 0, 57, 0, 55, 0, 54, 0, 56, 0, 57, 0, 56, 0, 59, 0, 57, 0, 56, 0, 58, 0, 59, 0, 58, 0, 61, 0, 59, 0, 58, 0, 60, 0, 61, 0, 60, 0, 63, 0, 61, 0, 60, 0, 62, 0, 63, 0, 62, 0, 65, 0, 63, 0, 62, 0, 64, 0, 65, 0, 64, 0, 67, 0, 65, 0, 64, 0, 66, 0, 67, 0, 66, 0, 69, 0, 67, 0, 66, 0, 68, 0, 69, 0, 68, 0, 37, 0, 69, 0, 68, 0, 36, 0, 37, 0, 68, 0, 38, 0, 36, 0, 38, 0, 42, 0, 40, 0, 42, 0, 46, 0, 44, 0, 46, 0, 50, 0, 48, 0, 50, 0, 54, 0, 52, 0, 54, 0, 58, 0, 56, 0, 58, 0, 62, 0, 60, 0, 62, 0, 66, 0, 64, 0, 66, 0, 38, 0, 68, 0, 38, 0, 46, 0, 42, 0, 46, 0, 54, 0, 50, 0, 54, 0, 62, 0, 58, 0, 62, 0, 38, 0, 66, 0, 38, 0, 54, 0, 46, 0, 38, 0, 62, 0, 54, 0, 41, 0, 37, 0, 39, 0, 37, 0, 67, 0, 69, 0, 67, 0, 63, 0, 65, 0, 63, 0, 59, 0, 61, 0, 59, 0, 55, 0, 57, 0, 55, 0, 51, 0, 53, 0, 51, 0, 47, 0, 49, 0, 47, 0, 43, 0, 45, 0, 43, 0, 37, 0, 41, 0, 37, 0, 63, 0, 67, 0, 63, 0, 55, 0, 59, 0, 55, 0, 47, 0, 51, 0, 47, 0, 37, 0, 43, 0, 37, 0, 55, 0, 63, 0, 37, 0, 47, 0, 55, 0, 37, 0, 34, 0, 39, 0, 37, 0, 35, 0, 34, 0, 39, 0, 0, 0, 38, 0, 39, 0, 1, 0, 0, 0, 38, 0, 2, 0, 36, 0, 38, 0, 0, 0, 2, 0, 36, 0, 3, 0, 37, 0, 36, 0, 2, 0, 3, 0),
"lods": [0.0303764, PackedByteArray(6, 0, 5, 0, 3, 0, 6, 0, 7, 0, 5, 0, 36, 0, 6, 0, 3, 0, 6, 0, 8, 0, 7, 0, 36, 0, 3, 0, 37, 0, 8, 0, 11, 0, 7, 0, 64, 0, 36, 0, 37, 0, 8, 0, 12, 0, 11, 0, 64, 0, 37, 0, 67, 0, 12, 0, 15, 0, 11, 0, 64, 0, 67, 0, 63, 0, 12, 0, 16, 0, 15, 0, 16, 0, 19, 0, 15, 0, 60, 0, 64, 0, 63, 0, 60, 0, 63, 0, 59, 0, 56, 0, 60, 0, 59, 0, 56, 0, 59, 0, 55, 0, 52, 0, 56, 0, 55, 0, 52, 0, 55, 0, 51, 0, 48, 0, 52, 0, 51, 0, 48, 0, 51, 0, 47, 0, 44, 0, 48, 0, 47, 0, 44, 0, 47, 0, 43, 0, 42, 0, 44, 0, 43, 0, 42, 0, 43, 0, 41, 0, 42, 0, 41, 0, 39, 0, 39, 0, 0, 0, 42, 0, 39, 0, 1, 0, 0, 0, 28, 0, 0, 0, 1, 0, 28, 0, 1, 0, 31, 0, 28, 0, 31, 0, 27, 0, 22, 0, 27, 0, 23, 0, 16, 0, 22, 0, 23, 0, 16, 0, 23, 0, 19, 0, 22, 0, 28, 0, 27, 0, 28, 0, 6, 0, 0, 0, 22, 0, 6, 0, 28, 0, 6, 0, 22, 0, 16, 0, 6, 0, 16, 0, 8, 0, 8, 0, 16, 0, 12, 0, 42, 0, 0, 0, 6, 0, 42, 0, 6, 0, 36, 0, 64, 0, 42, 0, 36, 0, 60, 0, 42, 0, 64, 0, 42, 0, 60, 0, 52, 0, 52, 0, 60, 0, 56, 0, 42, 0, 52, 0, 44, 0, 44, 0, 52, 0, 48, 0, 5, 0, 1, 0, 3, 0, 1, 0, 35, 0, 3, 0, 1, 0, 34, 0, 35, 0, 7, 0, 1, 0, 5, 0, 11, 0, 1, 0, 7, 0, 1, 0, 11, 0, 19, 0, 19, 0, 11, 0, 15, 0, 1, 0, 19, 0, 27, 0, 27, 0, 19, 0, 23, 0, 1, 0, 27, 0, 31, 0, 41, 0, 37, 0, 39, 0, 37, 0, 34, 0, 39, 0, 37, 0, 35, 0, 34, 0, 43, 0, 37, 0, 41, 0, 47, 0, 37, 0, 43, 0, 37, 0, 47, 0, 55, 0, 55, 0, 47, 0, 51, 0, 37, 0, 55, 0, 63, 0, 63, 0, 55, 0, 59, 0, 37, 0, 63, 0, 67, 0), 0.164506, PackedByteArray(16, 0, 5, 0, 3, 0, 16, 0, 19, 0, 5, 0, 64, 0, 16, 0, 3, 0, 64, 0, 3, 0, 37, 0, 64, 0, 37, 0, 67, 0, 64, 0, 67, 0, 63, 0, 56, 0, 64, 0, 63, 0, 56, 0, 63, 0, 55, 0, 48, 0, 56, 0, 55, 0, 22, 0, 27, 0, 19, 0, 16, 0, 22, 0, 19, 0, 22, 0, 28, 0, 27, 0, 28, 0, 31, 0, 27, 0, 28, 0, 1, 0, 31, 0, 39, 0, 1, 0, 28, 0, 39, 0, 28, 0, 48, 0, 48, 0, 41, 0, 39, 0, 48, 0, 55, 0, 41, 0, 22, 0, 16, 0, 28, 0, 48, 0, 28, 0, 16, 0, 48, 0, 16, 0, 64, 0, 56, 0, 48, 0, 64, 0, 5, 0, 1, 0, 3, 0, 1, 0, 35, 0, 3, 0, 1, 0, 34, 0, 35, 0, 1, 0, 5, 0, 19, 0, 1, 0, 19, 0, 27, 0, 1, 0, 27, 0, 31, 0, 41, 0, 37, 0, 39, 0, 37, 0, 34, 0, 39, 0, 37, 0, 35, 0, 34, 0, 37, 0, 41, 0, 55, 0, 37, 0, 55, 0, 63, 0, 37, 0, 63, 0, 67, 0)],
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 70,
"vertex_data": PackedByteArray(6, 47, 0, 0, 0, 0, 0, 0, 6, 47, 255, 255, 0, 0, 0, 0, 6, 47, 0, 0, 255, 255, 0, 0, 6, 47, 255, 255, 255, 255, 0, 0, 217, 37, 0, 0, 137, 253, 0, 0, 217, 37, 255, 255, 137, 253, 0, 0, 7, 29, 0, 0, 64, 246, 0, 0, 7, 29, 255, 255, 64, 246, 0, 0, 230, 20, 0, 0, 108, 234, 0, 0, 230, 20, 255, 255, 108, 234, 0, 0, 197, 13, 0, 0, 129, 218, 0, 0, 197, 13, 255, 255, 129, 218, 0, 0, 236, 7, 0, 0, 28, 199, 0, 0, 236, 7, 255, 255, 28, 199, 0, 0, 148, 3, 0, 0, 251, 176, 0, 0, 148, 3, 255, 255, 251, 176, 0, 0, 231, 0, 0, 0, 248, 152, 0, 0, 231, 0, 255, 255, 248, 152, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 0, 0, 255, 255, 255, 127, 0, 0, 231, 0, 0, 0, 6, 103, 0, 0, 231, 0, 255, 255, 6, 103, 0, 0, 148, 3, 0, 0, 3, 79, 0, 0, 148, 3, 255, 255, 3, 79, 0, 0, 236, 7, 0, 0, 226, 56, 0, 0, 236, 7, 255, 255, 226, 56, 0, 0, 197, 13, 0, 0, 125, 37, 0, 0, 197, 13, 255, 255, 125, 37, 0, 0, 230, 20, 0, 0, 146, 21, 0, 0, 230, 20, 255, 255, 146, 21, 0, 0, 7, 29, 0, 0, 190, 9, 0, 0, 7, 29, 255, 255, 190, 9, 0, 0, 217, 37, 0, 0, 117, 2, 0, 0, 217, 37, 255, 255, 117, 2, 0, 0, 255, 127, 255, 255, 0, 0, 0, 0, 255, 127, 255, 255, 255, 255, 0, 0, 248, 208, 0, 0, 255, 255, 0, 0, 248, 208, 255, 255, 255, 255, 0, 0, 248, 208, 0, 0, 0, 0, 0, 0, 248, 208, 255, 255, 0, 0, 0, 0, 37, 218, 0, 0, 117, 2, 0, 0, 37, 218, 255, 255, 117, 2, 0, 0, 247, 226, 0, 0, 190, 9, 0, 0, 247, 226, 255, 255, 190, 9, 0, 0, 24, 235, 0, 0, 146, 21, 0, 0, 24, 235, 255, 255, 146, 21, 0, 0, 57, 242, 0, 0, 125, 37, 0, 0, 57, 242, 255, 255, 125, 37, 0, 0, 18, 248, 0, 0, 226, 56, 0, 0, 18, 248, 255, 255, 226, 56, 0, 0, 106, 252, 0, 0, 3, 79, 0, 0, 106, 252, 255, 255, 3, 79, 0, 0, 23, 255, 0, 0, 6, 103, 0, 0, 23, 255, 255, 255, 6, 103, 0, 0, 255, 255, 0, 0, 255, 127, 0, 0, 255, 255, 255, 255, 255, 127, 0, 0, 23, 255, 0, 0, 248, 152, 0, 0, 23, 255, 255, 255, 248, 152, 0, 0, 106, 252, 0, 0, 251, 176, 0, 0, 106, 252, 255, 255, 251, 176, 0, 0, 18, 248, 0, 0, 28, 199, 0, 0, 18, 248, 255, 255, 28, 199, 0, 0, 57, 242, 0, 0, 129, 218, 0, 0, 57, 242, 255, 255, 129, 218, 0, 0, 24, 235, 0, 0, 108, 234, 0, 0, 24, 235, 255, 255, 108, 234, 0, 0, 247, 226, 0, 0, 64, 246, 0, 0, 247, 226, 255, 255, 64, 246, 0, 0, 37, 218, 0, 0, 137, 253, 0, 0, 37, 218, 255, 255, 137, 253, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_rhkqq"]
resource_name = "LaneModel_Cylinder"
_surfaces = [{
"aabb": AABB(-1.58072, -0.0189328, -0.778515, 3.16144, 0.0378657, 1.55703),
"attribute_data": PackedByteArray(0, 0, 255, 127, 255, 191, 142, 130, 0, 0, 255, 127, 0, 0, 0, 0, 255, 63, 142, 130, 0, 0, 0, 0, 255, 127, 255, 127, 255, 191, 111, 253, 255, 127, 255, 127, 255, 127, 0, 0, 255, 63, 111, 253, 255, 127, 0, 0, 2, 180, 65, 252, 255, 119, 255, 127, 255, 119, 255, 127, 3, 52, 65, 252, 255, 119, 0, 0, 255, 119, 0, 0, 124, 168, 194, 248, 255, 111, 255, 127, 255, 111, 255, 127, 124, 40, 194, 248, 255, 111, 0, 0, 255, 111, 0, 0, 221, 157, 20, 243, 255, 103, 255, 127, 255, 103, 255, 127, 221, 29, 20, 243, 255, 103, 0, 0, 255, 103, 0, 0, 141, 148, 112, 235, 255, 95, 255, 127, 255, 95, 255, 127, 142, 20, 112, 235, 255, 95, 0, 0, 255, 95, 0, 0, 233, 140, 33, 226, 255, 87, 255, 127, 255, 87, 255, 127, 234, 12, 33, 226, 255, 87, 0, 0, 255, 87, 0, 0, 60, 135, 130, 215, 255, 79, 255, 127, 255, 79, 255, 127, 60, 7, 130, 215, 255, 79, 0, 0, 255, 79, 0, 0, 189, 131, 251, 203, 255, 71, 255, 127, 255, 71, 255, 127, 189, 3, 251, 203, 255, 71, 0, 0, 255, 71, 0, 0, 142, 130, 255, 191, 255, 63, 255, 127, 255, 63, 255, 127, 143, 2, 255, 191, 255, 63, 0, 0, 255, 63, 0, 0, 189, 131, 2, 180, 255, 55, 255, 127, 255, 55, 255, 127, 189, 3, 2, 180, 255, 55, 0, 0, 255, 55, 0, 0, 60, 135, 124, 168, 255, 47, 255, 127, 255, 47, 255, 127, 60, 7, 124, 168, 255, 47, 0, 0, 255, 47, 0, 0, 233, 140, 221, 157, 255, 39, 255, 127, 255, 39, 255, 127, 234, 12, 221, 157, 255, 39, 0, 0, 255, 39, 0, 0, 141, 148, 141, 148, 255, 31, 255, 127, 255, 31, 255, 127, 142, 20, 141, 148, 255, 31, 0, 0, 255, 31, 0, 0, 221, 157, 233, 140, 255, 23, 255, 127, 255, 23, 255, 127, 221, 29, 233, 140, 255, 23, 0, 0, 255, 23, 0, 0, 124, 168, 60, 135, 255, 15, 255, 127, 255, 15, 255, 127, 124, 40, 60, 135, 255, 15, 0, 0, 255, 15, 0, 0, 2, 180, 189, 131, 255, 7, 255, 127, 255, 7, 255, 127, 3, 52, 189, 131, 255, 7, 0, 0, 255, 7, 0, 0, 255, 63, 142, 130, 255, 63, 111, 253, 255, 63, 142, 130, 255, 63, 111, 253, 0, 0, 255, 127, 255, 191, 142, 130, 0, 0, 255, 127, 0, 0, 0, 0, 255, 63, 142, 130, 0, 0, 0, 0, 255, 127, 255, 127, 255, 191, 111, 253, 255, 127, 255, 127, 255, 127, 0, 0, 255, 63, 111, 253, 255, 127, 0, 0, 2, 180, 65, 252, 255, 119, 255, 127, 255, 119, 255, 127, 3, 52, 65, 252, 255, 119, 0, 0, 255, 119, 0, 0, 124, 168, 194, 248, 255, 111, 255, 127, 255, 111, 255, 127, 124, 40, 194, 248, 255, 111, 0, 0, 255, 111, 0, 0, 221, 157, 20, 243, 255, 103, 255, 127, 255, 103, 255, 127, 221, 29, 20, 243, 255, 103, 0, 0, 255, 103, 0, 0, 141, 148, 112, 235, 255, 95, 255, 127, 255, 95, 255, 127, 142, 20, 112, 235, 255, 95, 0, 0, 255, 95, 0, 0, 233, 140, 33, 226, 255, 87, 255, 127, 255, 87, 255, 127, 234, 12, 33, 226, 255, 87, 0, 0, 255, 87, 0, 0, 60, 135, 130, 215, 255, 79, 255, 127, 255, 79, 255, 127, 60, 7, 130, 215, 255, 79, 0, 0, 255, 79, 0, 0, 189, 131, 251, 203, 255, 71, 255, 127, 255, 71, 255, 127, 189, 3, 251, 203, 255, 71, 0, 0, 255, 71, 0, 0, 142, 130, 255, 191, 255, 63, 255, 127, 255, 63, 255, 127, 143, 2, 255, 191, 255, 63, 0, 0, 255, 63, 0, 0, 189, 131, 2, 180, 255, 55, 255, 127, 255, 55, 255, 127, 189, 3, 2, 180, 255, 55, 0, 0, 255, 55, 0, 0, 60, 135, 124, 168, 255, 47, 255, 127, 255, 47, 255, 127, 60, 7, 124, 168, 255, 47, 0, 0, 255, 47, 0, 0, 233, 140, 221, 157, 255, 39, 255, 127, 255, 39, 255, 127, 234, 12, 221, 157, 255, 39, 0, 0, 255, 39, 0, 0, 141, 148, 141, 148, 255, 31, 255, 127, 255, 31, 255, 127, 142, 20, 141, 148, 255, 31, 0, 0, 255, 31, 0, 0, 221, 157, 233, 140, 255, 23, 255, 127, 255, 23, 255, 127, 221, 29, 233, 140, 255, 23, 0, 0, 255, 23, 0, 0, 124, 168, 60, 135, 255, 15, 255, 127, 255, 15, 255, 127, 124, 40, 60, 135, 255, 15, 0, 0, 255, 15, 0, 0, 2, 180, 189, 131, 255, 7, 255, 127, 255, 7, 255, 127, 3, 52, 189, 131, 255, 7, 0, 0, 255, 7, 0, 0, 255, 71, 255, 127, 255, 47, 255, 127, 255, 31, 0, 0, 255, 23, 255, 127, 255, 87, 255, 127, 255, 55, 255, 127, 255, 71, 255, 127, 255, 47, 255, 127, 255, 31, 0, 0, 255, 23, 255, 127),
"format": 34896613399,
"index_count": 402,
"index_data": PackedByteArray(8, 0, 16, 0, 11, 0, 8, 0, 13, 0, 16, 0, 14, 0, 22, 0, 17, 0, 14, 0, 19, 0, 22, 0, 20, 0, 28, 0, 23, 0, 20, 0, 25, 0, 28, 0, 26, 0, 34, 0, 29, 0, 26, 0, 31, 0, 34, 0, 32, 0, 40, 0, 35, 0, 32, 0, 37, 0, 40, 0, 38, 0, 46, 0, 41, 0, 38, 0, 43, 0, 46, 0, 44, 0, 52, 0, 47, 0, 44, 0, 49, 0, 52, 0, 50, 0, 58, 0, 53, 0, 50, 0, 55, 0, 58, 0, 56, 0, 65, 0, 59, 0, 56, 0, 62, 0, 65, 0, 61, 0, 71, 0, 64, 0, 61, 0, 68, 0, 71, 0, 67, 0, 77, 0, 70, 0, 67, 0, 74, 0, 77, 0, 73, 0, 83, 0, 76, 0, 73, 0, 80, 0, 83, 0, 79, 0, 89, 0, 82, 0, 79, 0, 86, 0, 89, 0, 85, 0, 95, 0, 88, 0, 85, 0, 92, 0, 95, 0, 91, 0, 101, 0, 94, 0, 91, 0, 98, 0, 101, 0, 97, 0, 5, 0, 100, 0, 97, 0, 2, 0, 5, 0, 96, 0, 7, 0, 1, 0, 7, 0, 18, 0, 12, 0, 18, 0, 30, 0, 24, 0, 30, 0, 42, 0, 36, 0, 42, 0, 54, 0, 48, 0, 54, 0, 66, 0, 60, 0, 66, 0, 78, 0, 72, 0, 78, 0, 90, 0, 84, 0, 90, 0, 7, 0, 96, 0, 7, 0, 30, 0, 18, 0, 30, 0, 54, 0, 42, 0, 54, 0, 78, 0, 66, 0, 78, 0, 7, 0, 90, 0, 7, 0, 54, 0, 30, 0, 7, 0, 78, 0, 54, 0, 15, 0, 4, 0, 10, 0, 4, 0, 93, 0, 99, 0, 93, 0, 81, 0, 87, 0, 81, 0, 69, 0, 75, 0, 69, 0, 57, 0, 63, 0, 57, 0, 45, 0, 51, 0, 45, 0, 33, 0, 39, 0, 33, 0, 21, 0, 27, 0, 21, 0, 4, 0, 15, 0, 4, 0, 81, 0, 93, 0, 81, 0, 57, 0, 69, 0, 57, 0, 33, 0, 45, 0, 33, 0, 4, 0, 21, 0, 4, 0, 57, 0, 81, 0, 4, 0, 33, 0, 57, 0, 4, 0, 105, 0, 10, 0, 4, 0, 102, 0, 105, 0, 114, 0, 122, 0, 117, 0, 114, 0, 119, 0, 122, 0, 120, 0, 128, 0, 123, 0, 120, 0, 125, 0, 128, 0, 126, 0, 134, 0, 129, 0, 126, 0, 131, 0, 134, 0, 132, 0, 140, 0, 135, 0, 132, 0, 137, 0, 140, 0, 138, 0, 146, 0, 141, 0, 138, 0, 143, 0, 146, 0, 144, 0, 152, 0, 147, 0, 144, 0, 149, 0, 152, 0, 150, 0, 158, 0, 153, 0, 150, 0, 155, 0, 158, 0, 156, 0, 165, 0, 159, 0, 156, 0, 162, 0, 165, 0, 161, 0, 171, 0, 164, 0, 161, 0, 168, 0, 171, 0, 167, 0, 177, 0, 170, 0, 167, 0, 174, 0, 177, 0, 173, 0, 183, 0, 176, 0, 173, 0, 180, 0, 183, 0, 179, 0, 189, 0, 182, 0, 179, 0, 186, 0, 189, 0, 185, 0, 195, 0, 188, 0, 185, 0, 192, 0, 195, 0, 191, 0, 201, 0, 194, 0, 191, 0, 198, 0, 201, 0, 197, 0, 207, 0, 200, 0, 197, 0, 204, 0, 207, 0, 203, 0, 111, 0, 206, 0, 203, 0, 108, 0, 111, 0, 202, 0, 113, 0, 107, 0, 113, 0, 124, 0, 118, 0, 124, 0, 136, 0, 130, 0, 136, 0, 148, 0, 142, 0, 148, 0, 160, 0, 154, 0, 160, 0, 172, 0, 166, 0, 172, 0, 184, 0, 178, 0, 184, 0, 196, 0, 190, 0, 196, 0, 113, 0, 202, 0, 113, 0, 136, 0, 124, 0, 136, 0, 160, 0, 148, 0, 160, 0, 184, 0, 172, 0, 184, 0, 113, 0, 196, 0, 113, 0, 160, 0, 136, 0, 113, 0, 184, 0, 160, 0, 121, 0, 110, 0, 116, 0, 110, 0, 199, 0, 205, 0, 199, 0, 187, 0, 193, 0, 187, 0, 175, 0, 181, 0, 175, 0, 163, 0, 169, 0, 163, 0, 151, 0, 157, 0, 151, 0, 139, 0, 145, 0, 139, 0, 127, 0, 133, 0, 127, 0, 110, 0, 121, 0, 110, 0, 187, 0, 199, 0, 187, 0, 163, 0, 175, 0, 163, 0, 139, 0, 151, 0, 139, 0, 110, 0, 127, 0, 110, 0, 163, 0, 187, 0, 110, 0, 139, 0, 163, 0, 110, 0, 103, 0, 116, 0, 110, 0, 104, 0, 103, 0, 115, 0, 0, 0, 112, 0, 115, 0, 3, 0, 0, 0, 113, 0, 7, 0, 107, 0, 113, 0, 1, 0, 7, 0, 106, 0, 9, 0, 109, 0, 106, 0, 6, 0, 9, 0),
"lods": [0.0303764, PackedByteArray(19, 0, 16, 0, 9, 0, 19, 0, 22, 0, 16, 0, 106, 0, 19, 0, 9, 0, 19, 0, 25, 0, 22, 0, 106, 0, 9, 0, 109, 0, 25, 0, 34, 0, 22, 0, 191, 0, 106, 0, 109, 0, 25, 0, 37, 0, 34, 0, 191, 0, 109, 0, 200, 0, 37, 0, 46, 0, 34, 0, 191, 0, 200, 0, 188, 0, 37, 0, 214, 0, 46, 0, 214, 0, 58, 0, 46, 0, 179, 0, 191, 0, 188, 0, 179, 0, 188, 0, 176, 0, 167, 0, 179, 0, 176, 0, 167, 0, 176, 0, 164, 0, 155, 0, 167, 0, 164, 0, 155, 0, 164, 0, 152, 0, 143, 0, 155, 0, 152, 0, 143, 0, 152, 0, 140, 0, 131, 0, 143, 0, 140, 0, 131, 0, 140, 0, 128, 0, 125, 0, 131, 0, 128, 0, 125, 0, 128, 0, 122, 0, 125, 0, 122, 0, 115, 0, 115, 0, 0, 0, 125, 0, 115, 0, 3, 0, 0, 0, 85, 0, 0, 0, 3, 0, 85, 0, 3, 0, 94, 0, 85, 0, 94, 0, 82, 0, 67, 0, 82, 0, 70, 0, 214, 0, 67, 0, 70, 0, 49, 0, 70, 0, 58, 0, 215, 0, 217, 0, 216, 0, 84, 0, 18, 0, 1, 0, 66, 0, 18, 0, 84, 0, 18, 0, 66, 0, 48, 0, 18, 0, 48, 0, 24, 0, 24, 0, 48, 0, 36, 0, 124, 0, 1, 0, 18, 0, 124, 0, 18, 0, 107, 0, 190, 0, 124, 0, 107, 0, 178, 0, 124, 0, 190, 0, 124, 0, 178, 0, 154, 0, 154, 0, 178, 0, 166, 0, 124, 0, 154, 0, 130, 0, 130, 0, 154, 0, 142, 0, 15, 0, 4, 0, 10, 0, 4, 0, 105, 0, 10, 0, 4, 0, 102, 0, 105, 0, 21, 0, 4, 0, 15, 0, 33, 0, 4, 0, 21, 0, 4, 0, 33, 0, 57, 0, 57, 0, 33, 0, 45, 0, 4, 0, 57, 0, 81, 0, 81, 0, 57, 0, 69, 0, 4, 0, 81, 0, 93, 0, 121, 0, 110, 0, 116, 0, 110, 0, 103, 0, 116, 0, 110, 0, 104, 0, 103, 0, 127, 0, 110, 0, 121, 0, 139, 0, 110, 0, 127, 0, 110, 0, 139, 0, 163, 0, 163, 0, 139, 0, 151, 0, 110, 0, 163, 0, 187, 0, 187, 0, 163, 0, 175, 0, 110, 0, 187, 0, 199, 0), 0.164506, PackedByteArray(49, 0, 16, 0, 9, 0, 49, 0, 58, 0, 16, 0, 191, 0, 49, 0, 9, 0, 191, 0, 9, 0, 109, 0, 191, 0, 109, 0, 200, 0, 191, 0, 200, 0, 188, 0, 167, 0, 191, 0, 188, 0, 167, 0, 188, 0, 164, 0, 212, 0, 213, 0, 164, 0, 67, 0, 82, 0, 58, 0, 208, 0, 209, 0, 58, 0, 209, 0, 211, 0, 210, 0, 85, 0, 94, 0, 82, 0, 85, 0, 3, 0, 94, 0, 115, 0, 3, 0, 85, 0, 115, 0, 85, 0, 143, 0, 143, 0, 122, 0, 115, 0, 143, 0, 164, 0, 122, 0, 66, 0, 48, 0, 84, 0, 142, 0, 84, 0, 48, 0, 142, 0, 48, 0, 190, 0, 166, 0, 142, 0, 190, 0, 15, 0, 4, 0, 10, 0, 4, 0, 105, 0, 10, 0, 4, 0, 102, 0, 105, 0, 4, 0, 15, 0, 57, 0, 4, 0, 57, 0, 81, 0, 4, 0, 81, 0, 93, 0, 121, 0, 110, 0, 116, 0, 110, 0, 103, 0, 116, 0, 110, 0, 104, 0, 103, 0, 110, 0, 121, 0, 163, 0, 110, 0, 163, 0, 187, 0, 110, 0, 187, 0, 199, 0)],
"material": SubResource("StandardMaterial3D_hqrfv"),
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 218,
"vertex_data": PackedByteArray(6, 47, 0, 0, 0, 0, 0, 0, 6, 47, 0, 0, 0, 0, 255, 63, 6, 47, 0, 0, 0, 0, 165, 250, 6, 47, 255, 255, 0, 0, 0, 0, 6, 47, 255, 255, 0, 0, 255, 191, 6, 47, 255, 255, 0, 0, 165, 250, 6, 47, 0, 0, 255, 255, 0, 0, 6, 47, 0, 0, 255, 255, 255, 63, 6, 47, 0, 0, 255, 255, 88, 133, 6, 47, 255, 255, 255, 255, 0, 0, 6, 47, 255, 255, 255, 255, 255, 191, 6, 47, 255, 255, 255, 255, 88, 133, 217, 37, 0, 0, 137, 253, 255, 63, 217, 37, 0, 0, 137, 253, 88, 133, 217, 37, 0, 0, 137, 253, 188, 143, 217, 37, 255, 255, 137, 253, 255, 191, 217, 37, 255, 255, 137, 253, 88, 133, 217, 37, 255, 255, 137, 253, 188, 143, 7, 29, 0, 0, 64, 246, 255, 63, 7, 29, 0, 0, 64, 246, 188, 143, 7, 29, 0, 0, 64, 246, 84, 153, 7, 29, 255, 255, 64, 246, 255, 191, 7, 29, 255, 255, 64, 246, 188, 143, 7, 29, 255, 255, 64, 246, 84, 153, 230, 20, 0, 0, 108, 234, 255, 63, 230, 20, 0, 0, 108, 234, 84, 153, 230, 20, 0, 0, 108, 234, 240, 161, 230, 20, 255, 255, 108, 234, 255, 191, 230, 20, 255, 255, 108, 234, 84, 153, 230, 20, 255, 255, 108, 234, 240, 161, 197, 13, 0, 0, 129, 218, 255, 63, 197, 13, 0, 0, 129, 218, 240, 161, 197, 13, 0, 0, 129, 218, 157, 169, 197, 13, 255, 255, 129, 218, 255, 191, 197, 13, 255, 255, 129, 218, 240, 161, 197, 13, 255, 255, 129, 218, 157, 169, 236, 7, 0, 0, 28, 199, 255, 63, 236, 7, 0, 0, 28, 199, 157, 169, 236, 7, 0, 0, 28, 199, 137, 176, 236, 7, 255, 255, 28, 199, 255, 191, 236, 7, 255, 255, 28, 199, 157, 169, 236, 7, 255, 255, 28, 199, 137, 176, 148, 3, 0, 0, 251, 176, 255, 63, 148, 3, 0, 0, 251, 176, 137, 176, 148, 3, 0, 0, 251, 176, 238, 182, 148, 3, 255, 255, 251, 176, 255, 191, 148, 3, 255, 255, 251, 176, 137, 176, 148, 3, 255, 255, 251, 176, 238, 182, 231, 0, 0, 0, 248, 152, 255, 63, 231, 0, 0, 0, 248, 152, 238, 182, 231, 0, 0, 0, 248, 152, 2, 189, 231, 0, 255, 255, 248, 152, 255, 191, 231, 0, 255, 255, 248, 152, 238, 182, 231, 0, 255, 255, 248, 152, 2, 189, 0, 0, 0, 0, 255, 127, 255, 63, 0, 0, 0, 0, 255, 127, 2, 189, 0, 0, 0, 0, 255, 127, 252, 194, 0, 0, 255, 255, 255, 127, 255, 191, 0, 0, 255, 255, 255, 127, 2, 189, 0, 0, 255, 255, 255, 127, 252, 194, 231, 0, 0, 0, 6, 103, 255, 63, 231, 0, 0, 0, 6, 103, 16, 201, 231, 0, 0, 0, 6, 103, 252, 194, 231, 0, 255, 255, 6, 103, 255, 191, 231, 0, 255, 255, 6, 103, 16, 201, 231, 0, 255, 255, 6, 103, 252, 194, 148, 3, 0, 0, 3, 79, 255, 63, 148, 3, 0, 0, 3, 79, 116, 207, 148, 3, 0, 0, 3, 79, 16, 201, 148, 3, 255, 255, 3, 79, 255, 191, 148, 3, 255, 255, 3, 79, 116, 207, 148, 3, 255, 255, 3, 79, 16, 201, 236, 7, 0, 0, 226, 56, 255, 63, 236, 7, 0, 0, 226, 56, 96, 214, 236, 7, 0, 0, 226, 56, 116, 207, 236, 7, 255, 255, 226, 56, 255, 191, 236, 7, 255, 255, 226, 56, 96, 214, 236, 7, 255, 255, 226, 56, 116, 207, 197, 13, 0, 0, 125, 37, 255, 63, 197, 13, 0, 0, 125, 37, 13, 222, 197, 13, 0, 0, 125, 37, 96, 214, 197, 13, 255, 255, 125, 37, 255, 191, 197, 13, 255, 255, 125, 37, 13, 222, 197, 13, 255, 255, 125, 37, 96, 214, 230, 20, 0, 0, 146, 21, 255, 63, 230, 20, 0, 0, 146, 21, 169, 230, 230, 20, 0, 0, 146, 21, 13, 222, 230, 20, 255, 255, 146, 21, 255, 191, 230, 20, 255, 255, 146, 21, 169, 230, 230, 20, 255, 255, 146, 21, 13, 222, 7, 29, 0, 0, 190, 9, 255, 63, 7, 29, 0, 0, 190, 9, 66, 240, 7, 29, 0, 0, 190, 9, 169, 230, 7, 29, 255, 255, 190, 9, 255, 191, 7, 29, 255, 255, 190, 9, 66, 240, 7, 29, 255, 255, 190, 9, 169, 230, 217, 37, 0, 0, 117, 2, 255, 63, 217, 37, 0, 0, 117, 2, 165, 250, 217, 37, 0, 0, 117, 2, 66, 240, 217, 37, 255, 255, 117, 2, 255, 191, 217, 37, 255, 255, 117, 2, 165, 250, 217, 37, 255, 255, 117, 2, 66, 240, 255, 127, 255, 255, 0, 0, 255, 191, 255, 127, 255, 255, 0, 0, 255, 191, 255, 127, 255, 255, 255, 255, 255, 191, 255, 127, 255, 255, 255, 255, 255, 191, 248, 208, 0, 0, 255, 255, 0, 0, 248, 208, 0, 0, 255, 255, 0, 0, 248, 208, 0, 0, 255, 255, 88, 133, 248, 208, 255, 255, 255, 255, 0, 0, 248, 208, 255, 255, 255, 255, 255, 255, 248, 208, 255, 255, 255, 255, 88, 133, 248, 208, 0, 0, 0, 0, 0, 0, 248, 208, 0, 0, 0, 0, 0, 0, 248, 208, 0, 0, 0, 0, 165, 250, 248, 208, 255, 255, 0, 0, 0, 0, 248, 208, 255, 255, 0, 0, 255, 255, 248, 208, 255, 255, 0, 0, 165, 250, 37, 218, 0, 0, 117, 2, 0, 0, 37, 218, 0, 0, 117, 2, 165, 250, 37, 218, 0, 0, 117, 2, 66, 240, 37, 218, 255, 255, 117, 2, 255, 255, 37, 218, 255, 255, 117, 2, 165, 250, 37, 218, 255, 255, 117, 2, 66, 240, 247, 226, 0, 0, 190, 9, 0, 0, 247, 226, 0, 0, 190, 9, 66, 240, 247, 226, 0, 0, 190, 9, 169, 230, 247, 226, 255, 255, 190, 9, 255, 255, 247, 226, 255, 255, 190, 9, 66, 240, 247, 226, 255, 255, 190, 9, 169, 230, 24, 235, 0, 0, 146, 21, 0, 0, 24, 235, 0, 0, 146, 21, 169, 230, 24, 235, 0, 0, 146, 21, 13, 222, 24, 235, 255, 255, 146, 21, 255, 255, 24, 235, 255, 255, 146, 21, 169, 230, 24, 235, 255, 255, 146, 21, 13, 222, 57, 242, 0, 0, 125, 37, 0, 0, 57, 242, 0, 0, 125, 37, 13, 222, 57, 242, 0, 0, 125, 37, 96, 214, 57, 242, 255, 255, 125, 37, 255, 255, 57, 242, 255, 255, 125, 37, 13, 222, 57, 242, 255, 255, 125, 37, 96, 214, 18, 248, 0, 0, 226, 56, 0, 0, 18, 248, 0, 0, 226, 56, 96, 214, 18, 248, 0, 0, 226, 56, 116, 207, 18, 248, 255, 255, 226, 56, 255, 255, 18, 248, 255, 255, 226, 56, 96, 214, 18, 248, 255, 255, 226, 56, 116, 207, 106, 252, 0, 0, 3, 79, 0, 0, 106, 252, 0, 0, 3, 79, 116, 207, 106, 252, 0, 0, 3, 79, 16, 201, 106, 252, 255, 255, 3, 79, 255, 255, 106, 252, 255, 255, 3, 79, 116, 207, 106, 252, 255, 255, 3, 79, 16, 201, 23, 255, 0, 0, 6, 103, 0, 0, 23, 255, 0, 0, 6, 103, 16, 201, 23, 255, 0, 0, 6, 103, 252, 194, 23, 255, 255, 255, 6, 103, 255, 255, 23, 255, 255, 255, 6, 103, 16, 201, 23, 255, 255, 255, 6, 103, 252, 194, 255, 255, 0, 0, 255, 127, 0, 0, 255, 255, 0, 0, 255, 127, 2, 189, 255, 255, 0, 0, 255, 127, 252, 194, 255, 255, 255, 255, 255, 127, 255, 255, 255, 255, 255, 255, 255, 127, 2, 189, 255, 255, 255, 255, 255, 127, 252, 194, 23, 255, 0, 0, 248, 152, 0, 0, 23, 255, 0, 0, 248, 152, 238, 182, 23, 255, 0, 0, 248, 152, 2, 189, 23, 255, 255, 255, 248, 152, 255, 255, 23, 255, 255, 255, 248, 152, 238, 182, 23, 255, 255, 255, 248, 152, 2, 189, 106, 252, 0, 0, 251, 176, 0, 0, 106, 252, 0, 0, 251, 176, 137, 176, 106, 252, 0, 0, 251, 176, 238, 182, 106, 252, 255, 255, 251, 176, 255, 255, 106, 252, 255, 255, 251, 176, 137, 176, 106, 252, 255, 255, 251, 176, 238, 182, 18, 248, 0, 0, 28, 199, 0, 0, 18, 248, 0, 0, 28, 199, 157, 169, 18, 248, 0, 0, 28, 199, 137, 176, 18, 248, 255, 255, 28, 199, 255, 255, 18, 248, 255, 255, 28, 199, 157, 169, 18, 248, 255, 255, 28, 199, 137, 176, 57, 242, 0, 0, 129, 218, 0, 0, 57, 242, 0, 0, 129, 218, 240, 161, 57, 242, 0, 0, 129, 218, 157, 169, 57, 242, 255, 255, 129, 218, 255, 255, 57, 242, 255, 255, 129, 218, 240, 161, 57, 242, 255, 255, 129, 218, 157, 169, 24, 235, 0, 0, 108, 234, 0, 0, 24, 235, 0, 0, 108, 234, 84, 153, 24, 235, 0, 0, 108, 234, 240, 161, 24, 235, 255, 255, 108, 234, 255, 255, 24, 235, 255, 255, 108, 234, 84, 153, 24, 235, 255, 255, 108, 234, 240, 161, 247, 226, 0, 0, 64, 246, 0, 0, 247, 226, 0, 0, 64, 246, 188, 143, 247, 226, 0, 0, 64, 246, 84, 153, 247, 226, 255, 255, 64, 246, 255, 255, 247, 226, 255, 255, 64, 246, 188, 143, 247, 226, 255, 255, 64, 246, 84, 153, 37, 218, 0, 0, 137, 253, 0, 0, 37, 218, 0, 0, 137, 253, 88, 133, 37, 218, 0, 0, 137, 253, 188, 143, 37, 218, 255, 255, 137, 253, 255, 255, 37, 218, 255, 255, 137, 253, 88, 133, 37, 218, 255, 255, 137, 253, 188, 143, 231, 0, 0, 0, 248, 152, 3, 203, 148, 3, 0, 0, 3, 79, 140, 222, 197, 13, 255, 255, 125, 37, 90, 232, 230, 20, 0, 0, 146, 21, 58, 238, 18, 248, 0, 0, 226, 56, 60, 227, 23, 255, 0, 0, 248, 152, 77, 208, 231, 0, 0, 0, 248, 152, 252, 185, 148, 3, 0, 0, 3, 79, 192, 222, 197, 13, 255, 255, 125, 37, 90, 232, 230, 20, 0, 0, 146, 21, 58, 238, 255, 255, 255, 127, 0, 0, 255, 127, 255, 127, 255, 255, 255, 255, 255, 127, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 127, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 127, 254, 255, 255, 127, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 255, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 255, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 255, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 0, 0, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 254, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 127, 255, 127, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 255, 127, 255, 127, 255, 191, 255, 127, 0, 0, 255, 255, 255, 127, 255, 127, 255, 63, 255, 127, 0, 0, 255, 255, 255, 127, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 191, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 191, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 63, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 38, 46, 130, 210, 10, 48, 177, 223, 103, 52, 207, 232, 36, 55, 71, 238, 252, 99, 1, 78, 186, 78, 162, 88, 219, 117, 97, 250, 204, 47, 152, 223, 103, 52, 207, 232, 36, 55, 71, 238)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_e0rnb")

[node name="Location" type="Node3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0.677435)
top_level = true
script = ExtResource("1_6twg1")

[node name="LocationName" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0.013, 0.0394132, -6.02902)
visible = false
text = "Event's Name "
font = ExtResource("2_77ff6")
font_size = 270
outline_size = 127
vertical_alignment = 0
line_spacing = -105.115
autowrap_mode = 3
width = 2000.0

[node name="UnrevealName" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0.013, 0.0565398, -6.19229)
alpha_cut = 1
texture_filter = 4
text = "TOXIC MIST"
font = ExtResource("2_77ff6")
font_size = 270
outline_size = 127
vertical_alignment = 0
autowrap_mode = 3
width = 1500.0

[node name="UnrevealDesc" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0.013, 0.151103, 2.69621)
alpha_cut = 1
texture_filter = 1
render_priority = 1
outline_render_priority = 0
modulate = Color(0, 0, 0, 1)
outline_modulate = Color(1, 1, 1, 1)
text = "Event's Name"
font = ExtResource("2_77ff6")
font_size = 210
outline_size = 62
vertical_alignment = 0
line_spacing = -30.9
autowrap_mode = 3
width = 1300.0

[node name="EnemyParticle" type="CPUParticles3D" parent="UnrevealDesc"]
transform = Transform3D(2.265, 0, 0, 0, 0.91, -2.13163e-14, 0, 1.42109e-14, 1.22, 0.206011, -1.00948, -0.0882202)
material_override = SubResource("ShaderMaterial_6oie6")
amount = 1
lifetime = 3.0
local_coords = true
mesh = SubResource("QuadMesh_n61r8")
gravity = Vector3(0, 0, 0)
color = Color(1, 0, 0, 1)
color_ramp = SubResource("Gradient_xhn1w")

[node name="AbilityText" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0.0132195, 0.026717, 4.69713)
visible = false
texture_filter = 5
text = "Not revealed yet"
font = ExtResource("2_77ff6")
font_size = 140
outline_size = 70
vertical_alignment = 2
autowrap_mode = 3
width = 1600.0

[node name="UnrevealTexture" type="MeshInstance3D" parent="."]
transform = Transform3D(8.33, 0, 0, 0, 0.046, 0, 0, 0, 9.615, 0, 0.0204091, -0.512465)
material_override = SubResource("StandardMaterial3D_0t3ib")
cast_shadow = 0
mesh = SubResource("BoxMesh_knnp4")

[node name="AnimationPlayer" type="AnimationPlayer" parent="UnrevealTexture"]
root_node = NodePath("../..")
libraries = {
"": SubResource("AnimationLibrary_8qmya")
}

[node name="UnrevealTexture2" type="MeshInstance3D" parent="."]
transform = Transform3D(6.519, 0, 0, 0, 0.036, 0, 0, 0, 13.4106, 0, -0.012907, -0.186597)
visible = false
material_override = SubResource("StandardMaterial3D_7ae73")
cast_shadow = 0
mesh = SubResource("BoxMesh_knnp4")

[node name="LocationTexture" type="MeshInstance3D" parent="."]
transform = Transform3D(8.33, 0, 0, 0, 0.046, 0, 0, 0, 9.615, 0, 0, -0.512465)
material_override = SubResource("StandardMaterial3D_0c504")
cast_shadow = 0
mesh = SubResource("BoxMesh_knnp4")

[node name="Area3D" type="Area3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1.2596, 0, 0, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="Area3D"]
transform = Transform3D(1.285, 0, 0, 0, 1.285, 0, 0, 0, 1.285, 0, 0, -0.406847)
shape = SubResource("BoxShape3D_7gkr8")

[node name="KeywordNotes" type="Label3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 4.82824, 0, -2.54815)
visible = false
texture_filter = 5
modulate = Color(1, 1, 1, 0.470588)
outline_modulate = Color(0, 0, 0, 0)
font = ExtResource("2_77ff6")
font_size = 64
horizontal_alignment = 0
vertical_alignment = 0
autowrap_mode = 2
width = 800.0

[node name="EventModel" type="MeshInstance3D" parent="."]
transform = Transform3D(-1.80528e-07, 0, 4.19, 0, 4.13, 0, -4.13, 0, -1.83151e-07, 0, -0.08, -0.16)
visible = false
material_override = SubResource("StandardMaterial3D_gifsr")
mesh = SubResource("ArrayMesh_rhkqq")
skeleton = NodePath("")

[connection signal="input_event" from="Area3D" to="." method="_on_area_3d_input_event"]
[connection signal="mouse_entered" from="Area3D" to="." method="_on_area_3d_mouse_entered"]
[connection signal="mouse_exited" from="Area3D" to="." method="_on_area_3d_mouse_exited"]
