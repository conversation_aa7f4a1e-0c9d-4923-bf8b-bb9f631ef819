[gd_scene load_steps=56 format=3 uid="uid://dsv6r37ingeaf"]

[ext_resource type="Script" path="res://effects/water_hand/water_hand_preview.gd" id="1_ocyw3"]
[ext_resource type="PackedScene" uid="uid://dfha3vt2clmfq" path="res://effects/water_hand/mesh/water_hand.glb" id="1_ul5tc"]
[ext_resource type="Shader" path="res://assets/ground_checkerboard.gdshader" id="2_8srkr"]
[ext_resource type="Material" uid="uid://btc57c1fm5im1" path="res://effects/water_hand/materials/water_hand_mat.tres" id="2_nopph"]
[ext_resource type="Script" path="res://effects/water_hand/mesh/water_hand.gd" id="3_f3c4c"]
[ext_resource type="Texture2D" uid="uid://3ro0yvv3roj3" path="res://Assets/checkerboard.svg" id="3_ky0hy"]
[ext_resource type="PackedScene" path="res://turner/turner.tscn" id="4_rvotv"]
[ext_resource type="Script" path="res://effects/water_hand/scripts/target.gd" id="8_o6qfi"]
[ext_resource type="PackedScene" uid="uid://obrsol71nwet" path="res://effects/water_hand/scripts/water_splash.tscn" id="8_qiyem"]
[ext_resource type="Shader" path="res://effects/water_hand/materials/wet_spot.gdshader" id="9_428na"]
[ext_resource type="PackedScene" uid="uid://ddjouhm76gjbx" path="res://Assets/dummy/dummy_skin.tscn" id="9_hx6do"]
[ext_resource type="Shader" path="res://effects/water_hand/materials/water_droplet_particles.gdshader" id="11_tcead"]
[ext_resource type="Texture2D" uid="uid://dgetus6yvt1hi" path="res://effects/water_hand/textures/water_particles_shape.png" id="12_6715a"]
[ext_resource type="Shader" path="res://effects/water_hand/materials/whirlpool.gdshader" id="13_bmyef"]
[ext_resource type="Script" path="res://effects/water_hand/scripts/arm_spawn_effect.gd" id="13_g52ik"]
[ext_resource type="Texture2D" uid="uid://bax1k2etu2b3" path="res://effects/water_hand/textures/caustic_texture.png" id="14_5kr1b"]

[sub_resource type="Environment" id="Environment_63fgl"]
background_mode = 1
background_color = Color(0.14902, 0.14902, 0.14902, 1)
ambient_light_source = 2
ambient_light_color = Color(0.368627, 0.368627, 0.368627, 1)
tonemap_mode = 2
glow_enabled = true
glow_levels/1 = 1.0
glow_levels/2 = 1.0
glow_levels/4 = 1.0
glow_levels/6 = 1.0
glow_levels/7 = 1.0
glow_normalized = true

[sub_resource type="Shader" id="Shader_v5le8"]
code = "shader_type spatial;
render_mode depth_draw_opaque,cull_back,unshaded;

uniform vec3 albedo : source_color;
uniform float proximity_fade_distance;
uniform sampler2D depth_texture : hint_depth_texture, repeat_disable, filter_nearest;

void fragment() {
	ALBEDO = albedo;
	float depth_tex = textureLod(depth_texture,SCREEN_UV,0.0).r;
	vec4 world_pos = INV_PROJECTION_MATRIX * vec4(SCREEN_UV*2.0-1.0,depth_tex,1.0);
	world_pos.xyz/=world_pos.w;
	ALPHA*=clamp(1.0-smoothstep(world_pos.z+proximity_fade_distance,world_pos.z,VERTEX.z),0.0,1.0);
}
"

[sub_resource type="ShaderMaterial" id="ShaderMaterial_6uv0p"]
render_priority = -128
shader = SubResource("Shader_v5le8")
shader_parameter/albedo = Color(0.14902, 0.14902, 0.14902, 1)
shader_parameter/proximity_fade_distance = 8.0

[sub_resource type="SphereMesh" id="SphereMesh_5nm6p"]
flip_faces = true
radius = 1.0
is_hemisphere = true

[sub_resource type="ShaderMaterial" id="ShaderMaterial_hjcv1"]
render_priority = 0
shader = ExtResource("2_8srkr")
shader_parameter/albedo_color = Color(0.2, 0.2, 0.2, 1)
shader_parameter/uv_scale = 20.0
shader_parameter/checkerboard_sampler = ExtResource("3_ky0hy")

[sub_resource type="PlaneMesh" id="PlaneMesh_k8hd6"]
size = Vector2(40, 40)

[sub_resource type="Animation" id="Animation_3pake"]
resource_name = "Slap"
length = 2.625
tracks/0/type = "position_3d"
tracks/0/imported = true
tracks/0/enabled = true
tracks/0/path = NodePath("Armature/Skeleton3D:BendyBone.001")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = PackedFloat32Array(0, 1, 0, -0.2, 0, 0.0333333, 1, 0, -0.19776, 0, 0.0666667, 1, 0, -0.19264, 0, 0.1, 1, 0, -0.18512, 0, 0.133333, 1, 0, -0.17568, 0, 0.166667, 1, 0, -0.1648, 0, 0.233333, 1, 0, -0.14112, 0, 0.266667, 1, 0, -0.12976, 0, 0.3, 1, 0, -0.11936, 0, 0.333333, 1, 0, -0.1104, 0, 0.366667, 1, 0, -0.10432, 0, 0.4, 1, 0, -0.10112, 0, 0.433333, 1, 0, -0.1, 0, 1.23333, 1, 0, -0.1, 0, 1.26667, 1, 0.000781858, -0.100857, 0, 1.3, 1, 0.00301455, -0.103429, 0, 1.33333, 1, 0.00725419, -0.108579, 0, 1.36667, 1, 0.0134689, -0.117277, 0, 1.4, 1, 0.0206056, -0.128701, 0, 1.43333, 1, 0.0282774, -0.14281, 0, 1.46667, 1, 0.0361039, -0.159499, 0, 1.5, 1, 0.0437116, -0.178601, 0, 1.53333, 1, 0.0503754, -0.200325, 0, 1.56667, 1, 0.055948, -0.223413, 0, 1.66667, 1, 0.0642788, -0.296281, 0, 1.7, 1, 0.0642788, -0.321089, 0, 1.73333, 1, 0.0642788, -0.346763, 0, 1.76667, 1, 0.0642788, -0.373199, 0, 1.8, 1, 0.0642788, -0.400292, 0, 1.83333, 1, 0.0642788, -0.427936, 0, 2.03333, 1, 0.0642788, -0.598265, 0, 2.06667, 1, 0.0642788, -0.626041, 0, 2.1, 1, 0.0642788, -0.653317, 0, 2.13333, 1, 0.0642788, -0.67999, 0, 2.16667, 1, 0.0642788, -0.705953, 0, 2.2, 1, 0.0642788, -0.730761, 0, 2.23333, 1, 0.0642788, -0.754545, 0, 2.26667, 1, 0.0642788, -0.777199, 0, 2.3, 1, 0.0642788, -0.798619, 0, 2.33333, 1, 0.0642788, -0.8187, 0, 2.36667, 1, 0.0642788, -0.836785, 0, 2.4, 1, 0.0642788, -0.853216, 0, 2.43333, 1, 0.0642788, -0.867887, 0, 2.46667, 1, 0.0642788, -0.880694, 0, 2.5, 1, 0.0642788, -0.891531, 0, 2.53333, 1, 0.0642788, -0.899532, 0, 2.56667, 1, 0.0642788, -0.905248, 0, 2.6, 1, 0.0642788, -0.908574, 0, 2.625, 1, 0.0642788, -0.909848, 0)
tracks/1/type = "rotation_3d"
tracks/1/imported = true
tracks/1/enabled = true
tracks/1/path = NodePath("Armature/Skeleton3D:BendyBone.001")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = PackedFloat32Array(0, 1, 0, 0, 0, 1, 1.23333, 1, 0, 0, -1.71779e-08, 1, 1.26667, 1, 0, 0, -0.00390955, 0.999992, 1.3, 1, 0, 0, -0.0150791, 0.999886, 1.33333, 1, 0, 0, -0.0362949, 0.999341, 1.36667, 1, 0, 0, -0.0675278, 0.997717, 1.4, 1, 0, 0, -0.103695, 0.994609, 1.43333, 1, 0, 0, -0.143043, 0.989716, 1.5, 1, 0, 0, -0.224271, 0.974527, 1.53333, 1, 0, 0, -0.261111, 0.965309, 1.56667, 1, 0, 0, -0.292772, 0.956182, 1.6, 1, 0, 0, -0.317768, 0.948169, 1.63333, 1, 0, 0, -0.334662, 0.942338, 1.66667, 1, 0, 0, -0.34202, 0.939693, 2.625, 1, 0, 0, -0.34202, 0.939693)
tracks/2/type = "scale_3d"
tracks/2/imported = true
tracks/2/enabled = true
tracks/2/path = NodePath("Armature/Skeleton3D:BendyBone.001")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = PackedFloat32Array(0, 1, 3.16228, 0.1, 3.16228, 0.0333333, 1, 3.14125, 0.101347, 3.14125, 0.0666667, 1, 3.09057, 0.104739, 3.09057, 0.1, 1, 3.01322, 0.11026, 3.01322, 0.133333, 1, 2.91273, 0.118024, 2.91273, 0.166667, 1, 2.79316, 0.128176, 2.79316, 0.2, 1, 2.65716, 0.141898, 2.65716, 0.233333, 1, 2.51521, 0.158644, 2.51521, 0.3, 1, 2.22707, 0.202263, 2.22707, 0.333333, 1, 2.08654, 0.229693, 2.08654, 0.366667, 1, 1.95366, 0.262854, 1.95366, 0.4, 1, 1.82792, 0.30079, 1.82792, 0.433333, 1, 1.71944, 0.342609, 1.71944, 0.466667, 1, 1.6357, 0.387367, 1.6357, 0.5, 1, 1.58156, 0.434072, 1.58156, 0.533333, 1, 1.55615, 0.483356, 1.55615, 0.566667, 1, 1.54668, 0.534345, 1.54668, 0.6, 1, 1.55034, 0.586851, 1.55034, 0.633333, 1, 1.56466, 0.640677, 1.56466, 0.666667, 1, 1.58745, 0.69563, 1.58745, 0.7, 1, 1.6181, 0.751701, 1.6181, 0.733333, 1, 1.65256, 0.808332, 1.65256, 0.766667, 1, 1.68971, 0.865338, 1.68971, 0.866667, 1, 1.80691, 1.03651, 1.80691, 0.9, 1, 1.84436, 1.09266, 1.84436, 0.933333, 1, 1.8798, 1.14793, 1.8798, 0.966667, 1, 1.91271, 1.20203, 1.91271, 1, 1, 1.94263, 1.25469, 1.94263, 1.03333, 1, 1.96779, 1.30468, 1.96779, 1.06667, 1, 1.98893, 1.35199, 1.98893, 1.1, 1, 2.00585, 1.396, 2.00585, 1.13333, 1, 2.01844, 1.43595, 2.01844, 1.16667, 1, 2.02666, 1.47097, 2.02666, 1.2, 1, 2.02954, 1.49667, 2.02954, 1.23333, 1, 2.02989, 1.51156, 2.02989, 1.26667, 1, 2.03718, 1.50609, 2.03718, 1.3, 1, 2.05991, 1.47346, 2.05991, 1.33333, 1, 2.10564, 1.40965, 2.10564, 1.36667, 1, 2.14873, 1.35409, 2.14873, 1.4, 1, 2.1572, 1.34307, 2.1572, 1.43333, 1, 2.14535, 1.35809, 2.14535, 1.46667, 1, 2.12993, 1.37769, 2.12993, 1.5, 1, 2.1301, 1.37746, 2.1301, 1.53333, 1, 2.14659, 1.35644, 2.14659, 1.56667, 1, 2.15121, 1.35056, 2.15121, 1.6, 1, 2.14559, 1.35769, 2.14559, 1.63333, 1, 2.13461, 1.37167, 2.13461, 1.66667, 1, 2.12636, 1.38231, 2.12636, 1.7, 1, 2.12203, 1.38796, 2.12203, 1.73333, 1, 2.11811, 1.39311, 2.11811, 1.76667, 1, 2.11444, 1.39795, 2.11444, 1.8, 1, 2.11092, 1.40261, 2.11092, 1.83333, 1, 2.10749, 1.40718, 2.10749, 2, 1, 2.09083, 1.42969, 2.09083, 2.06667, 1, 2.08437, 1.43858, 2.08437, 2.1, 1, 2.08119, 1.44296, 2.08119, 2.13333, 1, 2.07807, 1.44731, 2.07807, 2.16667, 1, 2.07499, 1.4516, 2.07499, 2.23333, 1, 2.06894, 1.46011, 2.06894, 2.26667, 1, 2.06587, 1.46446, 2.06587, 2.3, 1, 2.06266, 1.46902, 2.06266, 2.33333, 1, 2.0592, 1.47394, 2.0592, 2.36667, 1, 2.05489, 1.48015, 2.05489, 2.4, 1, 2.05021, 1.48692, 2.05021, 2.43333, 1, 2.04574, 1.49341, 2.04574, 2.46667, 1, 2.04181, 1.49917, 2.04181, 2.5, 1, 2.03845, 1.50412, 2.03845, 2.53333, 1, 2.03595, 1.50782, 2.03595, 2.56667, 1, 2.03415, 1.51047, 2.03415, 2.6, 1, 2.03311, 1.51203, 2.03311, 2.625, 1, 2.03271, 1.51263, 2.0327)
tracks/3/type = "position_3d"
tracks/3/imported = true
tracks/3/enabled = true
tracks/3/path = NodePath("Armature/Skeleton3D:BendyBone.010")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = PackedFloat32Array(0, 1, 0, -0.19, 0, 0.0333333, 1, 0, -0.187625, 0, 0.0666667, 1, 0, -0.182166, 0, 0.1, 1, 0, -0.174094, 0, 0.133333, 1, 0, -0.163878, 0, 0.166667, 1, 0, -0.151982, 0, 0.2, 1, 0, -0.13877, 0, 0.233333, 1, 0, -0.125256, 0, 0.266667, 1, 0, -0.111893, 0, 0.3, 1, 0, -0.0991337, 0, 0.333333, 1, 0, -0.0874307, 0, 0.366667, 1, 0, -0.0780346, 0, 0.4, 1, 0, -0.071041, 0, 0.433333, 1, -0.000105935, -0.0657384, 0, 0.466667, 1, -0.000366394, -0.0612603, 0, 0.5, 1, -0.000772622, -0.0565853, 0, 0.533333, 1, -0.00123471, -0.051647, 0, 0.566667, 1, -0.00170021, -0.0465341, 0, 0.6, 1, -0.00215351, -0.0412649, 0, 0.633333, 1, -0.0025814, -0.0358568, 0, 0.666667, 1, -0.00297304, -0.0303269, 0, 0.7, 1, -0.00330032, -0.0246648, 0, 0.733333, 1, -0.00357084, -0.0189234, 0, 0.8, 1, -0.00392802, -0.00724951, 0, 0.966667, 1, -0.00365403, 0.0224048, 0, 1, 1, -0.00343846, 0.0282584, 0, 1.03333, 1, -0.0031781, 0.0339742, 0, 1.06667, 1, -0.00289737, 0.0395172, 0, 1.1, 1, -0.00260571, 0.0448197, 0, 1.13333, 1, -0.00231226, 0.0497962, 0, 1.16667, 1, -0.00202583, 0.0543437, 0, 1.2, 1, -0.00175707, 0.0579873, 0, 1.23333, 1, -0.00147006, 0.0604917, 0, 1.26667, 1, 0.00119419, 0.0600771, 0, 1.3, 1, 0.0083171, 0.0551066, 0, 1.33333, 1, 0.0217025, 0.0440857, 0, 1.36667, 1, 0.0408443, 0.026988, 0, 1.4, 1, 0.0617927, 0.00840523, 0, 1.43333, 1, 0.0823034, -0.0119999, 0, 1.46667, 1, 0.10083, -0.0346672, 0, 1.5, 1, 0.116521, -0.0601388, 0, 1.53333, 1, 0.130399, -0.0882718, 0, 1.56667, 1, 0.143356, -0.115809, 0, 1.6, 1, 0.154234, -0.142297, 0, 1.63333, 1, 0.161893, -0.167591, 0, 1.66667, 1, 0.165217, -0.191847, 0, 1.7, 1, 0.16526, -0.215883, 0, 1.73333, 1, 0.165315, -0.240867, 0, 1.76667, 1, 0.165379, -0.266664, 0, 1.8, 1, 0.16545, -0.29315, 0, 1.83333, 1, 0.165529, -0.320207, 0, 2.03333, 1, 0.166139, -0.487183, 0, 2.06667, 1, 0.166257, -0.514423, 0, 2.1, 1, 0.166378, -0.541171, 0, 2.13333, 1, 0.166501, -0.567324, 0, 2.16667, 1, 0.166625, -0.592777, 0, 2.2, 1, 0.166752, -0.617082, 0, 2.23333, 1, 0.166883, -0.640363, 0, 2.26667, 1, 0.167018, -0.662507, 0, 2.3, 1, 0.167163, -0.683394, 0, 2.33333, 1, 0.167323, -0.702901, 0, 2.36667, 1, 0.167529, -0.720269, 0, 2.4, 1, 0.167758, -0.73592, 0, 2.43333, 1, 0.167983, -0.749846, 0, 2.46667, 1, 0.168185, -0.761995, 0, 2.5, 1, 0.168361, -0.772269, 0, 2.53333, 1, 0.168495, -0.779851, 0, 2.56667, 1, 0.168591, -0.785266, 0, 2.6, 1, 0.168648, -0.788416, 0, 2.625, 1, 0.16867, -0.789623, 0)
tracks/4/type = "rotation_3d"
tracks/4/imported = true
tracks/4/enabled = true
tracks/4/path = NodePath("Armature/Skeleton3D:BendyBone.010")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = PackedFloat32Array(0, 1, 0, 0, 0, 1, 0.4, 1, 0, 0, 0, 1, 0.433333, 1, 0, 0, 0.00272835, 0.999996, 0.466667, 1, 0, 0, 0.00889813, 0.99996, 0.5, 1, 0, 0, 0.0172069, 0.999852, 0.533333, 1, 0, 0, 0.0245292, 0.999699, 0.566667, 1, 0, 0, 0.0306132, 0.999531, 0.6, 1, 0, 0, 0.0354482, 0.999372, 0.633333, 1, 0, 0, 0.0390691, 0.999237, 0.666667, 1, 0, 0, 0.0415567, 0.999136, 0.7, 1, 0, 0, 0.0427348, 0.999086, 0.766667, 1, 0, 0, 0.0426969, 0.999088, 0.8, 1, 0, 0, 0.0416687, 0.999132, 0.833333, 1, 0, 0, 0.0400883, 0.999196, 0.866667, 1, 0, 0, 0.0379403, 0.99928, 0.9, 1, 0, 0, 0.0354871, 0.99937, 0.933333, 1, 0, 0, 0.032804, 0.999462, 0.966667, 1, 0, 0, 0.0299643, 0.999551, 1, 1, 0, 0, 0.0270395, 0.999634, 1.03333, 1, 0, 0, 0.0241331, 0.999709, 1.06667, 1, 0, 0, 0.0213293, 0.999773, 1.1, 1, 0, 0, 0.0186792, 0.999826, 1.13333, 1, 0, 0, 0.0162265, 0.999868, 1.16667, 1, 0, 0, 0.014008, 0.999902, 1.23333, 1, 0, 0, 0.010235, 0.999948, 1.26667, 1, 0, 0, 0.00253159, 0.999997, 1.3, 1, 0, 0, -0.0171721, 0.999853, 1.33333, 1, 0, 0, -0.0553386, 0.998468, 1.36667, 1, 0, 0, -0.115922, 0.993258, 1.4, 1, 0, 0, -0.181308, 0.983426, 1.43333, 1, 0, 0, -0.23966, 0.970857, 1.46667, 1, 0, 0, -0.285023, 0.958521, 1.5, 1, 0, 0, -0.317041, 0.948412, 1.53333, 1, 0, 0, -0.347801, 0.937568, 1.56667, 1, 0, 0, -0.375267, 0.926917, 1.6, 1, 0, 0, -0.395112, 0.918633, 1.63333, 1, 0, 0, -0.405574, 0.914062, 1.66667, 1, 0, 0, -0.407272, 0.913307, 1.7, 1, 0, 0, -0.404072, 0.914727, 1.73333, 1, 0, 0, -0.4013, 0.915947, 1.76667, 1, 0, 0, -0.39882, 0.917029, 1.8, 1, 0, 0, -0.396532, 0.918021, 1.83333, 1, 0, 0, -0.394378, 0.918948, 1.86667, 1, 0, 0, -0.392344, 0.919818, 1.9, 1, 0, 0, -0.39038, 0.920654, 1.93333, 1, 0, 0, -0.388478, 0.921458, 2, 1, 0, 0, -0.384845, 0.922981, 2.06667, 1, 0, 0, -0.381471, 0.924381, 2.1, 1, 0, 0, -0.379867, 0.925041, 2.13333, 1, 0, 0, -0.378316, 0.925676, 2.16667, 1, 0, 0, -0.376818, 0.926288, 2.2, 1, 0, 0, -0.375376, 0.926873, 2.3, 1, 0, 0, -0.371087, 0.928598, 2.33333, 1, 0, 0, -0.369546, 0.929212, 2.36667, 1, 0, 0, -0.367668, 0.929957, 2.4, 1, 0, 0, -0.365673, 0.930743, 2.43333, 1, 0, 0, -0.363804, 0.931475, 2.46667, 1, 0, 0, -0.362186, 0.932106, 2.5, 1, 0, 0, -0.360827, 0.932633, 2.53333, 1, 0, 0, -0.359831, 0.933017, 2.6, 1, 0, 0, -0.35871, 0.933449, 2.625, 1, 0, 0, -0.358553, 0.933509)
tracks/5/type = "scale_3d"
tracks/5/imported = true
tracks/5/enabled = true
tracks/5/path = NodePath("Armature/Skeleton3D:BendyBone.010")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = PackedFloat32Array(0, 1, 2.97254, 0.1, 2.97254, 0.0333333, 1, 2.95277, 0.101347, 2.95277, 0.0666667, 1, 2.90513, 0.104739, 2.90513, 0.1, 1, 2.83243, 0.11026, 2.83243, 0.133333, 1, 2.73797, 0.118024, 2.73797, 0.166667, 1, 2.62557, 0.128176, 2.62557, 0.2, 1, 2.49773, 0.141898, 2.49773, 0.233333, 1, 2.3643, 0.158644, 2.3643, 0.3, 1, 2.09345, 0.202263, 2.09345, 0.333333, 1, 1.96134, 0.229693, 1.96134, 0.366667, 1, 1.83644, 0.262854, 1.83644, 0.4, 1, 1.71824, 0.30079, 1.71824, 0.433333, 1, 1.61627, 0.342609, 1.61627, 0.466667, 1, 1.53756, 0.387367, 1.53756, 0.5, 1, 1.48667, 0.434072, 1.48667, 0.533333, 1, 1.46278, 0.483356, 1.46278, 0.566667, 1, 1.45388, 0.534345, 1.45388, 0.6, 1, 1.45732, 0.586851, 1.45732, 0.633333, 1, 1.47078, 0.640677, 1.47078, 0.666667, 1, 1.4922, 0.69563, 1.4922, 0.7, 1, 1.52101, 0.751701, 1.52101, 0.733333, 1, 1.55341, 0.808332, 1.55341, 0.766667, 1, 1.58833, 0.865338, 1.58833, 0.866667, 1, 1.6985, 1.03651, 1.6985, 0.9, 1, 1.7337, 1.09266, 1.7337, 0.933333, 1, 1.76701, 1.14793, 1.76701, 0.966667, 1, 1.79795, 1.20203, 1.79795, 1, 1, 1.82608, 1.25469, 1.82608, 1.03333, 1, 1.84972, 1.30468, 1.84972, 1.06667, 1, 1.86959, 1.35199, 1.86959, 1.1, 1, 1.8855, 1.396, 1.8855, 1.13333, 1, 1.89733, 1.43595, 1.89733, 1.16667, 1, 1.90506, 1.47097, 1.90506, 1.2, 1, 1.90777, 1.49667, 1.90777, 1.23333, 1, 1.9081, 1.51156, 1.9081, 1.26667, 1, 1.91495, 1.50609, 1.91495, 1.3, 1, 1.93632, 1.47346, 1.93632, 1.33333, 1, 1.9793, 1.40965, 1.9793, 1.36667, 1, 2.01981, 1.35409, 2.01981, 1.4, 1, 2.02777, 1.34307, 2.02777, 1.43333, 1, 2.01663, 1.35809, 2.01663, 1.46667, 1, 2.00213, 1.37769, 2.00213, 1.5, 1, 2.00229, 1.37746, 2.00229, 1.53333, 1, 2.01779, 1.35644, 2.01779, 1.56667, 1, 2.02214, 1.35056, 2.02214, 1.6, 1, 2.01686, 1.35769, 2.01686, 1.63333, 1, 2.00653, 1.37167, 2.00653, 1.66667, 1, 1.99878, 1.38231, 1.99878, 1.7, 1, 1.99471, 1.38796, 1.99471, 1.73333, 1, 1.99102, 1.39311, 1.99102, 1.76667, 1, 1.98757, 1.39795, 1.98757, 1.8, 1, 1.98426, 1.40261, 1.98426, 1.83333, 1, 1.98104, 1.40718, 1.98104, 2, 1, 1.96538, 1.42969, 1.96538, 2.06667, 1, 1.9593, 1.43858, 1.9593, 2.1, 1, 1.95632, 1.44296, 1.95632, 2.13333, 1, 1.95339, 1.44731, 1.95339, 2.16667, 1, 1.95049, 1.4516, 1.95049, 2.23333, 1, 1.9448, 1.46011, 1.9448, 2.26667, 1, 1.94191, 1.46446, 1.94191, 2.3, 1, 1.9389, 1.46902, 1.9389, 2.33333, 1, 1.93565, 1.47394, 1.93565, 2.36667, 1, 1.9316, 1.48015, 1.9316, 2.4, 1, 1.9272, 1.48692, 1.9272, 2.43333, 1, 1.923, 1.49341, 1.923, 2.46667, 1, 1.9193, 1.49917, 1.9193, 2.5, 1, 1.91614, 1.50412, 1.91614, 2.53333, 1, 1.91379, 1.50782, 1.91379, 2.56667, 1, 1.9121, 1.51047, 1.9121, 2.6, 1, 1.91112, 1.51203, 1.91112, 2.625, 1, 1.91074, 1.51263, 1.91074)
tracks/6/type = "position_3d"
tracks/6/imported = true
tracks/6/enabled = true
tracks/6/path = NodePath("Armature/Skeleton3D:BendyBone.009")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = PackedFloat32Array(0, 1, 0, -0.18, 0, 0.0333333, 1, 0, -0.177491, 0, 0.0666667, 1, 0, -0.171692, 0, 0.1, 1, 0, -0.163068, 0, 0.133333, 1, 0, -0.152075, 0, 0.166667, 1, 0, -0.139165, 0, 0.2, 1, 0, -0.12458, 0, 0.233333, 1, 0, -0.109391, 0, 0.266667, 1, 0, -0.0940251, 0, 0.3, 1, 0, -0.0789075, 0, 0.333333, 1, 0, -0.0644614, 0, 0.366667, 1, 0, -0.0517492, 0, 0.4, 1, 0, -0.040962, 0, 0.433333, 1, -0.00039605, -0.0314794, 0, 0.466667, 1, -0.00136909, -0.0225348, 0, 0.5, 1, -0.00288493, -0.0132148, 0, 0.533333, 1, -0.00461913, -0.00339803, 0, 0.566667, 1, -0.0063751, 0.00675297, 0, 0.6, 1, -0.00809616, 0.0172085, 0, 0.633333, 1, -0.00973411, 0.0279395, 0, 0.666667, 1, -0.0112493, 0.0389177, 0, 0.7, 1, -0.0125409, 0.0501744, 0, 0.733333, 1, -0.013632, 0.061605, 0, 0.8, 1, -0.0151566, 0.0848996, 0, 1, 1, -0.014003, 0.156127, 0, 1.03333, 1, -0.013139, 0.167617, 0, 1.06667, 1, -0.0121946, 0.178758, 0, 1.1, 1, -0.0112037, 0.189414, 0, 1.13333, 1, -0.0101992, 0.199412, 0, 1.16667, 1, -0.00921198, 0.208545, 0, 1.2, 1, -0.00827407, 0.215862, 0, 1.23333, 1, -0.00725081, 0.220898, 0, 1.26667, 1, -0.00164938, 0.220984, 0, 1.3, 1, 0.012726, 0.213597, 0, 1.33333, 1, 0.0397741, 0.196367, 0, 1.36667, 1, 0.0785008, 0.168731, 0, 1.4, 1, 0.119359, 0.13922, 0, 1.43333, 1, 0.156582, 0.108502, 0, 1.46667, 1, 0.186768, 0.0767914, 0, 1.5, 1, 0.208886, 0.0438493, 0, 1.53333, 1, 0.228496, 0.00834367, 0, 1.56667, 1, 0.247783, -0.0246793, 0, 1.6, 1, 0.264131, -0.0539908, 0, 1.63333, 1, 0.275372, -0.0795724, 0, 1.66667, 1, 0.279781, -0.102615, 0, 1.7, 1, 0.279343, -0.125089, 0, 1.73333, 1, 0.278993, -0.148705, 0, 1.76667, 1, 0.278705, -0.173266, 0, 1.8, 1, 0.278461, -0.1986, 0, 1.83333, 1, 0.278251, -0.224563, 0, 1.86667, 1, 0.278075, -0.251112, 0, 2.03333, 1, 0.27749, -0.38562, 0, 2.06667, 1, 0.277425, -0.411956, 0, 2.1, 1, 0.277372, -0.437822, 0, 2.13333, 1, 0.277332, -0.463116, 0, 2.16667, 1, 0.277303, -0.48773, 0, 2.2, 1, 0.277287, -0.51122, 0, 2.23333, 1, 0.27728, -0.533694, 0, 2.26667, 1, 0.277282, -0.555023, 0, 2.3, 1, 0.277293, -0.575066, 0, 2.33333, 1, 0.277314, -0.593672, 0, 2.36667, 1, 0.277359, -0.609925, 0, 2.4, 1, 0.277424, -0.624377, 0, 2.43333, 1, 0.2775, -0.637167, 0, 2.46667, 1, 0.277578, -0.648321, 0, 2.5, 1, 0.277654, -0.657751, 0, 2.53333, 1, 0.277717, -0.664707, 0, 2.56667, 1, 0.277764, -0.669675, 0, 2.6, 1, 0.277792, -0.672564, 0, 2.625, 1, 0.277803, -0.673671, 0)
tracks/7/type = "rotation_3d"
tracks/7/imported = true
tracks/7/enabled = true
tracks/7/path = NodePath("Armature/Skeleton3D:BendyBone.009")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = PackedFloat32Array(0, 1, 0, 0, 0, 1, 0.4, 1, 0, 0, 0, 1, 0.433333, 1, 0, 0, 0.00489942, 0.999988, 0.466667, 1, 0, 0, 0.0159704, 0.999873, 0.5, 1, 0, 0, 0.0308562, 0.999524, 0.533333, 1, 0, 0, 0.0441172, 0.999026, 0.566667, 1, 0, 0, 0.055252, 0.998472, 0.6, 1, 0, 0, 0.0642395, 0.997935, 0.633333, 1, 0, 0, 0.071138, 0.997466, 0.666667, 1, 0, 0, 0.0760843, 0.997101, 0.7, 1, 0, 0, 0.0787637, 0.996893, 0.733333, 1, 0, 0, 0.0799932, 0.996795, 0.766667, 1, 0, 0, 0.0799283, 0.996801, 0.8, 1, 0, 0, 0.0787199, 0.996897, 0.833333, 1, 0, 0, 0.0765144, 0.997068, 0.866667, 1, 0, 0, 0.0732574, 0.997313, 0.9, 1, 0, 0, 0.0694145, 0.997588, 0.933333, 1, 0, 0, 0.0651148, 0.997878, 0.966667, 1, 0, 0, 0.0604858, 0.998169, 1, 1, 0, 0, 0.0556529, 0.99845, 1.03333, 1, 0, 0, 0.0507843, 0.99871, 1.06667, 1, 0, 0, 0.0460497, 0.998939, 1.1, 1, 0, 0, 0.0415423, 0.999137, 1.13333, 1, 0, 0, 0.0373425, 0.999303, 1.16667, 1, 0, 0, 0.0335182, 0.999438, 1.23333, 1, 0, 0, 0.0269134, 0.999638, 1.26667, 1, 0, 0, 0.0162602, 0.999868, 1.3, 1, 0, 0, -0.00995917, 0.99995, 1.33333, 1, 0, 0, -0.0607588, 0.998153, 1.36667, 1, 0, 0, -0.141393, 0.989954, 1.4, 1, 0, 0, -0.226854, 0.973929, 1.43333, 1, 0, 0, -0.300745, 0.953705, 1.46667, 1, 0, 0, -0.355013, 0.934861, 1.5, 1, 0, 0, -0.389252, 0.921131, 1.53333, 1, 0, 0, -0.421735, 0.906719, 1.56667, 1, 0, 0, -0.449936, 0.893061, 1.6, 1, 0, 0, -0.467571, 0.883956, 1.63333, 1, 0, 0, -0.473218, 0.880946, 1.66667, 1, 0, 0, -0.469978, 0.882678, 1.7, 1, 0, 0, -0.463877, 0.8859, 1.73333, 1, 0, 0, -0.458606, 0.88864, 1.76667, 1, 0, 0, -0.453904, 0.891051, 1.8, 1, 0, 0, -0.449579, 0.893241, 1.83333, 1, 0, 0, -0.445517, 0.895274, 1.86667, 1, 0, 0, -0.441695, 0.897166, 1.9, 1, 0, 0, -0.43801, 0.89897, 1.93333, 1, 0, 0, -0.43445, 0.900696, 1.96667, 1, 0, 0, -0.431006, 0.902349, 2, 1, 0, 0, -0.427673, 0.903933, 2.03333, 1, 0, 0, -0.424487, 0.905434, 2.06667, 1, 0, 0, -0.42141, 0.90687, 2.1, 1, 0, 0, -0.41844, 0.908245, 2.13333, 1, 0, 0, -0.415574, 0.909559, 2.16667, 1, 0, 0, -0.412809, 0.910818, 2.2, 1, 0, 0, -0.410156, 0.912015, 2.23333, 1, 0, 0, -0.407552, 0.913182, 2.26667, 1, 0, 0, -0.404952, 0.914338, 2.3, 1, 0, 0, -0.402288, 0.915513, 2.33333, 1, 0, 0, -0.39947, 0.916746, 2.36667, 1, 0, 0, -0.396045, 0.918231, 2.4, 1, 0, 0, -0.392416, 0.919788, 2.43333, 1, 0, 0, -0.389021, 0.921229, 2.46667, 1, 0, 0, -0.386088, 0.922462, 2.5, 1, 0, 0, -0.383628, 0.923488, 2.53333, 1, 0, 0, -0.38183, 0.924232, 2.56667, 1, 0, 0, -0.38055, 0.92476, 2.625, 1, 0, 0, -0.379524, 0.925182)
tracks/8/type = "scale_3d"
tracks/8/imported = true
tracks/8/enabled = true
tracks/8/path = NodePath("Armature/Skeleton3D:BendyBone.009")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = PackedFloat32Array(0, 1, 2.7828, 0.1, 2.7828, 0.0333333, 1, 2.7643, 0.101347, 2.7643, 0.0666667, 1, 2.7197, 0.104739, 2.7197, 0.1, 1, 2.65163, 0.11026, 2.65163, 0.133333, 1, 2.5632, 0.118024, 2.5632, 0.166667, 1, 2.45798, 0.128176, 2.45798, 0.2, 1, 2.3383, 0.141898, 2.3383, 0.233333, 1, 2.21338, 0.158644, 2.21338, 0.3, 1, 1.95982, 0.202263, 1.95982, 0.333333, 1, 1.83615, 0.229693, 1.83615, 0.366667, 1, 1.71922, 0.262854, 1.71922, 0.4, 1, 1.60857, 0.30079, 1.60857, 0.433333, 1, 1.5131, 0.342609, 1.5131, 0.466667, 1, 1.43942, 0.387367, 1.43942, 0.5, 1, 1.39178, 0.434072, 1.39178, 0.533333, 1, 1.36941, 0.483356, 1.36941, 0.566667, 1, 1.36108, 0.534345, 1.36108, 0.6, 1, 1.3643, 0.586851, 1.3643, 0.633333, 1, 1.3769, 0.640677, 1.3769, 0.666667, 1, 1.39695, 0.69563, 1.39695, 0.7, 1, 1.42392, 0.751701, 1.42392, 0.733333, 1, 1.45425, 0.808332, 1.45425, 0.766667, 1, 1.48694, 0.865338, 1.48694, 0.866667, 1, 1.59008, 1.03651, 1.59008, 0.9, 1, 1.62304, 1.09266, 1.62304, 0.933333, 1, 1.65422, 1.14793, 1.65422, 0.966667, 1, 1.68319, 1.20203, 1.68319, 1, 1, 1.70952, 1.25469, 1.70952, 1.03333, 1, 1.73166, 1.30468, 1.73166, 1.06667, 1, 1.75026, 1.35199, 1.75026, 1.1, 1, 1.76515, 1.396, 1.76515, 1.13333, 1, 1.77622, 1.43595, 1.77622, 1.16667, 1, 1.78346, 1.47097, 1.78346, 1.2, 1, 1.786, 1.49667, 1.786, 1.23333, 1, 1.7863, 1.51156, 1.7863, 1.26667, 1, 1.79271, 1.50609, 1.79271, 1.3, 1, 1.81272, 1.47346, 1.81272, 1.33333, 1, 1.85297, 1.40965, 1.85297, 1.36667, 1, 1.89089, 1.35409, 1.89089, 1.4, 1, 1.89834, 1.34307, 1.89834, 1.43333, 1, 1.88791, 1.35809, 1.88791, 1.46667, 1, 1.87434, 1.37769, 1.87434, 1.5, 1, 1.87449, 1.37746, 1.87449, 1.53333, 1, 1.889, 1.35644, 1.889, 1.56667, 1, 1.89307, 1.35056, 1.89307, 1.6, 1, 1.88812, 1.35769, 1.88812, 1.63333, 1, 1.87845, 1.37167, 1.87845, 1.66667, 1, 1.8712, 1.38231, 1.8712, 1.7, 1, 1.86739, 1.38796, 1.86739, 1.73333, 1, 1.86393, 1.39311, 1.86393, 1.76667, 1, 1.8607, 1.39795, 1.8607, 1.8, 1, 1.85761, 1.40261, 1.85761, 1.83333, 1, 1.85459, 1.40718, 1.85459, 2, 1, 1.83993, 1.42969, 1.83993, 2.06667, 1, 1.83424, 1.43858, 1.83424, 2.1, 1, 1.83145, 1.44296, 1.83145, 2.13333, 1, 1.8287, 1.44731, 1.8287, 2.16667, 1, 1.826, 1.4516, 1.826, 2.23333, 1, 1.82067, 1.46011, 1.82067, 2.26667, 1, 1.81796, 1.46446, 1.81796, 2.3, 1, 1.81514, 1.46902, 1.81514, 2.33333, 1, 1.8121, 1.47394, 1.8121, 2.36667, 1, 1.8083, 1.48015, 1.8083, 2.4, 1, 1.80418, 1.48692, 1.80418, 2.43333, 1, 1.80025, 1.49341, 1.80025, 2.46667, 1, 1.79679, 1.49917, 1.79679, 2.5, 1, 1.79383, 1.50412, 1.79383, 2.53333, 1, 1.79163, 1.50782, 1.79163, 2.56667, 1, 1.79005, 1.51047, 1.79005, 2.6, 1, 1.78913, 1.51203, 1.78913, 2.625, 1, 1.78878, 1.51263, 1.78878)
tracks/9/type = "position_3d"
tracks/9/imported = true
tracks/9/enabled = true
tracks/9/path = NodePath("Armature/Skeleton3D:BendyBone.008")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = PackedFloat32Array(0, 1, 0, -0.17, 0, 0.0333333, 1, 0, -0.167356, 0, 0.0666667, 1, 0, -0.161218, 0, 0.1, 1, 0, -0.152042, 0, 0.133333, 1, 0, -0.140273, 0, 0.166667, 1, 0, -0.126347, 0, 0.2, 1, 0, -0.110391, 0, 0.233333, 1, 0, -0.0935269, 0, 0.3, 1, 0, -0.0586812, 0, 0.333333, 1, 0, -0.041492, 0, 0.366667, 1, 0, -0.0254639, 0, 0.4, 1, 0, -0.010883, 0, 0.466667, 1, -0.00286066, 0.0161714, 0, 0.5, 1, -0.00602546, 0.0300941, 0, 0.533333, 1, -0.00966329, 0.0447044, 0, 0.566667, 1, -0.0133631, 0.059786, 0, 0.6, 1, -0.0170106, 0.0753053, 0, 0.633333, 1, -0.0205091, 0.0912297, 0, 0.666667, 1, -0.0237791, 0.107527, 0, 0.7, 1, -0.026624, 0.124262, 0, 0.733333, 1, -0.0290824, 0.141282, 0, 0.766667, 1, -0.0311218, 0.158559, 0, 0.833333, 1, -0.0338482, 0.193755, 0, 1, 1, -0.0321044, 0.283145, 0, 1.03333, 1, -0.0305947, 0.300484, 0, 1.06667, 1, -0.0288987, 0.317301, 0, 1.1, 1, -0.0270836, 0.333387, 0, 1.13333, 1, -0.025213, 0.348479, 0, 1.16667, 1, -0.0233475, 0.362265, 0, 1.2, 1, -0.0215327, 0.373312, 0, 1.23333, 1, -0.0194874, 0.380937, 0, 1.26667, 1, -0.0101241, 0.381667, 0, 1.3, 1, 0.0134214, 0.372057, 0, 1.33333, 1, 0.0577898, 0.34869, 0, 1.36667, 1, 0.120966, 0.308932, 0, 1.4, 1, 0.185737, 0.265483, 0, 1.43333, 1, 0.242305, 0.221058, 0, 1.46667, 1, 0.285368, 0.177333, 0, 1.53333, 1, 0.339429, 0.0899925, 0, 1.56667, 1, 0.364854, 0.0495798, 0, 1.6, 1, 0.386238, 0.0165598, 0, 1.63333, 1, 0.400462, -0.00900492, 0, 1.66667, 1, 0.405441, -0.0298353, 0, 1.7, 1, 0.404275, -0.0499283, 0, 1.73333, 1, 0.403294, -0.0714805, 0, 1.76667, 1, 0.402443, -0.094192, 0, 1.8, 1, 0.401681, -0.117818, 0, 1.83333, 1, 0.400984, -0.142168, 0, 1.86667, 1, 0.400349, -0.167191, 0, 2.03333, 1, 0.397702, -0.294729, 0, 2.06667, 1, 0.397269, -0.319797, 0, 2.1, 1, 0.396861, -0.344434, 0, 2.13333, 1, 0.396479, -0.368535, 0, 2.16667, 1, 0.39612, -0.391993, 0, 2.2, 1, 0.395788, -0.414366, 0, 2.23333, 1, 0.395471, -0.435737, 0, 2.26667, 1, 0.395164, -0.455959, 0, 2.3, 1, 0.394858, -0.474863, 0, 2.33333, 1, 0.394545, -0.492256, 0, 2.36667, 1, 0.394184, -0.50702, 0, 2.4, 1, 0.393821, -0.519882, 0, 2.43333, 1, 0.393496, -0.531174, 0, 2.46667, 1, 0.393228, -0.541022, 0, 2.5, 1, 0.393015, -0.549349, 0, 2.53333, 1, 0.392867, -0.555493, 0, 2.56667, 1, 0.392763, -0.55988, 0, 2.6, 1, 0.392704, -0.562433, 0, 2.625, 1, 0.392682, -0.56341, 0)
tracks/10/type = "rotation_3d"
tracks/10/imported = true
tracks/10/enabled = true
tracks/10/path = NodePath("Armature/Skeleton3D:BendyBone.008")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = PackedFloat32Array(0, 1, 0, 0, 0, 1, 0.4, 1, 0, 0, 0, 1, 0.433333, 1, 0, 0, 0.00649658, 0.999979, 0.466667, 1, 0, 0, 0.0211774, 0.999776, 0.5, 1, 0, 0, 0.0409173, 0.999163, 0.533333, 1, 0, 0, 0.0586771, 0.998277, 0.566667, 1, 0, 0, 0.0737382, 0.997278, 0.6, 1, 0, 0, 0.0860786, 0.996288, 0.633333, 1, 0, 0, 0.0957792, 0.995403, 0.666667, 1, 0, 0, 0.103022, 0.994679, 0.7, 1, 0, 0, 0.10742, 0.994214, 0.733333, 1, 0, 0, 0.109995, 0.993932, 0.8, 1, 0, 0, 0.110425, 0.993885, 0.833333, 1, 0, 0, 0.10863, 0.994082, 0.866667, 1, 0, 0, 0.105445, 0.994425, 0.9, 1, 0, 0, 0.101451, 0.994841, 0.933333, 1, 0, 0, 0.0967977, 0.995304, 0.966667, 1, 0, 0, 0.0916341, 0.995793, 1, 1, 0, 0, 0.086109, 0.996286, 1.03333, 1, 0, 0, 0.0803915, 0.996763, 1.06667, 1, 0, 0, 0.0747337, 0.997204, 1.1, 1, 0, 0, 0.0692585, 0.997599, 1.13333, 1, 0, 0, 0.064075, 0.997945, 1.16667, 1, 0, 0, 0.0592778, 0.998242, 1.2, 1, 0, 0, 0.0550339, 0.998484, 1.23333, 1, 0, 0, 0.050704, 0.998714, 1.26667, 1, 0, 0, 0.0376907, 0.99929, 1.3, 1, 0, 0, 0.00612153, 0.999981, 1.33333, 1, 0, 0, -0.0550997, 0.998481, 1.36667, 1, 0, 0, -0.151023, 0.98853, 1.4, 1, 0, 0, -0.250443, 0.968132, 1.43333, 1, 0, 0, -0.335251, 0.942129, 1.46667, 1, 0, 0, -0.397473, 0.917614, 1.53333, 1, 0, 0, -0.477673, 0.878538, 1.56667, 1, 0, 0, -0.510791, 0.859705, 1.6, 1, 0, 0, -0.529871, 0.848079, 1.63333, 1, 0, 0, -0.533848, 0.845581, 1.66667, 1, 0, 0, -0.527666, 0.849452, 1.7, 1, 0, 0, -0.519528, 0.854454, 1.73333, 1, 0, 0, -0.512481, 0.858699, 1.76667, 1, 0, 0, -0.506179, 0.862429, 1.8, 1, 0, 0, -0.500372, 0.86581, 1.83333, 1, 0, 0, -0.49491, 0.868944, 1.86667, 1, 0, 0, -0.489764, 0.871855, 1.9, 1, 0, 0, -0.484798, 0.874626, 1.93333, 1, 0, 0, -0.479995, 0.877271, 1.96667, 1, 0, 0, -0.475345, 0.8798, 2, 1, 0, 0, -0.470841, 0.882218, 2.03333, 1, 0, 0, -0.466534, 0.884503, 2.06667, 1, 0, 0, -0.462372, 0.886686, 2.1, 1, 0, 0, -0.458353, 0.88877, 2.13333, 1, 0, 0, -0.454474, 0.89076, 2.16667, 1, 0, 0, -0.45073, 0.89266, 2.2, 1, 0, 0, -0.447138, 0.894465, 2.26667, 1, 0, 0, -0.44009, 0.897954, 2.3, 1, 0, 0, -0.436482, 0.899713, 2.33333, 1, 0, 0, -0.432665, 0.901555, 2.36667, 1, 0, 0, -0.428026, 0.903767, 2.4, 1, 0, 0, -0.423112, 0.906077, 2.43333, 1, 0, 0, -0.418516, 0.908209, 2.46667, 1, 0, 0, -0.414545, 0.910029, 2.5, 1, 0, 0, -0.411217, 0.911537, 2.53333, 1, 0, 0, -0.408785, 0.912631, 2.56667, 1, 0, 0, -0.407054, 0.913404, 2.6, 1, 0, 0, -0.40605, 0.913851, 2.625, 1, 0, 0, -0.405666, 0.914021)
tracks/11/type = "scale_3d"
tracks/11/imported = true
tracks/11/enabled = true
tracks/11/path = NodePath("Armature/Skeleton3D:BendyBone.008")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = PackedFloat32Array(0, 1, 2.59307, 0.1, 2.59307, 0.0333333, 1, 2.57582, 0.101347, 2.57582, 0.0666667, 1, 2.53427, 0.104739, 2.53427, 0.1, 1, 2.47084, 0.11026, 2.47084, 0.133333, 1, 2.38844, 0.118024, 2.38844, 0.166667, 1, 2.2904, 0.128176, 2.2904, 0.2, 1, 2.17887, 0.141898, 2.17887, 0.233333, 1, 2.06247, 0.158644, 2.06247, 0.3, 1, 1.8262, 0.202263, 1.8262, 0.333333, 1, 1.71096, 0.229693, 1.71096, 0.366667, 1, 1.602, 0.262854, 1.602, 0.4, 1, 1.49889, 0.30079, 1.49889, 0.433333, 1, 1.40994, 0.342609, 1.40994, 0.466667, 1, 1.34127, 0.387367, 1.34127, 0.5, 1, 1.29688, 0.434072, 1.29688, 0.533333, 1, 1.27604, 0.483356, 1.27604, 0.566667, 1, 1.26828, 0.534345, 1.26828, 0.6, 1, 1.27128, 0.586851, 1.27128, 0.633333, 1, 1.28302, 0.640677, 1.28302, 0.666667, 1, 1.30171, 0.69563, 1.30171, 0.7, 1, 1.32684, 0.751701, 1.32684, 0.733333, 1, 1.3551, 0.808332, 1.3551, 0.766667, 1, 1.38556, 0.865338, 1.38556, 0.866667, 1, 1.48167, 1.03651, 1.48167, 0.9, 1, 1.51238, 1.09266, 1.51238, 0.933333, 1, 1.54143, 1.14793, 1.54143, 0.966667, 1, 1.56842, 1.20203, 1.56842, 1, 1, 1.59296, 1.25469, 1.59296, 1.03333, 1, 1.61359, 1.30468, 1.61359, 1.06667, 1, 1.63092, 1.35199, 1.63092, 1.1, 1, 1.6448, 1.396, 1.6448, 1.13333, 1, 1.65512, 1.43595, 1.65512, 1.16667, 1, 1.66186, 1.47097, 1.66186, 1.2, 1, 1.66423, 1.49667, 1.66423, 1.23333, 1, 1.66451, 1.51156, 1.66451, 1.26667, 1, 1.67048, 1.50609, 1.67048, 1.3, 1, 1.68913, 1.47346, 1.68913, 1.33333, 1, 1.72663, 1.40965, 1.72663, 1.36667, 1, 1.76196, 1.35409, 1.76196, 1.4, 1, 1.76891, 1.34307, 1.76891, 1.43333, 1, 1.75919, 1.35809, 1.75919, 1.46667, 1, 1.74654, 1.37769, 1.74654, 1.5, 1, 1.74668, 1.37746, 1.74668, 1.53333, 1, 1.7602, 1.35644, 1.7602, 1.56667, 1, 1.76399, 1.35056, 1.76399, 1.6, 1, 1.75939, 1.35769, 1.75939, 1.63333, 1, 1.75038, 1.37167, 1.75038, 1.66667, 1, 1.74362, 1.38231, 1.74362, 1.7, 1, 1.74007, 1.38796, 1.74007, 1.73333, 1, 1.73685, 1.39311, 1.73685, 1.76667, 1, 1.73384, 1.39795, 1.73384, 1.8, 1, 1.73095, 1.40261, 1.73095, 1.83333, 1, 1.72814, 1.40718, 1.72814, 2, 1, 1.71448, 1.42969, 1.71448, 2.06667, 1, 1.70918, 1.43858, 1.70918, 2.1, 1, 1.70658, 1.44296, 1.70658, 2.13333, 1, 1.70402, 1.44731, 1.70402, 2.16667, 1, 1.7015, 1.4516, 1.7015, 2.23333, 1, 1.69653, 1.46011, 1.69653, 2.26667, 1, 1.69401, 1.46446, 1.69401, 2.3, 1, 1.69138, 1.46902, 1.69138, 2.33333, 1, 1.68855, 1.47394, 1.68855, 2.36667, 1, 1.68501, 1.48015, 1.68501, 2.4, 1, 1.68117, 1.48692, 1.68117, 2.43333, 1, 1.67751, 1.49341, 1.67751, 2.46667, 1, 1.67428, 1.49917, 1.67428, 2.5, 1, 1.67153, 1.50412, 1.67153, 2.53333, 1, 1.66948, 1.50782, 1.66948, 2.56667, 1, 1.668, 1.51047, 1.668, 2.6, 1, 1.66715, 1.51203, 1.66715, 2.625, 1, 1.66682, 1.51263, 1.66682)
tracks/12/type = "position_3d"
tracks/12/imported = true
tracks/12/enabled = true
tracks/12/path = NodePath("Armature/Skeleton3D:BendyBone.007")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = PackedFloat32Array(0, 1, 0, -0.16, 0, 0.0333333, 1, 0, -0.157221, 0, 0.0666667, 1, 0, -0.150744, 0, 0.1, 1, 0, -0.141016, 0, 0.133333, 1, 0, -0.12847, 0, 0.166667, 1, 0, -0.11353, 0, 0.2, 1, 0, -0.0962009, 0, 0.233333, 1, 0, -0.0776625, 0, 0.266667, 1, 0, -0.0582903, 0, 0.333333, 1, 0, -0.0185227, 0, 0.366667, 1, 0, 0.000821529, 0, 0.4, 1, 0, 0.019196, 0, 0.433333, 1, -0.00135755, 0.0370282, 0, 0.466667, 1, -0.00469158, 0.0548597, 0, 0.5, 1, -0.00988241, 0.0733458, 0, 0.533333, 1, -0.0158767, 0.0926685, 0, 0.566667, 1, -0.0219999, 0.112575, 0, 0.6, 1, -0.028072, 0.133033, 0, 0.633333, 1, -0.0339415, 0.154014, 0, 0.666667, 1, -0.0394853, 0.175486, 0, 0.7, 1, -0.0444089, 0.197557, 0, 0.733333, 1, -0.0487613, 0.22003, 0, 0.8, 1, -0.0555524, 0.266053, 0, 1, 1, -0.0581904, 0.40876, 0, 1.03333, 1, -0.0563464, 0.431987, 0, 1.06667, 1, -0.0541604, 0.454532, 0, 1.1, 1, -0.0517304, 0.47611, 0, 1.13333, 1, -0.0491499, 0.496366, 0, 1.16667, 1, -0.0465073, 0.514877, 0, 1.2, 1, -0.0438316, 0.529731, 0, 1.23333, 1, -0.0406601, 0.540034, 0, 1.26667, 1, -0.0269132, 0.541671, 0, 1.3, 1, 0.00743782, 0.53031, 0, 1.33333, 1, 0.0723711, 0.501399, 0, 1.36667, 1, 0.164146, 0.448745, 0, 1.4, 1, 0.255824, 0.389441, 0, 1.43333, 1, 0.333588, 0.329006, 0, 1.46667, 1, 0.390558, 0.27095, 0, 1.53333, 1, 0.458403, 0.159337, 0, 1.56667, 1, 0.490183, 0.108783, 0, 1.6, 1, 0.516642, 0.0703401, 0, 1.63333, 1, 0.533847, 0.0443477, 0, 1.66667, 1, 0.539417, 0.0262548, 0, 1.7, 1, 0.537574, 0.00917463, 0, 1.73333, 1, 0.535993, -0.0097668, 0, 1.76667, 1, 0.53459, -0.0301401, 0, 1.8, 1, 0.533309, -0.0516076, 0, 1.83333, 1, 0.532114, -0.0739239, 0, 1.86667, 1, 0.530998, -0.097023, 0, 1.9, 1, 0.529931, -0.120504, 0, 2.03333, 1, 0.526082, -0.215816, 0, 2.06667, 1, 0.525225, -0.239303, 0, 2.1, 1, 0.524403, -0.262409, 0, 2.13333, 1, 0.523617, -0.285028, 0, 2.16667, 1, 0.522864, -0.30705, 0, 2.2, 1, 0.522148, -0.328039, 0, 2.23333, 1, 0.521452, -0.348048, 0, 2.26667, 1, 0.520762, -0.366905, 0, 2.3, 1, 0.520061, -0.384406, 0, 2.33333, 1, 0.519327, -0.400309, 0, 2.36667, 1, 0.518446, -0.413251, 0, 2.4, 1, 0.517525, -0.424171, 0, 2.43333, 1, 0.516674, -0.433639, 0, 2.46667, 1, 0.515948, -0.441903, 0, 2.5, 1, 0.515346, -0.448894, 0, 2.53333, 1, 0.514911, -0.454057, 0, 2.56667, 1, 0.514603, -0.457745, 0, 2.6, 1, 0.514425, -0.459891, 0, 2.625, 1, 0.514358, -0.460712, 0)
tracks/13/type = "rotation_3d"
tracks/13/imported = true
tracks/13/enabled = true
tracks/13/path = NodePath("Armature/Skeleton3D:BendyBone.007")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = PackedFloat32Array(0, 1, 0, 0, 0, 1, 0.4, 1, 0, 0, 0, 1, 0.433333, 1, 0, 0, 0.00750829, 0.999972, 0.466667, 1, 0, 0, 0.0244973, 0.9997, 0.5, 1, 0, 0, 0.0473972, 0.998876, 0.533333, 1, 0, 0, 0.0682356, 0.997669, 0.566667, 1, 0, 0, 0.0861133, 0.996285, 0.6, 1, 0, 0, 0.101009, 0.994886, 0.633333, 1, 0, 0, 0.113021, 0.993593, 0.666667, 1, 0, 0, 0.122361, 0.992486, 0.7, 1, 0, 0, 0.128635, 0.991692, 0.733333, 1, 0, 0, 0.132957, 0.991122, 0.766667, 1, 0, 0, 0.135529, 0.990773, 0.8, 1, 0, 0, 0.136538, 0.990635, 0.833333, 1, 0, 0, 0.136163, 0.990686, 0.866667, 1, 0, 0, 0.134251, 0.990947, 0.9, 1, 0, 0, 0.131406, 0.991329, 0.933333, 1, 0, 0, 0.127765, 0.991805, 0.966667, 1, 0, 0, 0.123465, 0.992349, 1, 1, 0, 0, 0.118643, 0.992937, 1.03333, 1, 0, 0, 0.1134, 0.993549, 1.1, 1, 0, 0, 0.10271, 0.994711, 1.13333, 1, 0, 0, 0.0975152, 0.995234, 1.16667, 1, 0, 0, 0.0925694, 0.995706, 1.2, 1, 0, 0, 0.0880156, 0.996119, 1.23333, 1, 0, 0, 0.0831685, 0.996536, 1.26667, 1, 0, 0, 0.0684178, 0.997657, 1.3, 1, 0, 0, 0.0323988, 0.999475, 1.33333, 1, 0, 0, -0.0379801, 0.999278, 1.36667, 1, 0, 0, -0.147022, 0.989133, 1.4, 1, 0, 0, -0.257077, 0.966391, 1.43333, 1, 0, 0, -0.349702, 0.936861, 1.46667, 1, 0, 0, -0.418352, 0.908285, 1.5, 1, 0, 0, -0.466908, 0.884306, 1.53333, 1, 0, 0, -0.516173, 0.856484, 1.56667, 1, 0, 0, -0.5568, 0.830647, 1.6, 1, 0, 0, -0.580293, 0.814408, 1.63333, 1, 0, 0, -0.585789, 0.810464, 1.66667, 1, 0, 0, -0.579055, 0.815289, 1.7, 1, 0, 0, -0.56999, 0.821651, 1.73333, 1, 0, 0, -0.562096, 0.827072, 1.76667, 1, 0, 0, -0.555001, 0.83185, 1.8, 1, 0, 0, -0.548434, 0.836194, 1.83333, 1, 0, 0, -0.54223, 0.84023, 1.86667, 1, 0, 0, -0.53636, 0.843989, 1.9, 1, 0, 0, -0.530678, 0.847574, 1.93333, 1, 0, 0, -0.525165, 0.851001, 1.96667, 1, 0, 0, -0.51981, 0.854282, 2, 1, 0, 0, -0.514612, 0.857423, 2.03333, 1, 0, 0, -0.509624, 0.860397, 2.06667, 1, 0, 0, -0.504794, 0.86324, 2.1, 1, 0, 0, -0.50012, 0.865956, 2.13333, 1, 0, 0, -0.4956, 0.868551, 2.16667, 1, 0, 0, -0.491228, 0.871031, 2.2, 1, 0, 0, -0.487025, 0.873388, 2.26667, 1, 0, 0, -0.478759, 0.877946, 2.3, 1, 0, 0, -0.474518, 0.880246, 2.33333, 1, 0, 0, -0.470024, 0.882653, 2.36667, 1, 0, 0, -0.464552, 0.885546, 2.4, 1, 0, 0, -0.458746, 0.888567, 2.43333, 1, 0, 0, -0.453307, 0.891355, 2.46667, 1, 0, 0, -0.448601, 0.893732, 2.5, 1, 0, 0, -0.444652, 0.895703, 2.53333, 1, 0, 0, -0.441764, 0.897131, 2.56667, 1, 0, 0, -0.439707, 0.898141, 2.6, 1, 0, 0, -0.438513, 0.898725, 2.625, 1, 0, 0, -0.438056, 0.898948)
tracks/14/type = "scale_3d"
tracks/14/imported = true
tracks/14/enabled = true
tracks/14/path = NodePath("Armature/Skeleton3D:BendyBone.007")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = PackedFloat32Array(0, 1, 2.40333, 0.1, 2.40333, 0.0333333, 1, 2.38735, 0.101347, 2.38735, 0.0666667, 1, 2.34883, 0.104739, 2.34883, 0.1, 1, 2.29005, 0.11026, 2.29005, 0.133333, 1, 2.21368, 0.118024, 2.21368, 0.166667, 1, 2.12281, 0.128176, 2.12281, 0.2, 1, 2.01944, 0.141898, 2.01944, 0.233333, 1, 1.91156, 0.158644, 1.91156, 0.3, 1, 1.69257, 0.202263, 1.69257, 0.333333, 1, 1.58577, 0.229693, 1.58577, 0.366667, 1, 1.48478, 0.262854, 1.48478, 0.4, 1, 1.38922, 0.30079, 1.38922, 0.433333, 1, 1.30677, 0.342609, 1.30677, 0.466667, 1, 1.24313, 0.387367, 1.24313, 0.5, 1, 1.20199, 0.434072, 1.20199, 0.533333, 1, 1.18268, 0.483356, 1.18268, 0.566667, 1, 1.17548, 0.534345, 1.17548, 0.6, 1, 1.17826, 0.586851, 1.17826, 0.633333, 1, 1.18914, 0.640677, 1.18914, 0.666667, 1, 1.20646, 0.69563, 1.20646, 0.7, 1, 1.22975, 0.751701, 1.22975, 0.733333, 1, 1.25595, 0.808332, 1.25595, 0.766667, 1, 1.28418, 0.865338, 1.28418, 0.866667, 1, 1.37325, 1.03651, 1.37325, 0.9, 1, 1.40171, 1.09266, 1.40171, 0.933333, 1, 1.42865, 1.14793, 1.42865, 0.966667, 1, 1.45366, 1.20203, 1.45366, 1, 1, 1.4764, 1.25469, 1.4764, 1.03333, 1, 1.49552, 1.30468, 1.49552, 1.06667, 1, 1.51159, 1.35199, 1.51159, 1.1, 1, 1.52444, 1.396, 1.52444, 1.13333, 1, 1.53401, 1.43595, 1.53401, 1.16667, 1, 1.54026, 1.47097, 1.54026, 1.2, 1, 1.54245, 1.49667, 1.54245, 1.23333, 1, 1.54272, 1.51156, 1.54272, 1.26667, 1, 1.54825, 1.50609, 1.54825, 1.3, 1, 1.56553, 1.47346, 1.56553, 1.33333, 1, 1.60029, 1.40965, 1.60029, 1.36667, 1, 1.63304, 1.35409, 1.63304, 1.4, 1, 1.63947, 1.34307, 1.63947, 1.43333, 1, 1.63047, 1.35809, 1.63047, 1.46667, 1, 1.61874, 1.37769, 1.61874, 1.5, 1, 1.61888, 1.37746, 1.61888, 1.53333, 1, 1.63141, 1.35644, 1.63141, 1.56667, 1, 1.63492, 1.35056, 1.63492, 1.6, 1, 1.63065, 1.35769, 1.63065, 1.63333, 1, 1.6223, 1.37167, 1.6223, 1.66667, 1, 1.61604, 1.38231, 1.61604, 1.7, 1, 1.61275, 1.38796, 1.61275, 1.73333, 1, 1.60976, 1.39311, 1.60976, 1.76667, 1, 1.60697, 1.39795, 1.60697, 1.8, 1, 1.6043, 1.40261, 1.6043, 1.83333, 1, 1.60169, 1.40718, 1.60169, 2, 1, 1.58903, 1.42969, 1.58903, 2.06667, 1, 1.58412, 1.43858, 1.58412, 2.1, 1, 1.58171, 1.44296, 1.58171, 2.13333, 1, 1.57933, 1.44731, 1.57933, 2.16667, 1, 1.577, 1.4516, 1.577, 2.23333, 1, 1.57239, 1.46011, 1.57239, 2.26667, 1, 1.57006, 1.46446, 1.57006, 2.3, 1, 1.56762, 1.46902, 1.56762, 2.33333, 1, 1.565, 1.47394, 1.565, 2.36667, 1, 1.56172, 1.48015, 1.56172, 2.4, 1, 1.55816, 1.48692, 1.55816, 2.43333, 1, 1.55477, 1.49341, 1.55477, 2.46667, 1, 1.55177, 1.49917, 1.55177, 2.5, 1, 1.54922, 1.50412, 1.54922, 2.53333, 1, 1.54732, 1.50782, 1.54732, 2.56667, 1, 1.54596, 1.51047, 1.54596, 2.6, 1, 1.54516, 1.51203, 1.54516, 2.625, 1, 1.54486, 1.51263, 1.54486)
tracks/15/type = "position_3d"
tracks/15/imported = true
tracks/15/enabled = true
tracks/15/path = NodePath("Armature/Skeleton3D:BendyBone.006")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = PackedFloat32Array(0, 1, 0, -0.15, 0, 0.0333333, 1, 0, -0.147087, 0, 0.0666667, 1, 0, -0.14027, 0, 0.1, 1, 0, -0.12999, 0, 0.133333, 1, 0, -0.116668, 0, 0.166667, 1, 0, -0.100712, 0, 0.2, 1, 0, -0.0820111, 0, 0.233333, 1, 0, -0.0617982, 0, 0.266667, 1, 0, -0.0404228, 0, 0.3, 1, 0, -0.0182286, 0, 0.366667, 1, 0, 0.0271069, 0, 0.433333, 1, -0.00194108, 0.0712786, 0, 0.466667, 1, -0.00671134, 0.0935367, 0, 0.5, 1, -0.0141459, 0.11656, 0, 0.533333, 1, -0.0227762, 0.140537, 0, 0.566667, 1, -0.031636, 0.165188, 0, 0.6, 1, -0.0404782, 0.190486, 0, 0.633333, 1, -0.0490961, 0.216405, 0, 0.666667, 1, -0.0573243, 0.242919, 0, 0.7, 1, -0.0647862, 0.270175, 0, 0.733333, 1, -0.0715328, 0.297942, 0, 0.8, 1, -0.0826072, 0.354872, 0, 1, 1, -0.0927232, 0.532314, 0, 1.03333, 1, -0.0912628, 0.561348, 0, 1.06667, 1, -0.089265, 0.589565, 0, 1.1, 1, -0.0868452, 0.616604, 0, 1.13333, 1, -0.0841125, 0.642017, 0, 1.16667, 1, -0.0811697, 0.665272, 0, 1.2, 1, -0.0779678, 0.68398, 0, 1.23333, 1, -0.0738344, 0.697054, 0, 1.26667, 1, -0.0553642, 0.699991, 0, 1.3, 1, -0.00887531, 0.687722, 0, 1.33333, 1, 0.0795915, 0.654609, 0, 1.36667, 1, 0.204035, 0.589295, 0, 1.4, 1, 0.325424, 0.513443, 0, 1.43333, 1, 0.426082, 0.435829, 0, 1.46667, 1, 0.498041, 0.361823, 0, 1.5, 1, 0.543043, 0.293391, 0, 1.53333, 1, 0.581985, 0.219908, 0, 1.56667, 1, 0.620478, 0.155867, 0, 1.6, 1, 0.652165, 0.109494, 0, 1.63333, 1, 0.672566, 0.0817208, 0, 1.66667, 1, 0.679069, 0.0661837, 0, 1.7, 1, 0.676833, 0.0524295, 0, 1.73333, 1, 0.674882, 0.0363822, 0, 1.76667, 1, 0.673124, 0.0186106, 0, 1.8, 1, 0.671493, -0.000447807, 0, 1.83333, 1, 0.669949, -0.020487, 0, 1.86667, 1, 0.668483, -0.0414264, 0, 1.9, 1, 0.667064, -0.062814, 0, 2.06667, 1, 0.660584, -0.172037, 0, 2.1, 1, 0.659413, -0.193403, 0, 2.13333, 1, 0.65828, -0.214337, 0, 2.16667, 1, 0.657184, -0.234726, 0, 2.2, 1, 0.656129, -0.254139, 0, 2.23333, 1, 0.655093, -0.272594, 0, 2.26667, 1, 0.654056, -0.289895, 0, 2.3, 1, 0.652993, -0.305795, 0, 2.33333, 1, 0.651866, -0.319998, 0, 2.36667, 1, 0.650494, -0.330859, 0, 2.4, 1, 0.649039, -0.339562, 0, 2.43333, 1, 0.647677, -0.346946, 0, 2.46667, 1, 0.646499, -0.3534, 0, 2.5, 1, 0.645511, -0.358868, 0, 2.53333, 1, 0.644789, -0.362911, 0, 2.56667, 1, 0.644275, -0.3658, 0, 2.6, 1, 0.643976, -0.367481, 0, 2.625, 1, 0.643862, -0.368125, 0)
tracks/16/type = "rotation_3d"
tracks/16/imported = true
tracks/16/enabled = true
tracks/16/path = NodePath("Armature/Skeleton3D:BendyBone.006")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = PackedFloat32Array(0, 1, 0, 0, 0, 1, 0.4, 1, 0, 0, 0, 1, 0.433333, 1, 0, 0, 0.0079277, 0.999969, 0.466667, 1, 0, 0, 0.0259198, 0.999664, 0.5, 1, 0, 0, 0.0503143, 0.998733, 0.533333, 1, 0, 0, 0.0728541, 0.997343, 0.566667, 1, 0, 0, 0.0924918, 0.995714, 0.6, 1, 0, 0, 0.109198, 0.99402, 0.633333, 1, 0, 0, 0.123071, 0.992398, 0.666667, 1, 0, 0, 0.134333, 0.990936, 0.7, 1, 0, 0, 0.142633, 0.989776, 0.733333, 1, 0, 0, 0.149066, 0.988827, 0.766667, 1, 0, 0, 0.153837, 0.988096, 0.8, 1, 0, 0, 0.15713, 0.987578, 0.833333, 1, 0, 0, 0.159112, 0.987261, 0.9, 1, 0, 0, 0.159208, 0.987245, 0.933333, 1, 0, 0, 0.157969, 0.987444, 0.966667, 1, 0, 0, 0.15601, 0.987756, 1, 1, 0, 0, 0.153426, 0.98816, 1.03333, 1, 0, 0, 0.15021, 0.988654, 1.06667, 1, 0, 0, 0.146672, 0.989185, 1.1, 1, 0, 0, 0.142919, 0.989734, 1.2, 1, 0, 0, 0.131319, 0.99134, 1.23333, 1, 0, 0, 0.126828, 0.991925, 1.26667, 1, 0, 0, 0.111346, 0.993782, 1.3, 1, 0, 0, 0.0720904, 0.997398, 1.33333, 1, 0, 0, -0.00621312, 0.999981, 1.4, 1, 0, 0, -0.247129, 0.968983, 1.43333, 1, 0, 0, -0.346243, 0.938145, 1.46667, 1, 0, 0, -0.420928, 0.907094, 1.5, 1, 0, 0, -0.478876, 0.877882, 1.53333, 1, 0, 0, -0.539976, 0.84168, 1.56667, 1, 0, 0, -0.589817, 0.807537, 1.6, 1, 0, 0, -0.619758, 0.784793, 1.63333, 1, 0, 0, -0.629184, 0.777256, 1.66667, 1, 0, 0, -0.623985, 0.781437, 1.7, 1, 0, 0, -0.615055, 0.788485, 1.73333, 1, 0, 0, -0.607229, 0.794527, 1.76667, 1, 0, 0, -0.600154, 0.799884, 1.8, 1, 0, 0, -0.593571, 0.804782, 1.83333, 1, 0, 0, -0.58732, 0.809355, 1.86667, 1, 0, 0, -0.581372, 0.813638, 1.9, 1, 0, 0, -0.57559, 0.817739, 1.93333, 1, 0, 0, -0.569957, 0.821675, 1.96667, 1, 0, 0, -0.564465, 0.825457, 2, 1, 0, 0, -0.559113, 0.829091, 2.03333, 1, 0, 0, -0.553955, 0.832547, 2.06667, 1, 0, 0, -0.548942, 0.83586, 2.1, 1, 0, 0, -0.544077, 0.839035, 2.13333, 1, 0, 0, -0.539357, 0.842077, 2.16667, 1, 0, 0, -0.534779, 0.844992, 2.2, 1, 0, 0, -0.530362, 0.847772, 2.26667, 1, 0, 0, -0.521639, 0.853166, 2.3, 1, 0, 0, -0.517147, 0.855897, 2.33333, 1, 0, 0, -0.512374, 0.858762, 2.36667, 1, 0, 0, -0.506541, 0.862216, 2.4, 1, 0, 0, -0.50033, 0.865835, 2.43333, 1, 0, 0, -0.494494, 0.869181, 2.46667, 1, 0, 0, -0.48943, 0.872042, 2.5, 1, 0, 0, -0.485171, 0.874419, 2.53333, 1, 0, 0, -0.482048, 0.876145, 2.56667, 1, 0, 0, -0.479821, 0.877366, 2.6, 1, 0, 0, -0.478528, 0.878073, 2.625, 1, 0, 0, -0.478033, 0.878342)
tracks/17/type = "scale_3d"
tracks/17/imported = true
tracks/17/enabled = true
tracks/17/path = NodePath("Armature/Skeleton3D:BendyBone.006")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = PackedFloat32Array(0, 1, 2.21359, 0.1, 2.21359, 0.0333333, 1, 2.19887, 0.101347, 2.19887, 0.0666667, 1, 2.1634, 0.104739, 2.1634, 0.1, 1, 2.10925, 0.11026, 2.10925, 0.133333, 1, 2.03891, 0.118024, 2.03891, 0.166667, 1, 1.95522, 0.128176, 1.95522, 0.2, 1, 1.86001, 0.141898, 1.86001, 0.233333, 1, 1.76065, 0.158644, 1.76065, 0.3, 1, 1.55895, 0.202263, 1.55895, 0.333333, 1, 1.46058, 0.229693, 1.46058, 0.366667, 1, 1.36756, 0.262854, 1.36756, 0.4, 1, 1.27954, 0.30079, 1.27954, 0.433333, 1, 1.20361, 0.342609, 1.20361, 0.466667, 1, 1.14499, 0.387367, 1.14499, 0.5, 1, 1.1071, 0.434072, 1.10709, 0.533333, 1, 1.08931, 0.483355, 1.08931, 0.566667, 1, 1.08267, 0.534345, 1.08267, 0.6, 1, 1.08524, 0.586851, 1.08524, 0.633333, 1, 1.09526, 0.640677, 1.09526, 0.666667, 1, 1.11121, 0.69563, 1.11121, 0.7, 1, 1.13267, 0.751701, 1.13267, 0.733333, 1, 1.15679, 0.808332, 1.15679, 0.766667, 1, 1.1828, 0.865338, 1.1828, 0.866667, 1, 1.26484, 1.03651, 1.26484, 0.9, 1, 1.29105, 1.09266, 1.29105, 0.933333, 1, 1.31586, 1.14793, 1.31586, 0.966667, 1, 1.3389, 1.20203, 1.3389, 1, 1, 1.35984, 1.25469, 1.35984, 1.03333, 1, 1.37745, 1.30468, 1.37745, 1.06667, 1, 1.39225, 1.35199, 1.39225, 1.1, 1, 1.40409, 1.396, 1.40409, 1.13333, 1, 1.41291, 1.43595, 1.41291, 1.16667, 1, 1.41866, 1.47097, 1.41866, 1.2, 1, 1.42068, 1.49667, 1.42068, 1.23333, 1, 1.42092, 1.51156, 1.42092, 1.26667, 1, 1.42602, 1.50609, 1.42602, 1.3, 1, 1.44194, 1.47346, 1.44194, 1.33333, 1, 1.47395, 1.40965, 1.47395, 1.36667, 1, 1.50411, 1.35409, 1.50411, 1.4, 1, 1.51004, 1.34307, 1.51004, 1.43333, 1, 1.50175, 1.35809, 1.50175, 1.46667, 1, 1.49095, 1.37769, 1.49095, 1.5, 1, 1.49107, 1.37746, 1.49107, 1.53333, 1, 1.50261, 1.35644, 1.50261, 1.56667, 1, 1.50585, 1.35056, 1.50585, 1.6, 1, 1.50192, 1.35769, 1.50192, 1.63333, 1, 1.49423, 1.37167, 1.49423, 1.66667, 1, 1.48845, 1.38231, 1.48845, 1.7, 1, 1.48542, 1.38796, 1.48542, 1.73333, 1, 1.48267, 1.39311, 1.48267, 1.76667, 1, 1.48011, 1.39795, 1.48011, 1.8, 1, 1.47764, 1.40261, 1.47764, 1.83333, 1, 1.47524, 1.40718, 1.47524, 2, 1, 1.46358, 1.42969, 1.46358, 2.06667, 1, 1.45906, 1.43858, 1.45906, 2.1, 1, 1.45684, 1.44296, 1.45684, 2.13333, 1, 1.45465, 1.44731, 1.45465, 2.16667, 1, 1.4525, 1.4516, 1.4525, 2.23333, 1, 1.44826, 1.46011, 1.44826, 2.26667, 1, 1.44611, 1.46446, 1.44611, 2.3, 1, 1.44386, 1.46902, 1.44386, 2.33333, 1, 1.44144, 1.47394, 1.44144, 2.36667, 1, 1.43842, 1.48015, 1.43842, 2.4, 1, 1.43515, 1.48692, 1.43515, 2.43333, 1, 1.43202, 1.49341, 1.43202, 2.46667, 1, 1.42926, 1.49917, 1.42926, 2.5, 1, 1.42691, 1.50412, 1.42691, 2.53333, 1, 1.42516, 1.50782, 1.42516, 2.56667, 1, 1.42391, 1.51047, 1.42391, 2.6, 1, 1.42317, 1.51203, 1.42317, 2.625, 1, 1.42289, 1.51263, 1.42289)
tracks/18/type = "position_3d"
tracks/18/imported = true
tracks/18/enabled = true
tracks/18/path = NodePath("Armature/Skeleton3D:BendyBone.005")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = PackedFloat32Array(0, 1, 0, -0.14, 0, 0.0333333, 1, 0, -0.136952, 0, 0.0666667, 1, 0, -0.129796, 0, 0.1, 1, 0, -0.118964, 0, 0.133333, 1, 0, -0.104865, 0, 0.166667, 1, 0, -0.0878944, 0, 0.2, 1, 0, -0.0678213, 0, 0.233333, 1, 0, -0.0459338, 0, 0.266667, 1, 0, -0.0225554, 0, 0.3, 1, 0, 0.00199763, 0, 0.333333, 1, 0, 0.027416, 0, 0.4, 1, 0, 0.079354, 0, 0.433333, 1, -0.00253377, 0.105529, 0, 0.466667, 1, -0.00876918, 0.132211, 0, 0.5, 1, -0.0185083, 0.159763, 0, 0.533333, 1, -0.0298861, 0.188374, 0, 0.566667, 1, -0.0416381, 0.217731, 0, 0.6, 1, -0.0534538, 0.247811, 0, 0.633333, 1, -0.0650765, 0.278589, 0, 0.666667, 1, -0.0763033, 0.310041, 0, 0.733333, 1, -0.0963228, 0.375234, 0, 0.8, 1, -0.112874, 0.442649, 0, 1, 1, -0.136147, 0.653031, 0, 1.03333, 1, -0.136231, 0.687561, 0, 1.06667, 1, -0.135579, 0.721157, 0, 1.1, 1, -0.1343, 0.753392, 0, 1.13333, 1, -0.132492, 0.783735, 0, 1.16667, 1, -0.130244, 0.811545, 0, 1.2, 1, -0.127333, 0.833993, 0, 1.23333, 1, -0.122839, 0.849819, 0, 1.26667, 1, -0.0998231, 0.854543, 0, 1.3, 1, -0.0404595, 0.842691, 0, 1.33333, 1, 0.0739598, 0.807794, 0, 1.4, 1, 0.389736, 0.64001, 0, 1.43333, 1, 0.515433, 0.54514, 0, 1.46667, 1, 0.604099, 0.454172, 0, 1.5, 1, 0.66008, 0.368848, 0, 1.53333, 1, 0.707856, 0.275247, 0, 1.56667, 1, 0.75357, 0.193931, 0, 1.6, 1, 0.790626, 0.136507, 0, 1.63333, 1, 0.814395, 0.104818, 0, 1.66667, 1, 0.822208, 0.0909411, 0, 1.7, 1, 0.819945, 0.0804586, 0, 1.73333, 1, 0.817944, 0.0672747, 0, 1.76667, 1, 0.816118, 0.0520915, 0, 1.8, 1, 0.814404, 0.0354425, 0, 1.83333, 1, 0.812764, 0.0176916, 0, 1.86667, 1, 0.811186, -0.00106468, 0, 1.9, 1, 0.809643, -0.0203286, 0, 1.93333, 1, 0.808129, -0.0399667, 0, 2.06667, 1, 0.80239, -0.119731, 0, 2.1, 1, 0.80104, -0.139295, 0, 2.13333, 1, 0.799724, -0.158479, 0, 2.16667, 1, 0.79844, -0.177168, 0, 2.2, 1, 0.797193, -0.194934, 0, 2.23333, 1, 0.79596, -0.211764, 0, 2.26667, 1, 0.794719, -0.227431, 0, 2.3, 1, 0.793436, -0.241646, 0, 2.33333, 1, 0.792068, -0.254057, 0, 2.36667, 1, 0.790382, -0.262715, 0, 2.4, 1, 0.788576, -0.269066, 0, 2.43333, 1, 0.786871, -0.274233, 0, 2.46667, 1, 0.785384, -0.278757, 0, 2.5, 1, 0.784127, -0.282596, 0, 2.53333, 1, 0.7832, -0.28544, 0, 2.56667, 1, 0.782538, -0.287473, 0, 2.6, 1, 0.782152, -0.288656, 0, 2.625, 1, 0.782005, -0.28911, 0)
tracks/19/type = "rotation_3d"
tracks/19/imported = true
tracks/19/enabled = true
tracks/19/path = NodePath("Armature/Skeleton3D:BendyBone.005")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = PackedFloat32Array(0, 1, 0, 0, 0, 1, 0.4, 1, 0, 0, 0, 1, 0.433333, 1, 0, 0, 0.00775237, 0.99997, 0.466667, 1, 0, 0, 0.0254425, 0.999676, 0.5, 1, 0, 0, 0.0496813, 0.998765, 0.533333, 1, 0, 0, 0.0725771, 0.997363, 0.566667, 1, 0, 0, 0.0929644, 0.995669, 0.6, 1, 0, 0, 0.110791, 0.993844, 0.633333, 1, 0, 0, 0.126132, 0.992013, 0.666667, 1, 0, 0, 0.139188, 0.990266, 0.7, 1, 0, 0, 0.149688, 0.988733, 0.733333, 1, 0, 0, 0.158603, 0.987342, 0.766667, 1, 0, 0, 0.166123, 0.986105, 0.8, 1, 0, 0, 0.172416, 0.985024, 0.833333, 1, 0, 0, 0.177632, 0.984097, 0.866667, 1, 0, 0, 0.181641, 0.983365, 0.9, 1, 0, 0, 0.184875, 0.982762, 0.933333, 1, 0, 0, 0.187397, 0.982284, 0.966667, 1, 0, 0, 0.189266, 0.981926, 1, 1, 0, 0, 0.190533, 0.981681, 1.13333, 1, 0, 0, 0.190141, 0.981757, 1.16667, 1, 0, 0, 0.189125, 0.981953, 1.2, 1, 0, 0, 0.187646, 0.982237, 1.23333, 1, 0, 0, 0.18508, 0.982724, 1.26667, 1, 0, 0, 0.170839, 0.985299, 1.3, 1, 0, 0, 0.130904, 0.991395, 1.33333, 1, 0, 0, 0.0475518, 0.998869, 1.36667, 1, 0, 0, -0.0866475, 0.996239, 1.4, 1, 0, 0, -0.21559, 0.976484, 1.43333, 1, 0, 0, -0.322108, 0.946703, 1.46667, 1, 0, 0, -0.404823, 0.914395, 1.5, 1, 0, 0, -0.475895, 0.879502, 1.53333, 1, 0, 0, -0.551521, 0.834161, 1.56667, 1, 0, 0, -0.612272, 0.790648, 1.6, 1, 0, 0, -0.650175, 0.759784, 1.63333, 1, 0, 0, -0.665181, 0.746682, 1.66667, 1, 0, 0, -0.663043, 0.748581, 1.7, 1, 0, 0, -0.655093, 0.755548, 1.73333, 1, 0, 0, -0.648094, 0.76156, 1.76667, 1, 0, 0, -0.641738, 0.766924, 1.8, 1, 0, 0, -0.635798, 0.771856, 1.83333, 1, 0, 0, -0.630135, 0.776485, 1.86667, 1, 0, 0, -0.62472, 0.780849, 1.9, 1, 0, 0, -0.619435, 0.785048, 1.93333, 1, 0, 0, -0.614267, 0.789098, 1.96667, 1, 0, 0, -0.609211, 0.793009, 2, 1, 0, 0, -0.604264, 0.796785, 2.03333, 1, 0, 0, -0.599474, 0.800394, 2.06667, 1, 0, 0, -0.594804, 0.803871, 2.1, 1, 0, 0, -0.590255, 0.807217, 2.13333, 1, 0, 0, -0.585828, 0.810435, 2.16667, 1, 0, 0, -0.581519, 0.813532, 2.2, 1, 0, 0, -0.577345, 0.8165, 2.26667, 1, 0, 0, -0.569067, 0.822291, 2.3, 1, 0, 0, -0.564784, 0.825239, 2.33333, 1, 0, 0, -0.560219, 0.828345, 2.36667, 1, 0, 0, -0.554613, 0.832108, 2.4, 1, 0, 0, -0.548619, 0.836073, 2.43333, 1, 0, 0, -0.542965, 0.839755, 2.46667, 1, 0, 0, -0.53804, 0.842919, 2.5, 1, 0, 0, -0.533883, 0.845558, 2.53333, 1, 0, 0, -0.530825, 0.847481, 2.56667, 1, 0, 0, -0.528642, 0.848845, 2.6, 1, 0, 0, -0.527372, 0.849635, 2.625, 1, 0, 0, -0.526886, 0.849936)
tracks/20/type = "scale_3d"
tracks/20/imported = true
tracks/20/enabled = true
tracks/20/path = NodePath("Armature/Skeleton3D:BendyBone.005")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = PackedFloat32Array(0, 1, 2.02386, 0.1, 2.02386, 0.0333333, 1, 2.0104, 0.101347, 2.0104, 0.0666667, 1, 1.97796, 0.104739, 1.97796, 0.1, 1, 1.92846, 0.11026, 1.92846, 0.133333, 1, 1.86415, 0.118024, 1.86415, 0.166667, 1, 1.78763, 0.128176, 1.78763, 0.2, 1, 1.70058, 0.141898, 1.70058, 0.233333, 1, 1.60973, 0.158644, 1.60973, 0.3, 1, 1.42533, 0.202263, 1.42533, 0.333333, 1, 1.33538, 0.229693, 1.33538, 0.366667, 1, 1.25034, 0.262854, 1.25034, 0.4, 1, 1.16987, 0.30079, 1.16987, 0.433333, 1, 1.10044, 0.342609, 1.10044, 0.466667, 1, 1.04685, 0.387367, 1.04685, 0.5, 1, 1.0122, 0.434072, 1.0122, 0.533333, 1, 0.995937, 0.483356, 0.995938, 0.566667, 1, 0.989874, 0.534345, 0.989874, 0.6, 1, 0.992219, 0.586851, 0.992219, 0.633333, 1, 1.00138, 0.640677, 1.00138, 0.666667, 1, 1.01597, 0.69563, 1.01597, 0.7, 1, 1.03558, 0.751701, 1.03558, 0.733333, 1, 1.05764, 0.808332, 1.05764, 0.766667, 1, 1.08141, 0.865338, 1.08141, 0.866667, 1, 1.15642, 1.03651, 1.15642, 0.9, 1, 1.18039, 1.09266, 1.18039, 0.933333, 1, 1.20307, 1.14793, 1.20307, 0.966667, 1, 1.22414, 1.20203, 1.22414, 1, 1, 1.24329, 1.25469, 1.24329, 1.03333, 1, 1.25939, 1.30468, 1.25939, 1.06667, 1, 1.27291, 1.35199, 1.27291, 1.1, 1, 1.28374, 1.396, 1.28374, 1.13333, 1, 1.2918, 1.43595, 1.2918, 1.16667, 1, 1.29706, 1.47097, 1.29706, 1.2, 1, 1.29891, 1.49667, 1.29891, 1.23333, 1, 1.29913, 1.51156, 1.29913, 1.26667, 1, 1.30379, 1.50609, 1.30379, 1.3, 1, 1.31834, 1.47346, 1.31834, 1.33333, 1, 1.34761, 1.40965, 1.34761, 1.36667, 1, 1.37519, 1.35409, 1.37519, 1.4, 1, 1.38061, 1.34307, 1.38061, 1.43333, 1, 1.37303, 1.35809, 1.37303, 1.46667, 1, 1.36315, 1.37769, 1.36315, 1.5, 1, 1.36326, 1.37746, 1.36326, 1.53333, 1, 1.37382, 1.35644, 1.37382, 1.56667, 1, 1.37678, 1.35056, 1.37678, 1.6, 1, 1.37318, 1.35769, 1.37318, 1.63333, 1, 1.36615, 1.37167, 1.36615, 1.66667, 1, 1.36087, 1.38231, 1.36087, 1.7, 1, 1.3581, 1.38796, 1.3581, 1.73333, 1, 1.35559, 1.39311, 1.35559, 1.76667, 1, 1.35324, 1.39795, 1.35324, 1.8, 1, 1.35099, 1.40261, 1.35099, 1.83333, 1, 1.34879, 1.40718, 1.34879, 2, 1, 1.33813, 1.42969, 1.33813, 2.06667, 1, 1.33399, 1.43858, 1.33399, 2.1, 1, 1.33196, 1.44296, 1.33196, 2.13333, 1, 1.32997, 1.44731, 1.32997, 2.16667, 1, 1.328, 1.4516, 1.328, 2.23333, 1, 1.32412, 1.46011, 1.32412, 2.26667, 1, 1.32215, 1.46446, 1.32215, 2.3, 1, 1.3201, 1.46902, 1.3201, 2.33333, 1, 1.31789, 1.47394, 1.31789, 2.36667, 1, 1.31513, 1.48015, 1.31513, 2.4, 1, 1.31213, 1.48692, 1.31213, 2.43333, 1, 1.30928, 1.49341, 1.30928, 2.46667, 1, 1.30676, 1.49917, 1.30676, 2.5, 1, 1.30461, 1.50412, 1.30461, 2.53333, 1, 1.303, 1.50782, 1.303, 2.56667, 1, 1.30186, 1.51047, 1.30186, 2.6, 1, 1.30119, 1.51203, 1.30119, 2.625, 1, 1.30093, 1.51263, 1.30093)
tracks/21/type = "position_3d"
tracks/21/imported = true
tracks/21/enabled = true
tracks/21/path = NodePath("Armature/Skeleton3D:BendyBone.004")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = PackedFloat32Array(0, 1, 0, -0.13, 0, 0.0333333, 1, 0, -0.126817, 0, 0.0666667, 1, 0, -0.119323, 0, 0.1, 1, 0, -0.107938, 0, 0.133333, 1, 0, -0.093063, 0, 0.166667, 1, 0, -0.0750768, 0, 0.2, 1, 0, -0.0536315, 0, 0.233333, 1, 0, -0.0300694, 0, 0.266667, 1, 0, -0.00468793, 0, 0.3, 1, 0, 0.0222239, 0, 0.333333, 1, 0, 0.0503853, 0, 0.366667, 1, 0, 0.0796777, 0, 0.4, 1, 0, 0.109433, 0, 0.433333, 1, -0.00309101, 0.13978, 0, 0.466667, 1, -0.0107145, 0.170892, 0, 0.5, 1, -0.0226622, 0.202988, 0, 0.533333, 1, -0.0367341, 0.236249, 0, 0.566667, 1, -0.0513811, 0.270322, 0, 0.6, 1, -0.0662398, 0.305177, 0, 0.633333, 1, -0.0810125, 0.340783, 0, 0.666667, 1, -0.0954672, 0.377111, 0, 0.733333, 1, -0.122116, 0.452198, 0, 1, 1, -0.188835, 0.769995, 0, 1.03333, 1, -0.192065, 0.809352, 0, 1.06667, 1, -0.194422, 0.847643, 0, 1.1, 1, -0.195976, 0.88438, 0, 1.13333, 1, -0.196779, 0.918956, 0, 1.16667, 1, -0.196864, 0.950647, 0, 1.2, 1, -0.195719, 0.976242, 0, 1.23333, 1, -0.192124, 0.994386, 0, 1.26667, 1, -0.165657, 1.00113, 0, 1.3, 1, -0.0940309, 0.9913, 0, 1.33333, 1, 0.0469018, 0.958522, 0, 1.4, 1, 0.441211, 0.771862, 0, 1.43333, 1, 0.595509, 0.66104, 0, 1.46667, 1, 0.704288, 0.552482, 0, 1.5, 1, 0.775291, 0.447059, 0, 1.53333, 1, 0.834305, 0.328675, 0, 1.56667, 1, 0.888071, 0.22584, 0, 1.6, 1, 0.930602, 0.153724, 0, 1.63333, 1, 0.957728, 0.115363, 0, 1.66667, 1, 0.967096, 0.101658, 0, 1.7, 1, 0.965158, 0.0940622, 0, 1.73333, 1, 0.963425, 0.0834157, 0, 1.76667, 1, 0.961826, 0.0705367, 0, 1.8, 1, 0.960312, 0.0560413, 0, 1.83333, 1, 0.958849, 0.0403444, 0, 1.86667, 1, 0.957427, 0.0235576, 0, 1.9, 1, 0.956024, 0.00621705, 0, 1.93333, 1, 0.954639, -0.0115387, 0, 2.06667, 1, 0.949285, -0.0841832, 0, 2.1, 1, 0.948004, -0.102076, 0, 2.13333, 1, 0.946746, -0.119629, 0, 2.16667, 1, 0.945513, -0.136729, 0, 2.2, 1, 0.944305, -0.15295, 0, 2.23333, 1, 0.943102, -0.168249, 0, 2.26667, 1, 0.941883, -0.182374, 0, 2.3, 1, 0.940615, -0.194993, 0, 2.33333, 1, 0.939255, -0.205701, 0, 2.36667, 1, 0.937563, -0.212257, 0, 2.4, 1, 0.935735, -0.216351, 0, 2.43333, 1, 0.933996, -0.219385, 0, 2.46667, 1, 0.932468, -0.222044, 0, 2.5, 1, 0.931167, -0.224303, 0, 2.53333, 1, 0.930201, -0.225979, 0, 2.56667, 1, 0.92951, -0.227178, 0, 2.625, 1, 0.928952, -0.228144, 0)
tracks/22/type = "rotation_3d"
tracks/22/imported = true
tracks/22/enabled = true
tracks/22/path = NodePath("Armature/Skeleton3D:BendyBone.004")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = PackedFloat32Array(0, 1, 0, 0, 0, 1, 0.4, 1, 0, 0, 0, 1, 0.433333, 1, 0, 0, 0.00698416, 0.999976, 0.466667, 1, 0, 0, 0.023069, 0.999734, 0.5, 1, 0, 0, 0.0454973, 0.998964, 0.533333, 1, 0, 0, 0.0674084, 0.997725, 0.566667, 1, 0, 0, 0.0875513, 0.99616, 0.6, 1, 0, 0, 0.105837, 0.994384, 0.633333, 1, 0, 0, 0.122294, 0.992494, 0.666667, 1, 0, 0, 0.137067, 0.990562, 0.7, 1, 0, 0, 0.149989, 0.988688, 0.733333, 1, 0, 0, 0.161791, 0.986825, 0.766667, 1, 0, 0, 0.172628, 0.984987, 0.8, 1, 0, 0, 0.182635, 0.983181, 0.833333, 1, 0, 0, 0.191933, 0.981408, 0.866667, 1, 0, 0, 0.200461, 0.979702, 0.9, 1, 0, 0, 0.208491, 0.978024, 0.933333, 1, 0, 0, 0.216057, 0.976381, 0.966667, 1, 0, 0, 0.223181, 0.974777, 1, 1, 0, 0, 0.229877, 0.97322, 1.03333, 1, 0, 0, 0.236004, 0.971752, 1.06667, 1, 0, 0, 0.241688, 0.970354, 1.1, 1, 0, 0, 0.246915, 0.969037, 1.13333, 1, 0, 0, 0.251664, 0.967815, 1.16667, 1, 0, 0, 0.255906, 0.966702, 1.2, 1, 0, 0, 0.259277, 0.965803, 1.23333, 1, 0, 0, 0.261166, 0.965294, 1.26667, 1, 0, 0, 0.251813, 0.967776, 1.3, 1, 0, 0, 0.216982, 0.976176, 1.33333, 1, 0, 0, 0.137357, 0.990522, 1.36667, 1, 0, 0, -0.00697695, 0.999976, 1.4, 1, 0, 0, -0.147691, 0.989034, 1.43333, 1, 0, 0, -0.26712, 0.963663, 1.46667, 1, 0, 0, -0.365312, 0.930885, 1.5, 1, 0, 0, -0.457751, 0.88908, 1.53333, 1, 0, 0, -0.552141, 0.833751, 1.56667, 1, 0, 0, -0.626095, 0.779747, 1.6, 1, 0, 0, -0.673412, 0.739268, 1.63333, 1, 0, 0, -0.695185, 0.71883, 1.66667, 1, 0, 0, -0.697143, 0.716933, 1.7, 1, 0, 0, -0.690773, 0.723072, 1.73333, 1, 0, 0, -0.685157, 0.728396, 1.76667, 1, 0, 0, -0.680047, 0.733168, 1.8, 1, 0, 0, -0.675263, 0.737577, 1.83333, 1, 0, 0, -0.670694, 0.741734, 1.86667, 1, 0, 0, -0.666314, 0.745672, 1.9, 1, 0, 0, -0.66203, 0.749478, 1.93333, 1, 0, 0, -0.657833, 0.753164, 1.96667, 1, 0, 0, -0.653718, 0.756738, 2, 1, 0, 0, -0.649683, 0.760205, 2.03333, 1, 0, 0, -0.645765, 0.763536, 2.06667, 1, 0, 0, -0.641937, 0.766758, 2.1, 1, 0, 0, -0.6382, 0.769871, 2.13333, 1, 0, 0, -0.634554, 0.772878, 2.16667, 1, 0, 0, -0.630998, 0.775784, 2.2, 1, 0, 0, -0.627543, 0.778582, 2.26667, 1, 0, 0, -0.620666, 0.784075, 2.3, 1, 0, 0, -0.617093, 0.78689, 2.33333, 1, 0, 0, -0.613277, 0.789868, 2.36667, 1, 0, 0, -0.608572, 0.793499, 2.4, 1, 0, 0, -0.60352, 0.797348, 2.43333, 1, 0, 0, -0.598739, 0.800944, 2.46667, 1, 0, 0, -0.59456, 0.804051, 2.5, 1, 0, 0, -0.591021, 0.806656, 2.53333, 1, 0, 0, -0.588409, 0.808563, 2.56667, 1, 0, 0, -0.586541, 0.809919, 2.6, 1, 0, 0, -0.585454, 0.810706, 2.625, 1, 0, 0, -0.585037, 0.811006)
tracks/23/type = "scale_3d"
tracks/23/imported = true
tracks/23/enabled = true
tracks/23/path = NodePath("Armature/Skeleton3D:BendyBone.004")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = PackedFloat32Array(0, 1, 1.83412, 0.1, 1.83412, 0.0333333, 1, 1.82192, 0.101347, 1.82192, 0.0666667, 1, 1.79253, 0.104739, 1.79253, 0.1, 1, 1.74767, 0.11026, 1.74767, 0.133333, 1, 1.68938, 0.118024, 1.68938, 0.166667, 1, 1.62004, 0.128176, 1.62004, 0.2, 1, 1.54115, 0.141898, 1.54115, 0.233333, 1, 1.45882, 0.158644, 1.45882, 0.3, 1, 1.2917, 0.202263, 1.2917, 0.333333, 1, 1.21019, 0.229693, 1.21019, 0.366667, 1, 1.13312, 0.262854, 1.13312, 0.4, 1, 1.06019, 0.30079, 1.06019, 0.433333, 1, 0.997274, 0.342609, 0.997273, 0.466667, 1, 0.948706, 0.387367, 0.948706, 0.5, 1, 0.917307, 0.434072, 0.917307, 0.533333, 1, 0.902568, 0.483355, 0.902568, 0.566667, 1, 0.897074, 0.534345, 0.897074, 0.6, 1, 0.899198, 0.586851, 0.899198, 0.633333, 1, 0.9075, 0.640677, 0.9075, 0.666667, 1, 0.920719, 0.69563, 0.920719, 0.7, 1, 0.938495, 0.751701, 0.938495, 0.733333, 1, 0.958485, 0.808332, 0.958485, 0.766667, 1, 0.980031, 0.865338, 0.980031, 0.866667, 1, 1.04801, 1.03651, 1.04801, 0.9, 1, 1.06973, 1.09266, 1.06973, 0.933333, 1, 1.09028, 1.14793, 1.09028, 0.966667, 1, 1.10937, 1.20203, 1.10937, 1, 1, 1.12673, 1.25469, 1.12673, 1.03333, 1, 1.14132, 1.30468, 1.14132, 1.06667, 1, 1.15358, 1.35199, 1.15358, 1.1, 1, 1.16339, 1.396, 1.16339, 1.13333, 1, 1.17069, 1.43595, 1.17069, 1.16667, 1, 1.17546, 1.47097, 1.17546, 1.2, 1, 1.17713, 1.49667, 1.17714, 1.23333, 1, 1.17734, 1.51156, 1.17734, 1.26667, 1, 1.18156, 1.50609, 1.18156, 1.3, 1, 1.19475, 1.47346, 1.19475, 1.33333, 1, 1.22127, 1.40965, 1.22127, 1.36667, 1, 1.24627, 1.35409, 1.24627, 1.4, 1, 1.25118, 1.34307, 1.25118, 1.43333, 1, 1.2443, 1.35809, 1.2443, 1.46667, 1, 1.23536, 1.37769, 1.23536, 1.5, 1, 1.23546, 1.37746, 1.23546, 1.53333, 1, 1.24502, 1.35644, 1.24502, 1.56667, 1, 1.2477, 1.35056, 1.2477, 1.6, 1, 1.24444, 1.35769, 1.24444, 1.63333, 1, 1.23807, 1.37167, 1.23807, 1.66667, 1, 1.23329, 1.38231, 1.23329, 1.7, 1, 1.23078, 1.38796, 1.23078, 1.73333, 1, 1.2285, 1.39311, 1.2285, 1.76667, 1, 1.22637, 1.39795, 1.22637, 1.8, 1, 1.22433, 1.40261, 1.22433, 1.83333, 1, 1.22234, 1.40718, 1.22234, 2, 1, 1.21268, 1.42969, 1.21268, 2.06667, 1, 1.20893, 1.43858, 1.20893, 2.1, 1, 1.20709, 1.44296, 1.20709, 2.13333, 1, 1.20528, 1.44731, 1.20528, 2.16667, 1, 1.2035, 1.4516, 1.2035, 2.23333, 1, 1.19999, 1.46011, 1.19999, 2.26667, 1, 1.1982, 1.46446, 1.1982, 2.3, 1, 1.19634, 1.46902, 1.19634, 2.33333, 1, 1.19434, 1.47394, 1.19434, 2.36667, 1, 1.19184, 1.48015, 1.19184, 2.4, 1, 1.18912, 1.48692, 1.18912, 2.43333, 1, 1.18653, 1.49341, 1.18653, 2.46667, 1, 1.18425, 1.49917, 1.18425, 2.5, 1, 1.1823, 1.50412, 1.1823, 2.53333, 1, 1.18085, 1.50782, 1.18085, 2.56667, 1, 1.17981, 1.51047, 1.17981, 2.6, 1, 1.1792, 1.51203, 1.1792, 2.625, 1, 1.17897, 1.51263, 1.17897)
tracks/24/type = "position_3d"
tracks/24/imported = true
tracks/24/enabled = true
tracks/24/path = NodePath("Armature/Skeleton3D:BendyBone.003")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = PackedFloat32Array(0, 1, 0, -0.12, 0, 0.0333333, 1, 0, -0.116683, 0, 0.0666667, 1, 0, -0.108849, 0, 0.1, 1, 0, -0.0969117, 0, 0.133333, 1, 0, -0.0812605, 0, 0.166667, 1, 0, -0.0622591, 0, 0.2, 1, 0, -0.0394417, 0, 0.233333, 1, 0, -0.0142051, 0, 0.266667, 1, 0, 0.0131795, 0, 0.3, 1, 0, 0.0424502, 0, 0.333333, 1, 0, 0.0733546, 0, 0.366667, 1, 0, 0.105963, 0, 0.4, 1, 0, 0.139512, 0, 0.433333, 1, -0.00356848, 0.174034, 0, 0.466667, 1, -0.0123969, 0.209587, 0, 0.5, 1, -0.0262997, 0.246258, 0, 0.533333, 1, -0.0428454, 0.284224, 0, 0.566667, 1, -0.0602366, 0.323069, 0, 0.6, 1, -0.0780748, 0.362744, 0, 0.633333, 1, -0.0960357, 0.4032, 0, 0.666667, 1, -0.11387, 0.444393, 0, 0.766667, 1, -0.164005, 0.57236, 0, 0.933333, 1, -0.230983, 0.793776, 0, 0.966667, 1, -0.241477, 0.838168, 0, 1, 1, -0.251029, 0.882186, 0, 1.03333, 1, -0.259353, 0.925183, 0, 1.06667, 1, -0.266788, 0.966864, 0, 1.1, 1, -0.273339, 1.0067, 0, 1.13333, 1, -0.278981, 1.04404, 0, 1.16667, 1, -0.283652, 1.0781, 0, 1.2, 1, -0.28644, 1.10535, 0, 1.23333, 1, -0.285728, 1.12445, 0, 1.26667, 1, -0.258147, 1.13241, 0, 1.3, 1, -0.177383, 1.12537, 0, 1.33333, 1, -0.0141839, 1.09859, 0, 1.4, 1, 0.465344, 0.910641, 0, 1.43333, 1, 0.655282, 0.787945, 0, 1.46667, 1, 0.791833, 0.661506, 0, 1.5, 1, 0.885619, 0.531963, 0, 1.53333, 1, 0.959802, 0.38337, 0, 1.56667, 1, 1.02306, 0.254198, 0, 1.6, 1, 1.07122, 0.16322, 0, 1.63333, 1, 1.10146, 0.114902, 0, 1.66667, 1, 1.11237, 0.0994149, 0, 1.7, 1, 1.11102, 0.0940512, 0, 1.73333, 1, 1.1098, 0.0853701, 0, 1.76667, 1, 1.10866, 0.0742786, 0, 1.8, 1, 1.10757, 0.0614571, 0, 1.83333, 1, 1.10652, 0.0473596, 0, 1.86667, 1, 1.10548, 0.03211, 0, 1.9, 1, 1.10445, 0.016274, 0, 1.93333, 1, 1.10343, -6.49691e-06, 0, 1.96667, 1, 1.10242, -0.0166041, 0, 2.06667, 1, 1.09943, -0.0670388, 0, 2.1, 1, 1.09845, -0.0836055, 0, 2.13333, 1, 1.0975, -0.0998628, 0, 2.16667, 1, 1.09655, -0.115696, 0, 2.2, 1, 1.09562, -0.130682, 0, 2.23333, 1, 1.09469, -0.144754, 0, 2.26667, 1, 1.09375, -0.157638, 0, 2.3, 1, 1.09276, -0.168972, 0, 2.33333, 1, 1.09169, -0.178302, 0, 2.36667, 1, 1.09036, -0.183151, 0, 2.4, 1, 1.08891, -0.185405, 0, 2.43333, 1, 1.08752, -0.186691, 0, 2.46667, 1, 1.0863, -0.187817, 0, 2.5, 1, 1.08525, -0.188772, 0, 2.53333, 1, 1.08447, -0.189482, 0, 2.6, 1, 1.08358, -0.190284, 0, 2.625, 1, 1.08346, -0.190397, 0)
tracks/25/type = "rotation_3d"
tracks/25/imported = true
tracks/25/enabled = true
tracks/25/path = NodePath("Armature/Skeleton3D:BendyBone.003")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = PackedFloat32Array(0, 1, 0, 0, 0, 1, 0.4, 1, 0, 0, 0, 1, 0.433333, 1, 0, 0, 0.00562928, 0.999984, 0.466667, 1, 0, 0, 0.0188093, 0.999823, 0.5, 1, 0, 0, 0.0377501, 0.999287, 0.533333, 1, 0, 0, 0.0573121, 0.998356, 0.566667, 1, 0, 0, 0.076198, 0.997093, 0.6, 1, 0, 0, 0.094276, 0.995546, 0.633333, 1, 0, 0, 0.111509, 0.993764, 0.666667, 1, 0, 0, 0.127953, 0.99178, 0.7, 1, 0, 0, 0.143571, 0.98964, 0.733333, 1, 0, 0, 0.158721, 0.987324, 0.766667, 1, 0, 0, 0.173494, 0.984835, 0.8, 1, 0, 0, 0.187969, 0.982175, 0.833333, 1, 0, 0, 0.202214, 0.979341, 0.933333, 1, 0, 0, 0.243971, 0.969783, 1, 1, 0, 0, 0.271172, 0.962531, 1.03333, 1, 0, 0, 0.284482, 0.958681, 1.06667, 1, 0, 0, 0.297548, 0.954707, 1.1, 1, 0, 0, 0.310309, 0.950636, 1.13333, 1, 0, 0, 0.322688, 0.946506, 1.16667, 1, 0, 0, 0.33459, 0.942364, 1.2, 1, 0, 0, 0.345502, 0.938418, 1.23333, 1, 0, 0, 0.354777, 0.934951, 1.26667, 1, 0, 0, 0.355222, 0.934782, 1.3, 1, 0, 0, 0.335602, 0.942004, 1.33333, 1, 0, 0, 0.279975, 0.960007, 1.36667, 1, 0, 0, 0.144222, 0.989545, 1.4, 1, 0, 0, -0.0101508, 0.999949, 1.43333, 1, 0, 0, -0.158438, 0.987369, 1.5, 1, 0, 0, -0.422389, 0.906415, 1.53333, 1, 0, 0, -0.541971, 0.840397, 1.56667, 1, 0, 0, -0.632459, 0.774594, 1.6, 1, 0, 0, -0.690935, 0.722917, 1.63333, 1, 0, 0, -0.720512, 0.693443, 1.66667, 1, 0, 0, -0.72725, 0.686373, 1.7, 1, 0, 0, -0.722844, 0.691011, 1.73333, 1, 0, 0, -0.718969, 0.695042, 1.76667, 1, 0, 0, -0.715451, 0.698663, 1.8, 1, 0, 0, -0.712163, 0.702015, 1.83333, 1, 0, 0, -0.709026, 0.705182, 1.86667, 1, 0, 0, -0.706024, 0.708188, 1.9, 1, 0, 0, -0.70309, 0.711101, 1.93333, 1, 0, 0, -0.700219, 0.713928, 1.96667, 1, 0, 0, -0.697406, 0.716676, 2, 1, 0, 0, -0.69465, 0.719348, 2.03333, 1, 0, 0, -0.691976, 0.721921, 2.06667, 1, 0, 0, -0.689363, 0.724416, 2.1, 1, 0, 0, -0.686814, 0.726833, 2.13333, 1, 0, 0, -0.684328, 0.729174, 2.16667, 1, 0, 0, -0.681904, 0.731442, 2.2, 1, 0, 0, -0.679549, 0.733631, 2.26667, 1, 0, 0, -0.674859, 0.737946, 2.3, 1, 0, 0, -0.672423, 0.740167, 2.33333, 1, 0, 0, -0.669821, 0.742523, 2.36667, 1, 0, 0, -0.66661, 0.745407, 2.4, 1, 0, 0, -0.66316, 0.748478, 2.43333, 1, 0, 0, -0.659893, 0.751359, 2.46667, 1, 0, 0, -0.657036, 0.753859, 2.5, 1, 0, 0, -0.654615, 0.755962, 2.53333, 1, 0, 0, -0.652825, 0.757509, 2.56667, 1, 0, 0, -0.651545, 0.75861, 2.625, 1, 0, 0, -0.650515, 0.759494)
tracks/26/type = "scale_3d"
tracks/26/imported = true
tracks/26/enabled = true
tracks/26/path = NodePath("Armature/Skeleton3D:BendyBone.003")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = PackedFloat32Array(0, 1, 1.64438, 0.1, 1.64438, 0.0333333, 1, 1.63345, 0.101347, 1.63345, 0.0666667, 1, 1.6071, 0.104739, 1.6071, 0.1, 1, 1.56687, 0.11026, 1.56687, 0.133333, 1, 1.51462, 0.118024, 1.51462, 0.166667, 1, 1.45245, 0.128176, 1.45245, 0.2, 1, 1.38172, 0.141898, 1.38172, 0.233333, 1, 1.30791, 0.158644, 1.30791, 0.3, 1, 1.15808, 0.202263, 1.15808, 0.333333, 1, 1.085, 0.229693, 1.085, 0.366667, 1, 1.0159, 0.262854, 1.0159, 0.4, 1, 0.950517, 0.30079, 0.950517, 0.433333, 1, 0.894107, 0.342609, 0.894107, 0.466667, 1, 0.850564, 0.387367, 0.850564, 0.5, 1, 0.822413, 0.434072, 0.822413, 0.533333, 1, 0.809199, 0.483356, 0.809199, 0.566667, 1, 0.804273, 0.534345, 0.804273, 0.6, 1, 0.806178, 0.586851, 0.806178, 0.633333, 1, 0.813621, 0.640677, 0.813621, 0.666667, 1, 0.825472, 0.69563, 0.825472, 0.7, 1, 0.84141, 0.751701, 0.84141, 0.733333, 1, 0.859331, 0.808332, 0.859331, 0.766667, 1, 0.878648, 0.865338, 0.878648, 0.866667, 1, 0.939595, 1.03651, 0.939595, 0.9, 1, 0.959067, 1.09266, 0.959067, 0.933333, 1, 0.977495, 1.14793, 0.977495, 0.966667, 1, 0.99461, 1.20203, 0.99461, 1, 1, 1.01017, 1.25469, 1.01017, 1.03333, 1, 1.02325, 1.30468, 1.02325, 1.06667, 1, 1.03424, 1.35199, 1.03424, 1.1, 1, 1.04304, 1.396, 1.04304, 1.13333, 1, 1.04959, 1.43595, 1.04959, 1.16667, 1, 1.05386, 1.47097, 1.05386, 1.2, 1, 1.05536, 1.49667, 1.05536, 1.23333, 1, 1.05554, 1.51156, 1.05554, 1.26667, 1, 1.05933, 1.50609, 1.05933, 1.3, 1, 1.07115, 1.47346, 1.07115, 1.33333, 1, 1.09493, 1.40965, 1.09493, 1.36667, 1, 1.11734, 1.35409, 1.11734, 1.4, 1, 1.12174, 1.34307, 1.12174, 1.43333, 1, 1.11558, 1.35809, 1.11558, 1.46667, 1, 1.10756, 1.37769, 1.10756, 1.5, 1, 1.10765, 1.37746, 1.10765, 1.53333, 1, 1.11623, 1.35644, 1.11623, 1.56667, 1, 1.11863, 1.35056, 1.11863, 1.6, 1, 1.11571, 1.35769, 1.11571, 1.63333, 1, 1.11, 1.37167, 1.11, 1.66667, 1, 1.10571, 1.38231, 1.10571, 1.7, 1, 1.10346, 1.38796, 1.10346, 1.73333, 1, 1.10142, 1.39311, 1.10142, 1.76667, 1, 1.09951, 1.39795, 1.09951, 1.8, 1, 1.09768, 1.40261, 1.09768, 1.83333, 1, 1.09589, 1.40718, 1.09589, 2, 1, 1.08723, 1.42969, 1.08723, 2.06667, 1, 1.08387, 1.43858, 1.08387, 2.1, 1, 1.08222, 1.44296, 1.08222, 2.13333, 1, 1.0806, 1.44731, 1.0806, 2.16667, 1, 1.079, 1.4516, 1.079, 2.23333, 1, 1.07585, 1.46011, 1.07585, 2.26667, 1, 1.07425, 1.46446, 1.07425, 2.3, 1, 1.07258, 1.46902, 1.07258, 2.33333, 1, 1.07079, 1.47394, 1.07079, 2.36667, 1, 1.06854, 1.48015, 1.06854, 2.4, 1, 1.06611, 1.48692, 1.06611, 2.43333, 1, 1.06379, 1.49341, 1.06379, 2.46667, 1, 1.06174, 1.49917, 1.06174, 2.5, 1, 1.05999, 1.50412, 1.05999, 2.53333, 1, 1.05869, 1.50782, 1.05869, 2.56667, 1, 1.05776, 1.51047, 1.05776, 2.6, 1, 1.05722, 1.51203, 1.05722, 2.625, 1, 1.05701, 1.51263, 1.05701)
tracks/27/type = "position_3d"
tracks/27/imported = true
tracks/27/enabled = true
tracks/27/path = NodePath("Armature/Skeleton3D:BendyBone.002")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = PackedFloat32Array(0, 1, 0, -0.11, 0, 0.0333333, 1, 0, -0.106548, 0, 0.0666667, 1, 0, -0.0983746, 0, 0.1, 1, 0, -0.0858856, 0, 0.133333, 1, 0, -0.0694581, 0, 0.166667, 1, 0, -0.0494416, 0, 0.2, 1, 0, -0.025252, 0, 0.233333, 1, 0, 0.00165931, 0, 0.266667, 1, 0, 0.031047, 0, 0.3, 1, 0, 0.0626764, 0, 0.333333, 1, 0, 0.0963239, 0, 0.366667, 1, 0, 0.132248, 0, 0.4, 1, 0, 0.169591, 0, 0.433333, 1, -0.00392246, 0.208292, 0, 0.466667, 1, -0.0136671, 0.248302, 0, 0.5, 1, -0.0291108, 0.289589, 0, 0.533333, 1, -0.0477382, 0.33234, 0, 0.566667, 1, -0.067564, 0.37605, 0, 0.6, 1, -0.0881802, 0.420637, 0, 0.633333, 1, -0.109258, 0.466019, 0, 0.666667, 1, -0.130548, 0.51212, 0, 0.9, 1, -0.271206, 0.847005, 0, 0.933333, 1, -0.28907, 0.894799, 0, 0.966667, 1, -0.306271, 0.942044, 0, 1, 1, -0.322781, 0.988517, 0, 1.03333, 1, -0.338313, 1.03337, 0, 1.06667, 1, -0.353046, 1.07641, 0, 1.1, 1, -0.366893, 1.11707, 0, 1.13333, 1, -0.379716, 1.15467, 0, 1.16667, 1, -0.391318, 1.1884, 0, 1.2, 1, -0.400275, 1.21447, 0, 1.23333, 1, -0.404542, 1.23179, 0, 1.26667, 1, -0.378758, 1.23829, 0, 1.3, 1, -0.29415, 1.23171, 0, 1.33333, 1, -0.11853, 1.20956, 0, 1.36667, 1, 0.160629, 1.14584, 0, 1.4, 1, 0.438249, 1.04703, 0, 1.43333, 1, 0.675429, 0.92454, 0, 1.46667, 1, 0.856077, 0.784431, 0, 1.5, 1, 0.986906, 0.627371, 0, 1.53333, 1, 1.08261, 0.442458, 0, 1.56667, 1, 1.15785, 0.281448, 0, 1.6, 1, 1.21194, 0.166807, 0, 1.63333, 1, 1.24482, 0.10473, 0, 1.66667, 1, 1.25697, 0.0851395, 0, 1.7, 1, 1.25632, 0.081155, 0, 1.73333, 1, 1.25573, 0.073684, 0, 1.76667, 1, 1.25517, 0.0636899, 0, 1.8, 1, 1.25463, 0.0518936, 0, 1.83333, 1, 1.25411, 0.0387735, 0, 1.86667, 1, 1.25359, 0.0244611, 0, 1.9, 1, 1.25308, 0.00954083, 0, 1.93333, 1, 1.25256, -0.00584318, 0, 1.96667, 1, 1.25205, -0.0215619, 0, 2.06667, 1, 1.25053, -0.0694754, 0, 2.1, 1, 1.25003, -0.0852407, 0, 2.13333, 1, 1.24954, -0.100715, 0, 2.16667, 1, 1.24905, -0.115784, 0, 2.2, 1, 1.24857, -0.130024, 0, 2.23333, 1, 1.24808, -0.143356, 0, 2.26667, 1, 1.24759, -0.155493, 0, 2.3, 1, 1.24707, -0.166052, 0, 2.33333, 1, 1.24651, -0.174552, 0, 2.36667, 1, 1.24581, -0.178372, 0, 2.4, 1, 1.24504, -0.179517, 0, 2.46667, 1, 1.24366, -0.17995, 0, 2.53333, 1, 1.24268, -0.180245, 0, 2.625, 1, 1.24214, -0.180405, 0)
tracks/28/type = "rotation_3d"
tracks/28/imported = true
tracks/28/enabled = true
tracks/28/path = NodePath("Armature/Skeleton3D:BendyBone.002")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = PackedFloat32Array(0, 1, 0, 0, 0, 1, 0.4, 1, 0, 0, 0, 1, 0.433333, 1, 0, 0, 0.00369843, 0.999993, 0.466667, 1, 0, 0, 0.0126824, 0.99992, 0.5, 1, 0, 0, 0.0264262, 0.999651, 0.533333, 1, 0, 0, 0.0422396, 0.999108, 0.566667, 1, 0, 0, 0.0588149, 0.998269, 0.6, 1, 0, 0, 0.0759826, 0.997109, 0.633333, 1, 0, 0, 0.0936297, 0.995607, 0.666667, 1, 0, 0, 0.1117, 0.993742, 0.7, 1, 0, 0, 0.13032, 0.991472, 0.733333, 1, 0, 0, 0.149332, 0.988787, 0.766667, 1, 0, 0, 0.168728, 0.985663, 0.8, 1, 0, 0, 0.188494, 0.982074, 0.833333, 1, 0, 0, 0.208614, 0.977998, 0.866667, 1, 0, 0, 0.229198, 0.97338, 0.9, 1, 0, 0, 0.250061, 0.96823, 0.933333, 1, 0, 0, 0.271166, 0.962533, 0.966667, 1, 0, 0, 0.292463, 0.956277, 1, 1, 0, 0, 0.313898, 0.949457, 1.13333, 1, 0, 0, 0.399193, 0.916867, 1.16667, 1, 0, 0, 0.419677, 0.907673, 1.2, 1, 0, 0, 0.439054, 0.898461, 1.23333, 1, 0, 0, 0.456702, 0.88962, 1.26667, 1, 0, 0, 0.469092, 0.883149, 1.3, 1, 0, 0, 0.471063, 0.8821, 1.33333, 1, 0, 0, 0.455613, 0.890178, 1.36667, 1, 0, 0, 0.367376, 0.930073, 1.4, 1, 0, 0, 0.213025, 0.977047, 1.43333, 1, 0, 0, 0.0213711, 0.999772, 1.46667, 1, 0, 0, -0.179157, 0.983821, 1.5, 1, 0, 0, -0.366775, 0.93031, 1.53333, 1, 0, 0, -0.520122, 0.854092, 1.56667, 1, 0, 0, -0.63177, 0.775156, 1.6, 1, 0, 0, -0.703749, 0.710448, 1.63333, 1, 0, 0, -0.742266, 0.670106, 1.66667, 1, 0, 0, -0.754262, 0.656574, 1.7, 1, 0, 0, -0.752028, 0.659132, 1.73333, 1, 0, 0, -0.750077, 0.66135, 1.76667, 1, 0, 0, -0.748318, 0.663341, 1.8, 1, 0, 0, -0.746683, 0.66518, 1.83333, 1, 0, 0, -0.745132, 0.666917, 1.86667, 1, 0, 0, -0.743658, 0.668561, 1.93333, 1, 0, 0, -0.740828, 0.671695, 1.96667, 1, 0, 0, -0.739467, 0.673193, 2, 1, 0, 0, -0.738139, 0.674648, 2.03333, 1, 0, 0, -0.736859, 0.676047, 2.1, 1, 0, 0, -0.734402, 0.678715, 2.16667, 1, 0, 0, -0.732085, 0.681214, 2.2, 1, 0, 0, -0.730981, 0.682398, 2.23333, 1, 0, 0, -0.72989, 0.683564, 2.26667, 1, 0, 0, -0.728795, 0.684732, 2.3, 1, 0, 0, -0.727665, 0.685932, 2.33333, 1, 0, 0, -0.726463, 0.687205, 2.36667, 1, 0, 0, -0.724989, 0.68876, 2.4, 1, 0, 0, -0.723413, 0.690416, 2.43333, 1, 0, 0, -0.721927, 0.691969, 2.46667, 1, 0, 0, -0.720634, 0.693315, 2.5, 1, 0, 0, -0.719543, 0.694447, 2.53333, 1, 0, 0, -0.718739, 0.69528, 2.6, 1, 0, 0, -0.717832, 0.696216, 2.625, 1, 0, 0, -0.717704, 0.696348)
tracks/29/type = "scale_3d"
tracks/29/imported = true
tracks/29/enabled = true
tracks/29/path = NodePath("Armature/Skeleton3D:BendyBone.002")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = PackedFloat32Array(0, 1, 1.45465, 0.1, 1.45465, 0.0333333, 1, 1.44497, 0.101347, 1.44497, 0.0666667, 1, 1.42166, 0.104739, 1.42166, 0.1, 1, 1.38608, 0.11026, 1.38608, 0.133333, 1, 1.33986, 0.118024, 1.33986, 0.166667, 1, 1.28486, 0.128176, 1.28486, 0.2, 1, 1.22229, 0.141898, 1.22229, 0.233333, 1, 1.157, 0.158644, 1.157, 0.333333, 1, 0.959807, 0.229693, 0.959807, 0.366667, 1, 0.898682, 0.262854, 0.898682, 0.4, 1, 0.840842, 0.30079, 0.840842, 0.433333, 1, 0.790941, 0.342609, 0.790941, 0.466667, 1, 0.752422, 0.387367, 0.752422, 0.5, 1, 0.72752, 0.434072, 0.72752, 0.533333, 1, 0.71583, 0.483355, 0.71583, 0.566667, 1, 0.711472, 0.534345, 0.711472, 0.6, 1, 0.713157, 0.586851, 0.713157, 0.633333, 1, 0.719742, 0.640677, 0.719742, 0.666667, 1, 0.730225, 0.69563, 0.730225, 0.7, 1, 0.744324, 0.751701, 0.744324, 0.733333, 1, 0.760178, 0.808332, 0.760178, 0.866667, 1, 0.831181, 1.03651, 0.831181, 0.9, 1, 0.848406, 1.09266, 0.848406, 0.933333, 1, 0.864707, 1.14793, 0.864707, 0.966667, 1, 0.879848, 1.20203, 0.879848, 1, 1, 0.893612, 1.25469, 0.893612, 1.03333, 1, 0.905184, 1.30468, 0.905184, 1.06667, 1, 0.914907, 1.35199, 0.914907, 1.1, 1, 0.92269, 1.396, 0.92269, 1.13333, 1, 0.928481, 1.43595, 0.928481, 1.16667, 1, 0.932262, 1.47097, 0.932262, 1.2, 1, 0.93359, 1.49667, 0.93359, 1.23333, 1, 0.933749, 1.51156, 0.933749, 1.26667, 1, 0.937101, 1.50609, 0.937101, 1.3, 1, 0.94756, 1.47346, 0.94756, 1.33333, 1, 0.968596, 1.40965, 0.968596, 1.36667, 1, 0.988418, 1.35409, 0.988418, 1.4, 1, 0.992313, 1.34307, 0.992313, 1.43333, 1, 0.986862, 1.35809, 0.986862, 1.46667, 1, 0.979766, 1.37769, 0.979766, 1.5, 1, 0.979846, 1.37746, 0.979846, 1.53333, 1, 0.987432, 1.35644, 0.987432, 1.56667, 1, 0.989557, 1.35056, 0.989557, 1.6, 1, 0.986973, 1.35769, 0.986973, 1.63333, 1, 0.981919, 1.37167, 0.981919, 1.66667, 1, 0.978128, 1.38231, 0.978127, 1.7, 1, 0.976136, 1.38796, 0.976136, 1.73333, 1, 0.974329, 1.39311, 0.974329, 1.76667, 1, 0.972641, 1.39795, 0.972641, 1.8, 1, 0.971023, 1.40261, 0.971023, 1.83333, 1, 0.969446, 1.40718, 0.969446, 2, 1, 0.961783, 1.42969, 0.961783, 2.06667, 1, 0.958808, 1.43858, 0.958808, 2.1, 1, 0.95735, 1.44296, 0.95735, 2.13333, 1, 0.955913, 1.44731, 0.955913, 2.16667, 1, 0.954498, 1.4516, 0.954498, 2.23333, 1, 0.951713, 1.46011, 0.951713, 2.26667, 1, 0.950298, 1.46446, 0.950298, 2.3, 1, 0.948822, 1.46902, 0.948822, 2.33333, 1, 0.947234, 1.47394, 0.947234, 2.36667, 1, 0.945249, 1.48015, 0.945249, 2.4, 1, 0.943096, 1.48692, 0.943096, 2.43333, 1, 0.941042, 1.49341, 0.941042, 2.46667, 1, 0.939231, 1.49917, 0.939231, 2.5, 1, 0.937685, 1.50412, 0.937685, 2.53333, 1, 0.936535, 1.50782, 0.936535, 2.56667, 1, 0.93571, 1.51047, 0.93571, 2.6, 1, 0.935229, 1.51203, 0.935229, 2.625, 1, 0.935044, 1.51263, 0.935044)
tracks/30/type = "position_3d"
tracks/30/imported = true
tracks/30/enabled = true
tracks/30/path = NodePath("Armature/Skeleton3D:Hand")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = PackedFloat32Array(0, 1, 0, -0.1, 0, 0.0333333, 1, 0, -0.0964133, 0, 0.0666667, 1, 0, -0.0879007, 0, 0.1, 1, 0, -0.0748595, 0, 0.133333, 1, 0, -0.0576556, 0, 0.166667, 1, 0, -0.036624, 0, 0.2, 1, 0, -0.0110621, 0, 0.233333, 1, 0, 0.0175237, 0, 0.266667, 1, 0, 0.0489144, 0, 0.3, 1, 0, 0.0829027, 0, 0.333333, 1, 0, 0.119293, 0, 0.366667, 1, 0, 0.158534, 0, 0.4, 1, 0, 0.19967, 0, 0.433333, 1, -0.00411022, 0.242552, 0, 0.466667, 1, -0.0143773, 0.287036, 0, 0.5, 1, -0.0307845, 0.332979, 0, 0.533333, 1, -0.0509255, 0.380602, 0, 0.566667, 1, -0.0727095, 0.429288, 0, 0.6, 1, -0.0957557, 0.478912, 0, 0.633333, 1, -0.119761, 0.529344, 0, 0.666667, 1, -0.1445, 0.580456, 0, 0.866667, 1, -0.300497, 0.89198, 0, 0.9, 1, -0.326695, 0.942659, 0, 0.933333, 1, -0.352715, 0.992379, 0, 0.966667, 1, -0.378477, 1.04088, 0, 1, 1, -0.403894, 1.0879, 0, 1.03333, 1, -0.428633, 1.13225, 0, 1.06667, 1, -0.452638, 1.17396, 0, 1.1, 1, -0.475684, 1.21244, 0, 1.13333, 1, -0.497469, 1.24702, 0, 1.16667, 1, -0.517609, 1.27689, 0, 1.2, 1, -0.534017, 1.29816, 0, 1.23333, 1, -0.544284, 1.31016, 0, 1.26667, 1, -0.521843, 1.31135, 0, 1.3, 1, -0.436831, 1.30041, 0, 1.33333, 1, -0.256092, 1.27625, 0, 1.36667, 1, 0.0406518, 1.22887, 0, 1.4, 1, 0.353793, 1.15771, 0, 1.43333, 1, 0.643318, 1.05641, 0, 1.46667, 1, 0.885636, 0.918252, 0, 1.5, 1, 1.07357, 0.73616, 0, 1.53333, 1, 1.20049, 0.508981, 0, 1.56667, 1, 1.2917, 0.310013, 0, 1.6, 1, 1.35244, 0.166122, 0, 1.63333, 1, 1.38727, 0.0858788, 0, 1.66667, 1, 1.4, 0.0595614, 0, 1.7, 1, 1.4, 0.0559721, 0, 1.73333, 1, 1.4, 0.0488418, 0, 1.76667, 1, 1.4, 0.0391516, 0, 1.8, 1, 1.4, 0.0276354, 0, 1.83333, 1, 1.4, 0.0147792, 0, 1.86667, 1, 1.4, 0.000714409, 0, 1.9, 1, 1.4, -0.0139658, 0, 1.93333, 1, 1.4, -0.0291179, 0, 1.96667, 1, 1.4, -0.0446123, 0, 2.06667, 1, 1.4, -0.0918962, 0, 2.1, 1, 1.4, -0.107466, 0, 2.13333, 1, 1.4, -0.122752, 0, 2.16667, 1, 1.4, -0.137637, 0, 2.2, 1, 1.4, -0.151702, 0, 2.23333, 1, 1.4, -0.16486, 0, 2.26667, 1, 1.4, -0.176823, 0, 2.3, 1, 1.4, -0.187203, 0, 2.33333, 1, 1.4, -0.195513, 0, 2.36667, 1, 1.4, -0.199103, 0, 2.625, 1, 1.4, -0.2, 0)
tracks/31/type = "rotation_3d"
tracks/31/imported = true
tracks/31/enabled = true
tracks/31/path = NodePath("Armature/Skeleton3D:Hand")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = PackedFloat32Array(0, 1, 1.05367e-07, -4.21469e-08, -0.795242, 0.606292, 0.0333333, 1, 7.16497e-08, -4.21469e-08, -0.793722, 0.608281, 0.0666667, 1, 7.58651e-08, -6.74356e-08, -0.790005, 0.613101, 0.1, 1, 7.58662e-08, -6.74366e-08, -0.784145, 0.620578, 0.133333, 1, 5.90073e-08, -4.21482e-08, -0.776174, 0.630519, 0.166667, 1, 4.21468e-08, -4.21468e-08, -0.766106, 0.642714, 0.2, 1, 4.21489e-08, -8.43017e-09, -0.753384, 0.657581, 0.233333, 1, 4.2151e-08, 0, -0.738649, 0.67409, 0.266667, 1, 4.21522e-08, -1.68613e-08, -0.721902, 0.691995, 0.3, 1, 5.11194e-08, -4.63668e-08, -0.703145, 0.711046, 0.333333, 1, 8.69785e-08, -6.32203e-08, -0.68239, 0.730988, 0.366667, 1, 8.88519e-08, -4.63677e-08, -0.659275, 0.751902, 0.4, 1, 7.08943e-08, -4.21556e-08, -0.634333, 0.77306, 0.433333, 1, 5.13272e-08, -4.21579e-08, -0.60667, 0.794954, 0.466667, 1, 4.80723e-08, -5.05899e-08, -0.575391, 0.817878, 0.5, 1, 7.87207e-08, -8.42937e-08, -0.539614, 0.841913, 0.533333, 1, 4.95064e-08, -5.05932e-08, -0.498624, 0.866819, 0.566667, 1, 5.77041e-08, -6.74678e-08, -0.453775, 0.891116, 0.6, 1, 6.31788e-08, -8.43421e-08, -0.405297, 0.914185, 0.633333, 1, 8.03101e-08, -9.27656e-08, -0.353477, 0.935443, 0.666667, 1, 1.77928e-07, -1.26441e-07, -0.298652, 0.954362, 0.7, 1, 6.05022e-08, -1.01201e-07, -0.240871, 0.970557, 0.733333, 1, 1.4213e-08, -8.22455e-08, -0.181186, 0.983449, 0.8, 1, 8.85281e-08, -7.29044e-08, -0.0580206, 0.998315, 0.833333, 1, 7.3848e-08, -7.98485e-08, 0.00444274, 0.99999, 0.866667, 1, 3.11794e-08, -8.55527e-08, 0.0665827, 0.997781, 0.9, 1, 2.20378e-08, -7.59187e-08, 0.127822, 0.991797, 0.933333, 1, 7.06041e-08, -7.90797e-08, 0.187665, 0.982233, 0.966667, 1, 1.18446e-07, -8.85428e-08, 0.24564, 0.969361, 1, 1, 2.47935e-08, -6.32203e-08, 0.301314, 0.953525, 1.03333, 1, 3.08962e-08, -8.0108e-08, 0.353534, 0.935422, 1.06667, 1, 5.68633e-08, -8.43365e-08, 0.40243, 0.915451, 1.1, 1, 8.28231e-08, -6.74629e-08, 0.447681, 0.894193, 1.13333, 1, 9.59009e-08, -4.63733e-08, 0.489004, 0.872282, 1.16667, 1, 9.02551e-08, -6.32203e-08, 0.526146, 0.850394, 1.2, 1, 4.64119e-08, -4.63702e-08, 0.557338, 0.830286, 1.23333, 1, 1.43865e-07, -6.74455e-08, 0.583135, 0.812376, 1.26667, 1, 1.53008e-07, -8.43012e-08, 0.60376, 0.797166, 1.3, 1, 5.91317e-08, -7.58653e-08, 0.618736, 0.785599, 1.33333, 1, 6.23848e-08, -4.21468e-08, 0.626901, 0.779099, 1.36667, 1, 4.76723e-08, -7.58996e-08, 0.575229, 0.817992, 1.4, 1, 6.42011e-08, -7.20944e-08, 0.445665, 0.8952, 1.43333, 1, 7.26278e-08, -7.03605e-08, 0.245817, 0.969316, 1.46667, 1, 3.97087e-08, -8.28761e-08, -0.000431888, 1, 1.5, 1, -5.7711e-08, -9.48304e-08, -0.250752, 0.968051, 1.53333, 1, -3.80544e-08, -5.30434e-08, -0.44937, 0.893346, 1.56667, 1, -2.25938e-08, -4.23292e-08, -0.591504, 0.806302, 1.6, 1, -3.46995e-08, -2.53203e-08, -0.683406, 0.730039, 1.63333, 1, -6.32263e-08, 9.73757e-16, -0.73445, 0.678662, 1.66667, 1, -6.32203e-08, 0, -0.752921, 0.65811, 2.625, 1, -4.21468e-08, 0, -0.752921, 0.65811)
tracks/32/type = "scale_3d"
tracks/32/imported = true
tracks/32/enabled = true
tracks/32/path = NodePath("Armature/Skeleton3D:Hand")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = PackedFloat32Array(0, 1, 1, 1, 1, 1.5, 1, 1, 1, 1, 1.53333, 1, 1.05, 1.05, 1.05, 1.56667, 1, 1.145, 1.145, 1.145, 1.6, 1, 1.255, 1.255, 1.255, 1.63333, 1, 1.35, 1.35, 1.35, 1.7, 1, 1.35, 1.35, 1.35, 1.73333, 1, 1.255, 1.255, 1.255, 1.76667, 1, 1.145, 1.145, 1.145, 1.8, 1, 1.05, 1.05, 1.05, 1.83333, 1, 1, 1, 1, 2.625, 1, 1, 1, 1)
tracks/33/type = "position_3d"
tracks/33/imported = true
tracks/33/enabled = true
tracks/33/path = NodePath("Armature/Skeleton3D:Finger")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/keys = PackedFloat32Array(0, 1, 0.336603, -0.180161, 0.175658, 0.0333333, 1, 0.336999, -0.174888, 0.175658, 0.0666667, 1, 0.337917, -0.162264, 0.175658, 0.1, 1, 0.339247, -0.142786, 0.175658, 0.133333, 1, 0.34086, -0.116914, 0.175658, 0.166667, 1, 0.342606, -0.0850796, 0.175658, 0.2, 1, 0.344174, -0.0460682, 0.175658, 0.233333, 1, 0.345315, -0.00221726, 0.175658, 0.266667, 1, 0.345829, 0.0461219, 0.175658, 0.3, 1, 0.345517, 0.0985976, 0.175658, 0.333333, 1, 0.344183, 0.154858, 0.175658, 0.366667, 1, 0.341133, 0.215449, 0.175658, 0.4, 1, 0.336567, 0.278736, 0.175658, 0.433333, 1, 0.325974, 0.345125, 0.175658, 0.466667, 1, 0.306806, 0.414887, 0.175658, 0.5, 1, 0.278482, 0.488161, 0.175658, 0.533333, 1, 0.241712, 0.56449, 0.175658, 0.566667, 1, 0.19944, 0.641836, 0.175658, 0.6, 1, 0.151988, 0.71933, 0.175658, 0.633333, 1, 0.099693, 0.796111, 0.175658, 0.733333, 1, -0.0838301, 1.01066, 0.175658, 0.766667, 1, -0.150952, 1.0741, 0.175658, 0.8, 1, -0.21963, 1.13277, 0.175658, 0.833333, 1, -0.289109, 1.18626, 0.175658, 0.866667, 1, -0.35809, 1.23249, 0.175658, 0.9, 1, -0.425558, 1.27323, 0.175658, 0.933333, 1, -0.490828, 1.30862, 0.175658, 0.966667, 1, -0.553267, 1.33884, 0.175658, 1, 1, -0.612296, 1.36412, 0.175658, 1.03333, 1, -0.665885, 1.3834, 0.175658, 1.06667, 1, -0.714913, 1.39858, 0.175658, 1.1, 1, -0.759153, 1.40982, 0.175658, 1.13333, 1, -0.798376, 1.41719, 0.175658, 1.16667, 1, -0.83235, 1.42065, 0.175658, 1.2, 1, -0.85833, 1.41809, 0.175658, 1.23333, 1, -0.875496, 1.40951, 0.175658, 1.26667, 1, -0.857809, 1.39362, 0.175658, 1.3, 1, -0.775772, 1.36993, 0.175658, 1.33333, 1, -0.596428, 1.33869, 0.175658, 1.36667, 1, -0.28809, 1.3345, 0.175658, 1.4, 1, 0.0776938, 1.35211, 0.175658, 1.43333, 1, 0.475523, 1.34089, 0.175658, 1.46667, 1, 0.873076, 1.25315, 0.175658, 1.5, 1, 1.23111, 1.04423, 0.175658, 1.53333, 1, 1.48015, 0.728466, 0.18444, 1.56667, 1, 1.66054, 0.435401, 0.201128, 1.6, 1, 1.78224, 0.206861, 0.22045, 1.63333, 1, 1.85373, 0.0647816, 0.237138, 1.66667, 1, 1.882, 0.0112198, 0.245921, 1.7, 1, 1.86479, 0.00935706, 0.237138, 1.73333, 1, 1.83208, 0.00550704, 0.22045, 1.76667, 1, 1.79421, -0.000384863, 0.201128, 1.8, 1, 1.7615, -0.00862081, 0.18444, 1.83333, 1, 1.74429, -0.0197505, 0.175658, 1.86667, 1, 1.74429, -0.0338153, 0.175658, 1.9, 1, 1.74429, -0.0484954, 0.175658, 1.93333, 1, 1.74429, -0.0636476, 0.175658, 1.96667, 1, 1.74429, -0.079142, 0.175658, 2.06667, 1, 1.74429, -0.126426, 0.175658, 2.1, 1, 1.74429, -0.141996, 0.175658, 2.13333, 1, 1.74429, -0.157281, 0.175658, 2.16667, 1, 1.74429, -0.172167, 0.175658, 2.2, 1, 1.74429, -0.186231, 0.175658, 2.23333, 1, 1.74429, -0.19939, 0.175658, 2.26667, 1, 1.74429, -0.211353, 0.175658, 2.3, 1, 1.74429, -0.221732, 0.175658, 2.33333, 1, 1.74429, -0.230043, 0.175658, 2.36667, 1, 1.74429, -0.233632, 0.175658, 2.625, 1, 1.74429, -0.23453, 0.175658)
tracks/34/type = "rotation_3d"
tracks/34/imported = true
tracks/34/enabled = true
tracks/34/path = NodePath("Armature/Skeleton3D:Finger")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/keys = PackedFloat32Array(0, 1, 0.0206594, -0.00762765, -0.999747, 0.00462034, 0.0333333, 1, 0.0206784, -0.00757597, -0.999732, 0.00712262, 0.0666667, 1, 0.0207241, -0.00744992, -0.99967, 0.0132075, 0.1, 1, 0.020794, -0.00725269, -0.9995, 0.0227034, 0.133333, 1, 0.0208847, -0.00698719, -0.999129, 0.035437, 0.166667, 1, 0.0209926, -0.00665617, -0.998444, 0.0512322, 0.2, 1, 0.0211188, -0.00624404, -0.99725, 0.070759, 0.233333, 1, 0.0212518, -0.00577521, -0.995441, 0.0928072, 0.266667, 1, 0.021387, -0.00525252, -0.992866, 0.117182, 0.3, 1, 0.0215197, -0.00467896, -0.989379, 0.143683, 0.333333, 1, 0.0216455, -0.00405769, -0.984834, 0.172097, 0.366667, 1, 0.0217614, -0.00338104, -0.978991, 0.202709, 0.4, 1, 0.0218603, -0.0026676, -0.971839, 0.234616, 0.433333, 1, 0.0219401, -0.00191924, -0.96266, 0.269816, 0.466667, 1, 0.0219962, -0.00114139, -0.950434, 0.310146, 0.5, 1, 0.0220198, -0.000343539, -0.933758, 0.357226, 0.533333, 1, 0.0220241, 0.00045197, -0.910734, 0.412405, 0.566667, 1, 0.0220031, 0.00122652, -0.881147, 0.472329, 0.6, 1, 0.0219554, 0.00196731, -0.843971, 0.535936, 0.633333, 1, 0.0218796, 0.00266137, -0.798223, 0.601959, 0.666667, 1, 0.0217745, 0.00329563, -0.743012, 0.668916, 0.7, 1, 0.0217082, 0.00384282, -0.676183, 0.736404, 0.733333, 1, 0.0216396, 0.00430919, -0.598655, 0.800703, 0.766667, 1, 0.0215675, 0.00468875, -0.510437, 0.859632, 0.8, 1, 0.0214901, 0.00497737, -0.411997, 0.910918, 0.833333, 1, 0.0214062, 0.00517349, -0.304321, 0.952315, 0.866667, 1, 0.0214201, 0.00528284, -0.188416, 0.981841, 0.9, 1, 0.0214318, 0.0053268, -0.0682482, 0.997424, 0.933333, 1, 0.0214324, 0.00532144, 0.0531078, 0.998345, 0.966667, 1, 0.0214174, 0.00528697, 0.17238, 0.984783, 1, 1, 0.0213882, 0.00524775, 0.286344, 0.957874, 1.03333, 1, 0.0214166, 0.00527143, 0.389597, 0.920721, 1.06667, 1, 0.0214035, 0.00535453, 0.481188, 0.87634, 1.1, 1, 0.0213525, 0.00551214, 0.560085, 0.828142, 1.13333, 1, 0.0212707, 0.00575478, 0.626008, 0.779505, 1.16667, 1, 0.0211643, 0.00608775, 0.679269, 0.733559, 1.2, 1, 0.0210388, 0.00653057, 0.717405, 0.696308, 1.23333, 1, 0.0208766, 0.00701928, 0.7443, 0.667482, 1.26667, 1, 0.020689, 0.00754199, 0.761426, 0.647877, 1.3, 1, 0.0204924, 0.00806172, 0.769535, 0.638224, 1.33333, 1, 0.0203092, 0.00851619, 0.768657, 0.639282, 1.36667, 1, 0.0207055, 0.00753638, 0.714141, 0.699655, 1.4, 1, 0.0215633, 0.00474662, 0.585883, 0.810095, 1.43333, 1, 0.022096, 0.000489615, 0.38328, 0.923368, 1.46667, 1, 0.0215929, -0.00456094, 0.125755, 0.991816, 1.5, 1, 0.0198837, -0.00946707, -0.143078, 0.989466, 1.53333, 1, 0.0176191, -0.0132581, -0.359682, 0.932815, 1.56667, 1, 0.0152608, -0.0158932, -0.515037, 0.856884, 1.6, 1, 0.0132899, -0.0175605, -0.615281, 0.788, 1.63333, 1, 0.011984, -0.0184762, -0.670699, 0.741402, 1.66667, 1, 0.011469, -0.0188003, -0.690928, 0.722588, 2.625, 1, 0.011469, -0.0188003, -0.690928, 0.722588)
tracks/35/type = "scale_3d"
tracks/35/imported = true
tracks/35/enabled = true
tracks/35/path = NodePath("Armature/Skeleton3D:Finger")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/keys = PackedFloat32Array(0, 1, 1, 1, 1, 1.5, 1, 1, 1, 1, 1.53333, 1, 1.05, 1.05, 1.05, 1.56667, 1, 1.145, 1.145, 1.145, 1.6, 1, 1.255, 1.255, 1.255, 1.63333, 1, 1.35, 1.35, 1.35, 1.7, 1, 1.35, 1.35, 1.35, 1.73333, 1, 1.255, 1.255, 1.255, 1.76667, 1, 1.145, 1.145, 1.145, 1.8, 1, 1.05, 1.05, 1.05, 1.83333, 1, 1, 1, 1, 2.625, 1, 1, 1, 1)
tracks/36/type = "position_3d"
tracks/36/imported = true
tracks/36/enabled = true
tracks/36/path = NodePath("Armature/Skeleton3D:Finger.004")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/keys = PackedFloat32Array(0, 1, 0.337618, -0.293963, 0.177415, 0.0333333, 1, 0.338584, -0.288684, 0.177415, 0.0666667, 1, 0.340887, -0.276028, 0.177415, 0.1, 1, 0.344378, -0.256465, 0.177415, 0.133333, 1, 0.348886, -0.230424, 0.177415, 0.166667, 1, 0.354219, -0.198293, 0.177415, 0.2, 1, 0.360205, -0.158719, 0.177415, 0.233333, 1, 0.36631, -0.114025, 0.177415, 0.266667, 1, 0.372275, -0.0645104, 0.177415, 0.3, 1, 0.37784, -0.0104736, 0.177415, 0.333333, 1, 0.382746, 0.0477835, 0.177415, 0.366667, 1, 0.38627, 0.111036, 0.177415, 0.4, 1, 0.388416, 0.177531, 0.177415, 0.433333, 1, 0.385022, 0.247991, 0.177426, 0.466667, 1, 0.373817, 0.323076, 0.177457, 0.5, 1, 0.354413, 0.403389, 0.177521, 0.533333, 1, 0.327066, 0.489525, 0.177631, 0.566667, 1, 0.293851, 0.579031, 0.177776, 0.6, 1, 0.254489, 0.671111, 0.177955, 0.633333, 1, 0.208671, 0.764879, 0.178169, 0.666667, 1, 0.156058, 0.85937, 0.178416, 0.7, 1, 0.0938741, 0.952495, 0.1787, 0.833333, 1, -0.223112, 1.2789, 0.17994, 0.866667, 1, -0.316297, 1.33739, 0.180203, 0.9, 1, -0.410234, 1.38464, 0.180415, 0.933333, 1, -0.502714, 1.42046, 0.180565, 0.966667, 1, -0.591577, 1.44507, 0.180649, 1, 1, -0.674708, 1.45917, 0.180663, 1.03333, 1, -0.747052, 1.46206, 0.180599, 1.06667, 1, -0.81018, 1.45913, 0.180497, 1.1, 1, -0.864127, 1.45191, 0.180374, 1.13333, 1, -0.909155, 1.44165, 0.180248, 1.16667, 1, -0.945751, 1.42933, 0.180133, 1.2, 1, -0.971851, 1.41465, 0.180057, 1.23333, 1, -0.988454, 1.39713, 0.180018, 1.26667, 1, -0.970047, 1.37537, 0.180016, 1.3, 1, -0.887537, 1.34885, 0.180047, 1.33333, 1, -0.708249, 1.31791, 0.180103, 1.36667, 1, -0.401421, 1.33214, 0.180177, 1.4, 1, -0.0271059, 1.38675, 0.180254, 1.43333, 1, 0.399021, 1.41673, 0.180327, 1.46667, 1, 0.845142, 1.3592, 0.180392, 1.5, 1, 1.26329, 1.15331, 0.180445, 1.53333, 1, 1.55908, 0.81422, 0.189502, 1.56667, 1, 1.77417, 0.4941, 0.206668, 1.6, 1, 1.92007, 0.240517, 0.226529, 1.63333, 1, 2.00643, 0.0800699, 0.243676, 1.66667, 1, 2.04105, 0.0183862, 0.252702, 1.7, 1, 2.01815, 0.0162675, 0.243676, 1.73333, 1, 1.97465, 0.0119312, 0.226529, 1.76667, 1, 1.92429, 0.00547624, 0.206674, 1.8, 1, 1.88079, -0.00324599, 0.189526, 1.83333, 1, 1.85789, -0.0146317, 0.180501, 1.86667, 1, 1.85789, -0.0286964, 0.180501, 1.9, 1, 1.85789, -0.0433766, 0.180501, 1.93333, 1, 1.85789, -0.0585287, 0.180501, 1.96667, 1, 1.85789, -0.0740231, 0.180501, 2.06667, 1, 1.85789, -0.121307, 0.180501, 2.1, 1, 1.85789, -0.136877, 0.180501, 2.13333, 1, 1.85789, -0.152162, 0.180501, 2.16667, 1, 1.85789, -0.167048, 0.180501, 2.2, 1, 1.85789, -0.181112, 0.180501, 2.23333, 1, 1.85789, -0.194271, 0.180501, 2.26667, 1, 1.85789, -0.206234, 0.180501, 2.3, 1, 1.85789, -0.216614, 0.180501, 2.33333, 1, 1.85789, -0.224924, 0.180501, 2.36667, 1, 1.85789, -0.228514, 0.180501, 2.625, 1, 1.85789, -0.229411, 0.180501)
tracks/37/type = "rotation_3d"
tracks/37/imported = true
tracks/37/enabled = true
tracks/37/path = NodePath("Armature/Skeleton3D:Finger.004")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/keys = PackedFloat32Array(0, 1, -0.0204032, -0.00828885, 0.741687, 0.670385, 0.0333333, 1, -0.0203824, -0.00833991, 0.743363, 0.668526, 0.0666667, 1, -0.0203312, -0.00846381, 0.747418, 0.663989, 0.1, 1, -0.0202499, -0.00865655, 0.753692, 0.656859, 0.133333, 1, -0.020138, -0.00891383, 0.762, 0.647202, 0.166667, 1, -0.0199945, -0.00923117, 0.772139, 0.635071, 0.2, 1, -0.0198101, -0.00962063, 0.784418, 0.619841, 0.233333, 1, -0.0195923, -0.0100566, 0.797941, 0.602333, 0.266667, 1, -0.0193399, -0.0105339, 0.812467, 0.582591, 0.3, 1, -0.0190514, -0.0110471, 0.827751, 0.560663, 0.333333, 1, -0.0187256, -0.0115908, 0.843544, 0.536608, 0.366667, 1, -0.0183553, -0.0121688, 0.859859, 0.510056, 0.4, 1, -0.0179475, -0.0127625, 0.87608, 0.481664, 0.433333, 1, -0.0175179, -0.0133505, 0.893519, 0.448485, 0.466667, 1, -0.0170842, -0.0139072, 0.913006, 0.407352, 0.5, 1, -0.0166633, -0.0143989, 0.934666, 0.354845, 0.533333, 1, -0.0163182, -0.0148075, 0.957673, 0.287015, 0.566667, 1, -0.0160414, -0.0151317, 0.97811, 0.206918, 0.6, 1, -0.015842, -0.0153581, 0.993182, 0.114471, 0.633333, 1, -0.0157255, -0.0154702, 0.999706, 0.0100365, 0.666667, 1, 0.0156938, 0.0154499, -0.994186, 0.105398, 0.7, 1, 0.0158811, 0.0153241, -0.97212, 0.233443, 0.733333, 1, 0.0161874, 0.0150555, -0.929847, 0.367283, 0.766667, 1, 0.0165961, 0.0146245, -0.864268, 0.502544, 0.8, 1, 0.0170824, 0.0140147, -0.773255, 0.63371, 0.833333, 1, 0.0176156, 0.0132167, -0.656098, 0.754354, 0.866667, 1, 0.0183543, 0.0123127, -0.512829, 0.858206, 0.933333, 1, 0.0197252, 0.0100639, -0.174353, 0.984434, 0.966667, 1, 0.0202698, 0.00880172, 0.00559507, 0.99974, 1, 1, 0.0206948, 0.00753075, 0.181043, 0.983229, 1.03333, 1, 0.0211334, 0.00642715, 0.339476, 0.940355, 1.06667, 1, 0.0214168, 0.00546818, 0.476953, 0.878651, 1.1, 1, 0.0215744, 0.00471013, 0.590886, 0.806453, 1.13333, 1, 0.0216459, 0.00419326, 0.6811, 0.731859, 1.16667, 1, 0.0216668, 0.00394161, 0.749195, 0.661983, 1.2, 1, 0.0216611, 0.00404547, 0.793384, 0.608323, 1.23333, 1, 0.0215886, 0.00437574, 0.821355, 0.569992, 1.26667, 1, 0.0214652, 0.00490799, 0.836142, 0.54807, 1.3, 1, 0.0213002, 0.00558282, 0.839577, 0.542794, 1.33333, 1, 0.0211004, 0.0063058, 0.832346, 0.553819, 1.36667, 1, 0.0213102, 0.0056489, 0.77411, 0.632667, 1.4, 1, 0.0219037, 0.00320451, 0.642203, 0.766215, 1.43333, 1, 0.0221706, -0.000669395, 0.431012, 0.902074, 1.46667, 1, 0.0214718, -0.00530052, 0.159153, 0.987006, 1.5, 1, 0.0197194, -0.00980466, -0.126185, 0.991762, 1.53333, 1, 0.0175723, -0.0133661, -0.354788, 0.934686, 1.56667, 1, 0.0152858, -0.0158847, -0.515957, 0.856331, 1.6, 1, 0.013348, -0.0175162, -0.617889, 0.785957, 1.63333, 1, 0.0120452, -0.0184364, -0.673152, 0.739176, 1.66667, 1, 0.0115311, -0.0187622, -0.693319, 0.720295, 2.625, 1, 0.0115312, -0.0187622, -0.693319, 0.720295)
tracks/38/type = "scale_3d"
tracks/38/imported = true
tracks/38/enabled = true
tracks/38/path = NodePath("Armature/Skeleton3D:Finger.004")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/keys = PackedFloat32Array(0, 1, 1, 1, 1, 1.5, 1, 1, 1, 1, 1.53333, 1, 1.05, 1.05, 1.05, 1.56667, 1, 1.145, 1.145, 1.145, 1.6, 1, 1.255, 1.255, 1.255, 1.63333, 1, 1.35, 1.35, 1.35, 1.7, 1, 1.35, 1.35, 1.35, 1.73333, 1, 1.255, 1.255, 1.255, 1.76667, 1, 1.145, 1.145, 1.145, 1.8, 1, 1.05, 1.05, 1.05, 1.83333, 1, 1, 1, 1, 2.625, 1, 1, 1, 1)
tracks/39/type = "position_3d"
tracks/39/imported = true
tracks/39/enabled = true
tracks/39/path = NodePath("Armature/Skeleton3D:Finger.001")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/keys = PackedFloat32Array(0, 1, 0.359559, -0.175623, 0.0730534, 0.0333333, 1, 0.359931, -0.170235, 0.0730534, 0.0666667, 1, 0.36079, -0.157333, 0.0730534, 0.1, 1, 0.362021, -0.137421, 0.0730534, 0.133333, 1, 0.36349, -0.110971, 0.0730534, 0.166667, 1, 0.36504, -0.0784227, 0.0730534, 0.2, 1, 0.366326, -0.0385402, 0.0730534, 0.233333, 1, 0.367107, 0.0062817, 0.0730534, 0.266667, 1, 0.367176, 0.0556776, 0.0730534, 0.3, 1, 0.366324, 0.109282, 0.0730534, 0.333333, 1, 0.36435, 0.166727, 0.0730534, 0.366667, 1, 0.360511, 0.228545, 0.0730534, 0.4, 1, 0.355042, 0.293065, 0.0730534, 0.433333, 1, 0.343389, 0.360717, 0.0730534, 0.466667, 1, 0.322963, 0.431784, 0.0730534, 0.5, 1, 0.293137, 0.506403, 0.0730534, 0.533333, 1, 0.254537, 0.584031, 0.0730534, 0.566667, 1, 0.210198, 0.662563, 0.0730534, 0.6, 1, 0.160462, 0.741084, 0.0730534, 0.633333, 1, 0.105686, 0.81869, 0.0730534, 0.733333, 1, -0.0861325, 1.03388, 0.0730534, 0.766667, 1, -0.156099, 1.09686, 0.0730534, 0.8, 1, -0.227573, 1.15473, 0.0730534, 0.833333, 1, -0.29975, 1.20711, 0.0730534, 0.866667, 1, -0.37121, 1.25181, 0.0730534, 0.9, 1, -0.440937, 1.29078, 0.0730534, 0.933333, 1, -0.508217, 1.32418, 0.0730534, 0.966667, 1, -0.572397, 1.35224, 0.0730534, 1, 1, -0.632882, 1.37524, 0.0730534, 1.03333, 1, -0.68754, 1.39217, 0.0730534, 1.06667, 1, -0.737361, 1.40502, 0.0730534, 1.1, 1, -0.782138, 1.41399, 0.0730534, 1.13333, 1, -0.821668, 1.4192, 0.0730534, 1.16667, 1, -0.85575, 1.42064, 0.0730534, 1.2, 1, -0.881648, 1.41634, 0.0730534, 1.23333, 1, -0.89866, 1.4063, 0.0730534, 1.26667, 1, -0.880786, 1.38923, 0.0730534, 1.3, 1, -0.798572, 1.36467, 0.0730534, 1.33333, 1, -0.619115, 1.33295, 0.0730534, 1.36667, 1, -0.311279, 1.33174, 0.0730534, 1.4, 1, 0.0552456, 1.3563, 0.0730534, 1.43333, 1, 0.457202, 1.35366, 0.0730534, 1.46667, 1, 0.862884, 1.27339, 0.0730534, 1.5, 1, 1.23213, 1.06761, 0.0730534, 1.53333, 1, 1.49117, 0.749831, 0.0767061, 1.56667, 1, 1.67969, 0.453487, 0.0836461, 1.6, 1, 1.80752, 0.221455, 0.091682, 1.63333, 1, 1.88301, 0.0766047, 0.0986221, 1.66667, 1, 1.91301, 0.0218046, 0.102275, 1.7, 1, 1.89469, 0.0195638, 0.0986221, 1.73333, 1, 1.85987, 0.0149956, 0.091682, 1.76667, 1, 1.81957, 0.00827199, 0.0836461, 1.8, 1, 1.78476, -0.000682207, 0.0767061, 1.83333, 1, 1.76643, -0.01219, 0.0730534, 1.86667, 1, 1.76643, -0.0262547, 0.0730534, 1.9, 1, 1.76643, -0.0409349, 0.0730534, 1.93333, 1, 1.76643, -0.056087, 0.0730534, 1.96667, 1, 1.76643, -0.0715814, 0.0730534, 2.06667, 1, 1.76643, -0.118865, 0.0730534, 2.1, 1, 1.76643, -0.134435, 0.0730534, 2.13333, 1, 1.76643, -0.149721, 0.0730534, 2.16667, 1, 1.76643, -0.164606, 0.0730534, 2.2, 1, 1.76643, -0.178671, 0.0730534, 2.23333, 1, 1.76643, -0.191829, 0.0730534, 2.26667, 1, 1.76643, -0.203792, 0.0730534, 2.3, 1, 1.76643, -0.214172, 0.0730534, 2.33333, 1, 1.76643, -0.222483, 0.0730534, 2.36667, 1, 1.76643, -0.226072, 0.0730534, 2.625, 1, 1.76643, -0.226969, 0.0730534)
tracks/40/type = "rotation_3d"
tracks/40/imported = true
tracks/40/enabled = true
tracks/40/path = NodePath("Armature/Skeleton3D:Finger.001")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/keys = PackedFloat32Array(0, 1, 1.71469e-07, 1.26441e-07, -0.999998, 0.00197074, 0.0333333, 1, 1.65081e-07, 1.60158e-07, -0.99999, 0.00447366, 0.0666667, 1, 1.62201e-07, 1.68589e-07, -0.999944, 0.0105602, 0.1, 1, 1.62138e-07, 1.68592e-07, -0.999799, 0.0200588, 0.133333, 1, 1.62797e-07, 1.77023e-07, -0.999462, 0.0327964, 0.166667, 1, 1.60685e-07, 2.10734e-07, -0.998818, 0.0485972, 0.2, 1, 1.52263e-07, 1.77026e-07, -0.997676, 0.0681319, 0.233333, 1, 1.48582e-07, 1.68604e-07, -0.995925, 0.0901901, 0.266667, 1, 1.43317e-07, 1.51748e-07, -0.993414, 0.114578, 0.3, 1, 1.34884e-07, 1.26454e-07, -0.989996, 0.141094, 0.333333, 1, 1.26441e-07, 1.26441e-07, -0.985526, 0.169527, 0.366667, 1, 1.09596e-07, 1.93897e-07, -0.979763, 0.200161, 0.4, 1, 1.11712e-07, 1.60192e-07, -0.972693, 0.232096, 0.433333, 1, 1.11722e-07, 1.43342e-07, -0.963606, 0.267328, 0.466667, 1, 1.01183e-07, 1.6864e-07, -0.951483, 0.3077, 0.5, 1, 8.42937e-08, 1.68587e-07, -0.934929, 0.354835, 0.533333, 1, 1.01196e-07, 1.68663e-07, -0.912046, 0.410089, 0.566667, 1, 9.28077e-08, 1.43431e-07, -0.88261, 0.470106, 0.6, 1, 7.59498e-08, 1.43467e-07, -0.845594, 0.533827, 0.633333, 1, 6.32799e-08, 1.60301e-07, -0.80001, 0.599987, 0.666667, 1, 6.32203e-08, 1.26441e-07, -0.744963, 0.667106, 0.7, 1, 7.47175e-08, 1.26583e-07, -0.678298, 0.734787, 0.733333, 1, 4.79566e-08, 1.14025e-07, -0.600922, 0.799308, 0.766667, 1, 2.03163e-08, 1.22508e-07, -0.512839, 0.858485, 0.8, 1, 1.90568e-08, 1.3296e-07, -0.414511, 0.910044, 0.833333, 1, 6.08948e-08, 7.3757e-08, -0.306919, 0.951736, 0.866667, 1, 6.07632e-08, 8.65505e-08, -0.191065, 0.981577, 0.9, 1, 2.41328e-08, 9.17923e-08, -0.0709091, 0.997483, 0.933333, 1, 3.82022e-08, 8.12189e-08, 0.0504736, 0.998725, 0.966667, 1, 8.18074e-08, 6.96728e-08, 0.16981, 0.985477, 1, 1, 2.58257e-08, 9.48304e-08, 0.283872, 0.958862, 1.03333, 1, -3.68426e-08, 6.96569e-08, 0.387249, 0.921975, 1.06667, 1, 2.63492e-08, 1.01332e-07, 0.47898, 0.877826, 1.1, 1, 2.75295e-08, 1.26615e-07, 0.558023, 0.829825, 1.13333, 1, -3.56256e-08, 1.13859e-07, 0.624091, 0.781352, 1.16667, 1, 1.99517e-08, 6.32203e-08, 0.677487, 0.735535, 1.2, 1, 3.99316e-09, 1.13833e-07, 0.71573, 0.698377, 1.23333, 1, -1.26466e-08, 1.26469e-07, 0.742708, 0.669615, 1.26667, 1, -1.26447e-08, 1.09588e-07, 0.75989, 0.650051, 1.3, 1, 0, 9.27231e-08, 0.768027, 0.640417, 1.33333, 1, 0, 1.26441e-07, 0.767146, 0.641473, 1.36667, 1, 7.13517e-08, 1.09676e-07, 0.712457, 0.701716, 1.4, 1, 8.8166e-09, 1.06153e-07, 0.583876, 0.811843, 1.43333, 1, -3.19644e-08, 1.06787e-07, 0.380924, 0.924606, 1.46667, 1, 3.10524e-08, 1.03117e-07, 0.123155, 0.992387, 1.5, 1, 1.97935e-07, 8.95621e-08, -0.145736, 0.989324, 1.53333, 1, 1.95389e-07, 5.20177e-08, -0.362241, 0.932085, 1.56667, 1, 2.32133e-07, 5.50358e-08, -0.517433, 0.855724, 1.6, 1, 2.58124e-07, 6.33109e-08, -0.617518, 0.786557, 1.63333, 1, 2.28144e-07, 5.90108e-08, -0.672825, 0.739801, 1.66667, 1, 1.01189e-07, 4.21468e-08, -0.693009, 0.720929, 2.625, 1, 1.01189e-07, 4.21468e-08, -0.693009, 0.720929)
tracks/41/type = "scale_3d"
tracks/41/imported = true
tracks/41/enabled = true
tracks/41/path = NodePath("Armature/Skeleton3D:Finger.001")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/keys = PackedFloat32Array(0, 1, 1, 1, 1, 1.5, 1, 1, 1, 1, 1.53333, 1, 1.05, 1.05, 1.05, 1.56667, 1, 1.145, 1.145, 1.145, 1.6, 1, 1.255, 1.255, 1.255, 1.63333, 1, 1.35, 1.35, 1.35, 1.7, 1, 1.35, 1.35, 1.35, 1.73333, 1, 1.255, 1.255, 1.255, 1.76667, 1, 1.145, 1.145, 1.145, 1.8, 1, 1.05, 1.05, 1.05, 1.83333, 1, 1, 1, 1, 2.625, 1, 1, 1, 1)
tracks/42/type = "position_3d"
tracks/42/imported = true
tracks/42/enabled = true
tracks/42/path = NodePath("Armature/Skeleton3D:Finger.005")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/keys = PackedFloat32Array(0, 1, 0.360036, -0.296644, 0.0730534, 0.0333333, 1, 0.361014, -0.291252, 0.0730534, 0.0666667, 1, 0.363346, -0.278323, 0.0730534, 0.1, 1, 0.366875, -0.258333, 0.0730534, 0.133333, 1, 0.371423, -0.231718, 0.0730534, 0.166667, 1, 0.376789, -0.198873, 0.0730534, 0.2, 1, 0.382775, -0.158416, 0.0730534, 0.233333, 1, 0.38884, -0.112724, 0.0730534, 0.266667, 1, 0.394712, -0.0621071, 0.0730534, 0.3, 1, 0.400119, -0.00687427, 0.0730534, 0.333333, 1, 0.40479, 0.0526608, 0.0730534, 0.366667, 1, 0.407957, 0.117275, 0.0730534, 0.4, 1, 0.409641, 0.185171, 0.0730534, 0.433333, 1, 0.405666, 0.257114, 0.0730534, 0.466667, 1, 0.393735, 0.333799, 0.0730534, 0.5, 1, 0.373434, 0.415856, 0.0730534, 0.533333, 1, 0.344909, 0.503862, 0.0730534, 0.566667, 1, 0.31027, 0.595279, 0.0730534, 0.6, 1, 0.269217, 0.689269, 0.0730534, 0.633333, 1, 0.221423, 0.784909, 0.0730534, 0.666667, 1, 0.166533, 0.881185, 0.0730534, 0.7, 1, 0.101649, 0.975879, 0.0730534, 0.833333, 1, -0.229048, 1.30533, 0.0730534, 0.866667, 1, -0.326187, 1.36322, 0.0730534, 0.9, 1, -0.424034, 1.40926, 0.0730534, 0.933333, 1, -0.520258, 1.44327, 0.0730534, 0.966667, 1, -0.612579, 1.46552, 0.0730534, 1, 1, -0.698766, 1.47676, 0.0730534, 1.03333, 1, -0.773467, 1.47636, 0.0730534, 1.06667, 1, -0.83839, 1.47001, 0.0730534, 1.1, 1, -0.893598, 1.4594, 0.0730534, 1.13333, 1, -0.939403, 1.44589, 0.0730534, 1.16667, 1, -0.976365, 1.43056, 0.0730534, 1.2, 1, -1.00246, 1.41337, 0.0730534, 1.23333, 1, -1.01893, 1.39382, 0.0730534, 1.26667, 1, -1.00032, 1.3705, 0.0730534, 1.3, 1, -0.917624, 1.34292, 0.0730534, 1.33333, 1, -0.738226, 1.31153, 0.0730534, 1.36667, 1, -0.431887, 1.32993, 0.0730534, 1.4, 1, -0.0560659, 1.39379, 0.0730534, 1.43333, 1, 0.376246, 1.43483, 0.0730534, 1.46667, 1, 0.83379, 1.38642, 0.0730534, 1.5, 1, 1.26703, 1.18349, 0.0730534, 1.53333, 1, 1.57568, 0.840624, 0.0767061, 1.56667, 1, 1.80097, 0.515284, 0.0836461, 1.6, 1, 1.95441, 0.25646, 0.091682, 1.63333, 1, 2.0456, 0.0919741, 0.0986221, 1.66667, 1, 2.08231, 0.0284931, 0.102275, 1.7, 1, 2.05794, 0.0260134, 0.0986221, 1.73333, 1, 2.01164, 0.0209913, 0.091682, 1.76667, 1, 1.95803, 0.0137422, 0.0836461, 1.8, 1, 1.91173, 0.00433415, 0.0767061, 1.83333, 1, 1.88736, -0.00741248, 0.0730534, 1.86667, 1, 1.88736, -0.0214772, 0.0730534, 1.9, 1, 1.88736, -0.0361574, 0.0730534, 1.93333, 1, 1.88736, -0.0513095, 0.0730534, 1.96667, 1, 1.88736, -0.066804, 0.0730534, 2.06667, 1, 1.88736, -0.114088, 0.0730534, 2.1, 1, 1.88736, -0.129658, 0.0730534, 2.13333, 1, 1.88736, -0.144943, 0.0730534, 2.16667, 1, 1.88736, -0.159829, 0.0730534, 2.2, 1, 1.88736, -0.173893, 0.0730534, 2.23333, 1, 1.88736, -0.187052, 0.0730534, 2.26667, 1, 1.88736, -0.199014, 0.0730534, 2.3, 1, 1.88736, -0.209394, 0.0730534, 2.33333, 1, 1.88736, -0.217705, 0.0730534, 2.36667, 1, 1.88736, -0.221294, 0.0730534, 2.625, 1, 1.88736, -0.222192, 0.0730534)
tracks/43/type = "rotation_3d"
tracks/43/imported = true
tracks/43/enabled = true
tracks/43/path = NodePath("Armature/Skeleton3D:Finger.005")
tracks/43/interp = 1
tracks/43/loop_wrap = true
tracks/43/keys = PackedFloat32Array(0, 1, 8.42937e-08, -1.68587e-07, 0.743642, 0.668578, 0.0333333, 1, 8.42938e-08, -2.02305e-07, 0.745313, 0.666715, 0.0666667, 1, 8.42945e-08, -1.85448e-07, 0.749357, 0.662166, 0.1, 1, 7.58662e-08, -1.68592e-07, 0.755614, 0.655017, 0.133333, 1, 7.16522e-08, -1.68593e-07, 0.763899, 0.645336, 0.166667, 1, 1.05367e-07, -1.68587e-07, 0.774008, 0.633176, 0.2, 1, 1.05372e-07, -1.34877e-07, 0.786249, 0.61791, 0.233333, 1, 1.05378e-07, -1.26453e-07, 0.799729, 0.600361, 0.266667, 1, 8.85193e-08, -1.26457e-07, 0.814206, 0.580576, 0.3, 1, 7.1658e-08, -1.18023e-07, 0.829436, 0.558602, 0.333333, 1, 1.05367e-07, -8.42937e-08, 0.845168, 0.5345, 0.366667, 1, 8.85196e-08, -8.43039e-08, 0.861417, 0.507898, 0.4, 1, 9.69575e-08, -8.43111e-08, 0.877566, 0.479456, 0.433333, 1, 1.05402e-07, -1.01186e-07, 0.894922, 0.446223, 0.466667, 1, 9.90825e-08, -1.26492e-07, 0.914304, 0.405028, 0.5, 1, 7.3757e-08, -1.26441e-07, 0.93583, 0.352451, 0.533333, 1, 8.22378e-08, -2.53206e-08, 0.958663, 0.284544, 0.566667, 1, 8.44118e-08, -5.06392e-08, 0.978893, 0.204374, 0.6, 1, 7.54791e-08, -6.75589e-08, 0.993724, 0.111865, 0.633333, 1, 6.01568e-08, -3.37599e-08, 0.999973, 0.00738805, 0.666667, 1, -5.26836e-08, 0, -0.994144, 0.108059, 0.7, 1, -6.1238e-08, 0, -0.971735, 0.236076, 0.733333, 1, -3.80911e-08, 2.53758e-08, -0.929097, 0.369837, 0.766667, 1, -2.11712e-08, 4.23424e-08, -0.863144, 0.504957, 0.8, 1, 2.01652e-08, 2.95627e-08, -0.771761, 0.635913, 0.833333, 1, 1.84033e-07, -2.10734e-08, -0.654255, 0.756274, 0.866667, 1, 5.99326e-08, -4.24796e-09, -0.510677, 0.859772, 0.933333, 1, 2.67445e-08, -4.61369e-08, -0.171785, 0.985135, 0.966667, 1, 7.19579e-08, -5.81425e-08, 0.00824733, 0.999966, 1, 1, 1.40593e-07, -2.10734e-08, 0.183694, 0.982984, 1.03333, 1, 1.91169e-07, -2.11449e-08, 0.342051, 0.939681, 1.06667, 1, 1.25116e-07, -8.469e-09, 0.479398, 0.877598, 1.1, 1, 7.06727e-08, 0, 0.593167, 0.80508, 1.13333, 1, 7.54046e-08, 0, 0.683203, 0.730228, 1.16667, 1, 1.05367e-07, 0, 0.75113, 0.660155, 1.2, 1, 8.85645e-08, -3.37328e-08, 0.795187, 0.606365, 1.23333, 1, 7.16717e-08, -4.21596e-08, 0.823063, 0.56795, 1.26667, 1, 7.16519e-08, -4.21481e-08, 0.837795, 0.545984, 1.3, 1, 8.42955e-08, -4.21478e-08, 0.841217, 0.540698, 1.33333, 1, 8.42937e-08, -4.21468e-08, 0.834013, 0.551744, 1.36667, 1, 8.43952e-08, -4.21976e-08, 0.775973, 0.630766, 1.4, 1, 7.05029e-08, -1.70355e-08, 0.64439, 0.764698, 1.43333, 1, 6.39454e-08, -4.29097e-09, 0.433508, 0.90115, 1.46667, 1, 7.04389e-08, -1.27973e-08, 0.161808, 0.986822, 1.5, 1, 8.02536e-08, -2.10734e-08, -0.123586, 0.992334, 1.53333, 1, 2.88773e-08, -5.51169e-08, -0.352395, 0.935851, 1.56667, 1, 3.4249e-08, -6.35272e-08, -0.51381, 0.857904, 1.6, 1, 5.16679e-08, -6.33109e-08, -0.615953, 0.787783, 1.63333, 1, 4.64613e-08, -5.90108e-08, -0.671353, 0.741138, 1.66667, 1, -5.37378e-09, -4.21469e-08, -0.691575, 0.722305, 2.625, 1, 1.48031e-08, -2.10734e-08, -0.691575, 0.722305)
tracks/44/type = "scale_3d"
tracks/44/imported = true
tracks/44/enabled = true
tracks/44/path = NodePath("Armature/Skeleton3D:Finger.005")
tracks/44/interp = 1
tracks/44/loop_wrap = true
tracks/44/keys = PackedFloat32Array(0, 1, 1, 1, 1, 1.5, 1, 1, 1, 1, 1.53333, 1, 1.05, 1.05, 1.05, 1.56667, 1, 1.145, 1.145, 1.145, 1.6, 1, 1.255, 1.255, 1.255, 1.63333, 1, 1.35, 1.35, 1.35, 1.7, 1, 1.35, 1.35, 1.35, 1.73333, 1, 1.255, 1.255, 1.255, 1.76667, 1, 1.145, 1.145, 1.145, 1.8, 1, 1.05, 1.05, 1.05, 1.83333, 1, 1, 1, 1, 2.625, 1, 1, 1, 1)
tracks/45/type = "position_3d"
tracks/45/imported = true
tracks/45/enabled = true
tracks/45/path = NodePath("Armature/Skeleton3D:Finger.002")
tracks/45/interp = 1
tracks/45/loop_wrap = true
tracks/45/keys = PackedFloat32Array(0, 1, 0.364914, -0.17624, -0.0358783, 0.0333333, 1, 0.36529, -0.170826, -0.0358783, 0.0666667, 1, 0.366155, -0.157858, -0.0358783, 0.1, 1, 0.367395, -0.137844, -0.0358783, 0.133333, 1, 0.368872, -0.111257, -0.0358783, 0.166667, 1, 0.370429, -0.0785388, -0.0358783, 0.2, 1, 0.371714, -0.0384453, -0.0358783, 0.233333, 1, 0.372485, 0.0066148, -0.0358783, 0.266667, 1, 0.37253, 0.0562738, -0.0358783, 0.3, 1, 0.37164, 0.110163, -0.0358783, 0.333333, 1, 0.369609, 0.167913, -0.0358783, 0.366667, 1, 0.365683, 0.230056, -0.0358783, 0.4, 1, 0.360102, 0.294911, -0.0358783, 0.433333, 1, 0.348306, 0.362914, -0.0358783, 0.466667, 1, 0.327695, 0.434356, -0.0358783, 0.5, 1, 0.297634, 0.509376, -0.0358783, 0.533333, 1, 0.258724, 0.587416, -0.0358783, 0.566667, 1, 0.214015, 0.666354, -0.0358784, 0.6, 1, 0.16385, 0.745261, -0.0358784, 0.633333, 1, 0.108587, 0.823223, -0.0358784, 0.733333, 1, -0.0850058, 1.03913, -0.0358784, 0.766667, 1, -0.155629, 1.10222, -0.0358784, 0.8, 1, -0.227772, 1.1601, -0.0358784, 0.833333, 1, -0.30062, 1.21243, -0.0358784, 0.866667, 1, -0.37273, 1.25697, -0.0358784, 0.9, 1, -0.443077, 1.29571, -0.0358784, 0.933333, 1, -0.510938, 1.32882, -0.0358784, 0.966667, 1, -0.575651, 1.35653, -0.0358784, 1, 1, -0.636617, 1.37913, -0.0358784, 1.03333, 1, -0.691675, 1.39562, -0.0358784, 1.06667, 1, -0.741834, 1.40801, -0.0358784, 1.1, 1, -0.786888, 1.41652, -0.0358784, 1.13333, 1, -0.826638, 1.42127, -0.0358784, 1.16667, 1, -0.860885, 1.42228, -0.0358784, 1.2, 1, -0.886887, 1.4176, -0.0358784, 1.23333, 1, -0.903967, 1.40722, -0.0358784, 1.26667, 1, -0.886135, 1.38989, -0.0358784, 1.3, 1, -0.803944, 1.36512, -0.0358784, 1.33333, 1, -0.624495, 1.33329, -0.0358784, 1.36667, 1, -0.316559, 1.33277, -0.0358784, 1.4, 1, 0.0506153, 1.3588, -0.0358784, 1.43333, 1, 0.45408, 1.35775, -0.0358784, 1.46667, 1, 0.86207, 1.27855, -0.0358784, 1.5, 1, 1.234, 1.07267, -0.0358784, 1.53333, 1, 1.49509, 0.753743, -0.0376723, 1.56667, 1, 1.68517, 0.456109, -0.0410808, 1.6, 1, 1.8141, 0.22288, -0.0450274, 1.63333, 1, 1.89027, 0.0771408, -0.0484358, 1.66667, 1, 1.92055, 0.021948, -0.0502298, 1.7, 1, 1.90196, 0.0197021, -0.0484358, 1.73333, 1, 1.86664, 0.0151241, -0.0450274, 1.76667, 1, 1.82574, 0.00838927, -0.0410808, 1.8, 1, 1.79041, -0.000574658, -0.0376723, 1.83333, 1, 1.77182, -0.0120875, -0.0358784, 1.86667, 1, 1.77182, -0.0261523, -0.0358784, 1.9, 1, 1.77182, -0.0408325, -0.0358784, 1.93333, 1, 1.77182, -0.0559846, -0.0358784, 1.96667, 1, 1.77182, -0.071479, -0.0358784, 2.06667, 1, 1.77182, -0.118763, -0.0358784, 2.1, 1, 1.77182, -0.134333, -0.0358784, 2.13333, 1, 1.77182, -0.149618, -0.0358784, 2.16667, 1, 1.77182, -0.164504, -0.0358784, 2.2, 1, 1.77182, -0.178568, -0.0358784, 2.23333, 1, 1.77182, -0.191727, -0.0358784, 2.26667, 1, 1.77182, -0.20369, -0.0358784, 2.3, 1, 1.77182, -0.214069, -0.0358784, 2.33333, 1, 1.77182, -0.22238, -0.0358784, 2.36667, 1, 1.77182, -0.225969, -0.0358784, 2.625, 1, 1.77182, -0.226867, -0.0358784)
tracks/46/type = "rotation_3d"
tracks/46/imported = true
tracks/46/enabled = true
tracks/46/path = NodePath("Armature/Skeleton3D:Finger.002")
tracks/46/interp = 1
tracks/46/loop_wrap = true
tracks/46/keys = PackedFloat32Array(0, 1, -0.00104424, 0.000382525, -0.999997, 0.00197091, 0.0333333, 1, -0.00104521, 0.000379895, -0.999989, 0.00447383, 0.0666667, 1, -0.0010475, 0.000373501, -0.999944, 0.0105603, 0.1, 1, -0.001051, 0.000363543, -0.999798, 0.0200589, 0.133333, 1, -0.00105554, 0.00035015, -0.999461, 0.0327966, 0.166667, 1, -0.00106096, 0.000333382, -0.998818, 0.0485973, 0.2, 1, -0.00106727, 0.000312559, -0.997676, 0.068132, 0.233333, 1, -0.00107392, 0.000288878, -0.995924, 0.0901902, 0.266667, 1, -0.00108068, 0.000262482, -0.993414, 0.114578, 0.3, 1, -0.00108731, 0.000233492, -0.989996, 0.141094, 0.333333, 1, -0.00109359, 0.00020201, -0.985525, 0.169527, 0.366667, 1, -0.00109932, 0.000167875, -0.979762, 0.200162, 0.4, 1, -0.00110422, 0.00013188, -0.972692, 0.232096, 0.433333, 1, -0.00110816, 9.40984e-05, -0.963605, 0.267328, 0.466667, 1, -0.00111089, 5.48049e-05, -0.951483, 0.3077, 0.5, 1, -0.001112, 1.44985e-05, -0.934928, 0.354835, 0.533333, 1, -0.0011121, -2.56323e-05, -0.912045, 0.410088, 0.566667, 1, -0.00111091, -6.47676e-05, -0.882609, 0.470106, 0.6, 1, -0.00110841, -0.000102192, -0.845593, 0.533827, 0.633333, 1, -0.00110452, -0.000137224, -0.800009, 0.599987, 0.666667, 1, -0.00109915, -0.00016922, -0.744963, 0.667105, 0.7, 1, -0.00109577, -0.000196914, -0.678297, 0.734787, 0.733333, 1, -0.00109219, -0.000220477, -0.600922, 0.799307, 0.766667, 1, -0.00108848, -0.000239629, -0.512838, 0.858485, 0.8, 1, -0.00108455, -0.00025419, -0.414511, 0.910044, 0.833333, 1, -0.00108022, -0.000264113, -0.306918, 0.951735, 0.866667, 1, -0.0010809, -0.000269635, -0.191064, 0.981577, 0.9, 1, -0.00108153, -0.000271852, -0.0709089, 0.997482, 0.933333, 1, -0.00108162, -0.000271564, 0.0504737, 0.998725, 0.966667, 1, -0.0010809, -0.000269808, 0.16981, 0.985476, 1, 1, -0.00107929, -0.000267854, 0.283872, 0.958861, 1.03333, 1, -0.0010808, -0.00026906, 0.387249, 0.921975, 1.06667, 1, -0.00108015, -0.000273252, 0.47898, 0.877825, 1.1, 1, -0.00107756, -0.000281206, 0.558023, 0.829825, 1.13333, 1, -0.00107339, -0.000293459, 0.624091, 0.781351, 1.16667, 1, -0.0010679, -0.000310285, 0.677487, 0.735534, 1.2, 1, -0.0010616, -0.000332655, 0.71573, 0.698376, 1.23333, 1, -0.00105334, -0.00035725, 0.742708, 0.669615, 1.26667, 1, -0.0010438, -0.000383609, 0.75989, 0.650051, 1.3, 1, -0.00103382, -0.00040987, 0.768027, 0.640417, 1.33333, 1, -0.00102453, -0.000432764, 0.767145, 0.641473, 1.36667, 1, -0.00104463, -0.000383375, 0.712457, 0.701715, 1.4, 1, -0.00108833, -0.000242592, 0.583875, 0.811843, 1.43333, 1, -0.00111579, -2.76677e-05, 0.380924, 0.924606, 1.46667, 1, -0.00109105, 0.000227458, 0.123155, 0.992387, 1.5, 1, -0.00100543, 0.000475464, -0.145735, 0.989323, 1.53333, 1, -0.000891549, 0.000667192, -0.362241, 0.932084, 1.56667, 1, -0.000772783, 0.000800575, -0.517432, 0.855724, 1.6, 1, -0.000673474, 0.000885034, -0.617517, 0.786557, 1.63333, 1, -0.000607668, 0.000931459, -0.672825, 0.739801, 1.66667, 1, -0.000581697, 0.000947925, -0.693009, 0.720928, 2.625, 1, -0.000581656, 0.000947925, -0.693009, 0.720928)
tracks/47/type = "scale_3d"
tracks/47/imported = true
tracks/47/enabled = true
tracks/47/path = NodePath("Armature/Skeleton3D:Finger.002")
tracks/47/interp = 1
tracks/47/loop_wrap = true
tracks/47/keys = PackedFloat32Array(0, 1, 1, 1, 1, 1.5, 1, 1, 1, 1, 1.53333, 1, 1.05, 1.05, 1.05, 1.56667, 1, 1.145, 1.145, 1.145, 1.6, 1, 1.255, 1.255, 1.255, 1.63333, 1, 1.35, 1.35, 1.35, 1.7, 1, 1.35, 1.35, 1.35, 1.73333, 1, 1.255, 1.255, 1.255, 1.76667, 1, 1.145, 1.145, 1.145, 1.8, 1, 1.05, 1.05, 1.05, 1.83333, 1, 1, 1, 1, 2.625, 1, 1, 1, 1)
tracks/48/type = "position_3d"
tracks/48/imported = true
tracks/48/enabled = true
tracks/48/path = NodePath("Armature/Skeleton3D:Finger.006")
tracks/48/interp = 1
tracks/48/loop_wrap = true
tracks/48/keys = PackedFloat32Array(0, 1, 0.365391, -0.297261, -0.0359714, 0.0333333, 1, 0.366372, -0.291842, -0.0359714, 0.0666667, 1, 0.368711, -0.278849, -0.0359714, 0.1, 1, 0.372248, -0.258756, -0.0359714, 0.133333, 1, 0.376805, -0.232004, -0.0359714, 0.166667, 1, 0.382178, -0.198989, -0.0359714, 0.2, 1, 0.388164, -0.158321, -0.0359714, 0.233333, 1, 0.394218, -0.112391, -0.0359714, 0.266667, 1, 0.400066, -0.0615107, -0.0359714, 0.3, 1, 0.405434, -0.00599248, -0.0359714, 0.333333, 1, 0.410048, 0.0538475, -0.0359714, 0.366667, 1, 0.413128, 0.118786, -0.0359714, 0.4, 1, 0.414701, 0.187017, -0.0359714, 0.433333, 1, 0.410582, 0.259311, -0.035972, 0.466667, 1, 0.398467, 0.336371, -0.0359737, 0.5, 1, 0.377931, 0.418829, -0.0359771, 0.533333, 1, 0.349096, 0.507248, -0.035983, 0.566667, 1, 0.314086, 0.59907, -0.0359908, 0.6, 1, 0.272604, 0.693447, -0.0360005, 0.633333, 1, 0.224325, 0.789441, -0.036012, 0.666667, 1, 0.168894, 0.886031, -0.0360253, 0.7, 1, 0.103409, 0.980963, -0.0360406, 0.833333, 1, -0.229918, 1.31065, -0.0361076, 0.866667, 1, -0.327708, 1.36838, -0.0361219, 0.9, 1, -0.426174, 1.41419, -0.0361334, 0.933333, 1, -0.522979, 1.44791, -0.0361417, 0.966667, 1, -0.615833, 1.46981, -0.0361464, 1, 1, -0.7025, 1.48065, -0.0361473, 1.03333, 1, -0.777601, 1.4798, -0.036144, 1.06667, 1, -0.842862, 1.473, -0.0361387, 1.1, 1, -0.898347, 1.46193, -0.0361322, 1.13333, 1, -0.944372, 1.44796, -0.0361255, 1.16667, 1, -0.981499, 1.4322, -0.0361194, 1.2, 1, -1.0077, 1.41463, -0.0361154, 1.23333, 1, -1.02423, 1.39474, -0.0361133, 1.26667, 1, -1.00567, 1.37115, -0.0361132, 1.3, 1, -0.922995, 1.34337, -0.0361149, 1.33333, 1, -0.743606, 1.31186, -0.0361178, 1.36667, 1, -0.437167, 1.33095, -0.0361218, 1.4, 1, -0.0606959, 1.39628, -0.0361258, 1.43333, 1, 0.373125, 1.43892, -0.0361297, 1.46667, 1, 0.832976, 1.39157, -0.0361331, 1.5, 1, 1.2689, 1.18855, -0.0361359, 1.53333, 1, 1.5796, 0.844536, -0.0379446, 1.56667, 1, 1.80645, 0.517906, -0.0413787, 1.6, 1, 1.96098, 0.257885, -0.0453543, 1.63333, 1, 2.05286, 0.0925103, -0.0487875, 1.66667, 1, 2.08985, 0.0286366, -0.0505945, 1.7, 1, 2.06521, 0.0261518, -0.0487875, 1.73333, 1, 2.0184, 0.02112, -0.0453543, 1.76667, 1, 1.9642, 0.0138596, -0.0413791, 1.8, 1, 1.91739, 0.00444181, -0.0379459, 1.83333, 1, 1.89275, -0.00730995, -0.0361389, 1.86667, 1, 1.89275, -0.0213747, -0.0361389, 1.9, 1, 1.89275, -0.0360549, -0.0361389, 1.93333, 1, 1.89275, -0.051207, -0.0361389, 1.96667, 1, 1.89275, -0.0667014, -0.0361389, 2.06667, 1, 1.89275, -0.113985, -0.0361389, 2.1, 1, 1.89275, -0.129555, -0.0361389, 2.13333, 1, 1.89275, -0.144841, -0.0361389, 2.16667, 1, 1.89275, -0.159726, -0.0361389, 2.2, 1, 1.89275, -0.173791, -0.0361389, 2.23333, 1, 1.89275, -0.186949, -0.0361389, 2.26667, 1, 1.89275, -0.198912, -0.0361389, 2.3, 1, 1.89275, -0.209292, -0.0361389, 2.33333, 1, 1.89275, -0.217603, -0.0361389, 2.36667, 1, 1.89275, -0.221192, -0.0361389, 2.625, 1, 1.89275, -0.222089, -0.0361389)
tracks/49/type = "rotation_3d"
tracks/49/imported = true
tracks/49/enabled = true
tracks/49/path = NodePath("Armature/Skeleton3D:Finger.006")
tracks/49/interp = 1
tracks/49/loop_wrap = true
tracks/49/keys = PackedFloat32Array(0, 1, 0.00103154, 0.000415737, 0.743641, 0.668578, 0.0333333, 1, 0.00103045, 0.000418299, 0.745313, 0.666714, 0.0666667, 1, 0.00102787, 0.000424583, 0.749357, 0.662166, 0.1, 1, 0.00102381, 0.000434334, 0.755613, 0.655017, 0.133333, 1, 0.00101822, 0.000447335, 0.763898, 0.645336, 0.166667, 1, 0.00101104, 0.000463405, 0.774008, 0.633175, 0.2, 1, 0.00100173, 0.000483085, 0.786249, 0.617909, 0.233333, 1, 0.000990806, 0.000505146, 0.799728, 0.600361, 0.266667, 1, 0.000978138, 0.000529272, 0.814206, 0.580575, 0.3, 1, 0.000963627, 0.000555208, 0.829435, 0.558602, 0.333333, 1, 0.000947208, 0.000582764, 0.845168, 0.5345, 0.366667, 1, 0.000928625, 0.000611902, 0.861417, 0.507898, 0.4, 1, 0.000908095, 0.000641936, 0.877566, 0.479455, 0.433333, 1, 0.000886482, 0.000671729, 0.894921, 0.446223, 0.466667, 1, 0.00086468, 0.000699931, 0.914304, 0.405028, 0.5, 1, 0.000843453, 0.000724799, 0.93583, 0.352451, 0.533333, 1, 0.000826125, 0.000745505, 0.958662, 0.284544, 0.566667, 1, 0.000812199, 0.00076195, 0.978892, 0.204374, 0.6, 1, 0.000802153, 0.000773393, 0.993723, 0.111864, 0.633333, 1, 0.000796277, 0.000779039, 0.999972, 0.00738799, 0.666667, 1, -0.000794652, -0.000778073, -0.994144, 0.108059, 0.7, 1, -0.000804075, -0.000771719, -0.971734, 0.236076, 0.733333, 1, -0.000819487, -0.000758113, -0.929096, 0.369837, 0.766667, 1, -0.000840058, -0.000736316, -0.863144, 0.504957, 0.8, 1, -0.000864513, -0.000705483, -0.77176, 0.635913, 0.833333, 1, -0.000891263, -0.000665098, -0.654255, 0.756274, 0.866667, 1, -0.000928558, -0.000619347, -0.510677, 0.859772, 0.933333, 1, -0.000997408, -0.000505587, -0.171784, 0.985134, 0.966667, 1, -0.00102473, -0.000441761, 0.00824744, 0.999965, 1, 1, -0.00104598, -0.00037753, 0.183694, 0.982983, 1.03333, 1, -0.00106811, -0.000321734, 0.342051, 0.939681, 1.06667, 1, -0.00108224, -0.000273288, 0.479398, 0.877597, 1.1, 1, -0.00109005, -0.000234986, 0.593166, 0.805079, 1.13333, 1, -0.00109361, -0.000208855, 0.683203, 0.730228, 1.16667, 1, -0.0010947, -0.000196151, 0.751129, 0.660154, 1.2, 1, -0.00109445, -0.000201398, 0.795186, 0.606364, 1.23333, 1, -0.00109083, -0.000218125, 0.823062, 0.56795, 1.26667, 1, -0.00108467, -0.000245041, 0.837795, 0.545984, 1.3, 1, -0.00107643, -0.000279128, 0.841216, 0.540697, 1.33333, 1, -0.00106644, -0.000315638, 0.834013, 0.551744, 1.36667, 1, -0.00107697, -0.000282439, 0.775973, 0.630765, 1.4, 1, -0.00110659, -0.000158893, 0.644389, 0.764697, 1.43333, 1, -0.00111951, 3.67832e-05, 0.433508, 0.901149, 1.46667, 1, -0.00108359, 0.00027055, 0.161808, 0.986822, 1.5, 1, -0.000994569, 0.000497781, -0.123586, 0.992333, 1.53333, 1, -0.000885602, 0.000677347, -0.352395, 0.935851, 1.56667, 1, -0.000769775, 0.000804252, -0.513809, 0.857904, 1.6, 1, -0.000671659, 0.000886384, -0.615952, 0.787783, 1.63333, 1, -0.000605716, 0.000932664, -0.671353, 0.741137, 1.66667, 1, -0.00057983, 0.000949063, -0.691574, 0.722305, 2.625, 1, -0.00057979, 0.000949063, -0.691574, 0.722305)
tracks/50/type = "scale_3d"
tracks/50/imported = true
tracks/50/enabled = true
tracks/50/path = NodePath("Armature/Skeleton3D:Finger.006")
tracks/50/interp = 1
tracks/50/loop_wrap = true
tracks/50/keys = PackedFloat32Array(0, 1, 1, 1, 1, 1.5, 1, 1, 1, 1, 1.53333, 1, 1.05, 1.05, 1.05, 1.56667, 1, 1.145, 1.145, 1.145, 1.6, 1, 1.255, 1.255, 1.255, 1.63333, 1, 1.35, 1.35, 1.35, 1.7, 1, 1.35, 1.35, 1.35, 1.73333, 1, 1.255, 1.255, 1.255, 1.76667, 1, 1.145, 1.145, 1.145, 1.8, 1, 1.05, 1.05, 1.05, 1.83333, 1, 1, 1, 1, 2.625, 1, 1, 1, 1)
tracks/51/type = "position_3d"
tracks/51/imported = true
tracks/51/enabled = true
tracks/51/path = NodePath("Armature/Skeleton3D:Finger.003")
tracks/51/interp = 1
tracks/51/loop_wrap = true
tracks/51/keys = PackedFloat32Array(0, 1, 0.345317, -0.176812, -0.136023, 0.0333333, 1, 0.345696, -0.171495, -0.136023, 0.0666667, 1, 0.346572, -0.158766, -0.136023, 0.1, 1, 0.347833, -0.139124, -0.136023, 0.133333, 1, 0.34935, -0.113035, -0.136023, 0.166667, 1, 0.350971, -0.0809333, -0.136023, 0.2, 1, 0.352368, -0.0415987, -0.136023, 0.233333, 1, 0.353302, 0.0026094, -0.136023, 0.266667, 1, 0.353568, 0.0513336, -0.136023, 0.3, 1, 0.352967, 0.104216, -0.136023, 0.333333, 1, 0.351301, 0.160899, -0.136023, 0.366667, 1, 0.347857, 0.221918, -0.136023, 0.4, 1, 0.342853, 0.285628, -0.136023, 0.433333, 1, 0.331757, 0.352441, -0.136023, 0.466667, 1, 0.312005, 0.422631, -0.136023, 0.5, 1, 0.282999, 0.496331, -0.136023, 0.533333, 1, 0.245421, 0.573046, -0.136023, 0.566667, 1, 0.202255, 0.650717, -0.136023, 0.6, 1, 0.153836, 0.728459, -0.136023, 0.633333, 1, 0.100509, 0.805395, -0.136023, 0.733333, 1, -0.0863184, 1.01963, -0.136023, 0.766667, 1, -0.154527, 1.0827, -0.136023, 0.8, 1, -0.22425, 1.14086, -0.136023, 0.833333, 1, -0.294713, 1.19373, -0.136023, 0.866667, 1, -0.364564, 1.23919, -0.136023, 0.9, 1, -0.4328, 1.27908, -0.136023, 0.933333, 1, -0.498724, 1.31355, -0.136023, 0.966667, 1, -0.5617, 1.3428, -0.136023, 1, 1, -0.621147, 1.36709, -0.136023, 1.03333, 1, -0.674995, 1.38537, -0.136023, 1.06667, 1, -0.724176, 1.39958, -0.136023, 1.1, 1, -0.768472, 1.40989, -0.136023, 1.13333, 1, -0.807668, 1.41639, -0.136023, 1.16667, 1, -0.841547, 1.41905, -0.136023, 1.2, 1, -0.867377, 1.41581, -0.136023, 1.23333, 1, -0.884383, 1.40667, -0.136023, 1.26667, 1, -0.866541, 1.39033, -0.136023, 1.3, 1, -0.784376, 1.36631, -0.136023, 1.33333, 1, -0.604955, 1.33488, -0.136023, 1.36667, 1, -0.297018, 1.33183, -0.136023, 1.4, 1, 0.0685842, 1.35223, -0.136023, 1.43333, 1, 0.46745, 1.34466, -0.136023, 1.46667, 1, 0.867689, 1.26041, -0.136023, 1.5, 1, 1.22992, 1.05349, -0.136023, 1.53333, 1, 1.48303, 0.737616, -0.142824, 1.56667, 1, 1.66683, 0.443817, -0.155746, 1.6, 1, 1.79118, 0.214322, -0.170709, 1.63333, 1, 1.86443, 0.0714255, -0.183631, 1.66667, 1, 1.89347, 0.0174944, -0.190432, 1.7, 1, 1.87585, 0.0154075, -0.183631, 1.73333, 1, 1.84236, 0.0111317, -0.170709, 1.76667, 1, 1.80359, 0.00474682, -0.155746, 1.8, 1, 1.7701, -0.00391489, -0.142824, 1.83333, 1, 1.75248, -0.0152687, -0.136023, 1.86667, 1, 1.75248, -0.0293334, -0.136023, 1.9, 1, 1.75248, -0.0440136, -0.136023, 1.93333, 1, 1.75248, -0.0591658, -0.136023, 1.96667, 1, 1.75248, -0.0746602, -0.136023, 2.06667, 1, 1.75248, -0.121944, -0.136023, 2.1, 1, 1.75248, -0.137514, -0.136023, 2.13333, 1, 1.75248, -0.152799, -0.136023, 2.16667, 1, 1.75248, -0.167685, -0.136023, 2.2, 1, 1.75248, -0.181749, -0.136023, 2.23333, 1, 1.75248, -0.194908, -0.136023, 2.26667, 1, 1.75248, -0.206871, -0.136023, 2.3, 1, 1.75248, -0.217251, -0.136023, 2.33333, 1, 1.75248, -0.225561, -0.136023, 2.36667, 1, 1.75248, -0.229151, -0.136023, 2.625, 1, 1.75248, -0.230048, -0.136023)
tracks/52/type = "rotation_3d"
tracks/52/imported = true
tracks/52/enabled = true
tracks/52/path = NodePath("Armature/Skeleton3D:Finger.003")
tracks/52/interp = 1
tracks/52/loop_wrap = true
tracks/52/keys = PackedFloat32Array(0, 1, -0.0294545, 0.0109701, -0.999478, 0.00745659, 0.0333333, 1, -0.0294819, 0.0108963, -0.999456, 0.00995819, 0.0666667, 1, -0.0295477, 0.0107166, -0.999377, 0.0160413, 0.1, 1, -0.0296481, 0.0104355, -0.99918, 0.0255343, 0.133333, 1, -0.0297787, 0.0100569, -0.998773, 0.0382636, 0.166667, 1, -0.029934, 0.00958474, -0.998043, 0.0540528, 0.2, 1, -0.0301158, 0.00899715, -0.996795, 0.0735713, 0.233333, 1, -0.0303075, 0.00832853, -0.994923, 0.0956087, 0.266667, 1, -0.0305026, 0.00758305, -0.99228, 0.11997, 0.3, 1, -0.0306944, 0.00676495, -0.988718, 0.146454, 0.333333, 1, -0.0308764, 0.00587868, -0.984094, 0.174848, 0.366667, 1, -0.0310446, 0.00491354, -0.978166, 0.205435, 0.4, 1, -0.0311887, 0.0038957, -0.970924, 0.237314, 0.433333, 1, -0.0313055, 0.00282795, -0.961648, 0.272479, 0.466667, 1, -0.0313887, 0.00171803, -0.94931, 0.312764, 0.5, 1, -0.0314257, 0.00057935, -0.932506, 0.359784, 0.533333, 1, -0.031435, -0.000556073, -0.909331, 0.414884, 0.566667, 1, -0.0314082, -0.00166161, -0.879581, 0.474709, 0.6, 1, -0.0313432, -0.00271905, -0.842233, 0.538194, 0.633333, 1, -0.0312378, -0.00370994, -0.79631, 0.60407, 0.666667, 1, -0.0310903, -0.00461563, -0.740924, 0.670853, 0.7, 1, -0.0309977, -0.00539683, -0.67392, 0.738134, 0.733333, 1, -0.0309017, -0.00606273, -0.596229, 0.802196, 0.766667, 1, -0.0308005, -0.00660477, -0.507866, 0.86086, 0.8, 1, -0.0306912, -0.00701703, -0.409307, 0.911854, 0.833333, 1, -0.0305722, -0.00729728, -0.30154, 0.952935, 0.866667, 1, -0.0305925, -0.00745327, -0.185581, 0.982124, 0.9, 1, -0.0306094, -0.00751596, -0.0653998, 0.997361, 0.933333, 1, -0.0306102, -0.00750833, 0.0559276, 0.997937, 0.966667, 1, -0.0305887, -0.00745925, 0.175131, 0.984042, 1, 1, -0.0305467, -0.00740341, 0.288989, 0.956816, 1.03333, 1, -0.0305873, -0.0074371, 0.39211, 0.91938, 1.06667, 1, -0.030569, -0.00755574, 0.483552, 0.874749, 1.1, 1, -0.0304969, -0.0077809, 0.562292, 0.82634, 1.13333, 1, -0.0303812, -0.0081275, 0.62806, 0.777529, 1.16667, 1, -0.0302309, -0.0086031, 0.681178, 0.731443, 1.2, 1, -0.0300535, -0.00923573, 0.719198, 0.694094, 1.23333, 1, -0.0298239, -0.00993382, 0.746004, 0.665199, 1.26667, 1, -0.0295584, -0.0106806, 0.76307, 0.645551, 1.3, 1, -0.0292798, -0.0114232, 0.77115, 0.635877, 1.33333, 1, -0.0290201, -0.0120725, 0.770275, 0.636937, 1.36667, 1, -0.0295818, -0.0106726, 0.715944, 0.697449, 1.4, 1, -0.0307948, -0.00668748, 0.588031, 0.808224, 1.43333, 1, -0.0315378, -0.000609613, 0.385802, 0.922042, 1.46667, 1, -0.0307994, 0.00659662, 0.128537, 0.991204, 1.5, 1, -0.0283403, 0.0135919, -0.140233, 0.98962, 1.53333, 1, -0.0250929, 0.0189935, -0.356942, 0.933596, 1.56667, 1, -0.0217163, 0.0227448, -0.512474, 0.858127, 1.6, 1, -0.0188965, 0.0251164, -0.612888, 0.789545, 1.63333, 1, -0.0170292, 0.0264182, -0.668424, 0.743116, 1.66667, 1, -0.0162927, 0.0268786, -0.688701, 0.724364, 2.625, 1, -0.0162927, 0.0268786, -0.688701, 0.724364)
tracks/53/type = "scale_3d"
tracks/53/imported = true
tracks/53/enabled = true
tracks/53/path = NodePath("Armature/Skeleton3D:Finger.003")
tracks/53/interp = 1
tracks/53/loop_wrap = true
tracks/53/keys = PackedFloat32Array(0, 1, 1, 1, 1, 1.5, 1, 1, 1, 1, 1.53333, 1, 1.05, 1.05, 1.05, 1.56667, 1, 1.145, 1.145, 1.145, 1.6, 1, 1.255, 1.255, 1.255, 1.63333, 1, 1.35, 1.35, 1.35, 1.7, 1, 1.35, 1.35, 1.35, 1.73333, 1, 1.255, 1.255, 1.255, 1.76667, 1, 1.145, 1.145, 1.145, 1.8, 1, 1.05, 1.05, 1.05, 1.83333, 1, 1, 1, 1, 2.625, 1, 1, 1, 1)
tracks/54/type = "position_3d"
tracks/54/imported = true
tracks/54/enabled = true
tracks/54/path = NodePath("Armature/Skeleton3D:Finger.007")
tracks/54/interp = 1
tracks/54/loop_wrap = true
tracks/54/keys = PackedFloat32Array(0, 1, 0.346843, -0.283779, -0.138416, 0.0333333, 1, 0.347757, -0.278453, -0.138416, 0.0666667, 1, 0.349935, -0.265688, -0.138416, 0.1, 1, 0.353227, -0.245955, -0.138416, 0.133333, 1, 0.357464, -0.219692, -0.138416, 0.166667, 1, 0.362454, -0.187294, -0.138416, 0.2, 1, 0.368002, -0.147408, -0.138416, 0.233333, 1, 0.373597, -0.102383, -0.138416, 0.266667, 1, 0.378982, -0.0525263, -0.138416, 0.3, 1, 0.383898, 0.00185309, -0.138416, 0.333333, 1, 0.388087, 0.0604436, -0.138416, 0.366667, 1, 0.39081, 0.123998, -0.138416, 0.4, 1, 0.392097, 0.190758, -0.138416, 0.433333, 1, 0.387748, 0.261433, -0.13843, 0.466667, 1, 0.375453, 0.336666, -0.138472, 0.5, 1, 0.354796, 0.417028, -0.138558, 0.533333, 1, 0.326023, 0.503008, -0.138705, 0.566667, 1, 0.291307, 0.592154, -0.138898, 0.6, 1, 0.250414, 0.683644, -0.139139, 0.633333, 1, 0.203087, 0.776577, -0.139425, 0.666667, 1, 0.149041, 0.869976, -0.139754, 0.7, 1, 0.085649, 0.961683, -0.140134, 0.833333, 1, -0.23317, 1.28108, -0.141787, 0.866667, 1, -0.32583, 1.33793, -0.142136, 0.9, 1, -0.41897, 1.3838, -0.142416, 0.933333, 1, -0.510465, 1.41852, -0.142613, 0.966667, 1, -0.598241, 1.44237, -0.142721, 1, 1, -0.680274, 1.45602, -0.142736, 1.03333, 1, -0.751658, 1.4588, -0.142645, 1.06667, 1, -0.81399, 1.45591, -0.142505, 1.1, 1, -0.867308, 1.44883, -0.142337, 1.13333, 1, -0.911864, 1.43874, -0.142165, 1.16667, 1, -0.948121, 1.42656, -0.142009, 1.2, 1, -0.973997, 1.41193, -0.141906, 1.23333, 1, -0.990424, 1.39439, -0.141854, 1.26667, 1, -0.971872, 1.37254, -0.141851, 1.3, 1, -0.889246, 1.34587, -0.141892, 1.33333, 1, -0.709877, 1.31473, -0.141969, 1.36667, 1, -0.40346, 1.32898, -0.142069, 1.4, 1, -0.0300524, 1.38416, -0.142173, 1.43333, 1, 0.395163, 1.41547, -0.142272, 1.46667, 1, 0.840854, 1.35986, -0.142361, 1.5, 1, 1.25954, 1.15612, -0.142433, 1.53333, 1, 1.55668, 0.8186, -0.149603, 1.56667, 1, 1.77324, 0.499582, -0.163166, 1.6, 1, 1.92045, 0.246701, -0.17885, 1.63333, 1, 2.00776, 0.0866362, -0.192389, 1.66667, 1, 2.04281, 0.0251123, -0.199514, 1.7, 1, 2.01985, 0.0227533, -0.192389, 1.73333, 1, 1.97623, 0.0179606, -0.17885, 1.76667, 1, 1.92572, 0.0109772, -0.163174, 1.8, 1, 1.88211, 0.00179853, -0.149636, 1.83333, 1, 1.85915, -0.00982735, -0.14251, 1.86667, 1, 1.85915, -0.0238921, -0.14251, 1.9, 1, 1.85915, -0.0385723, -0.14251, 1.93333, 1, 1.85915, -0.0537244, -0.14251, 1.96667, 1, 1.85915, -0.0692188, -0.14251, 2.06667, 1, 1.85915, -0.116503, -0.14251, 2.1, 1, 1.85915, -0.132072, -0.14251, 2.13333, 1, 1.85915, -0.147358, -0.14251, 2.16667, 1, 1.85915, -0.162243, -0.14251, 2.2, 1, 1.85915, -0.176308, -0.14251, 2.23333, 1, 1.85915, -0.189466, -0.14251, 2.26667, 1, 1.85915, -0.201429, -0.14251, 2.3, 1, 1.85915, -0.211809, -0.14251, 2.33333, 1, 1.85915, -0.22012, -0.14251, 2.36667, 1, 1.85915, -0.223709, -0.14251, 2.625, 1, 1.85915, -0.224607, -0.14251)
tracks/55/type = "rotation_3d"
tracks/55/imported = true
tracks/55/enabled = true
tracks/55/path = NodePath("Armature/Skeleton3D:Finger.007")
tracks/55/interp = 1
tracks/55/loop_wrap = true
tracks/55/keys = PackedFloat32Array(0, 1, 0.0290861, 0.0119125, 0.739595, 0.672318, 0.0333333, 1, 0.0290562, 0.0119853, 0.741275, 0.670465, 0.0666667, 1, 0.0289827, 0.012162, 0.745342, 0.665941, 0.1, 1, 0.0288659, 0.0124367, 0.751635, 0.65883, 0.133333, 1, 0.0287051, 0.0128035, 0.759969, 0.649199, 0.166667, 1, 0.028499, 0.0132558, 0.77014, 0.6371, 0.2, 1, 0.0282342, 0.013811, 0.782458, 0.621909, 0.233333, 1, 0.0279217, 0.0144324, 0.796028, 0.604444, 0.266667, 1, 0.0275595, 0.0151125, 0.810606, 0.584748, 0.3, 1, 0.0271456, 0.0158438, 0.825948, 0.562869, 0.333333, 1, 0.0266784, 0.0166184, 0.841805, 0.538865, 0.366667, 1, 0.0261475, 0.0174418, 0.858192, 0.512366, 0.4, 1, 0.0255631, 0.0182876, 0.874489, 0.484027, 0.433333, 1, 0.0249476, 0.0191252, 0.892018, 0.450906, 0.466667, 1, 0.0243264, 0.0199179, 0.911616, 0.409839, 0.5, 1, 0.0237236, 0.0206179, 0.93342, 0.357407, 0.533333, 1, 0.0232294, 0.0211997, 0.956613, 0.289659, 0.566667, 1, 0.0228331, 0.0216613, 0.977272, 0.209641, 0.6, 1, 0.0225475, 0.0219835, 0.992602, 0.11726, 0.633333, 1, 0.0223809, 0.0221431, 0.999421, 0.0128711, 0.666667, 1, -0.0223356, -0.022114, -0.994231, 0.102549, 0.7, 1, -0.0226035, -0.0219352, -0.972533, 0.230624, 0.733333, 1, -0.0230417, -0.0215531, -0.93065, 0.364549, 0.766667, 1, -0.0236267, -0.0209397, -0.865472, 0.499962, 0.8, 1, -0.0243234, -0.0200712, -0.774855, 0.631352, 0.833333, 1, -0.0250876, -0.0189347, -0.658072, 0.752299, 0.866667, 1, -0.0261454, -0.0176474, -0.515133, 0.85653, 0.933333, 1, -0.0281112, -0.0144435, -0.177102, 0.983685, 0.966667, 1, -0.0288938, -0.0126443, 0.00275625, 0.999499, 1, 1, -0.0295055, -0.0108321, 0.178207, 0.983491, 1.03333, 1, -0.0301358, -0.0092588, 0.33672, 0.941077, 1.06667, 1, -0.030544, -0.00789129, 0.474337, 0.879778, 1.1, 1, -0.0307722, -0.00681003, 0.588446, 0.807922, 1.13333, 1, -0.0308765, -0.00607268, 0.678848, 0.733604, 1.16667, 1, -0.0309074, -0.00571368, 0.747125, 0.66394, 1.2, 1, -0.0308989, -0.00586185, 0.791454, 0.610419, 1.23333, 1, -0.0307941, -0.00633293, 0.819527, 0.572177, 1.26667, 1, -0.0306159, -0.00709204, 0.834373, 0.550303, 1.3, 1, -0.0303776, -0.00805441, 0.837822, 0.545038, 1.33333, 1, -0.0300894, -0.00908534, 0.830561, 0.556039, 1.36667, 1, -0.0303916, -0.00814877, 0.772117, 0.634702, 1.4, 1, -0.0312483, -0.00466253, 0.639862, 0.76784, 1.43333, 1, -0.0316449, 0.000865293, 0.42834, 0.903063, 1.46667, 1, -0.0306664, 0.00747779, 0.156311, 0.987203, 1.5, 1, -0.0281838, 0.0139134, -0.128968, 0.991151, 1.53333, 1, -0.0251339, 0.0190051, -0.35735, 0.933439, 1.56667, 1, -0.0218806, 0.0226091, -0.518255, 0.854647, 1.6, 1, -0.0191216, 0.0249455, -0.619962, 0.784002, 1.63333, 1, -0.0172659, 0.026264, -0.675078, 0.737077, 1.66667, 1, -0.0165338, 0.026731, -0.695186, 0.718143, 2.625, 1, -0.0165338, 0.026731, -0.695186, 0.718143)
tracks/56/type = "scale_3d"
tracks/56/imported = true
tracks/56/enabled = true
tracks/56/path = NodePath("Armature/Skeleton3D:Finger.007")
tracks/56/interp = 1
tracks/56/loop_wrap = true
tracks/56/keys = PackedFloat32Array(0, 1, 1, 1, 1, 1.5, 1, 1, 1, 1, 1.53333, 1, 1.05, 1.05, 1.05, 1.56667, 1, 1.145, 1.145, 1.145, 1.6, 1, 1.255, 1.255, 1.255, 1.63333, 1, 1.35, 1.35, 1.35, 1.7, 1, 1.35, 1.35, 1.35, 1.73333, 1, 1.255, 1.255, 1.255, 1.76667, 1, 1.145, 1.145, 1.145, 1.8, 1, 1.05, 1.05, 1.05, 1.83333, 1, 1, 1, 1, 2.625, 1, 1, 1, 1)
tracks/57/type = "position_3d"
tracks/57/imported = true
tracks/57/enabled = true
tracks/57/path = NodePath("Armature/Skeleton3D:Thumb")
tracks/57/interp = 1
tracks/57/loop_wrap = true
tracks/57/keys = PackedFloat32Array(0, 1, 0.101677, -0.212082, 0.135248, 0.0333333, 1, 0.102237, -0.207984, 0.135248, 0.0666667, 1, 0.103583, -0.198215, 0.135248, 0.1, 1, 0.105654, -0.183179, 0.135248, 0.133333, 1, 0.108377, -0.163246, 0.135248, 0.166667, 1, 0.111676, -0.138747, 0.135248, 0.2, 1, 0.115564, -0.108718, 0.135248, 0.233333, 1, 0.119747, -0.074905, 0.135248, 0.266667, 1, 0.124119, -0.0375244, 0.135248, 0.3, 1, 0.128574, 0.00321307, 0.135248, 0.333333, 1, 0.133001, 0.047104, 0.135248, 0.366667, 1, 0.137174, 0.0948037, 0.135248, 0.4, 1, 0.140998, 0.145062, 0.135248, 0.433333, 1, 0.140355, 0.198029, 0.135248, 0.466667, 1, 0.133121, 0.253834, 0.135248, 0.5, 1, 0.119164, 0.312585, 0.135248, 0.533333, 1, 0.100114, 0.374676, 0.135248, 0.566667, 1, 0.0780149, 0.438767, 0.135248, 0.6, 1, 0.0530457, 0.504477, 0.135248, 0.633333, 1, 0.0253401, 0.571407, 0.135248, 0.666667, 1, -0.00501159, 0.639138, 0.135248, 0.833333, 1, -0.194181, 0.969015, 0.135248, 0.866667, 1, -0.237165, 1.0291, 0.135248, 0.9, 1, -0.280712, 1.08639, 0.135248, 0.933333, 1, -0.324437, 1.14063, 0.135248, 0.966667, 1, -0.367954, 1.19159, 0.135248, 1, 1, -0.410872, 1.23907, 0.135248, 1.03333, 1, -0.452216, 1.2815, 0.135248, 1.06667, 1, -0.491804, 1.31981, 0.135248, 1.1, 1, -0.529228, 1.3537, 0.135248, 1.13333, 1, -0.564027, 1.38277, 0.135248, 1.16667, 1, -0.595687, 1.40653, 0.135248, 1.2, 1, -0.621434, 1.42156, 0.135248, 1.23333, 1, -0.639276, 1.42784, 0.135248, 1.26667, 1, -0.622769, 1.42404, 0.135248, 1.3, 1, -0.541981, 1.40923, 0.135248, 1.33333, 1, -0.363495, 1.38286, 0.135248, 1.36667, 1, -0.0518989, 1.3482, 0.135248, 1.4, 1, 0.302173, 1.29608, 0.135248, 1.43333, 1, 0.653036, 1.20053, 0.135248, 1.46667, 1, 0.963834, 1.04222, 0.135248, 1.5, 1, 1.20653, 0.808427, 0.135248, 1.53333, 1, 1.35564, 0.519108, 0.142011, 1.56667, 1, 1.45579, 0.26415, 0.15486, 1.6, 1, 1.51762, 0.0746515, 0.169737, 1.63333, 1, 1.5504, -0.0369665, 0.182585, 1.66667, 1, 1.56201, -0.0769559, 0.189348, 1.7, 1, 1.55623, -0.0756695, 0.182585, 1.73333, 1, 1.54523, -0.0735362, 0.169737, 1.76667, 1, 1.5325, -0.0725, 0.15486, 1.8, 1, 1.52151, -0.0747526, 0.142011, 1.83333, 1, 1.51572, -0.0827332, 0.135248, 1.86667, 1, 1.51572, -0.0967979, 0.135248, 1.9, 1, 1.51572, -0.111478, 0.135248, 1.93333, 1, 1.51572, -0.12663, 0.135248, 1.96667, 1, 1.51572, -0.142125, 0.135248, 2.06667, 1, 1.51572, -0.189409, 0.135248, 2.1, 1, 1.51572, -0.204978, 0.135248, 2.13333, 1, 1.51572, -0.220264, 0.135248, 2.16667, 1, 1.51572, -0.235149, 0.135248, 2.2, 1, 1.51572, -0.249214, 0.135248, 2.23333, 1, 1.51572, -0.262372, 0.135248, 2.26667, 1, 1.51572, -0.274335, 0.135248, 2.3, 1, 1.51572, -0.284715, 0.135248, 2.33333, 1, 1.51572, -0.293026, 0.135248, 2.36667, 1, 1.51572, -0.296615, 0.135248, 2.625, 1, 1.51572, -0.297512, 0.135248)
tracks/58/type = "rotation_3d"
tracks/58/imported = true
tracks/58/enabled = true
tracks/58/path = NodePath("Armature/Skeleton3D:Thumb")
tracks/58/interp = 1
tracks/58/loop_wrap = true
tracks/58/keys = PackedFloat32Array(0, 1, 0.789359, 0.380841, -0.464742, 0.126044, 0.0333333, 1, 0.788404, 0.382816, -0.464425, 0.127207, 0.0666667, 1, 0.786059, 0.387607, -0.463642, 0.130031, 0.1, 1, 0.782341, 0.395057, -0.462386, 0.13443, 0.133333, 1, 0.777244, 0.404993, -0.460635, 0.14031, 0.166667, 1, 0.770743, 0.417234, -0.458359, 0.147577, 0.2, 1, 0.762431, 0.432235, -0.455384, 0.156518, 0.233333, 1, 0.752681, 0.448999, -0.451809, 0.166555, 0.266667, 1, 0.741447, 0.467316, -0.44759, 0.177581, 0.3, 1, 0.72869, 0.48697, -0.442683, 0.189483, 0.333333, 1, 0.714373, 0.507739, -0.437046, 0.202145, 0.366667, 1, 0.698201, 0.529758, -0.430534, 0.215668, 0.4, 1, 0.680503, 0.552308, -0.42325, 0.229633, 0.433333, 1, 0.66107, 0.575439, -0.414674, 0.244747, 0.466667, 1, 0.639753, 0.599068, -0.404209, 0.261627, 0.5, 1, 0.616466, 0.622975, -0.391187, 0.280793, 0.533333, 1, 0.591545, 0.646743, -0.374603, 0.302441, 0.566667, 1, 0.56564, 0.669592, -0.354866, 0.325218, 0.6, 1, 0.539102, 0.691191, -0.331777, 0.348638, 0.633333, 1, 0.512307, 0.711258, -0.305173, 0.372188, 0.666667, 1, 0.485651, 0.729569, -0.27493, 0.39533, 0.7, 1, 0.460373, 0.745962, -0.239953, 0.417158, 0.766667, 1, 0.413841, 0.772943, -0.159282, 0.453789, 0.866667, 1, 0.35843, 0.800076, -0.0163234, 0.48077, 0.933333, 1, 0.331193, 0.811884, 0.0844034, 0.473319, 0.966667, 1, 0.318989, 0.816591, 0.133098, 0.462287, 1, 1, 0.307051, 0.820882, 0.179354, 0.446883, 1.03333, 1, 0.294498, 0.825707, 0.220847, 0.427441, 1.06667, 1, 0.280494, 0.830621, 0.257796, 0.40612, 1.1, 1, 0.264743, 0.8357, 0.289932, 0.384, 1.13333, 1, 0.247094, 0.840967, 0.317139, 0.362135, 1.16667, 1, 0.227533, 0.846379, 0.339449, 0.341536, 1.2, 1, 0.20611, 0.851895, 0.355706, 0.324449, 1.23333, 1, 0.184294, 0.856851, 0.36759, 0.310997, 1.26667, 1, 0.163945, 0.860959, 0.376048, 0.300765, 1.3, 1, 0.147624, 0.863907, 0.381672, 0.293596, 1.33333, 1, 0.1386, 0.8654, 0.384715, 0.289596, 1.36667, 1, 0.19425, 0.854631, 0.365191, 0.313859, 1.4, 1, 0.320572, 0.815697, 0.313835, 0.365212, 1.43333, 1, 0.486165, 0.729227, 0.229304, 0.423428, 1.46667, 1, 0.650757, 0.587063, 0.117981, 0.466854, 1.5, 1, 0.776999, 0.405464, -0.00264484, 0.481524, 1.53333, 1, 0.845059, 0.232384, -0.104189, 0.470124, 1.56667, 1, 0.871919, 0.0888005, -0.180825, 0.44629, 1.6, 1, 0.876287, -0.0157747, -0.232738, 0.421551, 1.63333, 1, 0.872847, -0.0791546, -0.262637, 0.403601, 1.66667, 1, 0.870327, -0.103241, -0.273689, 0.39619, 2.625, 1, 0.870327, -0.103241, -0.273689, 0.39619)
tracks/59/type = "scale_3d"
tracks/59/imported = true
tracks/59/enabled = true
tracks/59/path = NodePath("Armature/Skeleton3D:Thumb")
tracks/59/interp = 1
tracks/59/loop_wrap = true
tracks/59/keys = PackedFloat32Array(0, 1, 1, 1, 1, 1.5, 1, 1, 1, 1, 1.53333, 1, 1.05, 1.05, 1.05, 1.56667, 1, 1.145, 1.145, 1.145, 1.6, 1, 1.255, 1.255, 1.255, 1.63333, 1, 1.35, 1.35, 1.35, 1.7, 1, 1.35, 1.35, 1.35, 1.73333, 1, 1.255, 1.255, 1.255, 1.76667, 1, 1.145, 1.145, 1.145, 1.8, 1, 1.05, 1.05, 1.05, 1.83333, 1, 1, 1, 1, 2.625, 1, 1, 1, 1)
tracks/60/type = "position_3d"
tracks/60/imported = true
tracks/60/enabled = true
tracks/60/path = NodePath("Armature/Skeleton3D:Thumb.001")
tracks/60/interp = 1
tracks/60/loop_wrap = true
tracks/60/keys = PackedFloat32Array(0, 1, 0.179856, -0.285881, 0.118381, 0.0333333, 1, 0.180784, -0.281391, 0.118381, 0.0666667, 1, 0.183016, -0.270657, 0.118381, 0.1, 1, 0.186443, -0.254095, 0.118381, 0.133333, 1, 0.190945, -0.232078, 0.118381, 0.166667, 1, 0.19639, -0.204942, 0.118381, 0.2, 1, 0.202787, -0.171536, 0.118381, 0.233333, 1, 0.209645, -0.13379, 0.118381, 0.266667, 1, 0.216785, -0.0919269, 0.118381, 0.3, 1, 0.224023, -0.046164, 0.118381, 0.333333, 1, 0.231174, 0.0032829, 0.118381, 0.366667, 1, 0.237838, 0.0572035, 0.118381, 0.4, 1, 0.24387, 0.114133, 0.118381, 0.433333, 1, 0.245135, 0.174335, 0.118527, 0.466667, 1, 0.239465, 0.238031, 0.118974, 0.5, 1, 0.226658, 0.30539, 0.119882, 0.533333, 1, 0.207936, 0.3768, 0.121456, 0.566667, 1, 0.185393, 0.450533, 0.123568, 0.6, 1, 0.159134, 0.526054, 0.126243, 0.633333, 1, 0.129239, 0.602798, 0.129507, 0.666667, 1, 0.0957609, 0.680177, 0.133379, 0.8, 1, -0.0741053, 0.978053, 0.155765, 0.833333, 1, -0.122731, 1.04639, 0.162655, 0.866667, 1, -0.173104, 1.10977, 0.169905, 0.9, 1, -0.223928, 1.16897, 0.177171, 0.933333, 1, -0.274626, 1.22384, 0.184261, 0.966667, 1, -0.324643, 1.27431, 0.190985, 1, 1, -0.373457, 1.32037, 0.197158, 1.03333, 1, -0.419814, 1.36075, 0.202322, 1.06667, 1, -0.463846, 1.39695, 0.206635, 1.1, 1, -0.505276, 1.42891, 0.210108, 1.13333, 1, -0.543783, 1.45638, 0.212775, 1.16667, 1, -0.579005, 1.479, 0.214693, 1.2, 1, -0.608331, 1.49357, 0.21576, 1.23333, 1, -0.629789, 1.49982, 0.216279, 1.26667, 1, -0.616666, 1.49621, 0.216447, 1.3, 1, -0.538612, 1.4816, 0.216447, 1.33333, 1, -0.361638, 1.45529, 0.216447, 1.36667, 1, -0.0407316, 1.41964, 0.216447, 1.4, 1, 0.333416, 1.35951, 0.216447, 1.43333, 1, 0.706431, 1.24448, 0.216447, 1.46667, 1, 1.0327, 1.0557, 0.216447, 1.5, 1, 1.27538, 0.78585, 0.216447, 1.53333, 1, 1.41013, 0.468389, 0.227269, 1.56667, 1, 1.49371, 0.191926, 0.247832, 1.6, 1, 1.54001, -0.0129312, 0.271641, 1.63333, 1, 1.56119, -0.134144, 0.292204, 1.66667, 1, 1.56767, -0.178233, 0.303026, 1.7, 1, 1.56169, -0.17333, 0.292204, 1.73333, 1, 1.55031, -0.164324, 0.271641, 1.76667, 1, 1.53713, -0.155331, 0.247832, 1.8, 1, 1.52576, -0.150711, 0.227269, 1.83333, 1, 1.51977, -0.155074, 0.216447, 1.86667, 1, 1.51977, -0.169139, 0.216447, 1.9, 1, 1.51977, -0.183819, 0.216447, 1.93333, 1, 1.51977, -0.198971, 0.216447, 1.96667, 1, 1.51977, -0.214466, 0.216447, 2.06667, 1, 1.51977, -0.26175, 0.216447, 2.1, 1, 1.51977, -0.277319, 0.216447, 2.13333, 1, 1.51977, -0.292605, 0.216447, 2.16667, 1, 1.51977, -0.30749, 0.216447, 2.2, 1, 1.51977, -0.321555, 0.216447, 2.23333, 1, 1.51977, -0.334713, 0.216447, 2.26667, 1, 1.51977, -0.346676, 0.216447, 2.3, 1, 1.51977, -0.357056, 0.216447, 2.33333, 1, 1.51977, -0.365367, 0.216447, 2.36667, 1, 1.51977, -0.368956, 0.216447, 2.625, 1, 1.51977, -0.369853, 0.216447)
tracks/61/type = "rotation_3d"
tracks/61/imported = true
tracks/61/enabled = true
tracks/61/path = NodePath("Armature/Skeleton3D:Thumb.001")
tracks/61/interp = 1
tracks/61/loop_wrap = true
tracks/61/keys = PackedFloat32Array(0, 1, -0.566398, -0.668821, 0.47585, 0.0737495, 0.0333333, 1, -0.564722, -0.670236, 0.476033, 0.0725582, 0.0666667, 1, -0.560633, -0.673661, 0.476466, 0.0696594, 0.1, 1, -0.554208, -0.678957, 0.477106, 0.06513, 0.133333, 1, -0.545512, -0.685963, 0.477897, 0.0590455, 0.166667, 1, -0.534596, -0.694504, 0.478771, 0.051481, 0.2, 1, -0.520904, -0.704831, 0.479687, 0.042103, 0.233333, 1, -0.505181, -0.716184, 0.480501, 0.0314789, 0.266667, 1, -0.487472, -0.728354, 0.481128, 0.01969, 0.3, 1, -0.467827, -0.741125, 0.481482, 0.0068212, 0.333333, 1, 0.446304, 0.754282, -0.481479, 0.00703856, 0.366667, 1, 0.422578, 0.767826, -0.481026, 0.0220419, 0.4, 1, 0.397242, 0.781235, -0.480048, 0.037761, 0.433333, 1, 0.370732, 0.794178, -0.478295, 0.0554417, 0.466667, 1, 0.343659, 0.80629, -0.475368, 0.0762863, 0.5, 1, 0.316804, 0.817168, -0.470723, 0.101449, 0.533333, 1, 0.291664, 0.826564, -0.462949, 0.131916, 0.566667, 1, 0.268604, 0.834464, -0.451696, 0.165809, 0.6, 1, 0.248204, 0.840844, -0.43626, 0.202618, 0.633333, 1, 0.231019, 0.845692, -0.415937, 0.241728, 0.666667, 1, 0.217574, 0.848993, -0.390025, 0.282405, 0.7, 1, 0.210225, 0.851165, -0.355813, 0.323606, 0.733333, 1, 0.207808, 0.852018, -0.314496, 0.36328, 0.766667, 1, 0.210461, 0.851462, -0.265976, 0.39997, 0.8, 1, 0.218178, 0.849346, -0.21042, 0.432126, 0.833333, 1, 0.230806, 0.845492, -0.148287, 0.45813, 0.866667, 1, 0.249531, 0.840711, -0.0796658, 0.473913, 0.9, 1, 0.271342, 0.834237, -0.00842914, 0.479949, 0.966667, 1, 0.319184, 0.816789, 0.133004, 0.461831, 1, 1, 0.34249, 0.806739, 0.198636, 0.438652, 1.03333, 1, 0.362406, 0.798483, 0.255515, 0.407184, 1.06667, 1, 0.377529, 0.791555, 0.304014, 0.37214, 1.1, 1, 0.387133, 0.786744, 0.34396, 0.335937, 1.13333, 1, 0.390765, 0.784689, 0.375598, 0.300818, 1.16667, 1, 0.388175, 0.785779, 0.399521, 0.268804, 1.2, 1, 0.377675, 0.790978, 0.414991, 0.243922, 1.23333, 1, 0.362042, 0.798195, 0.425217, 0.225836, 1.26667, 1, 0.344587, 0.805846, 0.431752, 0.213219, 1.3, 1, 0.329276, 0.812222, 0.435709, 0.205011, 1.33333, 1, 0.320782, 0.815614, 0.437825, 0.200453, 1.36667, 1, 0.372833, 0.793173, 0.423952, 0.228335, 1.4, 1, 0.487881, 0.72808, 0.38479, 0.289497, 1.43333, 1, 0.631104, 0.608141, 0.314694, 0.364472, 1.46667, 1, 0.761418, 0.434017, 0.215259, 0.430739, 1.5, 1, 0.845823, 0.229588, 0.100576, 0.47091, 1.53333, 1, 0.875224, 0.0459468, -0.00105228, 0.48153, 1.56667, 1, 0.870699, -0.100058, -0.081015, 0.474667, 1.6, 1, 0.852562, -0.203141, -0.137023, 0.461624, 1.63333, 1, 0.835624, -0.264312, -0.170073, 0.450497, 1.66667, 1, 0.828002, -0.2873, -0.182456, 0.445625, 2.625, 1, 0.828002, -0.2873, -0.182456, 0.445625)
tracks/62/type = "scale_3d"
tracks/62/imported = true
tracks/62/enabled = true
tracks/62/path = NodePath("Armature/Skeleton3D:Thumb.001")
tracks/62/interp = 1
tracks/62/loop_wrap = true
tracks/62/keys = PackedFloat32Array(0, 1, 1, 1, 1, 1.5, 1, 1, 1, 1, 1.53333, 1, 1.05, 1.05, 1.05, 1.56667, 1, 1.145, 1.145, 1.145, 1.6, 1, 1.255, 1.255, 1.255, 1.63333, 1, 1.35, 1.35, 1.35, 1.7, 1, 1.35, 1.35, 1.35, 1.73333, 1, 1.255, 1.255, 1.255, 1.76667, 1, 1.145, 1.145, 1.145, 1.8, 1, 1.05, 1.05, 1.05, 1.83333, 1, 1, 1, 1, 2.625, 1, 1, 1, 1)

[sub_resource type="AnimationLibrary" id="AnimationLibrary_vclnt"]
_data = {
"Slap": SubResource("Animation_3pake")
}

[sub_resource type="Animation" id="Animation_evydo"]
length = 2.5
tracks/0/type = "method"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0.2, 1.6),
"transitions": PackedFloat32Array(1, 1),
"values": [{
"args": [&"raised"],
"method": &"emit_signal"
}, {
"args": [&"slaped"],
"method": &"emit_signal"
}]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_kjkoj"]
_data = {
"events": SubResource("Animation_evydo")
}

[sub_resource type="AnimationNodeAdd2" id="AnimationNodeAdd2_l3mgg"]

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_c0ugy"]
animation = &"custom/events"

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_1vtnd"]
animation = &"Slap"

[sub_resource type="AnimationNodeTransition" id="AnimationNodeTransition_4f8kt"]
allow_transition_to_self = true
input_0/name = "none"
input_0/auto_advance = false
input_0/reset = true
input_1/name = "default"
input_1/auto_advance = false
input_1/reset = true

[sub_resource type="AnimationNodeBlendTree" id="AnimationNodeBlendTree_wejts"]
nodes/Add2/node = SubResource("AnimationNodeAdd2_l3mgg")
nodes/Add2/position = Vector2(-220, 280)
nodes/Animation/node = SubResource("AnimationNodeAnimation_1vtnd")
nodes/Animation/position = Vector2(-420, 280)
"nodes/Animation 2/node" = SubResource("AnimationNodeAnimation_c0ugy")
"nodes/Animation 2/position" = Vector2(-420, 400)
nodes/Transition/node = SubResource("AnimationNodeTransition_4f8kt")
nodes/Transition/position = Vector2(0, 200)
nodes/output/position = Vector2(200, 200)
node_connections = [&"Add2", 0, &"Animation", &"Add2", 1, &"Animation 2", &"Transition", 1, &"Add2", &"output", 0, &"Transition"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_04cln"]
noise_type = 2
seed = -10
fractal_octaves = 2

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_asuuj"]
seamless = true
noise = SubResource("FastNoiseLite_04cln")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_evqnw"]
render_priority = 0
shader = ExtResource("9_428na")
shader_parameter/mix_blend = 0.0
shader_parameter/mix_voronoi = 0.0
shader_parameter/progress = 0.0
shader_parameter/voronoi_sampler = SubResource("NoiseTexture2D_asuuj")

[sub_resource type="QuadMesh" id="QuadMesh_e87sj"]
size = Vector2(12, 4)

[sub_resource type="Animation" id="Animation_tl23c"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("WetSpot:material_override:shader_parameter/mix_blend")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("WetSpot:material_override:shader_parameter/mix_voronoi")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("WetSpot:material_override:shader_parameter/progress")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}

[sub_resource type="Animation" id="Animation_gaafr"]
resource_name = "default"
length = 8.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("WetSpot:material_override:shader_parameter/mix_blend")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 2, 3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, 1.0, 1.0, 0.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("WetSpot:material_override:shader_parameter/mix_voronoi")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(1.5, 2, 3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.6, 0.6, 1.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("WetSpot:material_override:shader_parameter/progress")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(1.5, 1.6),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.3, 1.0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_5yt47"]
_data = {
"RESET": SubResource("Animation_tl23c"),
"default": SubResource("Animation_gaafr")
}

[sub_resource type="FastNoiseLite" id="FastNoiseLite_i2yyy"]
noise_type = 2
fractal_type = 0

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_tet50"]
seamless = true
noise = SubResource("FastNoiseLite_i2yyy")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_ci4wu"]
render_priority = 0
shader = ExtResource("11_tcead")
shader_parameter/atlas_sampler = ExtResource("12_6715a")
shader_parameter/voronoi_sampler = SubResource("NoiseTexture2D_tet50")

[sub_resource type="Gradient" id="Gradient_lp8et"]
offsets = PackedFloat32Array(0.4, 1)
colors = PackedColorArray(1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_4d14f"]
gradient = SubResource("Gradient_lp8et")

[sub_resource type="Curve" id="Curve_1qw1t"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0.8), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_hm8wa"]
curve = SubResource("Curve_1qw1t")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_6c8f8"]
lifetime_randomness = 0.2
emission_shape = 3
emission_box_extents = Vector3(4, 0, 1)
direction = Vector3(0, 1, 0)
spread = 35.0
initial_velocity_min = 4.0
initial_velocity_max = 6.0
scale_min = 0.6
scale_max = 1.2
scale_curve = SubResource("CurveTexture_hm8wa")
color = Color(0, 0.4, 1, 1)
color_ramp = SubResource("GradientTexture1D_4d14f")
anim_offset_max = 1.0

[sub_resource type="QuadMesh" id="QuadMesh_6hiym"]
size = Vector2(0.2, 0.2)

[sub_resource type="Gradient" id="Gradient_y4tp5"]
offsets = PackedFloat32Array(0.30038, 0.574144)
colors = PackedColorArray(1, 1, 1, 1, 0, 0, 0, 1)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_gte7f"]
noise_type = 2
seed = 5
fractal_type = 0

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_aq38x"]
color_ramp = SubResource("Gradient_y4tp5")
noise = SubResource("FastNoiseLite_gte7f")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_83lu1"]
render_priority = 0
shader = ExtResource("13_bmyef")
shader_parameter/base_color = Color(0, 0.415686, 1, 1)
shader_parameter/dark_color = Color(0.0117647, 0.184314, 0.560784, 1)
shader_parameter/opening = 0.0
shader_parameter/offset = 0.0
shader_parameter/voronoi_sampler = SubResource("NoiseTexture2D_aq38x")
shader_parameter/caustic_sampler = ExtResource("14_5kr1b")

[sub_resource type="QuadMesh" id="QuadMesh_j64g1"]
size = Vector2(6, 6)

[sub_resource type="Animation" id="Animation_wn4iv"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Whirlpool:material_override:shader_parameter/opening")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Whirlpool:material_override:shader_parameter/offset")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}

[sub_resource type="Animation" id="Animation_3gawx"]
resource_name = "default"
length = 2.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Whirlpool:material_override:shader_parameter/opening")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.4, 1.2, 2),
"transitions": PackedFloat32Array(1, -2, -2, 1),
"update": 0,
"values": [0.0, 1.0, 1.0, 0.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Whirlpool:material_override:shader_parameter/offset")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 4.0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_ijxxh"]
_data = {
"RESET": SubResource("Animation_wn4iv"),
"default": SubResource("Animation_3gawx")
}

[node name="WaterHandPreview" type="Node3D"]
script = ExtResource("1_ocyw3")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_63fgl")

[node name="DomeFade" type="MeshInstance3D" parent="."]
transform = Transform3D(10, 0, 0, 0, 10, 0, 0, 0, 10, -2, 0, 0)
material_override = SubResource("ShaderMaterial_6uv0p")
cast_shadow = 0
mesh = SubResource("SphereMesh_5nm6p")
metadata/_edit_lock_ = true

[node name="GroundMesh" type="MeshInstance3D" parent="."]
material_override = SubResource("ShaderMaterial_hjcv1")
mesh = SubResource("PlaneMesh_k8hd6")
metadata/_edit_lock_ = true

[node name="Turner" parent="." instance=ExtResource("4_rvotv")]
transform = Transform3D(0.906308, -0.109382, 0.408218, 0, 0.965926, 0.258819, -0.422618, -0.23457, 0.875426, -2, 2, 0)
_zoom = 8.5
min_zoom = 6.0

[node name="WaterHand" parent="." instance=ExtResource("1_ul5tc")]
unique_name_in_owner = true
transform = Transform3D(4, 0, 0, 0, 4, 0, 0, 0, 4, -3.6, 0, 0)
script = ExtResource("3_f3c4c")

[node name="Skeleton3D" parent="WaterHand/Armature" index="0"]
bones/0/position = Vector3(0, -0.2, 0)
bones/0/scale = Vector3(3.16228, 0.1, 3.16228)
bones/1/position = Vector3(0, -0.19, 0)
bones/1/scale = Vector3(2.97254, 0.1, 2.97254)
bones/2/position = Vector3(0, -0.18, 0)
bones/2/scale = Vector3(2.7828, 0.1, 2.7828)
bones/3/position = Vector3(0, -0.17, 0)
bones/3/scale = Vector3(2.59307, 0.1, 2.59307)
bones/4/position = Vector3(0, -0.16, 0)
bones/4/scale = Vector3(2.40333, 0.1, 2.40333)
bones/5/position = Vector3(0, -0.15, 0)
bones/5/scale = Vector3(2.21359, 0.1, 2.21359)
bones/6/position = Vector3(0, -0.14, 0)
bones/6/scale = Vector3(2.02386, 0.1, 2.02386)
bones/7/position = Vector3(0, -0.13, 0)
bones/7/scale = Vector3(1.83412, 0.1, 1.83412)
bones/8/position = Vector3(0, -0.12, 0)
bones/8/scale = Vector3(1.64438, 0.1, 1.64438)
bones/9/position = Vector3(0, -0.11, 0)
bones/9/scale = Vector3(1.45465, 0.1, 1.45465)
bones/10/position = Vector3(0, -0.1, 0)
bones/10/rotation = Quaternion(1.05367e-07, -4.21469e-08, -0.795242, 0.606292)
bones/11/position = Vector3(0.336603, -0.180161, 0.175658)
bones/11/rotation = Quaternion(-0.0206594, 0.00762765, 0.999747, -0.00462034)
bones/12/position = Vector3(0.337618, -0.293963, 0.177415)
bones/12/rotation = Quaternion(-0.0204032, -0.00828885, 0.741687, 0.670385)
bones/12/scale = Vector3(1, 1, 1)
bones/13/position = Vector3(0.359559, -0.175623, 0.0730534)
bones/13/rotation = Quaternion(-1.71469e-07, -1.26441e-07, 0.999998, -0.00197074)
bones/13/scale = Vector3(1, 1, 1)
bones/14/position = Vector3(0.360036, -0.296644, 0.0730534)
bones/14/rotation = Quaternion(8.42937e-08, -1.68587e-07, 0.743642, 0.668578)
bones/14/scale = Vector3(1, 1, 1)
bones/15/position = Vector3(0.364914, -0.17624, -0.0358783)
bones/15/rotation = Quaternion(0.00104424, -0.000382525, 0.999997, -0.00197091)
bones/15/scale = Vector3(1, 1, 1)
bones/16/position = Vector3(0.365391, -0.297261, -0.0359714)
bones/16/rotation = Quaternion(0.00103154, 0.000415737, 0.743641, 0.668578)
bones/16/scale = Vector3(1, 1, 1)
bones/17/position = Vector3(0.345317, -0.176812, -0.136023)
bones/17/rotation = Quaternion(0.0294545, -0.0109701, 0.999478, -0.00745659)
bones/17/scale = Vector3(1, 1, 1)
bones/18/position = Vector3(0.346843, -0.283779, -0.138416)
bones/18/rotation = Quaternion(0.0290861, 0.0119125, 0.739595, 0.672318)
bones/18/scale = Vector3(1, 1, 1)
bones/19/position = Vector3(0.101677, -0.212082, 0.135248)
bones/19/rotation = Quaternion(0.789359, 0.380841, -0.464742, 0.126044)
bones/19/scale = Vector3(1, 1, 1)
bones/20/position = Vector3(0.179856, -0.285881, 0.118381)
bones/20/rotation = Quaternion(0.566398, 0.668821, -0.47585, -0.0737495)
bones/20/scale = Vector3(1, 1, 1)

[node name="WaterHand" parent="WaterHand/Armature/Skeleton3D" index="0"]
material_override = ExtResource("2_nopph")

[node name="AnimationPlayer" parent="WaterHand" index="1"]
libraries = {
"": SubResource("AnimationLibrary_vclnt"),
"custom": SubResource("AnimationLibrary_kjkoj")
}

[node name="AnimationTree" type="AnimationTree" parent="WaterHand"]
unique_name_in_owner = true
root_node = NodePath("%AnimationTree/..")
libraries = {
"": SubResource("AnimationLibrary_vclnt"),
"custom": SubResource("AnimationLibrary_kjkoj")
}
tree_root = SubResource("AnimationNodeBlendTree_wejts")
anim_player = NodePath("../AnimationPlayer")
parameters/Add2/add_amount = 1.0
parameters/Transition/current_state = "none"
parameters/Transition/transition_request = ""
parameters/Transition/current_index = 0

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 0, 0)
shadow_enabled = true

[node name="Target" type="Node3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 3, 0, 0)
script = ExtResource("8_o6qfi")

[node name="DummySkin" parent="Target" instance=ExtResource("9_hx6do")]
transform = Transform3D(-5.46392e-08, 0, -1.25, 0, 1.25, 0, 1.25, 0, -5.46392e-08, 0, 0, 0)

[node name="DummySkin2" parent="." instance=ExtResource("9_hx6do")]
transform = Transform3D(0, 0, 1.25, 0, 1.25, 0, -1.25, 0, 0, -7, 0, 0)

[node name="ImpactSplash" parent="." instance=ExtResource("8_qiyem")]
unique_name_in_owner = true
transform = Transform3D(1.25, 0, 0, 0, 1.25, 0, 0, 0, 1.25, 3, 0, 0)

[node name="HandSplash" parent="." instance=ExtResource("8_qiyem")]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3.6, 0, 0)

[node name="WetSpot" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, -0.5, 0.002, 0)
material_override = SubResource("ShaderMaterial_evqnw")
cast_shadow = 0
mesh = SubResource("QuadMesh_e87sj")

[node name="WetPlayer" type="AnimationPlayer" parent="."]
unique_name_in_owner = true
libraries = {
"": SubResource("AnimationLibrary_5yt47")
}

[node name="CooldownTimer" type="Timer" parent="."]
unique_name_in_owner = true
one_shot = true

[node name="ArmImpactDroplet" type="GPUParticles3D" parent="."]
unique_name_in_owner = true
material_override = SubResource("ShaderMaterial_ci4wu")
emitting = false
amount = 32
lifetime = 1.2
one_shot = true
explosiveness = 0.9
transform_align = 3
process_material = SubResource("ParticleProcessMaterial_6c8f8")
draw_pass_1 = SubResource("QuadMesh_6hiym")

[node name="ArmSpawnEffect" type="Node3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -3.6, 0.01, 0)
script = ExtResource("13_g52ik")

[node name="Whirlpool" type="MeshInstance3D" parent="ArmSpawnEffect"]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, 1, 0, -1, -4.37114e-08, 0, 0, 0)
material_override = SubResource("ShaderMaterial_83lu1")
cast_shadow = 0
mesh = SubResource("QuadMesh_j64g1")
skeleton = NodePath("../..")

[node name="AnimationPlayer" type="AnimationPlayer" parent="ArmSpawnEffect"]
libraries = {
"": SubResource("AnimationLibrary_ijxxh")
}

[editable path="WaterHand"]
