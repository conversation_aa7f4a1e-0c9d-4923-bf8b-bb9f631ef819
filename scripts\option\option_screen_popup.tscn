[gd_scene load_steps=12 format=3 uid="uid://cortelt5ba30d"]

[ext_resource type="Script" path="res://scripts/option/option_screen_popup.gd" id="1_q3epq"]
[ext_resource type="Shader" path="res://blur.gdshader" id="2_v4rtd"]
[ext_resource type="PackedScene" uid="uid://cs8ncblbej60o" path="res://scripts/option/confirmation_screen_popup.tscn" id="3_sj7rl"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_gvkuy"]
shader = ExtResource("2_v4rtd")
shader_parameter/blur = 1.0
shader_parameter/brightness = 0.065

[sub_resource type="Gradient" id="Gradient_41vlh"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0, 0, 0, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_1254l"]
gradient = SubResource("Gradient_41vlh")

[sub_resource type="CanvasTexture" id="CanvasTexture_xcg1u"]
diffuse_texture = SubResource("GradientTexture1D_1254l")

[sub_resource type="Gradient" id="Gradient_4taag"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0.147672, 0.147672, 0.147672, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_xglr7"]
gradient = SubResource("Gradient_4taag")

[sub_resource type="Gradient" id="Gradient_eleys"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0.400698, 0.400698, 0.400698, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_d8hm2"]
gradient = SubResource("Gradient_eleys")

[node name="OptionScreen" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
size_flags_vertical = 4
script = ExtResource("1_q3epq")
metadata/_edit_lock_ = true

[node name="BlurBG" type="TextureRect" parent="."]
material = SubResource("ShaderMaterial_gvkuy")
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = -1.92
offset_right = 1918.08
offset_bottom = 1080.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("CanvasTexture_xcg1u")

[node name="BGBorder" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 571.08
offset_top = 323.0
offset_right = 1346.08
offset_bottom = 759.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_xglr7")

[node name="BG" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 610.08
offset_top = 345.0
offset_right = 1307.08
offset_bottom = 737.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_d8hm2")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.499
anchor_top = 0.512
anchor_right = 0.499
anchor_bottom = 0.512
offset_left = -182.08
offset_top = -96.96
offset_right = 183.92
offset_bottom = 46.04
theme_override_constants/separation = 30
alignment = 1

[node name="SettingsButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "Settings"

[node name="ReturnButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "Return to Title Screen"

[node name="QuitButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "Quit"

[node name="CloseButton" type="Button" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 333.0
offset_top = -749.0
offset_right = 377.0
offset_bottom = -704.0
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(0.94, 0.94)
theme_override_font_sizes/font_size = 30
text = "X"

[node name="ConfirmationScreen" parent="." instance=ExtResource("3_sj7rl")]
visible = false
layout_mode = 1

[connection signal="pressed" from="VBoxContainer/ReturnButton" to="." method="_on_return_button_pressed"]
[connection signal="pressed" from="VBoxContainer/QuitButton" to="." method="_on_quit_button_pressed"]
[connection signal="pressed" from="CloseButton" to="." method="_on_close_button_pressed"]
