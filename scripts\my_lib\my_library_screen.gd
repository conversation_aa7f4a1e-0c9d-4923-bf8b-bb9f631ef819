extends Control
@onready var option_screen: Control = $OptionScreen


# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	init_deck_manager()
	pass # Replace with function body.

# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	pass


#logic functions

func init_deck_manager() -> void:
	#load decks from profile
	print("init deck manager: ", PlayerProfile)
	print("init deck manager: ", PlayerProfile.decks)
	pass


#ui functions

func _on_new_deck_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/deck_manager/deck_manager_screen.tscn")


func _on_crafting_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/card_craft/card_crafting_screen.tscn")


func _on_settings_button_pressed() -> void:
	option_screen.visible = true
	pass # Replace with function body.


func _on_world_map_button_pressed() -> void:
	get_tree().change_scene_to_file("res://scripts/map/world_map_screen.tscn")
	pass # Replace with function body.
