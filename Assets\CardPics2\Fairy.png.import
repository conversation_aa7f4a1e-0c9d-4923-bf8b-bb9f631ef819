[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://cryfth5rwbikt"
path="res://.godot/imported/Fairy.png-595d0065b58e759cc2f97cd7c0da4856.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://Assets/CardPics2/Fairy.png"
dest_files=["res://.godot/imported/Fairy.png-595d0065b58e759cc2f97cd7c0da4856.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
