[gd_resource type="CanvasTexture" load_steps=3 format=3 uid="uid://bk0734qvwwa1s"]

[sub_resource type="Gradient" id="Gradient_rxkwv"]
offsets = PackedFloat32Array(0)
colors = PackedColorArray(0.0728426, 0.0728426, 0.0728426, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_ibrbx"]
gradient = SubResource("Gradient_rxkwv")

[resource]
diffuse_texture = SubResource("GradientTexture1D_ibrbx")
