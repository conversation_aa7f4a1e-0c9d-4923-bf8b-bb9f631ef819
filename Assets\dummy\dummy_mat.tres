[gd_resource type="ShaderMaterial" load_steps=5 format=3 uid="uid://je1p01igsoee"]

[ext_resource type="Shader" path="res://assets/dummy/dummy.gdshader" id="1_es3yn"]
[ext_resource type="Texture2D" uid="uid://bi0o8ccvuiej4" path="res://Assets/dummy/dummy_diffuse.png" id="2_38umc"]
[ext_resource type="Texture2D" uid="uid://dun4w5y08xrn3" path="res://Assets/dummy/dummy_normal.png" id="3_eet0m"]
[ext_resource type="Texture2D" uid="uid://dv0sajpiuwfv7" path="res://Assets/dummy/dummy_roughness.png" id="4_dbu0y"]

[resource]
resource_local_to_scene = true
render_priority = 0
shader = ExtResource("1_es3yn")
shader_parameter/emission_color = Color(0.0745098, 0.533333, 1, 1)
shader_parameter/emission_strength = 1.4
shader_parameter/emission_blend = 0.0
shader_parameter/albedo_sampler = ExtResource("2_38umc")
shader_parameter/roughness_sampler = ExtResource("4_dbu0y")
shader_parameter/normal_sampler = ExtResource("3_eet0m")
