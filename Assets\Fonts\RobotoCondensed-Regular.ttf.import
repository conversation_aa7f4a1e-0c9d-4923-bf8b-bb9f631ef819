[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://y6w1a2u3t2py"
path="res://.godot/imported/RobotoCondensed-Regular.ttf-8d6b6661af907f3fef833b5d50707464.fontdata"

[deps]

source_file="res://Assets/Fonts/RobotoCondensed-Regular.ttf"
dest_files=["res://.godot/imported/RobotoCondensed-Regular.ttf-8d6b6661af907f3fef833b5d50707464.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=1
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
