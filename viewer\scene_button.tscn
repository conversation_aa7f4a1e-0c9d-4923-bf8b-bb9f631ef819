[gd_scene load_steps=6 format=3 uid="uid://b51qc3dyo62jr"]

[ext_resource type="Script" path="res://viewer/scene_button.gd" id="1_nt0sp"]
[ext_resource type="Texture2D" uid="uid://db2sbyywaj6cg" path="res://assets/thumbnail_placeholder.png" id="1_ywdea"]
[ext_resource type="Shader" path="res://viewer/button.gdshader" id="2_ycour"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_qpbes"]
resource_local_to_scene = true
shader = ExtResource("2_ycour")
shader_parameter/scale = 1.0
shader_parameter/greyscale = 0.0

[sub_resource type="ButtonGroup" id="ButtonGroup_rlixp"]
resource_local_to_scene = false

[node name="SceneButton" type="TextureButton"]
material = SubResource("ShaderMaterial_qpbes")
custom_minimum_size = Vector2(0, 230)
offset_right = 360.0
offset_bottom = 240.0
focus_mode = 0
toggle_mode = true
button_group = SubResource("ButtonGroup_rlixp")
texture_normal = ExtResource("1_ywdea")
ignore_texture_size = true
stretch_mode = 6
script = ExtResource("1_nt0sp")

[node name="TitleLabel" type="Label" parent="."]
unique_name_in_owner = true
visible = false
z_index = 1
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -27.0
offset_top = -52.0
offset_right = 27.0
offset_bottom = -16.0
grow_horizontal = 2
grow_vertical = 0
size_flags_horizontal = 0
theme_override_font_sizes/font_size = 26
text = "Title"
