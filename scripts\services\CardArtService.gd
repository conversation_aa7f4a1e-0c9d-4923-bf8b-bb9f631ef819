extends Object
class_name CardArtService

static var CardArts:Dictionary
static var NullCardArt = load("res://Assets/CardPics2/Null.png")
static var EventArts:Dictionary
static var NullEventArt = load("res://Assets/EventPics2/Null.png")

static func prepareCardArt(cards:Array) -> void:
	for card in cards:
		var image = load("res://Assets/CardPics2/" + card.name + ".png")
		CardArts[card.name] = image

static func prepareEventArt(events:Array) -> void:
	for event in events:
		var image = load("res://Assets/EventPics2/" + event.name + ".png")
		EventArts[event.name] = image
