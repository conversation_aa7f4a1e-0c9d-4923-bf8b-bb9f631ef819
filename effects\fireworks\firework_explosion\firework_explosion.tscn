[gd_scene load_steps=20 format=3 uid="uid://b7rb1o1j7yh60"]

[ext_resource type="Script" path="res://effects/fireworks/firework_explosion/firework_explosion.gd" id="1_vrfg4"]
[ext_resource type="Shader" path="res://effects/fireworks/firework_explosion/firework_trails.gdshader" id="2_vbcpr"]
[ext_resource type="Shader" path="res://effects/fireworks/firework_explosion/sparks.gdshader" id="3_3he2u"]
[ext_resource type="Texture2D" uid="uid://c8nvpeycw60tf" path="res://effects/fireworks/firework_explosion/star_particles_sampler.png" id="4_dmxjf"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_ixr1m"]
resource_local_to_scene = true
render_priority = 0
shader = ExtResource("2_vbcpr")
shader_parameter/intensity = 2.0

[sub_resource type="Curve" id="Curve_72bwn"]
_data = [Vector2(0.5, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_y1trk"]
curve = SubResource("Curve_72bwn")

[sub_resource type="Curve" id="Curve_yevjd"]
min_value = -1.0
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 1.49012e-08), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_mwmyi"]
curve = SubResource("Curve_yevjd")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_0lh1w"]
resource_local_to_scene = true
lifetime_randomness = 0.5
direction = Vector3(0, 1, 0)
spread = 120.0
initial_velocity_min = 4.0
initial_velocity_max = 8.0
gravity = Vector3(0, -6, 0)
damping_min = 2.0
damping_max = 6.0
scale_min = 0.8
scale_max = 1.2
alpha_curve = SubResource("CurveTexture_y1trk")
hue_variation_max = 0.05
hue_variation_curve = SubResource("CurveTexture_mwmyi")
sub_emitter_mode = 1
sub_emitter_frequency = 16.0

[sub_resource type="Curve" id="Curve_87cpt"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0.2), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="RibbonTrailMesh" id="RibbonTrailMesh_4lrmr"]
shape = 0
size = 0.1
curve = SubResource("Curve_87cpt")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_iuev8"]
resource_local_to_scene = true
render_priority = 0
shader = ExtResource("3_3he2u")
shader_parameter/intensity = 1.5
shader_parameter/spark_sampler = ExtResource("4_dmxjf")

[sub_resource type="Curve" id="Curve_jk44o"]
_data = [Vector2(0.2, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_ybhw1"]
curve = SubResource("Curve_jk44o")

[sub_resource type="Curve" id="Curve_0ct1v"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.2, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_pb7ni"]
curve = SubResource("Curve_0ct1v")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_m7wyx"]
resource_local_to_scene = true
lifetime_randomness = 0.2
direction = Vector3(0, 0, 0)
spread = 180.0
initial_velocity_min = 0.4
initial_velocity_max = 1.0
scale_min = 0.8
scale_max = 1.2
scale_curve = SubResource("CurveTexture_pb7ni")
alpha_curve = SubResource("CurveTexture_ybhw1")

[sub_resource type="QuadMesh" id="QuadMesh_vvecv"]
size = Vector2(0.04, 0.04)

[node name="Firework" type="Node3D"]
script = ExtResource("1_vrfg4")

[node name="Trails" type="GPUParticles3D" parent="."]
unique_name_in_owner = true
material_override = SubResource("ShaderMaterial_ixr1m")
amount = 18
sub_emitter = NodePath("../Sparks")
lifetime = 2.0
explosiveness = 0.95
transform_align = 3
trail_enabled = true
process_material = SubResource("ParticleProcessMaterial_0lh1w")
draw_pass_1 = SubResource("RibbonTrailMesh_4lrmr")

[node name="Sparks" type="GPUParticles3D" parent="."]
unique_name_in_owner = true
material_override = SubResource("ShaderMaterial_iuev8")
amount = 256
lifetime = 0.25
transform_align = 2
process_material = SubResource("ParticleProcessMaterial_m7wyx")
draw_pass_1 = SubResource("QuadMesh_vvecv")
