extends Node3D

var target_transform
var cache_transform
# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	$Cube.global_transform.origin = Vector3(1,2,3)
	target_transform = $Cube.global_transform
	cache_transform = $Cube/x.transform
	#print($Cube/x.transform)

func find_camera_pos() -> Vector3:
	var camera = get_viewport().get_camera_3d()
	var temp_transform = $Cube/x.global_transform
	#temp_transform.basis = temp_transform.basis.rotated(Vector3.RIGHT, deg_to_rad(90))
	var unprojected = camera.unproject_position(temp_transform.origin)
	# I fiddled with the y coordinate and distance here so the full card is visible
	return camera.project_position(Vector2(unprojected.x, 1000), 4.0)

# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(delta: float) -> void:
	#var temp = target_transform
	#temp.basis = temp.basis.rotated(Vector3.RIGHT, deg_to_rad(90))
	var view_spot = target_transform
	view_spot.basis = view_spot.basis.rotated(Vector3.RIGHT, deg_to_rad(90))
	view_spot.origin = find_camera_pos()
	#view_spot.basis = view_spot.basis.rotated(Vector3.RIGHT, deg_to_rad(90))
	$Cube/x.global_transform = $Cube/x.global_transform.interpolate_with(view_spot, 12 * delta)
	if $Cube2/x:
		#$Cube2.reparent($Cube)
		#$Cube2/x.reparent($Cube)
		$Cube.add_child($Cube2/x)
