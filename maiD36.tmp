[gd_scene load_steps=3 format=3 uid="uid://div6wuxesh8rn"]

[ext_resource type="Script" path="res://scripts/Battle.gd" id="1_ujs5q"]
[ext_resource type="Script" path="res://scripts/LaneLabelGroup.gd" id="13_ljtfs"]

[node name="BattleScene" type="Node2D"]
script = ExtResource("1_ujs5q")

[node name="HandA" type="Label" parent="."]
offset_left = 30.0
offset_top = 608.0
offset_right = 905.0
offset_bottom = 631.0
text = "Hand A"

[node name="HandB" type="Label" parent="."]
offset_left = 27.0
offset_top = 16.0
offset_right = 899.0
offset_bottom = 39.0
text = "Hand B"

[node name="ManaA" type="Label" parent="."]
offset_left = 939.0
offset_top = 610.0
offset_right = 1130.0
offset_bottom = 633.0
text = "Mana 0 / 0"

[node name="ManaB" type="Label" parent="."]
offset_left = 935.0
offset_top = 15.0
offset_right = 1132.0
offset_bottom = 38.0
text = "Mana 0 / 0"

[node name="LaneLeft" type="Node2D" parent="."]
script = ExtResource("13_ljtfs")

[node name="MainPower" type="Label" parent="LaneLeft"]
offset_left = 294.0
offset_top = 400.0
offset_right = 335.0
offset_bottom = 423.0
text = "MP"
horizontal_alignment = 1

[node name="OppoPower" type="Label" parent="LaneLeft"]
offset_left = 294.0
offset_top = 201.0
offset_right = 335.0
offset_bottom = 224.0
text = "OP"
horizontal_alignment = 1

[node name="LaneLabel1" type="Label" parent="LaneLeft"]
offset_left = 263.0
offset_top = 350.0
offset_right = 304.0
offset_bottom = 373.0
text = "L1"
horizontal_alignment = 1

[node name="LaneLabel2" type="Label" parent="LaneLeft"]
offset_left = 326.0
offset_top = 350.0
offset_right = 366.0
offset_bottom = 373.0
text = "L2"
horizontal_alignment = 1

[node name="LaneLabel3" type="Label" parent="LaneLeft"]
offset_left = 262.0
offset_top = 301.0
offset_right = 303.0
offset_bottom = 324.0
text = "L3"
horizontal_alignment = 1

[node name="LaneLabel4" type="Label" parent="LaneLeft"]
offset_left = 325.0
offset_top = 301.0
offset_right = 365.0
offset_bottom = 324.0
text = "L4"
horizontal_alignment = 1

[node name="LaneLabel5" type="Label" parent="LaneLeft"]
offset_left = 263.0
offset_top = 251.0
offset_right = 304.0
offset_bottom = 274.0
text = "L5"
horizontal_alignment = 1

[node name="LaneLabel6" type="Label" parent="LaneLeft"]
offset_left = 325.0
offset_top = 252.0
offset_right = 365.0
offset_bottom = 275.0
text = "L6"
horizontal_alignment = 1

[node name="LaneCen" type="Node2D" parent="."]
script = ExtResource("13_ljtfs")

[node name="MainPower" type="Label" parent="LaneCen"]
offset_left = 546.0
offset_top = 400.0
offset_right = 587.0
offset_bottom = 423.0
text = "MP"
horizontal_alignment = 1

[node name="OppoPower" type="Label" parent="LaneCen"]
offset_left = 546.0
offset_top = 201.0
offset_right = 587.0
offset_bottom = 224.0
text = "OP"
horizontal_alignment = 1

[node name="LaneLabel1" type="Label" parent="LaneCen"]
offset_left = 515.0
offset_top = 350.0
offset_right = 556.0
offset_bottom = 373.0
text = "C1"
horizontal_alignment = 1

[node name="LaneLabel2" type="Label" parent="LaneCen"]
offset_left = 578.0
offset_top = 350.0
offset_right = 618.0
offset_bottom = 373.0
text = "C2"
horizontal_alignment = 1

[node name="LaneLabel3" type="Label" parent="LaneCen"]
offset_left = 514.0
offset_top = 301.0
offset_right = 555.0
offset_bottom = 324.0
text = "C3"
horizontal_alignment = 1

[node name="LaneLabel4" type="Label" parent="LaneCen"]
offset_left = 577.0
offset_top = 301.0
offset_right = 617.0
offset_bottom = 324.0
text = "C4"
horizontal_alignment = 1

[node name="LaneLabel5" type="Label" parent="LaneCen"]
offset_left = 515.0
offset_top = 251.0
offset_right = 556.0
offset_bottom = 274.0
text = "C5"
horizontal_alignment = 1

[node name="LaneLabel6" type="Label" parent="LaneCen"]
offset_left = 577.0
offset_top = 252.0
offset_right = 617.0
offset_bottom = 275.0
text = "C6"
horizontal_alignment = 1

[node name="LaneRight" type="Node2D" parent="."]
script = ExtResource("13_ljtfs")

[node name="MainPower" type="Label" parent="LaneRight"]
offset_left = 800.0
offset_top = 400.0
offset_right = 841.0
offset_bottom = 423.0
text = "MP"
horizontal_alignment = 1

[node name="OppoPower" type="Label" parent="LaneRight"]
offset_left = 800.0
offset_top = 201.0
offset_right = 841.0
offset_bottom = 224.0
text = "OP"
horizontal_alignment = 1

[node name="LaneLabel1" type="Label" parent="LaneRight"]
offset_left = 768.0
offset_top = 350.0
offset_right = 809.0
offset_bottom = 373.0
text = "R1"
horizontal_alignment = 1

[node name="LaneLabel2" type="Label" parent="LaneRight"]
offset_left = 831.0
offset_top = 350.0
offset_right = 871.0
offset_bottom = 373.0
text = "R2"
horizontal_alignment = 1

[node name="LaneLabel3" type="Label" parent="LaneRight"]
offset_left = 767.0
offset_top = 301.0
offset_right = 808.0
offset_bottom = 324.0
text = "R3"
horizontal_alignment = 1

[node name="LaneLabel4" type="Label" parent="LaneRight"]
offset_left = 830.0
offset_top = 301.0
offset_right = 870.0
offset_bottom = 324.0
text = "R4"
horizontal_alignment = 1

[node name="LaneLabel5" type="Label" parent="LaneRight"]
offset_left = 768.0
offset_top = 251.0
offset_right = 809.0
offset_bottom = 274.0
text = "R5"
horizontal_alignment = 1

[node name="LaneLabel6" type="Label" parent="LaneRight"]
offset_left = 830.0
offset_top = 252.0
offset_right = 870.0
offset_bottom = 275.0
text = "R6"
horizontal_alignment = 1

[node name="PassTurnButton" type="Button" parent="."]
offset_left = 1004.0
offset_top = 301.0
offset_right = 1118.0
offset_bottom = 342.0

[node name="CastLeftButton" type="Button" parent="."]
offset_left = 271.0
offset_top = 458.0
offset_right = 358.0
offset_bottom = 499.0
text = "Cast Left"

[node name="CastCenterButton" type="Button" parent="."]
offset_left = 527.0
offset_top = 458.0
offset_right = 614.0
offset_bottom = 499.0
text = "Cast Cen"

[node name="CastRightButton" type="Button" parent="."]
offset_left = 780.0
offset_top = 458.0
offset_right = 867.0
offset_bottom = 499.0
text = "Cast Right"
