class_name State
extends Object


var arena: Array[Array]
var aiplayer: PlayerData
var opponent: PlayerData

func _init(_lanes: Array[Lane], _player: Player, _opponent: Player):
    # Initialize the state of the game.
    self.arena = []
    self.aiplayer = Duplicator.duplicatePlayer(_player)
    self.opponent = Duplicator.duplicatePlayer(_opponent)

    for lane in _lanes:
        for slot in lane.cardSlots:
            
        

