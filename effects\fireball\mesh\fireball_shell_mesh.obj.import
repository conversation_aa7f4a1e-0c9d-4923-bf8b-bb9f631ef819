[remap]

importer="wavefront_obj"
importer_version=1
type="Mesh"
uid="uid://bhma7uxtf5nja"
path="res://.godot/imported/fireball_shell_mesh.obj-b4061514374517d828bb3d1727bcc016.mesh"

[deps]

files=["res://.godot/imported/fireball_shell_mesh.obj-b4061514374517d828bb3d1727bcc016.mesh"]

source_file="res://effects/fireball/mesh/fireball_shell_mesh.obj"
dest_files=["res://.godot/imported/fireball_shell_mesh.obj-b4061514374517d828bb3d1727bcc016.mesh", "res://.godot/imported/fireball_shell_mesh.obj-b4061514374517d828bb3d1727bcc016.mesh"]

[params]

generate_tangents=true
scale_mesh=Vector3(1, 1, 1)
offset_mesh=Vector3(0, 0, 0)
optimize_mesh=true
force_disable_mesh_compression=false
