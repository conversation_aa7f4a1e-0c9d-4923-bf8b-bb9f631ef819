[gd_scene load_steps=13 format=3 uid="uid://crlvj3566pe4d"]

[ext_resource type="Script" path="res://scripts/Menu.gd" id="1_slqga"]
[ext_resource type="Texture2D" uid="uid://djx7h6rh2yol7" path="res://Assets/ArenaBg2.jpg" id="2_vedqy"]
[ext_resource type="Texture2D" uid="uid://bvmavynb4a4ti" path="res://Assets/Menu2/Deck Green Starter.png" id="3_efipr"]
[ext_resource type="Texture2D" uid="uid://ctp53gpa5ue2t" path="res://Assets/Menu2/pfpGlow.png" id="4_fqsuq"]
[ext_resource type="Texture2D" uid="uid://bvwv7v1mgxu4m" path="res://Assets/Menu2/Deck Red Starter.png" id="4_ggvux"]
[ext_resource type="Texture2D" uid="uid://huvacngvejdm" path="res://Assets/Menu2/Deck Green.png" id="5_0m26s"]
[ext_resource type="Texture2D" uid="uid://dah6r6pty5c4c" path="res://Assets/Menu2/Deck Red.png" id="6_7cgad"]
[ext_resource type="Texture2D" uid="uid://bgpq27rmedeap" path="res://Assets/Menu2/Deck Dragon.png" id="7_br0uq"]
[ext_resource type="FontFile" uid="uid://mrhagcdlhgwc" path="res://Assets/Fonts/Cabin-VariableFont_wdth,wght.ttf" id="8_slbin"]
[ext_resource type="Texture2D" uid="uid://rhm8pis47eqw" path="res://Assets/Menu2/pfpGlowG.png" id="10_44r83"]

[sub_resource type="LabelSettings" id="LabelSettings_4b7l5"]
font_size = 64
outline_size = 24
outline_color = Color(0, 0, 0, 1)

[sub_resource type="LabelSettings" id="LabelSettings_q3m8c"]
line_spacing = -0.825
font_size = 21
font_color = Color(0, 0.87451, 0, 1)

[node name="Menu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
size_flags_vertical = 4
script = ExtResource("1_slqga")

[node name="TextureRect" type="TextureRect" parent="."]
modulate = Color(0.709804, 0.709804, 0.709804, 1)
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -982.0
offset_top = -540.0
offset_right = 982.0
offset_bottom = 540.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_vedqy")

[node name="MenuBg" type="Sprite2D" parent="."]
visible = false
position = Vector2(959, 540)
texture = ExtResource("2_vedqy")

[node name="OpponentButtons" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -126.0
offset_right = 20.0
offset_bottom = -86.0
grow_horizontal = 2
grow_vertical = 2

[node name="SelectLevel" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -314.0
offset_top = -244.0
offset_right = 332.0
offset_bottom = -163.0
grow_horizontal = 2
text = "Select your opponent"
label_settings = SubResource("LabelSettings_4b7l5")
horizontal_alignment = 1
vertical_alignment = 1

[node name="SelectLevel2" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -209.0
offset_top = 312.0
offset_right = -97.0
offset_bottom = 361.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 36
text = "Hello,"
horizontal_alignment = 1
vertical_alignment = 1

[node name="MainMenu10" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -85.0
offset_top = 199.0
offset_right = 90.0
offset_bottom = 229.0
grow_horizontal = 2
theme_override_colors/font_color = Color(1, 0.4, 0, 1)
theme_override_fonts/font = ExtResource("8_slbin")
theme_override_font_sizes/font_size = 20
text = "Very challenging. "
horizontal_alignment = 1

[node name="MainMenu11" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -375.0
offset_top = 238.0
offset_right = -189.0
offset_bottom = 268.0
grow_horizontal = 2
theme_override_colors/font_color = Color(0.918448, 0.69481, 0, 1)
theme_override_fonts/font = ExtResource("8_slbin")
theme_override_font_sizes/font_size = 20
text = "Standard difficulty"
horizontal_alignment = 1

[node name="MainMenu13" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = 193.0
offset_top = 238.0
offset_right = 379.0
offset_bottom = 268.0
grow_horizontal = 2
theme_override_colors/font_color = Color(0.918448, 0.69481, 0, 1)
theme_override_fonts/font = ExtResource("8_slbin")
theme_override_font_sizes/font_size = 20
text = "Standard difficulty"
horizontal_alignment = 1

[node name="MainMenu12" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = 498.0
offset_top = 300.0
offset_right = 638.0
offset_bottom = 330.0
grow_horizontal = 2
theme_override_colors/font_color = Color(0.714168, 0.720742, 0.690333, 1)
theme_override_fonts/font = ExtResource("8_slbin")
theme_override_font_sizes/font_size = 20
text = "For first-timer"
horizontal_alignment = 1

[node name="MainMenu14" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -633.0
offset_top = 300.0
offset_right = -493.0
offset_bottom = 330.0
grow_horizontal = 2
theme_override_colors/font_color = Color(0.714168, 0.720742, 0.690333, 1)
theme_override_fonts/font = ExtResource("8_slbin")
theme_override_font_sizes/font_size = 20
text = "For first-timer"
horizontal_alignment = 1

[node name="MainMenu15" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -85.0
offset_top = 158.0
offset_right = 90.0
offset_bottom = 195.0
grow_horizontal = 2
theme_override_colors/font_color = Color(0.997626, 0.68497, 0.557847, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_font_sizes/font_size = 36
text = "Pyragon"
horizontal_alignment = 1

[node name="MainMenu16" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -378.0
offset_top = 196.0
offset_right = -186.0
offset_bottom = 233.0
grow_horizontal = 2
theme_override_colors/font_color = Color(0.9996, 0.944161, 0.74662, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_font_sizes/font_size = 36
text = "King Krotos"
horizontal_alignment = 1

[node name="MainMenu17" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = 172.0
offset_top = 196.0
offset_right = 400.0
offset_bottom = 233.0
grow_horizontal = 2
theme_override_colors/font_color = Color(0.9996, 0.944161, 0.74662, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_font_sizes/font_size = 36
text = "General Prixa"
horizontal_alignment = 1

[node name="MainMenu18" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = 485.5
offset_top = 259.0
offset_right = 650.5
offset_bottom = 296.0
grow_horizontal = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_font_sizes/font_size = 36
text = "Leaf Slime"
horizontal_alignment = 1

[node name="MainMenu19" type="Label" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -644.5
offset_top = 259.0
offset_right = -481.5
offset_bottom = 296.0
grow_horizontal = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_font_sizes/font_size = 36
text = "Fire Slime"
horizontal_alignment = 1

[node name="Glow" type="Control" parent="OpponentButtons"]
anchors_preset = 0
offset_top = 49.0
offset_right = 40.0
offset_bottom = 89.0

[node name="GstarterGlow" type="TextureRect" parent="OpponentButtons/Glow"]
visible = false
layout_mode = 0
offset_left = -53.0
offset_top = -306.0
offset_right = 1227.0
offset_bottom = 414.0
texture = ExtResource("4_fqsuq")

[node name="RstarterGlow" type="TextureRect" parent="OpponentButtons/Glow"]
visible = false
layout_mode = 0
offset_left = -1181.0
offset_top = -306.0
offset_right = 99.0
offset_bottom = 414.0
texture = ExtResource("4_fqsuq")

[node name="GreenGlow" type="TextureRect" parent="OpponentButtons/Glow"]
visible = false
layout_mode = 0
offset_left = -337.0
offset_top = -368.0
offset_right = 943.0
offset_bottom = 352.0
texture = ExtResource("4_fqsuq")

[node name="RedGlow" type="TextureRect" parent="OpponentButtons/Glow"]
visible = false
layout_mode = 0
offset_left = -902.0
offset_top = -374.0
offset_right = 378.0
offset_bottom = 346.0
texture = ExtResource("4_fqsuq")

[node name="DragonGlow" type="TextureRect" parent="OpponentButtons/Glow"]
visible = false
layout_mode = 0
offset_left = -618.0
offset_top = -406.0
offset_right = 662.0
offset_bottom = 314.0
texture = ExtResource("4_fqsuq")

[node name="GStarter" type="Button" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = 447.0
offset_top = -62.0
offset_right = 694.0
offset_bottom = 226.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
size_flags_stretch_ratio = 0.0
theme_override_colors/font_color = Color(0.631373, 0.0784314, 0.164706, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0.643137, 0.223529, 0.0627451, 1)
theme_override_constants/outline_size = 15
theme_override_constants/h_separation = 0
theme_override_font_sizes/font_size = 0
text = "
"
icon = ExtResource("3_efipr")
flat = true

[node name="RStarter" type="Button" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -683.0
offset_top = -62.0
offset_right = -436.0
offset_bottom = 226.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0.631373, 0.0784314, 0.164706, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0.643137, 0.223529, 0.0627451, 1)
theme_override_constants/outline_size = 15
theme_override_constants/h_separation = 0
theme_override_font_sizes/font_size = 156
icon = ExtResource("4_ggvux")
flat = true

[node name="Green" type="Button" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = 164.0
offset_top = -125.0
offset_right = 411.0
offset_bottom = 163.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
size_flags_stretch_ratio = 0.0
theme_override_colors/font_color = Color(0.631373, 0.0784314, 0.164706, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0.643137, 0.223529, 0.0627451, 1)
theme_override_constants/outline_size = 15
theme_override_constants/h_separation = 0
theme_override_font_sizes/font_size = 0
text = "
"
icon = ExtResource("5_0m26s")
flat = true

[node name="Red" type="Button" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -403.0
offset_top = -127.0
offset_right = -156.0
offset_bottom = 161.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0.631373, 0.0784314, 0.164706, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0.643137, 0.223529, 0.0627451, 1)
theme_override_constants/outline_size = 15
theme_override_constants/h_separation = 0
theme_override_font_sizes/font_size = 156
icon = ExtResource("6_7cgad")
flat = true

[node name="Dragon" type="Button" parent="OpponentButtons"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -121.0
offset_top = -160.0
offset_right = 126.0
offset_bottom = 128.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0.631373, 0.0784314, 0.164706, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0.643137, 0.223529, 0.0627451, 1)
theme_override_constants/outline_size = 15
theme_override_constants/h_separation = 0
theme_override_font_sizes/font_size = 156
icon = ExtResource("7_br0uq")
flat = true

[node name="YourDeck" type="Control" parent="."]
anchors_preset = 0
offset_left = 829.0
offset_top = 13.0
offset_right = 869.0
offset_bottom = 53.0

[node name="YourDeckLabel" type="Label" parent="YourDeck"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = 23.0
offset_top = 767.0
offset_right = 212.0
offset_bottom = 828.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 28
text = "Pick Your Deck
v"
label_settings = SubResource("LabelSettings_q3m8c")
horizontal_alignment = 1
vertical_alignment = 1

[node name="DeckGlow" type="Control" parent="YourDeck"]
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="PickedGlow" type="TextureRect" parent="YourDeck/DeckGlow"]
visible = false
layout_mode = 0
offset_left = -239.0
offset_top = 697.0
offset_right = 518.0
offset_bottom = 1123.0
texture = ExtResource("10_44r83")
expand_mode = 1

[node name="DeckGstarterGlow" type="TextureRect" parent="YourDeck/DeckGlow"]
visible = false
layout_mode = 0
offset_left = 8.0
offset_top = 759.0
offset_right = 591.0
offset_bottom = 1087.0
texture = ExtResource("10_44r83")
expand_mode = 1

[node name="DeckRstarterGlow" type="TextureRect" parent="YourDeck/DeckGlow"]
visible = false
layout_mode = 0
offset_left = 133.0
offset_top = 759.0
offset_right = 716.0
offset_bottom = 1087.0
texture = ExtResource("10_44r83")
expand_mode = 1

[node name="DeckGreenGlow" type="TextureRect" parent="YourDeck/DeckGlow"]
visible = false
layout_mode = 0
offset_left = 257.0
offset_top = 759.0
offset_right = 840.0
offset_bottom = 1087.0
texture = ExtResource("10_44r83")
expand_mode = 1

[node name="DeckRedGlow" type="TextureRect" parent="YourDeck/DeckGlow"]
visible = false
layout_mode = 0
offset_left = 380.0
offset_top = 759.0
offset_right = 963.0
offset_bottom = 1087.0
texture = ExtResource("10_44r83")
expand_mode = 1

[node name="DeckDragonGlow" type="TextureRect" parent="YourDeck/DeckGlow"]
visible = false
layout_mode = 0
offset_left = 502.0
offset_top = 759.0
offset_right = 1085.0
offset_bottom = 1087.0
texture = ExtResource("10_44r83")
expand_mode = 1

[node name="YourDeckPic" type="Button" parent="YourDeck"]
layout_mode = 0
offset_left = 58.0
offset_top = 820.0
offset_right = 217.0
offset_bottom = 1007.0
icon = ExtResource("3_efipr")
flat = true
expand_icon = true

[node name="ChangeYourDeck" type="Button" parent="YourDeck"]
visible = false
layout_mode = 0
offset_left = 73.0
offset_top = 999.0
offset_right = 194.0
offset_bottom = 1042.0
size_flags_horizontal = 8
size_flags_vertical = 0
theme_override_font_sizes/font_size = 26
text = "Change"

[node name="YourDeckGroup" type="GridContainer" parent="YourDeck"]
visible = false
layout_mode = 0
offset_left = 238.0
offset_top = 857.0
offset_right = 854.0
offset_bottom = 997.0
columns = 5

[node name="YourGStarter" type="Button" parent="YourDeck/YourDeckGroup"]
custom_minimum_size = Vector2(120, 140)
layout_mode = 2
size_flags_horizontal = 4
size_flags_stretch_ratio = 0.0
theme_override_colors/font_color = Color(0.631373, 0.0784314, 0.164706, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0.643137, 0.223529, 0.0627451, 1)
theme_override_constants/outline_size = 15
theme_override_constants/h_separation = 0
theme_override_font_sizes/font_size = 0
text = "
"
icon = ExtResource("3_efipr")
flat = true
expand_icon = true

[node name="YourRStarter" type="Button" parent="YourDeck/YourDeckGroup"]
custom_minimum_size = Vector2(120, 140)
layout_mode = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0.631373, 0.0784314, 0.164706, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0.643137, 0.223529, 0.0627451, 1)
theme_override_constants/outline_size = 15
theme_override_constants/h_separation = 0
theme_override_font_sizes/font_size = 156
icon = ExtResource("4_ggvux")
flat = true
expand_icon = true

[node name="YourGreen" type="Button" parent="YourDeck/YourDeckGroup"]
custom_minimum_size = Vector2(120, 140)
layout_mode = 2
size_flags_horizontal = 4
size_flags_stretch_ratio = 0.0
theme_override_colors/font_color = Color(0.631373, 0.0784314, 0.164706, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0.643137, 0.223529, 0.0627451, 1)
theme_override_constants/outline_size = 15
theme_override_constants/h_separation = 0
theme_override_font_sizes/font_size = 0
text = "
"
icon = ExtResource("5_0m26s")
flat = true
expand_icon = true

[node name="YourRed" type="Button" parent="YourDeck/YourDeckGroup"]
custom_minimum_size = Vector2(120, 140)
layout_mode = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0.631373, 0.0784314, 0.164706, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0.643137, 0.223529, 0.0627451, 1)
theme_override_constants/outline_size = 15
theme_override_constants/h_separation = 0
theme_override_font_sizes/font_size = 156
icon = ExtResource("6_7cgad")
flat = true
expand_icon = true

[node name="YourDragon" type="Button" parent="YourDeck/YourDeckGroup"]
visible = false
custom_minimum_size = Vector2(120, 140)
layout_mode = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0.631373, 0.0784314, 0.164706, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_colors/font_hover_color = Color(0.643137, 0.223529, 0.0627451, 1)
theme_override_constants/outline_size = 15
theme_override_constants/h_separation = 0
theme_override_font_sizes/font_size = 156
icon = ExtResource("7_br0uq")
flat = true
expand_icon = true

[node name="YourName" type="TextEdit" parent="YourDeck"]
layout_mode = 1
offset_left = 50.0
offset_top = 707.0
offset_right = 397.0
offset_bottom = 753.0
theme_override_font_sizes/font_size = 36
placeholder_text = "Enter your name"
caret_blink = true

[node name="MarginContainer" type="MarginContainer" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 404.0
offset_top = 187.0
offset_right = -1048.0
offset_bottom = -614.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(2.42997, 2.42997)

[node name="HBoxContainer" type="HBoxContainer" parent="MarginContainer"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_constants/separation = 30
alignment = 1

[node name="QuitButton" type="Button" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 760.0
offset_top = -61.0
offset_right = 931.0
offset_bottom = -14.0
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(0.94, 0.94)
theme_override_font_sizes/font_size = 28
text = "Quit Game"

[node name="GameModeSelect" type="Control" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -400.0
offset_right = 100.0
offset_bottom = -350.0
grow_horizontal = 2
grow_vertical = 2

[node name="ModeLabel" type="Label" parent="GameModeSelect"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -150.0
offset_right = 150.0
offset_bottom = 40.0
grow_horizontal = 2
theme_override_font_sizes/font_size = 28
text = "Game Mode"
horizontal_alignment = 1

[node name="ModeButtons" type="HBoxContainer" parent="GameModeSelect"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -20.0
offset_right = 200.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 20
alignment = 1

[node name="ClassicMode" type="Button" parent="GameModeSelect/ModeButtons"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
toggle_mode = true
button_pressed = true
text = "Classic Mode"

[node name="DominationMode" type="Button" parent="GameModeSelect/ModeButtons"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
toggle_mode = true
text = "Domination Mode"

[connection signal="mouse_entered" from="OpponentButtons/GStarter" to="." method="_on_g_starter_mouse_entered"]
[connection signal="mouse_exited" from="OpponentButtons/GStarter" to="." method="_on_g_starter_mouse_exited"]
[connection signal="pressed" from="OpponentButtons/GStarter" to="." method="_on_gstarter_click"]
[connection signal="mouse_entered" from="OpponentButtons/RStarter" to="." method="_on_r_starter_mouse_entered"]
[connection signal="mouse_exited" from="OpponentButtons/RStarter" to="." method="_on_r_starter_mouse_exited"]
[connection signal="pressed" from="OpponentButtons/RStarter" to="." method="_on_rstarter_click"]
[connection signal="mouse_entered" from="OpponentButtons/Green" to="." method="_on_green_mouse_entered"]
[connection signal="mouse_exited" from="OpponentButtons/Green" to="." method="_on_green_mouse_exited"]
[connection signal="pressed" from="OpponentButtons/Green" to="." method="_on_green_click"]
[connection signal="mouse_entered" from="OpponentButtons/Red" to="." method="_on_red_mouse_entered"]
[connection signal="mouse_exited" from="OpponentButtons/Red" to="." method="_on_red_mouse_exited"]
[connection signal="pressed" from="OpponentButtons/Red" to="." method="_on_red_click"]
[connection signal="mouse_entered" from="OpponentButtons/Dragon" to="." method="_on_dragon_mouse_entered"]
[connection signal="mouse_exited" from="OpponentButtons/Dragon" to="." method="_on_dragon_mouse_exited"]
[connection signal="pressed" from="OpponentButtons/Dragon" to="." method="_on_boss_click"]
[connection signal="mouse_entered" from="YourDeck/YourDeckPic" to="." method="_on_change_your_deck"]
[connection signal="mouse_exited" from="YourDeck/YourDeckPic" to="." method="_on_your_deck_pic_mouse_exited"]
[connection signal="pressed" from="YourDeck/YourDeckPic" to="." method="_on_your_deck_pic_pressed"]
[connection signal="pressed" from="YourDeck/ChangeYourDeck" to="." method="_on_change_your_deck"]
[connection signal="mouse_entered" from="YourDeck/YourDeckGroup/YourGStarter" to="." method="_on_your_g_starter_mouse_entered"]
[connection signal="mouse_exited" from="YourDeck/YourDeckGroup/YourGStarter" to="." method="_on_your_g_starter_mouse_exited"]
[connection signal="pressed" from="YourDeck/YourDeckGroup/YourGStarter" to="." method="_on_your_gstarter_click"]
[connection signal="mouse_entered" from="YourDeck/YourDeckGroup/YourRStarter" to="." method="_on_your_r_starter_mouse_entered"]
[connection signal="mouse_exited" from="YourDeck/YourDeckGroup/YourRStarter" to="." method="_on_your_r_starter_mouse_exited"]
[connection signal="pressed" from="YourDeck/YourDeckGroup/YourRStarter" to="." method="_on_your_rstarter_click"]
[connection signal="mouse_entered" from="YourDeck/YourDeckGroup/YourGreen" to="." method="_on_your_green_mouse_entered"]
[connection signal="mouse_exited" from="YourDeck/YourDeckGroup/YourGreen" to="." method="_on_your_green_mouse_exited"]
[connection signal="pressed" from="YourDeck/YourDeckGroup/YourGreen" to="." method="_on_your_green_click"]
[connection signal="mouse_entered" from="YourDeck/YourDeckGroup/YourRed" to="." method="_on_your_red_mouse_entered"]
[connection signal="mouse_exited" from="YourDeck/YourDeckGroup/YourRed" to="." method="_on_your_red_mouse_exited"]
[connection signal="pressed" from="YourDeck/YourDeckGroup/YourRed" to="." method="_on_your_red_click"]
[connection signal="mouse_entered" from="YourDeck/YourDeckGroup/YourDragon" to="." method="_on_your_dragon_mouse_entered"]
[connection signal="mouse_exited" from="YourDeck/YourDeckGroup/YourDragon" to="." method="_on_your_dragon_mouse_exited"]
[connection signal="pressed" from="YourDeck/YourDeckGroup/YourDragon" to="." method="_on_your_dragon_click"]
[connection signal="text_changed" from="YourDeck/YourName" to="." method="_on_your_name_text_changed"]
[connection signal="pressed" from="QuitButton" to="." method="_on_quit_button_pressed"]
