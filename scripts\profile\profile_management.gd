extends Object
class_name profile_management

#varibles

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	pass
# func loadDeck(data) -> void:
# 	decks = data

func save() -> void:
	var save_file = FileAccess.open("user://savegame.save", FileAccess.WRITE)
	var data = {
		"name": PlayerProfile.player_name,
		"decks": PlayerProfile.decks,
		"resources": PlayerProfile.resources,
		"time": int(Time.get_unix_time_from_system())
	}
	var json_string = JSON.stringify(data)
	save_file.store_string(json_string)
	save_file.close()

func load(data) -> void:
	PlayerProfile.player_name = data["name"]
	PlayerProfile.decks = data["decks"]
	PlayerProfile.resources = data["resources"]
	PlayerProfile.time = data["time"]

	#convert cards key in string to integer
	for deck in PlayerProfile.decks:
		var new_cards = {}
		for card_key in deck["cards"]:
			var int_key = int(card_key)
			new_cards[int_key] = deck["cards"][card_key]
		deck["cards"] = new_cards

	print("Loading profile name: ", PlayerProfile.player_name)
	print("Loading profile decks: ", PlayerProfile.decks)
	print("Loading profile resources: ", PlayerProfile.resources)
	print("Loading profile time: ", PlayerProfile.time)

func update_resource(resource_name: String, amount: int) -> void:
	if PlayerProfile.resources.has(resource_name):
		PlayerProfile.resources[resource_name] += amount
	else:
		PlayerProfile.resources[resource_name] = amount

func update_progress(stage: String, status: int) -> void:
	if PlayerProfile.progress.has(stage):
		PlayerProfile.progress[stage] = status
	else:
		PlayerProfile.progress[stage] = status

func add_achievement(achievement_id: int) -> void:
	if achievement_id not in PlayerProfile.achievements:
		PlayerProfile.achievements.append(achievement_id)

func update_name(new_name: String) -> void:
	PlayerProfile.player_name = new_name

