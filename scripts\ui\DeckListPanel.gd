extends Panel

var tween: Tween
var is_open := false
@onready var card_list = $VBoxContainer/ScrollContainer/CardList

func _ready():
	# Start hidden and positioned off-screen
	position.x = -400
	size.x = 270
	size.y = 270
	
	# Set the panel to be centered vertically and 1/4 height plus 50 pixels
	var viewport_height = get_viewport().get_visible_rect().size.y
	custom_minimum_size.y = 270
	custom_minimum_size.x = 270
	position.y = viewport_height * 0.5 - size.y*0.5 -20
	
	# Create opaque background style
	var panel_style = StyleBoxFlat.new()
	panel_style.bg_color = Color(0.2, 0.2, 0.2, 1.0)  # Dark gray, fully opaque
	panel_style.border_width_left = 2
	panel_style.border_width_top = 2
	panel_style.border_width_right = 2
	panel_style.border_width_bottom = 2
	panel_style.border_color = Color(0.3, 0.3, 0.3, 1.0)
	
		# Add rounded corners
	panel_style.corner_radius_top_left = 12
	panel_style.corner_radius_top_right = 12
	panel_style.corner_radius_bottom_right = 12
	panel_style.corner_radius_bottom_left = 12

	# Apply the style to the panel
	add_theme_stylebox_override("panel", panel_style)

	hide()
	
	# Create style for the header label
	var header_style = StyleBoxFlat.new()
	header_style.bg_color = Color(0.8, 0.2, 0.2, 1.0)  # Red background
	header_style.content_margin_left = 8
	header_style.content_margin_right = 8
	header_style.content_margin_top = 4
	header_style.content_margin_bottom = 4
	
	# Add rounded corners only for top
	header_style.corner_radius_top_left = 12
	header_style.corner_radius_top_right = 12
	
	# Get reference to the header label and apply style
	var header_label = $VBoxContainer/Label
	header_label.add_theme_stylebox_override("normal", header_style)
	header_label.add_theme_color_override("font_color", Color.WHITE)  # Make text white for better contrast
	
	# Get reference to the deck list button and connect its pressed signal
	var deck_list_button = get_node("../Buttons/DeckList")
	deck_list_button.pressed.connect(func():
		show()
		if tween and tween.is_valid():
			tween.kill()
		tween = create_tween()
		tween.tween_property(self, "position:x", 0, 0.3).set_trans(Tween.TRANS_QUAD)
		update_deck_list()
		is_open = true
	)
	
	# Setup close button
	var close_button = Button.new()
	close_button.text = "<"
	close_button.custom_minimum_size = Vector2(40, 80)
	# Position the button on the right side
	close_button.position = Vector2(
		size.x,
		size.y*0.5 - 40
	)
	
	# Create style boxes for different states
	var normal_style = StyleBoxFlat.new()
	var hover_style = StyleBoxFlat.new()
	var pressed_style = StyleBoxFlat.new()
	
	# Normal state
	normal_style.bg_color = Color(0.2, 0.2, 0.2, 1)
	normal_style.border_width_left = 3
	normal_style.border_width_top = 3
	normal_style.border_width_right = 3
	normal_style.border_width_bottom = 3
	normal_style.border_color = Color(0.9, 0.3, 0.3, 1)
	normal_style.corner_radius_top_left = 6
	normal_style.corner_radius_top_right = 6
	normal_style.corner_radius_bottom_right = 6
	normal_style.corner_radius_bottom_left = 6
	normal_style.content_margin_left = 8
	normal_style.content_margin_right = 8
	normal_style.content_margin_top = 4
	normal_style.content_margin_bottom = 4
	
	# Hover state
	hover_style.bg_color = Color(0.9, 0.3, 0.3, 1)  # Lighter red for hover
	hover_style.border_width_left = 3
	hover_style.border_width_top = 3
	hover_style.border_width_right = 3
	hover_style.border_width_bottom = 3
	hover_style.border_color = Color(1, 0.4, 0.4, 1)
	hover_style.corner_radius_top_left = 6
	hover_style.corner_radius_top_right = 6
	hover_style.corner_radius_bottom_right = 6
	hover_style.corner_radius_bottom_left = 6
	hover_style.content_margin_left = 8
	hover_style.content_margin_right = 8
	hover_style.content_margin_top = 4
	hover_style.content_margin_bottom = 4
	
	# Pressed state
	pressed_style.bg_color = Color(0.7, 0.1, 0.1, 1)  # Darker red for pressed
	pressed_style.border_width_left = 3
	pressed_style.border_width_top = 3
	pressed_style.border_width_right = 3
	pressed_style.border_width_bottom = 3
	pressed_style.border_color = Color(0.8, 0.2, 0.2, 1)
	pressed_style.corner_radius_top_left = 6
	pressed_style.corner_radius_top_right = 6
	pressed_style.corner_radius_bottom_right = 6
	pressed_style.corner_radius_bottom_left = 6
	pressed_style.content_margin_left = 8
	pressed_style.content_margin_right = 8
	pressed_style.content_margin_top = 6  # Slightly offset when pressed
	pressed_style.content_margin_bottom = 2  # Slightly offset when pressed
	
	# Apply styles to button
	close_button.add_theme_stylebox_override("normal", normal_style)
	close_button.add_theme_stylebox_override("hover", hover_style)
	close_button.add_theme_stylebox_override("pressed", pressed_style)
	close_button.add_theme_color_override("font_color", Color.WHITE)
	close_button.add_theme_font_size_override("font_size", 24)
	
	close_button.pressed.connect(func():
		if tween and tween.is_valid():
			tween.kill()
		tween = create_tween()
		tween.tween_property(self, "position:x", -400, 0.3).set_trans(Tween.TRANS_QUAD)
		tween.tween_callback(func(): hide())
		is_open = false
	)
	
	add_child(close_button)

func _on_panel_gui_input(event: InputEvent):
	if event is InputEventMouseButton and event.pressed:
		get_viewport().set_input_as_handled()

func update_deck_list():
	# Clear existing items
	for child in card_list.get_children():
		child.queue_free()
	
	# Get the current deck
	var current_deck = DeckResource.MainDeck
	var deck_cards = ResourceService.GenerateDeck(current_deck, null)
	
	# Set number of cards per row
	var cards_per_row = 5
	
	# Calculate number of rows needed (rounded up)
	var num_rows = 4
	
	# Calculate card size based on available space
	var available_height = custom_minimum_size.y 
	var available_width = custom_minimum_size.x 
	
	# Calculate optimal card size
	var card_height = available_height / num_rows
	var card_width = min((available_width / cards_per_row) * 0.9, card_height * 0.7)  # Maintain aspect ratio
	
	# Create grid container for cards
	var grid = GridContainer.new()
	grid.columns = cards_per_row
	card_list.add_child(grid)
	
	# Add each card to the grid
	for card in deck_cards:
		var texture_rect = TextureRect.new()
		texture_rect.custom_minimum_size = Vector2(card_width*0.75, card_height*0.75)
		texture_rect.expand_mode = TextureRect.EXPAND_FIT_WIDTH
		texture_rect.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
		
		# Get card art from CardArtService
		if CardArtService.CardArts.has(card.name):
			texture_rect.texture = CardArtService.CardArts[card.name]
		else:
			texture_rect.texture = CardArtService.NullCardArt
			
		grid.add_child(texture_rect)

