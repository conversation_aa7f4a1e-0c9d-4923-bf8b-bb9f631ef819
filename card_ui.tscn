[gd_scene load_steps=2 format=3 uid="uid://dj3ay2qyknjuf"]

[ext_resource type="Script" path="res://scripts/ui/CardUI.gd" id="1_d7x6h"]

[node name="CardUI" type="Button"]
custom_minimum_size = Vector2(70, 90)
anchors_preset = -1
anchor_right = 0.061
anchor_bottom = 0.139
offset_right = -0.272003
offset_bottom = -0.0720062
script = ExtResource("1_d7x6h")

[node name="CardColor" type="ColorRect" parent="."]
layout_mode = 0
offset_right = 70.0
offset_bottom = 5.0
color = Color(0.0962047, 0.0962048, 0.0962047, 1)

[node name="CardRarity" type="ColorRect" parent="."]
layout_mode = 0
offset_left = 61.0
offset_top = 81.0
offset_right = 66.0
offset_bottom = 86.0
color = Color(0.0768359, 0.0768358, 0.0768356, 1)

[node name="CostLabel" type="Label" parent="."]
layout_mode = 0
offset_left = 6.0
offset_top = 7.0
offset_right = 23.0
offset_bottom = 30.0
theme_override_font_sizes/font_size = 10
vertical_alignment = 1

[node name="AbilityLabel" type="Label" parent="."]
layout_mode = 0
offset_left = 6.0
offset_top = 30.0
offset_right = 65.0
offset_bottom = 84.0
theme_override_font_sizes/font_size = 9
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 2

[node name="PowerLabel" type="Label" parent="."]
layout_mode = 0
offset_left = 43.0
offset_top = 7.0
offset_right = 65.0
offset_bottom = 30.0
theme_override_font_sizes/font_size = 16
horizontal_alignment = 2
vertical_alignment = 1
