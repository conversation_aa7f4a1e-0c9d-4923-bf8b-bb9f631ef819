[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://b0y04trna5ols"
path="res://.godot/imported/diffuse.png-aef148bd219351d50cfe531d89e73d33.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://effects/halloween_explosion/mesh/diffuse.png"
dest_files=["res://.godot/imported/diffuse.png-aef148bd219351d50cfe531d89e73d33.ctex"]

[params]

compress/mode=0
compress/high_quality=true
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
