[gd_resource type="ShaderMaterial" load_steps=6 format=3 uid="uid://d1nm3jquvgrb6"]

[ext_resource type="Shader" path="res://effects/fireball/materials/fireball_shell.gdshader" id="1_8itha"]

[sub_resource type="Gradient" id="Gradient_vxs6y"]
offsets = PackedFloat32Array(0.1, 0.9)
colors = PackedColorArray(0.141176, 0.262745, 0.572549, 1, 3.85046e-07, 0.840597, 0.855262, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_yyo8l"]
gradient = SubResource("Gradient_vxs6y")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_ap6v2"]
noise_type = 2
fractal_octaves = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_bcgo8"]
seamless = true
noise = SubResource("FastNoiseLite_ap6v2")

[resource]
resource_local_to_scene = true
render_priority = 0
shader = ExtResource("1_8itha")
shader_parameter/def_x = 0.0
shader_parameter/def_z = 0.0
shader_parameter/voronoi_sampler = SubResource("NoiseTexture2D_bcgo8")
shader_parameter/gradient_sampler = SubResource("GradientTexture1D_yyo8l")
