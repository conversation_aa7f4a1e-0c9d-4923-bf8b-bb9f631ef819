[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://dbnj25mvec2hr"
path="res://.godot/imported/rock_normal.png-68a002b27b0b50b80c4868969aeb903e.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://effects/earth_attack/textures/rock_normal.png"
dest_files=["res://.godot/imported/rock_normal.png-68a002b27b0b50b80c4868969aeb903e.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=1
roughness/src_normal="res://effects/earth_attack/rock_normal.png"
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
