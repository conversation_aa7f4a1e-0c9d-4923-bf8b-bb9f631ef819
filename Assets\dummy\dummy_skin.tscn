[gd_scene load_steps=8 format=3 uid="uid://ddjouhm76gjbx"]

[ext_resource type="PackedScene" uid="uid://1e45y3wup8c4" path="res://Assets/dummy/dummy.glb" id="1_rr1a5"]
[ext_resource type="Script" path="res://assets/dummy/dummy_skin.gd" id="1_uvklm"]
[ext_resource type="Material" uid="uid://je1p01igsoee" path="res://Assets/dummy/dummy_mat.tres" id="3_wu7ut"]

[sub_resource type="Animation" id="Animation_d556y"]
resource_name = "idle"
length = 5.33333
loop_mode = 1
tracks/0/type = "position_3d"
tracks/0/imported = true
tracks/0/enabled = true
tracks/0/path = NodePath("RIG-Armature/Skeleton3D:DEF-bun")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = PackedFloat32Array(0, 1, 0.023416, 1.07738, -0.212713, 0.0333333, 1, 0.0296755, 1.07726, -0.213233, 0.0666667, 1, 0.035697, 1.07711, -0.213747, 0.1, 1, 0.0414635, 1.07691, -0.214239, 0.133333, 1, 0.046958, 1.07667, -0.214695, 0.166667, 1, 0.0521632, 1.07641, -0.215101, 0.2, 1, 0.0570617, 1.07612, -0.215446, 0.233333, 1, 0.0616356, 1.07581, -0.215721, 0.266667, 1, 0.0658671, 1.0755, -0.215919, 0.3, 1, 0.069738, 1.07518, -0.216034, 0.333333, 1, 0.0732298, 1.07486, -0.216062, 0.366667, 1, 0.0763241, 1.07456, -0.216004, 0.4, 1, 0.0790023, 1.07427, -0.215861, 0.433333, 1, 0.0812455, 1.074, -0.215638, 0.466667, 1, 0.0830352, 1.07376, -0.215339, 0.5, 1, 0.0843527, 1.07356, -0.214974, 0.566667, 1, 0.0854971, 1.07329, -0.214087, 0.633333, 1, 0.0845317, 1.07321, -0.213078, 0.666667, 1, 0.0832128, 1.07325, -0.212565, 0.7, 1, 0.0813202, 1.07334, -0.212066, 0.733333, 1, 0.0788744, 1.07349, -0.211591, 0.766667, 1, 0.0759036, 1.07368, -0.211149, 0.8, 1, 0.072436, 1.07392, -0.210748, 0.833333, 1, 0.0684999, 1.07419, -0.210394, 0.866667, 1, 0.0641237, 1.07449, -0.210094, 0.9, 1, 0.0593359, 1.07481, -0.209854, 0.933333, 1, 0.0541651, 1.07514, -0.209679, 0.966667, 1, 0.0486399, 1.07548, -0.209573, 1, 1, 0.0427891, 1.07582, -0.209541, 1.03333, 1, 0.0366453, 1.07615, -0.209584, 1.06667, 1, 0.0302568, 1.07645, -0.2097, 1.1, 1, 0.0236757, 1.07673, -0.209888, 1.13333, 1, 0.0169543, 1.07697, -0.210143, 1.26667, 1, -0.0102962, 1.07745, -0.211712, 1.3, 1, -0.0169411, 1.07744, -0.212202, 1.33333, 1, -0.023416, 1.07738, -0.212713, 1.36667, 1, -0.0296755, 1.07726, -0.213233, 1.4, 1, -0.0356969, 1.07711, -0.213747, 1.43333, 1, -0.0414635, 1.07691, -0.214239, 1.46667, 1, -0.046958, 1.07667, -0.214695, 1.5, 1, -0.0521632, 1.07641, -0.215101, 1.53333, 1, -0.0570617, 1.07612, -0.215446, 1.56667, 1, -0.0616356, 1.07581, -0.215721, 1.6, 1, -0.0658671, 1.0755, -0.215919, 1.63333, 1, -0.069738, 1.07518, -0.216034, 1.66667, 1, -0.0732298, 1.07486, -0.216062, 1.7, 1, -0.0763241, 1.07456, -0.216004, 1.73333, 1, -0.0790022, 1.07427, -0.215861, 1.76667, 1, -0.0812455, 1.074, -0.215638, 1.8, 1, -0.0830352, 1.07376, -0.215339, 1.83333, 1, -0.0843527, 1.07356, -0.214974, 1.9, 1, -0.085497, 1.07329, -0.214087, 1.96667, 1, -0.0845317, 1.07321, -0.213078, 2, 1, -0.0832128, 1.07325, -0.212565, 2.03333, 1, -0.0813202, 1.07334, -0.212066, 2.06667, 1, -0.0788744, 1.07349, -0.211591, 2.1, 1, -0.0759036, 1.07368, -0.211149, 2.13333, 1, -0.072436, 1.07392, -0.210748, 2.16667, 1, -0.0684999, 1.07419, -0.210394, 2.2, 1, -0.0641237, 1.07449, -0.210094, 2.23333, 1, -0.0593359, 1.07481, -0.209854, 2.26667, 1, -0.0541651, 1.07514, -0.209679, 2.3, 1, -0.0486399, 1.07548, -0.209573, 2.33333, 1, -0.0427891, 1.07582, -0.209541, 2.36667, 1, -0.0366453, 1.07615, -0.209584, 2.4, 1, -0.0302568, 1.07645, -0.2097, 2.43333, 1, -0.0236758, 1.07673, -0.209888, 2.46667, 1, -0.0169543, 1.07697, -0.210143, 2.6, 1, 0.0102962, 1.07745, -0.211712, 2.63333, 1, 0.0169411, 1.07744, -0.212202, 2.66667, 1, 0.023416, 1.07738, -0.212713, 2.7, 1, 0.0296755, 1.07726, -0.213233, 2.73333, 1, 0.035697, 1.07711, -0.213747, 2.76667, 1, 0.0414635, 1.07691, -0.214239, 2.8, 1, 0.046958, 1.07667, -0.214695, 2.83333, 1, 0.0521632, 1.07641, -0.215101, 2.86667, 1, 0.0570617, 1.07612, -0.215446, 2.9, 1, 0.0616356, 1.07581, -0.215721, 2.93333, 1, 0.0658671, 1.0755, -0.215919, 2.96667, 1, 0.069738, 1.07518, -0.216034, 3, 1, 0.0732298, 1.07486, -0.216062, 3.03333, 1, 0.0763241, 1.07456, -0.216004, 3.06667, 1, 0.0790023, 1.07427, -0.215861, 3.1, 1, 0.0812455, 1.074, -0.215638, 3.13333, 1, 0.0830352, 1.07376, -0.215339, 3.16667, 1, 0.0843527, 1.07356, -0.214974, 3.23333, 1, 0.0854971, 1.07329, -0.214087, 3.3, 1, 0.0845317, 1.07321, -0.213078, 3.33333, 1, 0.0832128, 1.07325, -0.212565, 3.36667, 1, 0.0813202, 1.07334, -0.212066, 3.4, 1, 0.0788744, 1.07349, -0.211591, 3.43333, 1, 0.0759036, 1.07368, -0.211149, 3.46667, 1, 0.072436, 1.07392, -0.210748, 3.5, 1, 0.0684999, 1.07419, -0.210394, 3.53333, 1, 0.0641237, 1.07449, -0.210094, 3.56667, 1, 0.0593359, 1.07481, -0.209854, 3.6, 1, 0.0541651, 1.07514, -0.209679, 3.63333, 1, 0.0486399, 1.07548, -0.209573, 3.66667, 1, 0.0427891, 1.07582, -0.209541, 3.7, 1, 0.0366453, 1.07615, -0.209584, 3.73333, 1, 0.0302568, 1.07645, -0.2097, 3.76667, 1, 0.0236757, 1.07673, -0.209888, 3.8, 1, 0.0169543, 1.07697, -0.210143, 3.93333, 1, -0.0102962, 1.07745, -0.211712, 3.96667, 1, -0.0169411, 1.07744, -0.212202, 4, 1, -0.023416, 1.07738, -0.212713, 4.03333, 1, -0.0296755, 1.07726, -0.213233, 4.06667, 1, -0.0356969, 1.07711, -0.213747, 4.1, 1, -0.0414635, 1.07691, -0.214239, 4.13333, 1, -0.046958, 1.07667, -0.214695, 4.16667, 1, -0.0521632, 1.07641, -0.215101, 4.2, 1, -0.0570617, 1.07612, -0.215446, 4.23333, 1, -0.0616356, 1.07581, -0.215721, 4.26667, 1, -0.0658671, 1.0755, -0.215919, 4.3, 1, -0.069738, 1.07518, -0.216034, 4.33333, 1, -0.0732298, 1.07486, -0.216062, 4.36667, 1, -0.0763241, 1.07456, -0.216004, 4.4, 1, -0.0790022, 1.07427, -0.215861, 4.43333, 1, -0.0812455, 1.074, -0.215638, 4.46667, 1, -0.0830352, 1.07376, -0.215339, 4.5, 1, -0.0843527, 1.07356, -0.214974, 4.56667, 1, -0.085497, 1.07329, -0.214087, 4.63333, 1, -0.0845317, 1.07321, -0.213078, 4.66667, 1, -0.0832128, 1.07325, -0.212565, 4.7, 1, -0.0813202, 1.07334, -0.212066, 4.73333, 1, -0.0788744, 1.07349, -0.211591, 4.76667, 1, -0.0759036, 1.07368, -0.211149, 4.8, 1, -0.072436, 1.07392, -0.210748, 4.83333, 1, -0.0684999, 1.07419, -0.210394, 4.86667, 1, -0.0641237, 1.07449, -0.210094, 4.9, 1, -0.0593359, 1.07481, -0.209854, 4.93333, 1, -0.0541651, 1.07514, -0.209679, 4.96667, 1, -0.0486399, 1.07548, -0.209573, 5, 1, -0.0427891, 1.07582, -0.209541, 5.03333, 1, -0.0366453, 1.07615, -0.209584, 5.06667, 1, -0.0302568, 1.07645, -0.2097, 5.1, 1, -0.0236758, 1.07673, -0.209888, 5.13333, 1, -0.0169543, 1.07697, -0.210143, 5.26667, 1, 0.0102962, 1.07745, -0.211712, 5.3, 1, 0.0169411, 1.07744, -0.212202, 5.33333, 1, 0.023416, 1.07738, -0.212713)
tracks/1/type = "rotation_3d"
tracks/1/imported = true
tracks/1/enabled = true
tracks/1/path = NodePath("RIG-Armature/Skeleton3D:DEF-bun")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = PackedFloat32Array(0, 1, -0.372236, 0.0946955, 0.0809501, 0.919739, 0.0333333, 1, -0.372491, 0.0936912, 0.0754738, 0.920204, 0.0666667, 1, -0.372855, 0.0920035, 0.0694869, 0.920699, 0.1, 1, -0.373311, 0.0896718, 0.0630394, 0.921208, 0.133333, 1, -0.373844, 0.0867355, 0.0561813, 0.921716, 0.166667, 1, -0.374435, 0.0832341, 0.0489631, 0.922211, 0.2, 1, -0.375065, 0.0792069, 0.0414351, 0.922678, 0.233333, 1, -0.375717, 0.0746936, 0.0336481, 0.923106, 0.266667, 1, -0.376373, 0.0697342, 0.0256531, 0.923484, 0.3, 1, -0.377016, 0.0643688, 0.0175014, 0.923802, 0.333333, 1, -0.377629, 0.0586378, 0.00924444, 0.924052, 0.366667, 1, -0.378198, 0.0525822, 0.000933914, 0.92423, 0.533333, 1, -0.380004, 0.0188823, -0.0396027, 0.923944, 0.566667, 1, -0.380109, 0.0117505, -0.0471421, 0.923665, 0.6, 1, -0.380124, 0.00458627, -0.0543708, 0.923325, 0.633333, 1, -0.380049, -0.0025685, -0.0612369, 0.922934, 0.666667, 1, -0.379889, -0.00967185, -0.0676887, 0.922502, 0.7, 1, -0.379649, -0.0166844, -0.0736806, 0.922041, 0.733333, 1, -0.379335, -0.0235761, -0.0791896, 0.921563, 0.766667, 1, -0.378952, -0.0303195, -0.0841988, 0.921079, 0.8, 1, -0.378507, -0.0368873, -0.0886914, 0.920601, 0.833333, 1, -0.37801, -0.0432521, -0.0926508, 0.920138, 0.866667, 1, -0.377468, -0.0493868, -0.0960606, 0.919702, 0.9, 1, -0.376894, -0.0552647, -0.0989044, 0.919301, 0.933333, 1, -0.3763, -0.0608588, -0.101166, 0.918945, 0.966667, 1, -0.375697, -0.0661429, -0.102829, 0.918642, 1, 1, -0.3751, -0.0710905, -0.103878, 0.918398, 1.03333, 1, -0.374523, -0.0756742, -0.1043, 0.91822, 1.06667, 1, -0.373977, -0.0798594, -0.104099, 0.918111, 1.1, 1, -0.373477, -0.0836103, -0.103283, 0.918073, 1.13333, 1, -0.373034, -0.0868911, -0.101859, 0.918107, 1.2, 1, -0.372372, -0.0918992, -0.0972169, 0.918391, 1.23333, 1, -0.372175, -0.0935549, -0.094013, 0.918638, 1.26667, 1, -0.372081, -0.0945971, -0.0902296, 0.918948, 1.3, 1, -0.3721, -0.0949894, -0.0858732, 0.919318, 1.33333, 1, -0.372236, -0.0946955, -0.0809501, 0.919739, 1.36667, 1, -0.372491, -0.0936912, -0.0754738, 0.920204, 1.4, 1, -0.372855, -0.0920035, -0.0694869, 0.920699, 1.43333, 1, -0.373311, -0.0896718, -0.0630394, 0.921208, 1.46667, 1, -0.373844, -0.0867355, -0.0561813, 0.921716, 1.5, 1, -0.374435, -0.0832341, -0.0489631, 0.922211, 1.53333, 1, -0.375065, -0.0792069, -0.0414351, 0.922678, 1.56667, 1, -0.375717, -0.0746936, -0.0336481, 0.923106, 1.6, 1, -0.376373, -0.0697342, -0.0256531, 0.923484, 1.63333, 1, -0.377015, -0.0643688, -0.0175014, 0.923802, 1.66667, 1, -0.377629, -0.0586378, -0.00924444, 0.924052, 1.7, 1, -0.378198, -0.0525822, -0.00093393, 0.92423, 1.86667, 1, -0.380004, -0.0188823, 0.0396027, 0.923944, 1.9, 1, -0.380109, -0.0117505, 0.0471421, 0.923665, 1.93333, 1, -0.380123, -0.00458627, 0.0543708, 0.923325, 1.96667, 1, -0.380049, 0.00256851, 0.0612369, 0.922934, 2, 1, -0.379889, 0.00967186, 0.0676887, 0.922502, 2.03333, 1, -0.379649, 0.0166844, 0.0736806, 0.922041, 2.06667, 1, -0.379335, 0.0235761, 0.0791896, 0.921563, 2.1, 1, -0.378952, 0.0303195, 0.0841988, 0.921079, 2.13333, 1, -0.378507, 0.0368873, 0.0886914, 0.920601, 2.16667, 1, -0.37801, 0.0432521, 0.0926508, 0.920138, 2.2, 1, -0.377468, 0.0493868, 0.0960606, 0.919702, 2.23333, 1, -0.376894, 0.0552647, 0.0989044, 0.919301, 2.26667, 1, -0.3763, 0.0608588, 0.101166, 0.918945, 2.3, 1, -0.375697, 0.0661429, 0.102829, 0.918642, 2.33333, 1, -0.3751, 0.0710905, 0.103878, 0.918398, 2.36667, 1, -0.374523, 0.0756742, 0.1043, 0.91822, 2.4, 1, -0.373977, 0.0798594, 0.104099, 0.918111, 2.43333, 1, -0.373477, 0.0836102, 0.103283, 0.918073, 2.46667, 1, -0.373034, 0.0868911, 0.101859, 0.918107, 2.53333, 1, -0.372372, 0.0918992, 0.0972169, 0.918391, 2.56667, 1, -0.372175, 0.0935549, 0.094013, 0.918638, 2.6, 1, -0.372081, 0.094597, 0.0902296, 0.918948, 2.63333, 1, -0.3721, 0.0949894, 0.0858732, 0.919318, 2.66667, 1, -0.372236, 0.0946955, 0.0809501, 0.919739, 2.7, 1, -0.372491, 0.0936912, 0.0754738, 0.920204, 2.73333, 1, -0.372855, 0.0920035, 0.0694869, 0.920699, 2.76667, 1, -0.373311, 0.0896718, 0.0630394, 0.921208, 2.8, 1, -0.373844, 0.0867355, 0.0561813, 0.921716, 2.83333, 1, -0.374435, 0.0832341, 0.0489631, 0.922211, 2.86667, 1, -0.375065, 0.0792069, 0.0414351, 0.922678, 2.9, 1, -0.375717, 0.0746936, 0.0336481, 0.923106, 2.93333, 1, -0.376373, 0.0697342, 0.0256531, 0.923484, 2.96667, 1, -0.377016, 0.0643688, 0.0175014, 0.923802, 3, 1, -0.377629, 0.0586378, 0.00924444, 0.924052, 3.03333, 1, -0.378198, 0.0525822, 0.000933914, 0.92423, 3.2, 1, -0.380004, 0.0188823, -0.0396027, 0.923944, 3.23333, 1, -0.380109, 0.0117505, -0.0471421, 0.923665, 3.26667, 1, -0.380124, 0.00458627, -0.0543708, 0.923325, 3.3, 1, -0.380049, -0.0025685, -0.0612369, 0.922934, 3.33333, 1, -0.379889, -0.00967185, -0.0676887, 0.922502, 3.36667, 1, -0.379649, -0.0166844, -0.0736806, 0.922041, 3.4, 1, -0.379335, -0.0235761, -0.0791896, 0.921563, 3.43333, 1, -0.378952, -0.0303195, -0.0841988, 0.921079, 3.46667, 1, -0.378507, -0.0368873, -0.0886914, 0.920601, 3.5, 1, -0.37801, -0.0432521, -0.0926508, 0.920138, 3.53333, 1, -0.377468, -0.0493868, -0.0960606, 0.919702, 3.56667, 1, -0.376894, -0.0552647, -0.0989044, 0.919301, 3.6, 1, -0.3763, -0.0608588, -0.101166, 0.918945, 3.63333, 1, -0.375697, -0.0661429, -0.102829, 0.918642, 3.66667, 1, -0.3751, -0.0710905, -0.103878, 0.918398, 3.7, 1, -0.374523, -0.0756742, -0.1043, 0.91822, 3.73333, 1, -0.373977, -0.0798594, -0.104099, 0.918111, 3.76667, 1, -0.373477, -0.0836103, -0.103283, 0.918073, 3.8, 1, -0.373034, -0.0868911, -0.101859, 0.918107, 3.86667, 1, -0.372372, -0.0918992, -0.0972169, 0.918391, 3.9, 1, -0.372175, -0.0935549, -0.094013, 0.918638, 3.93333, 1, -0.372081, -0.0945971, -0.0902296, 0.918948, 3.96667, 1, -0.3721, -0.0949894, -0.0858732, 0.919318, 4, 1, -0.372236, -0.0946955, -0.0809501, 0.919739, 4.03333, 1, -0.372491, -0.0936912, -0.0754738, 0.920204, 4.06667, 1, -0.372855, -0.0920035, -0.0694869, 0.920699, 4.1, 1, -0.373311, -0.0896718, -0.0630394, 0.921208, 4.13333, 1, -0.373844, -0.0867355, -0.0561813, 0.921716, 4.16667, 1, -0.374435, -0.0832341, -0.0489631, 0.922211, 4.2, 1, -0.375065, -0.0792069, -0.0414351, 0.922678, 4.23333, 1, -0.375717, -0.0746936, -0.0336481, 0.923106, 4.26667, 1, -0.376373, -0.0697342, -0.0256531, 0.923484, 4.3, 1, -0.377015, -0.0643688, -0.0175014, 0.923802, 4.33333, 1, -0.377629, -0.0586378, -0.00924444, 0.924052, 4.36667, 1, -0.378198, -0.0525822, -0.00093393, 0.92423, 4.53333, 1, -0.380004, -0.0188823, 0.0396027, 0.923944, 4.56667, 1, -0.380109, -0.0117505, 0.0471421, 0.923665, 4.6, 1, -0.380123, -0.00458627, 0.0543708, 0.923325, 4.63333, 1, -0.380049, 0.00256851, 0.0612369, 0.922934, 4.66667, 1, -0.379889, 0.00967186, 0.0676887, 0.922502, 4.7, 1, -0.379649, 0.0166844, 0.0736806, 0.922041, 4.73333, 1, -0.379335, 0.0235761, 0.0791896, 0.921563, 4.76667, 1, -0.378952, 0.0303195, 0.0841988, 0.921079, 4.8, 1, -0.378507, 0.0368873, 0.0886914, 0.920601, 4.83333, 1, -0.37801, 0.0432521, 0.0926508, 0.920138, 4.86667, 1, -0.377468, 0.0493868, 0.0960606, 0.919702, 4.9, 1, -0.376894, 0.0552647, 0.0989044, 0.919301, 4.93333, 1, -0.3763, 0.0608588, 0.101166, 0.918945, 4.96667, 1, -0.375697, 0.0661429, 0.102829, 0.918642, 5, 1, -0.3751, 0.0710905, 0.103878, 0.918398, 5.03333, 1, -0.374523, 0.0756742, 0.1043, 0.91822, 5.06667, 1, -0.373977, 0.0798594, 0.104099, 0.918111, 5.1, 1, -0.373477, 0.0836102, 0.103283, 0.918073, 5.13333, 1, -0.373034, 0.0868911, 0.101859, 0.918107, 5.2, 1, -0.372372, 0.0918992, 0.0972169, 0.918391, 5.23333, 1, -0.372175, 0.0935549, 0.094013, 0.918638, 5.26667, 1, -0.372081, 0.094597, 0.0902296, 0.918948, 5.3, 1, -0.3721, 0.0949894, 0.0858732, 0.919318, 5.33333, 1, -0.372236, 0.0946955, 0.0809501, 0.919739)
tracks/2/type = "rotation_3d"
tracks/2/imported = true
tracks/2/enabled = true
tracks/2/path = NodePath("RIG-Armature/Skeleton3D:DEF-bun.001")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = PackedFloat32Array(0, 1, -7.1433e-08, 8.51822e-09, 0.0637703, 0.997965, 0.0333333, 1, -9.75483e-08, 8.73602e-09, 0.065856, 0.997829, 0.0666667, 1, -8.96015e-08, 6.1098e-09, 0.0675234, 0.997718, 0.1, 1, -8.16566e-08, 6.23731e-09, 0.0687466, 0.997634, 0.166667, 1, -7.04445e-08, 6.80938e-09, 0.0697565, 0.997564, 0.233333, 1, -6.22858e-08, 1.07883e-08, 0.0687466, 0.997634, 0.266667, 1, -7.48997e-08, 9.43523e-09, 0.0675234, 0.997718, 0.3, 1, -6.5581e-08, 2.90259e-09, 0.0658559, 0.997829, 0.333333, 1, -6.96833e-08, 7.11839e-09, 0.0637703, 0.997965, 0.366667, 1, -6.99381e-08, 9.19255e-09, 0.0612924, 0.99812, 0.4, 1, -9.86489e-08, 5.16373e-09, 0.0584483, 0.99829, 0.433333, 1, -8.83612e-08, 2.49987e-09, 0.0552639, 0.998472, 0.466667, 1, -9.01506e-08, 1.90239e-09, 0.0517651, 0.998659, 0.5, 1, -1.02673e-07, 1.85769e-09, 0.0479781, 0.998848, 0.533333, 1, -1.03974e-07, 4.75695e-09, 0.0439289, 0.999035, 0.566667, 1, -8.4409e-08, 5.99926e-09, 0.0396435, 0.999214, 0.6, 1, -9.39637e-08, 2.50137e-09, 0.0351481, 0.999382, 0.633333, 1, -9.08075e-08, 4.45931e-09, 0.0304687, 0.999536, 0.666667, 1, -9.57145e-08, 2.2078e-09, 0.0256317, 0.999671, 0.7, 1, -8.61794e-08, 1.22384e-09, 0.0206632, 0.999787, 0.733333, 1, -9.04986e-08, -2.09962e-09, 0.0155895, 0.999879, 0.766667, 1, -1.04244e-07, -4.49957e-09, 0.0104369, 0.999946, 0.8, 1, -1.0626e-07, -4.12552e-09, 0.0052316, 0.999986, 0.833333, 1, -6.76082e-08, -4.6566e-09, -1.86264e-09, 1, 0.9, 1, -1.04419e-07, -2.02001e-09, -0.0104369, 0.999946, 0.933333, 1, -7.49552e-08, -5.35185e-09, -0.0155895, 0.999878, 0.966667, 1, -9.35734e-08, -9.6075e-09, -0.0206632, 0.999787, 1, 1, -9.56563e-08, -4.53684e-09, -0.0256317, 0.999672, 1.03333, 1, -9.5641e-08, -8.76865e-09, -0.0304687, 0.999536, 1.06667, 1, -7.95484e-08, -5.53001e-09, -0.0351481, 0.999382, 1.1, 1, -8.88472e-08, -1.9048e-08, -0.0396435, 0.999214, 1.13333, 1, -8.5563e-08, -8.31102e-09, -0.0439289, 0.999035, 1.16667, 1, -9.81274e-08, -6.86929e-09, -0.0479781, 0.998848, 1.2, 1, -7.57248e-08, -3.53436e-09, -0.0517651, 0.998659, 1.23333, 1, -8.59601e-08, -9.49545e-09, -0.0552639, 0.998472, 1.26667, 1, -1.00398e-07, -1.356e-08, -0.0584483, 0.99829, 1.3, 1, -9.01159e-08, -8.25944e-09, -0.0612924, 0.99812, 1.33333, 1, -7.93654e-08, -1.98564e-09, -0.0637703, 0.997965, 1.36667, 1, -1.12015e-07, -1.71361e-08, -0.065856, 0.997829, 1.4, 1, -8.96015e-08, -2.37596e-09, -0.0675234, 0.997718, 1.43333, 1, -1.02661e-07, -1.2772e-08, -0.0687466, 0.997634, 1.5, 1, -5.45734e-08, -1.19441e-08, -0.0697565, 0.997564, 1.56667, 1, -7.62888e-08, -1.02048e-08, -0.0687466, 0.997634, 1.6, 1, -8.19006e-08, -9.20183e-09, -0.0675234, 0.997718, 1.63333, 1, -8.65814e-08, -5.93595e-09, -0.065856, 0.997829, 1.66667, 1, -8.42649e-08, -8.05158e-09, -0.0637703, 0.997965, 1.7, 1, -7.67029e-08, -6.85982e-09, -0.0612924, 0.99812, 1.73333, 1, -9.11856e-08, -5.1637e-09, -0.0584483, 0.99829, 1.76667, 1, -7.99082e-08, -2.73302e-09, -0.0552639, 0.998472, 1.8, 1, -9.76694e-08, -2.48522e-09, -0.0517651, 0.998659, 1.83333, 1, -1.01566e-07, -5.75611e-10, -0.0479781, 0.998848, 1.86667, 1, -8.09601e-08, -5.04824e-09, -0.0439289, 0.999035, 1.9, 1, -8.36235e-08, -6.23225e-09, -0.0396435, 0.999214, 1.93333, 1, -9.34686e-08, -3.89919e-09, -0.035148, 0.999382, 1.96667, 1, -8.23052e-08, -2.4793e-09, -0.0304687, 0.999536, 2, 1, -9.46082e-08, 5.8712e-10, -0.0256317, 0.999671, 2.03333, 1, -7.82615e-08, 1.73477e-10, -0.0206632, 0.999787, 2.06667, 1, -8.20574e-08, -2.09182e-09, -0.0155895, 0.999879, 2.1, 1, -9.51051e-08, 7.74111e-10, -0.0104369, 0.999946, 2.13333, 1, -1.05853e-07, -1.50612e-09, -0.00523158, 0.999986, 2.16667, 1, -8.184e-08, 2.79398e-09, 7.45058e-09, 1, 2.23333, 1, -8.82363e-08, 9.93671e-09, 0.0104369, 0.999946, 2.26667, 1, -9.75425e-08, 5.35187e-09, 0.0155895, 0.999878, 2.3, 1, -8.56554e-08, 4.94992e-09, 0.0206632, 0.999787, 2.33333, 1, -1.03109e-07, 8.26338e-09, 0.0256317, 0.999672, 2.36667, 1, -9.61068e-08, 4.10991e-09, 0.0304687, 0.999536, 2.4, 1, -8.56057e-08, 7.39384e-09, 0.0351481, 0.999382, 2.43333, 1, -7.25363e-08, 4.06931e-10, 0.0396435, 0.999214, 2.46667, 1, -1.00479e-07, 1.20399e-08, 0.0439289, 0.999035, 2.5, 1, -1.06053e-07, 5.00452e-09, 0.0479781, 0.998848, 2.53333, 1, -8.36517e-08, 9.12983e-09, 0.0517652, 0.998659, 2.56667, 1, -7.29017e-08, 1.1361e-08, 0.0552639, 0.998472, 2.6, 1, -9.15354e-08, 4.23081e-09, 0.0584483, 0.99829, 2.63333, 1, -1.13443e-07, 8.25947e-09, 0.0612924, 0.99812, 2.66667, 1, -7.1433e-08, 8.51822e-09, 0.0637703, 0.997965, 2.7, 1, -9.75483e-08, 8.73602e-09, 0.065856, 0.997829, 2.73333, 1, -8.96015e-08, 6.1098e-09, 0.0675234, 0.997718, 2.76667, 1, -8.16566e-08, 6.23731e-09, 0.0687466, 0.997634, 2.83333, 1, -7.04445e-08, 6.80938e-09, 0.0697565, 0.997564, 2.9, 1, -6.22858e-08, 1.07883e-08, 0.0687466, 0.997634, 2.93333, 1, -7.48997e-08, 9.43523e-09, 0.0675234, 0.997718, 2.96667, 1, -6.5581e-08, 2.90259e-09, 0.0658559, 0.997829, 3, 1, -6.96833e-08, 7.11839e-09, 0.0637703, 0.997965, 3.03333, 1, -6.99381e-08, 9.19255e-09, 0.0612924, 0.99812, 3.06667, 1, -9.86489e-08, 5.16373e-09, 0.0584483, 0.99829, 3.1, 1, -8.83612e-08, 2.49987e-09, 0.0552639, 0.998472, 3.13333, 1, -9.01506e-08, 1.90239e-09, 0.0517651, 0.998659, 3.16667, 1, -1.02673e-07, 1.85769e-09, 0.0479781, 0.998848, 3.2, 1, -1.03974e-07, 4.75695e-09, 0.0439289, 0.999035, 3.23333, 1, -8.4409e-08, 5.99926e-09, 0.0396435, 0.999214, 3.26667, 1, -9.39637e-08, 2.50137e-09, 0.0351481, 0.999382, 3.3, 1, -9.08075e-08, 4.45931e-09, 0.0304687, 0.999536, 3.33333, 1, -9.57145e-08, 2.2078e-09, 0.0256317, 0.999671, 3.36667, 1, -8.61794e-08, 1.22384e-09, 0.0206632, 0.999787, 3.4, 1, -9.04986e-08, -2.09962e-09, 0.0155895, 0.999879, 3.43333, 1, -1.04244e-07, -4.49957e-09, 0.0104369, 0.999946, 3.46667, 1, -1.0626e-07, -4.12552e-09, 0.0052316, 0.999986, 3.5, 1, -6.76082e-08, -4.6566e-09, -1.86264e-09, 1, 3.56667, 1, -1.04419e-07, -2.02001e-09, -0.0104369, 0.999946, 3.6, 1, -7.49552e-08, -5.35185e-09, -0.0155895, 0.999878, 3.63333, 1, -9.35734e-08, -9.6075e-09, -0.0206632, 0.999787, 3.66667, 1, -9.56563e-08, -4.53684e-09, -0.0256317, 0.999672, 3.7, 1, -9.5641e-08, -8.76865e-09, -0.0304687, 0.999536, 3.73333, 1, -7.95484e-08, -5.53001e-09, -0.0351481, 0.999382, 3.76667, 1, -8.88472e-08, -1.9048e-08, -0.0396435, 0.999214, 3.8, 1, -8.5563e-08, -8.31102e-09, -0.0439289, 0.999035, 3.83333, 1, -9.81274e-08, -6.86929e-09, -0.0479781, 0.998848, 3.86667, 1, -7.57248e-08, -3.53436e-09, -0.0517651, 0.998659, 3.9, 1, -8.59601e-08, -9.49545e-09, -0.0552639, 0.998472, 3.93333, 1, -1.00398e-07, -1.356e-08, -0.0584483, 0.99829, 3.96667, 1, -9.01159e-08, -8.25944e-09, -0.0612924, 0.99812, 4, 1, -7.93654e-08, -1.98564e-09, -0.0637703, 0.997965, 4.03333, 1, -1.12015e-07, -1.71361e-08, -0.065856, 0.997829, 4.06667, 1, -8.96015e-08, -2.37596e-09, -0.0675234, 0.997718, 4.1, 1, -1.02661e-07, -1.2772e-08, -0.0687466, 0.997634, 4.16667, 1, -5.45734e-08, -1.19441e-08, -0.0697565, 0.997564, 4.23333, 1, -7.62888e-08, -1.02048e-08, -0.0687466, 0.997634, 4.26667, 1, -8.19006e-08, -9.20183e-09, -0.0675234, 0.997718, 4.3, 1, -8.65814e-08, -5.93595e-09, -0.065856, 0.997829, 4.33333, 1, -8.42649e-08, -8.05158e-09, -0.0637703, 0.997965, 4.36667, 1, -7.67029e-08, -6.85982e-09, -0.0612924, 0.99812, 4.4, 1, -9.11856e-08, -5.1637e-09, -0.0584483, 0.99829, 4.43333, 1, -7.99082e-08, -2.73302e-09, -0.0552639, 0.998472, 4.46667, 1, -9.76694e-08, -2.48522e-09, -0.0517651, 0.998659, 4.5, 1, -1.01566e-07, -5.75611e-10, -0.0479781, 0.998848, 4.53333, 1, -8.09601e-08, -5.04824e-09, -0.0439289, 0.999035, 4.56667, 1, -8.36235e-08, -6.23225e-09, -0.0396435, 0.999214, 4.6, 1, -9.34686e-08, -3.89919e-09, -0.035148, 0.999382, 4.63333, 1, -8.23052e-08, -2.4793e-09, -0.0304687, 0.999536, 4.66667, 1, -9.46082e-08, 5.8712e-10, -0.0256317, 0.999671, 4.7, 1, -7.82615e-08, 1.73477e-10, -0.0206632, 0.999787, 4.73333, 1, -8.20574e-08, -2.09182e-09, -0.0155895, 0.999879, 4.76667, 1, -9.51051e-08, 7.74111e-10, -0.0104369, 0.999946, 4.8, 1, -1.05853e-07, -1.50612e-09, -0.00523158, 0.999986, 4.83333, 1, -8.184e-08, 2.79398e-09, 7.45058e-09, 1, 4.9, 1, -8.82363e-08, 9.93671e-09, 0.0104369, 0.999946, 4.93333, 1, -9.75425e-08, 5.35187e-09, 0.0155895, 0.999878, 4.96667, 1, -8.56554e-08, 4.94992e-09, 0.0206632, 0.999787, 5, 1, -1.03109e-07, 8.26338e-09, 0.0256317, 0.999672, 5.03333, 1, -9.61068e-08, 4.10991e-09, 0.0304687, 0.999536, 5.06667, 1, -8.56057e-08, 7.39384e-09, 0.0351481, 0.999382, 5.1, 1, -7.25363e-08, 4.06931e-10, 0.0396435, 0.999214, 5.13333, 1, -1.00479e-07, 1.20399e-08, 0.0439289, 0.999035, 5.16667, 1, -1.06053e-07, 5.00452e-09, 0.0479781, 0.998848, 5.2, 1, -8.36517e-08, 9.12983e-09, 0.0517652, 0.998659, 5.23333, 1, -7.29017e-08, 1.1361e-08, 0.0552639, 0.998472, 5.26667, 1, -9.15354e-08, 4.23081e-09, 0.0584483, 0.99829, 5.3, 1, -1.13443e-07, 8.25947e-09, 0.0612924, 0.99812, 5.33333, 1, -7.1433e-08, 8.51822e-09, 0.0637703, 0.997965)
tracks/3/type = "rotation_3d"
tracks/3/imported = true
tracks/3/enabled = true
tracks/3/path = NodePath("RIG-Armature/Skeleton3D:DEF-bun.002")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = PackedFloat32Array(0, 1, 7.80809e-08, -3.13972e-09, 0.0479781, 0.998848, 0.0333333, 1, 6.96631e-08, 3.9262e-09, 0.0517652, 0.998659, 0.0666667, 1, 9.66867e-08, -1.67984e-10, 0.0552639, 0.998472, 0.1, 1, 9.48006e-08, -4.2308e-09, 0.0584483, 0.99829, 0.133333, 1, 8.82497e-08, -1.01256e-08, 0.0612925, 0.99812, 0.166667, 1, 7.70324e-08, -6.65176e-09, 0.0637703, 0.997965, 0.2, 1, 8.26147e-08, -1.62028e-08, 0.0658559, 0.997829, 0.233333, 1, 5.92643e-08, 4.1582e-09, 0.0675234, 0.997718, 0.266667, 1, 8.39904e-08, -1.0905e-08, 0.0687466, 0.997634, 0.333333, 1, 7.88469e-08, -2.60818e-09, 0.0697565, 0.997564, 0.4, 1, 8.63243e-08, -9.97142e-09, 0.0687466, 0.997634, 0.433333, 1, 9.5669e-08, -1.14771e-08, 0.0675234, 0.997718, 0.466667, 1, 9.78983e-08, -9.37769e-09, 0.0658559, 0.997829, 0.5, 1, 8.50814e-08, -1.08513e-08, 0.0637703, 0.997965, 0.533333, 1, 8.44299e-08, -9.65907e-09, 0.0612924, 0.99812, 0.566667, 1, 9.50567e-08, -7.96247e-09, 0.0584483, 0.99829, 0.6, 1, 7.58165e-08, -6.23084e-09, 0.0552639, 0.998472, 0.633333, 1, 1.01079e-07, -4.81667e-09, 0.0517651, 0.998659, 0.666667, 1, 8.79585e-08, -8.08725e-10, 0.0479781, 0.998848, 0.7, 1, 1.03756e-07, -3.94124e-09, 0.0439289, 0.999035, 0.733333, 1, 8.41578e-08, -2.73706e-09, 0.0396435, 0.999214, 0.766667, 1, 8.7717e-08, -2.9673e-09, 0.035148, 0.999382, 0.8, 1, 9.5206e-08, -3.42563e-09, 0.0304687, 0.999536, 0.833333, 1, 1.09631e-07, -5.46848e-09, 0.0256317, 0.999671, 0.866667, 1, 8.99055e-08, -4.01839e-09, 0.0206632, 0.999787, 0.9, 1, 8.17081e-08, 2.09963e-09, 0.0155895, 0.999879, 0.933333, 1, 8.00867e-08, 2.63684e-09, 0.0104369, 0.999946, 0.966667, 1, 6.70533e-08, 3.17964e-09, 0.00523159, 0.999986, 1, 1, 1.04075e-07, 1.86265e-09, 1.86265e-09, 1, 1.06667, 1, 8.80034e-08, 2.9514e-09, -0.0104369, 0.999946, 1.1, 1, 8.56667e-08, 7.21473e-09, -0.0155895, 0.999878, 1.13333, 1, 8.19293e-08, -1.57075e-09, -0.0206632, 0.999787, 1.16667, 1, 8.19148e-08, 1.01266e-08, -0.0256317, 0.999672, 1.2, 1, 1.14509e-07, 8.76867e-09, -0.0304687, 0.999536, 1.23333, 1, 9.95842e-08, 5.53002e-09, -0.035148, 0.999382, 1.26667, 1, 7.16042e-08, 7.86336e-09, -0.0396435, 0.999214, 1.3, 1, 9.86141e-08, 1.39044e-08, -0.0439289, 0.999035, 1.33333, 1, 9.20669e-08, 1.05989e-08, -0.0479781, 0.998848, 1.36667, 1, 9.20448e-08, 1.66923e-09, -0.0517652, 0.998659, 1.4, 1, 8.26955e-08, -1.69751e-09, -0.0552639, 0.998472, 1.43333, 1, 1.02264e-07, 4.2308e-09, -0.0584483, 0.99829, 1.46667, 1, 9.1982e-08, 6.3933e-09, -0.0612924, 0.99812, 1.5, 1, 8.07653e-08, 8.51821e-09, -0.0637703, 0.997965, 1.53333, 1, 8.44814e-08, 1.06027e-08, -0.0658559, 0.997829, 1.56667, 1, 9.10017e-08, 5.17633e-09, -0.0675234, 0.997718, 1.6, 1, 8.39904e-08, 9.03789e-09, -0.0687466, 0.997634, 1.66667, 1, 8.63157e-08, 5.40898e-09, -0.0697565, 0.997564, 1.73333, 1, 9.21588e-08, 1.93067e-08, -0.0687466, 0.997634, 1.76667, 1, 8.14338e-08, 6.57651e-09, -0.0675234, 0.997718, 1.8, 1, 9.73149e-08, 5.93596e-09, -0.0658559, 0.997829, 1.83333, 1, 9.32471e-08, 1.30677e-08, -0.0637703, 0.997965, 1.86667, 1, 8.47798e-08, 5.46022e-09, -0.0612924, 0.99812, 1.9, 1, 9.52953e-08, 7.02955e-09, -0.0584483, 0.99829, 1.93333, 1, 7.56125e-08, 5.06491e-09, -0.0552639, 0.998472, 1.96667, 1, 9.40266e-08, 3.65095e-09, -0.0517651, 0.998659, 2, 1, 9.57673e-08, 2.20732e-09, -0.0479781, 0.998848, 2.03333, 1, 1.0396e-07, 1.84374e-09, -0.0439289, 0.999035, 2.06667, 1, 8.48714e-08, 4.60116e-09, -0.0396435, 0.999214, 2.1, 1, 8.09753e-08, 1.10351e-09, -0.035148, 0.999382, 2.13333, 1, 1.11006e-07, 2.39197e-09, -0.0304687, 0.999536, 2.16667, 1, 1.09864e-07, 3.60522e-09, -0.0256317, 0.999671, 2.2, 1, 1.0545e-07, 4.01838e-09, -0.0206632, 0.999787, 2.23333, 1, 8.96254e-08, -2.56535e-09, -0.0155895, 0.999879, 2.26667, 1, 7.26358e-08, -4.49959e-09, -0.0104369, 0.999946, 2.3, 1, 7.52025e-08, 5.45695e-10, -0.00523157, 0.999986, 2.33333, 1, 8.2422e-08, -5.58794e-09, 7.45058e-09, 1, 2.4, 1, 6.51848e-08, -1.08865e-09, 0.0104369, 0.999946, 2.43333, 1, 7.44895e-08, -1.62611e-09, 0.0155895, 0.999878, 2.46667, 1, 8.845e-08, -4.01839e-09, 0.0206632, 0.999787, 2.5, 1, 6.93378e-08, -2.6736e-09, 0.0256317, 0.999672, 2.53333, 1, 9.12151e-08, -6.90516e-09, 0.0304687, 0.999536, 2.56667, 1, 8.37419e-08, 6.13667e-11, 0.0351481, 0.999382, 2.6, 1, 6.97401e-08, -1.15916e-08, 0.0396435, 0.999214, 2.63333, 1, 9.30208e-08, -1.01755e-08, 0.0439289, 0.999035, 2.66667, 1, 7.80809e-08, -3.13972e-09, 0.0479781, 0.998848, 2.7, 1, 6.96631e-08, 3.9262e-09, 0.0517652, 0.998659, 2.73333, 1, 9.66867e-08, -1.67984e-10, 0.0552639, 0.998472, 2.76667, 1, 9.48006e-08, -4.2308e-09, 0.0584483, 0.99829, 2.8, 1, 8.82497e-08, -1.01256e-08, 0.0612925, 0.99812, 2.83333, 1, 7.70324e-08, -6.65176e-09, 0.0637703, 0.997965, 2.86667, 1, 8.26147e-08, -1.62028e-08, 0.0658559, 0.997829, 2.9, 1, 5.92643e-08, 4.1582e-09, 0.0675234, 0.997718, 2.93333, 1, 8.39904e-08, -1.0905e-08, 0.0687466, 0.997634, 3, 1, 7.88469e-08, -2.60818e-09, 0.0697565, 0.997564, 3.06667, 1, 8.63243e-08, -9.97142e-09, 0.0687466, 0.997634, 3.1, 1, 9.5669e-08, -1.14771e-08, 0.0675234, 0.997718, 3.13333, 1, 9.78983e-08, -9.37769e-09, 0.0658559, 0.997829, 3.16667, 1, 8.50814e-08, -1.08513e-08, 0.0637703, 0.997965, 3.2, 1, 8.44299e-08, -9.65907e-09, 0.0612924, 0.99812, 3.23333, 1, 9.50567e-08, -7.96247e-09, 0.0584483, 0.99829, 3.26667, 1, 7.58165e-08, -6.23084e-09, 0.0552639, 0.998472, 3.3, 1, 1.01079e-07, -4.81667e-09, 0.0517651, 0.998659, 3.33333, 1, 8.79585e-08, -8.08725e-10, 0.0479781, 0.998848, 3.36667, 1, 1.03756e-07, -3.94124e-09, 0.0439289, 0.999035, 3.4, 1, 8.41578e-08, -2.73706e-09, 0.0396435, 0.999214, 3.43333, 1, 8.7717e-08, -2.9673e-09, 0.035148, 0.999382, 3.46667, 1, 9.5206e-08, -3.42563e-09, 0.0304687, 0.999536, 3.5, 1, 1.09631e-07, -5.46848e-09, 0.0256317, 0.999671, 3.53333, 1, 8.99055e-08, -4.01839e-09, 0.0206632, 0.999787, 3.56667, 1, 8.17081e-08, 2.09963e-09, 0.0155895, 0.999879, 3.6, 1, 8.00867e-08, 2.63684e-09, 0.0104369, 0.999946, 3.63333, 1, 6.70533e-08, 3.17964e-09, 0.00523159, 0.999986, 3.66667, 1, 1.04075e-07, 1.86265e-09, 1.86265e-09, 1, 3.73333, 1, 8.80034e-08, 2.9514e-09, -0.0104369, 0.999946, 3.76667, 1, 8.56667e-08, 7.21473e-09, -0.0155895, 0.999878, 3.8, 1, 8.19293e-08, -1.57075e-09, -0.0206632, 0.999787, 3.83333, 1, 8.19148e-08, 1.01266e-08, -0.0256317, 0.999672, 3.86667, 1, 1.14509e-07, 8.76867e-09, -0.0304687, 0.999536, 3.9, 1, 9.95842e-08, 5.53002e-09, -0.035148, 0.999382, 3.93333, 1, 7.16042e-08, 7.86336e-09, -0.0396435, 0.999214, 3.96667, 1, 9.86141e-08, 1.39044e-08, -0.0439289, 0.999035, 4, 1, 9.20669e-08, 1.05989e-08, -0.0479781, 0.998848, 4.03333, 1, 9.20448e-08, 1.66923e-09, -0.0517652, 0.998659, 4.06667, 1, 8.26955e-08, -1.69751e-09, -0.0552639, 0.998472, 4.1, 1, 1.02264e-07, 4.2308e-09, -0.0584483, 0.99829, 4.13333, 1, 9.1982e-08, 6.3933e-09, -0.0612924, 0.99812, 4.16667, 1, 8.07653e-08, 8.51821e-09, -0.0637703, 0.997965, 4.2, 1, 8.44814e-08, 1.06027e-08, -0.0658559, 0.997829, 4.23333, 1, 9.10017e-08, 5.17633e-09, -0.0675234, 0.997718, 4.26667, 1, 8.39904e-08, 9.03789e-09, -0.0687466, 0.997634, 4.33333, 1, 8.63157e-08, 5.40898e-09, -0.0697565, 0.997564, 4.4, 1, 9.21588e-08, 1.93067e-08, -0.0687466, 0.997634, 4.43333, 1, 8.14338e-08, 6.57651e-09, -0.0675234, 0.997718, 4.46667, 1, 9.73149e-08, 5.93596e-09, -0.0658559, 0.997829, 4.5, 1, 9.32471e-08, 1.30677e-08, -0.0637703, 0.997965, 4.53333, 1, 8.47798e-08, 5.46022e-09, -0.0612924, 0.99812, 4.56667, 1, 9.52953e-08, 7.02955e-09, -0.0584483, 0.99829, 4.6, 1, 7.56125e-08, 5.06491e-09, -0.0552639, 0.998472, 4.63333, 1, 9.40266e-08, 3.65095e-09, -0.0517651, 0.998659, 4.66667, 1, 9.57673e-08, 2.20732e-09, -0.0479781, 0.998848, 4.7, 1, 1.0396e-07, 1.84374e-09, -0.0439289, 0.999035, 4.73333, 1, 8.48714e-08, 4.60116e-09, -0.0396435, 0.999214, 4.76667, 1, 8.09753e-08, 1.10351e-09, -0.035148, 0.999382, 4.8, 1, 1.11006e-07, 2.39197e-09, -0.0304687, 0.999536, 4.83333, 1, 1.09864e-07, 3.60522e-09, -0.0256317, 0.999671, 4.86667, 1, 1.0545e-07, 4.01838e-09, -0.0206632, 0.999787, 4.9, 1, 8.96254e-08, -2.56535e-09, -0.0155895, 0.999879, 4.93333, 1, 7.26358e-08, -4.49959e-09, -0.0104369, 0.999946, 4.96667, 1, 7.52025e-08, 5.45695e-10, -0.00523157, 0.999986, 5, 1, 8.2422e-08, -5.58794e-09, 7.45058e-09, 1, 5.06667, 1, 6.51848e-08, -1.08865e-09, 0.0104369, 0.999946, 5.1, 1, 7.44895e-08, -1.62611e-09, 0.0155895, 0.999878, 5.13333, 1, 8.845e-08, -4.01839e-09, 0.0206632, 0.999787, 5.16667, 1, 6.93378e-08, -2.6736e-09, 0.0256317, 0.999672, 5.2, 1, 9.12151e-08, -6.90516e-09, 0.0304687, 0.999536, 5.23333, 1, 8.37419e-08, 6.13667e-11, 0.0351481, 0.999382, 5.26667, 1, 6.97401e-08, -1.15916e-08, 0.0396435, 0.999214, 5.3, 1, 9.30208e-08, -1.01755e-08, 0.0439289, 0.999035, 5.33333, 1, 7.80809e-08, -3.13972e-09, 0.0479781, 0.998848)
tracks/4/type = "position_3d"
tracks/4/imported = true
tracks/4/enabled = true
tracks/4/path = NodePath("RIG-Armature/Skeleton3D:DEF-head")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = PackedFloat32Array(0, 1, 0.04359, 0.623367, 5.23595e-09, 0.0666667, 1, 0.045205, 0.623244, 2.35113e-05, 0.233333, 1, 0.0440832, 0.623244, 0.000266599, 0.266667, 1, 0.0430597, 0.623292, 0.000339155, 0.3, 1, 0.0417947, 0.623353, 0.000416441, 0.333333, 1, 0.0402989, 0.623425, 0.000496752, 0.366667, 1, 0.0385831, 0.623506, 0.000578265, 0.4, 1, 0.036658, 0.623596, 0.000659075, 0.433333, 1, 0.0345344, 0.623691, 0.000737217, 0.466667, 1, 0.0322231, 0.623791, 0.00081071, 0.5, 1, 0.0297348, 0.623892, 0.000877583, 0.533333, 1, 0.0270804, 0.623994, 0.000935925, 0.566667, 1, 0.0242706, 0.624094, 0.000983919, 0.6, 1, 0.0213162, 0.62419, 0.00101989, 0.633333, 1, 0.018228, 0.62428, 0.00104236, 0.666667, 1, 0.0150168, 0.624363, 0.00105007, 0.7, 1, 0.0116951, 0.624437, 0.00104236, 0.733333, 1, 0.00828305, 0.624499, 0.00101989, 0.766667, 1, 0.00480247, 0.624547, 0.000983919, 0.9, 1, -0.00936686, 0.624577, 0.000737216, 0.933333, 1, -0.0128613, 0.62454, 0.000659074, 0.966667, 1, -0.0162928, 0.624487, 0.000578264, 1, 1, -0.0196392, 0.624417, 0.000496751, 1.03333, 1, -0.0228787, 0.624333, 0.00041644, 1.06667, 1, -0.0259891, 0.624237, 0.000339154, 1.1, 1, -0.0289486, 0.62413, 0.000266597, 1.13333, 1, -0.0317351, 0.624017, 0.000200338, 1.16667, 1, -0.0343266, 0.623899, 0.000141779, 1.2, 1, -0.0367011, 0.62378, 9.21419e-05, 1.23333, 1, -0.0388367, 0.623664, 5.24488e-05, 1.26667, 1, -0.0407113, 0.623554, 2.35093e-05, 1.3, 1, -0.0423031, 0.623453, 5.90923e-06, 1.33333, 1, -0.04359, 0.623367, 3.15674e-09, 1.4, 1, -0.045205, 0.623244, 2.35105e-05, 1.56667, 1, -0.0440832, 0.623244, 0.000266601, 1.6, 1, -0.0430597, 0.623292, 0.000339158, 1.63333, 1, -0.0417947, 0.623353, 0.000416445, 1.66667, 1, -0.0402989, 0.623425, 0.000496756, 1.7, 1, -0.0385831, 0.623506, 0.00057827, 1.73333, 1, -0.036658, 0.623596, 0.00065908, 1.76667, 1, -0.0345344, 0.623691, 0.000737224, 1.8, 1, -0.0322231, 0.623791, 0.000810716, 1.83333, 1, -0.0297348, 0.623892, 0.00087759, 1.86667, 1, -0.0270804, 0.623994, 0.000935933, 1.9, 1, -0.0242705, 0.624094, 0.000983927, 1.93333, 1, -0.0213162, 0.62419, 0.0010199, 1.96667, 1, -0.018228, 0.62428, 0.00104237, 2, 1, -0.0150168, 0.624363, 0.00105008, 2.03333, 1, -0.0116951, 0.624437, 0.00104237, 2.06667, 1, -0.00828305, 0.624499, 0.0010199, 2.1, 1, -0.00480247, 0.624547, 0.000983928, 2.23333, 1, 0.00936687, 0.624577, 0.000737225, 2.26667, 1, 0.0128613, 0.62454, 0.000659082, 2.3, 1, 0.0162928, 0.624487, 0.000578272, 2.33333, 1, 0.0196392, 0.624417, 0.000496758, 2.36667, 1, 0.0228787, 0.624333, 0.000416447, 2.4, 1, 0.0259891, 0.624237, 0.00033916, 2.43333, 1, 0.0289486, 0.62413, 0.000266603, 2.46667, 1, 0.0317351, 0.624017, 0.000200344, 2.5, 1, 0.0343266, 0.623899, 0.000141784, 2.53333, 1, 0.0367011, 0.62378, 9.21464e-05, 2.56667, 1, 0.0388367, 0.623664, 5.24528e-05, 2.6, 1, 0.0407113, 0.623554, 2.35127e-05, 2.63333, 1, 0.0423031, 0.623453, 5.91195e-06, 2.66667, 1, 0.04359, 0.623367, 5.23595e-09, 2.73333, 1, 0.045205, 0.623244, 2.35113e-05, 2.9, 1, 0.0440832, 0.623244, 0.000266599, 2.93333, 1, 0.0430597, 0.623292, 0.000339155, 2.96667, 1, 0.0417947, 0.623353, 0.000416441, 3, 1, 0.0402989, 0.623425, 0.000496752, 3.03333, 1, 0.0385831, 0.623506, 0.000578265, 3.06667, 1, 0.036658, 0.623596, 0.000659075, 3.1, 1, 0.0345344, 0.623691, 0.000737217, 3.13333, 1, 0.0322231, 0.623791, 0.00081071, 3.16667, 1, 0.0297348, 0.623892, 0.000877583, 3.2, 1, 0.0270804, 0.623994, 0.000935925, 3.23333, 1, 0.0242706, 0.624094, 0.000983919, 3.26667, 1, 0.0213162, 0.62419, 0.00101989, 3.3, 1, 0.018228, 0.62428, 0.00104236, 3.33333, 1, 0.0150168, 0.624363, 0.00105007, 3.36667, 1, 0.0116951, 0.624437, 0.00104236, 3.4, 1, 0.00828305, 0.624499, 0.00101989, 3.43333, 1, 0.00480247, 0.624547, 0.000983919, 3.56667, 1, -0.00936686, 0.624577, 0.000737216, 3.6, 1, -0.0128613, 0.62454, 0.000659074, 3.63333, 1, -0.0162928, 0.624487, 0.000578264, 3.66667, 1, -0.0196392, 0.624417, 0.000496751, 3.7, 1, -0.0228787, 0.624333, 0.00041644, 3.73333, 1, -0.0259891, 0.624237, 0.000339154, 3.76667, 1, -0.0289486, 0.62413, 0.000266597, 3.8, 1, -0.0317351, 0.624017, 0.000200338, 3.83333, 1, -0.0343266, 0.623899, 0.000141779, 3.86667, 1, -0.0367011, 0.62378, 9.21419e-05, 3.9, 1, -0.0388367, 0.623664, 5.24488e-05, 3.93333, 1, -0.0407113, 0.623554, 2.35093e-05, 3.96667, 1, -0.0423031, 0.623453, 5.90923e-06, 4, 1, -0.04359, 0.623367, 3.15674e-09, 4.06667, 1, -0.045205, 0.623244, 2.35105e-05, 4.23333, 1, -0.0440832, 0.623244, 0.000266601, 4.26667, 1, -0.0430597, 0.623292, 0.000339158, 4.3, 1, -0.0417947, 0.623353, 0.000416445, 4.33333, 1, -0.0402989, 0.623425, 0.000496756, 4.36667, 1, -0.0385831, 0.623506, 0.00057827, 4.4, 1, -0.036658, 0.623596, 0.00065908, 4.43333, 1, -0.0345344, 0.623691, 0.000737224, 4.46667, 1, -0.0322231, 0.623791, 0.000810716, 4.5, 1, -0.0297348, 0.623892, 0.00087759, 4.53333, 1, -0.0270804, 0.623994, 0.000935933, 4.56667, 1, -0.0242705, 0.624094, 0.000983927, 4.6, 1, -0.0213162, 0.62419, 0.0010199, 4.63333, 1, -0.018228, 0.62428, 0.00104237, 4.66667, 1, -0.0150168, 0.624363, 0.00105008, 4.7, 1, -0.0116951, 0.624437, 0.00104237, 4.73333, 1, -0.00828305, 0.624499, 0.0010199, 4.76667, 1, -0.00480247, 0.624547, 0.000983928, 4.9, 1, 0.00936687, 0.624577, 0.000737225, 4.93333, 1, 0.0128613, 0.62454, 0.000659082, 4.96667, 1, 0.0162928, 0.624487, 0.000578272, 5, 1, 0.0196392, 0.624417, 0.000496758, 5.03333, 1, 0.0228787, 0.624333, 0.000416447, 5.06667, 1, 0.0259891, 0.624237, 0.00033916, 5.1, 1, 0.0289486, 0.62413, 0.000266603, 5.13333, 1, 0.0317351, 0.624017, 0.000200344, 5.16667, 1, 0.0343266, 0.623899, 0.000141784, 5.2, 1, 0.0367011, 0.62378, 9.21464e-05, 5.23333, 1, 0.0388367, 0.623664, 5.24528e-05, 5.26667, 1, 0.0407113, 0.623554, 2.35127e-05, 5.3, 1, 0.0423031, 0.623453, 5.91195e-06, 5.33333, 1, 0.04359, 0.623367, 5.23595e-09)
tracks/5/type = "rotation_3d"
tracks/5/imported = true
tracks/5/enabled = true
tracks/5/path = NodePath("RIG-Armature/Skeleton3D:DEF-head")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = PackedFloat32Array(0, 1, 0.00410665, 0.0696356, -0.0108815, 0.997505, 0.0333333, 1, 0.00398219, 0.0667715, -0.0153861, 0.997642, 0.0666667, 1, 0.0038292, 0.0634245, -0.019747, 0.997784, 0.1, 1, 0.00365347, 0.0596335, -0.0239515, 0.997926, 0.133333, 1, 0.00346142, 0.0554375, -0.0279864, 0.998064, 0.166667, 1, 0.00325905, 0.0508753, -0.031839, 0.998192, 0.2, 1, 0.00305283, 0.0459861, -0.0354961, 0.998307, 0.233333, 1, 0.00284861, 0.0408089, -0.0389447, 0.998404, 0.266667, 1, 0.00265192, 0.0353828, -0.0421719, 0.99848, 0.433333, 1, 0.00193913, 0.00589336, -0.0545254, 0.998493, 0.466667, 1, 0.00187245, -0.000200951, -0.0561476, 0.998421, 0.5, 1, 0.00183467, -0.00626893, -0.0574563, 0.998327, 0.533333, 1, 0.00182592, -0.0122712, -0.0584382, 0.998214, 0.566667, 1, 0.00184519, -0.0181686, -0.0590803, 0.998086, 0.6, 1, 0.00189127, -0.0239217, -0.0593692, 0.997948, 0.633333, 1, 0.00196194, -0.0294915, -0.0592918, 0.997803, 0.666667, 1, 0.00205462, -0.0348389, -0.058835, 0.997658, 0.7, 1, 0.00216628, -0.039929, -0.0579898, 0.997516, 0.733333, 1, 0.00229562, -0.0447446, -0.056765, 0.997382, 0.766667, 1, 0.00244098, -0.0492726, -0.0551733, 0.997257, 0.8, 1, 0.00259977, -0.0535, -0.053228, 0.997145, 0.833333, 1, 0.0027698, -0.0574138, -0.0509421, 0.997046, 0.866667, 1, 0.0029478, -0.0610008, -0.0483286, 0.996963, 1, 1, 0.00365867, -0.071821, -0.0348644, 0.996801, 1.03333, 1, 0.00381352, -0.0735792, -0.0308164, 0.996806, 1.06667, 1, 0.00395101, -0.0749327, -0.0265467, 0.996827, 1.1, 1, 0.00406807, -0.0758685, -0.0220946, 0.996865, 1.13333, 1, 0.00416177, -0.0763737, -0.0174993, 0.996917, 1.16667, 1, 0.00422957, -0.0764354, -0.0128002, 0.996983, 1.2, 1, 0.00426912, -0.0760404, -0.00803657, 0.997063, 1.23333, 1, 0.00427791, -0.075176, -0.00324748, 0.997156, 1.26667, 1, 0.00425449, -0.073829, 0.00152793, 0.997261, 1.3, 1, 0.00419774, -0.0719865, 0.0062506, 0.997377, 1.33333, 1, 0.00410663, -0.0696354, 0.0108815, 0.997505, 1.36667, 1, 0.00398226, -0.0667712, 0.0153861, 0.997642, 1.4, 1, 0.00382919, -0.0634242, 0.019747, 0.997784, 1.43333, 1, 0.00365354, -0.0596333, 0.0239514, 0.997926, 1.46667, 1, 0.00346141, -0.0554372, 0.0279864, 0.998064, 1.5, 1, 0.00325913, -0.0508751, 0.0318389, 0.998192, 1.53333, 1, 0.00305282, -0.0459859, 0.0354961, 0.998307, 1.56667, 1, 0.0028486, -0.0408086, 0.0389447, 0.998404, 1.6, 1, 0.00265191, -0.0353826, 0.0421719, 0.99848, 1.76667, 1, 0.00193921, -0.00589311, 0.0545254, 0.998493, 1.8, 1, 0.00187245, 0.000201177, 0.0561476, 0.998421, 1.83333, 1, 0.00183467, 0.00626916, 0.0574563, 0.998327, 1.86667, 1, 0.00182592, 0.0122714, 0.0584382, 0.998214, 1.9, 1, 0.00184519, 0.0181688, 0.0590802, 0.998086, 1.93333, 1, 0.00189127, 0.0239219, 0.0593692, 0.997948, 1.96667, 1, 0.00196195, 0.0294918, 0.0592918, 0.997803, 2, 1, 0.00205463, 0.0348391, 0.058835, 0.997658, 2.03333, 1, 0.00216638, 0.0399292, 0.0579898, 0.997516, 2.06667, 1, 0.00229563, 0.0447448, 0.056765, 0.997382, 2.1, 1, 0.00244091, 0.0492729, 0.0551733, 0.997257, 2.13333, 1, 0.00259986, 0.0535003, 0.053228, 0.997145, 2.16667, 1, 0.00276981, 0.057414, 0.050942, 0.997046, 2.23333, 1, 0.00312986, 0.0642483, 0.0454008, 0.996896, 2.33333, 1, 0.00365877, 0.0718213, 0.0348644, 0.996801, 2.36667, 1, 0.00381354, 0.0735794, 0.0308164, 0.996806, 2.4, 1, 0.00395103, 0.0749329, 0.0265467, 0.996827, 2.43333, 1, 0.00406808, 0.0758687, 0.0220946, 0.996865, 2.46667, 1, 0.00416188, 0.076374, 0.0174993, 0.996917, 2.5, 1, 0.00422959, 0.0764356, 0.0128002, 0.996983, 2.53333, 1, 0.00426905, 0.0760406, 0.00803658, 0.997063, 2.56667, 1, 0.00427793, 0.0751762, 0.00324747, 0.997156, 2.6, 1, 0.00425451, 0.0738292, -0.00152793, 0.997261, 2.63333, 1, 0.00419767, 0.0719867, -0.00625061, 0.997377, 2.66667, 1, 0.00410665, 0.0696356, -0.0108815, 0.997505, 2.7, 1, 0.00398219, 0.0667715, -0.0153861, 0.997642, 2.73333, 1, 0.0038292, 0.0634245, -0.019747, 0.997784, 2.76667, 1, 0.00365347, 0.0596335, -0.0239515, 0.997926, 2.8, 1, 0.00346142, 0.0554375, -0.0279864, 0.998064, 2.83333, 1, 0.00325905, 0.0508753, -0.031839, 0.998192, 2.86667, 1, 0.00305283, 0.0459861, -0.0354961, 0.998307, 2.9, 1, 0.00284861, 0.0408089, -0.0389447, 0.998404, 2.93333, 1, 0.00265192, 0.0353828, -0.0421719, 0.99848, 3.1, 1, 0.00193913, 0.00589336, -0.0545254, 0.998493, 3.13333, 1, 0.00187245, -0.000200951, -0.0561476, 0.998421, 3.16667, 1, 0.00183467, -0.00626893, -0.0574563, 0.998327, 3.2, 1, 0.00182592, -0.0122712, -0.0584382, 0.998214, 3.23333, 1, 0.00184519, -0.0181686, -0.0590803, 0.998086, 3.26667, 1, 0.00189127, -0.0239217, -0.0593692, 0.997948, 3.3, 1, 0.00196194, -0.0294915, -0.0592918, 0.997803, 3.33333, 1, 0.00205462, -0.0348389, -0.058835, 0.997658, 3.36667, 1, 0.00216628, -0.039929, -0.0579898, 0.997516, 3.4, 1, 0.00229562, -0.0447446, -0.056765, 0.997382, 3.43333, 1, 0.00244098, -0.0492726, -0.0551733, 0.997257, 3.46667, 1, 0.00259977, -0.0535, -0.053228, 0.997145, 3.5, 1, 0.0027698, -0.0574138, -0.0509421, 0.997046, 3.53333, 1, 0.0029478, -0.0610008, -0.0483286, 0.996963, 3.66667, 1, 0.00365867, -0.071821, -0.0348644, 0.996801, 3.7, 1, 0.00381352, -0.0735792, -0.0308164, 0.996806, 3.73333, 1, 0.00395101, -0.0749327, -0.0265467, 0.996827, 3.76667, 1, 0.00406807, -0.0758685, -0.0220946, 0.996865, 3.8, 1, 0.00416177, -0.0763737, -0.0174993, 0.996917, 3.83333, 1, 0.00422957, -0.0764354, -0.0128002, 0.996983, 3.86667, 1, 0.00426912, -0.0760404, -0.00803657, 0.997063, 3.9, 1, 0.00427791, -0.075176, -0.00324748, 0.997156, 3.93333, 1, 0.00425449, -0.073829, 0.00152793, 0.997261, 3.96667, 1, 0.00419774, -0.0719865, 0.0062506, 0.997377, 4, 1, 0.00410663, -0.0696354, 0.0108815, 0.997505, 4.03333, 1, 0.00398226, -0.0667712, 0.0153861, 0.997642, 4.06667, 1, 0.00382919, -0.0634242, 0.019747, 0.997784, 4.1, 1, 0.00365354, -0.0596333, 0.0239514, 0.997926, 4.13333, 1, 0.00346141, -0.0554372, 0.0279864, 0.998064, 4.16667, 1, 0.00325913, -0.0508751, 0.0318389, 0.998192, 4.2, 1, 0.00305282, -0.0459859, 0.0354961, 0.998307, 4.23333, 1, 0.0028486, -0.0408086, 0.0389447, 0.998404, 4.26667, 1, 0.00265191, -0.0353826, 0.0421719, 0.99848, 4.43333, 1, 0.00193921, -0.00589311, 0.0545254, 0.998493, 4.46667, 1, 0.00187245, 0.000201177, 0.0561476, 0.998421, 4.5, 1, 0.00183467, 0.00626916, 0.0574563, 0.998327, 4.53333, 1, 0.00182592, 0.0122714, 0.0584382, 0.998214, 4.56667, 1, 0.00184519, 0.0181688, 0.0590802, 0.998086, 4.6, 1, 0.00189127, 0.0239219, 0.0593692, 0.997948, 4.63333, 1, 0.00196195, 0.0294918, 0.0592918, 0.997803, 4.66667, 1, 0.00205463, 0.0348391, 0.058835, 0.997658, 4.7, 1, 0.00216638, 0.0399292, 0.0579898, 0.997516, 4.73333, 1, 0.00229563, 0.0447448, 0.056765, 0.997382, 4.76667, 1, 0.00244091, 0.0492729, 0.0551733, 0.997257, 4.8, 1, 0.00259986, 0.0535003, 0.053228, 0.997145, 4.83333, 1, 0.00276981, 0.057414, 0.050942, 0.997046, 4.9, 1, 0.00312986, 0.0642483, 0.0454008, 0.996896, 5, 1, 0.00365877, 0.0718213, 0.0348644, 0.996801, 5.03333, 1, 0.00381354, 0.0735794, 0.0308164, 0.996806, 5.06667, 1, 0.00395103, 0.0749329, 0.0265467, 0.996827, 5.1, 1, 0.00406808, 0.0758687, 0.0220946, 0.996865, 5.13333, 1, 0.00416188, 0.076374, 0.0174993, 0.996917, 5.16667, 1, 0.00422959, 0.0764356, 0.0128002, 0.996983, 5.2, 1, 0.00426905, 0.0760406, 0.00803658, 0.997063, 5.23333, 1, 0.00427793, 0.0751762, 0.00324747, 0.997156, 5.26667, 1, 0.00425451, 0.0738292, -0.00152793, 0.997261, 5.3, 1, 0.00419767, 0.0719867, -0.00625061, 0.997377, 5.33333, 1, 0.00410665, 0.0696356, -0.0108815, 0.997505)
tracks/6/type = "position_3d"
tracks/6/imported = true
tracks/6/enabled = true
tracks/6/path = NodePath("RIG-Armature/Skeleton3D:DEF-skirt")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = PackedFloat32Array(0, 1, 0.0285366, 0.408093, 6.37583e-08, 0.133333, 1, 0.0269409, 0.408201, 6.29859e-08, 0.166667, 1, 0.0260877, 0.408256, 6.27364e-08, 0.2, 1, 0.025074, 0.40832, 6.24656e-08, 0.233333, 1, 0.0239105, 0.40839, 6.21741e-08, 0.266667, 1, 0.0226078, 0.408464, 6.18629e-08, 0.3, 1, 0.0211765, 0.408541, 6.15327e-08, 0.333333, 1, 0.0196273, 0.408618, 6.11846e-08, 0.366667, 1, 0.0179708, 0.408694, 6.082e-08, 0.4, 1, 0.0162177, 0.408767, 6.04402e-08, 0.433333, 1, 0.0143787, 0.408836, 6.00469e-08, 0.466667, 1, 0.0124644, 0.408899, 5.96422e-08, 0.5, 1, 0.0104856, 0.408955, 5.92282e-08, 0.533333, 1, 0.00845307, 0.409002, 5.88074e-08, 0.6, 1, 0.00426959, 0.409067, 5.79567e-08, 0.766667, 1, -0.00637754, 0.409039, 5.59262e-08, 0.8, 1, -0.00845313, 0.409002, 5.55591e-08, 0.833333, 1, -0.0104857, 0.408955, 5.52104e-08, 0.866667, 1, -0.0124645, 0.408899, 5.48827e-08, 0.9, 1, -0.0143787, 0.408836, 5.45783e-08, 0.933333, 1, -0.0162177, 0.408767, 5.42998e-08, 0.966667, 1, -0.0179709, 0.408694, 5.40494e-08, 1, 1, -0.0196274, 0.408618, 5.38299e-08, 1.03333, 1, -0.0211766, 0.408541, 5.36434e-08, 1.06667, 1, -0.0226079, 0.408464, 5.34924e-08, 1.1, 1, -0.0239106, 0.40839, 5.3379e-08, 1.13333, 1, -0.0250741, 0.40832, 5.33055e-08, 1.16667, 1, -0.0260878, 0.408256, 5.32739e-08, 1.23333, 1, -0.0276231, 0.408155, 5.33432e-08, 1.5, 1, -0.0260878, 0.408256, 5.54523e-08, 1.53333, 1, -0.0250741, 0.40832, 5.58795e-08, 1.56667, 1, -0.0239106, 0.40839, 5.63275e-08, 1.6, 1, -0.0226079, 0.408464, 5.67917e-08, 1.63333, 1, -0.0211766, 0.408541, 5.72672e-08, 1.66667, 1, -0.0196274, 0.408618, 5.77495e-08, 1.7, 1, -0.0179709, 0.408694, 5.82343e-08, 1.73333, 1, -0.0162178, 0.408767, 5.87173e-08, 1.76667, 1, -0.0143787, 0.408836, 5.91945e-08, 1.8, 1, -0.0124645, 0.408899, 5.96623e-08, 1.83333, 1, -0.0104857, 0.408955, 6.01171e-08, 1.86667, 1, -0.00845313, 0.409002, 6.05558e-08, 1.93333, 1, -0.00426964, 0.409067, 6.13731e-08, 2.1, 1, 0.00637748, 0.409039, 6.29699e-08, 2.13333, 1, 0.00845307, 0.409002, 6.32072e-08, 2.16667, 1, 0.0104856, 0.408955, 6.34178e-08, 2.2, 1, 0.0124644, 0.408899, 6.36025e-08, 2.23333, 1, 0.0143787, 0.408836, 6.37619e-08, 2.26667, 1, 0.0162177, 0.408767, 6.38966e-08, 2.3, 1, 0.0179708, 0.408694, 6.40072e-08, 2.33333, 1, 0.0196273, 0.408618, 6.40944e-08, 2.36667, 1, 0.0211765, 0.408541, 6.41589e-08, 2.4, 1, 0.0226078, 0.408464, 6.42011e-08, 2.43333, 1, 0.0239105, 0.40839, 6.42214e-08, 2.46667, 1, 0.025074, 0.40832, 6.42203e-08, 2.5, 1, 0.0260877, 0.408256, 6.41979e-08, 2.56667, 1, 0.027623, 0.408155, 6.40888e-08, 2.83333, 1, 0.0260877, 0.408256, 6.27364e-08, 2.86667, 1, 0.025074, 0.40832, 6.24656e-08, 2.9, 1, 0.0239105, 0.40839, 6.21741e-08, 2.93333, 1, 0.0226078, 0.408464, 6.18629e-08, 2.96667, 1, 0.0211765, 0.408541, 6.15327e-08, 3, 1, 0.0196273, 0.408618, 6.11846e-08, 3.03333, 1, 0.0179708, 0.408694, 6.082e-08, 3.06667, 1, 0.0162177, 0.408767, 6.04402e-08, 3.1, 1, 0.0143787, 0.408836, 6.00469e-08, 3.13333, 1, 0.0124644, 0.408899, 5.96422e-08, 3.16667, 1, 0.0104856, 0.408955, 5.92282e-08, 3.2, 1, 0.00845307, 0.409002, 5.88074e-08, 3.26667, 1, 0.00426959, 0.409067, 5.79567e-08, 3.43333, 1, -0.00637754, 0.409039, 5.59262e-08, 3.46667, 1, -0.00845313, 0.409002, 5.55591e-08, 3.5, 1, -0.0104857, 0.408955, 5.52104e-08, 3.53333, 1, -0.0124645, 0.408899, 5.48827e-08, 3.56667, 1, -0.0143787, 0.408836, 5.45783e-08, 3.6, 1, -0.0162177, 0.408767, 5.42998e-08, 3.63333, 1, -0.0179709, 0.408694, 5.40494e-08, 3.66667, 1, -0.0196274, 0.408618, 5.38299e-08, 3.7, 1, -0.0211766, 0.408541, 5.36434e-08, 3.73333, 1, -0.0226079, 0.408464, 5.34924e-08, 3.76667, 1, -0.0239106, 0.40839, 5.3379e-08, 3.8, 1, -0.0250741, 0.40832, 5.33055e-08, 3.83333, 1, -0.0260878, 0.408256, 5.32739e-08, 3.9, 1, -0.0276231, 0.408155, 5.33432e-08, 4.16667, 1, -0.0260878, 0.408256, 5.54523e-08, 4.2, 1, -0.0250741, 0.40832, 5.58795e-08, 4.23333, 1, -0.0239106, 0.40839, 5.63275e-08, 4.26667, 1, -0.0226079, 0.408464, 5.67917e-08, 4.3, 1, -0.0211766, 0.408541, 5.72672e-08, 4.33333, 1, -0.0196274, 0.408618, 5.77495e-08, 4.36667, 1, -0.0179709, 0.408694, 5.82343e-08, 4.4, 1, -0.0162178, 0.408767, 5.87173e-08, 4.43333, 1, -0.0143787, 0.408836, 5.91945e-08, 4.46667, 1, -0.0124645, 0.408899, 5.96623e-08, 4.5, 1, -0.0104857, 0.408955, 6.01171e-08, 4.53333, 1, -0.00845313, 0.409002, 6.05558e-08, 4.6, 1, -0.00426964, 0.409067, 6.13731e-08, 4.76667, 1, 0.00637748, 0.409039, 6.29699e-08, 4.8, 1, 0.00845307, 0.409002, 6.32072e-08, 4.83333, 1, 0.0104856, 0.408955, 6.34178e-08, 4.86667, 1, 0.0124644, 0.408899, 6.36025e-08, 4.9, 1, 0.0143787, 0.408836, 6.37619e-08, 4.93333, 1, 0.0162177, 0.408767, 6.38966e-08, 4.96667, 1, 0.0179708, 0.408694, 6.40072e-08, 5, 1, 0.0196273, 0.408618, 6.40944e-08, 5.03333, 1, 0.0211765, 0.408541, 6.41589e-08, 5.06667, 1, 0.0226078, 0.408464, 6.42011e-08, 5.1, 1, 0.0239105, 0.40839, 6.42214e-08, 5.13333, 1, 0.025074, 0.40832, 6.42203e-08, 5.16667, 1, 0.0260877, 0.408256, 6.41979e-08, 5.23333, 1, 0.027623, 0.408155, 6.40888e-08, 5.33333, 1, 0.0285366, 0.408093, 6.37583e-08)
tracks/7/type = "rotation_3d"
tracks/7/imported = true
tracks/7/enabled = true
tracks/7/path = NodePath("RIG-Armature/Skeleton3D:DEF-skirt")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = PackedFloat32Array(0, 1, -0.992074, 0.034644, 0.120713, 0.00421538, 0.0333333, 1, -0.99114, 0.0370779, 0.127449, 0.00478151, 0.0666667, 1, -0.990272, 0.0392466, 0.133387, 0.00534148, 0.1, 1, -0.989487, 0.0411513, 0.138516, 0.00588335, 0.133333, 1, -0.988802, 0.0427935, 0.142825, 0.00639712, 0.166667, 1, -0.988229, 0.0441748, 0.146304, 0.00687232, 0.2, 1, -0.987781, 0.0452969, 0.148941, 0.00729992, 0.233333, 1, -0.987467, 0.0461615, 0.150726, 0.00767153, 0.266667, 1, -0.987295, 0.0467702, 0.151646, 0.00797961, 0.333333, 1, -0.987393, 0.0472265, 0.150844, 0.00838042, 0.366667, 1, -0.987665, 0.0470768, 0.149096, 0.00846318, 0.4, 1, -0.988083, 0.0466765, 0.146432, 0.00846281, 0.433333, 1, -0.98864, 0.0460264, 0.142838, 0.00837758, 0.466667, 1, -0.989328, 0.0451267, 0.138299, 0.00820688, 0.5, 1, -0.990135, 0.0439774, 0.132801, 0.00795191, 0.533333, 1, -0.991043, 0.0425778, 0.126344, 0.00761613, 0.566667, 1, -0.992024, 0.0409262, 0.119, 0.00720744, 0.6, 1, -0.993047, 0.0390208, 0.110858, 0.00673564, 0.633333, 1, -0.994081, 0.0368594, 0.102005, 0.00621174, 0.666667, 1, -0.995098, 0.0344395, 0.0925314, 0.00564743, 0.7, 1, -0.99607, 0.0317635, 0.0825218, 0.00505618, 0.733333, 1, -0.996974, 0.0288504, 0.0720448, 0.00445501, 0.766667, 1, -0.997789, 0.0257243, 0.0611654, 0.00386159, 0.8, 1, -0.998495, 0.0224095, 0.0499488, 0.00329284, 0.833333, 1, -0.999077, 0.0189312, 0.0384613, 0.00276395, 0.866667, 1, -0.999522, 0.0153147, 0.0267694, 0.00228941, 1, 1, -0.999785, -3.75634e-06, -0.0206967, 0.00115122, 1.03333, 1, -0.999467, -0.00391046, -0.0323995, 0.0010922, 1.06667, 1, -0.999005, -0.0077925, -0.0439015, 0.00112897, 1.1, 1, -0.99841, -0.0116219, -0.0551353, 0.00126044, 1.13333, 1, -0.997698, -0.0153708, -0.0660342, 0.00148259, 1.16667, 1, -0.996884, -0.0190114, -0.0765315, 0.00178902, 1.2, 1, -0.99599, -0.0225163, -0.0865615, 0.00217119, 1.23333, 1, -0.995036, -0.0258584, -0.0960589, 0.00261842, 1.26667, 1, -0.994048, -0.029011, -0.104959, 0.00311807, 1.3, 1, -0.993052, -0.0319481, -0.113198, 0.00365556, 1.33333, 1, -0.992074, -0.034644, -0.120713, 0.00421538, 1.36667, 1, -0.99114, -0.0370779, -0.127449, 0.00478142, 1.4, 1, -0.990272, -0.0392466, -0.133387, 0.00534148, 1.43333, 1, -0.989487, -0.0411513, -0.138516, 0.00588334, 1.46667, 1, -0.988802, -0.0427935, -0.142825, 0.00639712, 1.5, 1, -0.988229, -0.0441748, -0.146304, 0.00687232, 1.53333, 1, -0.987781, -0.0452969, -0.148941, 0.0073, 1.56667, 1, -0.987467, -0.0461615, -0.150726, 0.00767153, 1.6, 1, -0.987295, -0.0467702, -0.151646, 0.00797961, 1.66667, 1, -0.987393, -0.0472265, -0.150844, 0.00838042, 1.7, 1, -0.987665, -0.0470768, -0.149096, 0.00846318, 1.73333, 1, -0.988083, -0.0466765, -0.146432, 0.00846281, 1.76667, 1, -0.98864, -0.0460264, -0.142838, 0.00837767, 1.8, 1, -0.989328, -0.0451267, -0.138299, 0.00820697, 1.83333, 1, -0.990135, -0.0439774, -0.1328, 0.00795199, 1.86667, 1, -0.991043, -0.0425778, -0.126344, 0.00761613, 1.9, 1, -0.992024, -0.0409262, -0.119, 0.00720744, 1.93333, 1, -0.993047, -0.0390208, -0.110858, 0.00673564, 1.96667, 1, -0.994081, -0.0368593, -0.102005, 0.00621174, 2, 1, -0.995098, -0.0344395, -0.0925314, 0.00564742, 2.03333, 1, -0.99607, -0.0317634, -0.0825218, 0.00505618, 2.06667, 1, -0.996974, -0.0288504, -0.0720448, 0.0044551, 2.1, 1, -0.997789, -0.0257243, -0.0611654, 0.00386159, 2.13333, 1, -0.998495, -0.0224095, -0.0499489, 0.00329284, 2.16667, 1, -0.999077, -0.0189312, -0.0384614, 0.00276394, 2.2, 1, -0.999522, -0.0153147, -0.0267694, 0.00228941, 2.33333, 1, -0.999785, 3.75107e-06, 0.0206967, 0.0011513, 2.36667, 1, -0.999467, 0.00391046, 0.0323995, 0.0010922, 2.4, 1, -0.999005, 0.0077925, 0.0439015, 0.00112897, 2.43333, 1, -0.99841, 0.0116219, 0.0551353, 0.00126044, 2.46667, 1, -0.997698, 0.0153708, 0.0660341, 0.00148259, 2.5, 1, -0.996884, 0.0190114, 0.0765315, 0.00178902, 2.53333, 1, -0.99599, 0.0225163, 0.0865615, 0.00217119, 2.56667, 1, -0.995036, 0.0258584, 0.0960589, 0.00261842, 2.6, 1, -0.994048, 0.029011, 0.104959, 0.00311799, 2.63333, 1, -0.993052, 0.0319481, 0.113198, 0.00365565, 2.66667, 1, -0.992074, 0.034644, 0.120713, 0.00421538, 2.7, 1, -0.99114, 0.0370779, 0.127449, 0.00478151, 2.73333, 1, -0.990272, 0.0392466, 0.133387, 0.00534148, 2.76667, 1, -0.989487, 0.0411513, 0.138516, 0.00588335, 2.8, 1, -0.988802, 0.0427935, 0.142825, 0.00639712, 2.83333, 1, -0.988229, 0.0441748, 0.146304, 0.00687232, 2.86667, 1, -0.987781, 0.0452969, 0.148941, 0.00729992, 2.9, 1, -0.987467, 0.0461615, 0.150726, 0.00767153, 2.93333, 1, -0.987295, 0.0467702, 0.151646, 0.00797961, 3, 1, -0.987393, 0.0472265, 0.150844, 0.00838042, 3.03333, 1, -0.987665, 0.0470768, 0.149096, 0.00846318, 3.06667, 1, -0.988083, 0.0466765, 0.146432, 0.00846281, 3.1, 1, -0.98864, 0.0460264, 0.142838, 0.00837758, 3.13333, 1, -0.989328, 0.0451267, 0.138299, 0.00820688, 3.16667, 1, -0.990135, 0.0439774, 0.132801, 0.00795191, 3.2, 1, -0.991043, 0.0425778, 0.126344, 0.00761613, 3.23333, 1, -0.992024, 0.0409262, 0.119, 0.00720744, 3.26667, 1, -0.993047, 0.0390208, 0.110858, 0.00673564, 3.3, 1, -0.994081, 0.0368594, 0.102005, 0.00621174, 3.33333, 1, -0.995098, 0.0344395, 0.0925314, 0.00564743, 3.36667, 1, -0.99607, 0.0317635, 0.0825218, 0.00505618, 3.4, 1, -0.996974, 0.0288504, 0.0720448, 0.00445501, 3.43333, 1, -0.997789, 0.0257243, 0.0611654, 0.00386159, 3.46667, 1, -0.998495, 0.0224095, 0.0499488, 0.00329284, 3.5, 1, -0.999077, 0.0189312, 0.0384613, 0.00276395, 3.53333, 1, -0.999522, 0.0153147, 0.0267694, 0.00228941, 3.66667, 1, -0.999785, -3.75634e-06, -0.0206967, 0.00115122, 3.7, 1, -0.999467, -0.00391046, -0.0323995, 0.0010922, 3.73333, 1, -0.999005, -0.0077925, -0.0439015, 0.00112897, 3.76667, 1, -0.99841, -0.0116219, -0.0551353, 0.00126044, 3.8, 1, -0.997698, -0.0153708, -0.0660342, 0.00148259, 3.83333, 1, -0.996884, -0.0190114, -0.0765315, 0.00178902, 3.86667, 1, -0.99599, -0.0225163, -0.0865615, 0.00217119, 3.9, 1, -0.995036, -0.0258584, -0.0960589, 0.00261842, 3.93333, 1, -0.994048, -0.029011, -0.104959, 0.00311807, 3.96667, 1, -0.993052, -0.0319481, -0.113198, 0.00365556, 4, 1, -0.992074, -0.034644, -0.120713, 0.00421538, 4.03333, 1, -0.99114, -0.0370779, -0.127449, 0.00478142, 4.06667, 1, -0.990272, -0.0392466, -0.133387, 0.00534148, 4.1, 1, -0.989487, -0.0411513, -0.138516, 0.00588334, 4.13333, 1, -0.988802, -0.0427935, -0.142825, 0.00639712, 4.16667, 1, -0.988229, -0.0441748, -0.146304, 0.00687232, 4.2, 1, -0.987781, -0.0452969, -0.148941, 0.0073, 4.23333, 1, -0.987467, -0.0461615, -0.150726, 0.00767153, 4.26667, 1, -0.987295, -0.0467702, -0.151646, 0.00797961, 4.33333, 1, -0.987393, -0.0472265, -0.150844, 0.00838042, 4.36667, 1, -0.987665, -0.0470768, -0.149096, 0.00846318, 4.4, 1, -0.988083, -0.0466765, -0.146432, 0.00846281, 4.43333, 1, -0.98864, -0.0460264, -0.142838, 0.00837767, 4.46667, 1, -0.989328, -0.0451267, -0.138299, 0.00820697, 4.5, 1, -0.990135, -0.0439774, -0.1328, 0.00795199, 4.53333, 1, -0.991043, -0.0425778, -0.126344, 0.00761613, 4.56667, 1, -0.992024, -0.0409262, -0.119, 0.00720744, 4.6, 1, -0.993047, -0.0390208, -0.110858, 0.00673564, 4.63333, 1, -0.994081, -0.0368593, -0.102005, 0.00621174, 4.66667, 1, -0.995098, -0.0344395, -0.0925314, 0.00564742, 4.7, 1, -0.99607, -0.0317634, -0.0825218, 0.00505618, 4.73333, 1, -0.996974, -0.0288504, -0.0720448, 0.0044551, 4.76667, 1, -0.997789, -0.0257243, -0.0611654, 0.00386159, 4.8, 1, -0.998495, -0.0224095, -0.0499489, 0.00329284, 4.83333, 1, -0.999077, -0.0189312, -0.0384614, 0.00276394, 4.86667, 1, -0.999522, -0.0153147, -0.0267694, 0.00228941, 5, 1, -0.999785, 3.75107e-06, 0.0206967, 0.0011513, 5.03333, 1, -0.999467, 0.00391046, 0.0323995, 0.0010922, 5.06667, 1, -0.999005, 0.0077925, 0.0439015, 0.00112897, 5.1, 1, -0.99841, 0.0116219, 0.0551353, 0.00126044, 5.13333, 1, -0.997698, 0.0153708, 0.0660341, 0.00148259, 5.16667, 1, -0.996884, 0.0190114, 0.0765315, 0.00178902, 5.2, 1, -0.99599, 0.0225163, 0.0865615, 0.00217119, 5.23333, 1, -0.995036, 0.0258584, 0.0960589, 0.00261842, 5.26667, 1, -0.994048, 0.029011, 0.104959, 0.00311799, 5.3, 1, -0.993052, 0.0319481, 0.113198, 0.00365565, 5.33333, 1, -0.992074, 0.034644, 0.120713, 0.00421538)
tracks/8/type = "position_3d"
tracks/8/imported = true
tracks/8/enabled = true
tracks/8/path = NodePath("RIG-Armature/Skeleton3D:DEF-arm.L")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = PackedFloat32Array(0, 1, 0.160613, 0.550005, -0.0171256, 0.0333333, 1, 0.161308, 0.549347, -0.016421, 0.1, 1, 0.1619, 0.548269, -0.0146407, 0.133333, 1, 0.161813, 0.547849, -0.0135844, 0.166667, 1, 0.16148, 0.547508, -0.0124307, 0.2, 1, 0.16091, 0.547244, -0.0111897, 0.233333, 1, 0.160111, 0.547058, -0.00987183, 0.266667, 1, 0.15909, 0.546946, -0.00848767, 0.3, 1, 0.157856, 0.546909, -0.00704793, 0.333333, 1, 0.156418, 0.546944, -0.0055635, 0.366667, 1, 0.154784, 0.54705, -0.00404541, 0.4, 1, 0.152964, 0.547226, -0.00250475, 0.433333, 1, 0.150968, 0.547469, -0.000952695, 0.466667, 1, 0.148806, 0.547778, 0.000599572, 0.5, 1, 0.146488, 0.548151, 0.00214085, 0.533333, 1, 0.144024, 0.548586, 0.00366002, 0.566667, 1, 0.141426, 0.549082, 0.00514601, 0.6, 1, 0.138706, 0.549637, 0.00658789, 0.633333, 1, 0.135876, 0.55025, 0.00797492, 0.666667, 1, 0.132946, 0.550918, 0.00929655, 0.7, 1, 0.129932, 0.55164, 0.0105437, 0.866667, 1, 0.114247, 0.555797, 0.0155615, 0.9, 1, 0.111124, 0.556687, 0.0163073, 0.933333, 1, 0.108053, 0.557582, 0.0169631, 0.966667, 1, 0.105053, 0.558476, 0.0175271, 1, 1, 0.102145, 0.559364, 0.0179975, 1.03333, 1, 0.0993478, 0.560241, 0.0183728, 1.06667, 1, 0.0966825, 0.561103, 0.0186509, 1.1, 1, 0.0941691, 0.561945, 0.0188302, 1.13333, 1, 0.0918276, 0.562765, 0.0189085, 1.16667, 1, 0.0896783, 0.563557, 0.0188836, 1.2, 1, 0.0877409, 0.56432, 0.0187534, 1.23333, 1, 0.0860354, 0.565049, 0.0185152, 1.26667, 1, 0.0845812, 0.565742, 0.0181665, 1.3, 1, 0.0833978, 0.566395, 0.0177044, 1.33333, 1, 0.082504, 0.567006, 0.0171257, 1.36667, 1, 0.0819134, 0.56757, 0.0164293, 1.43333, 1, 0.0816046, 0.56856, 0.014714, 1.46667, 1, 0.0818595, 0.568984, 0.0137132, 1.5, 1, 0.0823693, 0.56936, 0.0126289, 1.53333, 1, 0.0831206, 0.569686, 0.0114697, 1.56667, 1, 0.0841001, 0.569962, 0.0102444, 1.6, 1, 0.0852944, 0.570184, 0.00896166, 1.63333, 1, 0.0866908, 0.570353, 0.00762993, 1.66667, 1, 0.0882763, 0.570465, 0.00625773, 1.7, 1, 0.0900386, 0.570518, 0.00485355, 1.73333, 1, 0.0919655, 0.570512, 0.00342581, 1.76667, 1, 0.0940452, 0.570444, 0.00198295, 1.8, 1, 0.0962661, 0.570312, 0.000533387, 1.83333, 1, 0.0986173, 0.570114, -0.000914446, 1.86667, 1, 0.101088, 0.569849, -0.00235208, 1.9, 1, 0.103668, 0.569515, -0.003771, 1.93333, 1, 0.106346, 0.569111, -0.00516262, 1.96667, 1, 0.109114, 0.568635, -0.00651826, 2, 1, 0.111962, 0.568086, -0.0078291, 2.03333, 1, 0.114879, 0.567463, -0.00908705, 2.2, 1, 0.129906, 0.563418, -0.0144285, 2.23333, 1, 0.132878, 0.562463, -0.0152771, 2.26667, 1, 0.135798, 0.561476, -0.016042, 2.3, 1, 0.13865, 0.560463, -0.0167189, 2.33333, 1, 0.141416, 0.559432, -0.0173033, 2.36667, 1, 0.14408, 0.558391, -0.0177908, 2.4, 1, 0.146623, 0.557348, -0.0181769, 2.43333, 1, 0.14903, 0.556312, -0.0184576, 2.46667, 1, 0.151284, 0.555292, -0.0186284, 2.5, 1, 0.153367, 0.554296, -0.0186854, 2.53333, 1, 0.155262, 0.553334, -0.0186246, 2.56667, 1, 0.156952, 0.552415, -0.0184419, 2.6, 1, 0.158419, 0.551547, -0.0181336, 2.63333, 1, 0.159645, 0.550741, -0.0176961, 2.66667, 1, 0.160613, 0.550005, -0.0171256, 2.7, 1, 0.161308, 0.549347, -0.016421, 2.76667, 1, 0.1619, 0.548269, -0.0146407, 2.8, 1, 0.161813, 0.547849, -0.0135844, 2.83333, 1, 0.16148, 0.547508, -0.0124307, 2.86667, 1, 0.16091, 0.547244, -0.0111897, 2.9, 1, 0.160111, 0.547058, -0.00987183, 2.93333, 1, 0.15909, 0.546946, -0.00848767, 2.96667, 1, 0.157856, 0.546909, -0.00704793, 3, 1, 0.156418, 0.546944, -0.0055635, 3.03333, 1, 0.154784, 0.54705, -0.00404541, 3.06667, 1, 0.152964, 0.547226, -0.00250475, 3.1, 1, 0.150968, 0.547469, -0.000952695, 3.13333, 1, 0.148806, 0.547778, 0.000599572, 3.16667, 1, 0.146488, 0.548151, 0.00214085, 3.2, 1, 0.144024, 0.548586, 0.00366002, 3.23333, 1, 0.141426, 0.549082, 0.00514601, 3.26667, 1, 0.138706, 0.549637, 0.00658789, 3.3, 1, 0.135876, 0.55025, 0.00797492, 3.33333, 1, 0.132946, 0.550918, 0.00929655, 3.36667, 1, 0.129932, 0.55164, 0.0105437, 3.53333, 1, 0.114247, 0.555797, 0.0155615, 3.56667, 1, 0.111124, 0.556687, 0.0163073, 3.6, 1, 0.108053, 0.557582, 0.0169631, 3.63333, 1, 0.105053, 0.558476, 0.0175271, 3.66667, 1, 0.102145, 0.559364, 0.0179975, 3.7, 1, 0.0993478, 0.560241, 0.0183728, 3.73333, 1, 0.0966825, 0.561103, 0.0186509, 3.76667, 1, 0.0941691, 0.561945, 0.0188302, 3.8, 1, 0.0918276, 0.562765, 0.0189085, 3.83333, 1, 0.0896783, 0.563557, 0.0188836, 3.86667, 1, 0.0877409, 0.56432, 0.0187534, 3.9, 1, 0.0860354, 0.565049, 0.0185152, 3.93333, 1, 0.0845812, 0.565742, 0.0181665, 3.96667, 1, 0.0833978, 0.566395, 0.0177044, 4, 1, 0.082504, 0.567006, 0.0171257, 4.03333, 1, 0.0819134, 0.56757, 0.0164293, 4.1, 1, 0.0816046, 0.56856, 0.014714, 4.13333, 1, 0.0818595, 0.568984, 0.0137132, 4.16667, 1, 0.0823693, 0.56936, 0.0126289, 4.2, 1, 0.0831206, 0.569686, 0.0114697, 4.23333, 1, 0.0841001, 0.569962, 0.0102444, 4.26667, 1, 0.0852944, 0.570184, 0.00896166, 4.3, 1, 0.0866908, 0.570353, 0.00762993, 4.33333, 1, 0.0882763, 0.570465, 0.00625773, 4.36667, 1, 0.0900386, 0.570518, 0.00485355, 4.4, 1, 0.0919655, 0.570512, 0.00342581, 4.43333, 1, 0.0940452, 0.570444, 0.00198295, 4.46667, 1, 0.0962661, 0.570312, 0.000533387, 4.5, 1, 0.0986173, 0.570114, -0.000914446, 4.53333, 1, 0.101088, 0.569849, -0.00235208, 4.56667, 1, 0.103668, 0.569515, -0.003771, 4.6, 1, 0.106346, 0.569111, -0.00516262, 4.63333, 1, 0.109114, 0.568635, -0.00651826, 4.66667, 1, 0.111962, 0.568086, -0.0078291, 4.7, 1, 0.114879, 0.567463, -0.00908705, 4.86667, 1, 0.129906, 0.563418, -0.0144285, 4.9, 1, 0.132878, 0.562463, -0.0152771, 4.93333, 1, 0.135798, 0.561476, -0.016042, 4.96667, 1, 0.13865, 0.560463, -0.0167189, 5, 1, 0.141416, 0.559432, -0.0173033, 5.03333, 1, 0.14408, 0.558391, -0.0177908, 5.06667, 1, 0.146623, 0.557348, -0.0181769, 5.1, 1, 0.14903, 0.556312, -0.0184576, 5.13333, 1, 0.151284, 0.555292, -0.0186284, 5.16667, 1, 0.153367, 0.554296, -0.0186854, 5.2, 1, 0.155262, 0.553334, -0.0186246, 5.23333, 1, 0.156952, 0.552415, -0.0184419, 5.26667, 1, 0.158419, 0.551547, -0.0181336, 5.3, 1, 0.159645, 0.550741, -0.0176961, 5.33333, 1, 0.160613, 0.550005, -0.0171256)
tracks/9/type = "rotation_3d"
tracks/9/imported = true
tracks/9/enabled = true
tracks/9/path = NodePath("RIG-Armature/Skeleton3D:DEF-arm.L")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = PackedFloat32Array(0, 1, -0.492004, -0.493145, -0.411147, 0.58796, 0.0333333, 1, -0.493836, -0.492232, -0.413861, 0.58528, 0.0666667, 1, -0.495338, -0.491745, -0.41676, 0.582356, 0.1, 1, -0.496512, -0.491669, -0.419827, 0.579209, 0.133333, 1, -0.497362, -0.491988, -0.423044, 0.575858, 0.166667, 1, -0.497887, -0.492685, -0.426395, 0.572325, 0.2, 1, -0.49809, -0.493744, -0.429863, 0.56863, 0.233333, 1, -0.497971, -0.495148, -0.433431, 0.564793, 0.266667, 1, -0.49753, -0.496881, -0.437079, 0.560834, 0.3, 1, -0.496768, -0.498924, -0.440793, 0.556774, 0.333333, 1, -0.495686, -0.501262, -0.444552, 0.552635, 0.366667, 1, -0.494285, -0.503876, -0.448339, 0.548438, 0.4, 1, -0.492575, -0.506741, -0.452124, 0.544213, 0.433333, 1, -0.490568, -0.509831, -0.455879, 0.539991, 0.466667, 1, -0.488278, -0.51312, -0.459574, 0.535802, 0.633333, 1, -0.473072, -0.531733, -0.476135, 0.516486, 0.666667, 1, -0.469394, -0.535733, -0.478863, 0.513176, 0.7, 1, -0.465545, -0.539778, -0.481333, 0.510123, 0.733333, 1, -0.461545, -0.543847, -0.483541, 0.50734, 0.766667, 1, -0.457415, -0.547923, -0.485485, 0.504834, 0.8, 1, -0.453176, -0.551986, -0.487161, 0.50261, 0.833333, 1, -0.448849, -0.55602, -0.48857, 0.500676, 0.866667, 1, -0.444458, -0.560006, -0.489706, 0.499037, 0.9, 1, -0.440025, -0.563929, -0.490569, 0.497699, 0.933333, 1, -0.435574, -0.567771, -0.491155, 0.496667, 0.966667, 1, -0.431131, -0.571517, -0.491461, 0.495945, 1, 1, -0.426719, -0.575151, -0.491482, 0.495538, 1.03333, 1, -0.422366, -0.578658, -0.491218, 0.495446, 1.06667, 1, -0.418106, -0.582014, -0.490674, 0.495667, 1.1, 1, -0.413976, -0.585196, -0.489859, 0.496193, 1.13333, 1, -0.410012, -0.588178, -0.488781, 0.49702, 1.16667, 1, -0.406251, -0.590939, -0.487448, 0.498143, 1.2, 1, -0.402728, -0.593453, -0.485869, 0.499555, 1.23333, 1, -0.399481, -0.595698, -0.484052, 0.50125, 1.26667, 1, -0.396546, -0.597652, -0.482005, 0.503225, 1.3, 1, -0.393957, -0.599289, -0.479737, 0.505473, 1.36667, 1, -0.389957, -0.601527, -0.474571, 0.510766, 1.4, 1, -0.38857, -0.602113, -0.471699, 0.513784, 1.43333, 1, -0.387583, -0.602354, -0.468657, 0.51702, 1.46667, 1, -0.386985, -0.602258, -0.465463, 0.520453, 1.5, 1, -0.386767, -0.601835, -0.462133, 0.524061, 1.53333, 1, -0.386916, -0.60109, -0.458685, 0.52782, 1.56667, 1, -0.387423, -0.600033, -0.455136, 0.531709, 1.6, 1, -0.388276, -0.59867, -0.451503, 0.535706, 1.63333, 1, -0.389464, -0.597009, -0.447803, 0.539788, 1.66667, 1, -0.390976, -0.595056, -0.444052, 0.543934, 1.7, 1, -0.392798, -0.592821, -0.440269, 0.54812, 1.73333, 1, -0.394911, -0.590319, -0.43648, 0.552317, 1.76667, 1, -0.397291, -0.587565, -0.432715, 0.556493, 1.8, 1, -0.399917, -0.584578, -0.429002, 0.560618, 1.96667, 1, -0.416034, -0.566791, -0.412236, 0.579417, 2, 1, -0.419727, -0.562808, -0.409451, 0.582604, 2.03333, 1, -0.423535, -0.558733, -0.406922, 0.585534, 2.06667, 1, -0.427441, -0.554585, -0.404657, 0.588203, 2.1, 1, -0.431423, -0.550384, -0.402662, 0.590605, 2.13333, 1, -0.435463, -0.546148, -0.400942, 0.592739, 2.16667, 1, -0.439541, -0.541899, -0.3995, 0.5946, 2.2, 1, -0.443639, -0.537657, -0.39834, 0.596183, 2.23333, 1, -0.44774, -0.533443, -0.397465, 0.597485, 2.26667, 1, -0.451826, -0.529279, -0.396879, 0.598501, 2.3, 1, -0.455881, -0.525188, -0.396584, 0.599226, 2.33333, 1, -0.459888, -0.521193, -0.39658, 0.599654, 2.36667, 1, -0.463831, -0.517318, -0.39687, 0.599781, 2.4, 1, -0.467682, -0.513595, -0.397443, 0.59961, 2.43333, 1, -0.471417, -0.510055, -0.398292, 0.599144, 2.46667, 1, -0.475006, -0.506732, -0.399407, 0.598387, 2.5, 1, -0.478423, -0.503656, -0.400777, 0.597344, 2.53333, 1, -0.481642, -0.500862, -0.402394, 0.596017, 2.56667, 1, -0.484637, -0.498382, -0.404249, 0.594412, 2.6, 1, -0.48738, -0.496247, -0.406332, 0.592532, 2.63333, 1, -0.489845, -0.494491, -0.408634, 0.59038, 2.7, 1, -0.493836, -0.492232, -0.413861, 0.58528, 2.73333, 1, -0.495338, -0.491745, -0.41676, 0.582356, 2.76667, 1, -0.496512, -0.491669, -0.419827, 0.579209, 2.8, 1, -0.497362, -0.491988, -0.423044, 0.575858, 2.83333, 1, -0.497887, -0.492685, -0.426395, 0.572325, 2.86667, 1, -0.49809, -0.493744, -0.429863, 0.56863, 2.9, 1, -0.497971, -0.495148, -0.433431, 0.564793, 2.93333, 1, -0.49753, -0.496881, -0.437079, 0.560834, 2.96667, 1, -0.496768, -0.498924, -0.440793, 0.556774, 3, 1, -0.495686, -0.501262, -0.444552, 0.552635, 3.03333, 1, -0.494285, -0.503876, -0.448339, 0.548438, 3.06667, 1, -0.492575, -0.506741, -0.452124, 0.544213, 3.1, 1, -0.490568, -0.509831, -0.455879, 0.539991, 3.13333, 1, -0.488278, -0.51312, -0.459574, 0.535802, 3.3, 1, -0.473072, -0.531733, -0.476135, 0.516486, 3.33333, 1, -0.469394, -0.535733, -0.478863, 0.513176, 3.36667, 1, -0.465545, -0.539778, -0.481333, 0.510123, 3.4, 1, -0.461545, -0.543847, -0.483541, 0.50734, 3.43333, 1, -0.457415, -0.547923, -0.485485, 0.504834, 3.46667, 1, -0.453176, -0.551986, -0.487161, 0.50261, 3.5, 1, -0.448849, -0.55602, -0.48857, 0.500676, 3.53333, 1, -0.444458, -0.560006, -0.489706, 0.499037, 3.56667, 1, -0.440025, -0.563929, -0.490569, 0.497699, 3.6, 1, -0.435574, -0.567771, -0.491155, 0.496667, 3.63333, 1, -0.431131, -0.571517, -0.491461, 0.495945, 3.66667, 1, -0.426719, -0.575151, -0.491482, 0.495538, 3.7, 1, -0.422366, -0.578658, -0.491218, 0.495446, 3.73333, 1, -0.418106, -0.582014, -0.490674, 0.495667, 3.76667, 1, -0.413976, -0.585196, -0.489859, 0.496193, 3.8, 1, -0.410012, -0.588178, -0.488781, 0.49702, 3.83333, 1, -0.406251, -0.590939, -0.487448, 0.498143, 3.86667, 1, -0.402728, -0.593453, -0.485869, 0.499555, 3.9, 1, -0.399481, -0.595698, -0.484052, 0.50125, 3.93333, 1, -0.396546, -0.597652, -0.482005, 0.503225, 3.96667, 1, -0.393957, -0.599289, -0.479737, 0.505473, 4.03333, 1, -0.389957, -0.601527, -0.474571, 0.510766, 4.06667, 1, -0.38857, -0.602113, -0.471699, 0.513784, 4.1, 1, -0.387583, -0.602354, -0.468657, 0.51702, 4.13333, 1, -0.386985, -0.602258, -0.465463, 0.520453, 4.16667, 1, -0.386767, -0.601835, -0.462133, 0.524061, 4.2, 1, -0.386916, -0.60109, -0.458685, 0.52782, 4.23333, 1, -0.387423, -0.600033, -0.455136, 0.531709, 4.26667, 1, -0.388276, -0.59867, -0.451503, 0.535706, 4.3, 1, -0.389464, -0.597009, -0.447803, 0.539788, 4.33333, 1, -0.390976, -0.595056, -0.444052, 0.543934, 4.36667, 1, -0.392798, -0.592821, -0.440269, 0.54812, 4.4, 1, -0.394911, -0.590319, -0.43648, 0.552317, 4.43333, 1, -0.397291, -0.587565, -0.432715, 0.556493, 4.46667, 1, -0.399917, -0.584578, -0.429002, 0.560618, 4.63333, 1, -0.416034, -0.566791, -0.412236, 0.579417, 4.66667, 1, -0.419727, -0.562808, -0.409451, 0.582604, 4.7, 1, -0.423535, -0.558733, -0.406922, 0.585534, 4.73333, 1, -0.427441, -0.554585, -0.404657, 0.588203, 4.76667, 1, -0.431423, -0.550384, -0.402662, 0.590605, 4.8, 1, -0.435463, -0.546148, -0.400942, 0.592739, 4.83333, 1, -0.439541, -0.541899, -0.3995, 0.5946, 4.86667, 1, -0.443639, -0.537657, -0.39834, 0.596183, 4.9, 1, -0.44774, -0.533443, -0.397465, 0.597485, 4.93333, 1, -0.451826, -0.529279, -0.396879, 0.598501, 4.96667, 1, -0.455881, -0.525188, -0.396584, 0.599226, 5, 1, -0.459888, -0.521193, -0.39658, 0.599654, 5.03333, 1, -0.463831, -0.517318, -0.39687, 0.599781, 5.06667, 1, -0.467682, -0.513595, -0.397443, 0.59961, 5.1, 1, -0.471417, -0.510055, -0.398292, 0.599144, 5.13333, 1, -0.475006, -0.506732, -0.399407, 0.598387, 5.16667, 1, -0.478423, -0.503656, -0.400777, 0.597344, 5.2, 1, -0.481642, -0.500862, -0.402394, 0.596017, 5.23333, 1, -0.484637, -0.498382, -0.404249, 0.594412, 5.26667, 1, -0.48738, -0.496247, -0.406332, 0.592532, 5.3, 1, -0.489845, -0.494491, -0.408634, 0.59038, 5.33333, 1, -0.492004, -0.493145, -0.411148, 0.587961)
tracks/10/type = "position_3d"
tracks/10/imported = true
tracks/10/enabled = true
tracks/10/path = NodePath("RIG-Armature/Skeleton3D:DEF-arm.R")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = PackedFloat32Array(0, 1, -0.082504, 0.567006, 0.0171257, 0.0333333, 1, -0.0819134, 0.56757, 0.0164293, 0.1, 1, -0.0816045, 0.56856, 0.014714, 0.133333, 1, -0.0818594, 0.568984, 0.0137132, 0.166667, 1, -0.0823692, 0.56936, 0.0126289, 0.2, 1, -0.0831206, 0.569686, 0.0114697, 0.233333, 1, -0.0841, 0.569962, 0.0102444, 0.266667, 1, -0.0852944, 0.570184, 0.00896165, 0.3, 1, -0.0866907, 0.570353, 0.00762991, 0.333333, 1, -0.0882763, 0.570465, 0.00625772, 0.366667, 1, -0.0900386, 0.570518, 0.00485353, 0.4, 1, -0.0919654, 0.570512, 0.0034258, 0.433333, 1, -0.0940451, 0.570444, 0.00198294, 0.466667, 1, -0.0962661, 0.570312, 0.000533373, 0.5, 1, -0.0986172, 0.570114, -0.000914459, 0.533333, 1, -0.101088, 0.569849, -0.00235209, 0.566667, 1, -0.103667, 0.569515, -0.00377101, 0.6, 1, -0.106346, 0.569111, -0.00516263, 0.633333, 1, -0.109114, 0.568635, -0.00651827, 0.666667, 1, -0.111962, 0.568086, -0.00782911, 0.7, 1, -0.114879, 0.567463, -0.00908706, 0.866667, 1, -0.129906, 0.563418, -0.0144285, 0.9, 1, -0.132878, 0.562463, -0.0152771, 0.933333, 1, -0.135798, 0.561476, -0.016042, 0.966667, 1, -0.13865, 0.560463, -0.0167189, 1, 1, -0.141416, 0.559432, -0.0173033, 1.03333, 1, -0.144079, 0.558391, -0.0177908, 1.06667, 1, -0.146623, 0.557348, -0.0181769, 1.1, 1, -0.14903, 0.556312, -0.0184576, 1.13333, 1, -0.151284, 0.555292, -0.0186284, 1.16667, 1, -0.153367, 0.554296, -0.0186854, 1.2, 1, -0.155262, 0.553334, -0.0186246, 1.23333, 1, -0.156951, 0.552415, -0.0184419, 1.26667, 1, -0.158419, 0.551547, -0.0181336, 1.3, 1, -0.159645, 0.550741, -0.0176961, 1.33333, 1, -0.160613, 0.550005, -0.0171256, 1.36667, 1, -0.161308, 0.549347, -0.016421, 1.43333, 1, -0.1619, 0.548269, -0.0146407, 1.46667, 1, -0.161813, 0.547849, -0.0135844, 1.5, 1, -0.16148, 0.547508, -0.0124307, 1.53333, 1, -0.16091, 0.547244, -0.0111897, 1.56667, 1, -0.160111, 0.547058, -0.00987183, 1.6, 1, -0.15909, 0.546946, -0.00848766, 1.63333, 1, -0.157856, 0.546909, -0.00704792, 1.66667, 1, -0.156418, 0.546944, -0.0055635, 1.7, 1, -0.154784, 0.54705, -0.00404541, 1.73333, 1, -0.152964, 0.547226, -0.00250475, 1.76667, 1, -0.150968, 0.547469, -0.000952688, 1.8, 1, -0.148806, 0.547778, 0.000599573, 1.83333, 1, -0.146487, 0.548151, 0.00214086, 1.86667, 1, -0.144024, 0.548586, 0.00366002, 1.9, 1, -0.141426, 0.549082, 0.00514601, 1.93333, 1, -0.138706, 0.549637, 0.00658789, 1.96667, 1, -0.135876, 0.55025, 0.00797493, 2, 1, -0.132946, 0.550918, 0.00929655, 2.03333, 1, -0.129932, 0.55164, 0.0105437, 2.2, 1, -0.114247, 0.555797, 0.0155615, 2.23333, 1, -0.111124, 0.556687, 0.0163073, 2.26667, 1, -0.108053, 0.557582, 0.0169631, 2.3, 1, -0.105053, 0.558476, 0.0175271, 2.33333, 1, -0.102145, 0.559364, 0.0179975, 2.36667, 1, -0.0993478, 0.560241, 0.0183728, 2.4, 1, -0.0966825, 0.561103, 0.0186509, 2.43333, 1, -0.0941691, 0.561945, 0.0188302, 2.46667, 1, -0.0918276, 0.562765, 0.0189084, 2.5, 1, -0.0896782, 0.563557, 0.0188836, 2.53333, 1, -0.0877409, 0.56432, 0.0187534, 2.56667, 1, -0.0860353, 0.565049, 0.0185152, 2.6, 1, -0.0845812, 0.565742, 0.0181665, 2.63333, 1, -0.0833977, 0.566395, 0.0177044, 2.66667, 1, -0.082504, 0.567006, 0.0171257, 2.7, 1, -0.0819134, 0.56757, 0.0164293, 2.76667, 1, -0.0816045, 0.56856, 0.014714, 2.8, 1, -0.0818594, 0.568984, 0.0137132, 2.83333, 1, -0.0823692, 0.56936, 0.0126289, 2.86667, 1, -0.0831206, 0.569686, 0.0114697, 2.9, 1, -0.0841, 0.569962, 0.0102444, 2.93333, 1, -0.0852944, 0.570184, 0.00896165, 2.96667, 1, -0.0866907, 0.570353, 0.00762991, 3, 1, -0.0882763, 0.570465, 0.00625772, 3.03333, 1, -0.0900386, 0.570518, 0.00485353, 3.06667, 1, -0.0919654, 0.570512, 0.0034258, 3.1, 1, -0.0940451, 0.570444, 0.00198294, 3.13333, 1, -0.0962661, 0.570312, 0.000533373, 3.16667, 1, -0.0986172, 0.570114, -0.000914459, 3.2, 1, -0.101088, 0.569849, -0.00235209, 3.23333, 1, -0.103667, 0.569515, -0.00377101, 3.26667, 1, -0.106346, 0.569111, -0.00516263, 3.3, 1, -0.109114, 0.568635, -0.00651827, 3.33333, 1, -0.111962, 0.568086, -0.00782911, 3.36667, 1, -0.114879, 0.567463, -0.00908706, 3.53333, 1, -0.129906, 0.563418, -0.0144285, 3.56667, 1, -0.132878, 0.562463, -0.0152771, 3.6, 1, -0.135798, 0.561476, -0.016042, 3.63333, 1, -0.13865, 0.560463, -0.0167189, 3.66667, 1, -0.141416, 0.559432, -0.0173033, 3.7, 1, -0.144079, 0.558391, -0.0177908, 3.73333, 1, -0.146623, 0.557348, -0.0181769, 3.76667, 1, -0.14903, 0.556312, -0.0184576, 3.8, 1, -0.151284, 0.555292, -0.0186284, 3.83333, 1, -0.153367, 0.554296, -0.0186854, 3.86667, 1, -0.155262, 0.553334, -0.0186246, 3.9, 1, -0.156951, 0.552415, -0.0184419, 3.93333, 1, -0.158419, 0.551547, -0.0181336, 3.96667, 1, -0.159645, 0.550741, -0.0176961, 4, 1, -0.160613, 0.550005, -0.0171256, 4.03333, 1, -0.161308, 0.549347, -0.016421, 4.1, 1, -0.1619, 0.548269, -0.0146407, 4.13333, 1, -0.161813, 0.547849, -0.0135844, 4.16667, 1, -0.16148, 0.547508, -0.0124307, 4.2, 1, -0.16091, 0.547244, -0.0111897, 4.23333, 1, -0.160111, 0.547058, -0.00987183, 4.26667, 1, -0.15909, 0.546946, -0.00848766, 4.3, 1, -0.157856, 0.546909, -0.00704792, 4.33333, 1, -0.156418, 0.546944, -0.0055635, 4.36667, 1, -0.154784, 0.54705, -0.00404541, 4.4, 1, -0.152964, 0.547226, -0.00250475, 4.43333, 1, -0.150968, 0.547469, -0.000952688, 4.46667, 1, -0.148806, 0.547778, 0.000599573, 4.5, 1, -0.146487, 0.548151, 0.00214086, 4.53333, 1, -0.144024, 0.548586, 0.00366002, 4.56667, 1, -0.141426, 0.549082, 0.00514601, 4.6, 1, -0.138706, 0.549637, 0.00658789, 4.63333, 1, -0.135876, 0.55025, 0.00797493, 4.66667, 1, -0.132946, 0.550918, 0.00929655, 4.7, 1, -0.129932, 0.55164, 0.0105437, 4.86667, 1, -0.114247, 0.555797, 0.0155615, 4.9, 1, -0.111124, 0.556687, 0.0163073, 4.93333, 1, -0.108053, 0.557582, 0.0169631, 4.96667, 1, -0.105053, 0.558476, 0.0175271, 5, 1, -0.102145, 0.559364, 0.0179975, 5.03333, 1, -0.0993478, 0.560241, 0.0183728, 5.06667, 1, -0.0966825, 0.561103, 0.0186509, 5.1, 1, -0.0941691, 0.561945, 0.0188302, 5.13333, 1, -0.0918276, 0.562765, 0.0189084, 5.16667, 1, -0.0896782, 0.563557, 0.0188836, 5.2, 1, -0.0877409, 0.56432, 0.0187534, 5.23333, 1, -0.0860353, 0.565049, 0.0185152, 5.26667, 1, -0.0845812, 0.565742, 0.0181665, 5.3, 1, -0.0833977, 0.566395, 0.0177044, 5.33333, 1, -0.082504, 0.567006, 0.0171257)
tracks/11/type = "rotation_3d"
tracks/11/imported = true
tracks/11/enabled = true
tracks/11/path = NodePath("RIG-Armature/Skeleton3D:DEF-arm.R")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = PackedFloat32Array(0, 1, -0.391752, 0.600587, 0.477255, 0.507989, 0.0333333, 1, -0.389957, 0.601527, 0.474571, 0.510766, 0.0666667, 1, -0.38857, 0.602113, 0.471699, 0.513784, 0.1, 1, -0.387583, 0.602354, 0.468657, 0.51702, 0.133333, 1, -0.386985, 0.602258, 0.465463, 0.520453, 0.166667, 1, -0.386767, 0.601835, 0.462133, 0.524061, 0.2, 1, -0.386916, 0.601091, 0.458685, 0.52782, 0.233333, 1, -0.387423, 0.600033, 0.455136, 0.531709, 0.266667, 1, -0.388276, 0.59867, 0.451503, 0.535706, 0.3, 1, -0.389464, 0.597009, 0.447803, 0.539788, 0.333333, 1, -0.390976, 0.595056, 0.444051, 0.543934, 0.366667, 1, -0.392798, 0.592821, 0.440269, 0.54812, 0.4, 1, -0.394911, 0.590319, 0.43648, 0.552317, 0.433333, 1, -0.397291, 0.587565, 0.432715, 0.556493, 0.466667, 1, -0.399917, 0.584578, 0.429002, 0.560618, 0.633333, 1, -0.416034, 0.566791, 0.412236, 0.579417, 0.666667, 1, -0.419727, 0.562808, 0.409451, 0.582604, 0.7, 1, -0.423535, 0.558733, 0.406922, 0.585534, 0.733333, 1, -0.427441, 0.554585, 0.404657, 0.588203, 0.766667, 1, -0.431423, 0.550384, 0.402662, 0.590606, 0.8, 1, -0.435463, 0.546148, 0.400942, 0.592739, 0.833333, 1, -0.439541, 0.541899, 0.3995, 0.5946, 0.866667, 1, -0.44364, 0.537657, 0.39834, 0.596183, 0.9, 1, -0.44774, 0.533443, 0.397465, 0.597485, 0.933333, 1, -0.451826, 0.529279, 0.396879, 0.598501, 0.966667, 1, -0.455881, 0.525188, 0.396583, 0.599226, 1, 1, -0.459888, 0.521193, 0.39658, 0.599654, 1.03333, 1, -0.463831, 0.517318, 0.39687, 0.599781, 1.06667, 1, -0.467682, 0.513595, 0.397443, 0.59961, 1.1, 1, -0.471417, 0.510055, 0.398292, 0.599144, 1.13333, 1, -0.475006, 0.506732, 0.399407, 0.598387, 1.16667, 1, -0.478423, 0.503656, 0.400777, 0.597344, 1.2, 1, -0.481642, 0.500862, 0.402394, 0.596017, 1.23333, 1, -0.484637, 0.498382, 0.404249, 0.594412, 1.26667, 1, -0.48738, 0.496247, 0.406332, 0.592532, 1.36667, 1, -0.493836, 0.492232, 0.413861, 0.58528, 1.4, 1, -0.495338, 0.491745, 0.41676, 0.582356, 1.43333, 1, -0.496512, 0.491669, 0.419827, 0.579209, 1.46667, 1, -0.497362, 0.491988, 0.423044, 0.575858, 1.5, 1, -0.497887, 0.492685, 0.426395, 0.572325, 1.53333, 1, -0.49809, 0.493744, 0.429863, 0.56863, 1.56667, 1, -0.497971, 0.495148, 0.433431, 0.564793, 1.6, 1, -0.49753, 0.496881, 0.437079, 0.560834, 1.63333, 1, -0.496768, 0.498924, 0.440793, 0.556775, 1.66667, 1, -0.495686, 0.501262, 0.444552, 0.552635, 1.7, 1, -0.494285, 0.503876, 0.448339, 0.548438, 1.73333, 1, -0.492575, 0.506741, 0.452124, 0.544213, 1.76667, 1, -0.490568, 0.509831, 0.455879, 0.53999, 1.8, 1, -0.488278, 0.51312, 0.459575, 0.535802, 1.96667, 1, -0.473072, 0.531733, 0.476135, 0.516486, 2, 1, -0.469394, 0.535733, 0.478863, 0.513176, 2.03333, 1, -0.465545, 0.539778, 0.481333, 0.510124, 2.06667, 1, -0.461545, 0.543847, 0.483541, 0.50734, 2.1, 1, -0.457415, 0.547923, 0.485485, 0.504834, 2.13333, 1, -0.453176, 0.551986, 0.487161, 0.50261, 2.16667, 1, -0.448849, 0.55602, 0.48857, 0.500676, 2.2, 1, -0.444458, 0.560006, 0.489706, 0.499037, 2.23333, 1, -0.440025, 0.563929, 0.490569, 0.497699, 2.26667, 1, -0.435574, 0.567771, 0.491155, 0.496667, 2.3, 1, -0.43113, 0.571517, 0.491461, 0.495945, 2.33333, 1, -0.426719, 0.575151, 0.491482, 0.495538, 2.36667, 1, -0.422366, 0.578658, 0.491218, 0.495446, 2.4, 1, -0.418106, 0.582014, 0.490674, 0.495666, 2.43333, 1, -0.413976, 0.585196, 0.489859, 0.496193, 2.46667, 1, -0.410012, 0.588178, 0.488781, 0.49702, 2.5, 1, -0.406251, 0.590939, 0.487448, 0.498143, 2.53333, 1, -0.402728, 0.593453, 0.485869, 0.499554, 2.56667, 1, -0.399481, 0.595698, 0.484052, 0.50125, 2.6, 1, -0.396546, 0.597652, 0.482005, 0.503225, 2.63333, 1, -0.393957, 0.599289, 0.479737, 0.505473, 2.7, 1, -0.389957, 0.601527, 0.474571, 0.510766, 2.73333, 1, -0.38857, 0.602113, 0.471699, 0.513784, 2.76667, 1, -0.387583, 0.602354, 0.468657, 0.51702, 2.8, 1, -0.386985, 0.602258, 0.465463, 0.520453, 2.83333, 1, -0.386767, 0.601835, 0.462133, 0.524061, 2.86667, 1, -0.386916, 0.601091, 0.458685, 0.52782, 2.9, 1, -0.387423, 0.600033, 0.455136, 0.531709, 2.93333, 1, -0.388276, 0.59867, 0.451503, 0.535706, 2.96667, 1, -0.389464, 0.597009, 0.447803, 0.539788, 3, 1, -0.390976, 0.595056, 0.444051, 0.543934, 3.03333, 1, -0.392798, 0.592821, 0.440269, 0.54812, 3.06667, 1, -0.394911, 0.590319, 0.43648, 0.552317, 3.1, 1, -0.397291, 0.587565, 0.432715, 0.556493, 3.13333, 1, -0.399917, 0.584578, 0.429002, 0.560618, 3.3, 1, -0.416034, 0.566791, 0.412236, 0.579417, 3.33333, 1, -0.419727, 0.562808, 0.409451, 0.582604, 3.36667, 1, -0.423535, 0.558733, 0.406922, 0.585534, 3.4, 1, -0.427441, 0.554585, 0.404657, 0.588203, 3.43333, 1, -0.431423, 0.550384, 0.402662, 0.590606, 3.46667, 1, -0.435463, 0.546148, 0.400942, 0.592739, 3.5, 1, -0.439541, 0.541899, 0.3995, 0.5946, 3.53333, 1, -0.44364, 0.537657, 0.39834, 0.596183, 3.56667, 1, -0.44774, 0.533443, 0.397465, 0.597485, 3.6, 1, -0.451826, 0.529279, 0.396879, 0.598501, 3.63333, 1, -0.455881, 0.525188, 0.396583, 0.599226, 3.66667, 1, -0.459888, 0.521193, 0.39658, 0.599654, 3.7, 1, -0.463831, 0.517318, 0.39687, 0.599781, 3.73333, 1, -0.467682, 0.513595, 0.397443, 0.59961, 3.76667, 1, -0.471417, 0.510055, 0.398292, 0.599144, 3.8, 1, -0.475006, 0.506732, 0.399407, 0.598387, 3.83333, 1, -0.478423, 0.503656, 0.400777, 0.597344, 3.86667, 1, -0.481642, 0.500862, 0.402394, 0.596017, 3.9, 1, -0.484637, 0.498382, 0.404249, 0.594412, 3.93333, 1, -0.48738, 0.496247, 0.406332, 0.592532, 4.03333, 1, -0.493836, 0.492232, 0.413861, 0.58528, 4.06667, 1, -0.495338, 0.491745, 0.41676, 0.582356, 4.1, 1, -0.496512, 0.491669, 0.419827, 0.579209, 4.13333, 1, -0.497362, 0.491988, 0.423044, 0.575858, 4.16667, 1, -0.497887, 0.492685, 0.426395, 0.572325, 4.2, 1, -0.49809, 0.493744, 0.429863, 0.56863, 4.23333, 1, -0.497971, 0.495148, 0.433431, 0.564793, 4.26667, 1, -0.49753, 0.496881, 0.437079, 0.560834, 4.3, 1, -0.496768, 0.498924, 0.440793, 0.556775, 4.33333, 1, -0.495686, 0.501262, 0.444552, 0.552635, 4.36667, 1, -0.494285, 0.503876, 0.448339, 0.548438, 4.4, 1, -0.492575, 0.506741, 0.452124, 0.544213, 4.43333, 1, -0.490568, 0.509831, 0.455879, 0.53999, 4.46667, 1, -0.488278, 0.51312, 0.459575, 0.535802, 4.63333, 1, -0.473072, 0.531733, 0.476135, 0.516486, 4.66667, 1, -0.469394, 0.535733, 0.478863, 0.513176, 4.7, 1, -0.465545, 0.539778, 0.481333, 0.510124, 4.73333, 1, -0.461545, 0.543847, 0.483541, 0.50734, 4.76667, 1, -0.457415, 0.547923, 0.485485, 0.504834, 4.8, 1, -0.453176, 0.551986, 0.487161, 0.50261, 4.83333, 1, -0.448849, 0.55602, 0.48857, 0.500676, 4.86667, 1, -0.444458, 0.560006, 0.489706, 0.499037, 4.9, 1, -0.440025, 0.563929, 0.490569, 0.497699, 4.93333, 1, -0.435574, 0.567771, 0.491155, 0.496667, 4.96667, 1, -0.43113, 0.571517, 0.491461, 0.495945, 5, 1, -0.426719, 0.575151, 0.491482, 0.495538, 5.03333, 1, -0.422366, 0.578658, 0.491218, 0.495446, 5.06667, 1, -0.418106, 0.582014, 0.490674, 0.495666, 5.1, 1, -0.413976, 0.585196, 0.489859, 0.496193, 5.13333, 1, -0.410012, 0.588178, 0.488781, 0.49702, 5.16667, 1, -0.406251, 0.590939, 0.487448, 0.498143, 5.2, 1, -0.402728, 0.593453, 0.485869, 0.499554, 5.23333, 1, -0.399481, 0.595698, 0.484052, 0.50125, 5.26667, 1, -0.396546, 0.597652, 0.482005, 0.503225, 5.3, 1, -0.393957, 0.599289, 0.479737, 0.505473, 5.33333, 1, -0.391752, 0.600587, 0.477255, 0.507989)
tracks/12/type = "position_3d"
tracks/12/imported = true
tracks/12/enabled = true
tracks/12/path = NodePath("RIG-Armature/Skeleton3D:DEF-torso")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = PackedFloat32Array(0, 1, 0.0285366, 0.408093, 5.03012e-09, 0.133333, 1, 0.0269409, 0.408201, 4.93461e-09, 0.166667, 1, 0.0260877, 0.408256, 4.91248e-09, 0.2, 1, 0.0250741, 0.40832, 4.89068e-09, 0.233333, 1, 0.0239106, 0.40839, 4.86887e-09, 0.266667, 1, 0.0226078, 0.408464, 4.84675e-09, 0.3, 1, 0.0211766, 0.408541, 4.8239e-09, 0.333333, 1, 0.0196273, 0.408618, 4.79997e-09, 0.366667, 1, 0.0179708, 0.408694, 4.77458e-09, 0.4, 1, 0.0162177, 0.408767, 4.74739e-09, 0.433333, 1, 0.0143787, 0.408836, 4.71801e-09, 0.466667, 1, 0.0124644, 0.408899, 4.68618e-09, 0.5, 1, 0.0104857, 0.408955, 4.65162e-09, 0.533333, 1, 0.0084531, 0.409002, 4.61412e-09, 0.6, 1, 0.00426962, 0.409067, 4.52991e-09, 0.766667, 1, -0.0063775, 0.409039, 4.26937e-09, 0.8, 1, -0.00845309, 0.409002, 4.21092e-09, 0.833333, 1, -0.0104856, 0.408955, 4.15145e-09, 0.866667, 1, -0.0124644, 0.408899, 4.09164e-09, 0.9, 1, -0.0143787, 0.408836, 4.03216e-09, 0.933333, 1, -0.0162177, 0.408767, 3.97382e-09, 0.966667, 1, -0.0179708, 0.408694, 3.9174e-09, 1, 1, -0.0196273, 0.408618, 3.86377e-09, 1.03333, 1, -0.0211765, 0.408541, 3.8138e-09, 1.06667, 1, -0.0226078, 0.408464, 3.76836e-09, 1.1, 1, -0.0239106, 0.40839, 3.72836e-09, 1.13333, 1, -0.0250741, 0.40832, 3.69465e-09, 1.16667, 1, -0.0260877, 0.408256, 3.66811e-09, 1.23333, 1, -0.0276231, 0.408155, 3.63967e-09, 1.5, 1, -0.0260877, 0.408256, 3.9182e-09, 1.53333, 1, -0.0250741, 0.40832, 3.99193e-09, 1.56667, 1, -0.0239106, 0.40839, 4.07128e-09, 1.6, 1, -0.0226078, 0.408464, 4.15515e-09, 1.63333, 1, -0.0211766, 0.408541, 4.24239e-09, 1.66667, 1, -0.0196273, 0.408618, 4.33187e-09, 1.7, 1, -0.0179708, 0.408694, 4.42245e-09, 1.73333, 1, -0.0162177, 0.408767, 4.51301e-09, 1.76667, 1, -0.0143787, 0.408836, 4.60243e-09, 1.8, 1, -0.0124644, 0.408899, 4.68965e-09, 1.83333, 1, -0.0104857, 0.408955, 4.77363e-09, 1.86667, 1, -0.0084531, 0.409002, 4.85345e-09, 1.93333, 1, -0.00426961, 0.409067, 4.99698e-09, 2.1, 1, 0.0063775, 0.409039, 5.23239e-09, 2.13333, 1, 0.0084531, 0.409002, 5.25664e-09, 2.16667, 1, 0.0104857, 0.408955, 5.2738e-09, 2.2, 1, 0.0124644, 0.408899, 5.28419e-09, 2.23333, 1, 0.0143787, 0.408836, 5.28827e-09, 2.26667, 1, 0.0162177, 0.408767, 5.28658e-09, 2.3, 1, 0.0179708, 0.408694, 5.27964e-09, 2.33333, 1, 0.0196273, 0.408618, 5.26808e-09, 2.36667, 1, 0.0211765, 0.408541, 5.2525e-09, 2.4, 1, 0.0226078, 0.408464, 5.23354e-09, 2.43333, 1, 0.0239105, 0.40839, 5.2118e-09, 2.46667, 1, 0.0250741, 0.40832, 5.18794e-09, 2.5, 1, 0.0260877, 0.408256, 5.16256e-09, 2.56667, 1, 0.0276231, 0.408155, 5.10937e-09, 2.83333, 1, 0.0260877, 0.408256, 4.91248e-09, 2.86667, 1, 0.0250741, 0.40832, 4.89068e-09, 2.9, 1, 0.0239106, 0.40839, 4.86887e-09, 2.93333, 1, 0.0226078, 0.408464, 4.84675e-09, 2.96667, 1, 0.0211766, 0.408541, 4.8239e-09, 3, 1, 0.0196273, 0.408618, 4.79997e-09, 3.03333, 1, 0.0179708, 0.408694, 4.77458e-09, 3.06667, 1, 0.0162177, 0.408767, 4.74739e-09, 3.1, 1, 0.0143787, 0.408836, 4.71801e-09, 3.13333, 1, 0.0124644, 0.408899, 4.68618e-09, 3.16667, 1, 0.0104857, 0.408955, 4.65162e-09, 3.2, 1, 0.0084531, 0.409002, 4.61412e-09, 3.26667, 1, 0.00426962, 0.409067, 4.52991e-09, 3.43333, 1, -0.0063775, 0.409039, 4.26937e-09, 3.46667, 1, -0.00845309, 0.409002, 4.21092e-09, 3.5, 1, -0.0104856, 0.408955, 4.15145e-09, 3.53333, 1, -0.0124644, 0.408899, 4.09164e-09, 3.56667, 1, -0.0143787, 0.408836, 4.03216e-09, 3.6, 1, -0.0162177, 0.408767, 3.97382e-09, 3.63333, 1, -0.0179708, 0.408694, 3.9174e-09, 3.66667, 1, -0.0196273, 0.408618, 3.86377e-09, 3.7, 1, -0.0211765, 0.408541, 3.8138e-09, 3.73333, 1, -0.0226078, 0.408464, 3.76836e-09, 3.76667, 1, -0.0239106, 0.40839, 3.72836e-09, 3.8, 1, -0.0250741, 0.40832, 3.69465e-09, 3.83333, 1, -0.0260877, 0.408256, 3.66811e-09, 3.9, 1, -0.0276231, 0.408155, 3.63967e-09, 4.16667, 1, -0.0260877, 0.408256, 3.9182e-09, 4.2, 1, -0.0250741, 0.40832, 3.99193e-09, 4.23333, 1, -0.0239106, 0.40839, 4.07128e-09, 4.26667, 1, -0.0226078, 0.408464, 4.15515e-09, 4.3, 1, -0.0211766, 0.408541, 4.24239e-09, 4.33333, 1, -0.0196273, 0.408618, 4.33187e-09, 4.36667, 1, -0.0179708, 0.408694, 4.42245e-09, 4.4, 1, -0.0162177, 0.408767, 4.51301e-09, 4.43333, 1, -0.0143787, 0.408836, 4.60243e-09, 4.46667, 1, -0.0124644, 0.408899, 4.68965e-09, 4.5, 1, -0.0104857, 0.408955, 4.77363e-09, 4.53333, 1, -0.0084531, 0.409002, 4.85345e-09, 4.6, 1, -0.00426961, 0.409067, 4.99698e-09, 4.76667, 1, 0.0063775, 0.409039, 5.23239e-09, 4.8, 1, 0.0084531, 0.409002, 5.25664e-09, 4.83333, 1, 0.0104857, 0.408955, 5.2738e-09, 4.86667, 1, 0.0124644, 0.408899, 5.28419e-09, 4.9, 1, 0.0143787, 0.408836, 5.28827e-09, 4.93333, 1, 0.0162177, 0.408767, 5.28658e-09, 4.96667, 1, 0.0179708, 0.408694, 5.27964e-09, 5, 1, 0.0196273, 0.408618, 5.26808e-09, 5.03333, 1, 0.0211765, 0.408541, 5.2525e-09, 5.06667, 1, 0.0226078, 0.408464, 5.23354e-09, 5.1, 1, 0.0239105, 0.40839, 5.2118e-09, 5.13333, 1, 0.0250741, 0.40832, 5.18794e-09, 5.16667, 1, 0.0260877, 0.408256, 5.16256e-09, 5.23333, 1, 0.0276231, 0.408155, 5.10937e-09, 5.33333, 1, 0.0285366, 0.408093, 5.03012e-09)
tracks/13/type = "rotation_3d"
tracks/13/imported = true
tracks/13/enabled = true
tracks/13/path = NodePath("RIG-Armature/Skeleton3D:DEF-torso")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = PackedFloat32Array(0, 1, 0.00243444, 0.0697141, -0.0348145, 0.996956, 0.0333333, 1, 0.00251436, 0.0668428, -0.0373003, 0.997063, 0.0666667, 1, 0.00257103, 0.0634879, -0.0395248, 0.997196, 0.1, 1, 0.00260479, 0.0596885, -0.041488, 0.997351, 0.133333, 1, 0.00261637, 0.0554838, -0.0431898, 0.997522, 0.166667, 1, 0.00260669, 0.0509129, -0.0446301, 0.997702, 0.2, 1, 0.0025775, 0.0460152, -0.0458089, 0.997887, 0.233333, 1, 0.00253042, 0.0408298, -0.0467261, 0.99807, 0.266667, 1, 0.00246733, 0.0353961, -0.0473819, 0.998246, 0.3, 1, 0.00239024, 0.0297534, -0.0477762, 0.998412, 0.333333, 1, 0.00230122, 0.0239409, -0.0479091, 0.998562, 0.366667, 1, 0.00220268, 0.0179979, -0.0477807, 0.998693, 0.4, 1, 0.00209659, 0.0119639, -0.0473912, 0.998803, 0.566667, 1, 0.00152554, -0.0181982, -0.041528, 0.99897, 0.6, 1, 0.00141665, -0.0239545, -0.0395726, 0.998929, 0.633333, 1, 0.00131361, -0.0295275, -0.037356, 0.998865, 0.666667, 1, 0.00121795, -0.0348781, -0.0348783, 0.998782, 0.7, 1, 0.00113185, -0.0399717, -0.0321434, 0.998683, 0.733333, 1, 0.00105789, -0.0447909, -0.0291732, 0.99857, 0.766667, 1, 0.000999302, -0.0493229, -0.0259933, 0.998444, 0.8, 1, 0.0009582, -0.0535546, -0.0226299, 0.998308, 0.833333, 1, 0.000936923, -0.0574729, -0.019109, 0.998164, 0.866667, 1, 0.000936423, -0.0610648, -0.0154566, 0.998014, 0.9, 1, 0.000957708, -0.0643171, -0.0116988, 0.99786, 0.933333, 1, 0.00100092, -0.0672167, -0.00786188, 0.997707, 0.966667, 1, 0.00106536, -0.0697504, -0.00397195, 0.997556, 1, 1, 0.00115001, -0.0719049, -5.52572e-05, 0.997411, 1.03333, 1, 0.00125285, -0.0736673, 0.00386199, 0.997275, 1.06667, 1, 0.00137144, -0.0750243, 0.00775352, 0.997151, 1.1, 1, 0.00150277, -0.0759626, 0.0115931, 0.997042, 1.13333, 1, 0.00164331, -0.0764694, 0.0153544, 0.996952, 1.16667, 1, 0.00178915, -0.0765314, 0.0190114, 0.996884, 1.2, 1, 0.00193569, -0.0761356, 0.0225378, 0.996841, 1.26667, 1, 0.00221213, -0.0739184, 0.0290941, 0.996837, 1.3, 1, 0.00233244, -0.072071, 0.0320718, 0.996881, 1.33333, 1, 0.00243451, -0.0697139, 0.0348145, 0.996956, 1.36667, 1, 0.00251426, -0.0668426, 0.0373003, 0.997063, 1.4, 1, 0.0025711, -0.0634877, 0.0395248, 0.997196, 1.43333, 1, 0.00260477, -0.0596883, 0.041488, 0.997351, 1.46667, 1, 0.00261636, -0.0554835, 0.0431898, 0.997522, 1.5, 1, 0.00260668, -0.0509127, 0.0446301, 0.997702, 1.53333, 1, 0.00257757, -0.046015, 0.0458089, 0.997887, 1.56667, 1, 0.00253041, -0.0408296, 0.0467261, 0.99807, 1.6, 1, 0.00246732, -0.0353959, 0.0473819, 0.998246, 1.63333, 1, 0.00239014, -0.0297531, 0.0477762, 0.998412, 1.66667, 1, 0.00230122, -0.0239406, 0.0479091, 0.998562, 1.7, 1, 0.00220268, -0.0179977, 0.0477807, 0.998693, 1.73333, 1, 0.0020965, -0.0119637, 0.0473912, 0.998803, 1.9, 1, 0.00152562, 0.0181984, 0.041528, 0.99897, 1.93333, 1, 0.00141665, 0.0239547, 0.0395725, 0.998929, 1.96667, 1, 0.00131362, 0.0295277, 0.037356, 0.998865, 2, 1, 0.00121796, 0.0348784, 0.0348782, 0.998782, 2.03333, 1, 0.00113186, 0.0399719, 0.0321434, 0.998683, 2.06667, 1, 0.00105799, 0.0447912, 0.0291732, 0.99857, 2.1, 1, 0.00099923, 0.0493232, 0.0259933, 0.998444, 2.13333, 1, 0.000958297, 0.0535548, 0.0226299, 0.998308, 2.16667, 1, 0.00093685, 0.0574731, 0.019109, 0.998164, 2.2, 1, 0.000936437, 0.061065, 0.0154566, 0.998014, 2.23333, 1, 0.000957808, 0.0643173, 0.0116988, 0.99786, 2.26667, 1, 0.00100093, 0.0672169, 0.00786188, 0.997707, 2.3, 1, 0.00106537, 0.0697506, 0.00397195, 0.997556, 2.33333, 1, 0.00114994, 0.0719052, 5.52545e-05, 0.997411, 2.36667, 1, 0.00125287, 0.0736675, -0.00386199, 0.997275, 2.4, 1, 0.00137146, 0.0750245, -0.00775352, 0.997151, 2.43333, 1, 0.00150279, 0.0759629, -0.0115931, 0.997042, 2.46667, 1, 0.00164333, 0.0764696, -0.0153544, 0.996952, 2.5, 1, 0.00178917, 0.0765316, -0.0190114, 0.996884, 2.53333, 1, 0.00193563, 0.0761358, -0.0225378, 0.996841, 2.63333, 1, 0.00233246, 0.0720713, -0.0320718, 0.996881, 2.66667, 1, 0.00243444, 0.0697141, -0.0348145, 0.996956, 2.7, 1, 0.00251436, 0.0668428, -0.0373003, 0.997063, 2.73333, 1, 0.00257103, 0.0634879, -0.0395248, 0.997196, 2.76667, 1, 0.00260479, 0.0596885, -0.041488, 0.997351, 2.8, 1, 0.00261637, 0.0554838, -0.0431898, 0.997522, 2.83333, 1, 0.00260669, 0.0509129, -0.0446301, 0.997702, 2.86667, 1, 0.0025775, 0.0460152, -0.0458089, 0.997887, 2.9, 1, 0.00253042, 0.0408298, -0.0467261, 0.99807, 2.93333, 1, 0.00246733, 0.0353961, -0.0473819, 0.998246, 2.96667, 1, 0.00239024, 0.0297534, -0.0477762, 0.998412, 3, 1, 0.00230122, 0.0239409, -0.0479091, 0.998562, 3.03333, 1, 0.00220268, 0.0179979, -0.0477807, 0.998693, 3.06667, 1, 0.00209659, 0.0119639, -0.0473912, 0.998803, 3.23333, 1, 0.00152554, -0.0181982, -0.041528, 0.99897, 3.26667, 1, 0.00141665, -0.0239545, -0.0395726, 0.998929, 3.3, 1, 0.00131361, -0.0295275, -0.037356, 0.998865, 3.33333, 1, 0.00121795, -0.0348781, -0.0348783, 0.998782, 3.36667, 1, 0.00113185, -0.0399717, -0.0321434, 0.998683, 3.4, 1, 0.00105789, -0.0447909, -0.0291732, 0.99857, 3.43333, 1, 0.000999302, -0.0493229, -0.0259933, 0.998444, 3.46667, 1, 0.0009582, -0.0535546, -0.0226299, 0.998308, 3.5, 1, 0.000936923, -0.0574729, -0.019109, 0.998164, 3.53333, 1, 0.000936423, -0.0610648, -0.0154566, 0.998014, 3.56667, 1, 0.000957708, -0.0643171, -0.0116988, 0.99786, 3.6, 1, 0.00100092, -0.0672167, -0.00786188, 0.997707, 3.63333, 1, 0.00106536, -0.0697504, -0.00397195, 0.997556, 3.66667, 1, 0.00115001, -0.0719049, -5.52572e-05, 0.997411, 3.7, 1, 0.00125285, -0.0736673, 0.00386199, 0.997275, 3.73333, 1, 0.00137144, -0.0750243, 0.00775352, 0.997151, 3.76667, 1, 0.00150277, -0.0759626, 0.0115931, 0.997042, 3.8, 1, 0.00164331, -0.0764694, 0.0153544, 0.996952, 3.83333, 1, 0.00178915, -0.0765314, 0.0190114, 0.996884, 3.86667, 1, 0.00193569, -0.0761356, 0.0225378, 0.996841, 3.93333, 1, 0.00221213, -0.0739184, 0.0290941, 0.996837, 3.96667, 1, 0.00233244, -0.072071, 0.0320718, 0.996881, 4, 1, 0.00243451, -0.0697139, 0.0348145, 0.996956, 4.03333, 1, 0.00251426, -0.0668426, 0.0373003, 0.997063, 4.06667, 1, 0.0025711, -0.0634877, 0.0395248, 0.997196, 4.1, 1, 0.00260477, -0.0596883, 0.041488, 0.997351, 4.13333, 1, 0.00261636, -0.0554835, 0.0431898, 0.997522, 4.16667, 1, 0.00260668, -0.0509127, 0.0446301, 0.997702, 4.2, 1, 0.00257757, -0.046015, 0.0458089, 0.997887, 4.23333, 1, 0.00253041, -0.0408296, 0.0467261, 0.99807, 4.26667, 1, 0.00246732, -0.0353959, 0.0473819, 0.998246, 4.3, 1, 0.00239014, -0.0297531, 0.0477762, 0.998412, 4.33333, 1, 0.00230122, -0.0239406, 0.0479091, 0.998562, 4.36667, 1, 0.00220268, -0.0179977, 0.0477807, 0.998693, 4.4, 1, 0.0020965, -0.0119637, 0.0473912, 0.998803, 4.56667, 1, 0.00152562, 0.0181984, 0.041528, 0.99897, 4.6, 1, 0.00141665, 0.0239547, 0.0395725, 0.998929, 4.63333, 1, 0.00131362, 0.0295277, 0.037356, 0.998865, 4.66667, 1, 0.00121796, 0.0348784, 0.0348782, 0.998782, 4.7, 1, 0.00113186, 0.0399719, 0.0321434, 0.998683, 4.73333, 1, 0.00105799, 0.0447912, 0.0291732, 0.99857, 4.76667, 1, 0.00099923, 0.0493232, 0.0259933, 0.998444, 4.8, 1, 0.000958297, 0.0535548, 0.0226299, 0.998308, 4.83333, 1, 0.00093685, 0.0574731, 0.019109, 0.998164, 4.86667, 1, 0.000936437, 0.061065, 0.0154566, 0.998014, 4.9, 1, 0.000957808, 0.0643173, 0.0116988, 0.99786, 4.93333, 1, 0.00100093, 0.0672169, 0.00786188, 0.997707, 4.96667, 1, 0.00106537, 0.0697506, 0.00397195, 0.997556, 5, 1, 0.00114994, 0.0719052, 5.52545e-05, 0.997411, 5.03333, 1, 0.00125287, 0.0736675, -0.00386199, 0.997275, 5.06667, 1, 0.00137146, 0.0750245, -0.00775352, 0.997151, 5.1, 1, 0.00150279, 0.0759629, -0.0115931, 0.997042, 5.13333, 1, 0.00164333, 0.0764696, -0.0153544, 0.996952, 5.16667, 1, 0.00178917, 0.0765316, -0.0190114, 0.996884, 5.2, 1, 0.00193563, 0.0761358, -0.0225378, 0.996841, 5.3, 1, 0.00233246, 0.0720713, -0.0320718, 0.996881, 5.33333, 1, 0.00243444, 0.0697141, -0.0348145, 0.996956)
tracks/14/type = "rotation_3d"
tracks/14/imported = true
tracks/14/enabled = true
tracks/14/path = NodePath("RIG-Armature/Skeleton3D:DEF-leg")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = PackedFloat32Array(0, 1, 1.34309e-07, -5.26836e-09, -0.0348995, 0.999391, 0.0666667, 1, -0.000179377, -0.00521543, -0.0343932, 0.999395, 0.2, 1, -0.000467012, -0.015229, -0.0306571, 0.999414, 0.5, 1, -0.000408663, -0.0318988, -0.0128104, 0.999409, 0.566667, 1, -0.000263176, -0.0337799, -0.00779057, 0.999399, 0.8, 1, 0.000340616, -0.0329441, 0.0103265, 0.999404, 0.833333, 1, 0.00040907, -0.0318988, 0.0128104, 0.999409, 1.36667, 1, -9.06195e-05, 0.0026142, 0.0347707, 0.999392, 1.43333, 1, -0.000263177, 0.00779053, 0.0337799, 0.999399, 1.53333, 1, -0.000467014, 0.0152289, 0.0306571, 0.999414, 1.83333, 1, -0.000408664, 0.0318987, 0.0128104, 0.999409, 1.86667, 1, -0.000340141, 0.0329441, 0.0103265, 0.999404, 1.93333, 1, -0.000179378, 0.0343932, 0.00521541, 0.999395, 2.13333, 1, 0.000340616, 0.0329441, -0.0103266, 0.999404, 2.16667, 1, 0.000409069, 0.0318988, -0.0128104, 0.999409, 2.6, 1, 0.000179629, 0.00521542, -0.0343932, 0.999395, 2.73333, 1, -0.000179377, -0.00521543, -0.0343932, 0.999395, 2.86667, 1, -0.000467012, -0.015229, -0.0306571, 0.999414, 3.16667, 1, -0.000408663, -0.0318988, -0.0128104, 0.999409, 3.23333, 1, -0.000263176, -0.0337799, -0.00779057, 0.999399, 3.46667, 1, 0.000340616, -0.0329441, 0.0103265, 0.999404, 3.5, 1, 0.00040907, -0.0318988, 0.0128104, 0.999409, 4.03333, 1, -9.06195e-05, 0.0026142, 0.0347707, 0.999392, 4.1, 1, -0.000263177, 0.00779053, 0.0337799, 0.999399, 4.2, 1, -0.000467014, 0.0152289, 0.0306571, 0.999414, 4.5, 1, -0.000408664, 0.0318987, 0.0128104, 0.999409, 4.53333, 1, -0.000340141, 0.0329441, 0.0103265, 0.999404, 4.6, 1, -0.000179378, 0.0343932, 0.00521541, 0.999395, 4.8, 1, 0.000340616, 0.0329441, -0.0103266, 0.999404, 4.83333, 1, 0.000409069, 0.0318988, -0.0128104, 0.999409, 5.26667, 1, 0.000179629, 0.00521542, -0.0343932, 0.999395, 5.33333, 1, 1.34309e-07, -5.26836e-09, -0.0348995, 0.999391)

[sub_resource type="AnimationLibrary" id="AnimationLibrary_d2sws"]
_data = {
"idle": SubResource("Animation_d556y")
}

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_5axn0"]
animation = &"idle"

[sub_resource type="AnimationNodeBlendTree" id="AnimationNodeBlendTree_mc74q"]
graph_offset = Vector2(-125.539, 89.6822)
nodes/Animation/node = SubResource("AnimationNodeAnimation_5axn0")
nodes/Animation/position = Vector2(-51, 107)
nodes/output/position = Vector2(360, 160)
node_connections = [&"output", 0, &"Animation"]

[node name="DummySkin" type="Node3D"]
script = ExtResource("1_uvklm")

[node name="dummy" parent="." instance=ExtResource("1_rr1a5")]
unique_name_in_owner = true

[node name="Skeleton3D" parent="dummy/RIG-Armature" index="0"]
bones/0/position = Vector3(0.0828972, 1.07378, -0.215362)
bones/0/rotation = Quaternion(-0.379382, 0.0328937, -0.0227698, 0.924375)
bones/1/rotation = Quaternion(-9.00126e-08, 1.94847e-09, 0.052035, 0.998645)
bones/2/rotation = Quaternion(9.77264e-08, -9.53959e-09, 0.0659845, 0.997821)
bones/3/position = Vector3(0.0324013, 0.623783, 0.000805042)
bones/3/rotation = Quaternion(0.0018776, 0.000269021, -0.0560226, 0.998428)
bones/4/position = Vector3(0.012612, 0.408894, 5.96734e-08)
bones/4/rotation = Quaternion(0.989276, -0.0451961, -0.138649, -0.00822005)
bones/5/position = Vector3(0.148973, 0.547754, 0.000479867)
bones/5/rotation = Quaternion(-0.488455, -0.512867, -0.45929, 0.536126)
bones/6/position = Vector3(-0.0960948, 0.570322, 0.000645158)
bones/6/rotation = Quaternion(-0.399715, 0.584809, 0.429289, 0.560301)
bones/7/position = Vector3(0.012612, 0.408894, 4.68863e-09)
bones/7/rotation = Quaternion(0.00187718, 0.000364016, -0.0451413, 0.998979)
bones/8/rotation = Quaternion(-0.00041566, -0.0299045, -0.0149469, 0.999441)

[node name="dummy2" parent="dummy/RIG-Armature/Skeleton3D" index="0"]
material_override = ExtResource("3_wu7ut")

[node name="AnimationTree" type="AnimationTree" parent="."]
root_node = NodePath("%dummy")
libraries = {
"": SubResource("AnimationLibrary_d2sws")
}
tree_root = SubResource("AnimationNodeBlendTree_mc74q")
anim_player = NodePath("../dummy/AnimationPlayer")

[editable path="dummy"]
