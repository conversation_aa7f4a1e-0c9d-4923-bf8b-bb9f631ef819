[gd_scene load_steps=12 format=3 uid="uid://cs8ncblbej60o"]

[ext_resource type="Script" path="res://scripts/option/confirmation_screen_popup.gd" id="1_edtsk"]
[ext_resource type="Shader" path="res://blur.gdshader" id="2_56nk3"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_gvkuy"]
shader = ExtResource("2_56nk3")
shader_parameter/blur = 1.0
shader_parameter/brightness = 0.065

[sub_resource type="Gradient" id="Gradient_41vlh"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0, 0, 0, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_1254l"]
gradient = SubResource("Gradient_41vlh")

[sub_resource type="CanvasTexture" id="CanvasTexture_xcg1u"]
diffuse_texture = SubResource("GradientTexture1D_1254l")

[sub_resource type="Gradient" id="Gradient_4taag"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0.147672, 0.147672, 0.147672, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_xglr7"]
gradient = SubResource("Gradient_4taag")

[sub_resource type="Gradient" id="Gradient_eleys"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0.400698, 0.400698, 0.400698, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_d8hm2"]
gradient = SubResource("Gradient_eleys")

[sub_resource type="LabelSettings" id="LabelSettings_cplhe"]
font_size = 51

[node name="ConfirmationScreen" type="Control"]
process_mode = 3
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
size_flags_vertical = 4
script = ExtResource("1_edtsk")
metadata/_edit_lock_ = true

[node name="BlurBG" type="TextureRect" parent="."]
material = SubResource("ShaderMaterial_gvkuy")
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = -1.92
offset_right = 1918.08
offset_bottom = 1080.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("CanvasTexture_xcg1u")

[node name="BGBorder" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 571.08
offset_top = 401.0
offset_right = 1346.08
offset_bottom = 681.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_xglr7")

[node name="BG" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 610.08
offset_top = 429.0
offset_right = 1307.08
offset_bottom = 653.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_d8hm2")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 2
offset_left = 574.0
offset_top = 320.0
offset_right = 1346.0
offset_bottom = 760.0
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_constants/separation = 50
alignment = 1

[node name="PopupLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Return to Title Screen?"
label_settings = SubResource("LabelSettings_cplhe")
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 3

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 105
alignment = 1

[node name="ConfirmButton" type="Button" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "Confirm"

[node name="CancelButton" type="Button" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "Cancel"

[node name="CloseButton" type="Button" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 334.0
offset_top = -671.0
offset_right = 378.0
offset_bottom = -626.0
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(0.94, 0.94)
theme_override_font_sizes/font_size = 30
text = "X"

[connection signal="pressed" from="VBoxContainer/HBoxContainer/ConfirmButton" to="." method="_on_confirm_button_pressed"]
[connection signal="pressed" from="VBoxContainer/HBoxContainer/CancelButton" to="." method="_on_cancel_button_pressed"]
[connection signal="pressed" from="CloseButton" to="." method="_on_close_button_pressed"]
