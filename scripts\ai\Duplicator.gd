class_name Duplicator

static func duplicateCard(card: Card) -> PlayCardData:
    var playerID = 1 if card.ownedPlayer.isOnMainSide() else 2 # playerID is 1 for main side and 2 for opponent
    return PlayCardData.new(
        playerID,
        card.color,
        card.name,
        card.rarity,
        card.cost,
        card.power,
        card.castCondition,
        card.abilities,
        card.givingAbilities
    )

static func duplicatePlayer(player: Player) -> PlayerData:
    var playerData = PlayerData.new()
    playerData.playerID = player.playerID
    playerData.name = player.name
    playerData.isOnMainSide = player.isOnMainSide
    playerData.hand = CardCollectionData.new(playerData)
    for card in player.hand.cardCollection:
        playerData.hand.cards.append(duplicateCard(card))
    playerData.deck = CardCollectionData.new(playerData)
    # for card in player.deck.cardCollection:
    #     playerData.deck.cards.append(duplicateCard(card))
    # playerData.graveyard = CardCollectionData.new(playerData)
    for card in player.graveyard.cardCollection:
        playerData.graveyard.cards.append(duplicateCard(card))
    return playerData

static func duplicateLaneCardSlot(_lcs: LaneCardSlot, _aiplayer: PlayerData, _oppoPlayer: PlayerData) -> CardCollectionData:
    var result = CardCollectionData.new(_aiplayer if _lcs.getOwner().isOnMainSide() == _aiplayer.onMainSide else _oppoPlayer)
    result.state = _lcs.state
    result.slot_lane_index = _lcs.lane_index
    for card in _lcs.cardCollection:
        result.cards.append(duplicateCard(card))
    return result

static func duplicateLane(_lane: Lane,_land_index:int, _aiplayer: PlayerData, _oppoPlayer: PlayerData) -> Array[CardCollectionData]:
    var result = []
    for cardSlot in _lane.cardSlots:
        result.append(duplicateLaneCardSlot(cardSlot, _aiplayer, _oppoPlayer))
    return result