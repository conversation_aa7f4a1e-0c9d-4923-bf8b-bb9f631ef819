[gd_resource type="VisualShader" load_steps=7 format=3 uid="uid://dmb18tjtgw5aq"]

[ext_resource type="Texture2D" uid="uid://6xflbkhvbqi" path="res://Assets/Particle/InlaneVfx3.png" id="1_ibde8"]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_20oul"]
expanded_output_ports = [0]
input_name = "color"

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_vhx2j"]
expanded_output_ports = [0]
texture = ExtResource("1_ibde8")

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_pjmn3"]
operator = 2

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_10tyj"]
operator = 2

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_oljy5"]
expanded_output_ports = [0]
texture = ExtResource("1_ibde8")

[resource]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_back, diffuse_lambert, specular_schlick_ggx, unshaded;

uniform sampler2D tex_frg_6;
uniform sampler2D tex_frg_3;



void fragment() {
// Input:2
	vec4 n_out2p0 = COLOR;
	float n_out2p4 = n_out2p0.a;


// Texture2D:6
	vec4 n_out6p0 = texture(tex_frg_6, UV);


// Texture2D:3
	vec4 n_out3p0 = texture(tex_frg_3, UV);


// FloatOp:4
	float n_out4p0 = n_out3p0.x * n_out2p4;


// FloatOp:5
	float n_out5p0 = n_out6p0.x * n_out4p0;


// Output:0
	ALBEDO = vec3(n_out2p0.xyz);
	ALPHA = n_out5p0;


}
"
flags/unshaded = true
nodes/fragment/0/position = Vector2(780, 160)
nodes/fragment/2/node = SubResource("VisualShaderNodeInput_20oul")
nodes/fragment/2/position = Vector2(-100, 100)
nodes/fragment/3/node = SubResource("VisualShaderNodeTexture_vhx2j")
nodes/fragment/3/position = Vector2(-120, 340)
nodes/fragment/4/node = SubResource("VisualShaderNodeFloatOp_pjmn3")
nodes/fragment/4/position = Vector2(200, 380)
nodes/fragment/5/node = SubResource("VisualShaderNodeFloatOp_10tyj")
nodes/fragment/5/position = Vector2(540, 500)
nodes/fragment/6/node = SubResource("VisualShaderNodeTexture_oljy5")
nodes/fragment/6/position = Vector2(240, 560)
nodes/fragment/connections = PackedInt32Array(2, 0, 0, 0, 2, 4, 4, 1, 3, 0, 4, 0, 6, 0, 5, 0, 4, 0, 5, 1, 5, 0, 0, 1)
