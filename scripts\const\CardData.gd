extends Object
class_name CardData

# CardData is a singleton that stores all the card data in the game.
enum {
	Slime1C,
	Slime2C,
	Slime3C,
	Slime4C,
	Slime5C,
	Slime6C,
	Slime1R,
	Slime2R,
	Slime3R,
	Slime4R,
	Slime5R,
	Slime6R,
	Slime1E,
	Slime2E,
	Slime3E,
	Slime4E,
	Slime5E,
	Slime6E,
	Slime1L,
	Slime2L,
	Slime3L,
	Slime4L,
	Slime5L,
	Slime6L,
	MudGolem,
	ClayGolem,
	EarthGolem,
	Terrik,
	RockGolem,
	StoneGolem,
	GraniteGolem,
	Obrelith,
	WoodGolem,
	BirchGolem,
	AlderGolem,
	Oakenheart,
	Entling,
	Ent,
	GrownEnt,
	Ironbark,
	Deeproot,
	ElderDeeproot,
	AncientDeeproot,
	LordDeeproot,
	Funglow,
	MagicFunglow,
	MysticFunglow,
	Fynori,
	Shroom,
	Lightshroom,
	Sunshroom,
	Mycora,
	YoungDryad,
	BloomingDryad,
	Sylvara,
	Fairy,
	HighFairy,
	Faelith,
	Vineling,
	SpiralVineling,
	Thornwick,
	Roseguard,
	EliteRoseguard,
	GreenMage,
	AldriontheGreen,
	EntSpirit,
	ElderEntSpirit,
	Elyndra,
	Runescar,
	Elderbloom,
	Myrthalia,
	Terragon,
	Butterfire,
	NightButterfire,
	HellButterfire,
	Flarefire,
	Bomberkid,
	Bomber,
	BigBomber,
	Boomgobb,
	Impling,
	Imp,
	PitImp,
	Kravveth,
	FireKnight,
	BlazeKnight,
	DragonKnight,
	Titus,
	Flinger,
	EliteFlinger,
	BossFlinger,
	ZarnaktheFling,
	YoungSuccubus,
	Succubus,
	HighSuccubus,
	LadyLamia,
	Larvaling,
	Larva,
	DeepLarva,
	AncientLarva,
	FireElemental,
	MagmaElemental,
	LordMalfyris,
	Redwings,
	GrandRedwings,
	Heliora,
	Flamecoat,
	DarkFlamecoat,
	Apocalypse,
	RedMage,
	EmberistheRed,
	Explodus,
	ExplodusII,
	Fireshadow,
	Evilshadow,
	Nymora,
	Grashnak,
	KingKrotos,
	Ignix,
	Pyragon,
	Siren,
	EliteSiren,
	QueenSiren,
	Calyra,
	Shelling,
	Shell,
	MagicShell,
	Pearind,
	Mimicling,
	Mimic,
	FangedMimic,
	TheUnhollow,
	Mermage,
	DeepMermage,
	HighMermage,
	Tydros,
	Angler,
	PearlAngler,
	GrandAngler,
	Lurkgloom,
	IceGolem,
	CrystalGolem,
	DiamondGolem,
	Frostbane,
	Metaling,
	Metamore,
	Metamonster,
	Murkendral,
	Silencer,
	EliteSilencer,
	Caelith,
	WaterElemental,
	TideElemental,
	Ocevaris,
	SnowFairy,
	BlizzardFairy,
	Eirwyn,
	Levisia,
	SkyLevisia,
	Trine,
	MasterTrine,
	BlueMage,
	ElyndriatheBlue,
	Phantarion,
	Leviathan,
	Tidecaller,
	SeaSovereign,
	Aquagon
}

static func getCardByName(cardName: int):
	return StandardCard[cardName]

static var StandardCard = {
    Slime1C: {
        "name": "Slime 1C",
        "id":Slime1C,
        "color":CardColor.GREEN,
        "cost": 1,
        "power": 2,
        "rarity":Rarity.COMMON,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 1C.png"
    },
    Slime2C: {
        "name": "Slime 2C",
        "id":Slime2C,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 3,
        "rarity":Rarity.COMMON,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 2C.png"
    },
    Slime3C: {
        "name": "Slime 3C",
        "id":Slime3C,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 4,
        "rarity":Rarity.COMMON,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 3C.png"
    },
    Slime4C: {
        "name": "Slime 4C",
        "id":Slime4C,
        "color":CardColor.GREEN,
        "cost": 4,
        "power": 6,
        "rarity":Rarity.COMMON,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 4C.png"
    },
    Slime5C: {
        "name": "Slime 5C",
        "id":Slime5C,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 7,
        "rarity":Rarity.COMMON,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 5C.png"
    },
    Slime6C: {
        "name": "Slime 6C",
        "id":Slime6C,
        "color":CardColor.GREEN,
        "cost": 6,
        "power": 8,
        "rarity":Rarity.COMMON,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 6C.png"
    },
    Slime1R: {
        "name": "Slime 1R",
        "id":Slime1R,
        "color":CardColor.GREEN,
        "cost": 1,
        "power": 3,
        "rarity":Rarity.RARE,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 1R.png"
    },
    Slime2R: {
        "name": "Slime 2R",
        "id":Slime2R,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 4,
        "rarity":Rarity.RARE,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 2R.png"
    },
    Slime3R: {
        "name": "Slime 3R",
        "id":Slime3R,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 6,
        "rarity":Rarity.RARE,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 3R.png"
    },
    Slime4R: {
        "name": "Slime 4R",
        "id":Slime4R,
        "color":CardColor.GREEN,
        "cost": 4,
        "power": 7,
        "rarity":Rarity.RARE,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 4R.png"
    },
    Slime5R: {
        "name": "Slime 5R",
        "id":Slime5R,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 8,
        "rarity":Rarity.RARE,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 5R.png"
    },
    Slime6R: {
        "name": "Slime 6R",
        "id":Slime6R,
        "color":CardColor.GREEN,
        "cost": 6,
        "power": 9,
        "rarity":Rarity.RARE,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 6R.png"
    },
    Slime1E: {
        "name": "Slime 1E",
        "id":Slime1E,
        "color":CardColor.GREEN,
        "cost": 1,
        "power": 4,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 1E.png"
    },
    Slime2E: {
        "name": "Slime 2E",
        "id":Slime2E,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 6,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 2E.png"
    },
    Slime3E: {
        "name": "Slime 3E",
        "id":Slime3E,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 7,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 3E.png"
    },
    Slime4E: {
        "name": "Slime 4E",
        "id":Slime4E,
        "color":CardColor.GREEN,
        "cost": 4,
        "power": 8,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 4E.png"
    },
    Slime5E: {
        "name": "Slime 5E",
        "id":Slime5E,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 9,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 5E.png"
    },
    Slime6E: {
        "name": "Slime 6E",
        "id":Slime6E,
        "color":CardColor.GREEN,
        "cost": 6,
        "power": 10,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 6E.png"
    },
    Slime1L: {
        "name": "Slime 1L",
        "id":Slime1L,
        "color":CardColor.GREEN,
        "cost": 1,
        "power": 6,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 1L.png"
    },
    Slime2L: {
        "name": "Slime 2L",
        "id":Slime2L,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 7,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 2L.png"
    },
    Slime3L: {
        "name": "Slime 3L",
        "id":Slime3L,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 8,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 3L.png"
    },
    Slime4L: {
        "name": "Slime 4L",
        "id":Slime4L,
        "color":CardColor.GREEN,
        "cost": 4,
        "power": 9,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 4L.png"
    },
    Slime5L: {
        "name": "Slime 5L",
        "id":Slime5L,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 10,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 5L.png"
    },
    Slime6L: {
        "name": "Slime 6L",
        "id":Slime6L,
        "color":CardColor.GREEN,
        "cost": 6,
        "power": 11,
        "rarity":Rarity.EPIC,
        "description": "",
        "image": "res://Assets/CardPics2/Slime 6L.png"
    },
    MudGolem: {
        "name": "Mud Golem",
        "id":MudGolem,
        "color":CardColor.GREEN,
        "cost": 1,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "Passive: If you have a 6-cost ally here, +2 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 18,
                    "conditionTier": 6
                },
                "keyword": {
                    "name": 5,
                    "tier": 2
                }
            }
        ],
        "description": "Passive: If you have a 6-cost ally here, +2 Power.",
        "image": "res://Assets/CardPics2/Mud Golem.png"
    },
    ClayGolem: {
        "name": "Clay Golem",
        "id":ClayGolem,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 2,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "Passive: If you have a 6-cost ally here, +3 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 18,
                    "conditionTier": 6
                },
                "keyword": {
                    "name": 5,
                    "tier": 3
                }
            }
        ],
        "description": "Passive: If you have a 6-cost ally here, +3 Power.",
        "image": "res://Assets/CardPics2/Clay Golem.png"
    },
    EarthGolem: {
        "name": "Earth Golem",
        "id":EarthGolem,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: If you have a 6-cost ally here, +4 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 18,
                    "conditionTier": 6
                },
                "keyword": {
                    "name": 5,
                    "tier": 4
                }
            }
        ],
        "description": "Passive: If you have a 6-cost ally here, +4 Power.",
        "image": "res://Assets/CardPics2/Earth Golem.png"
    },
    Terrik: {
        "name": "Terrik",
        "id":Terrik,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 5,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: If you have a 6-cost ally here, +5 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 18,
                    "conditionTier": 6
                },
                "keyword": {
                    "name": 5,
                    "tier": 5
                }
            }
        ],
        "description": "Passive: If you have a 6-cost ally here, +5 Power.",
        "image": "res://Assets/CardPics2/Terrik.png"
    },
    RockGolem: {
        "name": "Rock Golem",
        "id":RockGolem,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "Passive: If you have less cards here, +4 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 17
                },
                "keyword": {
                    "name": 5,
                    "tier": 4
                }
            }
        ],
        "description": "Passive: If you have less cards here, +4 Power.",
        "image": "res://Assets/CardPics2/Rock Golem.png"
    },
    StoneGolem: {
        "name": "Stone Golem",
        "id":StoneGolem,
        "color":CardColor.GREEN,
        "cost": 4,
        "power": 3,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "Passive: If you have less cards here, +5 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 17
                },
                "keyword": {
                    "name": 5,
                    "tier": 5
                }
            }
        ],
        "description": "Passive: If you have less cards here, +5 Power.",
        "image": "res://Assets/CardPics2/Stone Golem.png"
    },
    GraniteGolem: {
        "name": "Granite Golem",
        "id":GraniteGolem,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: If you have less cards here, +7 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 17
                },
                "keyword": {
                    "name": 5,
                    "tier": 7
                }
            }
        ],
        "description": "Passive: If you have less cards here, +7 Power.",
        "image": "res://Assets/CardPics2/Granite Golem.png"
    },
    Obrelith: {
        "name": "Obrelith",
        "id":Obrelith,
        "color":CardColor.GREEN,
        "cost": 6,
        "power": 5,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: If you have less cards here, +9 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 17
                },
                "keyword": {
                    "name": 5,
                    "tier": 9
                }
            }
        ],
        "description": "Passive: If you have less cards here, +9 Power.",
        "image": "res://Assets/CardPics2/Obrelith.png"
    },
    WoodGolem: {
        "name": "Wood Golem",
        "id":WoodGolem,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "Passive: If hosted by Symbiont, +2 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 20
                },
                "keyword": {
                    "name": 5,
                    "tier": 2
                }
            }
        ],
        "description": "Passive: If hosted by Symbiont, +2 Power.",
        "image": "res://Assets/CardPics2/Wood Golem.png"
    },
    BirchGolem: {
        "name": "Birch Golem",
        "id":BirchGolem,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 2,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "Passive: If hosted by Symbiont, +3 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 20
                },
                "keyword": {
                    "name": 5,
                    "tier": 3
                }
            }
        ],
        "description": "Passive: If hosted by Symbiont, +3 Power.",
        "image": "res://Assets/CardPics2/Birch Golem.png"
    },
    AlderGolem: {
        "name": "Alder Golem",
        "id":AlderGolem,
        "color":CardColor.GREEN,
        "cost": 4,
        "power": 3,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: If hosted by Symbiont, +4 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 20
                },
                "keyword": {
                    "name": 5,
                    "tier": 4
                }
            }
        ],
        "description": "Passive: If hosted by Symbiont, +4 Power.",
        "image": "res://Assets/CardPics2/Alder Golem.png"
    },
    Oakenheart: {
        "name": "Oakenheart",
        "id":Oakenheart,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: If hosted by Symbiont, +5 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 20
                },
                "keyword": {
                    "name": 5,
                    "tier": 5
                }
            }
        ],
        "description": "Passive: If hosted by Symbiont, +5 Power.",
        "image": "res://Assets/CardPics2/Oakenheart.png"
    },
    Entling: {
        "name": "Entling",
        "id":Entling,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "Passive: +1 Power for each ally here.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 1
                },
                "target": {
                    "name": 9
                }
            }
        ],
        "description": "Passive: +1 Power for each ally here.",
        "image": "res://Assets/CardPics2/Entling.png"
    },
    Ent: {
        "name": "Ent",
        "id":Ent,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 2,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "Passive: +1 Power for each ally here.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 1
                },
                "target": {
                    "name": 9
                }
            }
        ],
        "description": "Passive: +1 Power for each ally here.",
        "image": "res://Assets/CardPics2/Ent.png"
    },
    GrownEnt: {
        "name": "Grown Ent",
        "id":GrownEnt,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 3,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: +2 Power for each ally here.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 2
                },
                "target": {
                    "name": 9
                }
            }
        ],
        "description": "Passive: +2 Power for each ally here.",
        "image": "res://Assets/CardPics2/Grown Ent.png"
    },
    Ironbark: {
        "name": "Ironbark",
        "id":Ironbark,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 3,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: +3 Power for each ally here.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 3
                },
                "target": {
                    "name": 9
                }
            }
        ],
        "description": "Passive: +3 Power for each ally here.",
        "image": "res://Assets/CardPics2/Ironbark.png"
    },
    Deeproot: {
        "name": "Deeproot",
        "id":Deeproot,
        "color":CardColor.GREEN,
        "cost": 4,
        "power": 4,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "Thorn 1.",
                "trigger": {
                    "name": 10
                },
                "keyword": {
                    "name": 8,
                    "tier": 1
                },
                "target": {
                    "name": 18
                }
            }
        ],
        "description": "Thorn 1.",
        "image": "res://Assets/CardPics2/Deeproot.png"
    },
    ElderDeeproot: {
        "name": "Elder Deeproot",
        "id":ElderDeeproot,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 5,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "Thorn 3.",
                "trigger": {
                    "name": 10
                },
                "keyword": {
                    "name": 8,
                    "tier": 3
                },
                "target": {
                    "name": 18
                }
            }
        ],
        "description": "Thorn 3.",
        "image": "res://Assets/CardPics2/Elder Deeproot.png"
    },
    AncientDeeproot: {
        "name": "Ancient Deeproot",
        "id":AncientDeeproot,
        "color":CardColor.GREEN,
        "cost": 6,
        "power": 7,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Thorn 5.",
                "trigger": {
                    "name": 10
                },
                "keyword": {
                    "name": 8,
                    "tier": 5
                },
                "target": {
                    "name": 18
                }
            }
        ],
        "description": "Thorn 5.",
        "image": "res://Assets/CardPics2/Ancient Deeproot.png"
    },
    LordDeeproot: {
        "name": "Lord Deeproot",
        "id":LordDeeproot,
        "color":CardColor.GREEN,
        "cost": 6,
        "power": 8,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Thorn 8.",
                "trigger": {
                    "name": 10
                },
                "keyword": {
                    "name": 8,
                    "tier": 8
                },
                "target": {
                    "name": 18
                }
            }
        ],
        "description": "Thorn 8.",
        "image": "res://Assets/CardPics2/Lord Deeproot.png"
    },
    Funglow: {
        "name": "Funglow",
        "id":Funglow,
        "color":CardColor.GREEN,
        "cost": 1,
        "power": 1,
        "rarity":Rarity.COMMON,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Give +2 Power to a host.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 2
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "description": "On enter: Give +2 Power to a host.",
        "image": "res://Assets/CardPics2/Funglow.png"
    },
    MagicFunglow: {
        "name": "Magic Funglow",
        "id":MagicFunglow,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.RARE,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Give +3 Power to a host.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 3
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "description": "On enter: Give +3 Power to a host.",
        "image": "res://Assets/CardPics2/Magic Funglow.png"
    },
    MysticFunglow: {
        "name": "Mystic Funglow",
        "id":MysticFunglow,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 1,
        "rarity":Rarity.EPIC,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Give +5 Power to a host.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 5
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "description": "On enter: Give +5 Power to a host.",
        "image": "res://Assets/CardPics2/Mystic Funglow.png"
    },
    Fynori: {
        "name": "Fynori",
        "id":Fynori,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 1,
        "rarity":Rarity.EPIC,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Give power to a host equal to the strongest ally power.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "dynamicTier": 3
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "description": "On enter: Give power to a host equal to the strongest ally power.",
        "image": "res://Assets/CardPics2/Fynori.png"
    },
    Shroom: {
        "name": "Shroom",
        "id":Shroom,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.COMMON,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Heal a host,",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 1
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "description": "On enter: Heal a host,",
        "image": "res://Assets/CardPics2/Shroom.png"
    },
    Lightshroom: {
        "name": "Lightshroom",
        "id":Lightshroom,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 1,
        "rarity":Rarity.RARE,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Heal a host,",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 1
                },
                "target": {
                    "name": 2
                }
            },
            {
                "text": "then give +2 Power to a host",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 2
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "givingAbilities": [
            {
                "text": "[Healed]",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 7
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "On enter: Heal a host, then give +2 Power to a host",
        "image": "res://Assets/CardPics2/Lightshroom.png"
    },
    Sunshroom: {
        "name": "Sunshroom",
        "id":Sunshroom,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 1,
        "rarity":Rarity.EPIC,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Heal a host,",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 1
                },
                "target": {
                    "name": 2
                }
            },
            {
                "text": "then give +4 Power to a host",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 4
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "givingAbilities": [
            {
                "text": "[Healed]",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 7
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "On enter: Heal a host, then give +4 Power to a host",
        "image": "res://Assets/CardPics2/Sunshroom.png"
    },
    Mycora: {
        "name": "Mycora",
        "id":Mycora,
        "color":CardColor.GREEN,
        "cost": 4,
        "power": 1,
        "rarity":Rarity.EPIC,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Heal a host,",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 1
                },
                "target": {
                    "name": 2
                }
            },
            {
                "text": "then give +1 Power to a host at the end of each turn",
                "trigger": {
                    "name": 6
                },
                "keyword": {
                    "name": 4,
                    "tier": 1
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "givingAbilities": [
            {
                "text": "[Healed]",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 7
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "On enter: Heal a host, then give +1 Power to a host at the end of each turn",
        "image": "res://Assets/CardPics2/Mycora.png"
    },
    YoungDryad: {
        "name": "Young Dryad",
        "id":YoungDryad,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 1,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "Passive: +1 Power for each ally.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 1
                },
                "target": {
                    "name": 15
                }
            }
        ],
        "description": "Passive: +1 Power for each ally.",
        "image": "res://Assets/CardPics2/Young Dryad.png"
    },
    BloomingDryad: {
        "name": "Blooming Dryad",
        "id":BloomingDryad,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 3,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: +1 Power for each ally.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 1
                },
                "target": {
                    "name": 15
                }
            }
        ],
        "description": "Passive: +1 Power for each ally.",
        "image": "res://Assets/CardPics2/Blooming Dryad.png"
    },
    Sylvara: {
        "name": "Sylvara",
        "id":Sylvara,
        "color":CardColor.GREEN,
        "cost": 6,
        "power": 1,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: +2 Power for each ally.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 2
                },
                "target": {
                    "name": 15
                }
            }
        ],
        "description": "Passive: +2 Power for each ally.",
        "image": "res://Assets/CardPics2/Sylvara.png"
    },
    Fairy: {
        "name": "Fairy",
        "id":Fairy,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 1,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On enter: Gain 1 Permanent mana.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 2,
                    "tier": 1
                }
            }
        ],
        "description": "On enter: Gain 1 Permanent mana.",
        "image": "res://Assets/CardPics2/Fairy.png"
    },
    HighFairy: {
        "name": "High Fairy",
        "id":HighFairy,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Gain 1 Permanent mana.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 2,
                    "tier": 1
                }
            }
        ],
        "description": "On enter: Gain 1 Permanent mana.",
        "image": "res://Assets/CardPics2/High Fairy.png"
    },
    Faelith: {
        "name": "Faelith",
        "id":Faelith,
        "color":CardColor.GREEN,
        "cost": 1,
        "power": 1,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Gain 1 Permanent mana.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 2,
                    "tier": 1
                }
            }
        ],
        "description": "On enter: Gain 1 Permanent mana.",
        "image": "res://Assets/CardPics2/Faelith.png"
    },
    Vineling: {
        "name": "Vineling",
        "id":Vineling,
        "color":CardColor.GREEN,
        "cost": 1,
        "power": 1,
        "rarity":Rarity.RARE,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Host get Thorn 2",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 1
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "givingAbilities": [
            {
                "text": "Thorn 2",
                "trigger": {
                    "name": 10
                },
                "keyword": {
                    "name": 8,
                    "tier": 2
                },
                "target": {
                    "name": 18
                }
            }
        ],
        "description": "On enter: Host get Thorn 2",
        "image": "res://Assets/CardPics2/Vineling.png"
    },
    SpiralVineling: {
        "name": "Spiral Vineling",
        "id":SpiralVineling,
        "color":CardColor.GREEN,
        "cost": 1,
        "power": 1,
        "rarity":Rarity.EPIC,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Host get Thorn 3",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 1
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "givingAbilities": [
            {
                "text": "Thorn 3",
                "trigger": {
                    "name": 10
                },
                "keyword": {
                    "name": 8,
                    "tier": 3
                },
                "target": {
                    "name": 18
                }
            }
        ],
        "description": "On enter: Host get Thorn 3",
        "image": "res://Assets/CardPics2/Spiral Vineling.png"
    },
    Thornwick: {
        "name": "Thornwick",
        "id":Thornwick,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.EPIC,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Host get Thorn 5",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 1
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "givingAbilities": [
            {
                "text": "Thorn 5",
                "trigger": {
                    "name": 10
                },
                "keyword": {
                    "name": 8,
                    "tier": 5
                },
                "target": {
                    "name": 18
                }
            }
        ],
        "description": "On enter: Host get Thorn 5",
        "image": "res://Assets/CardPics2/Thornwick.png"
    },
    Roseguard: {
        "name": "Roseguard",
        "id":Roseguard,
        "color":CardColor.GREEN,
        "cost": 4,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: For the first five turns, +4 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 7,
                    "conditionTier": 6
                },
                "keyword": {
                    "name": 5,
                    "tier": 4
                }
            },
            {
                "text": "Thorn 4.",
                "trigger": {
                    "name": 10
                },
                "keyword": {
                    "name": 8,
                    "tier": 4
                },
                "target": {
                    "name": 18
                }
            }
        ],
        "description": "Passive: For the first five turns, +4 Power. Thorn 4.",
        "image": "res://Assets/CardPics2/Roseguard.png"
    },
    EliteRoseguard: {
        "name": "Elite Roseguard",
        "id":EliteRoseguard,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: For the first five turns, +6 Power.",
                "trigger": {
                    "name": 2,
                    "condition": 7,
                    "conditionTier": 6
                },
                "keyword": {
                    "name": 5,
                    "tier": 6
                }
            },
            {
                "text": "Thorn 6.",
                "trigger": {
                    "name": 10
                },
                "keyword": {
                    "name": 8,
                    "tier": 6
                },
                "target": {
                    "name": 18
                }
            }
        ],
        "description": "Passive: For the first five turns, +6 Power. Thorn 6.",
        "image": "res://Assets/CardPics2/Elite Roseguard.png"
    },
    GreenMage: {
        "name": "Green Mage",
        "id":GreenMage,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Give +2 Power to allies played last turn.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 2
                },
                "target": {
                    "name": 15,
                    "condition": 18,
                    "conditionTier": 1
                }
            },
            {
                "text": "Thorn 1.",
                "trigger": {
                    "name": 10
                },
                "keyword": {
                    "name": 8,
                    "tier": 1
                },
                "target": {
                    "name": 18
                }
            }
        ],
        "description": "On enter: Give +2 Power to allies played last turn. Thorn 1.",
        "image": "res://Assets/CardPics2/Green Mage.png"
    },
    AldriontheGreen: {
        "name": "Aldrion the Green",
        "id":AldriontheGreen,
        "color":CardColor.GREEN,
        "cost": 4,
        "power": 5,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Give +4 Power to allies played last turn.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 4
                },
                "target": {
                    "name": 15,
                    "condition": 18,
                    "conditionTier": 1
                }
            },
            {
                "text": "Thorn 1.",
                "trigger": {
                    "name": 10
                },
                "keyword": {
                    "name": 8,
                    "tier": 1
                },
                "target": {
                    "name": 18
                }
            }
        ],
        "description": "On enter: Give +4 Power to allies played last turn. Thorn 1.",
        "image": "res://Assets/CardPics2/Aldrion the Green.png"
    },
    EntSpirit: {
        "name": "Ent Spirit",
        "id":EntSpirit,
        "color":CardColor.GREEN,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.EPIC,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Host get +1 Power for each ally here.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 1
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "givingAbilities": [
            {
                "text": "Passive: +1 Power for each ally here.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 1
                },
                "target": {
                    "name": 9
                }
            }
        ],
        "description": "On enter: Host get +1 Power for each ally here.",
        "image": "res://Assets/CardPics2/Ent Spirit.png"
    },
    ElderEntSpirit: {
        "name": "Elder Ent Spirit",
        "id":ElderEntSpirit,
        "color":CardColor.GREEN,
        "cost": 3,
        "power": 1,
        "rarity":Rarity.EPIC,
        "castCondition": 3,
        "abilities": [
            {
                "text": "On enter: Host get +2 Power for each ally here.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 1
                },
                "target": {
                    "name": 2
                }
            }
        ],
        "givingAbilities": [
            {
                "text": "Passive: +2 Power for each ally here.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 2
                },
                "target": {
                    "name": 9
                }
            }
        ],
        "description": "On enter: Host get +2 Power for each ally here.",
        "image": "res://Assets/CardPics2/Elder Ent Spirit.png"
    },
    Elyndra: {
        "name": "Elyndra",
        "id":Elyndra,
        "color":CardColor.GREEN,
        "cost": 5,
        "power": 9,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Gain 1 Permanent mana.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 2,
                    "tier": 1
                }
            },
            {
                "text": "Heal all allies",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 7
                },
                "target": {
                    "name": 15
                }
            }
        ],
        "description": "On enter: Gain 1 Permanent mana. Heal all allies",
        "image": "res://Assets/CardPics2/Elyndra.png"
    },
    Runescar: {
        "name": "Runescar",
        "id":Runescar,
        "color":CardColor.GREEN,
        "cost": 6,
        "power": 8,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: If you have 4 allies here. +8 Power",
                "trigger": {
                    "name": 2,
                    "condition": 19,
                    "conditionTier": 4
                },
                "keyword": {
                    "name": 5,
                    "tier": 8
                }
            },
            {
                "text": "Heal itself at the end of each turn.",
                "trigger": {
                    "name": 6
                },
                "keyword": {
                    "name": 7
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "Passive: If you have 4 allies here. +8 Power Heal itself at the end of each turn.",
        "image": "res://Assets/CardPics2/Runescar.png"
    },
    Elderbloom: {
        "name": "Elderbloom",
        "id":Elderbloom,
        "color":CardColor.GREEN,
        "cost": 6,
        "power": 1,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: +Power equals to the strongest ally's power",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 5,
                    "dynamicTier": 3
                }
            }
        ],
        "description": "Passive: +Power equals to the strongest ally's power",
        "image": "res://Assets/CardPics2/Elderbloom.png"
    },
    Myrthalia: {
        "name": "Myrthalia",
        "id":Myrthalia,
        "color":CardColor.GREEN,
        "cost": 4,
        "power": 1,
        "rarity":Rarity.EPIC,
        "castCondition": 3,
        "abilities": [
            {
                "text": "Create a Sapling of a host next turn",
                "trigger": {
                    "name": 4,
                    "condition": 9,
                    "conditionTier": 1
                },
                "keyword": {
                    "name": 6,
                    "tier": 1
                },
                "target": {
                    "name": 2
                },
                "targetSlot": {
                    "name": 7,
                    "condition": 1
                }
            }
        ],
        "description": "Create a Sapling of a host next turn",
        "image": "res://Assets/CardPics2/Myrthalia.png"
    },
    Terragon: {
        "name": "Terragon",
        "id":Terragon,
        "color":CardColor.GREEN,
        "cost": 6,
        "power": 6,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Gets +2 Power at the end of each turn if you have unspent mana",
                "trigger": {
                    "name": 6,
                    "condition": 13
                },
                "keyword": {
                    "name": 4,
                    "tier": 2
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "Gets +2 Power at the end of each turn if you have unspent mana",
        "image": "res://Assets/CardPics2/Terragon.png"
    },
    Butterfire: {
        "name": "Butterfire",
        "id":Butterfire,
        "color":CardColor.RED,
        "cost": 1,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "On enter: Gain 1 Temp mana.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 9,
                    "tier": 1
                }
            }
        ],
        "description": "On enter: Gain 1 Temp mana.",
        "image": "res://Assets/CardPics2/Butterfire.png"
    },
    NightButterfire: {
        "name": "Night Butterfire",
        "id":NightButterfire,
        "color":CardColor.RED,
        "cost": 1,
        "power": 2,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On enter: Gain 1 Temp mana.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 9,
                    "tier": 1
                }
            }
        ],
        "description": "On enter: Gain 1 Temp mana.",
        "image": "res://Assets/CardPics2/Night Butterfire.png"
    },
    HellButterfire: {
        "name": "Hell Butterfire",
        "id":HellButterfire,
        "color":CardColor.RED,
        "cost": 1,
        "power": 3,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Gain 1 Temp mana.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 9,
                    "tier": 2
                }
            }
        ],
        "description": "On enter: Gain 1 Temp mana.",
        "image": "res://Assets/CardPics2/Hell Butterfire.png"
    },
    Flarefire: {
        "name": "Flarefire",
        "id":Flarefire,
        "color":CardColor.RED,
        "cost": 1,
        "power": 3,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Gain 2 Temp mana.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 9,
                    "tier": 3
                }
            }
        ],
        "description": "On enter: Gain 2 Temp mana.",
        "image": "res://Assets/CardPics2/Flarefire.png"
    },
    Bomberkid: {
        "name": "Bomberkid",
        "id":Bomberkid,
        "color":CardColor.RED,
        "cost": 1,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "On dead: Afflict -4 Power to a random enemy.",
                "trigger": {
                    "name": 8
                },
                "keyword": {
                    "name": 10,
                    "tier": 4
                },
                "target": {
                    "name": 16,
                    "condition": 1
                }
            }
        ],
        "description": "On dead: Afflict -4 Power to a random enemy.",
        "image": "res://Assets/CardPics2/Bomberkid.png"
    },
    Bomber: {
        "name": "Bomber",
        "id":Bomber,
        "color":CardColor.RED,
        "cost": 2,
        "power": 2,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On dead: Afflict -5 Power to a random enemy.",
                "trigger": {
                    "name": 8
                },
                "keyword": {
                    "name": 10,
                    "tier": 5
                },
                "target": {
                    "name": 16,
                    "condition": 1
                }
            }
        ],
        "description": "On dead: Afflict -5 Power to a random enemy.",
        "image": "res://Assets/CardPics2/Bomber.png"
    },
    BigBomber: {
        "name": "Big Bomber",
        "id":BigBomber,
        "color":CardColor.RED,
        "cost": 3,
        "power": 3,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On dead: Afflict -6 Power to a random enemy.",
                "trigger": {
                    "name": 8
                },
                "keyword": {
                    "name": 10,
                    "tier": 6
                },
                "target": {
                    "name": 16,
                    "condition": 1
                }
            }
        ],
        "description": "On dead: Afflict -6 Power to a random enemy.",
        "image": "res://Assets/CardPics2/Big Bomber.png"
    },
    Boomgobb: {
        "name": "Boomgobb",
        "id":Boomgobb,
        "color":CardColor.RED,
        "cost": 4,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On dead: Afflict -8 Power to a random enemy.",
                "trigger": {
                    "name": 8
                },
                "keyword": {
                    "name": 10,
                    "tier": 8
                },
                "target": {
                    "name": 16,
                    "condition": 1
                }
            }
        ],
        "description": "On dead: Afflict -8 Power to a random enemy.",
        "image": "res://Assets/CardPics2/Boomgobb.png"
    },
    Impling: {
        "name": "Impling",
        "id":Impling,
        "color":CardColor.RED,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "On enter: Afflict -2 Power to an enemy played last turn.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 10,
                    "tier": 2
                },
                "target": {
                    "name": 16,
                    "condition": 18,
                    "conditionTier": 1
                }
            }
        ],
        "description": "On enter: Afflict -2 Power to an enemy played last turn.",
        "image": "res://Assets/CardPics2/Impling.png"
    },
    Imp: {
        "name": "Imp",
        "id":Imp,
        "color":CardColor.RED,
        "cost": 2,
        "power": 2,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On enter: Afflict -3 Power to an enemy played last turn.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 10,
                    "tier": 3
                },
                "target": {
                    "name": 16,
                    "condition": 18,
                    "conditionTier": 1
                }
            }
        ],
        "description": "On enter: Afflict -3 Power to an enemy played last turn.",
        "image": "res://Assets/CardPics2/Imp.png"
    },
    PitImp: {
        "name": "Pit Imp",
        "id":PitImp,
        "color":CardColor.RED,
        "cost": 3,
        "power": 3,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Afflict -4 Power to an enemy played last turn.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 10,
                    "tier": 4
                },
                "target": {
                    "name": 16,
                    "condition": 18,
                    "conditionTier": 1
                }
            }
        ],
        "description": "On enter: Afflict -4 Power to an enemy played last turn.",
        "image": "res://Assets/CardPics2/Pit Imp.png"
    },
    Kravveth: {
        "name": "Kravveth",
        "id":Kravveth,
        "color":CardColor.RED,
        "cost": 3,
        "power": 3,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Afflict -5 Power to an enemy played last turn.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 10,
                    "tier": 5
                },
                "target": {
                    "name": 16,
                    "condition": 18,
                    "conditionTier": 1
                }
            }
        ],
        "description": "On enter: Afflict -5 Power to an enemy played last turn.",
        "image": "res://Assets/CardPics2/Kravveth.png"
    },
    FireKnight: {
        "name": "Fire Knight",
        "id":FireKnight,
        "color":CardColor.RED,
        "cost": 3,
        "power": 5,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "On enter: Ignite 3.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 12,
                    "tier": 3
                }
            }
        ],
        "description": "On enter: Ignite 3.",
        "image": "res://Assets/CardPics2/Fire Knight.png"
    },
    BlazeKnight: {
        "name": "Blaze Knight",
        "id":BlazeKnight,
        "color":CardColor.RED,
        "cost": 4,
        "power": 6,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On enter: Ignite 4.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 12,
                    "tier": 4
                }
            }
        ],
        "description": "On enter: Ignite 4.",
        "image": "res://Assets/CardPics2/Blaze Knight.png"
    },
    DragonKnight: {
        "name": "Dragon Knight",
        "id":DragonKnight,
        "color":CardColor.RED,
        "cost": 5,
        "power": 8,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Ignite 5.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 12,
                    "tier": 5
                }
            }
        ],
        "description": "On enter: Ignite 5.",
        "image": "res://Assets/CardPics2/Dragon Knight.png"
    },
    Titus: {
        "name": "Titus",
        "id":Titus,
        "color":CardColor.RED,
        "cost": 6,
        "power": 9,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Ignite 7.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 12,
                    "tier": 7
                }
            }
        ],
        "description": "On enter: Ignite 7.",
        "image": "res://Assets/CardPics2/Titus.png"
    },
    Flinger: {
        "name": "Flinger",
        "id":Flinger,
        "color":CardColor.RED,
        "cost": 1,
        "power": 3,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "On dead: Ignite X. Where X is this card's power.",
                "trigger": {
                    "name": 8
                },
                "keyword": {
                    "name": 12,
                    "dynamicTier": 1
                }
            }
        ],
        "description": "On dead: Ignite X. Where X is this card's power.",
        "image": "res://Assets/CardPics2/Flinger.png"
    },
    EliteFlinger: {
        "name": "Elite Flinger",
        "id":EliteFlinger,
        "color":CardColor.RED,
        "cost": 1,
        "power": 4,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On dead: Ignite X. Where X is this card's power.",
                "trigger": {
                    "name": 8
                },
                "keyword": {
                    "name": 12,
                    "dynamicTier": 1
                }
            }
        ],
        "description": "On dead: Ignite X. Where X is this card's power.",
        "image": "res://Assets/CardPics2/Elite Flinger.png"
    },
    BossFlinger: {
        "name": "Boss Flinger",
        "id":BossFlinger,
        "color":CardColor.RED,
        "cost": 2,
        "power": 5,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On dead: Ignite X. Where X is this card's power.",
                "trigger": {
                    "name": 8
                },
                "keyword": {
                    "name": 12,
                    "dynamicTier": 1
                }
            }
        ],
        "description": "On dead: Ignite X. Where X is this card's power.",
        "image": "res://Assets/CardPics2/Boss Flinger.png"
    },
    ZarnaktheFling: {
        "name": "Zarnak the Fling",
        "id":ZarnaktheFling,
        "color":CardColor.RED,
        "cost": 4,
        "power": 6,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On dead: Ignite X. Where X is this card's power.",
                "trigger": {
                    "name": 8
                },
                "keyword": {
                    "name": 12,
                    "dynamicTier": 1
                }
            }
        ],
        "description": "On dead: Ignite X. Where X is this card's power.",
        "image": "res://Assets/CardPics2/Zarnak the Fling.png"
    },
    YoungSuccubus: {
        "name": "Young Succubus",
        "id":YoungSuccubus,
        "color":CardColor.RED,
        "cost": 3,
        "power": 3,
        "rarity":Rarity.COMMON,
        "castCondition": 1,
        "abilities": [
            {
                "text": "Passive: Give your allies with Sacrifice +1 Power.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 16,
                    "tier": 1
                },
                "target": {
                    "name": 15,
                    "condition": 13
                }
            }
        ],
        "description": "Passive: Give your allies with Sacrifice +1 Power.",
        "image": "res://Assets/CardPics2/Young Succubus.png"
    },
    Succubus: {
        "name": "Succubus",
        "id":Succubus,
        "color":CardColor.RED,
        "cost": 3,
        "power": 3,
        "rarity":Rarity.RARE,
        "castCondition": 1,
        "abilities": [
            {
                "text": "Passive: Give your allies with Sacrifice +2 Power.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 16,
                    "tier": 2
                },
                "target": {
                    "name": 15,
                    "condition": 13
                }
            }
        ],
        "description": "Passive: Give your allies with Sacrifice +2 Power.",
        "image": "res://Assets/CardPics2/Succubus.png"
    },
    HighSuccubus: {
        "name": "High Succubus",
        "id":HighSuccubus,
        "color":CardColor.RED,
        "cost": 4,
        "power": 4,
        "rarity":Rarity.EPIC,
        "castCondition": 1,
        "abilities": [
            {
                "text": "Passive: Give your allies with Sacrifice +3 Power.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 16,
                    "tier": 3
                },
                "target": {
                    "name": 15,
                    "condition": 13
                }
            }
        ],
        "description": "Passive: Give your allies with Sacrifice +3 Power.",
        "image": "res://Assets/CardPics2/High Succubus.png"
    },
    LadyLamia: {
        "name": "Lady Lamia",
        "id":LadyLamia,
        "color":CardColor.RED,
        "cost": 5,
        "power": 5,
        "rarity":Rarity.EPIC,
        "castCondition": 1,
        "abilities": [
            {
                "text": "Passive: Give your allies with Sacrifice +4 Power.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 16,
                    "tier": 4
                },
                "target": {
                    "name": 15,
                    "condition": 13
                }
            }
        ],
        "description": "Passive: Give your allies with Sacrifice +4 Power.",
        "image": "res://Assets/CardPics2/Lady Lamia.png"
    },
    Larvaling: {
        "name": "Larvaling",
        "id":Larvaling,
        "color":CardColor.RED,
        "cost": 2,
        "power": 2,
        "rarity":Rarity.COMMON,
        "castCondition": 1,
        "abilities": [
            {
                "text": "On enter: +2 Power if sacrifice an ally cost 4 or more",
                "trigger": {
                    "name": 3,
                    "condition": 22,
                    "conditionTier": 4
                },
                "keyword": {
                    "name": 4,
                    "tier": 2
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "On enter: +2 Power if sacrifice an ally cost 4 or more",
        "image": "res://Assets/CardPics2/Larvaling.png"
    },
    Larva: {
        "name": "Larva",
        "id":Larva,
        "color":CardColor.RED,
        "cost": 3,
        "power": 3,
        "rarity":Rarity.RARE,
        "castCondition": 1,
        "abilities": [
            {
                "text": "On enter: +3 Power if sacrifice an ally cost 3 or more",
                "trigger": {
                    "name": 3,
                    "condition": 22,
                    "conditionTier": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 3
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "On enter: +3 Power if sacrifice an ally cost 3 or more",
        "image": "res://Assets/CardPics2/Larva.png"
    },
    DeepLarva: {
        "name": "Deep Larva",
        "id":DeepLarva,
        "color":CardColor.RED,
        "cost": 4,
        "power": 4,
        "rarity":Rarity.EPIC,
        "castCondition": 1,
        "abilities": [
            {
                "text": "On enter: +4 Power if sacrifice an ally cost 2 or more",
                "trigger": {
                    "name": 3,
                    "condition": 22,
                    "conditionTier": 2
                },
                "keyword": {
                    "name": 4,
                    "tier": 4
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "On enter: +4 Power if sacrifice an ally cost 2 or more",
        "image": "res://Assets/CardPics2/Deep Larva.png"
    },
    AncientLarva: {
        "name": "Ancient Larva",
        "id":AncientLarva,
        "color":CardColor.RED,
        "cost": 5,
        "power": 5,
        "rarity":Rarity.EPIC,
        "castCondition": 1,
        "abilities": [
            {
                "text": "On enter: Gains the power of the sacrificed ally.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "dynamicTier": 5
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "On enter: Gains the power of the sacrificed ally.",
        "image": "res://Assets/CardPics2/Ancient Larva.png"
    },
    FireElemental: {
        "name": "Fire Elemental",
        "id":FireElemental,
        "color":CardColor.RED,
        "cost": 4,
        "power": 4,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On enter: Gain 2 Temp mana.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 9,
                    "tier": 2
                }
            }
        ],
        "description": "On enter: Gain 2 Temp mana.",
        "image": "res://Assets/CardPics2/Fire Elemental.png"
    },
    MagmaElemental: {
        "name": "Magma Elemental",
        "id":MagmaElemental,
        "color":CardColor.RED,
        "cost": 5,
        "power": 6,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Gain 2 Temp mana.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 9,
                    "tier": 2
                }
            }
        ],
        "description": "On enter: Gain 2 Temp mana.",
        "image": "res://Assets/CardPics2/Magma Elemental.png"
    },
    LordMalfyris: {
        "name": "Lord Malfyris",
        "id":LordMalfyris,
        "color":CardColor.RED,
        "cost": 6,
        "power": 7,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Gain 3 Temp mana.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 9,
                    "tier": 3
                }
            }
        ],
        "description": "On enter: Gain 3 Temp mana.",
        "image": "res://Assets/CardPics2/Lord Malfyris.png"
    },
    Redwings: {
        "name": "Redwings",
        "id":Redwings,
        "color":CardColor.RED,
        "cost": 5,
        "power": 4,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On enter: Destroy enemies here with cost 1 or less",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 11
                },
                "target": {
                    "name": 10,
                    "condition": 10,
                    "conditionTier": 2
                }
            }
        ],
        "description": "On enter: Destroy enemies here with cost 1 or less",
        "image": "res://Assets/CardPics2/Redwings.png"
    },
    GrandRedwings: {
        "name": "Grand Redwings",
        "id":GrandRedwings,
        "color":CardColor.RED,
        "cost": 5,
        "power": 5,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Destroy enemies here with cost 2 or less",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 11
                },
                "target": {
                    "name": 10,
                    "condition": 10,
                    "conditionTier": 3
                }
            }
        ],
        "description": "On enter: Destroy enemies here with cost 2 or less",
        "image": "res://Assets/CardPics2/Grand Redwings.png"
    },
    Heliora: {
        "name": "Heliora",
        "id":Heliora,
        "color":CardColor.RED,
        "cost": 5,
        "power": 7,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Destroy enemies here with cost 2 or less",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 11
                },
                "target": {
                    "name": 10,
                    "condition": 10,
                    "conditionTier": 3
                }
            }
        ],
        "description": "On enter: Destroy enemies here with cost 2 or less",
        "image": "res://Assets/CardPics2/Heliora.png"
    },
    Flamecoat: {
        "name": "Flamecoat",
        "id":Flamecoat,
        "color":CardColor.RED,
        "cost": 5,
        "power": 5,
        "rarity":Rarity.RARE,
        "castCondition": 1,
        "abilities": [
            {
                "text": "On enter: Afflict -1 Power to all enemies here.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 10,
                    "tier": 1
                },
                "target": {
                    "name": 10
                }
            }
        ],
        "description": "On enter: Afflict -1 Power to all enemies here.",
        "image": "res://Assets/CardPics2/Flamecoat.png"
    },
    DarkFlamecoat: {
        "name": "Dark Flamecoat",
        "id":DarkFlamecoat,
        "color":CardColor.RED,
        "cost": 6,
        "power": 5,
        "rarity":Rarity.EPIC,
        "castCondition": 1,
        "abilities": [
            {
                "text": "On enter: Afflict -2 Power to all enemies here.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 10,
                    "tier": 2
                },
                "target": {
                    "name": 10
                }
            }
        ],
        "description": "On enter: Afflict -2 Power to all enemies here.",
        "image": "res://Assets/CardPics2/Dark Flamecoat.png"
    },
    Apocalypse: {
        "name": "Apocalypse",
        "id":Apocalypse,
        "color":CardColor.RED,
        "cost": 6,
        "power": 5,
        "rarity":Rarity.EPIC,
        "castCondition": 1,
        "abilities": [
            {
                "text": "On enter: Afflict -3 Power to all enemies here.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 10,
                    "tier": 3
                },
                "target": {
                    "name": 10
                }
            }
        ],
        "description": "On enter: Afflict -3 Power to all enemies here.",
        "image": "res://Assets/CardPics2/Apocalypse.png"
    },
    RedMage: {
        "name": "Red Mage",
        "id":RedMage,
        "color":CardColor.RED,
        "cost": 4,
        "power": 6,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Destroy the weakest enemy here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 11
                },
                "target": {
                    "name": 10,
                    "condition": 4
                }
            }
        ],
        "description": "On enter: Destroy the weakest enemy here",
        "image": "res://Assets/CardPics2/Red Mage.png"
    },
    EmberistheRed: {
        "name": "Emberis the Red",
        "id":EmberistheRed,
        "color":CardColor.RED,
        "cost": 3,
        "power": 6,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Destroy the weakest enemy here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 11
                },
                "target": {
                    "name": 10,
                    "condition": 4
                }
            }
        ],
        "description": "On enter: Destroy the weakest enemy here",
        "image": "res://Assets/CardPics2/Emberis the Red.png"
    },
    Explodus: {
        "name": "Explodus",
        "id":Explodus,
        "color":CardColor.RED,
        "cost": 4,
        "power": 2,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On dead: Destroy the strongest enemy here",
                "trigger": {
                    "name": 8
                },
                "keyword": {
                    "name": 11
                },
                "target": {
                    "name": 10,
                    "condition": 3
                }
            }
        ],
        "description": "On dead: Destroy the strongest enemy here",
        "image": "res://Assets/CardPics2/Explodus.png"
    },
    ExplodusII: {
        "name": "Explodus II",
        "id":ExplodusII,
        "color":CardColor.RED,
        "cost": 6,
        "power": 2,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On dead: Destroy the strongest enemy",
                "trigger": {
                    "name": 8
                },
                "keyword": {
                    "name": 11
                },
                "target": {
                    "name": 16,
                    "condition": 3
                }
            }
        ],
        "description": "On dead: Destroy the strongest enemy",
        "image": "res://Assets/CardPics2/Explodus II.png"
    },
    Fireshadow: {
        "name": "Fireshadow",
        "id":Fireshadow,
        "color":CardColor.RED,
        "cost": 6,
        "power": 5,
        "rarity":Rarity.EPIC,
        "castCondition": 1,
        "abilities": [
            {
                "text": "On enter: On dead ability of the sacrificed ally triggered twice.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 27
                },
                "target": {
                    "name": 20,
                    "condition": 16
                }
            }
        ],
        "description": "On enter: On dead ability of the sacrificed ally triggered twice.",
        "image": "res://Assets/CardPics2/Fireshadow.png"
    },
    Evilshadow: {
        "name": "Evilshadow",
        "id":Evilshadow,
        "color":CardColor.RED,
        "cost": 6,
        "power": 7,
        "rarity":Rarity.EPIC,
        "castCondition": 1,
        "abilities": [
            {
                "text": "On enter: On dead ability of the sacrificed ally triggered twice.",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 27
                },
                "target": {
                    "name": 20,
                    "condition": 16
                }
            }
        ],
        "description": "On enter: On dead ability of the sacrificed ally triggered twice.",
        "image": "res://Assets/CardPics2/Evilshadow.png"
    },
    Nymora: {
        "name": "Nymora",
        "id":Nymora,
        "color":CardColor.RED,
        "cost": 5,
        "power": 5,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Passive: Allies abilities that reduce enemy power, reduce 1 more.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 22,
                    "tier": 1
                },
                "target": {
                    "name": 15
                }
            }
        ],
        "description": "Passive: Allies abilities that reduce enemy power, reduce 1 more.",
        "image": "res://Assets/CardPics2/Nymora.png"
    },
    Grashnak: {
        "name": "Grashnak",
        "id":Grashnak,
        "color":CardColor.RED,
        "cost": 1,
        "power": 1,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On dead: Afflict -1 Power to all enemies.",
                "trigger": {
                    "name": 8
                },
                "keyword": {
                    "name": 10,
                    "tier": 1
                },
                "target": {
                    "name": 16
                }
            }
        ],
        "description": "On dead: Afflict -1 Power to all enemies.",
        "image": "res://Assets/CardPics2/Grashnak.png"
    },
    KingKrotos: {
        "name": "King Krotos",
        "id":KingKrotos,
        "color":CardColor.RED,
        "cost": 6,
        "power": 6,
        "rarity":Rarity.EPIC,
        "castCondition": 1,
        "abilities": [
            {
                "text": "Passive: +1 Power for each card in your graveyard.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 1
                },
                "target": {
                    "name": 6
                }
            }
        ],
        "description": "Passive: +1 Power for each card in your graveyard.",
        "image": "res://Assets/CardPics2/King Krotos.png"
    },
    Ignix: {
        "name": "Ignix",
        "id":Ignix,
        "color":CardColor.RED,
        "cost": 5,
        "power": 6,
        "rarity":Rarity.EPIC,
        "castCondition": 1,
        "abilities": [
            {
                "text": "On enter: Ignite 7,",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 12,
                    "tier": 7
                }
            },
            {
                "text": "then gain 3 Temp mana",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 9,
                    "tier": 3
                }
            }
        ],
        "description": "On enter: Ignite 7, then gain 3 Temp mana",
        "image": "res://Assets/CardPics2/Ignix.png"
    },
    Pyragon: {
        "name": "Pyragon",
        "id":Pyragon,
        "color":CardColor.RED,
        "cost": 6,
        "power": 9,
        "rarity":Rarity.EPIC,
        "castCondition": 1,
        "abilities": [
            {
                "text": "Afflict -3 Power to the strongest enemy at the end of each turn",
                "trigger": {
                    "name": 6
                },
                "keyword": {
                    "name": 10,
                    "tier": 3
                },
                "target": {
                    "name": 16,
                    "condition": 3
                }
            }
        ],
        "description": "Afflict -3 Power to the strongest enemy at the end of each turn",
        "image": "res://Assets/CardPics2/Pyragon.png"
    },
    Siren: {
        "name": "Siren",
        "id":Siren,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "On enter: Silent a random enemy",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 33
                },
                "target": {
                    "name": 16,
                    "condition": 1
                }
            }
        ],
        "description": "On enter: Silent a random enemy",
        "image": "res://Assets/CardPics2/Siren.png"
    },
    EliteSiren: {
        "name": "Elite Siren",
        "id":EliteSiren,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 2,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On enter: Silent a random enemy",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 33
                },
                "target": {
                    "name": 16,
                    "condition": 1
                }
            }
        ],
        "description": "On enter: Silent a random enemy",
        "image": "res://Assets/CardPics2/Elite Siren.png"
    },
    QueenSiren: {
        "name": "Queen Siren",
        "id":QueenSiren,
        "color":CardColor.BLUE,
        "cost": 4,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Silent a random enemy",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 33
                },
                "target": {
                    "name": 16,
                    "condition": 1
                }
            }
        ],
        "description": "On enter: Silent a random enemy",
        "image": "res://Assets/CardPics2/Queen Siren.png"
    },
    Calyra: {
        "name": "Calyra",
        "id":Calyra,
        "color":CardColor.BLUE,
        "cost": 5,
        "power": 2,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Silent all enemies here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 33
                },
                "target": {
                    "name": 10,
                    "condition": 19
                }
            }
        ],
        "description": "On enter: Silent all enemies here",
        "image": "res://Assets/CardPics2/Calyra.png"
    },
    Shelling: {
        "name": "Shelling",
        "id":Shelling,
        "color":CardColor.BLUE,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "On enter: Draw a card",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 14,
                    "tier": 1
                }
            }
        ],
        "description": "On enter: Draw a card",
        "image": "res://Assets/CardPics2/Shelling.png"
    },
    Shell: {
        "name": "Shell",
        "id":Shell,
        "color":CardColor.BLUE,
        "cost": 2,
        "power": 2,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On enter: Draw a card",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 14,
                    "tier": 1
                }
            }
        ],
        "description": "On enter: Draw a card",
        "image": "res://Assets/CardPics2/Shell.png"
    },
    MagicShell: {
        "name": "Magic Shell",
        "id":MagicShell,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Draw a card",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 14,
                    "tier": 1
                }
            }
        ],
        "description": "On enter: Draw a card",
        "image": "res://Assets/CardPics2/Magic Shell.png"
    },
    Pearind: {
        "name": "Pearind",
        "id":Pearind,
        "color":CardColor.BLUE,
        "cost": 4,
        "power": 5,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Draw 2 cards",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 14,
                    "tier": 2
                }
            }
        ],
        "description": "On enter: Draw 2 cards",
        "image": "res://Assets/CardPics2/Pearind.png"
    },
    Mimicling: {
        "name": "Mimicling",
        "id":Mimicling,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "On enter: Mimic the weakest enemy",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 35
                },
                "target": {
                    "name": 16,
                    "condition": 4
                }
            }
        ],
        "description": "On enter: Mimic the weakest enemy",
        "image": "res://Assets/CardPics2/Mimicling.png"
    },
    Mimic: {
        "name": "Mimic",
        "id":Mimic,
        "color":CardColor.BLUE,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On enter: Mimic the weakest enemy",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 35
                },
                "target": {
                    "name": 16,
                    "condition": 4
                }
            }
        ],
        "description": "On enter: Mimic the weakest enemy",
        "image": "res://Assets/CardPics2/Mimic.png"
    },
    FangedMimic: {
        "name": "Fanged Mimic",
        "id":FangedMimic,
        "color":CardColor.BLUE,
        "cost": 1,
        "power": 1,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Mimic the weakest enemy",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 35
                },
                "target": {
                    "name": 16,
                    "condition": 4
                }
            }
        ],
        "description": "On enter: Mimic the weakest enemy",
        "image": "res://Assets/CardPics2/Fanged Mimic.png"
    },
    TheUnhollow: {
        "name": "The Unhollow",
        "id":TheUnhollow,
        "color":CardColor.BLUE,
        "cost": 1,
        "power": 1,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Mimic the weakest enemy,",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 35
                },
                "target": {
                    "name": 16,
                    "condition": 4
                }
            },
            {
                "text": "then +3 Power to self",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 3
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "On enter: Mimic the weakest enemy, then +3 Power to self",
        "image": "res://Assets/CardPics2/The Unhollow.png"
    },
    Mermage: {
        "name": "Mermage",
        "id":Mermage,
        "color":CardColor.BLUE,
        "cost": 1,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "On enter: Unsummon a random enemy here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 34
                },
                "target": {
                    "name": 10,
                    "condition": 1
                }
            }
        ],
        "description": "On enter: Unsummon a random enemy here",
        "image": "res://Assets/CardPics2/Mermage.png"
    },
    DeepMermage: {
        "name": "Deep Mermage",
        "id":DeepMermage,
        "color":CardColor.BLUE,
        "cost": 1,
        "power": 2,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On enter: Unsummon a random enemy here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 34
                },
                "target": {
                    "name": 10,
                    "condition": 1
                }
            }
        ],
        "description": "On enter: Unsummon a random enemy here",
        "image": "res://Assets/CardPics2/Deep Mermage.png"
    },
    HighMermage: {
        "name": "High Mermage",
        "id":HighMermage,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Unsummon a strongest enemy here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 34
                },
                "target": {
                    "name": 10,
                    "condition": 3
                }
            }
        ],
        "description": "On enter: Unsummon a strongest enemy here",
        "image": "res://Assets/CardPics2/High Mermage.png"
    },
    Tydros: {
        "name": "Tydros",
        "id":Tydros,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 6,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Unsummon a strongest enemy here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 34
                },
                "target": {
                    "name": 10,
                    "condition": 3
                }
            }
        ],
        "description": "On enter: Unsummon a strongest enemy here",
        "image": "res://Assets/CardPics2/Tydros.png"
    },
    Angler: {
        "name": "Angler",
        "id":Angler,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 1,
        "rarity":Rarity.COMMON,
        "abilities": [
            {
                "text": "Draw a card whenever you play a card here.",
                "trigger": {
                    "name": 13
                },
                "keyword": {
                    "name": 14,
                    "tier": 1
                }
            }
        ],
        "description": "Draw a card whenever you play a card here.",
        "image": "res://Assets/CardPics2/Angler.png"
    },
    PearlAngler: {
        "name": "Pearl Angler",
        "id":PearlAngler,
        "color":CardColor.BLUE,
        "cost": 4,
        "power": 2,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "Draw a card whenever you play a card here.",
                "trigger": {
                    "name": 13
                },
                "keyword": {
                    "name": 14,
                    "tier": 1
                }
            }
        ],
        "description": "Draw a card whenever you play a card here.",
        "image": "res://Assets/CardPics2/Pearl Angler.png"
    },
    GrandAngler: {
        "name": "Grand Angler",
        "id":GrandAngler,
        "color":CardColor.BLUE,
        "cost": 5,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Draw a card whenever you play a card here.",
                "trigger": {
                    "name": 13
                },
                "keyword": {
                    "name": 14,
                    "tier": 1
                }
            }
        ],
        "description": "Draw a card whenever you play a card here.",
        "image": "res://Assets/CardPics2/Grand Angler.png"
    },
    Lurkgloom: {
        "name": "Lurkgloom",
        "id":Lurkgloom,
        "color":CardColor.BLUE,
        "cost": 6,
        "power": 6,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Draw a card whenever you play a card here.",
                "trigger": {
                    "name": 13
                },
                "keyword": {
                    "name": 14,
                    "tier": 1
                }
            }
        ],
        "description": "Draw a card whenever you play a card here.",
        "image": "res://Assets/CardPics2/Lurkgloom.png"
    },
    IceGolem: {
        "name": "Ice Golem",
        "id":IceGolem,
        "color":CardColor.BLUE,
        "cost": 4,
        "power": 1,
        "rarity":Rarity.COMMON,
        "castCondition": 2,
        "abilities": [
            {
                "text": "Passive: +1 power for each card in your hand",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 1
                },
                "target": {
                    "name": 3
                }
            }
        ],
        "description": "Passive: +1 power for each card in your hand",
        "image": "res://Assets/CardPics2/Ice Golem.png"
    },
    CrystalGolem: {
        "name": "Crystal Golem",
        "id":CrystalGolem,
        "color":CardColor.BLUE,
        "cost": 4,
        "power": 2,
        "rarity":Rarity.RARE,
        "castCondition": 2,
        "abilities": [
            {
                "text": "Passive: +1 power for each card in your hand",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 1
                },
                "target": {
                    "name": 3
                }
            }
        ],
        "description": "Passive: +1 power for each card in your hand",
        "image": "res://Assets/CardPics2/Crystal Golem.png"
    },
    DiamondGolem: {
        "name": "Diamond Golem",
        "id":DiamondGolem,
        "color":CardColor.BLUE,
        "cost": 5,
        "power": 4,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "Passive: +1 power for each card in your hand",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 1
                },
                "target": {
                    "name": 3
                }
            }
        ],
        "description": "Passive: +1 power for each card in your hand",
        "image": "res://Assets/CardPics2/Diamond Golem.png"
    },
    Frostbane: {
        "name": "Frostbane",
        "id":Frostbane,
        "color":CardColor.BLUE,
        "cost": 6,
        "power": 2,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "Passive: +2 power for each card in your hand",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 2
                },
                "target": {
                    "name": 3
                }
            }
        ],
        "description": "Passive: +2 power for each card in your hand",
        "image": "res://Assets/CardPics2/Frostbane.png"
    },
    Metaling: {
        "name": "Metaling",
        "id":Metaling,
        "color":CardColor.BLUE,
        "cost": 5,
        "power": 1,
        "rarity":Rarity.COMMON,
        "castCondition": 2,
        "abilities": [
            {
                "text": "On enter: Mimic the bounced ally",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 35
                },
                "target": {
                    "name": 21
                }
            }
        ],
        "description": "On enter: Mimic the bounced ally",
        "image": "res://Assets/CardPics2/Metaling.png"
    },
    Metamore: {
        "name": "Metamore",
        "id":Metamore,
        "color":CardColor.BLUE,
        "cost": 5,
        "power": 1,
        "rarity":Rarity.RARE,
        "castCondition": 2,
        "abilities": [
            {
                "text": "On enter: Mimic the bounced ally,",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 35
                },
                "target": {
                    "name": 21
                }
            },
            {
                "text": "then +1 Power to self",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 1
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "On enter: Mimic the bounced ally, then +1 Power to self",
        "image": "res://Assets/CardPics2/Metamore.png"
    },
    Metamonster: {
        "name": "Metamonster",
        "id":Metamonster,
        "color":CardColor.BLUE,
        "cost": 4,
        "power": 1,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "On enter: Mimic the bounced ally,",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 35
                },
                "target": {
                    "name": 21
                }
            },
            {
                "text": "then +2 Power to self",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 2
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "On enter: Mimic the bounced ally, then +2 Power to self",
        "image": "res://Assets/CardPics2/Metamonster.png"
    },
    Murkendral: {
        "name": "Murkendral",
        "id":Murkendral,
        "color":CardColor.BLUE,
        "cost": 4,
        "power": 1,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "On enter: Mimic the bounced ally,",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 35
                },
                "target": {
                    "name": 21
                }
            },
            {
                "text": "then +4 Power to self",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 4
                },
                "target": {
                    "name": 1
                }
            }
        ],
        "description": "On enter: Mimic the bounced ally, then +4 Power to self",
        "image": "res://Assets/CardPics2/Murkendral.png"
    },
    Silencer: {
        "name": "Silencer",
        "id":Silencer,
        "color":CardColor.BLUE,
        "cost": 5,
        "power": 4,
        "rarity":Rarity.RARE,
        "abilities": [
            {
                "text": "On enter: Silent all enemies played here last turn",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 33
                },
                "target": {
                    "name": 10,
                    "condition": 18,
                    "conditionTier": 1
                }
            }
        ],
        "description": "On enter: Silent all enemies played here last turn",
        "image": "res://Assets/CardPics2/Silencer.png"
    },
    EliteSilencer: {
        "name": "Elite Silencer",
        "id":EliteSilencer,
        "color":CardColor.BLUE,
        "cost": 5,
        "power": 5,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Silent all enemies played here last turn",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 33
                },
                "target": {
                    "name": 10,
                    "condition": 18,
                    "conditionTier": 1
                }
            }
        ],
        "description": "On enter: Silent all enemies played here last turn",
        "image": "res://Assets/CardPics2/Elite Silencer.png"
    },
    Caelith: {
        "name": "Caelith",
        "id":Caelith,
        "color":CardColor.BLUE,
        "cost": 6,
        "power": 5,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Silent all enemies played last turn",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 33
                },
                "target": {
                    "name": 16,
                    "condition": 18,
                    "conditionTier": 1
                }
            }
        ],
        "description": "On enter: Silent all enemies played last turn",
        "image": "res://Assets/CardPics2/Caelith.png"
    },
    WaterElemental: {
        "name": "Water Elemental",
        "id":WaterElemental,
        "color":CardColor.BLUE,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.RARE,
        "castCondition": 2,
        "abilities": [
            {
                "text": "Passive: +1 power for each ally with Bounce",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 1
                },
                "target": {
                    "name": 15,
                    "condition": 13
                }
            }
        ],
        "description": "Passive: +1 power for each ally with Bounce",
        "image": "res://Assets/CardPics2/Water Elemental.png"
    },
    TideElemental: {
        "name": "Tide Elemental",
        "id":TideElemental,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 3,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "Passive: +1 power for each ally with Bounce",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 1
                },
                "target": {
                    "name": 15,
                    "condition": 13
                }
            }
        ],
        "description": "Passive: +1 power for each ally with Bounce",
        "image": "res://Assets/CardPics2/Tide Elemental.png"
    },
    Ocevaris: {
        "name": "Ocevaris",
        "id":Ocevaris,
        "color":CardColor.BLUE,
        "cost": 4,
        "power": 3,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "Passive: +2 power for each ally with Bounce",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 3,
                    "tier": 2
                },
                "target": {
                    "name": 15,
                    "condition": 13
                }
            }
        ],
        "description": "Passive: +2 power for each ally with Bounce",
        "image": "res://Assets/CardPics2/Ocevaris.png"
    },
    SnowFairy: {
        "name": "Snow Fairy",
        "id":SnowFairy,
        "color":CardColor.BLUE,
        "cost": 2,
        "power": 1,
        "rarity":Rarity.RARE,
        "castCondition": 2,
        "abilities": [
            {
                "text": "On enter: Give +1 power to ally Bounced by this card",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 1
                },
                "target": {
                    "name": 21
                }
            }
        ],
        "description": "On enter: Give +1 power to ally Bounced by this card",
        "image": "res://Assets/CardPics2/Snow Fairy.png"
    },
    BlizzardFairy: {
        "name": "Blizzard Fairy",
        "id":BlizzardFairy,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 2,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "On enter: Give +2 power to ally Bounced by this card",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 2
                },
                "target": {
                    "name": 21
                }
            }
        ],
        "description": "On enter: Give +2 power to ally Bounced by this card",
        "image": "res://Assets/CardPics2/Blizzard Fairy.png"
    },
    Eirwyn: {
        "name": "Eirwyn",
        "id":Eirwyn,
        "color":CardColor.BLUE,
        "cost": 4,
        "power": 3,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "On enter: Give +3 power to ally Bounced by this card",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 4,
                    "tier": 3
                },
                "target": {
                    "name": 21
                }
            }
        ],
        "description": "On enter: Give +3 power to ally Bounced by this card",
        "image": "res://Assets/CardPics2/Eirwyn.png"
    },
    Levisia: {
        "name": "Levisia",
        "id":Levisia,
        "color":CardColor.BLUE,
        "cost": 4,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Unsummon a random enemy here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 34
                },
                "target": {
                    "name": 10,
                    "condition": 1
                }
            },
            {
                "text": "Passive: Opponent can't play a card here next turn",
                "trigger": {
                    "name": 2,
                    "condition": 7,
                    "conditionTier": 1
                },
                "keyword": {
                    "name": 29
                },
                "targetSlot": {
                    "name": 2
                }
            }
        ],
        "description": "On enter: Unsummon a random enemy here Passive: Opponent can't play a card here next turn",
        "image": "res://Assets/CardPics2/Levisia.png"
    },
    SkyLevisia: {
        "name": "Sky Levisia",
        "id":SkyLevisia,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 4,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Unsummon a random enemy here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 34
                },
                "target": {
                    "name": 10,
                    "condition": 1
                }
            },
            {
                "text": "Passive: Opponent can't play a card here next turn",
                "trigger": {
                    "name": 2,
                    "condition": 7,
                    "conditionTier": 1
                },
                "keyword": {
                    "name": 29
                },
                "targetSlot": {
                    "name": 2
                }
            }
        ],
        "description": "On enter: Unsummon a random enemy here Passive: Opponent can't play a card here next turn",
        "image": "res://Assets/CardPics2/Sky Levisia.png"
    },
    Trine: {
        "name": "Trine",
        "id":Trine,
        "color":CardColor.BLUE,
        "cost": 5,
        "power": 5,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Silent a random enemy here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 33
                },
                "target": {
                    "name": 10,
                    "condition": 1
                }
            },
            {
                "text": "Passive: On enter effect of ally entering the arena trigger twice.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 25,
                    "tier": 2
                },
                "target": {
                    "name": 15,
                    "condition": 14
                }
            }
        ],
        "description": "On enter: Silent a random enemy here Passive: On enter effect of ally entering the arena trigger twice.",
        "image": "res://Assets/CardPics2/Trine.png"
    },
    MasterTrine: {
        "name": "Master Trine",
        "id":MasterTrine,
        "color":CardColor.BLUE,
        "cost": 5,
        "power": 5,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Silent a random enemy here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 33
                },
                "target": {
                    "name": 10,
                    "condition": 1
                }
            },
            {
                "text": "Passive: On enter effect of ally entering the arena trigger twice.",
                "trigger": {
                    "name": 2
                },
                "keyword": {
                    "name": 25,
                    "tier": 2
                },
                "target": {
                    "name": 15,
                    "condition": 14
                }
            }
        ],
        "description": "On enter: Silent a random enemy here Passive: On enter effect of ally entering the arena trigger twice.",
        "image": "res://Assets/CardPics2/Master Trine.png"
    },
    BlueMage: {
        "name": "Blue Mage",
        "id":BlueMage,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 4,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "On enter: Duplicate the bounced ally with cost 3 or less",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 15
                },
                "target": {
                    "name": 21,
                    "condition": 10,
                    "conditionTier": 4
                },
                "targetSlot": {
                    "name": 1,
                    "condition": 1
                }
            }
        ],
        "description": "On enter: Duplicate the bounced ally with cost 3 or less",
        "image": "res://Assets/CardPics2/Blue Mage.png"
    },
    ElyndriatheBlue: {
        "name": "Elyndria the Blue",
        "id":ElyndriatheBlue,
        "color":CardColor.BLUE,
        "cost": 3,
        "power": 4,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "On enter: Duplicate the bounced ally with cost 3 or less",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 15
                },
                "target": {
                    "name": 21,
                    "condition": 10,
                    "conditionTier": 4
                },
                "targetSlot": {
                    "name": 1,
                    "condition": 1
                }
            }
        ],
        "description": "On enter: Duplicate the bounced ally with cost 3 or less",
        "image": "res://Assets/CardPics2/Elyndria the Blue.png"
    },
    Phantarion: {
        "name": "Phantarion",
        "id":Phantarion,
        "color":CardColor.BLUE,
        "cost": 5,
        "power": 1,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "On enter: Mimic the strongest enemy here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 35
                },
                "target": {
                    "name": 10,
                    "condition": 3
                }
            }
        ],
        "description": "On enter: Mimic the strongest enemy here",
        "image": "res://Assets/CardPics2/Phantarion.png"
    },
    Leviathan: {
        "name": "Leviathan",
        "id":Leviathan,
        "color":CardColor.BLUE,
        "cost": 5,
        "power": 7,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Draw a card at the end of your turn if you're winning here",
                "trigger": {
                    "name": 6,
                    "condition": 20
                },
                "keyword": {
                    "name": 14,
                    "tier": 1
                }
            }
        ],
        "description": "Draw a card at the end of your turn if you're winning here",
        "image": "res://Assets/CardPics2/Leviathan.png"
    },
    Tidecaller: {
        "name": "Tidecaller",
        "id":Tidecaller,
        "color":CardColor.BLUE,
        "cost": 6,
        "power": 9,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "On enter: Ally Bounced by this card cost is reduced to 1",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 28,
                    "tier": 1
                },
                "target": {
                    "name": 21
                }
            }
        ],
        "description": "On enter: Ally Bounced by this card cost is reduced to 1",
        "image": "res://Assets/CardPics2/Tidecaller.png"
    },
    SeaSovereign: {
        "name": "Sea Sovereign",
        "id":SeaSovereign,
        "color":CardColor.BLUE,
        "cost": 6,
        "power": 5,
        "rarity":Rarity.EPIC,
        "castCondition": 2,
        "abilities": [
            {
                "text": "On enter: Unsummon all enemies here",
                "trigger": {
                    "name": 3
                },
                "keyword": {
                    "name": 34
                },
                "target": {
                    "name": 10,
                    "condition": 19
                }
            }
        ],
        "description": "On enter: Unsummon all enemies here",
        "image": "res://Assets/CardPics2/Sea Sovereign.png"
    },
    Aquagon: {
        "name": "Aquagon",
        "id":Aquagon,
        "color":CardColor.BLUE,
        "cost": 6,
        "power": 8,
        "rarity":Rarity.EPIC,
        "abilities": [
            {
                "text": "Unsummon a random enemy at the end of each turn",
                "trigger": {
                    "name": 6
                },
                "keyword": {
                    "name": 34
                },
                "target": {
                    "name": 16,
                    "condition": 1
                }
            }
        ],
        "description": "Unsummon a random enemy at the end of each turn",
        "image": "res://Assets/CardPics2/Aquagon.png"
    }
}
