[gd_scene load_steps=11 format=3 uid="uid://dngyumd15a7s8"]

[ext_resource type="Script" path="res://scripts/deck_manager/deck_manager_screen.gd" id="1_kgwtm"]
[ext_resource type="PackedScene" uid="uid://cortelt5ba30d" path="res://scripts/option/option_screen_popup.tscn" id="2_eq3ou"]

[sub_resource type="Gradient" id="Gradient_eleys"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0.400698, 0.400698, 0.400698, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_d8hm2"]
gradient = SubResource("Gradient_eleys")

[sub_resource type="Gradient" id="Gradient_o22ov"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0.60933, 0.60933, 0.60933, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_uvapr"]
gradient = SubResource("Gradient_o22ov")

[sub_resource type="Gradient" id="Gradient_ww1vc"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0.481789, 0.48179, 0.481789, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_fi5jn"]
gradient = SubResource("Gradient_ww1vc")

[sub_resource type="Gradient" id="Gradient_c4be3"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0.192157, 0.192157, 0.192157, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_clfu7"]
gradient = SubResource("Gradient_c4be3")

[node name="DeckManagerScreen" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
size_flags_vertical = 4
script = ExtResource("1_kgwtm")
metadata/_edit_lock_ = true

[node name="TextureRect" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = -0.92
offset_top = 1.0
offset_right = 1919.08
offset_bottom = 1081.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_d8hm2")
metadata/_edit_lock_ = true

[node name="TestModeButton" type="Button" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 377.0
offset_top = -129.0
offset_right = 548.0
offset_bottom = -82.0001
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(0.94, 0.94)
theme_override_font_sizes/font_size = 28
text = "Combine"

[node name="TestModeButton2" type="Button" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 377.0
offset_top = -201.0
offset_right = 548.0
offset_bottom = -154.0
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(0.94, 0.94)
theme_override_font_sizes/font_size = 28
text = "Upgrade"

[node name="TextureRect12" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1576.08
offset_top = 99.0
offset_right = 1918.08
offset_bottom = 1078.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_uvapr")

[node name="TextureRect22" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1.08
offset_top = -3.0
offset_right = 1917.08
offset_bottom = 73.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_fi5jn")

[node name="TextureRect2" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 61.08
offset_top = 181.0
offset_right = 319.08
offset_bottom = 484.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect3" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 358.08
offset_top = 181.0
offset_right = 616.08
offset_bottom = 484.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect4" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 657.08
offset_top = 181.0
offset_right = 915.08
offset_bottom = 484.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect5" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 961.08
offset_top = 181.0
offset_right = 1219.08
offset_bottom = 484.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect6" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1260.08
offset_top = 181.0
offset_right = 1518.08
offset_bottom = 484.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect7" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 61.08
offset_top = 514.0
offset_right = 319.08
offset_bottom = 817.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect8" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 358.08
offset_top = 514.0
offset_right = 616.08
offset_bottom = 817.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect9" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 657.08
offset_top = 514.0
offset_right = 915.08
offset_bottom = 817.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect10" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 961.08
offset_top = 514.0
offset_right = 1219.08
offset_bottom = 817.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect11" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1260.08
offset_top = 514.0
offset_right = 1518.08
offset_bottom = 817.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect13" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1555.08
offset_top = 70.0
offset_right = 1918.08
offset_bottom = 153.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect14" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1620.08
offset_top = 174.0
offset_right = 1878.08
offset_bottom = 227.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect15" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1620.08
offset_top = 238.0
offset_right = 1878.08
offset_bottom = 291.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect16" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1620.08
offset_top = 300.0
offset_right = 1878.08
offset_bottom = 353.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect17" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1620.08
offset_top = 364.0
offset_right = 1878.08
offset_bottom = 417.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect18" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1620.08
offset_top = 429.0
offset_right = 1878.08
offset_bottom = 482.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect19" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1620.08
offset_top = 493.0
offset_right = 1878.08
offset_bottom = 546.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect20" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1620.08
offset_top = 555.0
offset_right = 1878.08
offset_bottom = 608.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="TextureRect21" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.001
anchor_right = 0.001
offset_left = 1620.08
offset_top = 619.0
offset_right = 1878.08
offset_bottom = 672.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("GradientTexture1D_clfu7")

[node name="SettingsButton" type="Button" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 779.0
offset_top = -1070.0
offset_right = 950.0
offset_bottom = -1023.0
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(0.94, 0.94)
theme_override_font_sizes/font_size = 28
text = "Setting"

[node name="DoneButton" type="Button" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 709.0
offset_top = -112.0
offset_right = 885.0
offset_bottom = -65.0
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(0.94, 0.94)
theme_override_font_sizes/font_size = 28
text = "Done"

[node name="Label" type="Label" parent="."]
layout_mode = 0
offset_left = 104.0
offset_top = 208.0
offset_right = 210.0
offset_bottom = 252.0
text = "card#01"

[node name="Label2" type="Label" parent="."]
layout_mode = 0
offset_left = 406.0
offset_top = 208.0
offset_right = 517.0
offset_bottom = 252.0
text = "card#02"

[node name="Label3" type="Label" parent="."]
layout_mode = 0
offset_left = 712.0
offset_top = 208.0
offset_right = 823.0
offset_bottom = 252.0
text = "card#03"

[node name="Label4" type="Label" parent="."]
layout_mode = 0
offset_left = 1014.0
offset_top = 208.0
offset_right = 1128.0
offset_bottom = 252.0
text = "card#04"

[node name="Label6" type="Label" parent="."]
layout_mode = 0
offset_left = 406.0
offset_top = 564.0
offset_right = 502.0
offset_bottom = 608.0
text = "card#07"

[node name="Label11" type="Label" parent="."]
layout_mode = 0
offset_left = 118.0
offset_top = 564.0
offset_right = 214.0
offset_bottom = 608.0
text = "card#06"

[node name="Label11" type="Label" parent="Label11"]
layout_mode = 0
offset_left = 1184.0
offset_top = -353.0
offset_right = 1295.0
offset_bottom = -309.0
text = "card#05"

[node name="Label7" type="Label" parent="."]
layout_mode = 0
offset_left = 708.0
offset_top = 564.0
offset_right = 804.0
offset_bottom = 608.0
text = "card#08"

[node name="Label8" type="Label" parent="."]
layout_mode = 0
offset_left = 1014.0
offset_top = 564.0
offset_right = 1110.0
offset_bottom = 608.0
text = "card#09"

[node name="Label9" type="Label" parent="."]
layout_mode = 0
offset_left = 1316.0
offset_top = 564.0
offset_right = 1412.0
offset_bottom = 608.0
text = "card#10"

[node name="OptionScreen" parent="." instance=ExtResource("2_eq3ou")]
visible = false
layout_mode = 1

[connection signal="pressed" from="SettingsButton" to="." method="_on_settings_button_pressed"]
[connection signal="pressed" from="DoneButton" to="." method="_on_done_button_pressed"]
