class_name Target
extends Object

enum {
	NONE,
	SELF,
	HOS<PERSON>,
	YOUR_HAND,
	OPPO_HAND,
	HANDS,
	YOUR_GRAVEYARD,
	OPPO_GRAVEYARD,
	GRAVEYARDS,
	LANE_ALLY,
	LANE_ENEMY,
	LANE,
	OTHER_LANES_ALLY,
	OTHER_LANES_ENEMY,
	OTHER_LANES,
	ARENA_ALLY,
	ARENA_ENEMY,
	ARENA,
	SOURCE,
	OTHER_LANES_ALLY_LOC,
	SACRIFICED_CARD,
	BOUNCED_CARD
}

static var labelDict = {
	NONE: "",
	SELF: "Self",
	HOST: "Host",
	YOUR_HAND: "Your hand",
	OPPO_HAND: "Oppo hand",
	HANDS: "All hands",
	YOUR_GRAVEYARD: "Your graveyard",
	OPPO_GRAVEYARD: "Oppo graveyard",
	GRAVEYARDS: "All graveyards",
	LANE_ALLY: "Lane allies",
	LANE_ENEMY: "Lane enemies",
	LANE: "Lane cards",
	<PERSON>THER_LANES_ALLY: "Other lanes allies",
	OTHER_LANES_ENEMY: "Other lanes enemies",
	OTHER_LANES: "Other lanes cards",
	ARENA_ALLY: "Allies",
	ARENA_ENEMY: "Enemies",
	ARENA: "Cards",
	SOURCE: "Source card",
	OTHER_LANES_ALLY_LOC: "Other lanes location",
	SACRIFICED_CARD: "Sacrificed cards",
	BOUNCED_CARD: "Bounced cards"
}

var name:int
var condition:int
var conditionTier:int

func _init(p_name:int = NONE, p_condition:int = TargetCondition.NONE, p_conditionTier:int = 0):
	self.name = p_name
	self.condition = p_condition
	self.conditionTier = p_conditionTier

func getName() -> int:
	return self.name

func getCondition() -> int:
	return self.condition

func getConditionTier() -> int:
	return self.conditionTier
