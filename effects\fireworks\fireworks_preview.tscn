[gd_scene load_steps=8 format=3 uid="uid://b3xrnuf1re758"]

[ext_resource type="PackedScene" uid="uid://cmj5l6xu36itg" path="res://turner/turner.tscn" id="1_7rdr7"]
[ext_resource type="Script" path="res://effects/fireworks/fireworks_preview.gd" id="1_v4rts"]
[ext_resource type="Material" uid="uid://m80rmki4fbox" path="res://assets/sky/skies/night_sky.tres" id="2_nvobh"]

[sub_resource type="Gradient" id="Gradient_qgt0a"]
interpolation_mode = 2
offsets = PackedFloat32Array(0, 0.28934, 0.500846, 0.751269, 1)
colors = PackedColorArray(1, 0.21, 0.21, 1, 1, 0.75, 0, 1, 0.0282, 0.94, 0.0737899, 1, 0, 0.583333, 1, 1, 0.954167, 0.45, 1, 1)

[sub_resource type="Curve" id="Curve_anc5n"]
max_value = 0.5
_data = [Vector2(0, 0.05), 0.0, 0.0, 0, 0, Vector2(0.3, 0.4), 0.0, 0.0, 0, 0, Vector2(0.6, 0.1), 0.0, 0.0, 0, 0, Vector2(0.8, 0.3), 0.0, 0.0, 0, 0, Vector2(1, 0.05), 0.0, 0.0, 0, 0]
point_count = 5

[sub_resource type="Sky" id="Sky_8xopp"]
sky_material = ExtResource("2_nvobh")

[sub_resource type="Environment" id="Environment_iulvw"]
background_mode = 2
background_color = Color(0.14902, 0.14902, 0.14902, 1)
sky = SubResource("Sky_8xopp")
ambient_light_source = 2
tonemap_mode = 2
glow_enabled = true
glow_normalized = true
glow_intensity = 1.2
glow_hdr_threshold = 0.1
glow_hdr_scale = 0.5
fog_light_color = Color(0.14902, 0.14902, 0.14902, 1)
fog_density = 0.04

[node name="FireworksPreview" type="Node3D"]
script = ExtResource("1_v4rts")
colors = SubResource("Gradient_qgt0a")
probability_curve = SubResource("Curve_anc5n")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_iulvw")

[node name="Turner" parent="." instance=ExtResource("1_7rdr7")]
low_angle = 50.0
min_zoom = 20.0
max_zoom = 35.0
_zoom = 30.0
auto_spin = true

[node name="SpawnTimer" type="Timer" parent="."]
unique_name_in_owner = true
wait_time = 0.1
autostart = true
