[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://b67h3dkdqopcy"
path="res://.godot/imported/DRC_Card_Modell_Test01.glb-9e043403b7debf18e0b9b0f2efb83e58.scn"

[deps]

source_file="res://Assets/3D models/DRC_Card_Modell_Test01.glb"
dest_files=["res://.godot/imported/DRC_Card_Modell_Test01.glb-9e043403b7debf18e0b9b0f2efb83e58.scn"]

[params]

nodes/root_type=""
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
import_script/path=""
_subresources={}
gltf/naming_version=1
gltf/embedded_image_handling=1
