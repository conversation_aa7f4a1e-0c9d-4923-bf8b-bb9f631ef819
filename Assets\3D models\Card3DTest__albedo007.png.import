[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://d4ieu84tv5umc"
path.s3tc="res://.godot/imported/Card3DTest__albedo007.png-edc85a8e5873aef0b19de33d85b527c5.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}
generator_parameters={}

[deps]

source_file="res://Assets/3D models/Card3DTest__albedo007.png"
dest_files=["res://.godot/imported/Card3DTest__albedo007.png-edc85a8e5873aef0b19de33d85b527c5.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
