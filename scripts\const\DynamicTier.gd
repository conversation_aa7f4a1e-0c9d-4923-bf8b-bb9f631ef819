class_name DynamicTier
enum {
	NONE,
	SELF_POWER,
	LANE_POWER,
	STRONGEST_ALLY_POWER,
	STRONGEST_ENEMY_POWER,
	ALL_ENEMY_WITHOUT_ABILITY,
	SACRIFICED_ALLY_POWER,
	# ALL_ALLY_WITHOUT_ABILITY, TODO
	# ALL_ENEMY_WITH_ABILITY,  TODO	
	# ALL_ALLY_WITH_ABILITY,  TODO
}

static var labelDict = {
	NONE: "",
	SELF_POWER: "equals to its power",
	LANE_POWER: "equals to this lane power",
	STRONGEST_ALLY_POWER: "equals highest ally power",
	STRONGEST_ENEMY_POWER: "equals highest enemy power",
	ALL_ENEMY_WITHOUT_ABILITY: "all enemy without ability",
	SACRIFICED_ALLY_POWER: "equals sacrificed ally power",
}

static func getDynamicTierValue(_ability: Ability, _dynamicTierType: int, _arena:Arena, _specificCards:Dictionary = {}) -> int:
	#print("getDynamicTierValue")
	match _dynamicTierType:
		SELF_POWER:
			return _ability.getParentCard().getPower()
		LANE_POWER:
			return _arena.getLaneByCard(_ability.getParentCard()).getPlayerTotalPower(_ability.getParentCard().getOwner())
		STRONGEST_ALLY_POWER:
			var alliesCards: Array[Card] = _arena.getArenaCardsByOwner(_ability.getParentCard().getOwner())
			alliesCards.erase(_ability.getParentCard())
			var highestPower = 0
			for card in alliesCards:
				if card.getPower() > highestPower:
					highestPower = card.getPower()
			return highestPower
		STRONGEST_ENEMY_POWER:
			var enemyCards: Array[Card] = _arena.getArenaCardsByOwner(TurnService.getOpponent(_ability.getParentCard().getOwner()))
			enemyCards.erase(_ability.getParentCard())
			var highestPower = 0
			for card in enemyCards:
				if card.getPower() > highestPower:
					highestPower = card.getPower()
			return highestPower
		ALL_ENEMY_WITHOUT_ABILITY:
			var enemyCards: Array[Card] = _arena.getArenaCardsByOwner(TurnService.getOpponent(_ability.getParentCard().getOwner()))
			enemyCards.erase(_ability.getParentCard())
			var adder = 0
			#print("ALL_ENEMY_WITHOUT_ABILITY size: "+str(enemyCards.size()))
			for card in enemyCards:
				if card.abilities.size() == 0:
					adder += 1
			#print("ALL_ENEMY_WITHOUT_ABILITY: "+str(adder))
			return adder
		SACRIFICED_ALLY_POWER:
			if _specificCards.has("sacrificedCard"):
				return _specificCards["sacrificedCard"].getPower()
			return 0
		_:
			return 0
