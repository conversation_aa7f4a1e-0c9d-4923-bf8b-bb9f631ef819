[gd_scene load_steps=39 format=3 uid="uid://caxhgud7mger4"]

[ext_resource type="Script" path="res://effects/fireball/fireball_impact/fireball_impact.gd" id="1_4c58t"]
[ext_resource type="Shader" path="res://effects/fireball/materials/fireball_impact_trail.gdshader" id="1_4l8ch"]
[ext_resource type="Shader" path="res://effects/fireball/materials/fireball_particles.gdshader" id="3_0qcs7"]
[ext_resource type="Texture2D" uid="uid://dblv3xi87exyb" path="res://effects/fireball/materials/fire_particles_gradient.tres" id="4_a87mk"]
[ext_resource type="Shader" path="res://effects/fireball/materials/fireball_impact_flash.gdshader" id="4_hv54w"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_gfrja"]
render_priority = 0
shader = ExtResource("1_4l8ch")

[sub_resource type="Gradient" id="Gradient_cw14b"]
offsets = PackedFloat32Array(0, 0.1, 1)
colors = PackedColorArray(1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_n3swa"]
gradient = SubResource("Gradient_cw14b")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_8awvl"]
lifetime_randomness = 0.25
direction = Vector3(0, 1, 0)
spread = 35.0
initial_velocity_min = 6.0
initial_velocity_max = 16.0
damping_min = 4.0
damping_max = 6.0
scale_min = 0.4
color = Color(1, 0.435294, 0, 1)
color_ramp = SubResource("GradientTexture1D_n3swa")
collision_mode = 1
collision_friction = 0.0
collision_bounce = 0.45
sub_emitter_mode = 1
sub_emitter_frequency = 60.0

[sub_resource type="Curve" id="Curve_8pxu3"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="RibbonTrailMesh" id="RibbonTrailMesh_dkwft"]
shape = 0
size = 0.1
section_length = 0.06
curve = SubResource("Curve_8pxu3")

[sub_resource type="Shader" id="Shader_hecxx"]
code = "shader_type spatial;
render_mode unshaded, ambient_light_disabled;

uniform sampler2D voronoi_sampler : filter_linear_mipmap, repeat_enable;

varying float id;
varying vec4 custom;

void vertex() {
	
	mat4 mat_world = mat4(normalize(INV_VIEW_MATRIX[0]), normalize(INV_VIEW_MATRIX[1]) ,normalize(INV_VIEW_MATRIX[2]), MODEL_MATRIX[3]);
	mat_world = mat_world * mat4(vec4(cos(INSTANCE_CUSTOM.x), -sin(INSTANCE_CUSTOM.x), 0.0, 0.0), vec4(sin(INSTANCE_CUSTOM.x), cos(INSTANCE_CUSTOM.x), 0.0, 0.0), vec4(0.0, 0.0, 1.0, 0.0), vec4(0.0, 0.0, 0.0, 1.0));
	MODELVIEW_MATRIX = VIEW_MATRIX * mat_world;
	MODELVIEW_MATRIX = MODELVIEW_MATRIX * mat4(vec4(length(MODEL_MATRIX[0].xyz), 0.0, 0.0, 0.0),vec4(0.0, length(MODEL_MATRIX[1].xyz), 0.0, 0.0), vec4(0.0, 0.0, length(MODEL_MATRIX[2].xyz), 0.0), vec4(0.0, 0.0, 0.0, 1.0));
	MODELVIEW_NORMAL_MATRIX = mat3(MODELVIEW_MATRIX);
	
	id = float(INSTANCE_ID);
	custom = INSTANCE_CUSTOM;
}

void fragment() {
	float voronoi = texture(voronoi_sampler, UV * 0.3 + id * 0.1 + custom.b).x;
	float dist = distance(UV, vec2(0.5));
	float dist_mask = smoothstep(0.45, 0.0, dist);
	float mask = max(0.0, dist_mask - voronoi);
	ALBEDO = COLOR.rgb;
	ALPHA = custom.y * mask * COLOR.a;
}
"

[sub_resource type="FastNoiseLite" id="FastNoiseLite_p0by2"]
noise_type = 2
fractal_octaves = 2

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_4uyxl"]
seamless = true
noise = SubResource("FastNoiseLite_p0by2")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_26l6h"]
render_priority = 0
shader = SubResource("Shader_hecxx")
shader_parameter/voronoi_sampler = SubResource("NoiseTexture2D_4uyxl")

[sub_resource type="Gradient" id="Gradient_3tj7v"]
colors = PackedColorArray(1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_b1ied"]
gradient = SubResource("Gradient_3tj7v")

[sub_resource type="Curve" id="Curve_vabli"]
_data = [Vector2(0, 0.5), 0.0, 0.0, 0, 0, Vector2(0.8, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_xfs2k"]
curve = SubResource("Curve_vabli")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_j1doh"]
lifetime_randomness = 0.1
emission_shape = 1
emission_sphere_radius = 0.15
direction = Vector3(0, 1, 0)
spread = 80.0
initial_velocity_max = 0.4
gravity = Vector3(0, 1, 0)
scale_min = 0.8
scale_max = 1.2
scale_curve = SubResource("CurveTexture_xfs2k")
color = Color(0.780392, 0.780392, 0.780392, 0.470588)
color_ramp = SubResource("GradientTexture1D_b1ied")
anim_speed_min = 0.01
anim_speed_max = 0.02
anim_offset_max = 1.0

[sub_resource type="QuadMesh" id="QuadMesh_o3qba"]
size = Vector2(0.5, 0.5)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_07f82"]
noise_type = 2
fractal_octaves = 2

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_ltcio"]
seamless = true
noise = SubResource("FastNoiseLite_07f82")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_4lgfn"]
render_priority = 0
shader = ExtResource("3_0qcs7")
shader_parameter/uv_scale = 0.3
shader_parameter/voronoi_sampler = SubResource("NoiseTexture2D_ltcio")
shader_parameter/gradient_sampler = ExtResource("4_a87mk")

[sub_resource type="Gradient" id="Gradient_imfg0"]
offsets = PackedFloat32Array(0, 0.2, 1)
colors = PackedColorArray(1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_pa4ih"]
gradient = SubResource("Gradient_imfg0")

[sub_resource type="Curve" id="Curve_87dcg"]
_data = [Vector2(0, 0.5), 0.0, 0.0, 0, 0, Vector2(0.4, 1), 0.0, 0.0, 0, 0, Vector2(1, 0.2), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_o54ba"]
curve = SubResource("Curve_87dcg")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_3jema"]
lifetime_randomness = 0.4
emission_shape = 1
emission_sphere_radius = 0.15
direction = Vector3(0, 1, 0)
spread = 80.0
initial_velocity_min = 0.4
initial_velocity_max = 2.0
gravity = Vector3(0, 1, 0)
damping_min = 0.5
damping_max = 1.0
scale_min = 0.8
scale_max = 1.2
scale_curve = SubResource("CurveTexture_o54ba")
color_ramp = SubResource("GradientTexture1D_pa4ih")
anim_speed_min = 0.1
anim_speed_max = 0.1
anim_offset_max = 1.0

[sub_resource type="QuadMesh" id="QuadMesh_cjg2g"]
size = Vector2(0.5, 0.5)

[sub_resource type="Curve" id="Curve_f3xny"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.5, 1), 4.0, -4.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="Curve" id="Curve_mc1kx"]
_data = [Vector2(0, 0.8), 0.0, 0.0, 0, 0, Vector2(0.2, 1), 0.0, 0.0, 0, 0, Vector2(0.5, 0.8), 0.0, 0.0, 0, 0, Vector2(0.7, 1), 0.0, 0.0, 0, 0, Vector2(1, 0.8), 0.0, 0.0, 0, 0]
point_count = 5

[sub_resource type="CurveXYZTexture" id="CurveXYZTexture_tn2i0"]
curve_x = SubResource("Curve_f3xny")
curve_y = SubResource("Curve_mc1kx")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_7g1to"]
resource_local_to_scene = true
render_priority = 1
shader = ExtResource("4_hv54w")
shader_parameter/base_color = Color(0.215686, 0.415686, 0.866667, 1)
shader_parameter/alpha = 1.0
shader_parameter/intensity = 1.0
shader_parameter/scale = 1.0
shader_parameter/gradient_sampler = SubResource("CurveXYZTexture_tn2i0")

[sub_resource type="QuadMesh" id="QuadMesh_cr0ao"]

[sub_resource type="Animation" id="Animation_y434m"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Anchor/FireTrail:emitting")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Anchor/FireSmoke:emitting")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Anchor/FireAmber:emitting")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Flash:material_override:shader_parameter/intensity")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.0]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Flash:material_override:shader_parameter/scale")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Flash:visible")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_y511v"]
resource_name = "default"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Anchor/FireTrail:emitting")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Anchor/FireSmoke:emitting")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Anchor/FireAmber:emitting")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Flash:material_override:shader_parameter/intensity")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.05, 0.075),
"transitions": PackedFloat32Array(-2, -2, -2),
"update": 0,
"values": [3.0, 4.0, 0.0]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Flash:material_override:shader_parameter/scale")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.05, 0.075),
"transitions": PackedFloat32Array(-2, -2, -2),
"update": 0,
"values": [0.4, 1.25, 0.4]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Flash:visible")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.075),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_6b8kc"]
_data = {
"RESET": SubResource("Animation_y434m"),
"default": SubResource("Animation_y511v")
}

[node name="FireballImpact" type="Node3D"]
transform = Transform3D(4.595, 0, 0, 0, 4.595, 0, 0, 0, 4.595, 0, 0, 0)
script = ExtResource("1_4c58t")

[node name="Anchor" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, -4.37114e-08, -1, 0, 1, -4.37114e-08, 0, 0, 0)

[node name="FireTrail" type="GPUParticles3D" parent="Anchor"]
visible = false
material_override = SubResource("ShaderMaterial_gfrja")
emitting = false
amount = 4
lifetime = 0.6
one_shot = true
preprocess = 0.03
explosiveness = 0.95
transform_align = 3
trail_enabled = true
trail_lifetime = 0.2
process_material = SubResource("ParticleProcessMaterial_8awvl")
draw_pass_1 = SubResource("RibbonTrailMesh_dkwft")

[node name="FireSmoke" type="GPUParticles3D" parent="Anchor"]
visible = false
material_override = SubResource("ShaderMaterial_26l6h")
emitting = false
amount = 16
lifetime = 1.2
one_shot = true
explosiveness = 0.8
process_material = SubResource("ParticleProcessMaterial_j1doh")
draw_pass_1 = SubResource("QuadMesh_o3qba")

[node name="FireAmber" type="GPUParticles3D" parent="Anchor"]
material_override = SubResource("ShaderMaterial_4lgfn")
emitting = false
amount = 16
lifetime = 0.6
one_shot = true
explosiveness = 0.8
process_material = SubResource("ParticleProcessMaterial_3jema")
draw_pass_1 = SubResource("QuadMesh_cjg2g")

[node name="Flash" type="MeshInstance3D" parent="."]
unique_name_in_owner = true
material_override = SubResource("ShaderMaterial_7g1to")
mesh = SubResource("QuadMesh_cr0ao")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
unique_name_in_owner = true
libraries = {
"": SubResource("AnimationLibrary_6b8kc")
}
