class_name ResourceService
extends Object

# Returns a string of icons with length 20
static func GenerateDeck(deckName:int, OwnedPlayer:Player) -> Array[Card]:
	var cardList:Array[Card] = []
	for cardInfo in DeckResource.getDeck(deckName):
		var castCondition
		if cardInfo.has("castCondition"):
			castCondition = cardInfo["castCondition"]
		else:
			castCondition = CastCondition.NORMAL
			
		var name
		if cardInfo.has("name"):
			name = cardInfo["name"]
		else:
			name = ""
		
		var color
		if cardInfo.has("color"):
			color = cardInfo["color"]
		else:
			color = 0
		
		var rarity
		if cardInfo.has("rarity"):
			rarity = cardInfo["rarity"]
		else:
			rarity = Rarity.COMMON
		
		#if not Mode.mode == Mode.TEST_CARD:
			#if rarity == Rarity.UNIQUE: continue
		
		var card = Card.new(OwnedPlayer, color, name, rarity, cardInfo["cost"], cardInfo["power"], castCondition)
		if cardInfo.has("abilities"):
			var abilities:Array[Ability] = []
			for ability in cardInfo["abilities"]:
				var text:String
				if ability.has("text"):
					text = ability["text"]
				else:
					text = ""
				var keyword:Keyword
				if ability.has("keyword"):
					var keywordName:int = ability["keyword"]["name"]
					var keywordTier:int
					var keywordDynamicTier:int
					if ability["keyword"].has("tier"):
						keywordTier = ability["keyword"]["tier"]
					else:
						keywordTier = 0
					if ability["keyword"].has("dynamicTier"):
						keywordDynamicTier = ability["keyword"]["dynamicTier"]
					else:
						keywordDynamicTier = 0
					keyword = Keyword.new(keywordName, keywordTier, keywordDynamicTier)
				else:
					keyword = Keyword.new()
				var trigger
				if ability.has("trigger"):
					var triggerName:int = ability["trigger"]["name"]
					var triggerCondition:int
					var triggerConditionTier:int
					if ability["trigger"].has("condition"):
						triggerCondition = ability["trigger"]["condition"]
					else:
						triggerCondition = TriggerCondition.NONE
					if ability["trigger"].has("conditionTier"):
						triggerConditionTier = ability["trigger"]["conditionTier"]
					else:
						triggerConditionTier = 0
					trigger = Trigger.new(triggerName, triggerCondition, triggerConditionTier)
				else:
					trigger = Trigger.new()
				var target
				if ability.has("target"):
					var targetName:int = ability["target"]["name"]
					var targetCondition:int
					var targetConditionTier:int
					if ability["target"].has("condition"):
						targetCondition = ability["target"]["condition"]
					else:
						targetCondition = TargetCondition.NONE
					if ability["target"].has("conditionTier"):
						targetConditionTier = ability["target"]["conditionTier"]
					else:
						targetConditionTier = 0
					target = Target.new(targetName, targetCondition, targetConditionTier)
				else:
					target = Target.new()
				var targetSlot
				if ability.has("targetSlot"):
					var targetName:int = ability["targetSlot"]["name"]
					var targetCondition:int
					var targetConditionTier:int
					if ability["targetSlot"].has("condition"):
						targetCondition = ability["targetSlot"]["condition"]
					else:
						targetCondition = TargetSlotCondition.NONE
					if ability["targetSlot"].has("conditionTier"):
						targetConditionTier = ability["targetSlot"]["conditionTier"]
					else:
						targetConditionTier = 0
					targetSlot = TargetSlot.new(targetName, targetCondition, targetConditionTier)
				else:
					targetSlot = TargetSlot.new()
				
				abilities.append(Ability.new(card, text, keyword, trigger, target, targetSlot))
			card.addAbilities(abilities)
		
		if cardInfo.has("givingAbilities"):
			var givingAbilities:Array[Ability] = []
			for ability in cardInfo["givingAbilities"]:
				var text:String
				if ability.has("text"):
					text = ability["text"]
				else:
					text = ""
				var keyword:Keyword
				if ability.has("keyword"):
					var keywordName:int = ability["keyword"]["name"]
					var keywordTier:int
					var keywordDynamicTier:int
					if ability["keyword"].has("tier"):
						keywordTier = ability["keyword"]["tier"]
					else:
						keywordTier = 0
					if ability["keyword"].has("dynamicTier"):
						keywordDynamicTier = ability["keyword"]["dynamicTier"]
					else:
						keywordDynamicTier = 0
					keyword = Keyword.new(keywordName, keywordTier, keywordDynamicTier)
				else:
					keyword = Keyword.new()
				var trigger
				if ability.has("trigger"):
					var triggerName:int = ability["trigger"]["name"]
					var triggerCondition:int
					var triggerConditionTier:int
					if ability["trigger"].has("condition"):
						triggerCondition = ability["trigger"]["condition"]
					else:
						triggerCondition = TriggerCondition.NONE
					if ability["trigger"].has("conditionTier"):
						triggerConditionTier = ability["trigger"]["conditionTier"]
					else:
						triggerConditionTier = 0
					trigger = Trigger.new(triggerName, triggerCondition, triggerConditionTier)
				else:
					trigger = Trigger.new()
				var target
				if ability.has("target"):
					var targetName:int = ability["target"]["name"]
					var targetCondition:int
					var targetConditionTier:int
					if ability["target"].has("condition"):
						targetCondition = ability["target"]["condition"]
					else:
						targetCondition = TargetCondition.NONE
					if ability["target"].has("conditionTier"):
						targetConditionTier = ability["target"]["conditionTier"]
					else:
						targetConditionTier = 0
					target = Target.new(targetName, targetCondition, targetConditionTier)
				else:
					target = Target.new()
				var targetSlot
				if ability.has("targetSlot"):
					var targetName:int = ability["targetSlot"]["name"]
					var targetCondition:int
					var targetConditionTier:int
					if ability["targetSlot"].has("condition"):
						targetCondition = ability["targetSlot"]["condition"]
					else:
						targetCondition = TargetSlotCondition.NONE
					if ability["targetSlot"].has("conditionTier"):
						targetConditionTier = ability["targetSlot"]["conditionTier"]
					else:
						targetConditionTier = 0
					targetSlot = TargetSlot.new(targetName, targetCondition, targetConditionTier)
				else:
					targetSlot = TargetSlot.new()
				
				givingAbilities.append(Ability.new(card, text, keyword, trigger, target, targetSlot))
			card.addGivingAbilities(givingAbilities)
		cardList.append(card)
		CardArtService.prepareCardArt(cardList)
	
	return cardList

static func GenerateLocation(_locationDataIndex:int, _ownedPlayer:Player) -> Card:
	var locationInfo = LocationData.LocationList[LocationData.DifficultySet]["events"][_locationDataIndex]
	
	var name
	if locationInfo.has("name"):
		name = locationInfo["name"]
	else:
		name = ""
			
	var rarity
	if locationInfo.has("rarity"):
		rarity = locationInfo["rarity"]
	else:
		rarity = Rarity.COMMON
	
	var generatedLocation = Card.new(_ownedPlayer, CardColor.NONE, name, rarity, 0, 0)
	if locationInfo.has("abilities"):
		var abilities:Array[Ability] = []
		for ability in locationInfo["abilities"]:
			var text:String
			if ability.has("text"):
				text = ability["text"]
			else:
				text = ""
			var keyword:Keyword
			if ability.has("keyword"):
				var keywordName:int = ability["keyword"]["name"]
				var keywordTier:int
				var keywordDynamicTier:int
				if ability["keyword"].has("tier"):
					keywordTier = ability["keyword"]["tier"]
				else:
					keywordTier = 0
				if ability["keyword"].has("dynamicTier"):
					keywordDynamicTier = ability["keyword"]["dynamicTier"]
				else:
					keywordDynamicTier = 0
				keyword = Keyword.new(keywordName, keywordTier, keywordDynamicTier)
			else:
				keyword = Keyword.new()
			var trigger
			if ability.has("trigger"):
				var triggerName:int = ability["trigger"]["name"]
				var triggerCondition:int
				var triggerConditionTier:int
				if ability["trigger"].has("condition"):
					triggerCondition = ability["trigger"]["condition"]
				else:
					triggerCondition = TriggerCondition.NONE
				if ability["trigger"].has("conditionTier"):
					triggerConditionTier = ability["trigger"]["conditionTier"]
				else:
					triggerConditionTier = 0
				trigger = Trigger.new(triggerName, triggerCondition, triggerConditionTier)
			else:
				trigger = Trigger.new()
			var target
			if ability.has("target"):
				var targetName:int = ability["target"]["name"]
				var targetCondition:int
				var targetConditionTier:int
				if ability["target"].has("condition"):
					targetCondition = ability["target"]["condition"]
				else:
					targetCondition = TargetCondition.NONE
				if ability["target"].has("conditionTier"):
					targetConditionTier = ability["target"]["conditionTier"]
				else:
					targetConditionTier = 0
				target = Target.new(targetName, targetCondition, targetConditionTier)
			else:
				target = Target.new()
			var targetSlot
			if ability.has("targetSlot"):
				var targetName:int = ability["targetSlot"]["name"]
				var targetCondition:int
				var targetConditionTier:int
				if ability["targetSlot"].has("condition"):
					targetCondition = ability["targetSlot"]["condition"]
				else:
					targetCondition = TargetSlotCondition.NONE
				if ability["targetSlot"].has("conditionTier"):
					targetConditionTier = ability["targetSlot"]["conditionTier"]
				else:
					targetConditionTier = 0
				targetSlot = TargetSlot.new(targetName, targetCondition, targetConditionTier)
			else:
				targetSlot = TargetSlot.new()
			
			abilities.append(Ability.new(generatedLocation, text, keyword, trigger, target, targetSlot))
		generatedLocation.addAbilities(abilities)
		generatedLocation.setEnableAbilities(false)
		
	if locationInfo.has("givingAbilities"):
		var givingAbilities:Array[Ability] = []
		for ability in locationInfo["givingAbilities"]:
			var text:String
			if ability.has("text"):
				text = ability["text"]
			else:
				text = ""
			var keyword:Keyword
			if ability.has("keyword"):
				var keywordName:int = ability["keyword"]["name"]
				var keywordTier:int
				var keywordDynamicTier:int
				if ability["keyword"].has("tier"):
					keywordTier = ability["keyword"]["tier"]
				else:
					keywordTier = 0
				if ability["keyword"].has("dynamicTier"):
					keywordDynamicTier = ability["keyword"]["dynamicTier"]
				else:
					keywordDynamicTier = 0
				keyword = Keyword.new(keywordName, keywordTier, keywordDynamicTier)
			else:
				keyword = Keyword.new()
			var trigger
			if ability.has("trigger"):
				var triggerName:int = ability["trigger"]["name"]
				var triggerCondition:int
				var triggerConditionTier:int
				if ability["trigger"].has("condition"):
					triggerCondition = ability["trigger"]["condition"]
				else:
					triggerCondition = TriggerCondition.NONE
				if ability["trigger"].has("conditionTier"):
					triggerConditionTier = ability["trigger"]["conditionTier"]
				else:
					triggerConditionTier = 0
				trigger = Trigger.new(triggerName, triggerCondition, triggerConditionTier)
			else:
				trigger = Trigger.new()
			var target
			if ability.has("target"):
				var targetName:int = ability["target"]["name"]
				var targetCondition:int
				var targetConditionTier:int
				if ability["target"].has("condition"):
					targetCondition = ability["target"]["condition"]
				else:
					targetCondition = TargetCondition.NONE
				if ability["target"].has("conditionTier"):
					targetConditionTier = ability["target"]["conditionTier"]
				else:
					targetConditionTier = 0
				target = Target.new(targetName, targetCondition, targetConditionTier)
			else:
				target = Target.new()
			var targetSlot
			if ability.has("targetSlot"):
				var targetName:int = ability["targetSlot"]["name"]
				var targetCondition:int
				var targetConditionTier:int
				if ability["targetSlot"].has("condition"):
					targetCondition = ability["targetSlot"]["condition"]
				else:
					targetCondition = TargetSlotCondition.NONE
				if ability["targetSlot"].has("conditionTier"):
					targetConditionTier = ability["targetSlot"]["conditionTier"]
				else:
					targetConditionTier = 0
				targetSlot = TargetSlot.new(targetName, targetCondition, targetConditionTier)
			else:
				targetSlot = TargetSlot.new()
			
			givingAbilities.append(Ability.new(generatedLocation, text, keyword, trigger, target, targetSlot))
		generatedLocation.addGivingAbilities(givingAbilities)
	CardArtService.prepareEventArt([generatedLocation])
	return generatedLocation
