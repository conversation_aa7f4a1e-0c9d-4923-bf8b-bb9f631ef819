class_name TriggerCondition

enum {
	NONE,
	ALWAYS,
	COUNT,
	FROM_TURN,
	EXACT_TURN,
	TURN_STAY,
	TURN_DRAWN,
	EXACT_TURN_STAY,
	EXACT_TURN_DRAWN,
	EVERY_X_TURN,
	MANA_EMPTY,
	MANA_LEFT,
	ALLY_DIED,
	ALONE,
	LANE_FULL,
	LESS_LANE_ALLY,
	LANE_ALLY_BASE_COST,
	LANE_ALLY_COUNT,
	IS_HOST,
	SACRIFICED_ALLY_BASE_COST,
	LANE_WINNING
}

static var labelDict = {
	NONE: "",
	ALWAYS: "",
	COUNT: "x times",
	FROM_TURN: "from turn",
	EXACT_TURN: "at turn",
	TURN_STAY: "after turn",
	TURN_DRAWN: "after turn",
	EXACT_TURN_STAY: "at turn",
	EXACT_TURN_DRAWN: "at turn",
	EVERY_X_TURN: "every x turn",
	<PERSON><PERSON>_EMPTY: "if used all mana",
	<PERSON><PERSON>_LEFT: "if has unspent mana",
	<PERSON><PERSON><PERSON>_DIED: "if ally died this turn",
	ALONE: "if alone",
	LANE_FULL: "if lane is full",
	LESS_LANE_ALLY: "if ally < enemy here",
	LANE_ALLY_BASE_COST: "if an ally here has base cost",
	LANE_ALLY_COUNT: "if ally here count",
	IS_HOST: "if host",
	SACRIFICED_ALLY_BASE_COST: "if sacrificed ally base cost",
	LANE_WINNING: "if you're winning here"
}

# Return whether the triggered condition of a card is met
static func isTriggerConditionMet(_ability:Ability, _lane:Lane, _specificCards:Dictionary = {}) -> bool:
	var triggerCard = _ability.getParentCard()
	var owner = triggerCard.getOwner()
	var conditionTier = _ability.getTrigger().getConditionTier()
	match _ability.getTrigger().getCondition():
		NONE:
			return true
		ALWAYS:
			return true
		COUNT:
			if _ability.getTrigger().getConditionTier() > 0:
				_ability.getTrigger().conditionTier -= 1
				return true
			else:
				return false
		FROM_TURN:
			return TurnService.TurnCount / 2 >= conditionTier
		EXACT_TURN:
			return TurnService.TurnCount / 2 == conditionTier
		TURN_STAY:
			return TurnService.turnCountfromPresent(triggerCard.getEnterArenaOnTurn()) / 2 >= conditionTier
		TURN_DRAWN:
			return TurnService.turnCountfromPresent(triggerCard.getDrawnOnTurn()) / 2 >= conditionTier
		EXACT_TURN_STAY:
			return TurnService.turnCountfromPresent(triggerCard.getEnterArenaOnTurn()) / 2 == conditionTier
		EXACT_TURN_DRAWN:
			return TurnService.turnCountfromPresent(triggerCard.getDrawnOnTurn()) / 2 == conditionTier
		EVERY_X_TURN:
			return (TurnService.turnCountfromPresent(triggerCard.getEnterArenaOnTurn()) / 2) % conditionTier == 0
		MANA_EMPTY:
			return owner.getManaAndTempMana() <= 0
		MANA_LEFT:
			return owner.getManaAndTempMana() > 0
		ALLY_DIED:
			var allyCards:Array[Card] = owner.graveyard.getCards()
			for card in allyCards:
				if TurnService.turnCountfromPresent(card.getDieOnTurn()) == conditionTier:
					return true
			return false
		ALONE:
			return _lane.getTopCardsByPlayer(owner).size() == 1
		LANE_FULL:
			return _lane.isFull()
		LESS_LANE_ALLY:
			return _lane.getTopCardsByPlayer(owner).size() < _lane.getTopCardsByPlayer(TurnService.getOpponent(owner)).size()
		LANE_ALLY_BASE_COST:
			var allyCards: Array[Card] = _lane.getTopCardsByPlayer(owner)
			allyCards.erase(_ability.getParentCard())
			for card in allyCards:
				if card.getBaseCost() == conditionTier:
					return true
			return false
		LANE_ALLY_COUNT:
			return _lane.getTopCardsByPlayer(owner).size() == conditionTier
		IS_HOST:
			return _lane.getCardSlotByCard(triggerCard).getCardCount() > 1
		SACRIFICED_ALLY_BASE_COST:
			if _specificCards.has("sacrificedCard"):
				var sacrificedCard:Card = _specificCards["sacrificedCard"]
				return sacrificedCard.getBaseCost() == conditionTier
			return false
		LANE_WINNING:
			return _lane.isPlayerWinning(owner)
		_:
			return true
