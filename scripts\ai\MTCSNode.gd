class_name MTCSNode
extends Object
# This class implements a node in the Monte Carlo Tree Search (MCTS) algorithm.

var state: State # The current state of the game.
var parent: MTCSNode # The parent node of this node.
var parent_action: Action # The action taken to reach this node from the parent node.
var children: Array[MTCSNode] # The child nodes of this node.
var _number_of_visits: int # The number of times this node has been visited.
var _results = {} # The results of the simulations for this node.
var _untried_actions: Array[Action] # The actions that have not been tried yet from this node.
var current_depth: int # The current depth of the node in the tree.
var max_depth: int # The maximum depth of the tree.


func _init(_state: State, _parent: MTCSNode, _parent_action: Action, _current_depth: int = 0, _max_depth: int = 4):
    self.state = _state
    self.parent = _parent
    self.parent_action = _parent_action
    self.children = []
    self._number_of_visits = 0
    self._results = defaultdict(int)
    self._results[1] = 0
    self._results[-1] = 0
    self._untried_actions = None
    self._untried_actions = self.untried_actions()
    self.current_depth = 0
    self.max_depth = 4

func untried_actions():
    self._untried_actions = self.state.get_legal_actions()
    return self._untried_actions

func q():
    wins = self._results[1]
    loses = self._results[-1]
    return wins - loses

func n():
    return self._number_of_visits

func expand():
    action = self._untried_actions.pop()
    next_state = self.state.move(action)
    child_node = MTCSNode(next_state, self, action)
    self.children.append(child_node)
    return child_node

func is_terminal_node():
    return self.state.is_game_over()

func rollout():
    current_rollout_state = self.state
    
    while not current_rollout_state.is_game_over():
        possible_moves = current_rollout_state.get_legal_actions()
        
        action = self.rollout_policy(possible_moves)
        current_rollout_state = current_rollout_state.move(action)
    return current_rollout_state.game_result()

func backpropagate(result):
    self._number_of_visits += 1.
    self._results[result] += 1.
    if self.parent:
        self.parent.backpropagate(result)

func is_fully_expanded():
    return len(self._untried_actions) == 0

func best_child(c_param = 0.1):
    choices_weights = [(c.q() / c.n()) + c_param * np.sqrt((2 * np.log(self.n()) / c.n())) for c in self.children]
    return self.children[np.argmax(choices_weights)]

func rollout_policy(possible_moves):
    return possible_moves[np.random.randint(len(possible_moves))]


func _tree_policy():
    current_node = self
    while not current_node.is_terminal_node():
        if not current_node.is_fully_expanded():
            return current_node.expand()
        else:
            current_node = current_node.best_child()
    return current_node

func best_action():
    simulation_no = 100
	
	
    for i in range(simulation_no):
        v = self._tree_policy()
        reward = v.rollout()
        v.backpropagate(reward)
	
    return self.best_child(0)

func get_legal_actions():
    #todo
    return null

func is_game_over():
    #todo
    return null

func game_result():
    #todo
    return null

func move(action):
    #todo
    return null
