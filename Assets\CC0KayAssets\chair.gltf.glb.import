[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://ismtkjci4yh3"
path="res://.godot/imported/chair.gltf.glb-91e8dc0b23f48c5ae52e574548cba3ef.scn"

[deps]

source_file="res://Assets/CC0KayAssets/chair.gltf.glb"
dest_files=["res://.godot/imported/chair.gltf.glb-91e8dc0b23f48c5ae52e574548cba3ef.scn"]

[params]

nodes/root_type="Node3D"
nodes/root_name="Scene Root"
nodes/apply_root_scale=true
nodes/root_scale=1.0
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=false
animation/remove_immutable_tracks=true
import_script/path=""
_subresources={}
gltf/naming_version=0
gltf/embedded_image_handling=1
