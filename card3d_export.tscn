[gd_scene load_steps=67 format=3 uid="uid://co2bqhueass4e"]

[ext_resource type="Texture2D" uid="uid://busm5teuncg02" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardCost.png" id="20_shyly"]
[ext_resource type="Texture2D" uid="uid://cupg7kqh2h68u" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardHeader.png" id="21_xnfke"]
[ext_resource type="Texture2D" uid="uid://gxyksdbb8n6j" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardPower.png" id="22_vy3pd"]
[ext_resource type="Texture2D" uid="uid://de12upeq5a5b5" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardFaction.png" id="23_uu5ee"]
[ext_resource type="Texture2D" uid="uid://bnkfwry6xp8g4" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_CardRarity_Texture_Test01.png" id="24_8wcg0"]
[ext_resource type="Texture2D" uid="uid://cbupndnm5yqi5" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardCompanion.png" id="25_ttghd"]
[ext_resource type="Texture2D" uid="uid://5umvgofvyapu" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_Design_08_UI_CMN_CardBack.png" id="27_s71tq"]
[ext_resource type="Texture2D" uid="uid://cfani3jl2h0dg" path="res://Assets/3D models/DRC_Card_Model_Test03_CardBack_DRC_Card_Design_08_UI_CMN_CardBack.png" id="28_wxdo1"]
[ext_resource type="Texture2D" uid="uid://bxlo1dluc4077" path="res://Assets/3D models/DRC_Card_Model_Test03_CardBase_DRC_Card_Design_08_UI_CMN_CardBack.png" id="29_4dfqf"]
[ext_resource type="Texture2D" uid="uid://d0nibat8fxxxo" path="res://Assets/3D models/DRC_Card_Modell_Test01_DRC_Card_CardPicFG_Texture_Test01.png" id="30_rdgme"]
[ext_resource type="Texture2D" uid="uid://rbxlqmm5j2r4" path="res://Assets/3D models/DRC_Card_Model_Test03_CardPicFG_DRC_Card_CardPicFG_Texture_Test01.png" id="31_va4gs"]
[ext_resource type="Texture2D" uid="uid://bc882dp6j0iwa" path="res://Assets/3D models/DRC_Card_Model_Test03_CardPicFgPLayed_DRC_Card_CardPicFG_Texture_Test01.png" id="32_jwawa"]

[sub_resource type="ViewportTexture" id="ViewportTexture_04poi"]
resource_name = "_albedo"
viewport_path = NodePath("AbilityOnCard/AbilitySubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_titv1"]
resource_local_to_scene = true
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_04poi")
texture_filter = 1

[sub_resource type="PlaneMesh" id="PlaneMesh_8wjqt"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_2mthu"]
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("20_shyly")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_2vtsw"]
resource_name = "CardCost"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("20_shyly")

[sub_resource type="ArrayMesh" id="ArrayMesh_o7oa7"]
_surfaces = [{
"aabb": AABB(0.0452289, 1.57067, 0.593072, 1.00508e-05, 0.933744, 1.18816),
"format": 34896613377,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1.29048e-08, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"name": "CardCost",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(84, 1, 46, 241, 106, 243, 0, 0, 0, 0, 0, 0, 255, 255, 0, 0, 84, 1, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 84, 1, 255, 255, 255, 127, 0, 0, 194, 0, 255, 127, 255, 255, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 194, 0, 255, 127, 0, 0, 0, 0, 194, 0, 255, 127, 255, 127, 0, 0, 84, 1, 255, 255, 255, 191, 0, 0, 145, 0, 255, 63, 255, 255, 0, 0, 0, 0, 0, 0, 255, 63, 0, 0, 242, 0, 255, 191, 0, 0, 0, 0, 84, 1, 255, 255, 255, 63, 0, 0, 242, 0, 255, 191, 255, 255, 0, 0, 0, 0, 0, 0, 255, 191, 0, 0, 145, 0, 255, 63, 0, 0, 0, 0, 194, 0, 255, 127, 255, 63, 0, 0, 194, 0, 255, 127, 255, 191, 0, 0, 242, 0, 255, 191, 255, 127, 0, 0, 145, 0, 255, 63, 255, 127, 0, 0, 145, 0, 255, 63, 255, 191, 0, 0, 242, 0, 255, 191, 255, 191, 0, 0, 242, 0, 255, 191, 255, 63, 0, 0, 145, 0, 255, 63, 255, 63, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_7urhl"]
resource_name = "DRC_Card_Modell_Test01_Plane_001"
_surfaces = [{
"aabb": AABB(0.0452289, 1.57067, 0.593072, 1.00508e-05, 0.933744, 1.18816),
"attribute_data": PackedByteArray(207, 34, 212, 26, 125, 23, 236, 242, 212, 253, 142, 13, 212, 253, 236, 242, 169, 138, 142, 13, 125, 23, 61, 128, 169, 138, 236, 242, 212, 253, 61, 128, 169, 138, 61, 128, 19, 81, 142, 13, 125, 23, 148, 185, 62, 196, 236, 242, 212, 253, 229, 70, 62, 196, 142, 13, 125, 23, 229, 70, 19, 81, 236, 242, 212, 253, 148, 185, 62, 196, 61, 128, 19, 81, 61, 128, 169, 138, 229, 70, 169, 138, 148, 185, 19, 81, 148, 185, 19, 81, 229, 70, 62, 196, 229, 70, 62, 196, 148, 185),
"format": 34896613399,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1.29048e-08, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"material": SubResource("StandardMaterial3D_2vtsw"),
"name": "CardCost",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(84, 1, 46, 241, 106, 243, 255, 191, 0, 0, 0, 0, 255, 255, 255, 191, 84, 1, 255, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 84, 1, 255, 255, 255, 127, 255, 191, 194, 0, 255, 127, 255, 255, 255, 191, 0, 0, 0, 0, 255, 127, 255, 191, 194, 0, 255, 127, 0, 0, 255, 191, 194, 0, 255, 127, 255, 127, 255, 191, 84, 1, 255, 255, 255, 191, 255, 191, 145, 0, 255, 63, 255, 255, 255, 191, 0, 0, 0, 0, 255, 63, 255, 191, 242, 0, 255, 191, 0, 0, 255, 191, 84, 1, 255, 255, 255, 63, 255, 191, 242, 0, 255, 191, 255, 255, 255, 191, 0, 0, 0, 0, 255, 191, 255, 191, 145, 0, 255, 63, 0, 0, 255, 191, 194, 0, 255, 127, 255, 63, 255, 191, 194, 0, 255, 127, 255, 191, 255, 191, 242, 0, 255, 191, 255, 127, 255, 191, 145, 0, 255, 63, 255, 127, 255, 191, 145, 0, 255, 63, 255, 191, 255, 191, 242, 0, 255, 191, 255, 191, 255, 191, 242, 0, 255, 191, 255, 63, 255, 191, 145, 0, 255, 63, 255, 63, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_o7oa7")

[sub_resource type="ViewportTexture" id="ViewportTexture_jwjgq"]
resource_name = "_albedo"
viewport_path = NodePath("CostOnCard/CostSubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_bqove"]
resource_local_to_scene = true
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_jwjgq")
texture_filter = 1

[sub_resource type="ViewportTexture" id="ViewportTexture_yoww3"]
resource_name = "_albedo"
viewport_path = NodePath("PowerOnCard/PowerSubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_7u0ls"]
resource_local_to_scene = true
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_yoww3")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_3tcp4"]
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("22_vy3pd")
texture_filter = 1
texture_repeat = false

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_c8kxe"]
resource_name = "CardPower"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("22_vy3pd")

[sub_resource type="ArrayMesh" id="ArrayMesh_8535f"]
_surfaces = [{
"aabb": AABB(0.0435666, -2.40294, 0.887948, 1.00583e-05, 1.52653, 0.922637),
"format": 34896613377,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1.25188e-10, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"name": "CardPower",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(132, 1, 18, 255, 254, 255, 0, 0, 0, 0, 0, 0, 254, 255, 0, 0, 132, 1, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 132, 1, 255, 255, 255, 127, 0, 0, 194, 0, 255, 127, 254, 255, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 194, 0, 255, 127, 0, 0, 0, 0, 194, 0, 255, 127, 255, 127, 0, 0, 132, 1, 255, 255, 254, 191, 0, 0, 97, 0, 255, 63, 254, 255, 0, 0, 0, 0, 0, 0, 255, 63, 0, 0, 35, 1, 255, 191, 0, 0, 0, 0, 132, 1, 255, 255, 255, 63, 0, 0, 35, 1, 255, 191, 254, 255, 0, 0, 0, 0, 0, 0, 254, 191, 0, 0, 97, 0, 255, 63, 0, 0, 0, 0, 194, 0, 255, 127, 255, 63, 0, 0, 194, 0, 255, 127, 254, 191, 0, 0, 35, 1, 255, 191, 255, 127, 0, 0, 97, 0, 255, 63, 255, 127, 0, 0, 97, 0, 255, 63, 254, 191, 0, 0, 35, 1, 255, 191, 254, 191, 0, 0, 35, 1, 255, 191, 255, 63, 0, 0, 97, 0, 255, 63, 255, 63, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_atnjb"]
resource_name = "DRC_Card_Modell_Test01_Plane_003"
_surfaces = [{
"aabb": AABB(0.0435666, -2.40294, 0.887948, 1.00583e-05, 1.52653, 0.922637),
"attribute_data": PackedByteArray(152, 144, 29, 126, 152, 144, 255, 255, 255, 255, 164, 125, 255, 255, 255, 255, 75, 200, 164, 125, 152, 144, 209, 190, 75, 200, 255, 255, 255, 255, 209, 190, 75, 200, 209, 190, 113, 172, 164, 125, 152, 144, 104, 223, 37, 228, 255, 255, 255, 255, 59, 158, 37, 228, 164, 125, 152, 144, 59, 158, 113, 172, 255, 255, 255, 255, 104, 223, 37, 228, 209, 190, 113, 172, 209, 190, 75, 200, 59, 158, 75, 200, 104, 223, 113, 172, 104, 223, 113, 172, 59, 158, 37, 228, 59, 158, 37, 228, 104, 223),
"format": 34896613399,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1.25188e-10, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"material": SubResource("StandardMaterial3D_c8kxe"),
"name": "CardPower",
"primitive": 3,
"uv_scale": Vector4(1.81925, 2.02753, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(132, 1, 18, 255, 254, 255, 255, 191, 0, 0, 0, 0, 254, 255, 255, 191, 132, 1, 255, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 132, 1, 255, 255, 255, 127, 255, 191, 194, 0, 255, 127, 254, 255, 255, 191, 0, 0, 0, 0, 255, 127, 255, 191, 194, 0, 255, 127, 0, 0, 255, 191, 194, 0, 255, 127, 255, 127, 255, 191, 132, 1, 255, 255, 254, 191, 255, 191, 97, 0, 255, 63, 254, 255, 255, 191, 0, 0, 0, 0, 255, 63, 255, 191, 35, 1, 255, 191, 0, 0, 255, 191, 132, 1, 255, 255, 255, 63, 255, 191, 35, 1, 255, 191, 254, 255, 255, 191, 0, 0, 0, 0, 254, 191, 255, 191, 97, 0, 255, 63, 0, 0, 255, 191, 194, 0, 255, 127, 255, 63, 255, 191, 194, 0, 255, 127, 254, 191, 255, 191, 35, 1, 255, 191, 255, 127, 255, 191, 97, 0, 255, 63, 255, 127, 255, 191, 97, 0, 255, 63, 254, 191, 255, 191, 35, 1, 255, 191, 254, 191, 255, 191, 35, 1, 255, 191, 255, 63, 255, 191, 97, 0, 255, 63, 255, 63, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_8535f")

[sub_resource type="ViewportTexture" id="ViewportTexture_bct5c"]
resource_name = "_albedo"
viewport_path = NodePath("NameOnCard/NameSubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ujq4y"]
resource_local_to_scene = true
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_bct5c")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_u0viu"]
transparency = 1
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("21_xnfke")
texture_filter = 1
texture_repeat = false

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_qgdib"]
resource_name = "CardHeader"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("21_xnfke")

[sub_resource type="ArrayMesh" id="ArrayMesh_jumra"]
_surfaces = [{
"aabb": AABB(0.0423165, 1.69666, -1.694, 1.00285e-05, 0.65001, 3.38801),
"format": 34896613377,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1e-10, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"name": "CardHeader",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(194, 0, 253, 255, 254, 255, 0, 0, 0, 0, 0, 0, 254, 255, 0, 0, 194, 0, 253, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 194, 0, 253, 255, 255, 127, 0, 0, 97, 0, 254, 127, 254, 255, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 97, 0, 254, 127, 0, 0, 0, 0, 97, 0, 254, 127, 255, 127, 0, 0, 194, 0, 253, 255, 255, 191, 0, 0, 48, 0, 255, 63, 254, 255, 0, 0, 0, 0, 0, 0, 255, 63, 0, 0, 146, 0, 254, 191, 0, 0, 0, 0, 194, 0, 253, 255, 255, 63, 0, 0, 146, 0, 254, 191, 254, 255, 0, 0, 0, 0, 0, 0, 255, 191, 0, 0, 48, 0, 255, 63, 0, 0, 0, 0, 97, 0, 254, 127, 255, 63, 0, 0, 97, 0, 254, 127, 255, 191, 0, 0, 146, 0, 254, 191, 255, 127, 0, 0, 48, 0, 255, 63, 255, 127, 0, 0, 48, 0, 255, 63, 255, 191, 0, 0, 146, 0, 254, 191, 255, 191, 0, 0, 146, 0, 254, 191, 255, 63, 0, 0, 48, 0, 255, 63, 255, 63, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_jj8nb"]
resource_name = "DRC_Card_Modell_Test01_Plane_002"
_surfaces = [{
"aabb": AABB(0.0423165, 1.69666, -1.694, 1.00285e-05, 0.65001, 3.38801),
"attribute_data": PackedByteArray(199, 127, 132, 125, 199, 127, 255, 255, 255, 255, 132, 125, 255, 255, 255, 255, 227, 191, 132, 125, 199, 127, 193, 190, 227, 191, 255, 255, 255, 255, 193, 190, 227, 191, 193, 190, 213, 159, 132, 125, 199, 127, 96, 223, 241, 223, 255, 255, 255, 255, 34, 158, 241, 223, 132, 125, 199, 127, 34, 158, 213, 159, 255, 255, 255, 255, 96, 223, 241, 223, 193, 190, 213, 159, 193, 190, 227, 191, 34, 158, 227, 191, 96, 223, 213, 159, 96, 223, 213, 159, 34, 158, 241, 223, 34, 158, 241, 223, 96, 223),
"format": 34896613399,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [1e-10, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"material": SubResource("StandardMaterial3D_qgdib"),
"name": "CardHeader",
"primitive": 3,
"uv_scale": Vector4(1.99957, 2.00723, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(194, 0, 253, 255, 254, 255, 255, 191, 0, 0, 0, 0, 254, 255, 255, 191, 194, 0, 253, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 194, 0, 253, 255, 255, 127, 255, 191, 97, 0, 254, 127, 254, 255, 255, 191, 0, 0, 0, 0, 255, 127, 255, 191, 97, 0, 254, 127, 0, 0, 255, 191, 97, 0, 254, 127, 255, 127, 255, 191, 194, 0, 253, 255, 255, 191, 255, 191, 48, 0, 255, 63, 254, 255, 255, 191, 0, 0, 0, 0, 255, 63, 255, 191, 146, 0, 254, 191, 0, 0, 255, 191, 194, 0, 253, 255, 255, 63, 255, 191, 146, 0, 254, 191, 254, 255, 255, 191, 0, 0, 0, 0, 255, 191, 255, 191, 48, 0, 255, 63, 0, 0, 255, 191, 97, 0, 254, 127, 255, 63, 255, 191, 97, 0, 254, 127, 255, 191, 255, 191, 146, 0, 254, 191, 255, 127, 255, 191, 48, 0, 255, 63, 255, 127, 255, 191, 48, 0, 255, 63, 255, 191, 255, 191, 146, 0, 254, 191, 255, 191, 255, 191, 146, 0, 254, 191, 255, 63, 255, 191, 48, 0, 255, 63, 255, 63, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_jumra")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_4q0or"]
resource_local_to_scene = true
transparency = 1
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("30_rdgme")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_4vwjp"]
resource_name = "CardPicFG"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("31_va4gs")

[sub_resource type="ArrayMesh" id="ArrayMesh_qdx37"]
_surfaces = [{
"aabb": AABB(0.0495976, -2.18494, -1.66119, 9.99868e-06, 3.89915, 3.34224),
"format": 34896613377,
"index_count": 18,
"index_data": PackedByteArray(2, 0, 7, 0, 1, 0, 2, 0, 3, 0, 7, 0, 3, 0, 6, 0, 7, 0, 3, 0, 0, 0, 6, 0, 0, 0, 4, 0, 6, 0, 0, 0, 5, 0, 4, 0),
"name": "CardPicFG",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray(0, 0, 189, 2, 41, 4, 0, 0, 0, 0, 255, 255, 255, 255, 0, 0, 0, 0, 255, 255, 0, 0, 0, 0, 0, 0, 68, 9, 0, 0, 0, 0, 0, 0, 0, 0, 255, 255, 0, 0, 0, 0, 0, 0, 165, 11, 0, 0, 0, 0, 189, 2, 255, 255, 0, 0, 0, 0, 68, 9, 255, 255, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_imadi"]
resource_name = "DRC_Card_Model_Test03_CardPicFG_Cube_002"
_surfaces = [{
"aabb": AABB(0.0495976, -2.18494, -1.66119, 9.99868e-06, 3.89915, 3.34224),
"attribute_data": PackedByteArray(106, 210, 141, 252, 192, 42, 82, 34, 48, 213, 82, 34, 48, 213, 237, 246, 192, 42, 233, 254, 111, 205, 233, 254, 192, 42, 141, 252, 192, 42, 237, 246),
"format": 34896613399,
"index_count": 18,
"index_data": PackedByteArray(2, 0, 7, 0, 1, 0, 2, 0, 3, 0, 7, 0, 3, 0, 6, 0, 7, 0, 3, 0, 0, 0, 6, 0, 0, 0, 4, 0, 6, 0, 0, 0, 5, 0, 4, 0),
"material": SubResource("StandardMaterial3D_4vwjp"),
"name": "CardPicFG",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray(0, 0, 189, 2, 41, 4, 255, 191, 0, 0, 255, 255, 255, 255, 255, 191, 0, 0, 255, 255, 0, 0, 255, 191, 0, 0, 68, 9, 0, 0, 255, 191, 0, 0, 0, 0, 255, 255, 255, 191, 0, 0, 0, 0, 165, 11, 255, 191, 0, 0, 189, 2, 255, 255, 255, 191, 0, 0, 68, 9, 255, 255, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_qdx37")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_47vpe"]
transparency = 1
cull_mode = 1
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("27_s71tq")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_mqgww"]
resource_name = "CardBack"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("28_wxdo1")

[sub_resource type="ArrayMesh" id="ArrayMesh_7w64u"]
_surfaces = [{
"aabb": AABB(-0.0386337, -2.49985, -1.78976, 9.99868e-06, 4.9997, 3.57952),
"format": 34896613377,
"index_count": 174,
"index_data": PackedByteArray(3, 0, 7, 0, 12, 0, 3, 0, 0, 0, 7, 0, 1, 0, 23, 0, 5, 0, 1, 0, 22, 0, 23, 0, 13, 0, 27, 0, 14, 0, 13, 0, 26, 0, 27, 0, 14, 0, 28, 0, 12, 0, 14, 0, 27, 0, 28, 0, 17, 0, 30, 0, 15, 0, 17, 0, 29, 0, 30, 0, 16, 0, 29, 0, 17, 0, 16, 0, 31, 0, 29, 0, 8, 0, 33, 0, 6, 0, 8, 0, 32, 0, 33, 0, 7, 0, 32, 0, 8, 0, 7, 0, 34, 0, 32, 0, 10, 0, 36, 0, 11, 0, 10, 0, 35, 0, 36, 0, 11, 0, 37, 0, 9, 0, 11, 0, 36, 0, 37, 0, 0, 0, 34, 0, 7, 0, 0, 0, 19, 0, 34, 0, 6, 0, 20, 0, 38, 0, 6, 0, 33, 0, 20, 0, 9, 0, 22, 0, 1, 0, 9, 0, 37, 0, 22, 0, 2, 0, 35, 0, 10, 0, 2, 0, 21, 0, 35, 0, 12, 0, 18, 0, 3, 0, 12, 0, 28, 0, 18, 0, 39, 0, 26, 0, 13, 0, 39, 0, 25, 0, 26, 0, 5, 0, 31, 0, 16, 0, 5, 0, 23, 0, 31, 0, 15, 0, 24, 0, 4, 0, 15, 0, 30, 0, 24, 0, 2, 0, 15, 0, 4, 0, 2, 0, 10, 0, 15, 0, 10, 0, 17, 0, 15, 0, 10, 0, 11, 0, 17, 0, 11, 0, 16, 0, 17, 0, 11, 0, 9, 0, 16, 0, 9, 0, 5, 0, 16, 0, 9, 0, 1, 0, 5, 0, 3, 0, 19, 0, 0, 0, 3, 0, 18, 0, 19, 0, 7, 0, 14, 0, 12, 0, 7, 0, 8, 0, 14, 0, 8, 0, 13, 0, 14, 0, 8, 0, 6, 0, 13, 0, 39, 0, 24, 0, 25, 0, 39, 0, 4, 0, 24, 0, 38, 0, 21, 0, 2, 0, 38, 0, 20, 0, 21, 0, 6, 0, 39, 0, 13, 0, 6, 0, 38, 0, 39, 0, 38, 0, 4, 0, 39, 0, 38, 0, 2, 0, 4, 0),
"lods": [1e-10, PackedByteArray(27, 0, 28, 0, 34, 0, 34, 0, 28, 0, 18, 0, 34, 0, 18, 0, 19, 0, 34, 0, 32, 0, 27, 0, 32, 0, 26, 0, 27, 0, 32, 0, 33, 0, 26, 0, 33, 0, 25, 0, 26, 0, 33, 0, 20, 0, 25, 0, 20, 0, 24, 0, 25, 0, 20, 0, 21, 0, 24, 0, 21, 0, 30, 0, 24, 0, 21, 0, 35, 0, 30, 0, 35, 0, 29, 0, 30, 0, 35, 0, 36, 0, 29, 0, 36, 0, 31, 0, 29, 0, 36, 0, 37, 0, 31, 0, 37, 0, 23, 0, 31, 0, 37, 0, 22, 0, 23, 0)],
"name": "CardBack",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray(0, 0, 185, 0, 147, 244, 0, 0, 0, 0, 69, 255, 146, 244, 0, 0, 0, 0, 174, 244, 69, 255, 0, 0, 0, 0, 185, 0, 108, 11, 0, 0, 0, 0, 174, 244, 185, 0, 0, 0, 0, 0, 69, 255, 108, 11, 0, 0, 0, 0, 232, 7, 251, 254, 0, 0, 0, 0, 44, 2, 37, 249, 0, 0, 0, 0, 148, 4, 156, 252, 0, 0, 0, 0, 209, 253, 9, 249, 0, 0, 0, 0, 28, 248, 56, 255, 0, 0, 0, 0, 102, 251, 229, 252, 0, 0, 0, 0, 44, 2, 217, 6, 0, 0, 0, 0, 232, 7, 3, 1, 0, 0, 0, 0, 148, 4, 98, 3, 0, 0, 0, 0, 28, 248, 198, 0, 0, 0, 0, 0, 209, 253, 245, 6, 0, 0, 0, 0, 102, 251, 25, 3, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 79, 11, 255, 255, 0, 0, 0, 0, 175, 244, 255, 255, 0, 0, 0, 0, 255, 255, 175, 244, 0, 0, 0, 0, 255, 255, 79, 11, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 166, 7, 79, 0, 0, 0, 0, 0, 14, 4, 221, 2, 0, 0, 0, 0, 131, 1, 134, 6, 0, 0, 0, 0, 240, 251, 151, 2, 0, 0, 0, 0, 88, 248, 13, 0, 0, 0, 0, 0, 123, 254, 166, 6, 0, 0, 0, 0, 14, 4, 33, 253, 0, 0, 0, 0, 166, 7, 175, 255, 0, 0, 0, 0, 131, 1, 120, 249, 0, 0, 0, 0, 88, 248, 241, 255, 0, 0, 0, 0, 240, 251, 103, 253, 0, 0, 0, 0, 123, 254, 88, 249, 0, 0, 0, 0, 87, 11, 69, 255, 0, 0, 0, 0, 87, 11, 185, 0, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_5cvg6"]
resource_name = "DRC_Card_Model_Test03_CardBack_Cube_004"
_surfaces = [{
"aabb": AABB(-0.0386337, -2.49985, -1.78976, 9.99868e-06, 4.9997, 3.57952),
"attribute_data": PackedByteArray(26, 242, 239, 251, 25, 242, 149, 4, 139, 252, 223, 14, 118, 14, 239, 251, 5, 4, 223, 14, 119, 14, 149, 4, 67, 252, 243, 244, 145, 246, 134, 250, 243, 249, 48, 248, 117, 246, 254, 5, 127, 252, 138, 11, 58, 250, 87, 8, 255, 9, 134, 250, 77, 4, 243, 244, 157, 6, 48, 248, 17, 4, 138, 11, 27, 10, 254, 5, 86, 6, 87, 8, 90, 14, 163, 252, 54, 242, 163, 252, 65, 253, 166, 241, 65, 253, 222, 14, 54, 242, 224, 3, 90, 14, 224, 3, 79, 3, 222, 14, 79, 3, 166, 241, 157, 3, 52, 245, 28, 6, 178, 248, 174, 9, 42, 251, 215, 5, 209, 7, 93, 3, 80, 11, 205, 9, 89, 5, 116, 250, 178, 248, 243, 252, 52, 245, 226, 246, 42, 251, 51, 253, 80, 11, 185, 250, 209, 7, 195, 246, 89, 5, 139, 252, 158, 241, 5, 4, 158, 241),
"format": 34896613399,
"index_count": 174,
"index_data": PackedByteArray(3, 0, 7, 0, 12, 0, 3, 0, 0, 0, 7, 0, 1, 0, 23, 0, 5, 0, 1, 0, 22, 0, 23, 0, 13, 0, 27, 0, 14, 0, 13, 0, 26, 0, 27, 0, 14, 0, 28, 0, 12, 0, 14, 0, 27, 0, 28, 0, 17, 0, 30, 0, 15, 0, 17, 0, 29, 0, 30, 0, 16, 0, 29, 0, 17, 0, 16, 0, 31, 0, 29, 0, 8, 0, 33, 0, 6, 0, 8, 0, 32, 0, 33, 0, 7, 0, 32, 0, 8, 0, 7, 0, 34, 0, 32, 0, 10, 0, 36, 0, 11, 0, 10, 0, 35, 0, 36, 0, 11, 0, 37, 0, 9, 0, 11, 0, 36, 0, 37, 0, 0, 0, 34, 0, 7, 0, 0, 0, 19, 0, 34, 0, 6, 0, 20, 0, 38, 0, 6, 0, 33, 0, 20, 0, 9, 0, 22, 0, 1, 0, 9, 0, 37, 0, 22, 0, 2, 0, 35, 0, 10, 0, 2, 0, 21, 0, 35, 0, 12, 0, 18, 0, 3, 0, 12, 0, 28, 0, 18, 0, 39, 0, 26, 0, 13, 0, 39, 0, 25, 0, 26, 0, 5, 0, 31, 0, 16, 0, 5, 0, 23, 0, 31, 0, 15, 0, 24, 0, 4, 0, 15, 0, 30, 0, 24, 0, 2, 0, 15, 0, 4, 0, 2, 0, 10, 0, 15, 0, 10, 0, 17, 0, 15, 0, 10, 0, 11, 0, 17, 0, 11, 0, 16, 0, 17, 0, 11, 0, 9, 0, 16, 0, 9, 0, 5, 0, 16, 0, 9, 0, 1, 0, 5, 0, 3, 0, 19, 0, 0, 0, 3, 0, 18, 0, 19, 0, 7, 0, 14, 0, 12, 0, 7, 0, 8, 0, 14, 0, 8, 0, 13, 0, 14, 0, 8, 0, 6, 0, 13, 0, 39, 0, 24, 0, 25, 0, 39, 0, 4, 0, 24, 0, 38, 0, 21, 0, 2, 0, 38, 0, 20, 0, 21, 0, 6, 0, 39, 0, 13, 0, 6, 0, 38, 0, 39, 0, 38, 0, 4, 0, 39, 0, 38, 0, 2, 0, 4, 0),
"lods": [1e-10, PackedByteArray(27, 0, 28, 0, 34, 0, 34, 0, 28, 0, 18, 0, 34, 0, 18, 0, 19, 0, 34, 0, 32, 0, 27, 0, 32, 0, 26, 0, 27, 0, 32, 0, 33, 0, 26, 0, 33, 0, 25, 0, 26, 0, 33, 0, 20, 0, 25, 0, 20, 0, 24, 0, 25, 0, 20, 0, 21, 0, 24, 0, 21, 0, 30, 0, 24, 0, 21, 0, 35, 0, 30, 0, 35, 0, 29, 0, 30, 0, 35, 0, 36, 0, 29, 0, 36, 0, 31, 0, 29, 0, 36, 0, 37, 0, 31, 0, 37, 0, 23, 0, 31, 0, 37, 0, 22, 0, 23, 0)],
"material": SubResource("StandardMaterial3D_mqgww"),
"name": "CardBack",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 40,
"vertex_data": PackedByteArray(0, 0, 185, 0, 147, 244, 0, 0, 0, 0, 69, 255, 146, 244, 0, 0, 0, 0, 174, 244, 69, 255, 0, 0, 0, 0, 185, 0, 108, 11, 0, 0, 0, 0, 174, 244, 185, 0, 0, 0, 0, 0, 69, 255, 108, 11, 0, 0, 0, 0, 232, 7, 251, 254, 0, 0, 0, 0, 44, 2, 37, 249, 0, 0, 0, 0, 148, 4, 156, 252, 0, 0, 0, 0, 209, 253, 9, 249, 0, 0, 0, 0, 28, 248, 56, 255, 0, 0, 0, 0, 102, 251, 229, 252, 0, 0, 0, 0, 44, 2, 217, 6, 0, 0, 0, 0, 232, 7, 3, 1, 0, 0, 0, 0, 148, 4, 98, 3, 0, 0, 0, 0, 28, 248, 198, 0, 0, 0, 0, 0, 209, 253, 245, 6, 0, 0, 0, 0, 102, 251, 25, 3, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 79, 11, 255, 255, 0, 0, 0, 0, 175, 244, 255, 255, 0, 0, 0, 0, 255, 255, 175, 244, 0, 0, 0, 0, 255, 255, 79, 11, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 166, 7, 79, 0, 0, 0, 0, 0, 14, 4, 221, 2, 0, 0, 0, 0, 131, 1, 134, 6, 0, 0, 0, 0, 240, 251, 151, 2, 0, 0, 0, 0, 88, 248, 13, 0, 0, 0, 0, 0, 123, 254, 166, 6, 0, 0, 0, 0, 14, 4, 33, 253, 0, 0, 0, 0, 166, 7, 175, 255, 0, 0, 0, 0, 131, 1, 120, 249, 0, 0, 0, 0, 88, 248, 241, 255, 0, 0, 0, 0, 240, 251, 103, 253, 0, 0, 0, 0, 123, 254, 88, 249, 0, 0, 0, 0, 87, 11, 69, 255, 0, 0, 0, 0, 87, 11, 185, 0, 0, 0, 255, 191, 255, 127, 255, 191, 255, 127, 0, 0, 255, 191, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 0, 0, 255, 191, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 0, 0, 255, 191, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 0, 0, 255, 191, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 0, 0, 255, 191, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127, 255, 191, 255, 127)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_7w64u")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_bwgkt"]
transparency = 2
alpha_scissor_threshold = 0.5
alpha_antialiasing_mode = 0
shading_mode = 0
albedo_texture = ExtResource("23_uu5ee")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_i62hh"]
resource_name = "CardFaction"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("23_uu5ee")

[sub_resource type="ArrayMesh" id="ArrayMesh_1m3qx"]
_surfaces = [{
"aabb": AABB(0.0398071, 0.880924, 1.04766, 1.00471e-05, 1.07111, 0.708228),
"format": 34896613377,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [9.12507e-09, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"name": "CardFaction",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(59, 1, 254, 255, 254, 255, 0, 0, 0, 0, 0, 0, 254, 255, 0, 0, 59, 1, 254, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 1, 254, 255, 255, 127, 0, 0, 145, 0, 69, 145, 254, 255, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 145, 0, 69, 145, 0, 0, 0, 0, 145, 0, 69, 145, 255, 127, 0, 0, 59, 1, 254, 255, 254, 191, 0, 0, 48, 0, 162, 72, 254, 255, 0, 0, 0, 0, 0, 0, 255, 63, 0, 0, 11, 1, 232, 217, 0, 0, 0, 0, 59, 1, 254, 255, 255, 63, 0, 0, 11, 1, 232, 217, 254, 255, 0, 0, 0, 0, 0, 0, 254, 191, 0, 0, 48, 0, 162, 72, 0, 0, 0, 0, 145, 0, 69, 145, 255, 63, 0, 0, 145, 0, 69, 145, 254, 191, 0, 0, 11, 1, 232, 217, 255, 127, 0, 0, 48, 0, 162, 72, 255, 127, 0, 0, 48, 0, 162, 72, 254, 191, 0, 0, 11, 1, 232, 217, 254, 191, 0, 0, 11, 1, 232, 217, 255, 63, 0, 0, 48, 0, 162, 72, 255, 63, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_k0ke4"]
resource_name = "DRC_Card_Modell_Test01_Plane_004"
_surfaces = [{
"aabb": AABB(0.0398071, 0.880924, 1.04766, 1.00471e-05, 1.07111, 0.708228),
"attribute_data": PackedByteArray(25, 2, 138, 2, 25, 2, 112, 244, 34, 253, 138, 2, 34, 253, 112, 244, 157, 127, 138, 2, 25, 2, 42, 107, 157, 127, 112, 244, 34, 253, 42, 107, 157, 127, 42, 107, 219, 64, 138, 2, 25, 2, 206, 175, 96, 190, 112, 244, 34, 253, 136, 38, 96, 190, 138, 2, 25, 2, 136, 38, 219, 64, 112, 244, 34, 253, 206, 175, 96, 190, 42, 107, 219, 64, 42, 107, 157, 127, 136, 38, 157, 127, 206, 175, 219, 64, 206, 175, 219, 64, 136, 38, 96, 190, 136, 38, 96, 190, 206, 175),
"format": 34896613399,
"index_count": 96,
"index_data": PackedByteArray(24, 0, 3, 0, 11, 0, 24, 0, 16, 0, 3, 0, 23, 0, 7, 0, 17, 0, 23, 0, 12, 0, 7, 0, 22, 0, 8, 0, 18, 0, 22, 0, 19, 0, 8, 0, 21, 0, 6, 0, 15, 0, 21, 0, 20, 0, 6, 0, 18, 0, 20, 0, 21, 0, 18, 0, 8, 0, 20, 0, 5, 0, 21, 0, 10, 0, 5, 0, 18, 0, 21, 0, 10, 0, 15, 0, 1, 0, 10, 0, 21, 0, 15, 0, 9, 0, 19, 0, 22, 0, 9, 0, 4, 0, 19, 0, 0, 0, 22, 0, 14, 0, 0, 0, 9, 0, 22, 0, 14, 0, 18, 0, 5, 0, 14, 0, 22, 0, 18, 0, 13, 0, 12, 0, 23, 0, 13, 0, 2, 0, 12, 0, 4, 0, 23, 0, 19, 0, 4, 0, 13, 0, 23, 0, 19, 0, 17, 0, 8, 0, 19, 0, 23, 0, 17, 0, 17, 0, 16, 0, 24, 0, 17, 0, 7, 0, 16, 0, 8, 0, 24, 0, 20, 0, 8, 0, 17, 0, 24, 0, 20, 0, 11, 0, 6, 0, 20, 0, 24, 0, 11, 0),
"lods": [9.12507e-09, PackedByteArray(5, 0, 6, 0, 10, 0, 10, 0, 15, 0, 1, 0, 10, 0, 6, 0, 15, 0, 14, 0, 6, 0, 5, 0, 14, 0, 9, 0, 6, 0, 0, 0, 9, 0, 14, 0, 9, 0, 3, 0, 6, 0, 6, 0, 3, 0, 11, 0, 9, 0, 16, 0, 3, 0, 9, 0, 7, 0, 16, 0, 4, 0, 7, 0, 9, 0, 4, 0, 13, 0, 7, 0, 13, 0, 12, 0, 7, 0, 13, 0, 2, 0, 12, 0)],
"material": SubResource("StandardMaterial3D_i62hh"),
"name": "CardFaction",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 25,
"vertex_data": PackedByteArray(59, 1, 254, 255, 254, 255, 255, 191, 0, 0, 0, 0, 254, 255, 255, 191, 59, 1, 254, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 59, 1, 254, 255, 255, 127, 255, 191, 145, 0, 69, 145, 254, 255, 255, 191, 0, 0, 0, 0, 255, 127, 255, 191, 145, 0, 69, 145, 0, 0, 255, 191, 145, 0, 69, 145, 255, 127, 255, 191, 59, 1, 254, 255, 254, 191, 255, 191, 48, 0, 162, 72, 254, 255, 255, 191, 0, 0, 0, 0, 255, 63, 255, 191, 11, 1, 232, 217, 0, 0, 255, 191, 59, 1, 254, 255, 255, 63, 255, 191, 11, 1, 232, 217, 254, 255, 255, 191, 0, 0, 0, 0, 254, 191, 255, 191, 48, 0, 162, 72, 0, 0, 255, 191, 145, 0, 69, 145, 255, 63, 255, 191, 145, 0, 69, 145, 254, 191, 255, 191, 11, 1, 232, 217, 255, 127, 255, 191, 48, 0, 162, 72, 255, 127, 255, 191, 48, 0, 162, 72, 254, 191, 255, 191, 11, 1, 232, 217, 254, 191, 255, 191, 11, 1, 232, 217, 255, 63, 255, 191, 48, 0, 162, 72, 255, 63, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_1m3qx")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_610e1"]
transparency = 1
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("24_8wcg0")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_72yu4"]
resource_name = "CardRarity"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("24_8wcg0")

[sub_resource type="ArrayMesh" id="ArrayMesh_72eop"]
_surfaces = [{
"aabb": AABB(0.0378236, -2.47757, -1.60525, 1.00136e-05, 0.39001, 0.22801),
"format": 34896613377,
"index_count": 24,
"index_data": PackedByteArray(1, 0, 8, 0, 6, 0, 1, 0, 5, 0, 8, 0, 5, 0, 4, 0, 8, 0, 5, 0, 0, 0, 4, 0, 4, 0, 7, 0, 8, 0, 4, 0, 2, 0, 7, 0, 8, 0, 3, 0, 6, 0, 8, 0, 7, 0, 3, 0),
"name": "CardRarity",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 9,
"vertex_data": PackedByteArray(97, 0, 253, 255, 252, 255, 0, 0, 0, 0, 0, 0, 252, 255, 0, 0, 97, 0, 253, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 0, 253, 255, 254, 127, 0, 0, 48, 0, 254, 127, 252, 255, 0, 0, 0, 0, 0, 0, 254, 127, 0, 0, 48, 0, 254, 127, 0, 0, 0, 0, 48, 0, 254, 127, 254, 127, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_cpfdt"]
resource_name = "DRC_Card_Modell_Test01_Plane_005"
_surfaces = [{
"aabb": AABB(0.0378236, -2.47757, -1.60525, 1.00136e-05, 0.39001, 0.22801),
"attribute_data": PackedByteArray(252, 118, 96, 110, 252, 118, 92, 141, 25, 137, 96, 110, 25, 137, 92, 141, 10, 128, 96, 110, 252, 118, 222, 125, 10, 128, 92, 141, 25, 137, 222, 125, 10, 128, 222, 125),
"format": 34896613399,
"index_count": 24,
"index_data": PackedByteArray(1, 0, 8, 0, 6, 0, 1, 0, 5, 0, 8, 0, 5, 0, 4, 0, 8, 0, 5, 0, 0, 0, 4, 0, 4, 0, 7, 0, 8, 0, 4, 0, 2, 0, 7, 0, 8, 0, 3, 0, 6, 0, 8, 0, 7, 0, 3, 0),
"material": SubResource("StandardMaterial3D_72yu4"),
"name": "CardRarity",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 9,
"vertex_data": PackedByteArray(97, 0, 253, 255, 252, 255, 255, 191, 0, 0, 0, 0, 252, 255, 255, 191, 97, 0, 253, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 97, 0, 253, 255, 254, 127, 255, 191, 48, 0, 254, 127, 252, 255, 255, 191, 0, 0, 0, 0, 254, 127, 255, 191, 48, 0, 254, 127, 0, 0, 255, 191, 48, 0, 254, 127, 254, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_72eop")

[sub_resource type="ViewportTexture" id="ViewportTexture_akcsu"]
resource_name = "_albedo"
viewport_path = NodePath("CardBg/SubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_uadp2"]
resource_local_to_scene = true
cull_mode = 2
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_akcsu")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_3spdg"]
resource_name = "CardBase"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("29_4dfqf")

[sub_resource type="ArrayMesh" id="ArrayMesh_4aff2"]
_surfaces = [{
"aabb": AABB(-0.0370001, -2.49985, -1.78976, 0.0740101, 4.9997, 3.57952),
"format": 34896613377,
"index_count": 828,
"index_data": PackedByteArray(62, 0, 139, 0, 138, 0, 62, 0, 64, 0, 139, 0, 40, 0, 31, 0, 23, 0, 40, 0, 51, 0, 31, 0, 49, 0, 24, 0, 30, 0, 49, 0, 41, 0, 24, 0, 66, 0, 139, 0, 73, 0, 66, 0, 138, 0, 139, 0, 52, 0, 20, 0, 33, 0, 52, 0, 43, 0, 20, 0, 3, 0, 7, 0, 12, 0, 3, 0, 0, 0, 7, 0, 46, 0, 27, 0, 26, 0, 46, 0, 47, 0, 27, 0, 47, 0, 28, 0, 27, 0, 47, 0, 48, 0, 28, 0, 55, 0, 36, 0, 35, 0, 55, 0, 56, 0, 36, 0, 56, 0, 37, 0, 36, 0, 56, 0, 57, 0, 37, 0, 42, 0, 34, 0, 19, 0, 42, 0, 54, 0, 34, 0, 38, 0, 19, 0, 18, 0, 38, 0, 42, 0, 19, 0, 39, 0, 24, 0, 41, 0, 39, 0, 25, 0, 24, 0, 1, 0, 23, 0, 5, 0, 1, 0, 22, 0, 23, 0, 2, 0, 59, 0, 58, 0, 2, 0, 4, 0, 59, 0, 13, 0, 27, 0, 14, 0, 13, 0, 26, 0, 27, 0, 14, 0, 28, 0, 12, 0, 14, 0, 27, 0, 28, 0, 17, 0, 30, 0, 15, 0, 17, 0, 29, 0, 30, 0, 16, 0, 29, 0, 17, 0, 16, 0, 31, 0, 29, 0, 8, 0, 33, 0, 6, 0, 8, 0, 32, 0, 33, 0, 7, 0, 32, 0, 8, 0, 7, 0, 34, 0, 32, 0, 10, 0, 36, 0, 11, 0, 10, 0, 35, 0, 36, 0, 11, 0, 37, 0, 9, 0, 11, 0, 36, 0, 37, 0, 0, 0, 34, 0, 7, 0, 0, 0, 19, 0, 34, 0, 6, 0, 20, 0, 58, 0, 6, 0, 33, 0, 20, 0, 9, 0, 22, 0, 1, 0, 9, 0, 37, 0, 22, 0, 2, 0, 35, 0, 10, 0, 2, 0, 21, 0, 35, 0, 12, 0, 18, 0, 3, 0, 12, 0, 28, 0, 18, 0, 59, 0, 26, 0, 13, 0, 59, 0, 25, 0, 26, 0, 5, 0, 31, 0, 16, 0, 5, 0, 23, 0, 31, 0, 15, 0, 24, 0, 4, 0, 15, 0, 30, 0, 24, 0, 122, 0, 54, 0, 42, 0, 122, 0, 134, 0, 54, 0, 136, 0, 57, 0, 56, 0, 136, 0, 137, 0, 57, 0, 135, 0, 56, 0, 55, 0, 135, 0, 136, 0, 56, 0, 32, 0, 54, 0, 53, 0, 32, 0, 34, 0, 54, 0, 33, 0, 53, 0, 52, 0, 33, 0, 32, 0, 53, 0, 29, 0, 51, 0, 50, 0, 29, 0, 31, 0, 51, 0, 30, 0, 50, 0, 49, 0, 30, 0, 29, 0, 50, 0, 127, 0, 48, 0, 47, 0, 127, 0, 128, 0, 48, 0, 126, 0, 47, 0, 46, 0, 126, 0, 127, 0, 47, 0, 18, 0, 48, 0, 38, 0, 18, 0, 28, 0, 48, 0, 35, 0, 45, 0, 55, 0, 35, 0, 21, 0, 45, 0, 132, 0, 43, 0, 52, 0, 132, 0, 123, 0, 43, 0, 129, 0, 41, 0, 49, 0, 129, 0, 121, 0, 41, 0, 120, 0, 51, 0, 40, 0, 120, 0, 131, 0, 51, 0, 26, 0, 39, 0, 46, 0, 26, 0, 25, 0, 39, 0, 45, 0, 123, 0, 125, 0, 45, 0, 43, 0, 123, 0, 22, 0, 57, 0, 44, 0, 22, 0, 37, 0, 57, 0, 23, 0, 44, 0, 40, 0, 23, 0, 22, 0, 44, 0, 59, 0, 24, 0, 25, 0, 59, 0, 4, 0, 24, 0, 2, 0, 15, 0, 4, 0, 2, 0, 10, 0, 15, 0, 10, 0, 17, 0, 15, 0, 10, 0, 11, 0, 17, 0, 11, 0, 16, 0, 17, 0, 11, 0, 9, 0, 16, 0, 9, 0, 5, 0, 16, 0, 9, 0, 1, 0, 5, 0, 3, 0, 19, 0, 0, 0, 3, 0, 18, 0, 19, 0, 7, 0, 14, 0, 12, 0, 7, 0, 8, 0, 14, 0, 8, 0, 13, 0, 14, 0, 8, 0, 6, 0, 13, 0, 20, 0, 45, 0, 21, 0, 20, 0, 43, 0, 45, 0, 58, 0, 21, 0, 2, 0, 58, 0, 20, 0, 21, 0, 40, 0, 124, 0, 120, 0, 40, 0, 44, 0, 124, 0, 44, 0, 137, 0, 124, 0, 44, 0, 57, 0, 137, 0, 46, 0, 119, 0, 126, 0, 46, 0, 39, 0, 119, 0, 55, 0, 125, 0, 135, 0, 55, 0, 45, 0, 125, 0, 38, 0, 128, 0, 118, 0, 38, 0, 48, 0, 128, 0, 49, 0, 130, 0, 129, 0, 49, 0, 50, 0, 130, 0, 50, 0, 131, 0, 130, 0, 50, 0, 51, 0, 131, 0, 52, 0, 133, 0, 132, 0, 52, 0, 53, 0, 133, 0, 53, 0, 134, 0, 133, 0, 53, 0, 54, 0, 134, 0, 118, 0, 42, 0, 38, 0, 118, 0, 122, 0, 42, 0, 138, 0, 81, 0, 62, 0, 138, 0, 80, 0, 81, 0, 105, 0, 123, 0, 125, 0, 105, 0, 103, 0, 123, 0, 100, 0, 91, 0, 83, 0, 100, 0, 111, 0, 91, 0, 109, 0, 84, 0, 90, 0, 109, 0, 101, 0, 84, 0, 112, 0, 80, 0, 93, 0, 112, 0, 103, 0, 80, 0, 63, 0, 67, 0, 72, 0, 63, 0, 60, 0, 67, 0, 106, 0, 87, 0, 86, 0, 106, 0, 107, 0, 87, 0, 107, 0, 88, 0, 87, 0, 107, 0, 108, 0, 88, 0, 115, 0, 96, 0, 95, 0, 115, 0, 116, 0, 96, 0, 116, 0, 97, 0, 96, 0, 116, 0, 117, 0, 97, 0, 102, 0, 94, 0, 79, 0, 102, 0, 114, 0, 94, 0, 98, 0, 79, 0, 78, 0, 98, 0, 102, 0, 79, 0, 99, 0, 84, 0, 101, 0, 99, 0, 85, 0, 84, 0, 61, 0, 83, 0, 65, 0, 61, 0, 82, 0, 83, 0, 64, 0, 85, 0, 139, 0, 64, 0, 84, 0, 85, 0, 73, 0, 87, 0, 74, 0, 73, 0, 86, 0, 87, 0, 74, 0, 88, 0, 72, 0, 74, 0, 87, 0, 88, 0, 77, 0, 90, 0, 75, 0, 77, 0, 89, 0, 90, 0, 76, 0, 89, 0, 77, 0, 76, 0, 91, 0, 89, 0, 68, 0, 93, 0, 66, 0, 68, 0, 92, 0, 93, 0, 67, 0, 92, 0, 68, 0, 67, 0, 94, 0, 92, 0, 70, 0, 96, 0, 71, 0, 70, 0, 95, 0, 96, 0, 71, 0, 97, 0, 69, 0, 71, 0, 96, 0, 97, 0, 60, 0, 94, 0, 67, 0, 60, 0, 79, 0, 94, 0, 66, 0, 80, 0, 138, 0, 66, 0, 93, 0, 80, 0, 69, 0, 82, 0, 61, 0, 69, 0, 97, 0, 82, 0, 62, 0, 95, 0, 70, 0, 62, 0, 81, 0, 95, 0, 72, 0, 78, 0, 63, 0, 72, 0, 88, 0, 78, 0, 139, 0, 86, 0, 73, 0, 139, 0, 85, 0, 86, 0, 65, 0, 91, 0, 76, 0, 65, 0, 83, 0, 91, 0, 75, 0, 84, 0, 64, 0, 75, 0, 90, 0, 84, 0, 122, 0, 114, 0, 102, 0, 122, 0, 134, 0, 114, 0, 136, 0, 117, 0, 116, 0, 136, 0, 137, 0, 117, 0, 135, 0, 116, 0, 115, 0, 135, 0, 136, 0, 116, 0, 92, 0, 114, 0, 113, 0, 92, 0, 94, 0, 114, 0, 93, 0, 113, 0, 112, 0, 93, 0, 92, 0, 113, 0, 89, 0, 111, 0, 110, 0, 89, 0, 91, 0, 111, 0, 90, 0, 110, 0, 109, 0, 90, 0, 89, 0, 110, 0, 127, 0, 108, 0, 107, 0, 127, 0, 128, 0, 108, 0, 126, 0, 107, 0, 106, 0, 126, 0, 127, 0, 107, 0, 63, 0, 79, 0, 60, 0, 63, 0, 78, 0, 79, 0, 78, 0, 108, 0, 98, 0, 78, 0, 88, 0, 108, 0, 95, 0, 105, 0, 115, 0, 95, 0, 81, 0, 105, 0, 132, 0, 103, 0, 112, 0, 132, 0, 123, 0, 103, 0, 129, 0, 101, 0, 109, 0, 129, 0, 121, 0, 101, 0, 120, 0, 111, 0, 100, 0, 120, 0, 131, 0, 111, 0, 86, 0, 99, 0, 106, 0, 86, 0, 85, 0, 99, 0, 82, 0, 117, 0, 104, 0, 82, 0, 97, 0, 117, 0, 83, 0, 104, 0, 100, 0, 83, 0, 82, 0, 104, 0, 121, 0, 99, 0, 101, 0, 121, 0, 119, 0, 99, 0, 62, 0, 75, 0, 64, 0, 62, 0, 70, 0, 75, 0, 70, 0, 77, 0, 75, 0, 70, 0, 71, 0, 77, 0, 71, 0, 76, 0, 77, 0, 71, 0, 69, 0, 76, 0, 69, 0, 65, 0, 76, 0, 69, 0, 61, 0, 65, 0, 118, 0, 102, 0, 98, 0, 118, 0, 122, 0, 102, 0, 67, 0, 74, 0, 72, 0, 67, 0, 68, 0, 74, 0, 68, 0, 73, 0, 74, 0, 68, 0, 66, 0, 73, 0, 80, 0, 105, 0, 81, 0, 80, 0, 103, 0, 105, 0, 119, 0, 41, 0, 121, 0, 119, 0, 39, 0, 41, 0, 100, 0, 124, 0, 120, 0, 100, 0, 104, 0, 124, 0, 104, 0, 137, 0, 124, 0, 104, 0, 117, 0, 137, 0, 106, 0, 119, 0, 126, 0, 106, 0, 99, 0, 119, 0, 115, 0, 125, 0, 135, 0, 115, 0, 105, 0, 125, 0, 98, 0, 128, 0, 118, 0, 98, 0, 108, 0, 128, 0, 109, 0, 130, 0, 129, 0, 109, 0, 110, 0, 130, 0, 110, 0, 131, 0, 130, 0, 110, 0, 111, 0, 131, 0, 112, 0, 133, 0, 132, 0, 112, 0, 113, 0, 133, 0, 113, 0, 134, 0, 133, 0, 113, 0, 114, 0, 134, 0, 6, 0, 59, 0, 13, 0, 6, 0, 58, 0, 59, 0),
"lods": [0.0409402, PackedByteArray(70, 0, 73, 0, 66, 0, 73, 0, 107, 0, 72, 0, 66, 0, 73, 0, 72, 0, 72, 0, 98, 0, 63, 0, 63, 0, 66, 0, 72, 0, 63, 0, 60, 0, 66, 0, 63, 0, 79, 0, 60, 0, 63, 0, 98, 0, 79, 0, 60, 0, 79, 0, 114, 0, 60, 0, 114, 0, 66, 0, 66, 0, 114, 0, 113, 0, 66, 0, 113, 0, 93, 0, 98, 0, 114, 0, 79, 0, 66, 0, 93, 0, 95, 0, 93, 0, 105, 0, 95, 0, 93, 0, 103, 0, 105, 0, 66, 0, 95, 0, 70, 0, 70, 0, 95, 0, 96, 0, 93, 0, 113, 0, 103, 0, 103, 0, 113, 0, 133, 0, 103, 0, 133, 0, 132, 0, 70, 0, 96, 0, 61, 0, 61, 0, 96, 0, 104, 0, 135, 0, 136, 0, 104, 0, 135, 0, 104, 0, 105, 0, 61, 0, 104, 0, 83, 0, 110, 0, 83, 0, 111, 0, 75, 0, 110, 0, 109, 0, 61, 0, 83, 0, 65, 0, 65, 0, 83, 0, 75, 0, 61, 0, 65, 0, 75, 0, 75, 0, 83, 0, 110, 0, 70, 0, 61, 0, 75, 0, 70, 0, 75, 0, 73, 0, 75, 0, 109, 0, 73, 0, 99, 0, 73, 0, 109, 0, 121, 0, 99, 0, 109, 0, 99, 0, 107, 0, 73, 0, 126, 0, 107, 0, 99, 0, 126, 0, 127, 0, 107, 0, 127, 0, 108, 0, 107, 0, 72, 0, 107, 0, 108, 0, 72, 0, 108, 0, 98, 0, 99, 0, 119, 0, 126, 0, 121, 0, 119, 0, 99, 0, 118, 0, 114, 0, 98, 0, 118, 0, 122, 0, 114, 0, 122, 0, 134, 0, 114, 0, 98, 0, 128, 0, 118, 0, 3, 0, 6, 0, 12, 0, 12, 0, 38, 0, 3, 0, 3, 0, 38, 0, 19, 0, 12, 0, 48, 0, 38, 0, 3, 0, 19, 0, 0, 0, 3, 0, 0, 0, 6, 0, 0, 0, 19, 0, 54, 0, 0, 0, 54, 0, 6, 0, 6, 0, 54, 0, 53, 0, 12, 0, 27, 0, 48, 0, 6, 0, 53, 0, 33, 0, 13, 0, 27, 0, 12, 0, 6, 0, 13, 0, 12, 0, 13, 0, 26, 0, 27, 0, 6, 0, 33, 0, 35, 0, 126, 0, 48, 0, 39, 0, 6, 0, 35, 0, 10, 0, 10, 0, 13, 0, 6, 0, 10, 0, 15, 0, 13, 0, 13, 0, 15, 0, 26, 0, 39, 0, 26, 0, 15, 0, 39, 0, 15, 0, 49, 0, 49, 0, 50, 0, 130, 0, 49, 0, 130, 0, 129, 0, 15, 0, 50, 0, 49, 0, 129, 0, 121, 0, 49, 0, 119, 0, 49, 0, 121, 0, 17, 0, 50, 0, 15, 0, 10, 0, 17, 0, 15, 0, 17, 0, 23, 0, 50, 0, 50, 0, 23, 0, 51, 0, 51, 0, 124, 0, 120, 0, 51, 0, 44, 0, 124, 0, 5, 0, 23, 0, 17, 0, 1, 0, 44, 0, 23, 0, 1, 0, 23, 0, 5, 0, 1, 0, 5, 0, 17, 0, 10, 0, 1, 0, 17, 0, 1, 0, 57, 0, 44, 0, 10, 0, 57, 0, 1, 0, 10, 0, 56, 0, 57, 0, 10, 0, 35, 0, 56, 0, 56, 0, 123, 0, 125, 0, 33, 0, 43, 0, 56, 0, 132, 0, 123, 0, 43, 0, 56, 0, 43, 0, 123, 0, 56, 0, 125, 0, 135, 0, 135, 0, 136, 0, 56, 0, 136, 0, 57, 0, 56, 0, 44, 0, 57, 0, 137, 0, 39, 0, 27, 0, 26, 0, 39, 0, 48, 0, 27, 0, 38, 0, 54, 0, 19, 0, 122, 0, 134, 0, 54, 0, 136, 0, 137, 0, 57, 0, 33, 0, 53, 0, 43, 0, 53, 0, 54, 0, 134, 0, 33, 0, 56, 0, 35, 0, 127, 0, 128, 0, 48, 0, 126, 0, 127, 0, 48, 0, 120, 0, 131, 0, 51, 0, 23, 0, 44, 0, 51, 0, 44, 0, 137, 0, 124, 0, 39, 0, 119, 0, 126, 0, 38, 0, 128, 0, 118, 0, 38, 0, 48, 0, 128, 0, 50, 0, 131, 0, 130, 0, 50, 0, 51, 0, 131, 0, 43, 0, 133, 0, 132, 0, 43, 0, 53, 0, 133, 0, 53, 0, 134, 0, 133, 0, 118, 0, 54, 0, 38, 0, 118, 0, 122, 0, 54, 0, 105, 0, 123, 0, 125, 0, 105, 0, 103, 0, 123, 0, 105, 0, 96, 0, 95, 0, 105, 0, 104, 0, 96, 0, 136, 0, 137, 0, 104, 0, 127, 0, 128, 0, 108, 0, 132, 0, 123, 0, 103, 0, 129, 0, 121, 0, 109, 0, 120, 0, 131, 0, 111, 0, 83, 0, 104, 0, 111, 0, 119, 0, 39, 0, 49, 0, 111, 0, 124, 0, 120, 0, 111, 0, 104, 0, 124, 0, 104, 0, 137, 0, 124, 0, 105, 0, 125, 0, 135, 0, 98, 0, 108, 0, 128, 0, 109, 0, 130, 0, 129, 0, 109, 0, 110, 0, 130, 0, 110, 0, 131, 0, 130, 0, 110, 0, 111, 0, 131, 0, 113, 0, 134, 0, 133, 0, 113, 0, 114, 0, 134, 0), 0.0674687, PackedByteArray(96, 0, 119, 0, 66, 0, 66, 0, 103, 0, 105, 0, 66, 0, 105, 0, 96, 0, 135, 0, 104, 0, 105, 0, 96, 0, 75, 0, 119, 0, 96, 0, 104, 0, 75, 0, 135, 0, 136, 0, 104, 0, 104, 0, 65, 0, 75, 0, 75, 0, 65, 0, 110, 0, 110, 0, 124, 0, 120, 0, 65, 0, 104, 0, 110, 0, 105, 0, 104, 0, 96, 0, 126, 0, 127, 0, 119, 0, 119, 0, 127, 0, 118, 0, 118, 0, 127, 0, 128, 0, 66, 0, 119, 0, 118, 0, 118, 0, 60, 0, 66, 0, 60, 0, 133, 0, 66, 0, 118, 0, 122, 0, 133, 0, 122, 0, 134, 0, 133, 0, 118, 0, 133, 0, 60, 0, 3, 0, 0, 0, 33, 0, 0, 0, 53, 0, 33, 0, 3, 0, 53, 0, 0, 0, 118, 0, 122, 0, 53, 0, 33, 0, 27, 0, 3, 0, 10, 0, 27, 0, 33, 0, 33, 0, 136, 0, 10, 0, 10, 0, 136, 0, 124, 0, 10, 0, 15, 0, 27, 0, 39, 0, 27, 0, 15, 0, 126, 0, 3, 0, 39, 0, 126, 0, 127, 0, 3, 0, 39, 0, 3, 0, 27, 0, 39, 0, 15, 0, 130, 0, 5, 0, 124, 0, 130, 0, 10, 0, 124, 0, 15, 0, 124, 0, 5, 0, 15, 0, 15, 0, 5, 0, 130, 0, 136, 0, 137, 0, 124, 0, 136, 0, 125, 0, 135, 0, 136, 0, 123, 0, 125, 0, 130, 0, 124, 0, 120, 0, 120, 0, 131, 0, 130, 0, 129, 0, 121, 0, 130, 0, 119, 0, 130, 0, 121, 0, 122, 0, 134, 0, 53, 0, 127, 0, 128, 0, 3, 0, 132, 0, 123, 0, 33, 0, 136, 0, 33, 0, 123, 0, 39, 0, 119, 0, 126, 0, 3, 0, 128, 0, 118, 0, 33, 0, 133, 0, 132, 0, 33, 0, 53, 0, 133, 0, 53, 0, 134, 0, 133, 0, 118, 0, 53, 0, 3, 0, 105, 0, 123, 0, 125, 0, 105, 0, 103, 0, 123, 0, 136, 0, 137, 0, 104, 0, 66, 0, 133, 0, 103, 0, 103, 0, 133, 0, 132, 0, 132, 0, 123, 0, 103, 0, 129, 0, 121, 0, 75, 0, 120, 0, 131, 0, 110, 0, 121, 0, 119, 0, 75, 0, 119, 0, 39, 0, 130, 0, 110, 0, 104, 0, 124, 0, 104, 0, 137, 0, 124, 0, 105, 0, 125, 0, 135, 0, 75, 0, 130, 0, 129, 0, 75, 0, 110, 0, 130, 0, 110, 0, 131, 0, 130, 0), 0.195405, PackedByteArray(125, 0, 119, 0, 133, 0, 125, 0, 133, 0, 123, 0, 132, 0, 123, 0, 133, 0, 133, 0, 122, 0, 134, 0, 118, 0, 122, 0, 133, 0, 133, 0, 119, 0, 118, 0, 119, 0, 127, 0, 118, 0, 118, 0, 127, 0, 128, 0, 126, 0, 127, 0, 119, 0, 125, 0, 129, 0, 119, 0, 121, 0, 119, 0, 129, 0, 125, 0, 137, 0, 129, 0, 135, 0, 137, 0, 125, 0, 135, 0, 136, 0, 137, 0, 137, 0, 131, 0, 129, 0, 129, 0, 131, 0, 130, 0, 131, 0, 137, 0, 124, 0, 131, 0, 124, 0, 120, 0, 136, 0, 126, 0, 132, 0, 126, 0, 127, 0, 128, 0, 132, 0, 126, 0, 128, 0, 118, 0, 132, 0, 128, 0, 118, 0, 122, 0, 132, 0, 122, 0, 134, 0, 132, 0, 132, 0, 134, 0, 133, 0, 136, 0, 132, 0, 123, 0, 136, 0, 123, 0, 125, 0, 136, 0, 125, 0, 135, 0, 136, 0, 130, 0, 126, 0, 119, 0, 126, 0, 130, 0, 119, 0, 130, 0, 121, 0, 129, 0, 121, 0, 130, 0, 136, 0, 124, 0, 130, 0, 136, 0, 137, 0, 124, 0, 130, 0, 124, 0, 120, 0, 120, 0, 131, 0, 130, 0)],
"name": "CardBase",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 140,
"vertex_data": PackedByteArray(246, 255, 185, 0, 146, 244, 0, 0, 246, 255, 69, 255, 146, 244, 0, 0, 246, 255, 174, 244, 69, 255, 0, 0, 246, 255, 185, 0, 108, 11, 0, 0, 246, 255, 174, 244, 185, 0, 0, 0, 246, 255, 69, 255, 108, 11, 0, 0, 246, 255, 232, 7, 251, 254, 0, 0, 246, 255, 44, 2, 37, 249, 0, 0, 246, 255, 148, 4, 156, 252, 0, 0, 246, 255, 209, 253, 9, 249, 0, 0, 246, 255, 28, 248, 56, 255, 0, 0, 246, 255, 102, 251, 229, 252, 0, 0, 246, 255, 44, 2, 217, 6, 0, 0, 246, 255, 232, 7, 3, 1, 0, 0, 246, 255, 148, 4, 98, 3, 0, 0, 246, 255, 28, 248, 198, 0, 0, 0, 246, 255, 209, 253, 245, 6, 0, 0, 246, 255, 102, 251, 25, 3, 0, 0, 246, 255, 0, 0, 79, 11, 0, 0, 246, 255, 0, 0, 175, 244, 0, 0, 246, 255, 79, 11, 255, 255, 0, 0, 246, 255, 175, 244, 255, 255, 0, 0, 246, 255, 255, 255, 175, 244, 0, 0, 246, 255, 255, 255, 79, 11, 0, 0, 246, 255, 175, 244, 0, 0, 0, 0, 246, 255, 79, 11, 0, 0, 0, 0, 246, 255, 166, 7, 79, 0, 0, 0, 246, 255, 14, 4, 221, 2, 0, 0, 246, 255, 131, 1, 134, 6, 0, 0, 246, 255, 240, 251, 151, 2, 0, 0, 246, 255, 88, 248, 13, 0, 0, 0, 246, 255, 123, 254, 166, 6, 0, 0, 246, 255, 14, 4, 33, 253, 0, 0, 246, 255, 166, 7, 175, 255, 0, 0, 246, 255, 131, 1, 120, 249, 0, 0, 246, 255, 88, 248, 241, 255, 0, 0, 246, 255, 240, 251, 103, 253, 0, 0, 246, 255, 123, 254, 88, 249, 0, 0, 234, 217, 0, 0, 79, 11, 0, 0, 234, 217, 79, 11, 0, 0, 0, 0, 234, 217, 255, 255, 79, 11, 0, 0, 234, 217, 175, 244, 0, 0, 0, 0, 234, 217, 0, 0, 175, 244, 0, 0, 234, 217, 79, 11, 255, 255, 0, 0, 234, 217, 255, 255, 175, 244, 0, 0, 234, 217, 175, 244, 255, 255, 0, 0, 234, 217, 166, 7, 79, 0, 0, 0, 234, 217, 14, 4, 221, 2, 0, 0, 234, 217, 131, 1, 134, 6, 0, 0, 234, 217, 88, 248, 13, 0, 0, 0, 234, 217, 240, 251, 151, 2, 0, 0, 234, 217, 123, 254, 166, 6, 0, 0, 234, 217, 166, 7, 175, 255, 0, 0, 234, 217, 14, 4, 33, 253, 0, 0, 234, 217, 131, 1, 120, 249, 0, 0, 234, 217, 88, 248, 241, 255, 0, 0, 234, 217, 240, 251, 103, 253, 0, 0, 234, 217, 123, 254, 88, 249, 0, 0, 246, 255, 87, 11, 69, 255, 0, 0, 246, 255, 87, 11, 185, 0, 0, 0, 0, 0, 185, 0, 146, 244, 0, 0, 0, 0, 69, 255, 146, 244, 0, 0, 0, 0, 174, 244, 69, 255, 0, 0, 0, 0, 185, 0, 108, 11, 0, 0, 0, 0, 174, 244, 185, 0, 0, 0, 0, 0, 69, 255, 108, 11, 0, 0, 0, 0, 232, 7, 251, 254, 0, 0, 0, 0, 44, 2, 37, 249, 0, 0, 0, 0, 148, 4, 156, 252, 0, 0, 0, 0, 209, 253, 9, 249, 0, 0, 0, 0, 28, 248, 56, 255, 0, 0, 0, 0, 102, 251, 229, 252, 0, 0, 0, 0, 44, 2, 217, 6, 0, 0, 0, 0, 232, 7, 3, 1, 0, 0, 0, 0, 148, 4, 98, 3, 0, 0, 0, 0, 28, 248, 198, 0, 0, 0, 0, 0, 209, 253, 245, 6, 0, 0, 0, 0, 102, 251, 25, 3, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 79, 11, 255, 255, 0, 0, 0, 0, 175, 244, 255, 255, 0, 0, 0, 0, 255, 255, 175, 244, 0, 0, 0, 0, 255, 255, 79, 11, 0, 0, 0, 0, 175, 244, 0, 0, 0, 0, 0, 0, 79, 11, 0, 0, 0, 0, 0, 0, 166, 7, 79, 0, 0, 0, 0, 0, 14, 4, 221, 2, 0, 0, 0, 0, 131, 1, 134, 6, 0, 0, 0, 0, 240, 251, 151, 2, 0, 0, 0, 0, 88, 248, 13, 0, 0, 0, 0, 0, 123, 254, 166, 6, 0, 0, 0, 0, 14, 4, 33, 253, 0, 0, 0, 0, 166, 7, 175, 255, 0, 0, 0, 0, 131, 1, 120, 249, 0, 0, 0, 0, 88, 248, 241, 255, 0, 0, 0, 0, 240, 251, 103, 253, 0, 0, 0, 0, 123, 254, 88, 249, 0, 0, 11, 38, 0, 0, 79, 11, 0, 0, 11, 38, 79, 11, 0, 0, 0, 0, 11, 38, 255, 255, 79, 11, 0, 0, 11, 38, 175, 244, 0, 0, 0, 0, 11, 38, 0, 0, 175, 244, 0, 0, 11, 38, 79, 11, 255, 255, 0, 0, 11, 38, 255, 255, 175, 244, 0, 0, 11, 38, 175, 244, 255, 255, 0, 0, 11, 38, 166, 7, 79, 0, 0, 0, 11, 38, 14, 4, 221, 2, 0, 0, 11, 38, 131, 1, 134, 6, 0, 0, 11, 38, 88, 248, 13, 0, 0, 0, 11, 38, 240, 251, 151, 2, 0, 0, 11, 38, 123, 254, 166, 6, 0, 0, 11, 38, 166, 7, 175, 255, 0, 0, 11, 38, 14, 4, 33, 253, 0, 0, 11, 38, 131, 1, 120, 249, 0, 0, 11, 38, 88, 248, 241, 255, 0, 0, 11, 38, 240, 251, 103, 253, 0, 0, 11, 38, 123, 254, 88, 249, 0, 0, 251, 127, 0, 0, 79, 11, 0, 0, 251, 127, 79, 11, 0, 0, 0, 0, 251, 127, 255, 255, 79, 11, 0, 0, 251, 127, 175, 244, 0, 0, 0, 0, 251, 127, 0, 0, 175, 244, 0, 0, 251, 127, 79, 11, 255, 255, 0, 0, 251, 127, 255, 255, 175, 244, 0, 0, 251, 127, 175, 244, 255, 255, 0, 0, 251, 127, 166, 7, 79, 0, 0, 0, 251, 127, 14, 4, 221, 2, 0, 0, 251, 127, 131, 1, 134, 6, 0, 0, 251, 127, 88, 248, 13, 0, 0, 0, 251, 127, 240, 251, 151, 2, 0, 0, 251, 127, 123, 254, 166, 6, 0, 0, 251, 127, 166, 7, 175, 255, 0, 0, 251, 127, 14, 4, 33, 253, 0, 0, 251, 127, 131, 1, 120, 249, 0, 0, 251, 127, 88, 248, 241, 255, 0, 0, 251, 127, 240, 251, 103, 253, 0, 0, 251, 127, 123, 254, 88, 249, 0, 0, 0, 0, 87, 11, 69, 255, 0, 0, 0, 0, 87, 11, 185, 0, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_hsh2j"]
resource_name = "DRC_Card_Model_Test03_CardBase_Cube_001"
_surfaces = [{
"aabb": AABB(-0.0370001, -2.49985, -1.78976, 0.0740101, 4.9997, 3.57952),
"attribute_data": PackedByteArray(174, 134, 163, 255, 174, 134, 122, 129, 95, 129, 186, 134, 84, 250, 163, 255, 162, 255, 186, 134, 84, 250, 122, 129, 132, 129, 19, 252, 105, 132, 235, 254, 177, 130, 185, 253, 119, 132, 50, 130, 102, 129, 7, 133, 141, 130, 101, 131, 153, 252, 235, 254, 126, 255, 19, 252, 81, 254, 185, 253, 156, 255, 7, 133, 139, 252, 50, 130, 117, 254, 101, 131, 98, 250, 255, 255, 159, 134, 255, 255, 3, 129, 100, 250, 3, 129, 185, 134, 159, 134, 30, 129, 98, 250, 30, 129, 255, 255, 185, 134, 255, 255, 100, 250, 215, 255, 52, 252, 147, 254, 252, 253, 194, 252, 62, 255, 182, 254, 33, 131, 248, 255, 233, 132, 178, 252, 222, 129, 111, 130, 252, 253, 43, 129, 52, 252, 64, 132, 62, 255, 10, 129, 233, 132, 76, 130, 33, 131, 80, 132, 222, 129, 98, 250, 255, 255, 255, 255, 100, 250, 98, 250, 30, 129, 255, 255, 185, 134, 159, 134, 255, 255, 3, 129, 100, 250, 159, 134, 30, 129, 3, 129, 185, 134, 215, 255, 52, 252, 147, 254, 252, 253, 194, 252, 62, 255, 248, 255, 233, 132, 182, 254, 33, 131, 178, 252, 222, 129, 43, 129, 52, 252, 111, 130, 252, 253, 64, 132, 62, 255, 10, 129, 233, 132, 76, 130, 33, 131, 80, 132, 222, 129, 95, 129, 96, 250, 162, 255, 96, 250, 140, 134, 163, 255, 140, 134, 122, 129, 61, 129, 186, 134, 50, 250, 163, 255, 128, 255, 186, 134, 50, 250, 122, 129, 98, 129, 19, 252, 71, 132, 235, 254, 143, 130, 185, 253, 85, 132, 50, 130, 68, 129, 7, 133, 107, 130, 101, 131, 119, 252, 235, 254, 92, 255, 19, 252, 47, 254, 185, 253, 122, 255, 7, 133, 105, 252, 50, 130, 83, 254, 101, 131, 64, 250, 255, 255, 125, 134, 255, 255, 225, 128, 100, 250, 225, 128, 185, 134, 125, 134, 30, 129, 64, 250, 30, 129, 220, 255, 185, 134, 220, 255, 100, 250, 181, 255, 52, 252, 113, 254, 252, 253, 160, 252, 62, 255, 148, 254, 33, 131, 214, 255, 233, 132, 144, 252, 222, 129, 77, 130, 252, 253, 9, 129, 52, 252, 30, 132, 62, 255, 232, 128, 233, 132, 42, 130, 33, 131, 46, 132, 222, 129, 64, 250, 255, 255, 220, 255, 100, 250, 64, 250, 30, 129, 220, 255, 185, 134, 125, 134, 255, 255, 225, 128, 100, 250, 125, 134, 30, 129, 225, 128, 185, 134, 181, 255, 52, 252, 113, 254, 252, 253, 160, 252, 62, 255, 214, 255, 233, 132, 148, 254, 33, 131, 144, 252, 222, 129, 9, 129, 52, 252, 77, 130, 252, 253, 30, 132, 62, 255, 232, 128, 233, 132, 42, 130, 33, 131, 46, 132, 222, 129, 64, 250, 255, 255, 98, 250, 255, 255, 220, 255, 100, 250, 255, 255, 100, 250, 64, 250, 30, 129, 98, 250, 30, 129, 220, 255, 185, 134, 255, 255, 185, 134, 125, 134, 255, 255, 159, 134, 255, 255, 225, 128, 100, 250, 3, 129, 100, 250, 125, 134, 30, 129, 159, 134, 30, 129, 225, 128, 185, 134, 3, 129, 185, 134, 181, 255, 52, 252, 215, 255, 52, 252, 113, 254, 252, 253, 147, 254, 252, 253, 160, 252, 62, 255, 194, 252, 62, 255, 214, 255, 233, 132, 248, 255, 233, 132, 148, 254, 33, 131, 182, 254, 33, 131, 144, 252, 222, 129, 178, 252, 222, 129, 9, 129, 52, 252, 43, 129, 52, 252, 77, 130, 252, 253, 111, 130, 252, 253, 30, 132, 62, 255, 64, 132, 62, 255, 232, 128, 233, 132, 10, 129, 233, 132, 42, 130, 33, 131, 76, 130, 33, 131, 46, 132, 222, 129, 80, 132, 222, 129, 61, 129, 96, 250, 128, 255, 96, 250, 64, 250, 255, 255, 98, 250, 255, 255, 220, 255, 100, 250, 255, 255, 100, 250, 64, 250, 30, 129, 98, 250, 30, 129, 220, 255, 185, 134, 255, 255, 185, 134, 125, 134, 255, 255, 159, 134, 255, 255, 225, 128, 100, 250, 3, 129, 100, 250, 125, 134, 30, 129, 159, 134, 30, 129, 225, 128, 185, 134, 3, 129, 185, 134, 181, 255, 52, 252, 215, 255, 52, 252, 113, 254, 252, 253, 147, 254, 252, 253, 160, 252, 62, 255, 194, 252, 62, 255, 214, 255, 233, 132, 248, 255, 233, 132, 148, 254, 33, 131, 182, 254, 33, 131, 144, 252, 222, 129, 178, 252, 222, 129, 9, 129, 52, 252, 43, 129, 52, 252, 77, 130, 252, 253, 111, 130, 252, 253, 30, 132, 62, 255, 64, 132, 62, 255, 232, 128, 233, 132, 10, 129, 233, 132, 42, 130, 33, 131, 76, 130, 33, 131, 46, 132, 222, 129, 80, 132, 222, 129, 84, 250, 163, 255, 84, 250, 163, 255, 84, 250, 163, 255, 84, 250, 122, 129, 147, 254, 252, 253, 43, 129, 52, 252, 43, 129, 52, 252, 43, 129, 52, 252, 43, 129, 52, 252, 43, 129, 52, 252, 255, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 98, 129, 19, 252, 122, 255, 7, 133, 122, 255, 7, 133, 122, 255, 7, 133, 122, 255, 7, 133, 42, 130, 33, 131, 225, 128, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 64, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 220, 255, 100, 250, 220, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 64, 250, 30, 129, 64, 250, 30, 129, 98, 250, 30, 129, 220, 255, 185, 134, 220, 255, 185, 134, 255, 255, 185, 134, 125, 134, 255, 255, 159, 134, 255, 255, 159, 134, 255, 255, 225, 128, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 3, 129, 100, 250, 3, 129, 100, 250, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 159, 134, 30, 129, 159, 134, 30, 129, 225, 128, 185, 134, 225, 128, 185, 134, 3, 129, 185, 134, 181, 255, 52, 252, 215, 255, 52, 252, 215, 255, 52, 252, 113, 254, 252, 253, 147, 254, 252, 253, 147, 254, 252, 253, 160, 252, 62, 255, 194, 252, 62, 255, 194, 252, 62, 255, 214, 255, 233, 132, 214, 255, 233, 132, 248, 255, 233, 132, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 182, 254, 33, 131, 182, 254, 33, 131, 182, 254, 33, 131, 144, 252, 222, 129, 144, 252, 222, 129, 178, 252, 222, 129, 9, 129, 52, 252, 9, 129, 52, 252, 43, 129, 52, 252, 43, 129, 52, 252, 77, 130, 252, 253, 77, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 30, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 232, 128, 233, 132, 232, 128, 233, 132, 232, 128, 233, 132, 10, 129, 233, 132, 42, 130, 33, 131, 42, 130, 33, 131, 76, 130, 33, 131, 76, 130, 33, 131, 76, 130, 33, 131, 46, 132, 222, 129, 46, 132, 222, 129, 80, 132, 222, 129, 174, 134, 122, 129, 84, 250, 163, 255, 153, 252, 235, 254, 156, 255, 7, 133, 159, 134, 255, 255, 98, 250, 30, 129, 215, 255, 52, 252, 147, 254, 252, 253, 43, 129, 52, 252, 10, 129, 233, 132, 98, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 255, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 3, 129, 100, 250, 3, 129, 100, 250, 3, 129, 100, 250, 159, 134, 30, 129, 159, 134, 30, 129, 159, 134, 30, 129, 194, 252, 62, 255, 194, 252, 62, 255, 194, 252, 62, 255, 194, 252, 62, 255, 248, 255, 233, 132, 248, 255, 233, 132, 248, 255, 233, 132, 248, 255, 233, 132, 248, 255, 233, 132, 182, 254, 33, 131, 182, 254, 33, 131, 182, 254, 33, 131, 178, 252, 222, 129, 178, 252, 222, 129, 178, 252, 222, 129, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 64, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 76, 130, 33, 131, 76, 130, 33, 131, 80, 132, 222, 129, 80, 132, 222, 129, 80, 132, 222, 129, 119, 252, 235, 254, 92, 255, 19, 252, 122, 255, 7, 133, 125, 134, 255, 255, 125, 134, 255, 255, 64, 250, 30, 129, 64, 250, 30, 129, 9, 129, 52, 252, 9, 129, 52, 252, 232, 128, 233, 132, 232, 128, 233, 132, 42, 130, 33, 131, 42, 130, 33, 131, 64, 250, 255, 255, 64, 250, 255, 255, 64, 250, 255, 255, 64, 250, 255, 255, 220, 255, 100, 250, 220, 255, 100, 250, 220, 255, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 225, 128, 185, 134, 113, 254, 252, 253, 113, 254, 252, 253, 160, 252, 62, 255, 160, 252, 62, 255, 160, 252, 62, 255, 214, 255, 233, 132, 214, 255, 233, 132, 214, 255, 233, 132, 214, 255, 233, 132, 214, 255, 233, 132, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 144, 252, 222, 129, 144, 252, 222, 129, 144, 252, 222, 129, 144, 252, 222, 129, 144, 252, 222, 129, 77, 130, 252, 253, 77, 130, 252, 253, 77, 130, 252, 253, 30, 132, 62, 255, 30, 132, 62, 255, 30, 132, 62, 255, 30, 132, 62, 255, 64, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 98, 250, 255, 255, 220, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 255, 255, 100, 250, 64, 250, 30, 129, 64, 250, 30, 129, 98, 250, 30, 129, 220, 255, 185, 134, 220, 255, 185, 134, 220, 255, 185, 134, 255, 255, 185, 134, 125, 134, 255, 255, 159, 134, 255, 255, 159, 134, 255, 255, 225, 128, 100, 250, 225, 128, 100, 250, 225, 128, 100, 250, 3, 129, 100, 250, 3, 129, 100, 250, 125, 134, 30, 129, 125, 134, 30, 129, 125, 134, 30, 129, 159, 134, 30, 129, 225, 128, 185, 134, 225, 128, 185, 134, 3, 129, 185, 134, 3, 129, 185, 134, 181, 255, 52, 252, 215, 255, 52, 252, 215, 255, 52, 252, 215, 255, 52, 252, 113, 254, 252, 253, 113, 254, 252, 253, 147, 254, 252, 253, 147, 254, 252, 253, 160, 252, 62, 255, 160, 252, 62, 255, 160, 252, 62, 255, 194, 252, 62, 255, 194, 252, 62, 255, 194, 252, 62, 255, 214, 255, 233, 132, 214, 255, 233, 132, 248, 255, 233, 132, 148, 254, 33, 131, 148, 254, 33, 131, 148, 254, 33, 131, 182, 254, 33, 131, 144, 252, 222, 129, 144, 252, 222, 129, 144, 252, 222, 129, 178, 252, 222, 129, 178, 252, 222, 129, 178, 252, 222, 129, 9, 129, 52, 252, 9, 129, 52, 252, 43, 129, 52, 252, 43, 129, 52, 252, 77, 130, 252, 253, 77, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 111, 130, 252, 253, 30, 132, 62, 255, 30, 132, 62, 255, 30, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 64, 132, 62, 255, 232, 128, 233, 132, 232, 128, 233, 132, 232, 128, 233, 132, 10, 129, 233, 132, 10, 129, 233, 132, 42, 130, 33, 131, 42, 130, 33, 131, 76, 130, 33, 131, 76, 130, 33, 131, 46, 132, 222, 129, 46, 132, 222, 129, 80, 132, 222, 129, 80, 132, 222, 129, 80, 132, 222, 129),
"format": 34896613399,
"index_count": 828,
"index_data": PackedByteArray(62, 0, 159, 0, 158, 0, 62, 0, 64, 0, 159, 0, 40, 0, 31, 0, 23, 0, 40, 0, 51, 0, 31, 0, 49, 0, 24, 0, 30, 0, 49, 0, 41, 0, 24, 0, 66, 0, 159, 0, 73, 0, 66, 0, 158, 0, 159, 0, 52, 0, 20, 0, 33, 0, 52, 0, 43, 0, 20, 0, 3, 0, 7, 0, 12, 0, 3, 0, 0, 0, 7, 0, 46, 0, 27, 0, 26, 0, 46, 0, 47, 0, 27, 0, 47, 0, 28, 0, 27, 0, 47, 0, 48, 0, 28, 0, 55, 0, 36, 0, 35, 0, 55, 0, 56, 0, 36, 0, 56, 0, 37, 0, 36, 0, 56, 0, 57, 0, 37, 0, 42, 0, 34, 0, 19, 0, 42, 0, 54, 0, 34, 0, 38, 0, 19, 0, 18, 0, 38, 0, 42, 0, 19, 0, 39, 0, 24, 0, 41, 0, 39, 0, 25, 0, 24, 0, 1, 0, 23, 0, 5, 0, 1, 0, 22, 0, 23, 0, 2, 0, 59, 0, 58, 0, 2, 0, 4, 0, 59, 0, 13, 0, 27, 0, 14, 0, 13, 0, 26, 0, 27, 0, 14, 0, 28, 0, 12, 0, 14, 0, 27, 0, 28, 0, 17, 0, 30, 0, 15, 0, 17, 0, 29, 0, 30, 0, 16, 0, 29, 0, 17, 0, 16, 0, 31, 0, 29, 0, 8, 0, 33, 0, 6, 0, 8, 0, 32, 0, 33, 0, 7, 0, 32, 0, 8, 0, 7, 0, 34, 0, 32, 0, 10, 0, 36, 0, 11, 0, 10, 0, 35, 0, 36, 0, 11, 0, 37, 0, 9, 0, 11, 0, 36, 0, 37, 0, 0, 0, 34, 0, 7, 0, 0, 0, 19, 0, 34, 0, 6, 0, 20, 0, 58, 0, 6, 0, 33, 0, 20, 0, 9, 0, 22, 0, 1, 0, 9, 0, 37, 0, 22, 0, 2, 0, 35, 0, 10, 0, 2, 0, 21, 0, 35, 0, 12, 0, 18, 0, 3, 0, 12, 0, 28, 0, 18, 0, 59, 0, 26, 0, 13, 0, 59, 0, 25, 0, 26, 0, 5, 0, 31, 0, 16, 0, 5, 0, 23, 0, 31, 0, 15, 0, 24, 0, 4, 0, 15, 0, 30, 0, 24, 0, 127, 0, 54, 0, 42, 0, 127, 0, 151, 0, 54, 0, 155, 0, 57, 0, 56, 0, 155, 0, 157, 0, 57, 0, 153, 0, 56, 0, 55, 0, 153, 0, 155, 0, 56, 0, 32, 0, 54, 0, 53, 0, 32, 0, 34, 0, 54, 0, 33, 0, 53, 0, 52, 0, 33, 0, 32, 0, 53, 0, 29, 0, 51, 0, 50, 0, 29, 0, 31, 0, 51, 0, 30, 0, 50, 0, 49, 0, 30, 0, 29, 0, 50, 0, 137, 0, 48, 0, 47, 0, 137, 0, 139, 0, 48, 0, 135, 0, 47, 0, 46, 0, 135, 0, 137, 0, 47, 0, 18, 0, 48, 0, 38, 0, 18, 0, 28, 0, 48, 0, 35, 0, 45, 0, 55, 0, 35, 0, 21, 0, 45, 0, 147, 0, 43, 0, 52, 0, 147, 0, 129, 0, 43, 0, 141, 0, 41, 0, 49, 0, 141, 0, 125, 0, 41, 0, 123, 0, 51, 0, 40, 0, 123, 0, 145, 0, 51, 0, 26, 0, 39, 0, 46, 0, 26, 0, 25, 0, 39, 0, 45, 0, 129, 0, 133, 0, 45, 0, 43, 0, 129, 0, 22, 0, 57, 0, 44, 0, 22, 0, 37, 0, 57, 0, 23, 0, 44, 0, 40, 0, 23, 0, 22, 0, 44, 0, 59, 0, 24, 0, 25, 0, 59, 0, 4, 0, 24, 0, 2, 0, 15, 0, 4, 0, 2, 0, 10, 0, 15, 0, 10, 0, 17, 0, 15, 0, 10, 0, 11, 0, 17, 0, 11, 0, 16, 0, 17, 0, 11, 0, 9, 0, 16, 0, 9, 0, 5, 0, 16, 0, 9, 0, 1, 0, 5, 0, 3, 0, 19, 0, 0, 0, 3, 0, 18, 0, 19, 0, 7, 0, 14, 0, 12, 0, 7, 0, 8, 0, 14, 0, 8, 0, 13, 0, 14, 0, 8, 0, 6, 0, 13, 0, 20, 0, 45, 0, 21, 0, 20, 0, 43, 0, 45, 0, 58, 0, 21, 0, 2, 0, 58, 0, 20, 0, 21, 0, 40, 0, 131, 0, 123, 0, 40, 0, 44, 0, 131, 0, 44, 0, 157, 0, 131, 0, 44, 0, 57, 0, 157, 0, 46, 0, 121, 0, 135, 0, 46, 0, 39, 0, 121, 0, 55, 0, 133, 0, 153, 0, 55, 0, 45, 0, 133, 0, 38, 0, 139, 0, 119, 0, 38, 0, 48, 0, 139, 0, 49, 0, 143, 0, 141, 0, 49, 0, 50, 0, 143, 0, 50, 0, 145, 0, 143, 0, 50, 0, 51, 0, 145, 0, 52, 0, 149, 0, 147, 0, 52, 0, 53, 0, 149, 0, 53, 0, 151, 0, 149, 0, 53, 0, 54, 0, 151, 0, 119, 0, 42, 0, 38, 0, 119, 0, 127, 0, 42, 0, 158, 0, 81, 0, 62, 0, 158, 0, 80, 0, 81, 0, 105, 0, 128, 0, 132, 0, 105, 0, 103, 0, 128, 0, 100, 0, 91, 0, 83, 0, 100, 0, 111, 0, 91, 0, 109, 0, 84, 0, 90, 0, 109, 0, 101, 0, 84, 0, 112, 0, 80, 0, 93, 0, 112, 0, 103, 0, 80, 0, 63, 0, 67, 0, 72, 0, 63, 0, 60, 0, 67, 0, 106, 0, 87, 0, 86, 0, 106, 0, 107, 0, 87, 0, 107, 0, 88, 0, 87, 0, 107, 0, 108, 0, 88, 0, 115, 0, 96, 0, 95, 0, 115, 0, 116, 0, 96, 0, 116, 0, 97, 0, 96, 0, 116, 0, 117, 0, 97, 0, 102, 0, 94, 0, 79, 0, 102, 0, 114, 0, 94, 0, 98, 0, 79, 0, 78, 0, 98, 0, 102, 0, 79, 0, 99, 0, 84, 0, 101, 0, 99, 0, 85, 0, 84, 0, 61, 0, 83, 0, 65, 0, 61, 0, 82, 0, 83, 0, 64, 0, 85, 0, 159, 0, 64, 0, 84, 0, 85, 0, 73, 0, 87, 0, 74, 0, 73, 0, 86, 0, 87, 0, 74, 0, 88, 0, 72, 0, 74, 0, 87, 0, 88, 0, 77, 0, 90, 0, 75, 0, 77, 0, 89, 0, 90, 0, 76, 0, 89, 0, 77, 0, 76, 0, 91, 0, 89, 0, 68, 0, 93, 0, 66, 0, 68, 0, 92, 0, 93, 0, 67, 0, 92, 0, 68, 0, 67, 0, 94, 0, 92, 0, 70, 0, 96, 0, 71, 0, 70, 0, 95, 0, 96, 0, 71, 0, 97, 0, 69, 0, 71, 0, 96, 0, 97, 0, 60, 0, 94, 0, 67, 0, 60, 0, 79, 0, 94, 0, 66, 0, 80, 0, 158, 0, 66, 0, 93, 0, 80, 0, 69, 0, 82, 0, 61, 0, 69, 0, 97, 0, 82, 0, 62, 0, 95, 0, 70, 0, 62, 0, 81, 0, 95, 0, 72, 0, 78, 0, 63, 0, 72, 0, 88, 0, 78, 0, 159, 0, 86, 0, 73, 0, 159, 0, 85, 0, 86, 0, 65, 0, 91, 0, 76, 0, 65, 0, 83, 0, 91, 0, 75, 0, 84, 0, 64, 0, 75, 0, 90, 0, 84, 0, 126, 0, 114, 0, 102, 0, 126, 0, 150, 0, 114, 0, 154, 0, 117, 0, 116, 0, 154, 0, 156, 0, 117, 0, 152, 0, 116, 0, 115, 0, 152, 0, 154, 0, 116, 0, 92, 0, 114, 0, 113, 0, 92, 0, 94, 0, 114, 0, 93, 0, 113, 0, 112, 0, 93, 0, 92, 0, 113, 0, 89, 0, 111, 0, 110, 0, 89, 0, 91, 0, 111, 0, 90, 0, 110, 0, 109, 0, 90, 0, 89, 0, 110, 0, 136, 0, 108, 0, 107, 0, 136, 0, 138, 0, 108, 0, 134, 0, 107, 0, 106, 0, 134, 0, 136, 0, 107, 0, 63, 0, 79, 0, 60, 0, 63, 0, 78, 0, 79, 0, 78, 0, 108, 0, 98, 0, 78, 0, 88, 0, 108, 0, 95, 0, 105, 0, 115, 0, 95, 0, 81, 0, 105, 0, 146, 0, 103, 0, 112, 0, 146, 0, 128, 0, 103, 0, 140, 0, 101, 0, 109, 0, 140, 0, 124, 0, 101, 0, 122, 0, 111, 0, 100, 0, 122, 0, 144, 0, 111, 0, 86, 0, 99, 0, 106, 0, 86, 0, 85, 0, 99, 0, 82, 0, 117, 0, 104, 0, 82, 0, 97, 0, 117, 0, 83, 0, 104, 0, 100, 0, 83, 0, 82, 0, 104, 0, 124, 0, 99, 0, 101, 0, 124, 0, 120, 0, 99, 0, 62, 0, 75, 0, 64, 0, 62, 0, 70, 0, 75, 0, 70, 0, 77, 0, 75, 0, 70, 0, 71, 0, 77, 0, 71, 0, 76, 0, 77, 0, 71, 0, 69, 0, 76, 0, 69, 0, 65, 0, 76, 0, 69, 0, 61, 0, 65, 0, 118, 0, 102, 0, 98, 0, 118, 0, 126, 0, 102, 0, 67, 0, 74, 0, 72, 0, 67, 0, 68, 0, 74, 0, 68, 0, 73, 0, 74, 0, 68, 0, 66, 0, 73, 0, 80, 0, 105, 0, 81, 0, 80, 0, 103, 0, 105, 0, 121, 0, 41, 0, 125, 0, 121, 0, 39, 0, 41, 0, 100, 0, 130, 0, 122, 0, 100, 0, 104, 0, 130, 0, 104, 0, 156, 0, 130, 0, 104, 0, 117, 0, 156, 0, 106, 0, 120, 0, 134, 0, 106, 0, 99, 0, 120, 0, 115, 0, 132, 0, 152, 0, 115, 0, 105, 0, 132, 0, 98, 0, 138, 0, 118, 0, 98, 0, 108, 0, 138, 0, 109, 0, 142, 0, 140, 0, 109, 0, 110, 0, 142, 0, 110, 0, 144, 0, 142, 0, 110, 0, 111, 0, 144, 0, 112, 0, 148, 0, 146, 0, 112, 0, 113, 0, 148, 0, 113, 0, 150, 0, 148, 0, 113, 0, 114, 0, 150, 0, 6, 0, 59, 0, 13, 0, 6, 0, 58, 0, 59, 0),
"lods": [0.0409402, PackedByteArray(70, 0, 73, 0, 66, 0, 73, 0, 148, 1, 72, 0, 66, 0, 73, 0, 72, 0, 72, 0, 127, 1, 63, 0, 63, 0, 66, 0, 72, 0, 63, 0, 60, 0, 66, 0, 63, 0, 117, 1, 60, 0, 63, 0, 126, 1, 117, 1, 60, 0, 117, 1, 170, 1, 60, 0, 170, 1, 66, 0, 66, 0, 170, 1, 167, 1, 66, 0, 167, 1, 120, 1, 126, 1, 114, 0, 116, 1, 66, 0, 120, 1, 122, 1, 120, 1, 145, 1, 122, 1, 120, 1, 134, 1, 145, 1, 66, 0, 122, 1, 70, 0, 70, 0, 122, 1, 125, 1, 121, 1, 113, 0, 134, 1, 134, 1, 113, 0, 236, 1, 134, 1, 236, 1, 233, 1, 70, 0, 125, 1, 61, 0, 61, 0, 125, 1, 136, 1, 248, 1, 253, 1, 136, 1, 247, 1, 136, 1, 144, 1, 61, 0, 136, 1, 118, 1, 157, 1, 118, 1, 162, 1, 115, 1, 157, 1, 152, 1, 61, 0, 118, 1, 65, 0, 65, 0, 118, 1, 75, 0, 61, 0, 65, 0, 75, 0, 75, 0, 118, 1, 158, 1, 70, 0, 61, 0, 75, 0, 70, 0, 75, 0, 73, 0, 75, 0, 152, 1, 73, 0, 130, 1, 73, 0, 152, 1, 186, 1, 130, 1, 154, 1, 130, 1, 147, 1, 114, 1, 205, 1, 147, 1, 130, 1, 205, 1, 209, 1, 147, 1, 209, 1, 149, 1, 147, 1, 113, 1, 147, 1, 149, 1, 113, 1, 149, 1, 126, 1, 132, 1, 178, 1, 205, 1, 187, 1, 178, 1, 131, 1, 174, 1, 172, 1, 126, 1, 174, 1, 189, 1, 172, 1, 189, 1, 241, 1, 171, 1, 128, 1, 214, 1, 174, 1, 63, 1, 6, 0, 64, 1, 64, 1, 72, 1, 63, 1, 63, 1, 72, 1, 66, 1, 64, 1, 85, 1, 72, 1, 63, 1, 66, 1, 0, 0, 63, 1, 0, 0, 6, 0, 0, 0, 66, 1, 103, 1, 0, 0, 103, 1, 6, 0, 6, 0, 103, 1, 100, 1, 64, 1, 69, 1, 85, 1, 6, 0, 100, 1, 70, 1, 13, 0, 69, 1, 64, 1, 6, 0, 13, 0, 64, 1, 13, 0, 68, 1, 69, 1, 6, 0, 70, 1, 71, 1, 206, 1, 85, 1, 76, 1, 6, 0, 71, 1, 10, 0, 10, 0, 13, 0, 6, 0, 10, 0, 15, 0, 13, 0, 13, 0, 65, 1, 68, 1, 76, 1, 68, 1, 65, 1, 76, 1, 65, 1, 89, 1, 89, 1, 50, 0, 143, 0, 91, 1, 143, 0, 221, 1, 65, 1, 94, 1, 89, 1, 221, 1, 188, 1, 90, 1, 180, 1, 92, 1, 188, 1, 17, 0, 94, 1, 65, 1, 10, 0, 17, 0, 65, 1, 17, 0, 67, 1, 94, 1, 94, 1, 67, 1, 97, 1, 97, 1, 131, 0, 123, 0, 97, 1, 82, 1, 131, 0, 5, 0, 67, 1, 17, 0, 62, 1, 82, 1, 67, 1, 62, 1, 67, 1, 5, 0, 62, 1, 5, 0, 17, 0, 10, 0, 62, 1, 17, 0, 62, 1, 110, 1, 82, 1, 10, 0, 110, 1, 62, 1, 10, 0, 108, 1, 110, 1, 10, 0, 71, 1, 108, 1, 108, 1, 196, 1, 203, 1, 70, 1, 79, 1, 108, 1, 234, 1, 195, 1, 79, 1, 109, 1, 79, 1, 195, 1, 109, 1, 204, 1, 251, 1, 250, 1, 254, 1, 109, 1, 254, 1, 111, 1, 109, 1, 84, 1, 111, 1, 4, 2, 39, 0, 27, 0, 26, 0, 39, 0, 48, 0, 27, 0, 38, 0, 54, 0, 19, 0, 190, 1, 244, 1, 104, 1, 255, 1, 2, 2, 112, 1, 33, 0, 53, 0, 43, 0, 53, 0, 105, 1, 246, 1, 33, 0, 56, 0, 35, 0, 211, 1, 216, 1, 86, 1, 207, 1, 212, 1, 87, 1, 184, 1, 229, 1, 98, 1, 23, 0, 44, 0, 51, 0, 83, 1, 3, 2, 200, 1, 77, 1, 179, 1, 208, 1, 73, 1, 217, 1, 175, 1, 74, 1, 88, 1, 218, 1, 95, 1, 230, 1, 225, 1, 96, 1, 99, 1, 231, 1, 80, 1, 238, 1, 235, 1, 81, 1, 101, 1, 239, 1, 102, 1, 245, 1, 240, 1, 176, 1, 106, 1, 75, 1, 177, 1, 191, 1, 107, 1, 141, 1, 192, 1, 201, 1, 142, 1, 133, 1, 193, 1, 143, 1, 124, 1, 123, 1, 105, 0, 104, 0, 96, 0, 252, 1, 0, 2, 137, 1, 210, 1, 213, 1, 150, 1, 232, 1, 194, 1, 135, 1, 219, 1, 185, 1, 153, 1, 182, 1, 226, 1, 163, 1, 119, 1, 138, 1, 164, 1, 181, 1, 78, 1, 93, 1, 165, 1, 197, 1, 183, 1, 166, 1, 139, 1, 198, 1, 140, 1, 1, 2, 199, 1, 146, 1, 202, 1, 249, 1, 129, 1, 151, 1, 215, 1, 155, 1, 222, 1, 220, 1, 156, 1, 159, 1, 223, 1, 160, 1, 227, 1, 224, 1, 161, 1, 111, 0, 228, 1, 168, 1, 242, 1, 237, 1, 169, 1, 173, 1, 243, 1), 0.0674687, PackedByteArray(223, 0, 246, 0, 66, 0, 66, 0, 227, 0, 235, 0, 66, 0, 234, 0, 223, 0, 50, 1, 229, 0, 234, 0, 223, 0, 75, 0, 246, 0, 223, 0, 229, 0, 75, 0, 51, 1, 55, 1, 229, 0, 229, 0, 65, 0, 75, 0, 75, 0, 65, 0, 237, 0, 237, 0, 9, 1, 252, 0, 65, 0, 104, 0, 110, 0, 105, 0, 104, 0, 96, 0, 17, 1, 20, 1, 246, 0, 246, 0, 20, 1, 242, 0, 242, 0, 20, 1, 23, 1, 66, 0, 246, 0, 242, 0, 242, 0, 60, 0, 66, 0, 60, 0, 42, 1, 66, 0, 242, 0, 1, 1, 42, 1, 1, 1, 47, 1, 42, 1, 118, 0, 148, 0, 60, 0, 3, 0, 0, 0, 205, 0, 0, 0, 213, 0, 205, 0, 3, 0, 53, 0, 0, 0, 245, 0, 3, 1, 213, 0, 205, 0, 204, 0, 3, 0, 10, 0, 204, 0, 205, 0, 33, 0, 155, 0, 10, 0, 10, 0, 56, 1, 12, 1, 10, 0, 15, 0, 204, 0, 210, 0, 204, 0, 15, 0, 18, 1, 3, 0, 210, 0, 18, 1, 22, 1, 3, 0, 39, 0, 3, 0, 27, 0, 39, 0, 15, 0, 143, 0, 5, 0, 131, 0, 143, 0, 10, 0, 13, 1, 15, 0, 13, 1, 203, 0, 15, 0, 15, 0, 203, 0, 32, 1, 57, 1, 61, 1, 13, 1, 57, 1, 16, 1, 53, 1, 57, 1, 8, 1, 16, 1, 33, 1, 13, 1, 253, 0, 253, 0, 37, 1, 33, 1, 28, 1, 0, 1, 33, 1, 249, 0, 33, 1, 0, 1, 2, 1, 48, 1, 214, 0, 21, 1, 24, 1, 200, 0, 40, 1, 7, 1, 206, 0, 58, 1, 207, 0, 129, 0, 211, 0, 248, 0, 19, 1, 201, 0, 25, 1, 243, 0, 208, 0, 44, 1, 41, 1, 209, 0, 215, 0, 45, 1, 216, 0, 49, 1, 46, 1, 244, 0, 217, 0, 202, 0, 232, 0, 4, 1, 14, 1, 233, 0, 224, 0, 5, 1, 54, 1, 59, 1, 228, 0, 218, 0, 43, 1, 225, 0, 225, 0, 43, 1, 39, 1, 38, 1, 6, 1, 226, 0, 26, 1, 254, 0, 219, 0, 251, 0, 35, 1, 238, 0, 255, 0, 247, 0, 220, 0, 250, 0, 212, 0, 34, 1, 239, 0, 230, 0, 10, 1, 231, 0, 60, 1, 11, 1, 236, 0, 15, 1, 52, 1, 221, 0, 29, 1, 27, 1, 222, 0, 240, 0, 30, 1, 241, 0, 36, 1, 31, 1), 0.195405, PackedByteArray(174, 0, 162, 0, 190, 0, 174, 0, 190, 0, 170, 0, 188, 0, 170, 0, 190, 0, 190, 0, 168, 0, 192, 0, 160, 0, 168, 0, 190, 0, 190, 0, 162, 0, 160, 0, 162, 0, 178, 0, 160, 0, 160, 0, 178, 0, 180, 0, 176, 0, 178, 0, 162, 0, 174, 0, 182, 0, 162, 0, 166, 0, 162, 0, 182, 0, 174, 0, 198, 0, 182, 0, 194, 0, 198, 0, 174, 0, 194, 0, 196, 0, 198, 0, 198, 0, 186, 0, 182, 0, 182, 0, 186, 0, 184, 0, 186, 0, 198, 0, 172, 0, 186, 0, 172, 0, 164, 0, 197, 0, 177, 0, 189, 0, 177, 0, 179, 0, 181, 0, 189, 0, 177, 0, 181, 0, 161, 0, 189, 0, 181, 0, 161, 0, 169, 0, 189, 0, 169, 0, 193, 0, 189, 0, 189, 0, 193, 0, 191, 0, 197, 0, 189, 0, 171, 0, 197, 0, 171, 0, 175, 0, 197, 0, 175, 0, 195, 0, 197, 0, 185, 0, 177, 0, 163, 0, 177, 0, 185, 0, 163, 0, 185, 0, 167, 0, 183, 0, 167, 0, 185, 0, 197, 0, 173, 0, 185, 0, 197, 0, 199, 0, 173, 0, 185, 0, 173, 0, 165, 0, 165, 0, 187, 0, 185, 0)],
"material": SubResource("StandardMaterial3D_3spdg"),
"name": "CardBase",
"primitive": 3,
"uv_scale": Vector4(2.00224, 2.00019, 0, 0),
"vertex_count": 517,
"vertex_data": PackedByteArray(246, 255, 185, 0, 146, 244, 255, 191, 246, 255, 69, 255, 146, 244, 255, 191, 246, 255, 174, 244, 69, 255, 255, 191, 246, 255, 185, 0, 108, 11, 255, 191, 246, 255, 174, 244, 185, 0, 255, 191, 246, 255, 69, 255, 108, 11, 255, 191, 246, 255, 232, 7, 251, 254, 255, 191, 246, 255, 44, 2, 37, 249, 255, 191, 246, 255, 148, 4, 156, 252, 255, 191, 246, 255, 209, 253, 9, 249, 255, 191, 246, 255, 28, 248, 56, 255, 255, 191, 246, 255, 102, 251, 229, 252, 255, 191, 246, 255, 44, 2, 217, 6, 255, 191, 246, 255, 232, 7, 3, 1, 255, 191, 246, 255, 148, 4, 98, 3, 255, 191, 246, 255, 28, 248, 198, 0, 255, 191, 246, 255, 209, 253, 245, 6, 255, 191, 246, 255, 102, 251, 25, 3, 255, 191, 246, 255, 0, 0, 79, 11, 34, 204, 246, 255, 0, 0, 175, 244, 108, 193, 246, 255, 79, 11, 255, 255, 167, 159, 246, 255, 175, 244, 255, 255, 237, 159, 246, 255, 255, 255, 175, 244, 78, 193, 246, 255, 255, 255, 79, 11, 72, 204, 246, 255, 175, 244, 0, 0, 16, 224, 246, 255, 79, 11, 0, 0, 94, 224, 246, 255, 166, 7, 79, 0, 44, 225, 246, 255, 14, 4, 221, 2, 179, 219, 246, 255, 131, 1, 134, 6, 249, 212, 246, 255, 240, 251, 151, 2, 128, 219, 246, 255, 88, 248, 13, 0, 162, 225, 246, 255, 123, 254, 166, 6, 80, 212, 246, 255, 14, 4, 33, 253, 68, 172, 246, 255, 166, 7, 175, 255, 48, 161, 246, 255, 131, 1, 120, 249, 154, 182, 246, 255, 88, 248, 241, 255, 97, 160, 246, 255, 240, 251, 103, 253, 254, 172, 246, 255, 123, 254, 88, 249, 39, 183, 234, 217, 0, 0, 79, 11, 123, 200, 234, 217, 79, 11, 0, 0, 195, 254, 234, 217, 255, 255, 79, 11, 174, 200, 234, 217, 175, 244, 0, 0, 199, 255, 234, 217, 0, 0, 175, 244, 124, 72, 234, 217, 79, 11, 255, 255, 196, 126, 234, 217, 255, 255, 175, 244, 175, 72, 234, 217, 175, 244, 255, 255, 200, 127, 234, 217, 166, 7, 79, 0, 42, 245, 234, 217, 14, 4, 221, 2, 23, 230, 234, 217, 131, 1, 134, 6, 44, 216, 234, 217, 88, 248, 13, 0, 64, 246, 234, 217, 240, 251, 151, 2, 27, 229, 234, 217, 123, 254, 166, 6, 81, 215, 234, 217, 166, 7, 175, 255, 43, 117, 234, 217, 14, 4, 33, 253, 24, 102, 234, 217, 131, 1, 120, 249, 44, 88, 234, 217, 88, 248, 241, 255, 64, 118, 234, 217, 240, 251, 103, 253, 27, 101, 234, 217, 123, 254, 88, 249, 82, 87, 246, 255, 87, 11, 69, 255, 255, 191, 246, 255, 87, 11, 185, 0, 255, 191, 0, 0, 185, 0, 146, 244, 255, 191, 0, 0, 69, 255, 146, 244, 255, 191, 0, 0, 174, 244, 69, 255, 255, 191, 0, 0, 185, 0, 108, 11, 255, 191, 0, 0, 174, 244, 185, 0, 255, 191, 0, 0, 69, 255, 108, 11, 255, 191, 0, 0, 232, 7, 251, 254, 255, 191, 0, 0, 44, 2, 37, 249, 255, 191, 0, 0, 148, 4, 156, 252, 255, 191, 0, 0, 209, 253, 9, 249, 255, 191, 0, 0, 28, 248, 56, 255, 255, 191, 0, 0, 102, 251, 229, 252, 255, 191, 0, 0, 44, 2, 217, 6, 255, 191, 0, 0, 232, 7, 3, 1, 255, 191, 0, 0, 148, 4, 98, 3, 255, 191, 0, 0, 28, 248, 198, 0, 255, 191, 0, 0, 209, 253, 245, 6, 255, 191, 0, 0, 102, 251, 25, 3, 255, 191, 0, 0, 0, 0, 79, 11, 108, 193, 0, 0, 0, 0, 175, 244, 34, 204, 0, 0, 79, 11, 255, 255, 94, 224, 0, 0, 175, 244, 255, 255, 16, 224, 0, 0, 255, 255, 175, 244, 72, 204, 0, 0, 255, 255, 79, 11, 78, 193, 0, 0, 175, 244, 0, 0, 237, 159, 0, 0, 79, 11, 0, 0, 167, 159, 0, 0, 166, 7, 79, 0, 48, 161, 0, 0, 14, 4, 221, 2, 68, 172, 0, 0, 131, 1, 134, 6, 154, 182, 0, 0, 240, 251, 151, 2, 255, 172, 0, 0, 88, 248, 13, 0, 97, 160, 0, 0, 123, 254, 166, 6, 39, 183, 0, 0, 14, 4, 33, 253, 179, 219, 0, 0, 166, 7, 175, 255, 44, 225, 0, 0, 131, 1, 120, 249, 249, 212, 0, 0, 88, 248, 241, 255, 162, 225, 0, 0, 240, 251, 103, 253, 128, 219, 0, 0, 123, 254, 88, 249, 80, 212, 11, 38, 0, 0, 79, 11, 124, 72, 11, 38, 79, 11, 0, 0, 196, 126, 11, 38, 255, 255, 79, 11, 175, 72, 11, 38, 175, 244, 0, 0, 200, 127, 11, 38, 0, 0, 175, 244, 123, 200, 11, 38, 79, 11, 255, 255, 195, 254, 11, 38, 255, 255, 175, 244, 174, 200, 11, 38, 175, 244, 255, 255, 199, 255, 11, 38, 166, 7, 79, 0, 43, 117, 11, 38, 14, 4, 221, 2, 24, 102, 11, 38, 131, 1, 134, 6, 44, 88, 11, 38, 88, 248, 13, 0, 64, 118, 11, 38, 240, 251, 151, 2, 27, 101, 11, 38, 123, 254, 166, 6, 82, 87, 11, 38, 166, 7, 175, 255, 42, 245, 11, 38, 14, 4, 33, 253, 23, 230, 11, 38, 131, 1, 120, 249, 44, 216, 11, 38, 88, 248, 241, 255, 64, 246, 11, 38, 240, 251, 103, 253, 27, 229, 11, 38, 123, 254, 88, 249, 81, 215, 251, 127, 0, 0, 79, 11, 255, 191, 251, 127, 0, 0, 79, 11, 255, 191, 251, 127, 79, 11, 0, 0, 255, 191, 251, 127, 79, 11, 0, 0, 255, 191, 251, 127, 255, 255, 79, 11, 255, 191, 251, 127, 255, 255, 79, 11, 255, 191, 251, 127, 175, 244, 0, 0, 255, 191, 251, 127, 175, 244, 0, 0, 255, 191, 251, 127, 0, 0, 175, 244, 255, 191, 251, 127, 0, 0, 175, 244, 255, 191, 251, 127, 79, 11, 255, 255, 254, 127, 251, 127, 79, 11, 255, 255, 254, 127, 251, 127, 255, 255, 175, 244, 255, 191, 251, 127, 255, 255, 175, 244, 255, 191, 251, 127, 175, 244, 255, 255, 255, 191, 251, 127, 175, 244, 255, 255, 255, 191, 251, 127, 166, 7, 79, 0, 255, 191, 251, 127, 166, 7, 79, 0, 255, 191, 251, 127, 14, 4, 221, 2, 27, 109, 251, 127, 14, 4, 221, 2, 27, 109, 251, 127, 131, 1, 134, 6, 255, 255, 251, 127, 131, 1, 134, 6, 255, 255, 251, 127, 88, 248, 13, 0, 255, 191, 251, 127, 88, 248, 13, 0, 255, 191, 251, 127, 240, 251, 151, 2, 255, 223, 251, 127, 240, 251, 151, 2, 255, 223, 251, 127, 123, 254, 166, 6, 255, 191, 251, 127, 123, 254, 166, 6, 255, 191, 251, 127, 166, 7, 175, 255, 255, 191, 251, 127, 166, 7, 175, 255, 255, 191, 251, 127, 14, 4, 33, 253, 255, 223, 251, 127, 14, 4, 33, 253, 255, 223, 251, 127, 131, 1, 120, 249, 254, 127, 251, 127, 131, 1, 120, 249, 254, 127, 251, 127, 88, 248, 241, 255, 255, 191, 251, 127, 88, 248, 241, 255, 255, 191, 251, 127, 240, 251, 103, 253, 27, 109, 251, 127, 240, 251, 103, 253, 27, 109, 251, 127, 123, 254, 88, 249, 227, 82, 251, 127, 123, 254, 88, 249, 227, 82, 0, 0, 87, 11, 69, 255, 255, 191, 0, 0, 87, 11, 185, 0, 255, 191, 251, 127, 0, 0, 79, 11, 252, 184, 251, 127, 0, 0, 79, 11, 58, 196, 251, 127, 79, 11, 0, 0, 87, 166, 251, 127, 79, 11, 0, 0, 235, 165, 251, 127, 255, 255, 79, 11, 207, 186, 251, 127, 255, 255, 79, 11, 59, 180, 251, 127, 175, 244, 0, 0, 148, 150, 251, 127, 175, 244, 0, 0, 167, 164, 251, 127, 0, 0, 175, 244, 198, 203, 251, 127, 0, 0, 175, 244, 176, 201, 251, 127, 79, 11, 255, 255, 136, 32, 251, 127, 79, 11, 255, 255, 193, 39, 251, 127, 255, 255, 175, 244, 123, 192, 251, 127, 255, 255, 175, 244, 206, 201, 251, 127, 175, 244, 255, 255, 213, 215, 251, 127, 175, 244, 255, 255, 171, 217, 251, 127, 166, 7, 79, 0, 194, 160, 251, 127, 166, 7, 79, 0, 24, 171, 251, 127, 14, 4, 221, 2, 13, 84, 251, 127, 14, 4, 221, 2, 3, 89, 251, 127, 131, 1, 134, 6, 53, 176, 251, 127, 131, 1, 134, 6, 205, 174, 251, 127, 88, 248, 13, 0, 2, 166, 251, 127, 88, 248, 13, 0, 236, 161, 251, 127, 240, 251, 151, 2, 33, 164, 251, 127, 240, 251, 151, 2, 213, 171, 251, 127, 123, 254, 166, 6, 8, 177, 251, 127, 123, 254, 166, 6, 208, 173, 251, 127, 166, 7, 175, 255, 253, 224, 251, 127, 166, 7, 175, 255, 215, 212, 251, 127, 14, 4, 33, 253, 84, 212, 251, 127, 14, 4, 33, 253, 88, 221, 251, 127, 131, 1, 120, 249, 116, 44, 251, 127, 131, 1, 120, 249, 68, 43, 251, 127, 88, 248, 241, 255, 217, 216, 251, 127, 88, 248, 241, 255, 105, 224, 251, 127, 240, 251, 103, 253, 68, 39, 251, 127, 240, 251, 103, 253, 2, 46, 251, 127, 123, 254, 88, 249, 103, 49, 251, 127, 123, 254, 88, 249, 187, 46, 246, 255, 185, 0, 108, 11, 7, 219, 246, 255, 185, 0, 108, 11, 65, 228, 246, 255, 185, 0, 108, 11, 145, 207, 246, 255, 69, 255, 108, 11, 202, 194, 246, 255, 14, 4, 221, 2, 233, 203, 246, 255, 166, 7, 175, 255, 74, 178, 246, 255, 166, 7, 175, 255, 119, 153, 246, 255, 166, 7, 175, 255, 67, 152, 246, 255, 166, 7, 175, 255, 107, 152, 246, 255, 166, 7, 175, 255, 158, 159, 234, 217, 79, 11, 0, 0, 213, 217, 234, 217, 79, 11, 0, 0, 102, 227, 234, 217, 79, 11, 0, 0, 135, 218, 234, 217, 14, 4, 33, 253, 62, 82, 234, 217, 14, 4, 33, 253, 95, 104, 234, 217, 14, 4, 33, 253, 38, 109, 234, 217, 14, 4, 33, 253, 33, 122, 234, 217, 14, 4, 33, 253, 16, 61, 0, 0, 232, 7, 251, 254, 238, 235, 0, 0, 28, 248, 198, 0, 82, 183, 0, 0, 28, 248, 198, 0, 168, 188, 0, 0, 28, 248, 198, 0, 4, 172, 0, 0, 28, 248, 198, 0, 133, 186, 0, 0, 240, 251, 103, 253, 160, 203, 11, 38, 79, 11, 255, 255, 32, 255, 11, 38, 79, 11, 255, 255, 226, 238, 11, 38, 79, 11, 255, 255, 209, 195, 11, 38, 79, 11, 255, 255, 161, 240, 11, 38, 255, 255, 175, 244, 109, 191, 11, 38, 255, 255, 175, 244, 215, 208, 11, 38, 255, 255, 175, 244, 123, 194, 11, 38, 255, 255, 175, 244, 88, 190, 11, 38, 175, 244, 255, 255, 133, 228, 11, 38, 175, 244, 255, 255, 140, 255, 11, 38, 175, 244, 255, 255, 33, 217, 11, 38, 175, 244, 255, 255, 82, 240, 11, 38, 175, 244, 255, 255, 229, 221, 11, 38, 240, 251, 151, 2, 97, 76, 11, 38, 240, 251, 151, 2, 132, 85, 11, 38, 240, 251, 151, 2, 54, 66, 11, 38, 240, 251, 151, 2, 250, 108, 11, 38, 240, 251, 151, 2, 74, 78, 251, 127, 0, 0, 79, 11, 207, 186, 251, 127, 0, 0, 79, 11, 107, 235, 251, 127, 0, 0, 79, 11, 53, 199, 251, 127, 0, 0, 79, 11, 55, 196, 251, 127, 79, 11, 0, 0, 192, 170, 251, 127, 79, 11, 0, 0, 217, 152, 251, 127, 79, 11, 0, 0, 26, 206, 251, 127, 79, 11, 0, 0, 28, 161, 251, 127, 79, 11, 0, 0, 212, 204, 251, 127, 255, 255, 79, 11, 30, 171, 251, 127, 255, 255, 79, 11, 213, 188, 251, 127, 255, 255, 79, 11, 202, 179, 251, 127, 175, 244, 0, 0, 21, 170, 251, 127, 175, 244, 0, 0, 126, 153, 251, 127, 175, 244, 0, 0, 107, 162, 251, 127, 0, 0, 175, 244, 204, 203, 251, 127, 0, 0, 175, 244, 156, 146, 251, 127, 0, 0, 175, 244, 55, 196, 251, 127, 79, 11, 255, 255, 140, 94, 251, 127, 79, 11, 255, 255, 150, 0, 251, 127, 79, 11, 255, 255, 150, 83, 251, 127, 79, 11, 255, 255, 172, 96, 251, 127, 79, 11, 255, 255, 177, 40, 251, 127, 255, 255, 175, 244, 63, 193, 251, 127, 255, 255, 175, 244, 181, 188, 251, 127, 255, 255, 175, 244, 196, 176, 251, 127, 255, 255, 175, 244, 128, 166, 251, 127, 255, 255, 175, 244, 120, 200, 251, 127, 175, 244, 255, 255, 132, 205, 251, 127, 175, 244, 255, 255, 242, 211, 251, 127, 175, 244, 255, 255, 146, 214, 251, 127, 166, 7, 79, 0, 122, 159, 251, 127, 166, 7, 79, 0, 12, 214, 251, 127, 166, 7, 79, 0, 198, 207, 251, 127, 14, 4, 221, 2, 32, 82, 251, 127, 14, 4, 221, 2, 163, 41, 251, 127, 14, 4, 221, 2, 245, 45, 251, 127, 131, 1, 134, 6, 150, 177, 251, 127, 131, 1, 134, 6, 234, 224, 251, 127, 131, 1, 134, 6, 24, 229, 251, 127, 88, 248, 13, 0, 122, 162, 251, 127, 88, 248, 13, 0, 122, 160, 251, 127, 88, 248, 13, 0, 123, 158, 251, 127, 240, 251, 151, 2, 91, 191, 251, 127, 240, 251, 151, 2, 22, 130, 251, 127, 240, 251, 151, 2, 10, 251, 251, 127, 240, 251, 151, 2, 144, 224, 251, 127, 240, 251, 151, 2, 223, 169, 251, 127, 240, 251, 151, 2, 68, 202, 251, 127, 123, 254, 166, 6, 134, 178, 251, 127, 123, 254, 166, 6, 27, 189, 251, 127, 123, 254, 166, 6, 70, 174, 251, 127, 166, 7, 175, 255, 65, 182, 251, 127, 166, 7, 175, 255, 168, 226, 251, 127, 166, 7, 175, 255, 234, 166, 251, 127, 166, 7, 175, 255, 223, 179, 251, 127, 14, 4, 33, 253, 67, 211, 251, 127, 14, 4, 33, 253, 166, 233, 251, 127, 14, 4, 33, 253, 50, 177, 251, 127, 14, 4, 33, 253, 143, 139, 251, 127, 14, 4, 33, 253, 185, 157, 251, 127, 131, 1, 120, 249, 11, 46, 251, 127, 131, 1, 120, 249, 18, 113, 251, 127, 131, 1, 120, 249, 138, 122, 251, 127, 88, 248, 241, 255, 82, 219, 251, 127, 88, 248, 241, 255, 5, 230, 251, 127, 88, 248, 241, 255, 64, 213, 251, 127, 88, 248, 241, 255, 247, 222, 251, 127, 240, 251, 103, 253, 237, 84, 251, 127, 240, 251, 103, 253, 105, 27, 251, 127, 240, 251, 103, 253, 135, 96, 251, 127, 240, 251, 103, 253, 25, 44, 251, 127, 240, 251, 103, 253, 235, 117, 251, 127, 123, 254, 88, 249, 222, 76, 251, 127, 123, 254, 88, 249, 160, 76, 251, 127, 123, 254, 88, 249, 158, 45, 246, 255, 69, 255, 146, 244, 249, 191, 246, 255, 185, 0, 108, 11, 178, 192, 246, 255, 44, 2, 217, 6, 109, 194, 246, 255, 28, 248, 198, 0, 229, 201, 246, 255, 0, 0, 175, 244, 36, 191, 246, 255, 255, 255, 79, 11, 17, 199, 246, 255, 166, 7, 79, 0, 103, 218, 246, 255, 14, 4, 221, 2, 254, 211, 246, 255, 166, 7, 175, 255, 100, 166, 246, 255, 88, 248, 241, 255, 187, 170, 234, 217, 0, 0, 79, 11, 195, 200, 234, 217, 0, 0, 79, 11, 236, 213, 234, 217, 0, 0, 79, 11, 245, 217, 234, 217, 0, 0, 79, 11, 90, 195, 234, 217, 79, 11, 0, 0, 48, 239, 234, 217, 79, 11, 0, 0, 167, 217, 234, 217, 79, 11, 0, 0, 238, 235, 234, 217, 79, 11, 255, 255, 14, 121, 234, 217, 79, 11, 255, 255, 34, 117, 234, 217, 79, 11, 255, 255, 86, 127, 234, 217, 255, 255, 175, 244, 24, 69, 234, 217, 255, 255, 175, 244, 36, 86, 234, 217, 255, 255, 175, 244, 0, 88, 234, 217, 131, 1, 134, 6, 45, 216, 234, 217, 131, 1, 134, 6, 172, 215, 234, 217, 131, 1, 134, 6, 118, 226, 234, 217, 131, 1, 134, 6, 90, 218, 234, 217, 88, 248, 13, 0, 200, 240, 234, 217, 88, 248, 13, 0, 124, 217, 234, 217, 88, 248, 13, 0, 44, 227, 234, 217, 88, 248, 13, 0, 229, 212, 234, 217, 88, 248, 13, 0, 144, 232, 234, 217, 240, 251, 151, 2, 77, 223, 234, 217, 240, 251, 151, 2, 36, 241, 234, 217, 240, 251, 151, 2, 178, 235, 234, 217, 123, 254, 166, 6, 200, 201, 234, 217, 123, 254, 166, 6, 143, 207, 234, 217, 123, 254, 166, 6, 51, 230, 234, 217, 14, 4, 33, 253, 172, 93, 234, 217, 14, 4, 33, 253, 228, 119, 234, 217, 14, 4, 33, 253, 238, 114, 234, 217, 131, 1, 120, 249, 144, 79, 234, 217, 131, 1, 120, 249, 13, 120, 234, 217, 131, 1, 120, 249, 155, 102, 234, 217, 131, 1, 120, 249, 226, 70, 234, 217, 131, 1, 120, 249, 113, 71, 234, 217, 240, 251, 103, 253, 98, 98, 234, 217, 240, 251, 103, 253, 89, 107, 234, 217, 123, 254, 88, 249, 107, 78, 234, 217, 123, 254, 88, 249, 158, 92, 234, 217, 123, 254, 88, 249, 137, 120, 0, 0, 44, 2, 217, 6, 97, 189, 0, 0, 232, 7, 3, 1, 94, 186, 0, 0, 28, 248, 198, 0, 219, 177, 0, 0, 0, 0, 175, 244, 236, 216, 0, 0, 0, 0, 175, 244, 188, 198, 0, 0, 255, 255, 79, 11, 7, 189, 0, 0, 255, 255, 79, 11, 36, 199, 0, 0, 166, 7, 175, 255, 53, 224, 0, 0, 166, 7, 175, 255, 161, 236, 0, 0, 88, 248, 241, 255, 119, 219, 0, 0, 88, 248, 241, 255, 183, 234, 0, 0, 240, 251, 103, 253, 149, 232, 0, 0, 240, 251, 103, 253, 115, 208, 11, 38, 0, 0, 79, 11, 233, 69, 11, 38, 0, 0, 79, 11, 58, 73, 11, 38, 0, 0, 79, 11, 55, 43, 11, 38, 0, 0, 79, 11, 232, 57, 11, 38, 79, 11, 0, 0, 6, 115, 11, 38, 79, 11, 0, 0, 3, 95, 11, 38, 79, 11, 0, 0, 246, 84, 11, 38, 79, 11, 255, 255, 32, 255, 11, 38, 79, 11, 255, 255, 26, 243, 11, 38, 79, 11, 255, 255, 201, 223, 11, 38, 255, 255, 175, 244, 169, 201, 11, 38, 255, 255, 175, 244, 137, 195, 11, 38, 255, 255, 175, 244, 128, 192, 11, 38, 255, 255, 175, 244, 155, 193, 11, 38, 255, 255, 175, 244, 56, 192, 11, 38, 175, 244, 255, 255, 77, 207, 11, 38, 175, 244, 255, 255, 152, 255, 11, 38, 175, 244, 255, 255, 40, 238, 11, 38, 175, 244, 255, 255, 95, 225, 11, 38, 175, 244, 255, 255, 143, 246, 11, 38, 175, 244, 255, 255, 160, 234, 11, 38, 14, 4, 221, 2, 79, 101, 11, 38, 14, 4, 221, 2, 57, 84, 11, 38, 131, 1, 134, 6, 96, 91, 11, 38, 131, 1, 134, 6, 18, 88, 11, 38, 131, 1, 134, 6, 14, 70, 11, 38, 88, 248, 13, 0, 248, 110, 11, 38, 88, 248, 13, 0, 1, 94, 11, 38, 88, 248, 13, 0, 126, 114, 11, 38, 88, 248, 13, 0, 87, 98, 11, 38, 88, 248, 13, 0, 104, 115, 11, 38, 240, 251, 151, 2, 190, 97, 11, 38, 240, 251, 151, 2, 235, 84, 11, 38, 240, 251, 151, 2, 133, 112, 11, 38, 240, 251, 151, 2, 160, 81, 11, 38, 240, 251, 151, 2, 93, 86, 11, 38, 123, 254, 166, 6, 190, 83, 11, 38, 123, 254, 166, 6, 104, 93, 11, 38, 123, 254, 166, 6, 20, 74, 11, 38, 123, 254, 166, 6, 207, 72, 11, 38, 123, 254, 166, 6, 122, 69, 11, 38, 14, 4, 33, 253, 108, 220, 11, 38, 14, 4, 33, 253, 208, 215, 11, 38, 14, 4, 33, 253, 89, 215, 11, 38, 131, 1, 120, 249, 247, 206, 11, 38, 131, 1, 120, 249, 74, 181, 11, 38, 131, 1, 120, 249, 105, 194, 11, 38, 131, 1, 120, 249, 126, 208, 251, 127, 0, 0, 79, 11, 239, 195, 251, 127, 0, 0, 79, 11, 143, 254, 251, 127, 0, 0, 79, 11, 160, 191, 251, 127, 0, 0, 79, 11, 166, 171, 251, 127, 79, 11, 0, 0, 14, 178, 251, 127, 79, 11, 0, 0, 106, 206, 251, 127, 79, 11, 0, 0, 16, 200, 251, 127, 79, 11, 0, 0, 207, 225, 251, 127, 255, 255, 79, 11, 146, 167, 251, 127, 255, 255, 79, 11, 184, 162, 251, 127, 255, 255, 79, 11, 44, 202, 251, 127, 175, 244, 0, 0, 219, 175, 251, 127, 175, 244, 0, 0, 174, 162, 251, 127, 175, 244, 0, 0, 151, 181, 251, 127, 175, 244, 0, 0, 29, 204, 251, 127, 0, 0, 175, 244, 171, 189, 251, 127, 0, 0, 175, 244, 85, 138, 251, 127, 0, 0, 175, 244, 39, 172, 251, 127, 79, 11, 255, 255, 21, 85, 251, 127, 79, 11, 255, 255, 248, 1, 251, 127, 79, 11, 255, 255, 196, 86, 251, 127, 79, 11, 255, 255, 108, 122, 251, 127, 79, 11, 255, 255, 125, 104, 251, 127, 255, 255, 175, 244, 72, 179, 251, 127, 255, 255, 175, 244, 132, 189, 251, 127, 255, 255, 175, 244, 68, 191, 251, 127, 255, 255, 175, 244, 45, 172, 251, 127, 175, 244, 255, 255, 0, 198, 251, 127, 175, 244, 255, 255, 188, 217, 251, 127, 175, 244, 255, 255, 55, 147, 251, 127, 175, 244, 255, 255, 27, 148, 251, 127, 166, 7, 79, 0, 28, 172, 251, 127, 166, 7, 79, 0, 114, 234, 251, 127, 166, 7, 79, 0, 139, 214, 251, 127, 166, 7, 79, 0, 160, 205, 251, 127, 14, 4, 221, 2, 68, 99, 251, 127, 14, 4, 221, 2, 42, 93, 251, 127, 14, 4, 221, 2, 143, 76, 251, 127, 14, 4, 221, 2, 247, 112, 251, 127, 131, 1, 134, 6, 176, 187, 251, 127, 131, 1, 134, 6, 4, 225, 251, 127, 131, 1, 134, 6, 126, 211, 251, 127, 131, 1, 134, 6, 212, 249, 251, 127, 131, 1, 134, 6, 176, 250, 251, 127, 131, 1, 134, 6, 165, 229, 251, 127, 88, 248, 13, 0, 224, 174, 251, 127, 88, 248, 13, 0, 242, 185, 251, 127, 88, 248, 13, 0, 24, 211, 251, 127, 240, 251, 151, 2, 189, 192, 251, 127, 240, 251, 151, 2, 115, 185, 251, 127, 240, 251, 151, 2, 150, 200, 251, 127, 240, 251, 151, 2, 107, 242, 251, 127, 123, 254, 166, 6, 209, 171, 251, 127, 123, 254, 166, 6, 151, 186, 251, 127, 123, 254, 166, 6, 243, 175, 251, 127, 123, 254, 166, 6, 89, 207, 251, 127, 123, 254, 166, 6, 65, 247, 251, 127, 123, 254, 166, 6, 117, 253, 251, 127, 166, 7, 175, 255, 246, 202, 251, 127, 166, 7, 175, 255, 5, 226, 251, 127, 166, 7, 175, 255, 45, 144, 251, 127, 166, 7, 175, 255, 144, 158, 251, 127, 14, 4, 33, 253, 226, 233, 251, 127, 14, 4, 33, 253, 246, 218, 251, 127, 14, 4, 33, 253, 161, 147, 251, 127, 14, 4, 33, 253, 96, 167, 251, 127, 14, 4, 33, 253, 253, 138, 251, 127, 131, 1, 120, 249, 50, 93, 251, 127, 131, 1, 120, 249, 231, 59, 251, 127, 131, 1, 120, 249, 100, 70, 251, 127, 131, 1, 120, 249, 86, 123, 251, 127, 131, 1, 120, 249, 10, 126, 251, 127, 131, 1, 120, 249, 68, 112, 251, 127, 88, 248, 241, 255, 58, 228, 251, 127, 88, 248, 241, 255, 193, 222, 251, 127, 88, 248, 241, 255, 205, 212, 251, 127, 88, 248, 241, 255, 63, 171, 251, 127, 88, 248, 241, 255, 164, 155, 251, 127, 240, 251, 103, 253, 207, 90, 251, 127, 240, 251, 103, 253, 228, 35, 251, 127, 240, 251, 103, 253, 179, 97, 251, 127, 240, 251, 103, 253, 118, 123, 251, 127, 123, 254, 88, 249, 87, 76, 251, 127, 123, 254, 88, 249, 85, 70, 251, 127, 123, 254, 88, 249, 66, 117, 251, 127, 123, 254, 88, 249, 235, 127, 251, 127, 123, 254, 88, 249, 40, 104, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 254, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 254, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 221, 96, 197, 57, 17, 99, 190, 62, 31, 126, 115, 6, 43, 129, 86, 0, 215, 190, 234, 28, 191, 185, 45, 31, 123, 128, 88, 0, 17, 126, 184, 2, 151, 112, 154, 21, 165, 101, 156, 39, 147, 97, 249, 49, 200, 168, 8, 27, 251, 147, 83, 14, 35, 178, 75, 30, 207, 107, 75, 60, 174, 115, 239, 42, 0, 103, 227, 63, 214, 168, 144, 11, 78, 189, 172, 20, 114, 191, 29, 25, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 254, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 254, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 189, 190, 237, 28, 197, 185, 34, 31, 183, 130, 237, 1, 167, 127, 124, 0, 209, 96, 191, 57, 20, 99, 216, 62, 169, 127, 44, 1, 114, 134, 224, 1, 239, 170, 80, 12, 74, 188, 47, 20, 226, 191, 255, 24, 83, 107, 79, 61, 111, 116, 215, 40, 226, 102, 114, 63, 156, 167, 89, 26, 153, 149, 103, 15, 249, 177, 108, 30, 172, 113, 251, 19, 247, 100, 200, 40, 180, 97, 36, 50, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 127, 255, 255, 255, 127, 255, 255, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 127, 0, 0, 255, 127, 0, 0, 62, 195, 62, 67, 100, 191, 100, 63, 237, 141, 237, 13, 41, 132, 41, 4, 143, 55, 112, 72, 35, 57, 219, 70, 197, 129, 197, 1, 153, 116, 101, 11, 243, 198, 243, 70, 146, 190, 147, 62, 252, 141, 253, 13, 180, 129, 180, 1, 7, 57, 247, 70, 246, 60, 8, 67, 135, 119, 120, 8, 184, 103, 71, 24, 26, 177, 26, 49, 120, 170, 121, 42, 7, 187, 8, 59, 57, 196, 57, 68, 17, 204, 18, 76, 122, 184, 122, 56, 161, 108, 93, 19, 215, 97, 40, 30, 29, 63, 225, 64, 205, 78, 49, 49, 127, 69, 127, 58, 42, 49, 212, 78, 48, 164, 49, 36, 173, 168, 173, 40, 231, 178, 232, 50, 83, 192, 83, 64, 109, 205, 109, 77, 255, 188, 255, 60, 77, 80, 178, 47, 47, 81, 207, 46, 138, 61, 117, 66, 147, 73, 108, 54, 73, 70, 181, 57, 3, 50, 252, 77, 114, 93, 23, 75, 88, 92, 191, 80, 92, 92, 197, 71, 35, 142, 10, 7, 54, 92, 50, 19, 35, 144, 129, 28, 129, 171, 50, 63, 175, 131, 36, 52, 234, 135, 120, 48, 87, 89, 211, 81, 11, 113, 244, 14, 255, 255, 255, 127, 255, 255, 255, 127, 250, 80, 5, 47, 0, 0, 255, 127, 158, 1, 96, 126, 0, 0, 255, 127, 233, 45, 21, 82, 237, 199, 212, 19, 26, 192, 163, 13, 128, 197, 107, 15, 156, 125, 176, 9, 113, 108, 112, 68, 206, 91, 82, 19, 255, 255, 255, 127, 60, 253, 61, 125, 255, 255, 255, 127, 74, 136, 74, 8, 0, 0, 255, 127, 199, 73, 55, 54, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 161, 113, 93, 14, 22, 133, 22, 5, 255, 255, 255, 127, 154, 65, 100, 62, 0, 0, 255, 127, 0, 0, 255, 127, 230, 10, 24, 117, 0, 0, 255, 127, 165, 195, 165, 67, 44, 1, 211, 126, 197, 13, 57, 114, 79, 191, 80, 63, 151, 161, 151, 33, 154, 254, 155, 126, 255, 255, 255, 127, 116, 132, 117, 4, 255, 255, 255, 127, 0, 0, 255, 127, 247, 52, 7, 75, 16, 57, 239, 70, 89, 255, 90, 127, 42, 253, 43, 125, 140, 116, 114, 11, 226, 198, 226, 70, 0, 0, 255, 127, 80, 191, 80, 63, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 4, 255, 5, 127, 181, 128, 182, 0, 171, 50, 84, 77, 0, 0, 255, 127, 0, 0, 255, 127, 189, 216, 189, 88, 160, 61, 95, 66, 255, 255, 255, 127, 255, 255, 255, 127, 255, 96, 0, 31, 250, 171, 250, 43, 145, 88, 110, 39, 255, 255, 255, 127, 122, 189, 123, 61, 155, 8, 99, 119, 162, 72, 92, 55, 22, 205, 22, 77, 251, 2, 3, 125, 192, 1, 63, 126, 197, 253, 198, 125, 152, 246, 152, 118, 25, 90, 229, 37, 124, 251, 125, 123, 98, 108, 157, 19, 255, 255, 255, 127, 55, 214, 56, 86, 253, 74, 2, 53, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 38, 48, 216, 79, 255, 255, 255, 127, 216, 255, 217, 127, 177, 255, 177, 127, 149, 255, 150, 127, 184, 190, 185, 62, 46, 255, 47, 127, 152, 255, 152, 127, 194, 3, 60, 124, 255, 255, 255, 127, 85, 203, 85, 75, 0, 0, 255, 127, 255, 255, 255, 127, 202, 89, 53, 38, 157, 73, 97, 54, 255, 255, 255, 127, 176, 79, 78, 48, 0, 0, 255, 127, 149, 48, 105, 79, 18, 230, 19, 102, 187, 65, 68, 62, 249, 254, 250, 126, 0, 0, 255, 127, 0, 0, 255, 127, 151, 48, 103, 79, 151, 153, 203, 12, 66, 116, 122, 23, 164, 117, 194, 20, 205, 136, 29, 4, 121, 106, 229, 49, 149, 164, 96, 22, 184, 110, 237, 14, 246, 101, 118, 28, 77, 133, 10, 28, 63, 103, 23, 5, 185, 34, 69, 93, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 135, 96, 119, 31, 255, 255, 255, 127, 255, 255, 255, 127, 88, 178, 89, 50, 255, 255, 255, 127, 0, 0, 255, 127, 32, 235, 32, 107, 255, 255, 255, 127, 255, 255, 255, 127, 206, 41, 48, 86, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 20, 213, 21, 85, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 138, 205, 138, 77, 255, 255, 255, 127, 255, 255, 255, 127, 226, 239, 226, 111, 255, 255, 255, 127, 255, 255, 255, 127, 242, 72, 13, 55, 0, 0, 255, 127, 0, 0, 255, 127, 79, 58, 176, 69, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 154, 14, 101, 113, 167, 172, 167, 44, 222, 249, 222, 121, 190, 203, 190, 75, 255, 255, 255, 127, 255, 255, 255, 127, 146, 186, 191, 27, 184, 195, 185, 15, 201, 112, 82, 39, 45, 210, 111, 41, 42, 171, 44, 25, 212, 115, 34, 34, 251, 87, 237, 83, 255, 140, 224, 14, 234, 167, 199, 10, 112, 111, 87, 13, 177, 122, 239, 16, 22, 127, 142, 31, 38, 99, 72, 24, 90, 239, 90, 111, 139, 218, 139, 90, 255, 255, 255, 127, 255, 255, 255, 127, 233, 227, 233, 99, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 125, 240, 125, 112, 255, 255, 255, 127, 223, 53, 31, 74, 0, 0, 255, 127, 225, 2, 30, 125, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 227, 250, 228, 122, 25, 108, 230, 19, 12, 134, 12, 6, 255, 255, 255, 127, 58, 246, 59, 118, 18, 184, 19, 56, 140, 235, 140, 107, 255, 255, 255, 127, 255, 255, 255, 127, 63, 91, 192, 36, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 106, 47, 148, 80, 141, 76, 113, 51, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 91, 42, 164, 85, 0, 0, 255, 127, 5, 255, 6, 127, 111, 26, 144, 101, 0, 0, 255, 127, 69, 181, 70, 53, 255, 255, 255, 127, 255, 255, 255, 127, 248, 194, 248, 66, 255, 255, 255, 127, 44, 244, 45, 116, 255, 255, 255, 127, 27, 253, 28, 125, 0, 0, 255, 127, 255, 255, 255, 127, 219, 233, 219, 105, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 128, 18, 126, 109, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 179, 253, 179, 125, 255, 255, 255, 127, 109, 244, 109, 116, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 195, 133, 195, 5, 211, 11, 44, 116, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 50, 165, 51, 37, 145, 226, 145, 98, 158, 255, 159, 127, 82, 46, 172, 81, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 105, 255, 105, 127, 255, 255, 255, 127, 255, 255, 255, 127, 236, 252, 237, 124, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 55, 80, 200, 47, 83, 78, 172, 49, 255, 255, 255, 127, 255, 255, 255, 127, 180, 215, 180, 87, 0, 0, 255, 127, 21, 58, 233, 69, 255, 255, 255, 127, 255, 255, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 255, 255, 255, 127, 255, 255, 255, 127)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_4aff2")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1v286"]
resource_local_to_scene = true
render_priority = 2
transparency = 1
cull_mode = 2
depth_draw_mode = 1
no_depth_test = true
shading_mode = 0
albedo_color = Color(1, 1, 1, 0)
texture_filter = 1

[sub_resource type="ViewportTexture" id="ViewportTexture_oshrb"]
resource_name = "_albedo"
viewport_path = NodePath("CardPicGradient/SubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_d4a2i"]
resource_local_to_scene = true
transparency = 1
cull_mode = 2
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_oshrb")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_opdvl"]
transparency = 1
depth_draw_mode = 1
shading_mode = 0
albedo_texture = ExtResource("25_ttghd")
texture_filter = 1
texture_repeat = false

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_cglip"]
resource_name = "CardCompanion"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("25_ttghd")

[sub_resource type="ArrayMesh" id="ArrayMesh_8kah1"]
_surfaces = [{
"aabb": AABB(0.0393262, -2.5, -1.3296, 1.00136e-05, 0.39001, 0.36801),
"format": 34896613377,
"index_count": 24,
"index_data": PackedByteArray(1, 0, 8, 0, 6, 0, 1, 0, 5, 0, 8, 0, 5, 0, 4, 0, 8, 0, 5, 0, 0, 0, 4, 0, 4, 0, 7, 0, 8, 0, 4, 0, 2, 0, 7, 0, 8, 0, 3, 0, 6, 0, 8, 0, 7, 0, 3, 0),
"name": "CardCompanion",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 9,
"vertex_data": PackedByteArray(97, 0, 253, 255, 253, 255, 0, 0, 0, 0, 0, 0, 253, 255, 0, 0, 97, 0, 253, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 0, 253, 255, 254, 127, 0, 0, 48, 0, 254, 127, 253, 255, 0, 0, 0, 0, 0, 0, 254, 127, 0, 0, 48, 0, 254, 127, 0, 0, 0, 0, 48, 0, 254, 127, 254, 127, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_s5rux"]
resource_name = "DRC_Card_Modell_Test01_Plane_006"
_surfaces = [{
"aabb": AABB(0.0393262, -2.5, -1.3296, 1.00136e-05, 0.39001, 0.36801),
"attribute_data": PackedByteArray(43, 105, 118, 103, 43, 105, 255, 255, 255, 255, 118, 103, 255, 255, 255, 255, 149, 180, 118, 103, 43, 105, 186, 179, 149, 180, 255, 255, 255, 255, 186, 179, 149, 180, 186, 179),
"format": 34896613399,
"index_count": 24,
"index_data": PackedByteArray(1, 0, 8, 0, 6, 0, 1, 0, 5, 0, 8, 0, 5, 0, 4, 0, 8, 0, 5, 0, 0, 0, 4, 0, 4, 0, 7, 0, 8, 0, 4, 0, 2, 0, 7, 0, 8, 0, 3, 0, 6, 0, 8, 0, 7, 0, 3, 0),
"material": SubResource("StandardMaterial3D_cglip"),
"name": "CardCompanion",
"primitive": 3,
"uv_scale": Vector4(2.39877, 2.51373, 0, 0),
"vertex_count": 9,
"vertex_data": PackedByteArray(97, 0, 253, 255, 253, 255, 255, 191, 0, 0, 0, 0, 253, 255, 255, 191, 97, 0, 253, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 97, 0, 253, 255, 254, 127, 255, 191, 48, 0, 254, 127, 253, 255, 255, 191, 0, 0, 0, 0, 254, 127, 255, 191, 48, 0, 254, 127, 0, 0, 255, 191, 48, 0, 254, 127, 254, 127, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_8kah1")

[sub_resource type="ViewportTexture" id="ViewportTexture_4hi5t"]
resource_name = "_albedo"
viewport_path = NodePath("CardBg/SubViewport")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_8as5m"]
resource_local_to_scene = true
cull_mode = 2
depth_draw_mode = 1
shading_mode = 0
albedo_texture = SubResource("ViewportTexture_4hi5t")
texture_filter = 1

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_6dglo"]
resource_name = "CardPicFG.001"
cull_mode = 2
albedo_color = Color(0, 0, 0, 1)
metallic = 1.0
emission_enabled = true
emission_texture = ExtResource("32_jwawa")

[sub_resource type="ArrayMesh" id="ArrayMesh_w1jay"]
_surfaces = [{
"aabb": AABB(0.0495976, -1.33334, -1.66119, 9.99868e-06, 3.04757, 3.34225),
"format": 34896613377,
"index_count": 6,
"index_data": PackedByteArray(1, 0, 3, 0, 0, 0, 1, 0, 2, 0, 3, 0),
"name": "CardPicFG.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 4,
"vertex_data": PackedByteArray(0, 0, 254, 255, 254, 255, 0, 0, 0, 0, 254, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 255, 0, 0)
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_wyg3f"]
resource_name = "DRC_Card_Model_Test03_CardPicFgPLayed_Cube_003"
_surfaces = [{
"aabb": AABB(0.0495976, -1.33334, -1.66119, 9.99868e-06, 3.04757, 3.34225),
"attribute_data": PackedByteArray(192, 42, 82, 34, 48, 213, 82, 34, 48, 213, 187, 206, 192, 42, 187, 206),
"format": 34896613399,
"index_count": 6,
"index_data": PackedByteArray(1, 0, 3, 0, 0, 0, 1, 0, 2, 0, 3, 0),
"material": SubResource("StandardMaterial3D_6dglo"),
"name": "CardPicFG.001",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 4,
"vertex_data": PackedByteArray(0, 0, 254, 255, 254, 255, 255, 191, 0, 0, 254, 255, 0, 0, 255, 191, 0, 0, 0, 0, 0, 0, 255, 191, 0, 0, 0, 0, 254, 255, 255, 191, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0, 255, 127, 0, 0)
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_w1jay")

[node name="Card" type="CharacterBody3D"]
top_level = true
collision_mask = 2

[node name="CardText" type="MeshInstance3D" parent="."]
transform = Transform3D(0.940427, 0, 0, 0, -4.02088e-08, -0.62237, 0, 0.91987, -2.72046e-08, 0.0632194, -0.634444, 0.0272855)
layers = 524289
material_override = SubResource("StandardMaterial3D_titv1")
mesh = SubResource("PlaneMesh_8wjqt")
skeleton = NodePath("")

[node name="CostBg" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.235642, 0.00551444)
layers = 524289
material_override = SubResource("StandardMaterial3D_2mthu")
mesh = SubResource("ArrayMesh_7urhl")
skeleton = NodePath("")

[node name="CostText" type="MeshInstance3D" parent="."]
transform = Transform3D(0.478, 0, 0, 0, -1.37691e-08, -0.272, 0, 0.315, -1.18895e-08, -0.658149, 0.700551, 0.0404162)
layers = 524289
material_override = SubResource("StandardMaterial3D_bqove")
mesh = SubResource("PlaneMesh_8wjqt")
skeleton = NodePath("")

[node name="PowerText" type="MeshInstance3D" parent="."]
transform = Transform3D(0.541726, 0, 0, 0, -1.37691e-08, -0.302863, 0, 0.315, -1.32386e-08, -0.666011, -1.21867, 0.0404162)
layers = 524289
material_override = SubResource("StandardMaterial3D_7u0ls")
mesh = SubResource("PlaneMesh_8wjqt")
skeleton = NodePath("")

[node name="PowerBg" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, 0.004)
layers = 524289
material_override = SubResource("StandardMaterial3D_3tcp4")
mesh = SubResource("ArrayMesh_atnjb")
skeleton = NodePath("")

[node name="NameText" type="MeshInstance3D" parent="."]
transform = Transform3D(0.656323, 0, 0, 0, -1.81402e-08, -0.161951, 0, 0.415, -7.07912e-09, 0.144811, 0.753399, 0.0404162)
layers = 524289
material_override = SubResource("StandardMaterial3D_ujq4y")
mesh = SubResource("PlaneMesh_8wjqt")
skeleton = NodePath("")

[node name="NameBg" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, -0.0152043, -0.235642, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_u0viu")
mesh = SubResource("ArrayMesh_jj8nb")
skeleton = NodePath("")

[node name="CardFront" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, -0.003)
layers = 524289
material_override = SubResource("StandardMaterial3D_4q0or")
mesh = SubResource("ArrayMesh_imadi")
skeleton = NodePath("")

[node name="CardBack" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, 0)
material_override = SubResource("StandardMaterial3D_47vpe")
mesh = SubResource("ArrayMesh_5cvg6")
skeleton = NodePath("")

[node name="ColorBar" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.235642, 0.00666136)
layers = 524289
material_override = SubResource("StandardMaterial3D_bwgkt")
mesh = SubResource("ArrayMesh_k0ke4")
skeleton = NodePath("")

[node name="RarityBar" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.235642, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_610e1")
mesh = SubResource("ArrayMesh_cpfdt")
skeleton = NodePath("")

[node name="CardBg" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_uadp2")
mesh = SubResource("ArrayMesh_hsh2j")
skeleton = NodePath("")

[node name="CardBg2" type="MeshInstance3D" parent="."]
transform = Transform3D(-6.37749e-08, 0, -0.5, 0, 0.491, 0, 1.459, 0, -2.18557e-08, 0, -0.236273, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_1v286")
mesh = SubResource("ArrayMesh_hsh2j")
skeleton = NodePath("")

[node name="CardPicGradient" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, -6.29812e-05)
layers = 524289
material_override = SubResource("StandardMaterial3D_d4a2i")
mesh = SubResource("ArrayMesh_imadi")
skeleton = NodePath("")

[node name="CardCompanion" type="MeshInstance3D" parent="."]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.235642, 0)
material_override = SubResource("StandardMaterial3D_opdvl")
mesh = SubResource("ArrayMesh_s5rux")
skeleton = NodePath("")

[node name="OnArena" type="Node3D" parent="."]

[node name="PowerText2" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(0.541726, 0, 0, 0, -1.37691e-08, -0.302863, 0, 0.315, -1.32386e-08, -0.666011, -0.819845, 0.0404162)
layers = 524289
material_override = SubResource("StandardMaterial3D_7u0ls")
mesh = SubResource("PlaneMesh_8wjqt")
skeleton = NodePath("")

[node name="PowerBg2" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, 0.146876, 0.004)
layers = 524289
material_override = SubResource("StandardMaterial3D_3tcp4")
mesh = SubResource("ArrayMesh_atnjb")
skeleton = NodePath("")

[node name="ColorBar2" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.309, 0.007)
layers = 524289
material_override = SubResource("StandardMaterial3D_bwgkt")
mesh = SubResource("ArrayMesh_k0ke4")
skeleton = NodePath("")

[node name="RarityBar2" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, 0.184947, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_610e1")
mesh = SubResource("ArrayMesh_cpfdt")
skeleton = NodePath("")

[node name="CardCompanion2" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, 0.184947, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_opdvl")
mesh = SubResource("ArrayMesh_s5rux")
skeleton = NodePath("")

[node name="CardBack4" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.33, 0, 0.49, 0, -2.14186e-08, 0, -0.18, 0)
material_override = SubResource("StandardMaterial3D_47vpe")
mesh = SubResource("ArrayMesh_5cvg6")
skeleton = NodePath("")

[node name="CardBg2" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.339102, 0, 0.49, 0, -2.14186e-08, 0, -0.18454, 0)
layers = 524289
material_override = SubResource("StandardMaterial3D_8as5m")
mesh = SubResource("ArrayMesh_hsh2j")
skeleton = NodePath("")

[node name="CardPicFGPlayed" type="MeshInstance3D" parent="OnArena"]
transform = Transform3D(-2.14186e-08, 0, -0.49, 0, 0.49, 0, 0.49, 0, -2.14186e-08, 0, -0.236, -0.003)
layers = 524289
material_override = SubResource("StandardMaterial3D_4q0or")
mesh = SubResource("ArrayMesh_wyg3f")
skeleton = NodePath("")
