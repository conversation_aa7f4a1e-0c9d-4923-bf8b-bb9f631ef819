extends Object
class_name DeckResource
enum {
	GREEN_STARTER,
	GRE<PERSON>,
	RED_STARTER,
	RED,
	BLUE,
	GR_DRAGON,
	FIXED_FOR_TEST,
	FIXED_FOR_TEST2,
	FIXED_FOR_TEST_NO_ABLT
}

static var MainDeck = DeckResource.BLUE
static var OppoDeck = DeckResource.GREEN

static func getDeck(deckID:int):
	var fullDeck = DeckResource.DeckList[deckID]["cards"]
	for card in fullDeck:
		card = CardData.getCardByName(card)
	return fullDeck

static var DeckList = {
	DeckResource.GREEN: {
		"cards": [
			CardData.Card.Fairy,
			CardData.Card.Fairy,
			CardData.Card.Sunshroom,
			CardData.Card.Sunshroom,
			CardData.Card.Daddylion,
			CardData.Card.Daddylion,
			CardData.Card.Entling,
			CardData.Card.Entling,
			CardData.Card.MudGolem,
			CardData.Card.MudGolem,
			CardData.Card.GreenMage,
			CardData.Card.Healshroom,
			CardData.Card.Healshroom,
			CardData.Card.YoungDryad,
			CardData.Card.StoneGolem,
			CardData.Card.StoneGolem,
			CardData.Card.Elderbloom,
			CardData.Card.ElderEnt,
			CardData.Card.ElderEnt,
			CardData.Card.GeneralPrixa
		]
	},
	DeckResource.RED: {
		"cards": [
			CardData.Card.Butterfire,
			CardData.Card.Butterfire,
			CardData.Card.Bomberkid,
			CardData.Card.Bomberkid,
			CardData.Card.Imp,
			CardData.Card.Imp,
			CardData.Card.RedMage,
			CardData.Card.RedMage,
			CardData.Card.Larva,
			CardData.Card.Larva,
			CardData.Card.LadyLamia,
			CardData.Card.Flamecoat,
			CardData.Card.Flamecoat,
			CardData.Card.Explodus,
			CardData.Card.BloodLord,
			CardData.Card.BloodLord,
			CardData.Card.Nymora,
			CardData.Card.Pyros,
			CardData.Card.Fireshadow,
			CardData.Card.KingKrotos
		]
	},
	DeckResource.BLUE: {
		"cards": [
			CardData.Card.Siren,
			CardData.Card.Siren,
			CardData.Card.MagicShell,
			CardData.Card.MagicShell,
			CardData.Card.SnowFairy,
			CardData.Card.SnowFairy,
			CardData.Card.Metamore,
			CardData.Card.Metamore,
			CardData.Card.Mermage,
			CardData.Card.Mermage,
			CardData.Card.BlueMage,
			CardData.Card.PearlAngler,
			CardData.Card.PearlAngler,
			CardData.Card.Levisia,
			CardData.Card.IceGolem,
			CardData.Card.IceGolem,
			CardData.Card.MasterTrine,
			CardData.Card.WaterElemental,
			CardData.Card.WaterElemental,
			CardData.Card.Phantarion
		]
	}
	# DeckResource.GREEN_STARTER: {
	# 	"cards": [
	# 		{
	# 			"name": "Fairy",
	# 			"color": 1,
	# 			"cost": 1,
	# 			"power": 1,
	# 			"rarity": 0,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Gain 1 Permanent mana.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 2,
	# 						"tier": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Fairy",
	# 			"color": 1,
	# 			"cost": 1,
	# 			"power": 1,
	# 			"rarity": 0,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Gain 1 Permanent mana.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 2,
	# 						"tier": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Green Slime",
	# 			"color": 1,
	# 			"cost": 1,
	# 			"power": 2,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Green Slime",
	# 			"color": 1,
	# 			"cost": 1,
	# 			"power": 2,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Leaf Slime",
	# 			"color": 1,
	# 			"cost": 2,
	# 			"power": 3,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Leaf Slime",
	# 			"color": 1,
	# 			"cost": 2,
	# 			"power": 3,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Entling",
	# 			"color": 1,
	# 			"cost": 2,
	# 			"power": 2,
	# 			"rarity": 1,
	# 			"abilities": [
	# 				{
	# 					"text": "Passive: +1 Power for each ally here.",
	# 					"trigger": {
	# 						"name": 2
	# 					},
	# 					"keyword": {
	# 						"name": 3,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 9
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Entling",
	# 			"color": 1,
	# 			"cost": 2,
	# 			"power": 2,
	# 			"rarity": 1,
	# 			"abilities": [
	# 				{
	# 					"text": "Passive: +1 Power for each ally here.",
	# 					"trigger": {
	# 						"name": 2
	# 					},
	# 					"keyword": {
	# 						"name": 3,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 9
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Cave Slime",
	# 			"color": 1,
	# 			"cost": 3,
	# 			"power": 4,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Cave Slime",
	# 			"color": 1,
	# 			"cost": 3,
	# 			"power": 4,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Green Mage",
	# 			"color": 1,
	# 			"cost": 3,
	# 			"power": 4,
	# 			"rarity": 2,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Give +2 Power to allies played last turn.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 4,
	# 						"tier": 2
	# 					},
	# 					"target": {
	# 						"name": 15,
	# 						"condition": 17,
	# 						"conditionTier": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Forest Slime",
	# 			"color": 1,
	# 			"cost": 4,
	# 			"power": 5,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Forest Slime",
	# 			"color": 1,
	# 			"cost": 4,
	# 			"power": 5,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Young Dryad",
	# 			"color": 1,
	# 			"cost": 4,
	# 			"power": 3,
	# 			"rarity": 2,
	# 			"abilities": [
	# 				{
	# 					"text": "Passive: +2 Power for each ally here.",
	# 					"trigger": {
	# 						"name": 2
	# 					},
	# 					"keyword": {
	# 						"name": 3,
	# 						"tier": 2
	# 					},
	# 					"target": {
	# 						"name": 9
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Great Slime",
	# 			"color": 1,
	# 			"cost": 5,
	# 			"power": 6,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Great Slime",
	# 			"color": 1,
	# 			"cost": 5,
	# 			"power": 6,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Stone Golem",
	# 			"color": 1,
	# 			"cost": 5,
	# 			"power": 4,
	# 			"rarity": 1,
	# 			"abilities": [
	# 				{
	# 					"text": "Passive: If you have a 6-cost ally here, +5 Power.",
	# 					"trigger": {
	# 						"name": 2,
	# 						"condition": 16,
	# 						"conditionTier": 6
	# 					},
	# 					"keyword": {
	# 						"name": 5,
	# 						"tier": 5
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Elder Ent",
	# 			"color": 1,
	# 			"cost": 6,
	# 			"power": 3,
	# 			"rarity": 1,
	# 			"abilities": [
	# 				{
	# 					"text": "Passive: +1 Power for each ally in the arena.",
	# 					"trigger": {
	# 						"name": 2
	# 					},
	# 					"keyword": {
	# 						"name": 3,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 15
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Elder Ent",
	# 			"color": 1,
	# 			"cost": 6,
	# 			"power": 3,
	# 			"rarity": 1,
	# 			"abilities": [
	# 				{
	# 					"text": "Passive: +1 Power for each ally in the arena.",
	# 					"trigger": {
	# 						"name": 2
	# 					},
	# 					"keyword": {
	# 						"name": 3,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 15
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Mutant Slime",
	# 			"color": 1,
	# 			"cost": 6,
	# 			"power": 7,
	# 			"rarity": 0
	# 		}
	# 	]
	# },
	# DeckResource.RED_STARTER: {
	# 	"cards": [
	# 		{
	# 			"name": "Butterfire",
	# 			"color": 2,
	# 			"cost": 1,
	# 			"power": 1,
	# 			"rarity": 0,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Gain 1 Temp mana.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 9,
	# 						"tier": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Butterfire",
	# 			"color": 2,
	# 			"cost": 1,
	# 			"power": 1,
	# 			"rarity": 0,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Gain 1 Temp mana.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 9,
	# 						"tier": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Red Slime",
	# 			"color": 2,
	# 			"cost": 1,
	# 			"power": 2,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Red Slime",
	# 			"color": 2,
	# 			"cost": 1,
	# 			"power": 2,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Imp",
	# 			"color": 2,
	# 			"cost": 2,
	# 			"power": 1,
	# 			"rarity": 1,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Afflict -3 Power to a random enemy.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 10,
	# 						"tier": 3
	# 					},
	# 					"target": {
	# 						"name": 16,
	# 						"condition": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Imp",
	# 			"color": 2,
	# 			"cost": 2,
	# 			"power": 1,
	# 			"rarity": 1,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Afflict -3 Power to a random enemy.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 10,
	# 						"tier": 3
	# 					},
	# 					"target": {
	# 						"name": 16,
	# 						"condition": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Crimslime",
	# 			"color": 2,
	# 			"cost": 2,
	# 			"power": 3,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Crimslime",
	# 			"color": 2,
	# 			"cost": 2,
	# 			"power": 3,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Larva",
	# 			"color": 2,
	# 			"cost": 3,
	# 			"power": 4,
	# 			"rarity": 0,
	# 			"castCondition": 1
	# 		},
	# 		{
	# 			"name": "Larva",
	# 			"color": 2,
	# 			"cost": 3,
	# 			"power": 4,
	# 			"rarity": 0,
	# 			"castCondition": 1
	# 		},
	# 		{
	# 			"name": "Fire Slime",
	# 			"color": 2,
	# 			"cost": 3,
	# 			"power": 4,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Flamecoat",
	# 			"color": 2,
	# 			"cost": 4,
	# 			"power": 5,
	# 			"rarity": 0,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Ignite 3.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 12,
	# 						"tier": 3
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Flamecoat",
	# 			"color": 2,
	# 			"cost": 4,
	# 			"power": 5,
	# 			"rarity": 0,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Ignite 3.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 12,
	# 						"tier": 3
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Magma Slime",
	# 			"color": 2,
	# 			"cost": 4,
	# 			"power": 6,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Blood Lord",
	# 			"color": 2,
	# 			"cost": 5,
	# 			"power": 3,
	# 			"rarity": 1,
	# 			"castCondition": 1,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Afflict -1 Power to all enemies here.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 10,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 10
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Blood Lord",
	# 			"color": 2,
	# 			"cost": 5,
	# 			"power": 3,
	# 			"rarity": 1,
	# 			"castCondition": 1,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Afflict -1 Power to all enemies here.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 10,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 10
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Nymora",
	# 			"color": 2,
	# 			"cost": 5,
	# 			"power": 4,
	# 			"rarity": 2,
	# 			"abilities": [
	# 				{
	# 					"text": "Passive: Allies abilities that reduce enemy power, reduce 1 more.",
	# 					"trigger": {
	# 						"name": 2
	# 					},
	# 					"keyword": {
	# 						"name": 22,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 15
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Hell Slime",
	# 			"color": 2,
	# 			"cost": 6,
	# 			"power": 6,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Hell Slime",
	# 			"color": 2,
	# 			"cost": 6,
	# 			"power": 6,
	# 			"rarity": 0
	# 		},
	# 		{
	# 			"name": "Pyros",
	# 			"color": 2,
	# 			"cost": 6,
	# 			"power": 6,
	# 			"rarity": 0,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Gain 1 Temp mana.",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 9,
	# 						"tier": 1
	# 					}
	# 				}
	# 			]
	# 		}
	# 	]
	# },
	# DeckResource.GR_DRAGON: {
	# 	"cards": [
	# 		{
	# 			"name": "Pit Imp",
	# 			"color": 2,
	# 			"cost": 1,
	# 			"power": 1,
	# 			"rarity": 2,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Afflict -3 Power to a random enemy",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 10,
	# 						"tier": 3
	# 					},
	# 					"target": {
	# 						"name": 16,
	# 						"condition": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Pit Imp",
	# 			"color": 2,
	# 			"cost": 1,
	# 			"power": 1,
	# 			"rarity": 2,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Afflict -3 Power to a random enemy",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 10,
	# 						"tier": 3
	# 					},
	# 					"target": {
	# 						"name": 16,
	# 						"condition": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Hell Butterfire",
	# 			"color": 2,
	# 			"cost": 1,
	# 			"power": 2,
	# 			"rarity": 2,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Gain 2 Temp mana",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 9,
	# 						"tier": 2
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Hell Butterfire",
	# 			"color": 2,
	# 			"cost": 1,
	# 			"power": 2,
	# 			"rarity": 2,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Gain 2 Temp mana",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 9,
	# 						"tier": 2
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Leaf Dragonling",
	# 			"color": 1,
	# 			"cost": 2,
	# 			"power": 3,
	# 			"rarity": 3,
	# 			"abilities": [
	# 				{
	# 					"text": "Give +1 Power to random ally at the end of each turn",
	# 					"trigger": {
	# 						"name": 6
	# 					},
	# 					"keyword": {
	# 						"name": 4,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 15,
	# 						"condition": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Leaf Dragonling",
	# 			"color": 1,
	# 			"cost": 2,
	# 			"power": 3,
	# 			"rarity": 3,
	# 			"abilities": [
	# 				{
	# 					"text": "Give +1 Power to random ally at the end of each turn",
	# 					"trigger": {
	# 						"name": 6
	# 					},
	# 					"keyword": {
	# 						"name": 4,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 15,
	# 						"condition": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Fire Dragonling",
	# 			"color": 2,
	# 			"cost": 2,
	# 			"power": 2,
	# 			"rarity": 3,
	# 			"abilities": [
	# 				{
	# 					"text": "Afflict -1 Power to random enemy at the end of each turn",
	# 					"trigger": {
	# 						"name": 6
	# 					},
	# 					"keyword": {
	# 						"name": 10,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 16,
	# 						"condition": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Fire Dragonling",
	# 			"color": 2,
	# 			"cost": 2,
	# 			"power": 2,
	# 			"rarity": 3,
	# 			"abilities": [
	# 				{
	# 					"text": "Afflict -1 Power to random enemy at the end of each turn",
	# 					"trigger": {
	# 						"name": 6
	# 					},
	# 					"keyword": {
	# 						"name": 10,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 16,
	# 						"condition": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Blesshroom",
	# 			"color": 1,
	# 			"cost": 3,
	# 			"power": 1,
	# 			"rarity": 2,
	# 			"castCondition": 3,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Gain 1 Permanent mana,",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 2,
	# 						"tier": 1
	# 					}
	# 				},
	# 				{
	# 					"text": "then give +3 Power to a host",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 4,
	# 						"tier": 3
	# 					},
	# 					"target": {
	# 						"name": 2
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Blesshroom",
	# 			"color": 1,
	# 			"cost": 3,
	# 			"power": 1,
	# 			"rarity": 2,
	# 			"castCondition": 3,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Gain 1 Permanent mana,",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 2,
	# 						"tier": 1
	# 					}
	# 				},
	# 				{
	# 					"text": "then give +3 Power to a host",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 4,
	# 						"tier": 3
	# 					},
	# 					"target": {
	# 						"name": 2
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Dragon Knight Titus",
	# 			"color": 2,
	# 			"cost": 3,
	# 			"power": 6,
	# 			"rarity": 3,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Destroy the weakest enemy here",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 11
	# 					},
	# 					"target": {
	# 						"name": 10,
	# 						"condition": 4
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Young Dryad",
	# 			"color": 1,
	# 			"cost": 4,
	# 			"power": 3,
	# 			"rarity": 2,
	# 			"abilities": [
	# 				{
	# 					"text": "Passive: +2 Power for each ally here.",
	# 					"trigger": {
	# 						"name": 2
	# 					},
	# 					"keyword": {
	# 						"name": 3,
	# 						"tier": 2
	# 					},
	# 					"target": {
	# 						"name": 9
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Young Dryad",
	# 			"color": 1,
	# 			"cost": 4,
	# 			"power": 3,
	# 			"rarity": 2,
	# 			"abilities": [
	# 				{
	# 					"text": "Passive: +2 Power for each ally here.",
	# 					"trigger": {
	# 						"name": 2
	# 					},
	# 					"keyword": {
	# 						"name": 3,
	# 						"tier": 2
	# 					},
	# 					"target": {
	# 						"name": 9
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Lord Darkflame",
	# 			"color": 2,
	# 			"cost": 4,
	# 			"power": 4,
	# 			"rarity": 3,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Ignite 5,",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 12,
	# 						"tier": 5
	# 					}
	# 				},
	# 				{
	# 					"text": "then gain 3 Temp mana",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 9,
	# 						"tier": 3
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Rot Wyrm",
	# 			"color": 5,
	# 			"cost": 5,
	# 			"power": 5,
	# 			"rarity": 3,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Afflict -3 Power to a random enemy",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 10,
	# 						"tier": 3
	# 					},
	# 					"target": {
	# 						"name": 16,
	# 						"condition": 1
	# 					}
	# 				},
	# 				{
	# 					"text": "then give +3 Power to random ally",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 4,
	# 						"tier": 3
	# 					},
	# 					"target": {
	# 						"name": 15,
	# 						"condition": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Rot Wyrm",
	# 			"color": 5,
	# 			"cost": 5,
	# 			"power": 5,
	# 			"rarity": 3,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: Afflict -3 Power to a random enemy",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 10,
	# 						"tier": 3
	# 					},
	# 					"target": {
	# 						"name": 16,
	# 						"condition": 1
	# 					}
	# 				},
	# 				{
	# 					"text": "then give +3 Power to random ally",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 4,
	# 						"tier": 3
	# 					},
	# 					"target": {
	# 						"name": 15,
	# 						"condition": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Elderbloom",
	# 			"color": 1,
	# 			"cost": 5,
	# 			"power": 1,
	# 			"rarity": 3,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: This card gains the power of the strongest ally",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 4,
	# 						"dynamicTier": 3
	# 					},
	# 					"target": {
	# 						"name": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Terragon",
	# 			"color": 1,
	# 			"cost": 6,
	# 			"power": 6,
	# 			"rarity": 4,
	# 			"abilities": [
	# 				{
	# 					"text": "Gets +2 Power at the end of each turn if you have unspent mana",
	# 					"trigger": {
	# 						"name": 6,
	# 						"condition": 11
	# 					},
	# 					"keyword": {
	# 						"name": 4,
	# 						"tier": 2
	# 					},
	# 					"target": {
	# 						"name": 1
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Pyragon",
	# 			"color": 2,
	# 			"cost": 6,
	# 			"power": 8,
	# 			"rarity": 4,
	# 			"castCondition": 1,
	# 			"abilities": [
	# 				{
	# 					"text": "Afflict -3 Power to the strongest enemy at the end of each turn",
	# 					"trigger": {
	# 						"name": 6
	# 					},
	# 					"keyword": {
	# 						"name": 10,
	# 						"tier": 3
	# 					},
	# 					"target": {
	# 						"name": 16,
	# 						"condition": 3
	# 					}
	# 				}
	# 			]
	# 		},
	# 		{
	# 			"name": "Skelegon",
	# 			"color": 5,
	# 			"cost": 6,
	# 			"power": 7,
	# 			"rarity": 4,
	# 			"abilities": [
	# 				{
	# 					"text": "Create a Hatchling at the end of each turn",
	# 					"trigger": {
	# 						"name": 6
	# 					},
	# 					"keyword": {
	# 						"name": 6,
	# 						"tier": 3
	# 					},
	# 					"targetSlot": {
	# 						"name": 7,
	# 						"condition": 1
	# 					}
	# 				}
	# 			],
	# 			"givingAbilities": [
	# 				{
	# 					"text": "Gets +1 Power at the end of each turn",
	# 					"trigger": {
	# 						"name": 6
	# 					},
	# 					"keyword": {
	# 						"name": 4,
	# 						"tier": 1
	# 					},
	# 					"target": {
	# 						"name": 1
	# 					}
	# 				}
	# 			]
	# 		}
	# 	]
	# },
	# 40: {
	# 	"cards": [
	# 		{
	# 			"name": "Al\'car",
	# 			"color": 6,
	# 			"cost": 6,
	# 			"power": 40,
	# 			"rarity": 4,
	# 			"abilities": [
	# 				{
	# 					"text": "Indestructible",
	# 					"trigger": {
	# 						"name": 10
	# 					},
	# 					"keyword": {
	# 						"name": 32
	# 					}
	# 				},
	# 				{
	# 					"text": "On enter: Destroy all other cards",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 11
	# 					},
	# 					"target": {
	# 						"name": 17
	# 					}
	# 				}
	# 			]
	# 			}
	# 	]
	# },
	# 48: {
	# 	"cards": [
	# 		{
	# 			"name": "Mor\'co",
	# 			"color": 6,
	# 			"cost": 6,
	# 			"power": 48,
	# 			"rarity": 4,
	# 			"castCondition": 1,
	# 			"abilities": [
	# 				{
	# 					"text": "On enter: You gain control of all enemies",
	# 					"trigger": {
	# 						"name": 3
	# 					},
	# 					"keyword": {
	# 						"name": 30
	# 					},
	# 					"target": {
	# 						"name": 16
	# 					}
	# 				}
	# 			]
	# 			}
	# 	]
	# }
}

static func getDeckName(deckID:int):
	match deckID:
		DeckResource.GREEN_STARTER:
			return "Leaf Slime"
		DeckResource.RED_STARTER:
			return "Fire Slime"
		DeckResource.GREEN:
			return "General Prixa"
		DeckResource.RED:
			return "King Krotos"
		DeckResource.GR_DRAGON:
			return "Pyragon"
		_:
			return "Foxy"
