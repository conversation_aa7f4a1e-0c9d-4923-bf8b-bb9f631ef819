[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://ousmejoyyg35"
path="res://.godot/imported/Montserrat-Black.ttf-4b03369dd543368a5dd3498c9b501cd3.fontdata"

[deps]

source_file="res://Assets/Fonts/Montserrat-Black.ttf"
dest_files=["res://.godot/imported/Montserrat-Black.ttf-4b03369dd543368a5dd3498c9b501cd3.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=1
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
