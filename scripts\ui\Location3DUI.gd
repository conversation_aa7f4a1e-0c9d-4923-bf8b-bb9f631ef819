class_name Location_3DUI
extends Node3D

@onready var locationName = $LocationName
@onready var unrevealName = $UnrevealName
@onready var abilityLabel = $AbilityText
@onready var unrevealDesc = $UnrevealDesc
@onready var locationTexture = $LocationTexture
@onready var unrevealTexture = $UnrevealTexture
@onready var anim = $UnrevealTexture/AnimationPlayer
@onready var collision = $Area3D/CollisionShape3D
@onready var keywordNotes = $KeywordNotes
@onready var particle = $UnrevealDesc/EnemyParticle
@onready var target_transform = global_transform
@onready var battleNode = get_tree().get_root().get_node("Battle")
@onready var eventModel: MeshInstance3D = $EventModel


var target_rotation := 0.0
var inspecting = false
#ready
func _ready():
	pass

func _process(delta: float) -> void:
	transform = transform.interpolate_with(target_transform, Const.ANIM_SPEED * delta)
	rotation.z = lerp(rotation.z, target_rotation, Const.ANIM_SPEED * delta)
	
	if inspecting:
		#scale = Vector3.ONE
		if Input.is_action_just_pressed("clicked"):
			battleNode.abort_inspecting(self)

func displayLocation(_location:Card) -> void:
	if not is_node_ready():
		await ready

	if _location:
		var abilitiesText = ""
		for ability in _location.getAbilities():
			abilitiesText += ability.printAbility()
			abilitiesText += "\n"
		self.abilityLabel.text = abilitiesText
		self.locationName.text = _location.name
		self.keywordNotes.text = KeywordNoteService.generateKeywordNotes(abilitiesText, _location.getCastCondition())

		if CardArtService.EventArts.has(_location.name):
			self.locationTexture.material_override.albedo_texture = CardArtService.EventArts[_location.name]
		else:
			self.locationTexture.material_override.albedo_texture = CardArtService.NullEventArt
		
	anim.play("Location Reveal")

func set_layer(layer: int, switch: bool) -> void:
	locationName.set_layer_mask_value(layer, switch)
	abilityLabel.set_layer_mask_value(layer, switch)
	locationTexture.set_layer_mask_value(layer, switch)
	unrevealTexture.set_layer_mask_value(layer, switch)
	keywordNotes.set_layer_mask_value(layer, switch)
	unrevealName.set_layer_mask_value(layer, switch)
	unrevealDesc.set_layer_mask_value(layer, switch)
	particle.set_layer_mask_value(layer, switch)

func _on_area_3d_input_event(camera: Node, event: InputEvent, position: Vector3, normal: Vector3, shape_idx: int) -> void:
	if Input.is_action_just_pressed("clicked"):
		battleNode.inspecting_location.emit(self)

func change_collision_mode(inspecting: bool):
	if collision:
		if inspecting:
			collision.disabled = true
		else:
			collision.disabled = false


#func _on_area_3d_mouse_entered() -> void:
	##locationName.scale *= 2
	#abilityLabel.scale *= 2
	##var i = 0
	##for UI in get_children():
		##if i == 0 or 1 or 2 or 3 or 4 or 6:
			##UI.scale *= 2
		##i+=1
#
#
#func _on_area_3d_mouse_exited() -> void:
	##locationName.scale *= 0.5
	#abilityLabel.scale *= 0.5
	##var i = 0
	##for UI in get_children():
		##if i == 0 or 1 or 2 or 3 or 4 or 6:
			##UI.scale *= 0.5
		##i+=1
