extends Control

@onready var cardSlotUI = $CardSlotUI
@onready var mainPower = $MainPower
@onready var oppoPower = $OppoPower

# Assign corresponding label to display cards in this Lane
func displayLaneState(lane:Lane, powerA:int, powerB:int):
	self.mainPower.text = str(powerA)
	self.oppoPower.text = str(powerB)
	
	var laneTopCards = CardCollection.new()
	for cardSlot in lane.cardSlots:
		if not cardSlot.isEmpty():
			laneTopCards.add([cardSlot.getCardAt(0)])
		else:
			laneTopCards.add([null])
		
	self.cardSlotUI.displayCardCollection(laneTopCards)
