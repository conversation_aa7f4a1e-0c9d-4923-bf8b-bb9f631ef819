class_name Stack
extends Object

var arena: Arena
var stack: Array[StackItem] = []
var resolveInProgress: bool = false
var pause: bool = false
signal globalEffectUpdated
signal updateLog

func _init(_arena: Arena):
	self.arena = _arena

func getStackSize() -> int:
	return stack.size()

func isEmpty() -> bool:
	return stack.is_empty()

func pushAbility(_ability: Ability, _targetCardLane: LaneUIController, _specificCards: Dictionary = {}) -> void:
	stack.append(StackItem.new(_ability, _targetCardLane, _specificCards))

func popAbility() -> StackItem:
	return stack.pop_back()

func isResolveInProgress() -> bool:
	return resolveInProgress

func setResolveInProgress(isResolving: bool) -> void:
	resolveInProgress = isResolving

func sortAbilityByPriority(stackItemA: StackItem, stackItemB: StackItem) -> bool:
	return Keyword.KeywordInfo[stackItemA.ability.getKeyword().getName()]["priority"] < Keyword.KeywordInfo[stackItemB.ability.getKeyword().getName()]["priority"]

###############################################################

# Trigger all ON_ARENA and ON_HAND abilities
func resolveAllPassiveAbilities(_reason: String = "") -> void:
	# print("Resolve passive - " + _reason)
	var reverseLanes = self.arena.lanes.duplicate()
	reverseLanes.reverse()
	for lane: LaneUIController in reverseLanes:
		pushAbilityByTrigger([lane.getLocationByPlayer(TurnService.ActivePlayer)], Trigger.ON_ARENA)
		pushAbilityByTrigger([lane.getLocationByPlayer(TurnService.InactivePlayer)], Trigger.ON_ARENA)
	for lane: LaneUIController in reverseLanes:
		pushAbilityByTrigger(lane.getAllCardsByPlayer(TurnService.ActivePlayer), Trigger.ON_ARENA)
	pushAbilityByTrigger(TurnService.ActivePlayer.hand.getCards(), Trigger.ON_HAND)
	for lane: LaneUIController in reverseLanes:
		pushAbilityByTrigger(lane.getAllCardsByPlayer(TurnService.InactivePlayer), Trigger.ON_ARENA)
	pushAbilityByTrigger(TurnService.InactivePlayer.hand.getCards(), Trigger.ON_HAND)
	
	self.stack.sort_custom(self.sortAbilityByPriority)

	GlobalEffectService.resetAll()
	applyGlobalEffect() # Apply zero global effect to cards
	await resolveStack() # Rebuild global effect
	stateBaseCheck()


# Trigger all abilities by Trigger Type
func resolveAllAbilitiesByType(_triggerType: int) -> void:
	var reverseLanes = self.arena.lanes.duplicate()
	reverseLanes.reverse()
	for lane: LaneUIController in reverseLanes:
		pushAbilityByTrigger(lane.getAllCardsByPlayer(TurnService.ActivePlayer), _triggerType)
	for lane: LaneUIController in reverseLanes:
		pushAbilityByTrigger(lane.getAllCardsByPlayer(TurnService.InactivePlayer), _triggerType)
	await resolveStack()


func resolveAllTurnStartAbilities() -> void:
	var reverseLanes = self.arena.lanes.duplicate()
	reverseLanes.reverse()
	for lane: LaneUIController in reverseLanes: # Trigger active player abilities onlypushAbilityByTrigger([lane.getLocationByPlayer(TurnService.ActivePlayer)], Trigger.ON_ARENA)
		pushAbilityByTrigger([lane.getLocationByPlayer(TurnService.ActivePlayer)], Trigger.ON_TURN_START_ARENA)
		pushAbilityByTrigger(lane.getAllCardsByPlayer(TurnService.ActivePlayer), Trigger.ON_TURN_START_ARENA)
	pushAbilityByTrigger(TurnService.ActivePlayer.hand.getCards(), Trigger.ON_TURN_START_HAND)
	await resolveStack()


func resolveAllTurnEndAbilities() -> void:
	var reverseLanes = self.arena.lanes.duplicate()
	reverseLanes.reverse()
	for lane: LaneUIController in reverseLanes: # Trigger active player abilities only
		pushAbilityByTrigger([lane.getLocationByPlayer(TurnService.ActivePlayer)], Trigger.ON_TURN_END_ARENA)
		pushAbilityByTrigger(lane.getAllCardsByPlayer(TurnService.ActivePlayer), Trigger.ON_TURN_END_ARENA)
	pushAbilityByTrigger(TurnService.ActivePlayer.hand.getCards(), Trigger.ON_TURN_END_HAND)
	await resolveStack()


func resolveAllOnBuffDebuffAbilities(_targetCards: Array[Card], _triggerCard: Card) -> void:
	# print("resolveAllOnBuffDebuffAbilities")
	for card in _targetCards:
		# print("resolveAllOnBuffDebuffAbilities _triggerCard :" + _triggerCard.getOwner().name)
		# print("resolveAllOnBuffDebuffAbilities card :" + card.getOwner().name)
		if _triggerCard.getOwner() == card.getOwner():
			var specificCards: Dictionary = {"sourceCard": _triggerCard}
			pushAbilityByTrigger([card], Trigger.ON_BUFFED, specificCards)
		elif _triggerCard.getOwner() == TurnService.getOpponent(card.getOwner()):
			var specificCards: Dictionary = {"sourceCard": _triggerCard}
			pushAbilityByTrigger([card], Trigger.ON_DEBUFFED, specificCards)
	await resolveStack()


func resolveCardAbilityByTrigger(_cards: Array[Card], _trigger: int = Trigger.NONE, _specificCards: Dictionary = {}) -> void:
	pushAbilityByTrigger(_cards, _trigger, _specificCards)
	await resolveStack()


func resolveGivingAbilities(_givenCard: Card, _givingAbilities: Array[Ability], _specificCards: Dictionary = {}) -> void:
	if _givenCard.isAbilitiesEnabled():
		var abilities = _givingAbilities.duplicate()
		abilities.reverse()
		for givingAbility: Ability in abilities:
			if givingAbility.getTrigger().getName() == Trigger.ON_ENTER:
				pushAbility(givingAbility, self.arena.getLaneByCard(_givenCard), _specificCards)
		await resolveStack()


# Push ability to Stack by trigger type
func pushAbilityByTrigger(_cards: Array[Card], _trigger: int = Trigger.NONE, _specificCards: Dictionary = {}):
	for card in _cards:
		if card.isAbilitiesEnabled():
			var abilities = card.getAbilities().duplicate()
			abilities.reverse()
			for ability in abilities:
				if ability.getTrigger().getName() == _trigger or _trigger == Trigger.NONE:
					pushAbility(ability, self.arena.getLaneByCard(card), _specificCards)
					print("- Add " + ability.getText() + " (" + str(getStackSize()) + ")")


# Resolve the Ability Stack 
func resolveStack() -> void:
	if isResolveInProgress(): return
	
	print("Stack: " + str(getStackSize()))
	setResolveInProgress(true)
	while (!isEmpty()):
		var index = getStackSize()
		var topStack: StackItem = popAbility()
		var ability = topStack.ability
		if TriggerCondition.isTriggerConditionMet(ability, topStack.triggerCardLane, topStack.specificCards):
			print("- Resolve: " + ability.getText() + " (" + str(index) + ")")
			await resolveAbility(ability, topStack.triggerCardLane, topStack.specificCards)
		applyGlobalEffect()
	setResolveInProgress(false)


# Resolve the ability / _lane of the triggerCard in case it not on arena anymore
func resolveAbility(_ability: Ability, _lane: Lane, _specificCards: Dictionary = {}):
	#print("resolveAbility")
	var triggerCard: Card = _ability.getParentCard()
	var owner: Player = _ability.getParentCard().getOwner()
	#print("resolveAbility card : " + triggerCard.name)
	#print("resolveAbility owner : " + owner.name)

	if triggerCard.is_silenced:
		#print("silent")
		return
	# Calculate keyword tier
	var abilityKeywordTier: int
	if not _ability.getKeyword().getDynamicTier() == DynamicTier.NONE:
		abilityKeywordTier = DynamicTier.getDynamicTierValue(_ability, _ability.getKeyword().getDynamicTier(), self.arena, _specificCards)
	else: abilityKeywordTier = _ability.getKeyword().getTier()
	if abilityKeywordTier > 0:
		var tierMultiplier: int = GlobalEffectService.getEffectTierMultiplier(triggerCard, _ability.getKeyword().getName(), _ability.getTrigger().getName())
		var tierModifier: int = GlobalEffectService.getEffectTierModifier(triggerCard, _ability.getKeyword().getName(), _ability.getTrigger().getName())
		#print("tierModifier: " + str(tierModifier))
		abilityKeywordTier = (abilityKeywordTier + tierModifier) * tierMultiplier
		#print("abilityKeywordTier: " + str(abilityKeywordTier))
	# Find target / targetSlot
	var targetCards: Array[Card] = self.arena.getTargetCards(_ability.getTarget(), triggerCard, owner, _lane, _specificCards)
	var targetSlots: Array[LaneCardSlot] = self.arena.getTargetSlots(_ability.getTargetSlot(), triggerCard, owner, _lane, _specificCards)
	resolveAllOnBuffDebuffAbilities(targetCards, _ability.getParentCard())
	
	match _ability.getKeyword().getName():
		Keyword.STRENGTHEN:
			if targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] gives +" + str(abilityKeywordTier) + " to " + MiscService.getNameByCardList(targetCards))
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.STRENGTHEN, targetCards, abilityKeywordTier)
				updateLog.emit()
				await self.arena.modifyCardPermanentPowerEvent(triggerCard, targetCards, abilityKeywordTier)
		Keyword.REPRODUCE:
			if targetSlots.size() > 0 and targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] reproduces")
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.REPRODUCE, targetCards)
				updateLog.emit()
				await self.arena.reproduceEvent(triggerCard, targetCards[0], targetSlots, owner, abilityKeywordTier)
		Keyword.REGEN:
			if targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] heals " + MiscService.getNameByCardList(targetCards))
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.REGEN, targetCards)
				updateLog.emit()
				await self.arena.regenCardPermanentPowerEvent(triggerCard, targetCards)
		Keyword.PROD_MANA:
			#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] produce " + str(abilityKeywordTier) + " permanent mana to " + owner.getName())
			EventLogService.LogCardEffectEvent(triggerCard, Keyword.PROD_MANA, [], abilityKeywordTier)
			updateLog.emit()
			await self.arena.modifyPlayerManaEvent(triggerCard, owner, abilityKeywordTier)
		Keyword.TEMP_MANA:
			#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] produce " + str(abilityKeywordTier) + " temp mana to " + owner.getName())
			EventLogService.LogCardEffectEvent(triggerCard, Keyword.TEMP_MANA, [], abilityKeywordTier)
			updateLog.emit()
			await self.arena.modifyPlayerTempManaEvent(triggerCard, owner, abilityKeywordTier)
		Keyword.WEAKEN:
			if targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] afflicts -" + str(abilityKeywordTier) + " to " + MiscService.getNameByCardList(targetCards))
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.WEAKEN, targetCards, abilityKeywordTier)
				updateLog.emit()
				await self.arena.modifyCardPermanentPowerEvent(triggerCard, targetCards, -1 * abilityKeywordTier)
		Keyword.DESTROY:
			if targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] destroys " + MiscService.getNameByCardList(targetCards))
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.DESTROY, targetCards)
				updateLog.emit()
				await self.arena.destroyCardEvent(triggerCard, targetCards)
		Keyword.IGNITE:
			#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] ignites " + str(abilityKeywordTier))
			EventLogService.LogCardEffectEvent(triggerCard, Keyword.IGNITE)
			updateLog.emit()
			await self.arena.igniteCardEvent(triggerCard, abilityKeywordTier, 1, owner)
		Keyword.DISCARD:
			if targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] discards " + MiscService.getNameByCardList(targetCards))
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.DISCARD, targetCards)
				updateLog.emit()
				await self.arena.discardCardEvent(triggerCard, targetCards)
		Keyword.DRAW:
			self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] draws " + str(abilityKeywordTier) + " card(s) for " + owner.getName())
			EventLogService.LogCardEffectEvent(triggerCard, Keyword.DRAW, [], abilityKeywordTier)
			updateLog.emit()
			await self.arena.drawEvent(abilityKeywordTier, owner)
		Keyword.DUPLICATE:
			#print("duplicate stack")
			#print("targetSlots: " + str(targetSlots.size()))
			#print("targetCards: " + str(targetCards.size()))
			if targetSlots.size() > 0 and targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] duplicates " + MiscService.getNameByCardList(targetCards))
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.DUPLICATE, targetCards)
				updateLog.emit()
				await self.arena.duplicateEvent(triggerCard, targetCards[0], targetSlots, owner)
		Keyword.GIVE_ABILITY:
			if targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] gives ability to " + MiscService.getNameByCardList(targetCards))
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.GIVE_ABILITY, targetCards)
				updateLog.emit()
				await self.arena.giveAbilityEvent(triggerCard, targetCards)
		Keyword.TRIGGER_ON_ENTER:
			if targetCards.size() > 0:
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.TRIGGER_ON_ENTER, targetCards)
				updateLog.emit()
				for card in targetCards:
					await resolveCardAbilityByTrigger([card], Trigger.ON_ENTER)
		Keyword.TRIGGER_ON_DEAD:
			if targetCards.size() > 0:
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.TRIGGER_ON_DEAD, targetCards)
				updateLog.emit()
				for card in targetCards:
					await resolveCardAbilityByTrigger([card], Trigger.ON_DEAD)
		Keyword.THORN:
			if targetCards.size() > 0:
				#self.arena.alertMessage.emit("Thorn " + str(abilityKeywordTier))
				
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.THORN, targetCards, abilityKeywordTier)
				updateLog.emit()
				await self.arena.modifyCardPermanentPowerEvent(triggerCard, targetCards, -1 * abilityKeywordTier)
		Keyword.SWAP_OWNER:
			if targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] swaps " + MiscService.getNameByCardList(targetCards) + " owner")
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.SWAP_OWNER, targetCards)
				updateLog.emit()
				await self.arena.swapOwnerEvent(triggerCard, targetCards)
		Keyword.SILENT:
			if targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] swaps " + MiscService.getNameByCardList(targetCards) + " owner")
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.SILENT, targetCards)
				updateLog.emit()
				await self.arena.silentAbilityEvent(triggerCard, targetCards)
		Keyword.MIMIC:
			if targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] duplicates " + MiscService.getNameByCardList(targetCards))
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.MIMIC, targetCards)
				updateLog.emit()
				await self.arena.mimicEvent(triggerCard, targetCards[0])
		Keyword.UNSUMMONED:
			if targetCards.size() > 0:
				#self.arena.alertMessage.emit("[color=#ffffff]" + triggerCard.getName() + "[/color] swaps " + MiscService.getNameByCardList(targetCards) + " owner")
				EventLogService.LogCardEffectEvent(triggerCard, Keyword.UNSUMMONED, targetCards)
				updateLog.emit()
				await self.arena.unsummonedAbilityEvent(triggerCard, targetCards)
		Keyword.DISABLE_SLOT_ACTIVE:
			GlobalEffectService.updateLaneCardSlotStatus(_ability, targetSlots, LaneCardSlot.DISABLED, floor(abilityKeywordTier + TurnService.TurnCount))

		# Passive abilities
		Keyword.EMPOWER:
			GlobalEffectService.modifyCardPowerModifier(_ability, [triggerCard], targetCards.size() * abilityKeywordTier)
		Keyword.GROWTH:
			GlobalEffectService.modifyCardPowerModifier(_ability, [triggerCard], abilityKeywordTier)
		Keyword.INSPIRE:
			GlobalEffectService.modifyCardPowerModifier(_ability, targetCards, abilityKeywordTier)
		Keyword.MANA_EMPOWER:
			GlobalEffectService.modifyCardPowerModifier(_ability, [triggerCard], owner.getBaseMaxMana() * abilityKeywordTier)
		Keyword.CONVOKE:
			GlobalEffectService.modifyCardCostModifier(_ability, [triggerCard], targetCards.size() * abilityKeywordTier * -1)
		Keyword.MULTIPLY_WEAKEN:
			GlobalEffectService.addEffectTierMultiplier(_ability, targetCards, Keyword.WEAKEN, Trigger.NONE, abilityKeywordTier)
		Keyword.MULTIPLY_STRENGTHEN:
			GlobalEffectService.addEffectTierMultiplier(_ability, targetCards, Keyword.STRENGTHEN, Trigger.NONE, abilityKeywordTier)
		Keyword.MODIFY_WEAKEN:
			GlobalEffectService.addEffectTierModifier(_ability, targetCards, Keyword.WEAKEN, Trigger.NONE, abilityKeywordTier)
		Keyword.MODIFY_STRENGTHEN:
			GlobalEffectService.addEffectTierModifier(_ability, targetCards, Keyword.STRENGTHEN, Trigger.NONE, abilityKeywordTier)
		Keyword.MULTIPLY_ON_ARENA:
			GlobalEffectService.addEffectTierMultiplier(_ability, targetCards, Keyword.NONE, Trigger.ON_ARENA, abilityKeywordTier)
		Keyword.MULTIPLY_ON_ENTER:
			GlobalEffectService.addEffectTierMultiplier(_ability, targetCards, Keyword.NONE, Trigger.ON_ENTER, abilityKeywordTier)
		Keyword.COST_BECOME:
			GlobalEffectService.modifyCardCostModifier(_ability, [triggerCard], triggerCard.getBaseCost() * -1 + abilityKeywordTier)
		Keyword.DISABLE_SLOT:
			GlobalEffectService.updateLaneCardSlotStatus(_ability, targetSlots, LaneCardSlot.DISABLED, floor(abilityKeywordTier + TurnService.TurnCount))
		Keyword.LOWER_POWER_WINS:
			GlobalEffectService.setLaneWinConditionEffect(_ability, _lane, LaneWinCondition.LESS_POWER)
		Keyword.STRENGTHEN_PASSIVE:
			# EventLogService.LogCardEffectEvent(triggerCard, Keyword.STRENGTHEN_PASSIVE, targetCards, abilityKeywordTier)
			GlobalEffectService.modifyCardPowerModifier(_ability, [triggerCard], abilityKeywordTier)


# Apply global effect to all players and cards
func applyGlobalEffect() -> void:
	# Apply to cards
	for card in self.arena.getAllArenaCards():
		card.updateEffectPowerModifier3DUI(GlobalEffectService.getCardPowerModifier(card))
		card.updateEffectCostModifier3DUI(GlobalEffectService.getCardCostModifier(card))
	for card in self.arena.getAllHandCards():
		card.updateEffectPowerModifier3DUI(GlobalEffectService.getCardPowerModifier(card))
		card.updateEffectCostModifier3DUI(GlobalEffectService.getCardCostModifier(card))
	for card in self.arena.getAllGraveyardCards():
		card.updateEffectPowerModifier3DUI(GlobalEffectService.getCardPowerModifier(card))
		card.updateEffectCostModifier3DUI(GlobalEffectService.getCardCostModifier(card))
	
	# Apply to locations & slots
	for lane: LaneUIController in self.arena.lanes:
		if lane.getLocationByPlayer(self.arena.mainPlayer):
			lane.getLocationByPlayer(self.arena.mainPlayer).updateEffectPowerModifier3DUI(GlobalEffectService.getCardPowerModifier(lane.getLocationByPlayer(self.arena.mainPlayer)))
		if lane.getLocationByPlayer(self.arena.oppoPlayer):
			lane.getLocationByPlayer(self.arena.oppoPlayer).updateEffectPowerModifier3DUI(GlobalEffectService.getCardPowerModifier(lane.getLocationByPlayer(self.arena.oppoPlayer)))
		for slotIndex in Const.CARD_PER_LANE:
			lane.getSlot(slotIndex).setState(GlobalEffectService.getLaneCardSlotStatus(lane.getSlot(slotIndex)))
	globalEffectUpdated.emit()


# Destroy card with Power <= 0
func stateBaseCheck() -> void:
	for lane: LaneUIController in self.arena.lanes:
		for card in lane.getAllCards():
			if card.getPower() <= 0:
				self.arena.destroyCardEvent(card, [card])
