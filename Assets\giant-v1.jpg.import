[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://uh3ye8darnsj"
path.s3tc="res://.godot/imported/giant-v1.jpg-c9738c21fb7d66a53fe47bff84ece7f7.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}

[deps]

source_file="res://EssentialGames/CardGame/Assets/giant-v1.jpg"
dest_files=["res://.godot/imported/giant-v1.jpg-c9738c21fb7d66a53fe47bff84ece7f7.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
