class_name Arena
extends Node3D

@onready var lanes = [$Left, $Mid, $Right]
@onready var mainPlayer = $Player
@onready var oppoPlayer = $Enemy
@onready var neutral = $Neutral
@onready var castableSlot = []
@onready var battleNode = get_tree().get_root().get_node("Battle")

var abilityStack: AbilityStack
var passiveStack: PassiveStack
var handSelectedIndex: int = -1
var is_dragging = false
var closetSlot
var closetSlotIndex
var closetSlotLane
var closetSlotLaneIndex
var isCasting : bool = false
var turnEnding : bool = false

signal playerManaChanged(player: Player)
signal alertMessage(message: String)
signal changeEndTurnButton(disabled: bool)
signal situationUpdate(player: Player)
signal dragging(bool)
signal updateLog
signal opponentDialog(message: String)
signal playerDialog(message: String)


# Initiate players, decks, hands, and lanes
func _ready():
	self.mainPlayer.setName(MiscService.PlayerName)
	self.oppoPlayer.setName(DeckResource.getDeckName(DeckResource.OppoDeck))
	self.mainPlayer.setSide(true)
	self.oppoPlayer.setSide(false)
	self.mainPlayer.setArena(self)
	self.oppoPlayer.setArena(self)
	self.mainPlayer.getDeck().add(ResourceService.GenerateDeck(DeckResource.MainDeck, mainPlayer))
	self.oppoPlayer.getDeck().add(ResourceService.GenerateDeck(DeckResource.OppoDeck, oppoPlayer))
	self.mainPlayer.getHand().handSelectedIndex.connect(self.onHandSelectedIndex.bind(mainPlayer))
	self.oppoPlayer.getHand().handSelectedIndex.connect(self.onHandSelectedIndex.bind(oppoPlayer))
	self.situationUpdate.connect(self.lightCastableCard)
	#self.situationUpdate.connect(mainPlayer.hand.lightCastableCard)
	self.dragging.connect(self.set_dragging)
	self.mainPlayer.getHand().draggedIndex.connect(self.lightCastableSlot)
	self.mainPlayer.manaChanged.connect(self.onManaChanged.bind(mainPlayer))
	self.oppoPlayer.manaChanged.connect(self.onManaChanged.bind(oppoPlayer))
	self.mainPlayer.aiCastCard.connect(self.cardCastEvent.bind(mainPlayer))
	self.oppoPlayer.aiCastCard.connect(self.cardCastEvent.bind(oppoPlayer))
	self.mainPlayer.aiEndTurn.connect(self.endTurnEvent)
	self.oppoPlayer.aiEndTurn.connect(self.endTurnEvent)
	
	var existedLocation: Array[int] = []

	for laneIndex in Const.LANE_COUNT:
		lanes[laneIndex].setslot(laneIndex, mainPlayer, oppoPlayer, neutral)

		#random location
		var locationDataIndex = LocationData.LocationList[LocationData.DifficultySet]["events"].keys()[randi() % LocationData.LocationList[LocationData.DifficultySet]["events"].size()]
		while (existedLocation.has(locationDataIndex)):
			locationDataIndex = LocationData.LocationList[LocationData.DifficultySet]["events"].keys()[randi() % LocationData.LocationList[LocationData.DifficultySet]["events"].size()]

		#fix random location here
		locationDataIndex = LocationData.event.ManaSurge

		get_parent().inspecting.connect(lanes[laneIndex].locationDisplay.change_collision_mode)
		self.lanes[laneIndex].laneSlotSelected.connect(self.onLaneSlotSelected.bind(laneIndex))
		self.lanes[laneIndex].setUnrevealedLocationText(laneIndex)
		self.lanes[laneIndex].setLocationByPlayer(ResourceService.GenerateLocation(locationDataIndex, mainPlayer), mainPlayer)
		self.lanes[laneIndex].setLocationByPlayer(ResourceService.GenerateLocation(locationDataIndex, oppoPlayer), oppoPlayer)
		existedLocation.append(locationDataIndex)
	
	self.abilityStack = AbilityStack.new(self)
	self.passiveStack = PassiveStack.new(self)
	self.passiveStack.globalEffectUpdated.connect(self.onGlobalEffectUpdated)
	
	initBattleEvent()

func _physics_process(delta):
	if TurnService.ActivePlayer.get_name() != mainPlayer.get_name() or handSelectedIndex == -1 or isCasting:
		return
	#if !is_dragging:
		#closetSlot = null
		#dropableSlot = null
	var draggingCard = mainPlayer.getHand().get_child(handSelectedIndex)
	if draggingCard == null:
		return
	if is_dragging and draggingCard.brightness_on: # and draggingCard.get_parent().is_player:
		var mouseY = get_parent().cardAboveUI.get_child(0).get_mouse_position().y
		if mouseY < get_parent().cardAboveUI.get_child(0).size.y * 0.78:
			var closestDistant = 9999.0
			var castCon
			#var unprojected = camera.unproject_position(target_transform.origin)
			for slot in castableSlot:
				var lane = get_tree().get_nodes_in_group("lane")
				if slot[0] == handSelectedIndex:
					var camera = get_viewport().get_camera_3d()
					var slotPreviewNode = lane[slot[1]].get_child(13).get_child(slot[2]).get_child(0)
					#slotPreviewNode.global_transform.origin = camera.project_position(camera.unproject_position(slotPreviewNode.global_transform.origin), 10)
					if (get_parent().cardAboveUI.get_child(0).get_mouse_position()).distance_to(camera.unproject_position(slotPreviewNode.global_transform.origin)) < closestDistant:
						#print(get_viewport().get_mouse_position().distance_to(camera.unproject_position(slotPreviewNode.global_transform.origin)))
					#if draggingCard.global_transform.origin.distance_to(camera.project_position(camera.unproject_position(slotPreviewNode.global_transform.origin), 10)) < closestDistant:
						if closetSlot:
							var recentClosetlane = closetSlot.get_parent().get_parent().get_parent()
							castCon = mainPlayer.getHand().get_child(handSelectedIndex).castConLabel.text
							match castCon:
								"SACRIFICE":
									recentClosetlane.set_slot_state(closetSlotIndex, closetSlot, LaneCardSlot.SACRIFIABLE)
								"SYMBIONT":
									recentClosetlane.set_slot_state(closetSlotIndex, closetSlot, LaneCardSlot.SYMBIONTABLE)
								"BOUNCE":
									recentClosetlane.set_slot_state(closetSlotIndex, closetSlot, LaneCardSlot.BOUNCEABLE)
								_:
									if closetSlotIndex >= 4:
										recentClosetlane.set_slot_state(closetSlotIndex, closetSlot, LaneCardSlot.CASTABLE_PLAYER_SIDE)
									else:
										recentClosetlane.set_slot_state(closetSlotIndex, closetSlot, LaneCardSlot.CASTABLE)
									#if slot[2] >3:
										#lane[slot[1]].set_slot_state(lane[slot[1]].get_child(13).get_child(2).get_child(0), "default")
										#lane[slot[1]].set_slot_state(lane[slot[1]].get_child(13).get_child(3).get_child(0), "default")
						closestDistant = get_viewport().get_mouse_position().distance_to(camera.unproject_position(slotPreviewNode.global_transform.origin))
						#closestDistant = draggingCard.global_transform.origin.distance_to(slotPreviewNode.global_transform.origin)
						closetSlot = slotPreviewNode
						closetSlotLaneIndex = slot[1]
						closetSlotIndex = slot[2]
						closetSlotLane = closetSlot.get_parent().get_parent().get_parent()
						castCon = mainPlayer.getHand().get_child(handSelectedIndex).castConLabel.text
						#print(closestDistant)
						if closestDistant < 170:
							match castCon:
								"🩸":
									closetSlotLane.set_slot_state(closetSlotIndex, closetSlot, LaneCardSlot.READY_TO_SAC)
									#dropableSlot = closetSlot
								"🌳":
									closetSlotLane.set_slot_state(closetSlotIndex, closetSlot, LaneCardSlot.READY_TO_SYM)
									#dropableSlot = closetSlot
								"⤴️":
									closetSlotLane.set_slot_state(closetSlotIndex, closetSlot, LaneCardSlot.READY_TO_SYM)
									#dropableSlot = closetSlot
								_:
									if closetSlotIndex >= 4:
										closetSlotLane.set_slot_state(closetSlotIndex, closetSlot, LaneCardSlot.READY_TO_CAST_PLAYER_SIDE)
									else:
										closetSlotLane.set_slot_state(closetSlotIndex, closetSlot, LaneCardSlot.READY_TO_CAST)
									#dropableSlot = closetSlot
						else:
							#dropableSlot = null
							closetSlot = null
						#closetSlotLane.set_slot_state(closetSlot, "dropping")
		else:
			mainPlayer.getHand().draggedIndex.emit(draggingCard.brightness_on, is_dragging)
			#dropableSlot = null
			closetSlot = null
					#lane[slot[1]].set_slot_state(lane[slot[1]].get_child(13).get_child(slot[2]).get_child(0), "default")
	if closetSlot and Input.is_action_just_released("clicked"):
		dragging.emit(false)
		mainPlayer.getHand().draggedIndex.emit(draggingCard.brightness_on, is_dragging)
		onLaneSlotSelected(closetSlotIndex, closetSlotLaneIndex)
		#closetSlotLane.get_child(closetSlotIndex).setState(LaneCardSlot.DISABLED)
		closetSlotLane.get_child(closetSlotIndex).slotStateChange.emit(closetSlotIndex, LaneCardSlot.DISABLED)
		#dropableSlot = null
		closetSlot = null
	elif Input.is_action_just_released("clicked"):
		closetSlot = null

##########################################################################
##########################################################################
############################# Events #####################################
##########################################################################
##########################################################################

func randomLocationReveal() -> void:
	var _rand = randi()
	if TurnService.TurnCount >= 0 and TurnService.TurnCount < 5 and not self.lanes[0].lane.isLocationRevealed:
		if _rand % (5 - TurnService.TurnCount) == 0:
			self.lanes[0].revealLocation()
			await self.abilityStack.resolveCardAbilityByTrigger([self.lanes[0].getLocationByPlayer(mainPlayer)], Trigger.ON_ENTER)
			await self.abilityStack.resolveCardAbilityByTrigger([self.lanes[0].getLocationByPlayer(oppoPlayer)], Trigger.ON_ENTER)
	if TurnService.TurnCount >= 5 and TurnService.TurnCount < 9 and not self.lanes[1].lane.isLocationRevealed:
		if _rand % (9 - TurnService.TurnCount) == 0:
			self.lanes[1].revealLocation()
			await self.abilityStack.resolveCardAbilityByTrigger([self.lanes[1].getLocationByPlayer(mainPlayer)], Trigger.ON_ENTER)
			await self.abilityStack.resolveCardAbilityByTrigger([self.lanes[1].getLocationByPlayer(oppoPlayer)], Trigger.ON_ENTER)
	if TurnService.TurnCount >= 9 and TurnService.TurnCount < 13 and not self.lanes[2].lane.isLocationRevealed:
		if _rand % (13 - TurnService.TurnCount) == 0:
			self.lanes[2].revealLocation()
			await self.abilityStack.resolveCardAbilityByTrigger([self.lanes[2].getLocationByPlayer(mainPlayer)], Trigger.ON_ENTER)
			await self.abilityStack.resolveCardAbilityByTrigger([self.lanes[2].getLocationByPlayer(oppoPlayer)], Trigger.ON_ENTER)


func initBattleEvent() -> void:
	TurnService.initBattleEvent(mainPlayer, oppoPlayer)
	if not Mode.mode == Mode.TEST_CARD: shuffleEvent(mainPlayer)
	if not Mode.mode == Mode.TEST_CARD: shuffleEvent(oppoPlayer)
	await get_tree().create_timer(Const.AWAIT_TIME * 0.5).timeout
	
	# Only for FP challenge
	if DeckResource.OppoDeck == DeckResource.GREEN_RED:
		await dragonDialog()
	
	drawEvent(Const.INITIAL_DRAW, mainPlayer)
	await drawEvent(Const.INITIAL_DRAW, oppoPlayer)
	initTurnEvent()


# When a turn start
func initTurnEvent() -> void:
	turnEnding = false
	TurnService.initTurnEvent()
	get_parent().find_child("TurnCount").text = str(ceil(float(TurnService.TurnCount) / 2.0))
	await randomLocationReveal()
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	await self.passiveStack.resolveAllPassiveAbilities("New turn") # TurnCount changes, trigger all passives
	await self.abilityStack.resolveAllTurnStartAbilities()
	TurnService.ActivePlayer.modifyMaxMana(1)
	TurnService.ActivePlayer.fillMana()
	TurnService.InactivePlayer.setHandBrightness(false)
	await drawEvent(Const.TURN_DRAW, TurnService.ActivePlayer)
	
	if (TurnService.ActivePlayer == oppoPlayer):
		oppoPlayer.hasCastThisTurn = false
		if Mode.mode == Mode.MAN_VS_BOT or Mode.mode == Mode.BOT_VS_BOT:
			oppoPlayer.think()
	else:
		alertMessage.emit("[color=#ffffff]Your Turn[/color] (" + str(ceil(float(TurnService.TurnCount) / 2.0)) + ")")
		mainPlayer.hasCastThisTurn = false
		if Mode.mode == Mode.BOT_VS_BOT:
			mainPlayer.think()
		else:
			changeEndTurnButton.emit(false)


# When a turn end (PLayer click end turn)
func endTurnEvent() -> void:
	turnEnding = true
	#lightCastableCard(TurnService.ActivePlayer)
	situationUpdate.emit(TurnService.ActivePlayer)
	changeEndTurnButton.emit(true)
	EventLogService.LogEndTurnEvent(TurnService.ActivePlayer)
	updateLog.emit()
	await self.abilityStack.resolveAllTurnEndAbilities()
	
	#if TurnService.TurnCount >= Const.TURN_LIMIT * 2:
		#return
	if not mainPlayer.hasCastThisTurn and not oppoPlayer.hasCastThisTurn and getAllArenaCards().size() >= 15 or TurnService.TurnCount >= Const.TURN_LIMIT *2 :
		#await get_tree().create_timer(Const.AWAIT_TIME).timeout
		get_parent().find_child("VictoryPopup").visible = true
		if getArenaWinner()[0] == 0:
			if DeckResource.OppoDeck == DeckResource.GREEN_RED: ScoreService.dragonLose += 1
			get_parent().find_child("Victory").visible = true
		elif getArenaWinner()[0] == 1:
			if DeckResource.OppoDeck == DeckResource.GREEN_RED: ScoreService.dragonWin += 1
			get_parent().find_child("Defeat").visible = true
		else:
			get_parent().find_child("Tie").visible = true
		return
	
	TurnService.endTurnEvent()
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	initTurnEvent()


# When a mainPlayer draw X cards
func drawEvent(_amount: int, _player: Player = TurnService.ActivePlayer) -> void:
	var drawnCards: Array[Card] = await _player.getDeck().pull(_amount)
	for card in drawnCards:
		card.setDrawnTurn()
	#var tween = create_tween()
	#tween.tween_interval(0.5)
	#tween.tween_callback(_player.getHand().add(drawnCards))
	await _player.getHand().add(drawnCards)
	await self.passiveStack.resolveAllPassiveAbilities("Card drawn") # Cards in hand changes, trigger all passives


# When a mainPlayer shuffle their deck
func shuffleEvent(_player: Player = TurnService.ActivePlayer) -> void:
	_player.getDeck().shuffle()


# When a mainPlayer mulligan their hand
func mulliganEvent(_player: Player = TurnService.ActivePlayer) -> void:
	if TurnService.TurnCount > 1: return
	var amount = _player.getHand().getCardCount()
	#for i in movingQueue.get_children():
		#i.queue_free()
	#print("cardcount is " + str(amount))
	#var pullCards: Array[Card] = _player.getHand().pull(amount)
	#_player.getDeck().add(pullCards)
	_player.getDeck().add(await _player.getHand().pull(amount))
	await get_tree().create_timer(0.5).timeout
	shuffleEvent(_player)
	drawEvent(amount, _player)


# When a player cast a card
func cardCastEvent(_castingCard: Card, _laneIndex: int, _slotIndex: int, _player: Player = TurnService.ActivePlayer) -> bool:
	if not isCastable(_castingCard, _laneIndex, _slotIndex, _player, true):
		return false
	#print("cardCastEvent")
	#print(_castingCard.name)
	changeEndTurnButton.emit(true)
	isCasting = true
	EventLogService.LogCastCardEvent(_player, _castingCard)
	updateLog.emit()
	#Draw3d.line(_castingCard.getCard3DUI().global_position, getLane(_laneIndex).cardSlots[_slotIndex].global_position, Color.WHITE, int(Const.AWAIT_TIME))
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout

	var specificCards: Dictionary = {}
	match _castingCard.getCastCondition():
		CastCondition.NORMAL:
			pass
		CastCondition.SYMBIONT:
			var hostCard: Card = getLane(_laneIndex).getSlotTopCard(_slotIndex)
			await self.abilityStack.resolveCardAbilityByTrigger([hostCard], Trigger.ON_HOST)
			specificCards["hostCard"] = hostCard
		CastCondition.SACRIFICE:
			var sacrificedCard: Card = getLane(_laneIndex).getSlotTopCard(_slotIndex)
			await self.abilityStack.resolveCardAbilityByTrigger([sacrificedCard], Trigger.ON_DEAD)
			sacrificedCard.setDieTurn()
			
			# Get the card's 3D UI and move it to movingQueue
			var card3d = sacrificedCard.getCard3DUI()
			var movingQueue
			if sacrificedCard.getOwner().isOnMainSide():
				movingQueue = battleNode.find_child("Player").movingQueue
			else:
				movingQueue = battleNode.find_child("Enemy").movingQueue
				
			# Reparent the card3d to movingQueue before removing from lane
			card3d.reparent(movingQueue)
			
			# Remove from lane and add to graveyard
			getLane(_laneIndex).removeCards([sacrificedCard], false) # Remove w/o rearranging
			_player.getGraveyard().add([sacrificedCard])
			specificCards["sacrificedCard"] = sacrificedCard
		CastCondition.BOUNCE:
			var bouncedCard: Card = getLane(_laneIndex).getSlotTopCard(_slotIndex)
			
			await self.abilityStack.resolveCardAbilityByTrigger([bouncedCard], Trigger.ON_BOUNCE)
			# Get the card's 3D UI and move it to movingQueue
			var card3d = bouncedCard.getCard3DUI()
			var movingQueue
			if bouncedCard.getOwner().isOnMainSide():
				movingQueue = battleNode.find_child("Player").movingQueue
			else:
				movingQueue = battleNode.find_child("Enemy").movingQueue
				
			# Reparent the card3d to movingQueue before removing from lane
			card3d.reparent(movingQueue)
			
			getLane(_laneIndex).removeCards([bouncedCard], false) # Remove w/o rearranging
			# _player.getGraveyard().add(temp)
			var temp = getLane(_laneIndex).getSlotCards(_slotIndex)
			
			getLane(_laneIndex).getSlot(_slotIndex).removeCards(temp)
			_player.getGraveyard().add(temp)
			_player.getHand().add([bouncedCard])
			specificCards["bouncedCard"] = bouncedCard

	alertMessage.emit("[color=#ffffff]" + _player.getName() + "[/color] cast [color=#ffffff]" + _castingCard.getName() + "[/color]")
	#await get_tree().create_timer(Const.AWAIT_TIME * 1).timeout

	#print(getLane(_laneIndex)._cardSlots)
	
	_player.useMana(_castingCard.getCost())
	#var tween = self.create_tween()
	await _player.getHand().removeCards([_castingCard])
	await getLane(_laneIndex).addAt(_player, [_castingCard], _slotIndex)
	#tween.tween_interval(3)
	#tween.tween_callback(_player.getHand().removeCards.bind([_castingCard]))
	_player.hasCastThisTurn = true
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	await self.passiveStack.resolveAllPassiveAbilities("Card entered") # Cards move from hand to arena, trigger all passives
	await self.abilityStack.resolveCardAbilityByTrigger([_castingCard], Trigger.ON_ENTER, specificCards)
	
	# Trigger other cards with ON_[something]_ENTER abilities
	specificCards = {}
	specificCards["sourceCard"] = _castingCard
	
	var laneAllies: Array[Card] = []
	laneAllies.append_array(getLane(_laneIndex).getAllCardsByPlayer(_castingCard.getOwner()))
	laneAllies.append(getLane(_laneIndex).getLocationByPlayer(_castingCard.getOwner()))
	laneAllies.erase(_castingCard)
	await self.abilityStack.resolveCardAbilityByTrigger(laneAllies, Trigger.ON_LANE_ALLY_ENTER, specificCards)
	isCasting = false
	situationUpdate.emit(_player)
	
	changeEndTurnButton.emit(false)
	updateLog.emit()
	return true


# For DESTROY ability
func destroyCardEvent(_triggerCard: Card, _targetCards: Array[Card]) -> void:
	if _targetCards.size() == 1 and _triggerCard == _targetCards[0]:
		pass
	else:
		for card in _targetCards:
			Draw3d.line(_triggerCard.getCard3DUI().global_position, card.getCard3DUI().global_position, Color.LIGHT_CORAL, int(Const.AWAIT_TIME))
		#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	
	for card in _targetCards:
		if _triggerCard.getOwner() != card.getOwner() and isProtected(card, Keyword.DESTROY): continue
		var lane: LaneUIController = getLaneByCard(card)
		if lane:
			await self.abilityStack.resolveCardAbilityByTrigger([card], Trigger.ON_DEAD)
			card.setDieTurn()
			
			# Get the card's 3D UI and move it to movingQueue
			var card3d = card.getCard3DUI()
			var movingQueue
			if card.getOwner().isOnMainSide():
				movingQueue = battleNode.find_child("Player").movingQueue
			else:
				movingQueue = battleNode.find_child("Enemy").movingQueue
				
			# Reparent the card3d to movingQueue before removing from lane
			card3d.reparent(movingQueue)
			
			# Add to graveyard which will handle the movement animation
			card.getOwner().getGraveyard().add(lane.removeCards([card],true))
			EventLogService.LogCardDieEvent(card)
			updateLog.emit()
	
	await self.passiveStack.resolveAllPassiveAbilities("Card died") # Cards move from arena to graveyard, trigger all passives


# For IGNITE, DISCARD ability
func discardCardEvent(_triggerCard: Card, _targetCards: Array[Card]) -> void:
	for card in _targetCards:
		Draw3d.line(_triggerCard.getCard3DUI().global_position, card.getCard3DUI().global_position, Color.LIGHT_CORAL, int(Const.AWAIT_TIME))
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	for card in _targetCards:
		var hand: CardCollectionUIController = card.getOwner().getHand()
		if hand:
			await self.abilityStack.resolveCardAbilityByTrigger([card], Trigger.ON_DISCARD)
			#hand.removeSingle(card)
			card.getOwner().getGraveyard().addSingle(await hand.removeSingle(card))
	await self.passiveStack.resolveAllPassiveAbilities("Card discard") # Cards move from hand to graveyard, trigger all passives


# For IGNITE ability
func igniteCardEvent(_triggerCard: Card, _powerModifier: int, _ignitedTurns: int, _player: Player = TurnService.ActivePlayer) -> void:
	var ignitedCards: Array[Card] = _player.getDeck().pull(1)
	if ignitedCards.size() == 0:
		return
	for card in ignitedCards:
		card.is_ignited = true
	modifyCardPermanentPowerEvent(_triggerCard, ignitedCards, _powerModifier)
	for card in ignitedCards:
		card.setDrawnTurn()
		# card.is_ignited = true
		card.addAbilities([Ability.new(card, "[Ignited]", Keyword.new(Keyword.DISCARD), Trigger.new(Trigger.ON_TURN_END_HAND, TriggerCondition.TURN_DRAWN, _ignitedTurns), Target.new(Target.SELF))])
	await _player.getHand().add(ignitedCards)
	
	for card in ignitedCards:
		Draw3d.line(_triggerCard.getCard3DUI().global_position, card.getCard3DUI().global_position, Color.WHITE, int(Const.AWAIT_TIME))
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	await self.passiveStack.resolveAllPassiveAbilities("Ignite") # Cards in hand changes, trigger all passives


# For REPRODUCE ability
func reproduceEvent(_triggerCard: Card, _targetCard: Card, _targetSlots: Array[LaneCardSlot], _owner: Player, _childPower: int) -> void:
	for slot in _targetSlots:
		var createdCardName: String = _targetCard.getName() + " " + KeywordNoteService.extractReproduceCardName(_triggerCard)
		var createdCard: Card = Card.new(_owner, _targetCard.getColor(), createdCardName, _targetCard.getRarity(), 1, _childPower, CastCondition.NORMAL)
		var givingAbilities: Array[Ability] = _triggerCard.getGivingAbilities()
		var abilities: Array[Ability] = []
		abilities.append(Ability.new(createdCard, "Fully grown in 2 turns", Keyword.new(Keyword.GROWTH, _targetCard.getPower()-1), Trigger.new(Trigger.ON_ARENA, TriggerCondition.TURN_STAY, 2)))
		for givingAbility in givingAbilities:
			abilities.append(Ability.new(createdCard, givingAbility.getText(), givingAbility.getKeyword(), givingAbility.getTrigger(), givingAbility.getTarget(), givingAbility.getTargetSlot()))
		createdCard.addAbilities(abilities)
		
		var lane = getLaneBySlot(slot)
		var slotIndex = lane.getAllSlots().find(slot)
		createCardOnArenaEvent(_owner, createdCard, lane, slotIndex)
		
		Draw3d.line(_triggerCard.getCard3DUI().global_position, createdCard.getCard3DUI().global_position, Color.WHITE, int(Const.AWAIT_TIME))
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout


# For DUPLICATE ability
func duplicateEvent(_triggerCard: Card, _targetCard: Card, _targetSlots: Array[LaneCardSlot], _owner: Player) -> void:
	#print("duplicateEvent")
	if getLaneByCard(_targetCard) and not getLaneByCard(_targetCard).isTopCard(_targetCard): return
	for slot in _targetSlots:
		var createdCard: Card = Card.new(_owner, _targetCard.getColor(), _targetCard.getName(), _targetCard.getRarity(), _targetCard.getBaseCost(), _targetCard.getBasePower(), _targetCard.getCastCondition())
		createdCard.cloneAbilitiesFrom(_targetCard)
		createdCard.cloneGivingAbilitiesFrom(_targetCard)
		var lane = getLaneBySlot(slot)
		var slotIndex = lane.getAllSlots().find(slot)
		createCardOnArenaEvent(_owner, createdCard, lane, slotIndex)
		
		Draw3d.line(_triggerCard.getCard3DUI().global_position, createdCard.getCard3DUI().global_position, Color.WHITE, int(Const.AWAIT_TIME))
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout


# For REPRODUCE, DUPLICATE ability
func createCardOnArenaEvent(_owner: Player, _card: Card, _lane: LaneUIController, _slotIndex: int) -> void:
	CardArtService.prepareCardArt([_card])
	_card.setOwner(_owner)
	_lane.addAt(_owner, [_card], _slotIndex)
	await self.abilityStack.resolveCardAbilityByTrigger([_card], Trigger.ON_ENTER)
	await self.passiveStack.resolveAllPassiveAbilities("Card created") # New card on arena, trigger all passives


# For GIVE_ABILITY ability
func giveAbilityEvent(_triggerCard: Card, _targetCards: Array[Card]) -> void:
	for card in _targetCards:
		Draw3d.line(_triggerCard.getCard3DUI().global_position, card.getCard3DUI().global_position, Color.WHITE, int(Const.AWAIT_TIME))
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	
	for card in _targetCards:
		if _triggerCard.getOwner() != card.getOwner() and isProtected(card, Keyword.GIVE_ABILITY): continue
		var givingAbilities: Array[Ability] = []
		for ability in _triggerCard.getGivingAbilities():
			givingAbilities.append(Ability.new(card, ability.getText(), ability.getKeyword(), ability.getTrigger(), ability.getTarget(), ability.getTargetSlot()))
		card.addAbilities(givingAbilities)
		await self.abilityStack.resolveGivingAbilities(card, givingAbilities)
	await self.passiveStack.resolveAllPassiveAbilities("Card ability given") # Cards has new abilities, trigger all passives


# For TEMP_MANA ability
func modifyPlayerManaEvent(_triggerCard: Card, _player: Player, _mana: int) -> void:
	_player.modifyEffectMaxMana(_mana)
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	await self.passiveStack.resolveAllPassiveAbilities("Mana") # Player get mana, trigger all passives


# For TEMP_MANA ability
func modifyPlayerTempManaEvent(_triggerCard: Card, _player: Player, _tempMana: int) -> void:
	_player.modifyTempMana(_tempMana)
	situationUpdate.emit(_player)
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	await self.passiveStack.resolveAllPassiveAbilities("Temp mana") # Player get temp mana, trigger all passives


# For STRENGTHEN, WEAKEN ability
func modifyCardPermanentPowerEvent(_triggerCard: Card, _targetCards: Array[Card], _modifier: int) -> void:
	var color
	if _modifier >= 0: color = Color.GREEN_YELLOW
	else: color = Color.CRIMSON
	for card in _targetCards:
		if !card.is_ignited: Draw3d.line(_triggerCard.getCard3DUI().global_position, card.getCard3DUI().global_position, color, int(Const.AWAIT_TIME))
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	
	for card in _targetCards:
		#print("weaken")
		#print(card.getOwner())
		if _triggerCard.getOwner() != card.getOwner() and isProtected(card, Keyword.WEAKEN): continue
		if _triggerCard.getOwner() == card.getOwner() and isProtected(card, Keyword.STRENGTHEN): continue
		card.addPermanentPowerModifier3DUI(_modifier)
	await self.passiveStack.resolveAllPassiveAbilities("Card power changed") # Cards permanent power changes, trigger all passives


# For REGEN ability
func regenCardPermanentPowerEvent(_triggerCard: Card, _targetCards: Array[Card]) -> void:
	for card in _targetCards:
		Draw3d.line(_triggerCard.getCard3DUI().global_position, card.getCard3DUI().global_position, Color.GREEN_YELLOW, int(Const.AWAIT_TIME))
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	
	for card in _targetCards:
		if isProtected(card, Keyword.REGEN): continue
		if card.getPermanentPowerModifier() < 0:
			card.addPermanentPowerModifier3DUI(card.getPermanentPowerModifier() * -1)
	await self.passiveStack.resolveAllPassiveAbilities("Card power changed") # Cards permanent power changes, trigger all passives


# For SWAP_OWNER ability
func swapOwnerEvent(_triggerCard: Card, _targetCards: Array[Card]) -> void:
	for card in _targetCards:
		Draw3d.line(_triggerCard.getCard3DUI().global_position, card.getCard3DUI().global_position, Color.WHITE, int(Const.AWAIT_TIME))
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	
	for card in _targetCards:
		if not getLaneByCard(card) or not getLaneByCard(card).isTopCard(card) or isProtected(card, Keyword.SWAP_OWNER): continue
		var newOwner = TurnService.getOpponent(card.getOwner())
		card.setOwner3DUI(newOwner)
		getLaneByCard(card).getCardSlotByCard(card).setOwner(newOwner)
		getLaneByCard(card).rearrangeCard(newOwner)
	await self.passiveStack.resolveAllPassiveAbilities("Card owner changed") # Cards owner changed, trigger all passives

# For SILENT ability
func silentAbilityEvent(_triggerCard: Card, _targetCards: Array[Card]) -> void:
	for card in _targetCards:
		Draw3d.line(_triggerCard.getCard3DUI().global_position, card.getCard3DUI().global_position, Color.CRIMSON, int(Const.AWAIT_TIME))
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	
	for card in _targetCards:
		if isProtected(card, Keyword.SILENT): continue
		card.setSilent3DUI(true)
		
	await self.passiveStack.resolveAllPassiveAbilities("Card power changed") # Cards permanent power changes, trigger all passives

# For UNSUMMONED ability
func unsummonedAbilityEvent(_triggerCard: Card, _targetCards: Array[Card]) -> void:
	#print("unsummonedAbilityEvent")
	#print(_targetCards.size())
	var i = -1
	for card in _targetCards:
		# if isProtected(card, Keyword.SILENT): continue
		# Draw3d.line(_triggerCard.getCard3DUI().global_position, card.getCard3DUI().global_position, Color.CRIMSON, int(Const.AWAIT_TIME))
		# var slot = getLaneByCard(card).getCardSlotByCard(card)
		# slot.removeCards([card])
		# card.getOwner().getHand().add([card])
		# card.getOwner().getGraveyard().add(slot.empty())
		i+=1
		if isProtected(card, Keyword.SILENT): continue
		Draw3d.line(_triggerCard.getCard3DUI().global_position, card.getCard3DUI().global_position, Color.CRIMSON, int(Const.AWAIT_TIME))
		var lane = getLaneByCard(card)
		var unsumedOwner =  card.getOwner()
		var slotIndex = lane.getCardSlotIndexByCard(card)
		var slot = lane._cardSlots[slotIndex]
		# Get the card's 3D UI and move it to movingQueue
		var card3d = card.getCard3DUI()
		var movingQueue
		if card.getOwner().isOnMainSide():
			movingQueue = battleNode.find_child("Player").movingQueue
		else:
			movingQueue = battleNode.find_child("Enemy").movingQueue
			
		# Reparent the card3d to movingQueue before removing from lane
		card3d.reparent(movingQueue)
		
		lane.removeCards([card],true)
		card.getOwner().getHand().add([card])
		#card.getOwner().getGraveyard().add(slot.empty())

	#await get_tree().create_timer(Const.AWAIT_TIME).timeout

	await self.passiveStack.resolveAllPassiveAbilities("Card power changed") # Cards permanent power changes, trigger all passives

func mimicEvent(_triggerCard: Card, _targetCard: Card) -> void:
	Draw3d.line(_triggerCard.getCard3DUI().global_position, _targetCard.getCard3DUI().global_position, Color.WHITE, int(Const.AWAIT_TIME))
	#await get_tree().create_timer(Const.AWAIT_TIME).timeout
	
	if isProtected(_targetCard, Keyword.MIMIC): return
	_triggerCard.removeAbilityByKeyword(Keyword.MIMIC)
	_triggerCard.cloneAbilitiesFrom(_targetCard)
	_triggerCard.setSilent3DUI(false)
	
	await self.passiveStack.resolveAllPassiveAbilities("Card power changed") # Cards permanent power changes, trigger all passives
	await self.abilityStack.resolveCardAbilityByTrigger([_triggerCard], Trigger.ON_ENTER)


##########################################################################
##########################################################################
############################# Getters ####################################
##########################################################################
##########################################################################

# Returns target Slots from the arena
func getTargetSlots(_target: TargetSlot, _triggerCard: Card, _owner: Player, _lane: Lane = null, _specificCards: Dictionary = {}) -> Array[LaneCardSlot]:
	var targetSlots: Array[LaneCardSlot] = []
	# print("getTargetSlots")
	# print(_lane.lane_index)
	match _target.getName():
		#
		TargetSlot.LANE:
			for slotIndex in Const.CARD_PER_LANE:
				if _lane.getSlot(slotIndex).isEmpty():
					targetSlots.append(_lane.getSlot(slotIndex))
		TargetSlot.LANE_ALLY:
			for slotIndex in _lane.getAllowedSlot(_owner):
				if _lane.getSlot(slotIndex).isEmpty():
					targetSlots.append(_lane.getSlot(slotIndex))
		TargetSlot.LANE_ENEMY:
			#print("lane enemy " + str(_lane.lane_index))
			for slotIndex in _lane.getAllowedSlot(TurnService.getOpponent(_owner)):
				if _lane.getSlot(slotIndex).isEmpty():
					targetSlots.append(_lane.getSlot(slotIndex))
					#print("lane enemy slotIndex " + str(slotIndex))
		TargetSlot.OTHER_LANES:
			for lane in self.lanes:
				if lane != _lane:
					for slotIndex in Const.CARD_PER_LANE:
						if lane.getSlot(slotIndex).isEmpty():
							targetSlots.append(lane.getSlot(slotIndex))
		TargetSlot.OTHER_LANES_ALLY:
			for lane in self.lanes:
				if lane != _lane:
					for slotIndex in lane.getAllowedSlot(_owner):
						if lane.getSlot(slotIndex).isEmpty():
							targetSlots.append(lane.getSlot(slotIndex))
		TargetSlot.OTHER_LANES_ENEMY:
			for lane in self.lanes:
				if lane != _lane:
					for slotIndex in lane.getAllowedSlot(TurnService.getOpponent(_owner)):
						if lane.getSlot(slotIndex).isEmpty():
							targetSlots.append(lane.getSlot(slotIndex))
		TargetSlot.ARENA:
			for lane in self.lanes:
				for slotIndex in Const.CARD_PER_LANE:
					if lane.getSlot(slotIndex).isEmpty():
						targetSlots.append(lane.getSlot(slotIndex))
		TargetSlot.ARENA_ALLY:
			for lane in self.lanes:
				for slotIndex in lane.getAllowedSlot(_owner):
					if lane.getSlot(slotIndex).isEmpty():
						targetSlots.append(lane.getSlot(slotIndex))
		TargetSlot.ARENA_ENEMY:
			for lane in self.lanes:
				for slotIndex in lane.getAllowedSlot(TurnService.getOpponent(_owner)):
					if lane.getSlot(slotIndex).isEmpty():
						targetSlots.append(lane.getSlot(slotIndex))
	return TargetSlotCondition.filterTargetSlotCondition(targetSlots, _target)


# Returns target Cards from the arena
func getTargetCards(_target: Target, _triggerCard: Card, _owner: Player, _lane: Lane = null, _specificCards: Dictionary = {}) -> Array[Card]:
	var targetCards: Array[Card] = []
	match _target.getName():
		Target.SELF:
			targetCards.append(_triggerCard)
		Target.HOST:
			if _lane and _lane.getCardSlotByCard(_triggerCard):
				targetCards.append(_lane.getCardSlotByCard(_triggerCard).getCardAt(0))
		Target.YOUR_HAND:
			targetCards = _triggerCard.getOwner().getHand().getCards()
		Target.OPPO_HAND:
			targetCards = TurnService.getOpponent(_triggerCard.getOwner()).getHand().getCards()
		Target.HANDS:
			targetCards = getAllHandCards()
		Target.YOUR_GRAVEYARD:
			targetCards = _triggerCard.getOwner().getGraveyard().getCards()
		Target.OPPO_GRAVEYARD:
			targetCards = TurnService.getOpponent(_triggerCard.getOwner()).getGraveyard().getCards()
		Target.GRAVEYARDS:
			targetCards = getAllGraveyardCards()
		Target.LANE_ALLY:
			if _lane:
				targetCards = _lane.getTopCardsByPlayer(_owner)
				targetCards.erase(_triggerCard)
		Target.LANE_ENEMY:
			if _lane:
				targetCards = _lane.getTopCardsByPlayer(TurnService.getOpponent(_owner))
				targetCards.erase(_triggerCard)
		Target.LANE:
			if _lane:
				targetCards = _lane.getTopCards()
				targetCards.erase(_triggerCard)
		Target.ARENA_ALLY:
			targetCards = getArenaCardsByOwner(_owner)
			targetCards.erase(_triggerCard)
		Target.ARENA_ENEMY:
			targetCards = getArenaCardsByOwner(TurnService.getOpponent(_owner))
			targetCards.erase(_triggerCard)
		Target.ARENA:
			targetCards = getAllArenaCards()
			targetCards.erase(_triggerCard)
		Target.SOURCE:
			if _specificCards.has("sourceCard"):
				targetCards.append(_specificCards["sourceCard"])
		Target.OTHER_LANES_ALLY_LOC:
			for lane: LaneUIController in self.lanes:
				if lane != _lane and lane.getLocationByPlayer(_owner):
					targetCards.append(lane.getLocationByPlayer(_owner))
		Target.SACRIFICED_CARD:
			if _specificCards.has("sacrificedCard"):
				targetCards.append(_specificCards["sacrificedCard"])
		Target.BOUNCED_CARD:
			if _specificCards.has("bouncedCard"):
				targetCards.append(_specificCards["bouncedCard"])
	return TargetCondition.filterTargetCardCondition(targetCards, _target)


func isCastable(_card: Card, _laneIndex: int, _slotIndex: int, _player: Player, alert: bool = false) -> bool:
	if _player.getManaAndTempMana() < _card.getCost(): # If card cost > current mana
		if alert: alertMessage.emit("Insufficient mana")
		return false
	elif getLane(_laneIndex).getSlot(_slotIndex).getState() == LaneCardSlot.DISABLED:
		if alert: alertMessage.emit("Slot disabled")
		return false
	
	match _card.getCastCondition():
		CastCondition.NORMAL:
			if getLane(_laneIndex).getSlotCardCount(_slotIndex) > 0: # If cast on occupied slot
				if alert: alertMessage.emit("Slot occupied")
				return false
			elif not getLane(_laneIndex).getAllowedSlot(_player).has(_slotIndex): # If cast on opponent's exclusive slot
				if alert: alertMessage.emit("Can't cast on opponent's slot")
				return false
			else: pass
		CastCondition.SYMBIONT:
			if getLane(_laneIndex).getSlotCardCount(_slotIndex) <= 0: # If cast on empty slot
				if alert: alertMessage.emit("Need to symbiote ally")
				return false
			elif not getLane(_laneIndex).getSlotOwner(_slotIndex) == _player: # If try to symbiont enemy
				if alert: alertMessage.emit("Symbiote ally only")
				return false
			#elif getLane(_laneIndex).getSlotCardCount(_slotIndex) > 1: # If cast on symbioted card
				#if alert: alertMessage.emit("This ally is already symbioted")
				#return false
			else: pass
		CastCondition.SACRIFICE:
			if getLane(_laneIndex).getSlotCardCount(_slotIndex) <= 0: # If cast on empty slot
				if alert: alertMessage.emit("Need to sacrifice ally")
				return false
			elif not getLane(_laneIndex).getSlotOwner(_slotIndex) == _player: # If try to sacrifice enemy
				if alert: alertMessage.emit("Sacrifice ally only")
				return false
			else: pass
		CastCondition.BOUNCE:
			if getLane(_laneIndex).getSlotCardCount(_slotIndex) <= 0: # If cast on empty slot
				if alert: alertMessage.emit("Need to bounce ally")
				return false
			elif not getLane(_laneIndex).getSlotOwner(_slotIndex) == _player: # If try to bounce enemy
				if alert: alertMessage.emit("Bounce ally only")
				return false
			else: pass
	return true


func isProtected(_targetCard: Card, _affectingKeyword: int) -> bool:
	var isProtected: bool = false
	
	#!!!! LOGIC SEEMS ODD NEED REVIEW (isProtected = true) !!!!
	
	match _affectingKeyword:
		Keyword.DESTROY:
			if _targetCard.hasAbilityKeyword(Keyword.SHIELD): # Highest Priority to pop
				_targetCard.removeAbilityByKeyword(Keyword.SHIELD)
				isProtected = true
			elif _targetCard.hasAbilityKeyword(Keyword.INDESTRUCTIBLE):
				isProtected = true
		Keyword.WEAKEN:
			if _targetCard.hasAbilityKeyword(Keyword.SHIELD):
				_targetCard.removeAbilityByKeyword(Keyword.SHIELD)
				isProtected = true
		Keyword.GIVE_ABILITY:
			if _targetCard.hasAbilityKeyword(Keyword.SHIELD):
				_targetCard.removeAbilityByKeyword(Keyword.SHIELD)
				isProtected = true
		Keyword.SILENT:
			if _targetCard.hasAbilityKeyword(Keyword.SHIELD):
				_targetCard.removeAbilityByKeyword(Keyword.SHIELD)
				isProtected = true
	return isProtected


# returns all cards in the arena
func getAllArenaCards() -> Array[Card]:
	var allCards: Array[Card] = []
	for lane: LaneUIController in self.lanes:
		allCards.append_array(lane.getTopCards())
	return allCards


# returns all cards owned by a mainPlayer in the arena
func getArenaCardsByOwner(_owner: Player) -> Array[Card]:
	var allCards: Array[Card] = []
	for lane: LaneUIController in self.lanes:
		allCards.append_array(lane.getTopCardsByPlayer(_owner))
	return allCards


func getAllHandCards() -> Array[Card]:
	var allCards: Array[Card] = []
	allCards.append_array(self.mainPlayer.getHand().getCards())
	allCards.append_array(self.oppoPlayer.getHand().getCards())
	return allCards


func getAllGraveyardCards() -> Array[Card]:
	var allCards: Array[Card] = []
	allCards.append_array(self.mainPlayer.getGraveyard().getCards())
	allCards.append_array(self.oppoPlayer.getGraveyard().getCards())
	return allCards


# returns a selected lane object
func getLane(_laneIndex: int) -> LaneUIController:
	return self.lanes[_laneIndex]


# returns a lane which has that slot
func getLaneBySlot(_slot: LaneCardSlot) -> LaneUIController:
	return self.lanes[_slot.lane_index]


# returns a lane which has that card
func getLaneByCard(_card: Card) -> LaneUIController:
	for lane: LaneUIController in lanes:
		if lane.getAllCards().has(_card) or lane.getLocationByPlayer(self.mainPlayer) == _card or lane.getLocationByPlayer(self.oppoPlayer) == _card:
			return lane
	return null


#get active mainPlayer card at index
func getCardByHandIndex(_index: int) -> Card:
	return TurnService.ActivePlayer.getHand().getCardAt(_index)


func getArenaWinner() -> Array[int]:
	var winner:Array[int] = [-1, -1, -1] # Snap[0] QB[1] Sum[2]
	var greenScore:Array[int] = [0, 0, 0]
	var redScore:Array[int] = [0, 0, 0]
	var snapTieBreak:int = 0
	
	for lane: LaneUIController in self.lanes:
		greenScore[2] += lane.getPlayerTotalPower(mainPlayer)
		redScore[2] += lane.getPlayerTotalPower(oppoPlayer)
		snapTieBreak += lane.getPlayerTotalPower(mainPlayer) - lane.getPlayerTotalPower(oppoPlayer)
		
		if lane.isPlayerWinning(mainPlayer):
			greenScore[0] += 1
			greenScore[1] += lane.getPlayerTotalPower(mainPlayer)
		elif lane.isPlayerWinning(oppoPlayer):
			redScore[0] += 1
			redScore[1] += lane.getPlayerTotalPower(oppoPlayer)
		else: pass
	
	for i in winner.size():
		if greenScore[i] > redScore[i]: winner[i] = 0
		elif greenScore[i] < redScore[i]: winner[i] = 1
		else:
			if snapTieBreak > 0: winner[i] = 0
			elif snapTieBreak < 0: winner[i] = 1
			else: winner[i] = 2
	return winner

##########################################################################
##########################################################################
############################ Listeners ###################################
##########################################################################
##########################################################################

func onManaChanged(_player: Player) -> void:
	playerManaChanged.emit(_player)
	situationUpdate.emit(_player)


func onHandSelectedIndex(_index: int, _player: Player) -> void:
	if TurnService.ActivePlayer == _player:
		handSelectedIndex = _index
		#_player.getHand().highlightSelectedCard(_index)


func onLaneSlotSelected(_slotIndex: int, _laneIndex: int) -> void:
	if self.handSelectedIndex != -1:
		if await cardCastEvent(getCardByHandIndex(self.handSelectedIndex), _laneIndex, _slotIndex):
			self.handSelectedIndex = -1


func onGlobalEffectUpdated():
	for lane: LaneUIController in self.lanes:
		lane.playerPower.text = str(lane.getPlayerTotalPower(mainPlayer))
		lane.enemyPower.text = str(lane.getPlayerTotalPower(oppoPlayer))
		if lane.isPlayerWinning(mainPlayer):
			lane.playerPowerW.visible = true
			lane.enemyPowerW.visible = false
		elif lane.isPlayerWinning(oppoPlayer):
			lane.playerPowerW.visible = false
			lane.enemyPowerW.visible = true
		else:
			lane.playerPowerW.visible = false
			lane.enemyPowerW.visible = false

func lightCastableCard(player: Player):
	#if !get_parent().find_child("EndButtonY").disabled:
		#TurnService.ActivePlayer.setHandBrightness(false)
	if turnEnding:
		mainPlayer.setHandBrightness(false)
		return
	if player == mainPlayer and isCasting:
		TurnService.ActivePlayer.setHandBrightness(false)
		await changeEndTurnButton
	if player.get_name() != TurnService.ActivePlayer.get_name() or player != mainPlayer:
		return
	var noCastable = true
	var cardIndex = 0
	castableSlot = []
	for card in player.hand.get_children():
	# for card in player.getCardCollection().getCards():
		var laneIndex = 0
		var castable = false
		for lane in get_tree().get_nodes_in_group("lane"):
			var slotIndex = 0
			for slot in lane.getAllSlots():
				if isCastable(getCardByHandIndex(cardIndex), laneIndex, slotIndex, player):
					noCastable = false
					castable = true
					var castableCard = []
					castableCard.append(cardIndex)
					castableCard.append(laneIndex)
					castableCard.append(slotIndex)
					castableSlot.append(castableCard)
					#if slotIndex >= 4 and getCardByHandIndex(cardIndex).castCondition == CastCondition.NORMAL:
						#
				#else
					#
				slotIndex += 1
			laneIndex += 1
		if TurnService.ActivePlayer == mainPlayer:
			card.brightness_up(castable)
		cardIndex += 1
	if TurnService.ActivePlayer.isOnMainSide() and noCastable and TurnService.TurnCount > 0:
		#get_parent().find_child("EndButton2").visible = false
		#changeEndTurnButton.emit(false)
		get_parent().find_child("EndButtonG").visible = true
	else:
		#get_parent().find_child("EndButton2").visible = true
		#changeEndTurnButton.emit(true)
		get_parent().find_child("EndButtonG").visible = false
		#print("===========================================================")
	#print(castableSlot)
	#print("done")

func lightCastableSlot(castable: bool, still_dragging: bool):
	#if !castable:
		#return
	if isCasting:
		return
	for slot in castableSlot:
		var lane = get_tree().get_nodes_in_group("lane")
		if still_dragging and castable:
			if slot[0] == handSelectedIndex:
				if mainPlayer.getHand().get_child(handSelectedIndex).castConLabel.text == "SACRIFICE":
					lane[slot[1]].set_slot_state(slot[2], lane[slot[1]].get_child(13).get_child(slot[2]).get_child(0), LaneCardSlot.SACRIFIABLE)
				elif mainPlayer.getHand().get_child(handSelectedIndex).castConLabel.text == "SYMBIONT":
					lane[slot[1]].set_slot_state(slot[2], lane[slot[1]].get_child(13).get_child(slot[2]).get_child(0), LaneCardSlot.SYMBIONTABLE)
				elif mainPlayer.getHand().get_child(handSelectedIndex).castConLabel.text == "BOUNCE":
					lane[slot[1]].set_slot_state(slot[2], lane[slot[1]].get_child(13).get_child(slot[2]).get_child(0), LaneCardSlot.BOUNCEABLE)
				else:
					if slot[2] >= 4:
						lane[slot[1]].set_slot_state(slot[2], lane[slot[1]].get_child(13).get_child(slot[2]).get_child(0), LaneCardSlot.CASTABLE_PLAYER_SIDE)
					else:
						lane[slot[1]].set_slot_state(slot[2], lane[slot[1]].get_child(13).get_child(slot[2]).get_child(0), LaneCardSlot.CASTABLE)
					#if slot[2] >3:
						#lane[slot[1]].set_slot_state(lane[slot[1]].get_child(13).get_child(2).get_child(0), "default")
						#lane[slot[1]].set_slot_state(lane[slot[1]].get_child(13).get_child(3).get_child(0), "default")
		else:
			if slot[2] >= 4:
				lane[slot[1]].set_slot_state(slot[2], lane[slot[1]].get_child(13).get_child(slot[2]).get_child(0), LaneCardSlot.IDLE_PLAYER_SIDE)
			elif slot[2] <= 1:
				lane[slot[1]].set_slot_state(slot[2], lane[slot[1]].get_child(13).get_child(slot[2]).get_child(0), LaneCardSlot.IDLE_ENEMY_SIDE)
			else:
				lane[slot[1]].set_slot_state(slot[2], lane[slot[1]].get_child(13).get_child(slot[2]).get_child(0), LaneCardSlot.IDLE)

func set_dragging(on: bool):
	is_dragging = on

func dragonDialog() -> void:
	if ScoreService.dragonLose == 0 and ScoreService.dragonWin == 0: # Very first time
		opponentDialog.emit("Mortals! How dare you challenge me?")
	elif ScoreService.dragonLose > 0 and ScoreService.dragonWin - ScoreService.dragonLose < 3: # As equals
		match randi() % 4:
			0: opponentDialog.emit("A worthy opponent. We meet again.")
			1: opponentDialog.emit("You stand as my equal. Mortals!")
			2: opponentDialog.emit("You challenge me with honor. I respect that.")
			3: opponentDialog.emit("Let our clash be legendary. My friend.")
	elif ScoreService.dragonLose > 0 and ScoreService.dragonWin - ScoreService.dragonLose < -1: # You win more
		match randi() % 4:
			0: opponentDialog.emit("You've proven yourself worthy.")
			1: opponentDialog.emit("Your strength is unmatched, even to me.")
			2: opponentDialog.emit("I cannot deny the power you possess.")
			3: opponentDialog.emit("Your might surpasses even the divine.")
	else: # You lose more or never won
		match randi() % 7:
			0: opponentDialog.emit("You weaklings are no match for me!")
			1: opponentDialog.emit("You are but a bug beneath my feet.")
			2: opponentDialog.emit("Pitiful mortal, you dare defy a god?")
			3: opponentDialog.emit("Your bravery is misguided and foolish.")
			4: opponentDialog.emit("You are nothing more than a dust.")
			5: opponentDialog.emit("Your end is inevitable. Mortals!")
			6: opponentDialog.emit("You stand no chance against me!")
	await get_tree().create_timer(Const.DIALOG_AWAIT_TIME).timeout
