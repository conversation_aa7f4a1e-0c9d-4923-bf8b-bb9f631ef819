class_name Deck
extends Card<PERSON>ollection<PERSON><PERSON><PERSON>roller


# Called when the node enters the scene tree for the first time.
func _ready():
	super()

func setOwner(player: Player):
	super.setOwner(player)

func add(p_cards:Array[Card]) -> void:
	cardCollection.add(p_cards)
	for card in p_cards:
		var card3d = card_3dui.instantiate()
		card3d.displayCard(card)
		add_child(card3d)
		card3d.set_flipup(false)
		var movingQueue
		if getOwner().isOnMainSide():
			movingQueue = battleNode.find_child("Player").movingQueue
		else:
			movingQueue = battleNode.find_child("Enemy").movingQueue
		if movingQueue.get_child_count() > 0:
			card3d.transform.origin = movingQueue.get_child(0).global_position
			movingQueue.get_child(0).free()
		card3d.cardSelected.connect(battleNode.inspect_card)
		battleNode.inspecting.connect(card3d.change_collision_mode)
	stackCard()

func addToTop(card:Card) -> void:
	self.cardCollection.addSingle(card)
	var card3d = card_3dui.instantiate()
	card3d.displayCard(card)
	add_child(card3d)
	card3d.set_flipup(false)
	var movingQueue
	if getOwner().isOnMainSide():
		movingQueue = battleNode.find_child("Player").movingQueue
	else:
		movingQueue = battleNode.find_child("Enemy").movingQueue
	if movingQueue.get_child_count() > 0:
		card3d.transform.origin = movingQueue.get_child(0).global_position
		movingQueue.get_child(0).free()
	#if movingQueue.get_child_count() > 0:
		#card3d.transform.origin = movingQueue.get_child(0).global_position
		#movingQueue.get_child(0).queue_free()
	card3d.cardSelected.connect(battleNode.inspect_card)
	battleNode.inspecting.connect(card3d.change_collision_mode)
	stackCard()

func pull(amount:int) -> Array[Card]:
	for i in amount:
		if not self.cardCollection.isEmpty():
			var movingQueue
			if getOwner().isOnMainSide():
				movingQueue = battleNode.find_child("Player").movingQueue
			else:
				movingQueue = battleNode.find_child("Enemy").movingQueue
				#if get_child_count()>0:
			if get_child(0):
				get_child(0).reparent(movingQueue)
				#remove_child(get_child(0))
	stackCard()
	return self.cardCollection.pull(amount)
	#return pulledCards

func stackCard() -> void:
	#battleNode.deckListPanel.update_deck_list()
	var positionY = get_child_count() * -0.05
	var camera = get_viewport().get_camera_3d()
	var destination := global_transform
	for card in get_children():
		card.set_layer(20, false)
		#card.rotation.x = deg_to_rad(-90)
		#card.rotation.y = deg_to_rad(180)
		#card.target_transform.origin = global_transform.origin
		destination.basis = camera.global_transform.basis
		#destination.origin += camera.basis * Vector3.UP * 0.1
		destination.origin += camera.basis * Vector3.FORWARD * 0.03
		#destination.origin += camera.basis * Vector3.LEFT * 1
		card.target_transform.origin = destination.origin
		card.target_rotation = deg_to_rad(randf_range(-2, 2))
		#card.target_transform.basis = destination.basis.rotated(Vector3.RIGHT, deg_to_rad())
		#card.target_transform.basis.rotated(Vector3.RIGHT, deg_to_rad(180))
		#card.target_transform.origin.y = positionY
		positionY += 0.05
		
		#card.target_transform = card.global_transform
		#card.target_transform.basis = card.transform.basis
