[gd_scene load_steps=12 format=3 uid="uid://bx7vcpvucpns1"]

[ext_resource type="Shader" path="res://blur.gdshader" id="1_o7uxq"]
[ext_resource type="Script" path="res://scripts/ui/howtoplay.gd" id="1_t6kpx"]
[ext_resource type="Texture2D" uid="uid://ds1tkna4qcc1g" path="res://Assets/HowToPlay/CardSample.png" id="3_kp5mt"]
[ext_resource type="FontFile" uid="uid://mrhagcdlhgwc" path="res://Assets/Fonts/Cabin-VariableFont_wdth,wght.ttf" id="4_bf748"]
[ext_resource type="FontFile" uid="uid://c2e0i2vp8rnp6" path="res://Assets/CardPics/CabinCondensed-Regular.ttf" id="5_qg5fo"]
[ext_resource type="Texture2D" uid="uid://ygu05dlamj55" path="res://Assets/HowToPlay/LaneSample.png" id="6_v1r8r"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_at3wy"]
shader = ExtResource("1_o7uxq")
shader_parameter/blur = 1.0
shader_parameter/brightness = 0.065

[sub_resource type="Gradient" id="Gradient_plcl6"]
offsets = PackedFloat32Array(1)
colors = PackedColorArray(0, 0, 0, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_2khdy"]
gradient = SubResource("Gradient_plcl6")

[sub_resource type="CanvasTexture" id="CanvasTexture_vi0ko"]
diffuse_texture = SubResource("GradientTexture1D_2khdy")

[sub_resource type="RectangleShape2D" id="RectangleShape2D_oaxk8"]
size = Vector2(1920, 1080)

[node name="HowtoPlay" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_t6kpx")

[node name="InspectBg" type="TextureRect" parent="."]
modulate = Color(1, 1, 1, 0.803922)
z_index = 5
material = SubResource("ShaderMaterial_at3wy")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = SubResource("CanvasTexture_vi0ko")

[node name="Area2D" type="Area2D" parent="InspectBg"]
visible = false

[node name="CollisionShape2D" type="CollisionShape2D" parent="InspectBg/Area2D"]
position = Vector2(962, 538)
shape = SubResource("RectangleShape2D_oaxk8")

[node name="Card" type="Control" parent="."]
z_index = 5
anchors_preset = 0
offset_left = 75.0
offset_top = 48.0
offset_right = 115.0
offset_bottom = 88.0

[node name="CardSample" type="Sprite2D" parent="Card"]
position = Vector2(345, 407)
texture = ExtResource("3_kp5mt")

[node name="Card" type="Label" parent="Card"]
layout_mode = 0
offset_left = 275.0
offset_top = 118.0
offset_right = 410.0
offset_bottom = 162.0
theme_override_fonts/font = ExtResource("4_bf748")
theme_override_font_sizes/font_size = 36
text = "The Card"

[node name="HSeparator" type="HSeparator" parent="Card/Card"]
layout_mode = 0
offset_left = -100.0
offset_top = 169.0
offset_right = -27.0
offset_bottom = 173.0

[node name="HSeparator3" type="HSeparator" parent="Card/Card"]
layout_mode = 0
offset_left = -113.0
offset_top = 548.0
offset_right = 239.0
offset_bottom = 552.0

[node name="HSeparator2" type="HSeparator" parent="Card/Card"]
layout_mode = 0
offset_left = -100.0
offset_top = 400.0
offset_right = 49.0
offset_bottom = 404.0

[node name="Label3" type="Label" parent="Card/Card"]
layout_mode = 1
offset_left = 127.0
offset_top = 61.0
offset_right = 219.0
offset_bottom = 93.0
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
text = "Power"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Label2" type="Label" parent="Card/Card"]
layout_mode = 0
offset_left = -81.0
offset_top = 61.0
offset_right = 11.0
offset_bottom = 93.0
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
text = "Mana Cost"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Label6" type="Label" parent="Card/Card"]
layout_mode = 0
offset_left = -60.0
offset_top = 489.0
offset_right = 192.0
offset_bottom = 521.0
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
text = "Card abilities displayed here"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Label7" type="Label" parent="Card/Card"]
layout_mode = 0
offset_left = -64.0
offset_top = 573.0
offset_right = 188.0
offset_bottom = 669.0
theme_override_colors/font_color = Color(0.72549, 0.72549, 0.72549, 1)
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
text = "Card owner keywords
Your card = “Ally”
Opponent card = “Enemy”"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Label4" type="Label" parent="Card/Card"]
layout_mode = 0
offset_left = -179.0
offset_top = 153.0
offset_right = -83.0
offset_bottom = 216.0
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
text = "Cast
Condition"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Label5" type="Label" parent="Card/Card"]
layout_mode = 0
offset_left = -164.0
offset_top = 384.0
offset_right = -111.0
offset_bottom = 414.0
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
text = "Rarity
"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Lane" type="Control" parent="."]
z_index = 5
anchors_preset = 0
offset_left = 603.0
offset_top = 48.0
offset_right = 643.0
offset_bottom = 88.0

[node name="Card" type="Label" parent="Lane"]
layout_mode = 0
offset_left = 275.0
offset_top = 118.0
offset_right = 410.0
offset_bottom = 162.0
theme_override_fonts/font = ExtResource("4_bf748")
theme_override_font_sizes/font_size = 36
text = "The Lane"

[node name="HSeparator3" type="HSeparator" parent="Lane/Card"]
layout_mode = 0
offset_left = -113.0
offset_top = 548.0
offset_right = 239.0
offset_bottom = 552.0

[node name="HSeparator" type="HSeparator" parent="Lane/Card"]
layout_mode = 0
offset_left = 87.0
offset_top = 99.0
offset_right = 110.0
offset_bottom = 103.0

[node name="HSeparator2" type="HSeparator" parent="Lane/Card"]
layout_mode = 0
offset_left = 87.0
offset_top = 492.0
offset_right = 110.0
offset_bottom = 496.0

[node name="Label3" type="Label" parent="Lane/Card"]
layout_mode = 1
offset_left = 116.0
offset_top = 85.0
offset_right = 326.0
offset_bottom = 117.0
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
text = "Opponent's total power"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Label4" type="Label" parent="Lane/Card"]
layout_mode = 1
offset_left = 116.0
offset_top = 477.0
offset_right = 267.0
offset_bottom = 509.0
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
text = "Your total power"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Label7" type="Label" parent="Lane/Card"]
layout_mode = 0
offset_left = -142.0
offset_top = 573.0
offset_right = 265.0
offset_bottom = 669.0
theme_override_colors/font_color = Color(0.724643, 0.724643, 0.724643, 1)
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
text = "You may cast your cards on Neutral Slot
only if all Ally Slots in that lane are occupied.
You can't cast cards on Enemy Slot."
horizontal_alignment = 1
vertical_alignment = 1

[node name="LaneSample" type="Sprite2D" parent="Lane"]
position = Vector2(339, 416)
texture = ExtResource("6_v1r8r")

[node name="Game" type="Control" parent="."]
z_index = 5
anchors_preset = 0
offset_left = 1194.0
offset_top = 48.0
offset_right = 1234.0
offset_bottom = 88.0

[node name="Card" type="Label" parent="Game"]
layout_mode = 0
offset_left = 278.0
offset_top = 118.0
offset_right = 434.0
offset_bottom = 162.0
theme_override_fonts/font = ExtResource("4_bf748")
theme_override_font_sizes/font_size = 36
text = "The Game"

[node name="HSeparator3" type="HSeparator" parent="Game/Card"]
layout_mode = 0
offset_left = -101.0
offset_top = 548.0
offset_right = 251.0
offset_bottom = 552.0

[node name="Label7" type="Label" parent="Game/Card"]
layout_mode = 0
offset_left = -142.0
offset_top = 573.0
offset_right = 265.0
offset_bottom = 669.0
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
horizontal_alignment = 1
vertical_alignment = 1

[node name="Label8" type="Label" parent="Game/Card"]
layout_mode = 0
offset_left = -130.0
offset_top = 88.0
offset_right = 277.0
offset_bottom = 514.0
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
text = "On your turn - Cast cards from your hand
as long as you have enough mana.

When you run out of mana, or there's no
available slots for you, or your card's
 cast condition can't be met. You must \"pass\".

If you pass without casting any cards,
you're considered \"skip\" your turn.

The game is over when both players
skip their turns consecutively. 
Whoever wins more lanes, win the game.
"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Label7" type="Label" parent="Game/Card/Label8"]
layout_mode = 0
offset_left = 3.0
offset_top = 485.0
offset_right = 410.0
offset_bottom = 581.0
theme_override_colors/font_color = Color(0.724643, 0.724643, 0.724643, 1)
theme_override_fonts/font = ExtResource("5_qg5fo")
theme_override_font_sizes/font_size = 24
text = "Your maximum mana each turn starts at 1 
and cap at 6 unless there's additional effects
from cards or events."
horizontal_alignment = 1
vertical_alignment = 1

[node name="Close" type="Button" parent="."]
z_index = 5
layout_mode = 0
offset_left = 1828.0
offset_top = 18.0
offset_right = 1895.0
offset_bottom = 51.0
text = "Close"

[connection signal="pressed" from="Close" to="." method="_on_close_pressed"]
