class_name Card_3DUI
extends Node3D

@export var height_curve: Curve

@onready var displayComponent = [$CardFront, $CardBack, $NameText, $NameBg, $AbilityTextFloat, $KeywordNotes,\
	$CardText, $CastConditionText, $CostText, $CostBg, $PowerText,\
	$PowerBg, $ColorBar, $RarityBar, $FloatText, $MostRecent,\
	$CastableVFX, $IgniteParticle, $DivineParticle, $DarkParticle, $CardBg, $CardPicGradient]
@onready var animationPlayer: AnimationPlayer = $AnimationPlayer
@onready var cardArt = $CardFront
@onready var cardBg: MeshInstance3D = $CardBg
#@onready var header: MeshInstance3D = $Header
@onready var abilityOnCard = $AbilityOnCard/AbilitySubViewport/AbilityContainer/AbilityTextOnCard/AbilityDetail
@onready var abilityDisplay = $CardText
@onready var abilityDetailEng: RichTextLabel = $AbilityOnCard/AbilitySubViewport/AbilityContainer/AbilityTextOnCard/AbilityDetailENG
@onready var cardPicGradient: MeshInstance3D = $CardPicGradient
@onready var cardBack = $CardBack
@onready var cardName = $NameText
@onready var nameLabel: RichTextLabel = $NameOnCard/NameSubViewport/NameText2D
@onready var nameBg = $NameBg
@onready var abilityFloat = $AbilityTextFloat
#@onready var abilityDescription = $AbilityDescription
@onready var keywordNotes = $KeywordNotes
@onready var castConContainer: HBoxContainer = $AbilityOnCard/AbilitySubViewport/AbilityContainer/CastConContainer
@onready var castConLabel = $AbilityOnCard/AbilitySubViewport/AbilityContainer/CastConContainer/CastConBanner/CastConFG/CastConText
@onready var costDisplay: MeshInstance3D = $CostText
@onready var costLabel = $CostOnCard/CostSubViewport/CostText2D
@onready var costBg = $CostBg
@onready var powerDisplay: MeshInstance3D = $PowerText
@onready var powerLabel = $PowerOnCard/PowerSubViewport/PowerText2D
@onready var powerLabelArena = $PowerOnCard/PowerSubViewport/PowerText2D
@onready var powerBg = $PowerBg
@onready var powerParticle = $PowerParticle

@onready var colorBar = $ColorBar
@onready var rarityBar = $RarityBar
@onready var floatLabel = $FloatText
@onready var anim = $FloatText/AnimationPlayer
@onready var collision = $CollisionShape3D
@onready var collisionHand = $CollisionHand
@onready var currentTurn = $Current
@onready var cardBorder = $MostRecent
@onready var castableVfx = $CastableVFX
@onready var divineParticle = $DivineParticle
@onready var darkParticle = $DarkParticle
@onready var igniteParticle = $IgniteParticle
@onready var playerParticle = $PlayerParticle
@onready var enemyParticle = $EnemyParticle
@onready var siliencedVfx: TextureRect = $AbilityOnCard/AbilitySubViewport/AbilityContainer/AbilityTextOnCard/SiliencedVFX
@onready var target_transform := global_transform
@onready var cache_transform = []
@onready var onArena: Node3D = $OnArena
#@onready var arenaNode = get_tree().get_root().get_node("Arena") 
@onready var battleNode = get_tree().get_root().get_node("Battle")


enum states{
	IDLE,
	HOVER,
	PRESS,
	INSPECT,
	DRAG,
	PLAYING,
	HOVERINLANE,
	ANIMATION
}

var cardOwner
var currentState = states.IDLE
var isOnHand:bool = false
var card_order
var target_rotation := 0.0
var brightness_on = true
var art_color
var cost_color
var cost_bg_color
var castConLabel_color
var power_color
var power_bg_color
var colorBar_color
var ability_color
var rarity_color
var name_color
var name_bg_color
var name_outline_color
var border_color
var enlarge_transform

signal cardSelected(card:Card_3DUI)

func _ready():
	art_color = cardArt.material_override.albedo_color
	cost_color = costLabel.modulate
	cost_bg_color = costBg.material_override.albedo_color
	castConLabel_color = castConLabel.modulate
	power_color = powerLabel.modulate
	power_bg_color = powerBg.material_override.albedo_color
	colorBar_color = colorBar.material_override.albedo_color
	ability_color = abilityFloat.modulate
	rarity_color = rarityBar.material_override.albedo_color
	#name_color = cardName.modulate
	#name_bg_color = nameBg.material_override.albedo_color
	#name_outline_color = cardName.outline_modulate
	border_color = cardBorder.material_override.albedo_color
	#if !brightness_on:
		#power_color = powerLabel.modulate + Color.from_hsv(0,0,0.7,0)
		#cost_color = costLabel.modulate + Color.from_hsv(0,0,0.7,0)
		#rarity_color = rarityBar.modulate + Color.from_hsv(0,0,0.25,0)
		#border_color = cardBorder.material_override.albedo_color + Color.from_hsv(0,0,0.7,0)
	
	#var i = 0
	overwrite_cache_transform()
		#i += 1
	#art_dim_color = art_color - Color.from_hsv(0,0,0.5,0)
	#cost_dim_color = cost_color - Color.from_hsv(0,0,0.4,0)
	#cost_bg_dim_color = cost_bg_color - Color.from_hsv(0,0,0.4,0)
	#power_dim_color = power_color - Color.from_hsv(0,0,0.2,0)
	#power_bg_dim_color = power_bg_color - Color.from_hsv(0,0,0.2,0)

func find_camera_pos() -> Vector3:
	var camera = get_viewport().get_camera_3d()
	var unprojected = camera.unproject_position(target_transform.origin)
	#print(get_viewport().size)
	# I fiddled with the y coordinate and distance here so the full card is visible
	return camera.project_position(Vector2(unprojected.x-350, battleNode.panelContainer.size.y * 0.9), 0)

func _physics_process(delta):
	
	if get_parent() is Deck:
		collision.disabled = true
		collisionHand.disabled = true
	
	
	if currentState == states.IDLE:
		#scale = Vector3.ONE
		#cardArt.transform.origin.z = target_transform.origin.z
		#if battleNode.find_child("Arena").is_dragging:
			#battleNode.find_child("Arena").dragging.emit(false)
		
		var i = 0
		for component in displayComponent:
			component.transform = component.transform.interpolate_with(cache_transform[i], Const.ANIM_SPEED * delta)
			i += 1
		
		if get_parent() is  LaneCardSlot:
			transform = transform.interpolate_with(target_transform, Const.ANIM_SPEED * delta)
		else:
			#enlarge_transform = target_transform
			#enlarge_transform.scale = target_transform.scale
			transform = transform.interpolate_with(target_transform.scaled(Vector3.ONE*2.5), Const.ANIM_SPEED * delta)
			
		rotation.z = lerp(rotation.z, target_rotation, Const.ANIM_SPEED * 1.5 * delta)
		#transform.origin.z = height_curve.sample(global_transform.origin.x)
		#rotation.x += deg_to_rad(10)
		#if isOnHand:
			#rotation = Vector3(deg_to_rad(-45),deg_to_rad(-45) ,deg_to_rad(-45))
		#rotation.y = deg_to_rad(90)
		#if get_parent() is LaneCardSlot:
			#if 
			#
		
	#elif currentState == states.ANIMATION:
		##if isOnHand:
			##print(global_transform.origin.y)# = 15
		##global_transform.origin.z = 5
		#transform = transform.interpolate_with(target_transform.scaled(Vector3.ONE*2.5), 0.1 * delta)
		#if target_transform.scaled(Vector3.ONE*2.5).origin.distance_to(global_transform.scaled(Vector3.ONE*2.5).origin) < 50:
			#currentState = states.IDLE
	elif currentState == states.HOVER and !battleNode.find_child("Arena").is_dragging and isOnHand and get_parent().is_player:
		#cardArt.rotation.z = lerp(rotation.z, 0.0, Const.ANIM_SPEED * delta)
		#var view_spot = target_transform
		#view_spot.origin = find_camera_pos()
		#target_transform.basis.y += Vector3.UP *20
		#rotation = Vector3(-90.0,0.0,0.0)
		#view_spot.basis = view_spot.basis.rotated(Vector3.LEFT, deg_to_rad(80))
		#transform = transform.interpolate_with(target_transform.translated(Vector3(0, 0,1)), Const.ANIM_SPEED * delta)
		#unproject_position(target_transform.origin)
		#if get_parent().is_player:
			#cardArt.scale = Vector3.ONE * 1.2
		#else:
			#scale = Vector3.ONE * 0.8
		if get_parent().is_player:
			var i = 0
			for component in displayComponent:
				#component.global_transform.basis = target_transform.basis
				component.global_transform = component.global_transform.interpolate_with(target_transform*cache_transform[i].rotated(Vector3(0,0,1), target_rotation).scaled(Vector3.ONE*2.5).translated(Vector3(target_transform.origin.x-3,-2,25)), Const.ANIM_SPEED * 1.2 * delta)
				#component.rotation.z = lerp(component.rotation.z, target_rotation, Const.ANIM_SPEED * 1.5 * delta)
				i += 1
		#scale = Vector3.ONE * 5
		#else:
			##view_spot.origin.z = -10
			#var i = 0
			#for component in displayComponent:
				#component.global_transform = component.global_transform.interpolate_with(view_spot* cache_transform[i], Const.ANIM_SPEED * 1.2 * delta)
				#i += 1
		
	elif currentState == states.PRESS:
		#scale = Vector3.ONE
		var i = 0
		for component in displayComponent:
			component.transform = cache_transform[i]
			i += 1
		
		
		
	elif currentState == states.INSPECT:
		#scale = Vector3.ONE
		transform = transform.interpolate_with(target_transform.scaled(Vector3.ONE*1), Const.ANIM_SPEED * 1.5 * delta)
		if Input.is_action_just_pressed("clicked"):
			battleNode.abort_inspecting(self)
			#queue_free()
		
	elif currentState == states.DRAG:
		castableVfx.visible = false
		scale = Vector3.ONE*2
		#var cache_origin = transform.origin
		#transform.origin = cache_origin + Vector3.UP
		rotation = lerp(get_parent().rotation, Vector3(-90.0,0.0,0.0), 1)
		if Input.is_action_just_released("clicked"):
			#var arenaNode = get_parent().get_parent().get_parent()
			get_parent().draggedIndex.emit(brightness_on, false)
			battleNode.find_child("Arena").situationUpdate.emit(get_parent().get_parent())
			battleNode.find_child("Arena").dragging.emit(false)
			
			#arenaNode.handSelectedIndex = -1
			currentState = states.IDLE
		
	elif currentState == states.HOVERINLANE:
		scale = Vector3.ONE * 2
	#elif currentState == states.PLAYING:
		#if animationPlayer.animation_finished:
			#currentState = states.IDLE


func displayCard(_card:Card) -> void:
	if not is_node_ready():
		await ready
	if _card:
		_card.setCard3DUI(self)
		self.cardOwner = _card.getOwner()
		self.nameLabel.text = "[center]" + _card.getName()
		var abilitiesText = ""
		for ability in _card.getAbilities():
			abilitiesText += ability.printAbility()
			abilitiesText += "\n"
		self.abilityFloat.text = KeywordNoteService.generateKeywordNotes(abilitiesText, _card.getCastCondition())
		#self.abilityDescription.text = abilitiesText
		self.abilityOnCard.text = "[center]" + abilitiesText
		self.abilityDetailEng.text = "[center]" + abilitiesText
		self.keywordNotes.text = KeywordNoteService.generateKeywordNotes(abilitiesText, _card.getCastCondition())
		self.costLabel.text = "[center]" + str(_card.getCost())
		if _card.getCastCondition():
			castConContainer.visible = true
			self.castConLabel.text = CastCondition.labelDict[_card.getCastCondition()]
		else:
			castConContainer.visible = false
		self.powerLabel.text = "[center]" + str(_card.getPower()) 
		#self.powerLabelArena.text = str(_card.getPower())

		#/////
		var vValue = 0
		self.colorBar.material_override.albedo_color = CardColor.labelDict[_card.getColor()] + Color.from_hsv(0,0,vValue,0)
		self.nameBg.material_override.albedo_color = CardColor.labelDict[_card.getColor()] + Color.from_hsv(0,0,vValue,0)
		#self.cardBack.material_override.albedo_color = CardColor.labelDict[_card.getColor()] + Color.from_hsv(0,0,vValue,0)
		self.cardBg.material_override.albedo_color = CardColor.labelDict[_card.getColor()] + Color.from_hsv(0,0,vValue,0)
		self.onArena.find_child("ColorBar").material_override.albedo_color = CardColor.labelDict[_card.getColor()] + Color.from_hsv(0,0,vValue,0)
		#self.onArena.find_child("CardBack").material_override.albedo_color = CardColor.labelDict[_card.getColor()] + Color.from_hsv(0,0,vValue,0)
		self.onArena.find_child("CardBg").material_override.albedo_color = CardColor.labelDict[_card.getColor()] + Color.from_hsv(0,0,vValue,0)
		# self.rarityBar.modulate = Rarity.labelDict[_card.getRarity()]
		# if !brightness_on:
		# 	rarityBar.material_override.albedo_color -= Color.from_hsv(0,0,0.7,0)
		# self.cardBorder.material_override.albedo_color = CardColor.labelDict[_card.getColor()]# - Color.from_hsv(0,0,0,0.9) 
		# if !brightness_on:
		# 	self.cardBorder.material_override.albedo_color -= Color.from_hsv(0,0,0.4,0)
		
		
		if(_card.getDiffPower() > 0): 
			self.powerLabel.modulate = Color.GREEN_YELLOW
			self.powerLabelArena.modulate = Color.GREEN_YELLOW
			if !brightness_on:
				powerLabel.modulate -= Color.from_hsv(0,0,0.7,0)
		elif(_card.getDiffPower() < 0): 
			self.powerLabel.modulate = Color.RED
			self.powerLabelArena.modulate = Color.RED
			if !brightness_on:
				powerLabel.modulate -= Color.from_hsv(0,0,0.7,0)
		#else: self.powerLabel.modulate = Color.WHITE
		
		if(_card.getDiffCost() < 0): 
			self.costLabel.modulate = Color.GREEN_YELLOW
			if !brightness_on:
				costLabel.modulate -= Color.from_hsv(0,0,0.7,0)
		elif(_card.getDiffCost() > 0): 
			self.costLabel.modulate = Color.RED
			if !brightness_on:
				costLabel.modulate -= Color.from_hsv(0,0,0.7,0)
		else: self.costLabel.modulate = Color.WHITE
		
		#////
		# if isOnHand && _card.getCost() > _card.getOwner().getManaAndTempMana():
		# 	self.colorBar.modulate = Color.BLACK
		# 	if !brightness_on:
		# 		colorBar.modulate -= Color.from_hsv(0,0,0.7,0)
		
		power_color = powerLabel.modulate
		cost_color = costLabel.modulate
		#colorBar_color = colorBar.material_override.albedo_color
		#rarity_color = rarityBar.modulate
		border_color = cardBorder.material_override.albedo_color
		#name_outline_color = CardColor.labelDict[_card.getColor()]
		

		#////
		if CardArtService.CardArts.has(_card.name):
			self.cardArt.material_override.albedo_texture = CardArtService.CardArts[_card.name]
		else:
			self.cardArt.material_override.albedo_texture = CardArtService.NullCardArt
		#if get_parent() is LaneCardSlot:
			#self.animationPlayer.play("PlayCard")
			#self.animationPlayer.advance(1.0)
			#animationPlayer.seek(1.0)


func animatePowerChange(_powerDiff:int) -> void:
	if(_powerDiff > 0):
		self.floatLabel.modulate = Color.GREEN_YELLOW
		self.floatLabel.text = "+" + str(_powerDiff)
	elif(_powerDiff < 0):
		self.floatLabel.modulate = Color.CRIMSON
		self.floatLabel.text = str(_powerDiff)
	anim.play("float")

func setOnHand(_isOnHand:bool) -> void:
	self.isOnHand = _isOnHand

func _on_mouse_entered():
	if get_parent() is Hand and currentState == states.IDLE:
		if cardOwner.isOnMainSide():
			currentState = states.HOVER
			#abilityFloat.visible = true

func _on_mouse_exited() -> void:
	if currentState == states.HOVER:
		await get_tree().create_timer(Const.AWAIT_TIME * 0.03).timeout
		currentState = states.IDLE
		abilityFloat.visible = false

func _on_input_event(_camera, _event, _position, _normal, _shape_idx):
	if Input.is_action_just_pressed("clicked"):
		abilityFloat.visible = false
		if currentState == states.HOVER or currentState == states.IDLE:
			currentState = states.PRESS
			cardSelected.emit(self)



func set_layer(layer: int, switch: bool) -> void:
	cardArt.set_layer_mask_value(layer, switch)
	cardBack.set_layer_mask_value(layer, switch)
	cardName.set_layer_mask_value(layer, switch)
	nameBg.set_layer_mask_value(layer, switch)
	abilityFloat.set_layer_mask_value(layer, switch)
	#abilityDescription.set_layer_mask_value(layer, switch)
	abilityDisplay.set_layer_mask_value(layer, switch)
	keywordNotes.set_layer_mask_value(layer, switch)
	#castConLabel.set_layer_mask_value(layer, switch)
	costDisplay.set_layer_mask_value(layer, switch)
	costBg.set_layer_mask_value(layer, switch)
	powerDisplay.set_layer_mask_value(layer, switch)
	powerBg.set_layer_mask_value(layer, switch)
	colorBar.set_layer_mask_value(layer, switch)
	rarityBar.set_layer_mask_value(layer, switch)
	currentTurn.set_layer_mask_value(layer, switch)
	cardBorder.set_layer_mask_value(layer, switch)
	cardBg.set_layer_mask_value(layer, switch)
	cardPicGradient.set_layer_mask_value(layer, switch)
	castableVfx.set_layer_mask_value(layer, switch)
	
	#var i =0
	if get_parent() is LaneCardSlot:
		onArena.visible = true
		for child in onArena.get_children():
			child.set_layer_mask_value(layer, switch)
			#i+=1
	else:
		onArena.visible = false
			#i+=1
	#power_bg.set_layer_mask_value(layer, true)
	#cost_bg.set_layer_mask_value(layer, true)
	#card_description.set_layer_mask_value(layer, true)


func change_collision_mode(inspecting: bool):
	if collision:
		if inspecting:
			collision.disabled = true
			collisionHand.disabled = true
		else:
			if get_parent() is Hand:
				collision.disabled = false
				collisionHand.disabled = false
			elif get_parent() is LaneCardSlot or Graveyard:
				collision.disabled = false
				collisionHand.disabled = true
			else:
				collision.disabled = true
				collisionHand.disabled = true

func brightness_up(switch:bool):
	
	castableVfx.visible = switch
	return
	
	if switch:
		#if !brightness_on:
			#art_color += Color.from_hsv(0,0,0.7,0)
			#cost_color += Color.from_hsv(0,0,0.7,0)
			#cost_bg_color += Color.from_hsv(0,0,0.7,0)
			#castConLabel_color += Color.from_hsv(0,0,0.7,0)
			#power_color += Color.from_hsv(0,0,0.7,0)
			#power_bg_color += Color.from_hsv(0,0,0.7,0)
			#colorBar_color += Color.from_hsv(0,0,0.7,0)
			##ability_color += Color.from_hsv(0,0,0.7,0)
			#rarity_color += Color.from_hsv(0,0,0.7,0)
			##name_color += Color.from_hsv(0,0,0.7,0)
			##name_bg_color += Color.from_hsv(0,0,0.7,0)
			##name_outline_color += Color.from_hsv(0,0,0.7,0)
			#border_color += Color.from_hsv(0,0,0.7,0)
			##name_outline_color += Color.from_hsv(0,0,0.7,0)
			##border_color += Color.from_hsv(0,0,0.4,0)
		brightness_on = true
		
	else:
		#if brightness_on:
			#art_color -= Color.from_hsv(0,0,0.7,0)
			#cost_color -= Color.from_hsv(0,0,0.7,0)
			#cost_bg_color -= Color.from_hsv(0,0,0.7,0)
			#castConLabel_color -= Color.from_hsv(0,0,0.7,0)
			#power_color -= Color.from_hsv(0,0,0.7,0)
			#power_bg_color -= Color.from_hsv(0,0,0.7,0)
			#colorBar_color -= Color.from_hsv(0,0,0.7,0)
			##ability_color -= Color.from_hsv(0,0,0.7,0)
			#rarity_color -= Color.from_hsv(0,0,0.7,0)
			##name_color -= Color.from_hsv(0,0,0.7,0)
			##name_bg_color -= Color.from_hsv(0,0,0.7,0)
			##name_outline_color -= Color.from_hsv(0,0,0.7,0)
			#border_color -= Color.from_hsv(0,0,0.7,0)
			##name_outline_color -= Color.from_hsv(0,0,0.7,0)
			##border_color -= Color.from_hsv(0,0,0.4,0)
		brightness_on = false
	
	##cardArt.material_override.albedo_color = art_color
	##costLabel.modulate = cost_color
	##costBg.modulate = cost_bg_color
	##castConLabel.modulate = castConLabel_color
	##powerLabel.modulate = power_color
	##powerBg.modulate = power_bg_color
	##colorBar.modulate = colorBar_color
	##abilityFloat.modulate = ability_color
	##rarityBar.modulate = rarity_color
	##cardName.modulate = name_color
	##nameBg.material_override.albedo_color = name_bg_color
	##cardName.outline_modulate = name_outline_color
	#cardBorder.material_override.albedo_color = border_color
	#if get_parent() is LaneCardSlot:
		#cardBorder.material_override.albedo_color += Color.from_hsv(5,0,0,0)
		#border_color = cardBorder.material_override.albedo_color

func set_default():
	brightness_up(true)

func set_flipup(up:bool):
	cardArt.visible = up
	#costLabel.visible = up
	costDisplay.visible = up
	costBg.visible = up
	castConLabel.visible = up
	powerDisplay.visible = up
	powerBg.visible = up
	abilityFloat.visible = up
	keywordNotes.visible = up
	rarityBar.visible = up
	cardName.visible = up
	nameBg.visible = up
	abilityDisplay.visible = up
	#cardBorder.visible = up
	cardBg.visible = up
	#castableVfx.visible = up
	
	

func cost_visible(visibility:bool):
	#costLabel.visible = visibility
	costDisplay.visible = visibility
	costBg.visible = visibility
	#castConLabel.visible = visibility
	#abilityDisplay.visible = visibility
	
func name_visible(visibility:bool):
	cardName.visible = visibility
	nameBg.visible = visibility
	rarityBar.visible = visibility

func lightBorder(on:bool):
	cardBorder.material_override.albedo_color += Color.from_hsv(0,0,0, 0.5)
	border_color = cardBorder.material_override.albedo_color
	
func toIdle():
	currentState = states.IDLE

func overwrite_cache_transform():
	cache_transform.clear()
	for component in displayComponent:
		cache_transform.append(component.transform)
